{"round": 2, "timestamp": "2025-07-16T03:21:30.656509", "summary": {"total_cases": 1250, "valid_protection_rate": 41.86046511627907, "fp_detection_rate": 92.62634631317316, "score": 57.09022947534729}, "insights": {"camera_performance": {"QC605 WOS Cam (C) (VATO)": {"total": 110, "correct": 102, "too_conservative": 4}, "QC519 WOS Cam (C) (VALO)": {"total": 25, "correct": 22, "too_conservative": 3}, "QC320 WOS Cam (C) (VATO/VALO)": {"total": 77, "correct": 67, "too_conservative": 8}, "QC109F (VALO)": {"total": 85, "correct": 83, "too_conservative": 2}, "QC108F (VALO)": {"total": 29, "correct": 26, "too_conservative": 2}, "QC506 WOS Cam (C) (VATO)": {"total": 101, "correct": 96, "too_conservative": 3}, "QC106F (VALO)": {"total": 18, "correct": 18, "too_conservative": 0}, "QC614F (VALO)": {"total": 16, "correct": 16, "too_conservative": 0}, "QC523 WOS Cam (C) (VALO)": {"total": 44, "correct": 41, "too_conservative": 2}, "QC504F (VALO)": {"total": 5, "correct": 4, "too_conservative": 1}, "QC601 WOS Cam (C) (VATO/VALO)": {"total": 156, "correct": 136, "too_conservative": 14}, "QC313F (VALO)": {"total": 21, "correct": 21, "too_conservative": 0}, "QC307F (VALO)": {"total": 29, "correct": 24, "too_conservative": 5}, "QC528 WOS Cam (C) (VATO/VALO)": {"total": 41, "correct": 38, "too_conservative": 2}, "QC507F (VALO)": {"total": 11, "correct": 10, "too_conservative": 1}, "QC512 WOS Cam (C) (VALO)": {"total": 31, "correct": 26, "too_conservative": 5}, "QC510 WOS Cam (C) (VATO/VALO)": {"total": 86, "correct": 83, "too_conservative": 1}, "QC611 WOS Cam (C) (VATO/VALO)": {"total": 28, "correct": 23, "too_conservative": 2}, "QC308F (VALO)": {"total": 21, "correct": 21, "too_conservative": 0}, "QC531F": {"total": 34, "correct": 28, "too_conservative": 5}, "QC530F (VALO)": {"total": 7, "correct": 4, "too_conservative": 3}, "QC318F (VALO)": {"total": 8, "correct": 5, "too_conservative": 3}, "QC104F (VALO)": {"total": 82, "correct": 72, "too_conservative": 10}, "QC526F (VALO)": {"total": 3, "correct": 3, "too_conservative": 0}, "QC602F (VALO)": {"total": 8, "correct": 8, "too_conservative": 0}, "QC520F (VALO)": {"total": 2, "correct": 0, "too_conservative": 2}, "QC509F (VALO)": {"total": 5, "correct": 3, "too_conservative": 2}, "QC302F (VALO)": {"total": 8, "correct": 8, "too_conservative": 0}, "QC525F (VALO)": {"total": 3, "correct": 3, "too_conservative": 0}, "QC521F (VALO)": {"total": 5, "correct": 5, "too_conservative": 0}, "QC306F (VALO)": {"total": 17, "correct": 15, "too_conservative": 2}, "QC610F (VALO)": {"total": 8, "correct": 6, "too_conservative": 1}, "QC517F (VALO)": {"total": 1, "correct": 1, "too_conservative": 0}, "QC505F (VALO)": {"total": 14, "correct": 13, "too_conservative": 0}, "QC107F (VALO)": {"total": 9, "correct": 9, "too_conservative": 0}, "QC110F (VALO)": {"total": 23, "correct": 21, "too_conservative": 2}, "QC604F (VALO)": {"total": 13, "correct": 13, "too_conservative": 0}, "QC603F (VALO)": {"total": 1, "correct": 1, "too_conservative": 0}, "QC615F (VALO)": {"total": 7, "correct": 6, "too_conservative": 1}, "QC516F (VALO)": {"total": 6, "correct": 6, "too_conservative": 0}, "QC316F (VALO)": {"total": 26, "correct": 24, "too_conservative": 2}, "QC510F (VALO)": {"total": 1, "correct": 1, "too_conservative": 0}, "QC518F (VALO)": {"total": 3, "correct": 2, "too_conservative": 1}, "QC508F (VALO)": {"total": 1, "correct": 1, "too_conservative": 0}, "QC514F (VALO)": {"total": 6, "correct": 6, "too_conservative": 0}, "QC111F (VALO)": {"total": 6, "correct": 6, "too_conservative": 0}, "QC515F (VALO)": {"total": 3, "correct": 3, "too_conservative": 0}, "QC606F (VALO)": {"total": 6, "correct": 6, "too_conservative": 0}}, "infringement_performance": {"PPE Non-compliance": {"total": 894, "correct": 801, "too_conservative": 70}, "STA Double-up": {"total": 27, "correct": 25, "too_conservative": 2}, "One man Lashing": {"total": 189, "correct": 176, "too_conservative": 12}, "2-Container Distance": {"total": 60, "correct": 58, "too_conservative": 1}, "Ex.Row Violation": {"total": 79, "correct": 75, "too_conservative": 4}, "Spreader Ride": {"total": 1, "correct": 1, "too_conservative": 0}}, "confidence_distribution": {"PPE Non-compliance": [95, 92, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 92, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 92, 95, 95, 95, 95, 95, 95, 95, 95, 85, 95, 92, 85, 95, 85, 95, 95, 95, 95, 95, 95, 95, 95, 85, 85, 95, 95, 95, 95, 95, 95, 95, 85, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 85, 95, 92, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 92, 95, 95, 95, 85, 85, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 85, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 92, 95, 85, 75, 95, 92, 85, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 92, 95, 92, 95, 95, 95, 95, 95, 92, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 85, 95, 95, 95, 92, 95, 95, 92, 95, 92, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 85, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 92, 95, 95, 95, 85, 95, 95, 95, 95, 95, 85, 95, 95, 95, 95, 95, 92, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 92, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 85, 95, 95, 95, 95, 95, 95, 95, 95, 92, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 85, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 92, 95, 95, 95, 95, 85, 95, 92, 95, 95, 95, 92, 95, 95, 95, 95, 95, 95, 95, 95, 92, 95, 95, 85, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 85, 95, 95, 95, 95, 92, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 92, 95, 95, 95, 92, 95, 95, 95, 95, 95, 95, 95, 95, 95, 85, 95, 95, 92, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 92, 85, 95, 95, 95, 95, 95, 95, 95, 85, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 85, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 92, 95, 85, 95, 95, 85, 95, 95, 95, 95, 95, 95, 95, 95, 95, 85, 95, 95, 95, 95, 85, 95, 95, 95, 92, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 85, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 92, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 85, 95, 95, 95, 95, 95, 92, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 92, 95, 95, 95, 95, 85, 95, 95, 95, 95, 95, 95, 95, 85, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 85, 95, 92, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 92, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 92, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 85, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 92, 95, 95, 85, 95, 95, 95, 95, 95, 95, 95, 95, 95], "STA Double-up": [90, 90, 90, 90, 90, 92, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 75, 75, 90, 90, 92, 90, 90, 90, 90, 90], "One man Lashing": [90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 92, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 75, 90, 90, 90, 90, 90, 90, 92, 92, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 92, 90, 92, 90, 90, 90, 90, 90, 90, 90, 75, 75, 90, 90, 90, 75, 95, 90, 90, 90, 95, 95, 95, 95, 95, 90, 75, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 95, 95, 90, 90, 90, 90, 90, 75, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 75, 90, 90, 90, 92, 90, 90, 90, 90, 90, 75, 90, 90, 75, 90, 75, 75, 90, 75, 90, 90, 90, 90, 90, 90, 90, 92, 90, 92, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 92, 90, 90, 92, 92, 90, 90, 90, 90, 90, 90, 90, 90, 90, 92, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90], "2-Container Distance": [90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 75, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90], "Ex.Row Violation": [90, 90, 90, 90, 90, 90, 90, 75, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 75, 90, 75, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 75, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90], "Spreader Ride": [95]}, "person_detection_accuracy": {"total": 1195, "correct": 439}, "false_negative_cases": ["V1250623137", "V1250623174", "V1250624046", "V1250624071", "V1250624083", "V1250624160", "V1250625027", "V1250625063", "V1250625157", "V1250626041", "V1250626054", "V1250626060", "V1250626069", "V1250626073", "V1250627179", "V1250627204", "V1250628042", "V1250628101", "V1250628112", "V1250628120", "V1250629084", "V1250629102", "V1250629119", "V1250630019", "V1250630065"], "overly_conservative_cases": ["V1250623122", "V1250623171", "V1250624020", "V1250624029", "V1250624031", "V1250624032", "V1250624035", "V1250624052", "V1250624053", "V1250624054", "V1250624063", "V1250624084", "V1250624086", "V1250624101", "V1250624106", "V1250624107", "V1250624130", "V1250624148", "V1250624162", "V1250624165", "V1250624166", "V1250624168", "V1250624169", "V1250625016", "V1250625018", "V1250625024", "V1250625037", "V1250625041", "V1250625044", "V1250625093", "V1250625130", "V1250625131", "V1250625145", "V1250625149", "V1250625155", "V1250626004", "V1250626022", "V1250626044", "V1250626064", "V1250626075", "V1250626138", "V1250626161", "V1250626163", "V1250626164", "V1250626169", "V1250627007", "V1250627011", "V1250627026", "V1250627037", "V1250627045", "V1250627047", "V1250627077", "V1250627083", "V1250627092", "V1250627097", "V1250627103", "V1250627122", "V1250627123", "V1250627134", "V1250627176", "V1250627214", "V1250627216", "V1250627220", "V1250627221", "V1250627228", "V1250627238", "V1250627244", "V1250628002", "V1250628003", "V1250628010", "V1250628011", "V1250628018", "V1250628034", "V1250628086", "V1250628135", "V1250628139", "V1250628141", "V1250628143", "V1250629015", "V1250629020", "V1250629021", "V1250629029", "V1250629091", "V1250629096", "V1250629148", "V1250630043", "V1250630078", "V1250630096", "V1250630099"]}}