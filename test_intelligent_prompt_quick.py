#!/usr/bin/env python3
"""
Quick test of intelligent prompt with key false positive patterns
"""

import json
import base64
import requests
import os

VLM_API_URL = "http://100.106.127.35:9500/v1/chat/completions"
VLM_MODEL = "VLM-38B-AWQ"

# Load the intelligent prompt
with open('intelligent_vlm_prompt.txt', 'r') as f:
    INTELLIGENT_PROMPT = f.read()

def encode_image(image_path):
    """Encode image to base64"""
    if not os.path.exists(image_path):
        return None
    with open(image_path, 'rb') as f:
        return base64.b64encode(f.read()).decode('utf-8')

def test_key_patterns():
    """Test the most common false positive patterns"""
    
    print("\nTESTING INTELLIGENT PROMPT ON KEY PATTERNS")
    print("="*60)
    
    # Load data
    with open('valo_batch_round3_complete.json', 'r') as f:
        data = json.load(f)
    
    # Find test cases for each major pattern
    test_patterns = {
        "Worker in Full PPE": "FULL PPE",
        "Crane Structure": "CRANE STRUCTURE",
        "Vessel Structure": "VESSEL STRUCTURE", 
        "Multiple Workers": "2 LS",
        "Worker at Wharf": "AT WHARF"
    }
    
    test_cases = []
    for pattern_name, search_term in test_patterns.items():
        for case in data['results']:
            if case.get('remarks') and search_term in case['remarks'].upper():
                test_cases.append({
                    'pattern': pattern_name,
                    'case': case
                })
                break
    
    # Test each pattern
    session = requests.Session()
    results = []
    
    for test in test_cases[:5]:  # Test first 5 patterns
        pattern = test['pattern']
        case = test['case']
        
        print(f"\n\nTesting Pattern: {pattern}")
        print(f"Case: {case['case_number']}")
        print(f"Violation: {case['infringement_type']}")
        print(f"Remark: {case['remarks']}")
        print(f"Actual: {'False Positive' if case['is_false_positive'] else 'Valid'}")
        
        # Encode images
        source_b64 = encode_image(case['source_image'])
        cropped_b64 = encode_image(case['cropped_image'])
        
        if not source_b64 or not cropped_b64:
            print("ERROR: Could not encode images")
            continue
        
        # Create request with both images
        payload = {
            "model": VLM_MODEL,
            "messages": [{
                "role": "user",
                "content": [
                    {"type": "text", "text": INTELLIGENT_PROMPT.replace("General Safety Violation", case['infringement_type'])},
                    {"type": "text", "text": "\n\nSOURCE IMAGE (full view):"},
                    {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{source_b64}"}},
                    {"type": "text", "text": "\n\nCROPPED IMAGE (area of concern):"},
                    {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{cropped_b64}"}}
                ]
            }],
            "temperature": 0.1,
            "max_tokens": 200
        }
        
        try:
            print("Calling VLM...")
            response = session.post(VLM_API_URL, json=payload, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                vlm_response = result['choices'][0]['message']['content'].strip()
                print(f"\nVLM Response:\n{vlm_response}")
                
                # Check if correct
                predicted_fp = 'YES' in vlm_response.upper()
                actual_fp = case['is_false_positive']
                correct = predicted_fp == actual_fp
                
                print(f"\nResult: {'✓ CORRECT' if correct else '✗ INCORRECT'}")
                results.append({
                    'pattern': pattern,
                    'correct': correct,
                    'response': vlm_response
                })
            else:
                print(f"ERROR: VLM returned status {response.status_code}")
                
        except Exception as e:
            print(f"ERROR: {e}")
    
    # Summary
    print("\n\n" + "="*60)
    print("SUMMARY")
    print("="*60)
    
    correct = sum(1 for r in results if r['correct'])
    print(f"Tested: {len(results)} patterns")
    print(f"Correct: {correct}/{len(results)} ({correct/len(results)*100:.0f}%)")
    
    # Save results
    with open('intelligent_prompt_quick_test_results.json', 'w') as f:
        json.dump({
            'timestamp': str(datetime.now()),
            'results': results,
            'accuracy': correct/len(results)*100 if results else 0
        }, f, indent=2)

if __name__ == "__main__":
    from datetime import datetime
    test_key_patterns()