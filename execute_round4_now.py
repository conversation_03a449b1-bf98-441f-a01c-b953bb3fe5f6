#!/usr/bin/env python3
"""
Execute Round 4 Equipment Pattern Recognition NOW
"""

import asyncio
import json
import logging
import sys

sys.path.append('/home/<USER>/VALO_AI-FARM_2025')

from achieve_70_percent_safe import SafetyFirst70PercentSystem

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(message)s',
    handlers=[
        logging.FileHandler('round4_execution.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

async def main():
    """Execute Round 4"""
    logger.info("="*80)
    logger.info("EXECUTING ROUND 4: EQUIPMENT PATTERN RECOGNITION")
    logger.info("="*80)
    
    # Load Round 3 results
    with open('valo_batch_round3_complete.json', 'r') as f:
        round3_data = json.load(f)
        previous_results = round3_data['results']
    
    logger.info(f"Starting from Round 3: FP Detection {round3_data['stats']['fp_detection_rate']:.1f}%")
    
    # Initialize system
    system = SafetyFirst70PercentSystem()
    
    # Run Round 4
    logger.info("\nLaunching Round 4 with Equipment Pattern Recognition...")
    round4_results = await system.run_round_safe(
        round_num=4,
        prompt_generator=system.generate_round4_prompt,
        previous_results=previous_results
    )
    
    # The system automatically saves results and checks if target is achieved
    logger.info("\nRound 4 execution complete!")
    
    # Check if we achieved the target
    if hasattr(system, 'fp_detection_rate') and system.fp_detection_rate >= 70:
        logger.info("\n🎯 TARGET ACHIEVED! 70% FP reduction with 100% safety!")
    else:
        logger.info("\nRound 4 complete. Check valo_round4_safe_complete.json for results.")

if __name__ == "__main__":
    asyncio.run(main())