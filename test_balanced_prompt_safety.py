#!/usr/bin/env python3
"""
Test balanced prompt on critical safety cases
Ensure we protect 100% of valid violations
"""

import json
import base64
import requests
import os
from datetime import datetime

VLM_API_URL = "http://100.106.127.35:9500/v1/chat/completions"
VLM_MODEL = "VLM-38B-AWQ"

class SafetyPromptTester:
    def __init__(self):
        self.session = requests.Session()
        self.results = {
            'valid_violations': [],
            'false_positives': []
        }
        
    def encode_image(self, image_path):
        """Encode image to base64"""
        try:
            if os.path.exists(image_path):
                with open(image_path, 'rb') as f:
                    return base64.b64encode(f.read()).decode('utf-8')
        except:
            pass
        return None
    
    def load_balanced_prompt(self):
        """Load the balanced safety prompt"""
        with open('balanced_safety_prompt.txt', 'r') as f:
            return f.read()
    
    def call_vlm(self, source_b64, cropped_b64, prompt, timeout=20):
        """Call VLM with dual images"""
        payload = {
            "model": VLM_MODEL,
            "messages": [{
                "role": "user",
                "content": [
                    {"type": "text", "text": prompt},
                    {"type": "text", "text": "\n\nSOURCE IMAGE (full context):"},
                    {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{source_b64}"}},
                    {"type": "text", "text": "\n\nCROPPED IMAGE (area of concern):"},
                    {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{cropped_b64}"}}
                ]
            }],
            "temperature": 0.1,
            "max_tokens": 200
        }
        
        try:
            response = self.session.post(VLM_API_URL, json=payload, timeout=timeout)
            if response.status_code == 200:
                result = response.json()
                return result['choices'][0]['message']['content'].strip()
        except:
            pass
        return None
    
    def test_critical_valid_cases(self):
        """Test on known valid violations that were missed"""
        print("\nTESTING CRITICAL VALID VIOLATIONS")
        print("="*60)
        print("These are REAL safety violations that must be caught!\n")
        
        # Load the error analysis
        with open('valid_violation_error_analysis.json', 'r') as f:
            error_data = json.load(f)
        
        # Get sample of critical valid violations
        critical_cases = error_data['sample_errors'][:10]
        
        # Load full data for image paths
        with open('enhanced_1250_progress.json', 'r') as f:
            full_data = json.load(f)
        
        # Create case lookup
        case_lookup = {r['case_number']: r for r in full_data['results']}
        
        # Load balanced prompt
        balanced_prompt = self.load_balanced_prompt()
        
        for i, error_case in enumerate(critical_cases):
            case_num = error_case['case_number']
            
            # Get full case data
            if case_num not in case_lookup:
                continue
                
            case = case_lookup[case_num]
            
            print(f"\nCase {i+1}: {case_num}")
            print(f"Violation: {case['infringement_type']}")
            print(f"Remark: {case.get('remarks', 'N/A')[:80]}...")
            print(f"Previous result: Entity={error_case.get('entity_type')}, PPE={error_case.get('ppe_status')}")
            
            # Get images from original batch data
            batch_data = json.load(open('valo_batch_round3_complete.json'))
            batch_case = next((c for c in batch_data['results'] if c['case_number'] == case_num), None)
            
            if not batch_case:
                continue
            
            # Encode images
            source_b64 = self.encode_image(batch_case['source_image'])
            cropped_b64 = self.encode_image(batch_case['cropped_image'])
            
            if not source_b64 or not cropped_b64:
                continue
            
            # Test with balanced prompt
            print("Testing with balanced prompt...")
            response = self.call_vlm(source_b64, cropped_b64, balanced_prompt)
            
            if response:
                print(f"Response:\n{response}")
                
                # Check result
                is_fp = 'YES' in response.upper().split('FALSE POSITIVE:')[1][:10] if 'FALSE POSITIVE:' in response.upper() else False
                
                # This is a valid violation, so it should NOT be marked as FP
                correct = not is_fp
                
                print(f"\nResult: {'✓ PROTECTED' if correct else '✗ MISSED'} - Marked as {'FP' if is_fp else 'VALID'}")
                
                self.results['valid_violations'].append({
                    'case': case_num,
                    'correct': correct,
                    'response': response,
                    'remarks': case.get('remarks', '')
                })
    
    def test_known_false_positives(self):
        """Test on known false positives"""
        print("\n\nTESTING KNOWN FALSE POSITIVES")
        print("="*60)
        print("These should be correctly identified as false alarms\n")
        
        # Known FP patterns
        fp_patterns = [
            "CRANE STRUCTURE CAPTURED AS LS",
            "VESSEL STRUCTURE CAPTURED AS LS",
            "LS Full PPE at wharf",
            "2 LS doing lashing",
            "CAMERA STRUCTURE CAPTURED AS LS"
        ]
        
        # Load data
        batch_data = json.load(open('valo_batch_round3_complete.json'))
        balanced_prompt = self.load_balanced_prompt()
        
        tested = 0
        for pattern in fp_patterns:
            # Find a case with this pattern
            for case in batch_data['results']:
                if case['is_false_positive'] and case.get('remarks') and pattern in case['remarks'].upper():
                    print(f"\nTesting FP pattern: {pattern}")
                    print(f"Case: {case['case_number']}")
                    print(f"Remark: {case['remarks']}")
                    
                    # Encode images
                    source_b64 = self.encode_image(case['source_image'])
                    cropped_b64 = self.encode_image(case['cropped_image'])
                    
                    if not source_b64 or not cropped_b64:
                        continue
                    
                    # Test
                    response = self.call_vlm(source_b64, cropped_b64, balanced_prompt)
                    
                    if response:
                        is_fp = 'YES' in response.upper().split('FALSE POSITIVE:')[1][:10] if 'FALSE POSITIVE:' in response.upper() else False
                        correct = is_fp  # Should be marked as FP
                        
                        print(f"Result: {'✓ CORRECT' if correct else '✗ WRONG'}")
                        
                        self.results['false_positives'].append({
                            'case': case['case_number'],
                            'correct': correct,
                            'pattern': pattern
                        })
                    
                    tested += 1
                    break
            
            if tested >= 5:
                break
    
    def generate_report(self):
        """Generate test report"""
        print("\n\n" + "="*60)
        print("BALANCED PROMPT TEST REPORT")
        print("="*60)
        
        # Valid violation protection
        valid_results = self.results['valid_violations']
        if valid_results:
            protected = sum(1 for r in valid_results if r['correct'])
            protection_rate = protected / len(valid_results) * 100
            
            print(f"\nVALID VIOLATION PROTECTION:")
            print(f"Tested: {len(valid_results)} real violations")
            print(f"Protected: {protected}/{len(valid_results)} ({protection_rate:.1f}%)")
            
            if protection_rate < 100:
                print("\n⚠️  WARNING: Still missing some valid violations!")
                print("Missed cases:")
                for r in valid_results:
                    if not r['correct']:
                        print(f"  - {r['case']}: {r['remarks'][:60]}...")
            else:
                print("\n✅ SUCCESS: All valid violations protected!")
        
        # False positive detection
        fp_results = self.results['false_positives']
        if fp_results:
            detected = sum(1 for r in fp_results if r['correct'])
            fp_rate = detected / len(fp_results) * 100
            
            print(f"\nFALSE POSITIVE DETECTION:")
            print(f"Tested: {len(fp_results)} known false positives")
            print(f"Detected: {detected}/{len(fp_results)} ({fp_rate:.1f}%)")
        
        # Save results
        report = {
            'timestamp': datetime.now().isoformat(),
            'valid_protection_rate': protection_rate if valid_results else 0,
            'fp_detection_rate': fp_rate if fp_results else 0,
            'results': self.results
        }
        
        with open('balanced_prompt_test_report.json', 'w') as f:
            json.dump(report, f, indent=2)
        
        print(f"\nReport saved to: balanced_prompt_test_report.json")
        
        print("\n\nRECOMMENDATIONS:")
        if valid_results and protection_rate < 95:
            print("❌ Need to further adjust prompt to protect valid violations")
            print("   - Lower structure confidence threshold")
            print("   - Add more behavioral violation checks")
        else:
            print("✅ Balanced prompt is protecting valid violations!")
            print("   - Ready for full dataset testing")
            print("   - Monitor both protection and FP detection rates")

if __name__ == "__main__":
    tester = SafetyPromptTester()
    tester.test_critical_valid_cases()
    tester.test_known_false_positives()
    tester.generate_report()