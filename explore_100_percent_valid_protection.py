#!/usr/bin/env python3
"""
VALO 100% Valid Protection Explorer
Explores aggressive strategies to achieve 100% valid case protection
Documents the trade-offs with false positive detection
"""

import json
import base64
import requests
import os
from datetime import datetime
import time
from collections import defaultdict
import random

class ValidProtectionExplorer:
    def __init__(self):
        self.vlm_url = "http://**************:9500/v1/chat/completions"
        self.vlm_model = "VLM-38B-AWQ"
        self.session = requests.Session()
        
        # Exploration strategies
        self.strategies = {
            'ultra_conservative': {
                'name': 'Ultra-Conservative Thresholds',
                'description': 'Set extremely high thresholds for structure detection',
                'thresholds': {
                    'structure': 99,  # Nearly impossible to call structure
                    'person': 30,     # Very easy to detect person
                    'ppe_compliant': 90,  # Very strict PPE requirements
                    'behavioral': 40  # Very sensitive to violations
                }
            },
            'multi_layer_validation': {
                'name': 'Multi-Layer Validation',
                'description': 'Require multiple confidence checks before marking as FP',
                'thresholds': {
                    'structure': 95,
                    'person': 40,
                    'ppe_compliant': 85,
                    'behavioral': 50,
                    'double_check': True  # Special flag
                }
            },
            'uncertainty_rejection': {
                'name': 'Uncertainty-Based Rejection',
                'description': 'Any uncertainty defaults to valid violation',
                'thresholds': {
                    'structure': 93,
                    'person': 45,
                    'ppe_compliant': 80,
                    'behavioral': 55,
                    'uncertainty_threshold': 70  # Below this = valid violation
                }
            },
            'context_sensitive': {
                'name': 'Context-Sensitive Rules',
                'description': 'Different thresholds based on violation type',
                'thresholds': {
                    'structure': 92,
                    'person': 50,
                    'ppe_compliant': 75,
                    'behavioral': 60,
                    'type_specific': True  # Apply different rules per type
                }
            },
            'ensemble_voting': {
                'name': 'Ensemble Voting System',
                'description': 'Multiple prompts vote, any "valid" vote wins',
                'thresholds': {
                    'structure': 90,
                    'person': 50,
                    'ppe_compliant': 70,
                    'behavioral': 60,
                    'ensemble_size': 3  # Number of different prompts
                }
            },
            'maximum_safety': {
                'name': 'Maximum Safety Mode',
                'description': 'When in doubt, always flag as valid violation',
                'thresholds': {
                    'structure': 98,
                    'person': 35,
                    'ppe_compliant': 95,
                    'behavioral': 35,
                    'safety_mode': 'maximum'
                }
            }
        }
        
        # Results storage
        self.exploration_results = []
        
    def encode_image(self, image_path):
        """Encode image to base64"""
        try:
            if os.path.exists(image_path):
                with open(image_path, 'rb') as f:
                    return base64.b64encode(f.read()).decode('utf-8')
        except:
            pass
        return None
    
    def create_strategy_prompt(self, strategy_name, thresholds):
        """Create prompt based on strategy"""
        
        base_prompt = open('FINAL_PRODUCTION_PROMPT.txt', 'r').read()
        
        if strategy_name == 'ultra_conservative':
            # Make it nearly impossible to call something a structure
            prompt = base_prompt.replace(
                "A) INDUSTRIAL STRUCTURE (need >90% confidence)",
                f"A) INDUSTRIAL STRUCTURE (need >{thresholds['structure']}% confidence - EXTREME CAUTION)"
            )
            prompt += "\n\nEXTREME SAFETY MODE: Unless you are 99% certain this is a structure with NO human features, assume it might be a person."
            
        elif strategy_name == 'multi_layer_validation':
            prompt = base_prompt + f"""

MULTI-LAYER VALIDATION PROTOCOL:
1. Initial assessment with standard thresholds
2. If marking as FALSE POSITIVE, double-check:
   - Is structure confidence >{thresholds['structure']}%?
   - Is there ANY possibility this could be a person?
   - Are you ABSOLUTELY certain about PPE compliance?
3. If ANY doubt exists, mark as VALID VIOLATION"""
            
        elif strategy_name == 'uncertainty_rejection':
            prompt = base_prompt + f"""

UNCERTAINTY HANDLING:
- Calculate overall confidence for your decision
- If confidence <{thresholds.get('uncertainty_threshold', 70)}%, automatically mark as VALID VIOLATION
- Include confidence percentage in output
- When uncertain between structure/person, ALWAYS choose person"""
            
        elif strategy_name == 'context_sensitive':
            prompt = base_prompt + """

CONTEXT-SENSITIVE RULES:
- PPE violations: Apply strictest standards
- Behavioral violations: Zero tolerance
- One-man lashing: Assume violation unless clearly safe
- Structure identification: Require multiple confirming features"""
            
        elif strategy_name == 'ensemble_voting':
            # This would use multiple prompts in practice
            prompt = base_prompt + """

CONSERVATIVE VOTER #1:
- Prioritize safety above all
- Any doubt = valid violation
- Require extreme confidence for false positive determination"""
            
        elif strategy_name == 'maximum_safety':
            prompt = base_prompt.replace(
                "REMEMBER: Safety is paramount. When uncertain, flag for human review.",
                """MAXIMUM SAFETY PROTOCOL:
- ANY uncertainty = VALID VIOLATION
- ANY ambiguity = VALID VIOLATION  
- ANY mixed signals = VALID VIOLATION
- Only mark FALSE POSITIVE with 100% certainty
- Better to have 1000 false alerts than miss 1 real violation"""
            )
        
        # Update all thresholds in prompt
        for key, value in thresholds.items():
            if isinstance(value, int) and key != 'ensemble_size':
                old_pattern = f"{key}: \\d+%"
                new_pattern = f"{key}: {value}%"
                import re
                prompt = re.sub(old_pattern, new_pattern, prompt)
        
        return prompt
    
    def test_strategy(self, strategy_name, strategy_config, test_cases):
        """Test a specific strategy"""
        
        print(f"\n{'='*70}")
        print(f"TESTING: {strategy_config['name']}")
        print(f"{'='*70}")
        print(f"Description: {strategy_config['description']}")
        print(f"Thresholds: {json.dumps(strategy_config['thresholds'], indent=2)}")
        
        results = []
        
        # Test on sample of cases
        sample_size = min(100, len(test_cases))
        sample_cases = random.sample(test_cases, sample_size)
        
        print(f"\nTesting {sample_size} cases...")
        
        for i, case in enumerate(sample_cases):
            if i % 20 == 0 and i > 0:
                print(f"Progress: {i}/{sample_size}")
            
            # Special handling for ensemble voting
            if strategy_name == 'ensemble_voting':
                result = self.test_ensemble(case, strategy_config['thresholds'])
            else:
                prompt = self.create_strategy_prompt(strategy_name, strategy_config['thresholds'])
                result = self.test_single_case(case, prompt)
            
            if result:
                results.append(result)
            
            time.sleep(0.5)  # Rate limit
        
        # Calculate metrics
        metrics = self.calculate_detailed_metrics(results)
        
        print(f"\nResults for {strategy_config['name']}:")
        print(f"├─ Valid Protection: {metrics['valid_protection']:.1f}% ({metrics['valid_protected']}/{metrics['valid_total']})")
        print(f"├─ FP Detection: {metrics['fp_detection']:.1f}% ({metrics['fp_detected']}/{metrics['fp_total']})")
        print(f"├─ Overall Accuracy: {metrics['accuracy']:.1f}%")
        print(f"└─ Valid Cases Missed: {metrics['valid_missed']}")
        
        # Store results
        strategy_result = {
            'strategy': strategy_name,
            'config': strategy_config,
            'metrics': metrics,
            'sample_size': len(results),
            'timestamp': datetime.now().isoformat()
        }
        
        self.exploration_results.append(strategy_result)
        
        return metrics
    
    def test_single_case(self, case, prompt):
        """Test a single case with given prompt"""
        
        # Encode images
        source_b64 = self.encode_image(case['source_image'])
        cropped_b64 = self.encode_image(case['cropped_image'])
        
        if not source_b64 or not cropped_b64:
            return None
        
        payload = {
            "model": self.vlm_model,
            "messages": [{
                "role": "user",
                "content": [
                    {"type": "text", "text": prompt},
                    {"type": "text", "text": "\n\nSOURCE IMAGE:"},
                    {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{source_b64}"}},
                    {"type": "text", "text": "\n\nCROPPED IMAGE:"},
                    {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{cropped_b64}"}}
                ]
            }],
            "temperature": 0.1,
            "max_tokens": 300
        }
        
        try:
            response = self.session.post(self.vlm_url, json=payload, timeout=15)
            if response.status_code == 200:
                vlm_response = response.json()['choices'][0]['message']['content']
                
                # Parse response
                predicted_fp = 'YES' in vlm_response.split('FALSE POSITIVE:')[1][:5]
                
                return {
                    'case_number': case['case_number'],
                    'actual_fp': case['is_false_positive'],
                    'predicted_fp': predicted_fp,
                    'correct': predicted_fp == case['is_false_positive'],
                    'response': vlm_response
                }
        except:
            pass
        
        return None
    
    def test_ensemble(self, case, thresholds):
        """Test using ensemble voting"""
        
        ensemble_size = thresholds.get('ensemble_size', 3)
        votes = []
        
        # Create different prompts for ensemble
        prompts = [
            self.create_strategy_prompt('ultra_conservative', thresholds),
            self.create_strategy_prompt('uncertainty_rejection', thresholds),
            self.create_strategy_prompt('maximum_safety', thresholds)
        ]
        
        for prompt in prompts[:ensemble_size]:
            result = self.test_single_case(case, prompt)
            if result:
                votes.append(result['predicted_fp'])
        
        if not votes:
            return None
        
        # Conservative voting: ANY vote for "valid violation" wins
        predicted_fp = all(votes)  # Only FP if ALL agree it's FP
        
        return {
            'case_number': case['case_number'],
            'actual_fp': case['is_false_positive'],
            'predicted_fp': predicted_fp,
            'correct': predicted_fp == case['is_false_positive'],
            'ensemble_votes': votes
        }
    
    def calculate_detailed_metrics(self, results):
        """Calculate detailed performance metrics"""
        
        if not results:
            return {
                'accuracy': 0, 'fp_detection': 0, 'valid_protection': 0,
                'fp_total': 0, 'fp_detected': 0, 'valid_total': 0,
                'valid_protected': 0, 'valid_missed': 0
            }
        
        correct = sum(r['correct'] for r in results)
        
        # FP metrics
        actual_fps = [r for r in results if r['actual_fp']]
        fp_detected = sum(r['predicted_fp'] for r in actual_fps)
        
        # Valid metrics
        actual_valid = [r for r in results if not r['actual_fp']]
        valid_protected = sum(not r['predicted_fp'] for r in actual_valid)
        valid_missed = len(actual_valid) - valid_protected
        
        return {
            'accuracy': correct / len(results) * 100,
            'fp_detection': fp_detected / len(actual_fps) * 100 if actual_fps else 0,
            'valid_protection': valid_protected / len(actual_valid) * 100 if actual_valid else 100,
            'fp_total': len(actual_fps),
            'fp_detected': fp_detected,
            'valid_total': len(actual_valid),
            'valid_protected': valid_protected,
            'valid_missed': valid_missed
        }
    
    def explore_100_percent_protection(self):
        """Run exploration of strategies to achieve 100% valid protection"""
        
        print("="*70)
        print("EXPLORING 100% VALID PROTECTION STRATEGIES")
        print("="*70)
        print("\nGoal: Achieve 100% valid case protection")
        print("Trade-off: Document impact on false positive detection")
        
        # Load test data
        print("\nLoading test data...")
        with open('valo_batch_round3_complete.json', 'r') as f:
            all_cases = json.load(f)['results']
        
        print(f"Total cases available: {len(all_cases)}")
        
        # Test each strategy
        for strategy_name, strategy_config in self.strategies.items():
            metrics = self.test_strategy(strategy_name, strategy_config, all_cases)
            
            # If we achieved 100% valid protection, note it
            if metrics['valid_protection'] == 100.0:
                print(f"\n🎯 ACHIEVED 100% VALID PROTECTION with {strategy_name}!")
                print(f"   Cost: FP Detection dropped to {metrics['fp_detection']:.1f}%")
        
        # Generate final report
        self.generate_exploration_report()
    
    def generate_exploration_report(self):
        """Generate comprehensive exploration report"""
        
        print("\n" + "="*70)
        print("EXPLORATION SUMMARY")
        print("="*70)
        
        # Sort by valid protection rate
        sorted_results = sorted(self.exploration_results, 
                              key=lambda x: x['metrics']['valid_protection'], 
                              reverse=True)
        
        print("\nStrategies Ranked by Valid Protection:")
        print("─" * 70)
        
        for result in sorted_results:
            metrics = result['metrics']
            print(f"\n{result['config']['name']}:")
            print(f"├─ Valid Protection: {metrics['valid_protection']:.1f}%")
            print(f"├─ FP Detection: {metrics['fp_detection']:.1f}%")
            print(f"├─ Trade-off: {metrics['valid_protection'] - metrics['fp_detection']:.1f} point spread")
            print(f"└─ Feasibility: {'PRODUCTION READY' if metrics['valid_protection'] >= 98 and metrics['fp_detection'] >= 50 else 'NOT RECOMMENDED'}")
        
        # Find strategies that achieved 100%
        perfect_strategies = [r for r in sorted_results if r['metrics']['valid_protection'] == 100.0]
        
        if perfect_strategies:
            print("\n🎯 STRATEGIES ACHIEVING 100% VALID PROTECTION:")
            for result in perfect_strategies:
                print(f"\n{result['config']['name']}:")
                print(f"├─ FP Detection: {result['metrics']['fp_detection']:.1f}%")
                print(f"├─ Overall Accuracy: {result['metrics']['accuracy']:.1f}%")
                print(f"└─ Practical Impact: {100 - result['metrics']['fp_detection']:.1f}% of FPs would still alert")
        
        # Save full report
        report = {
            'timestamp': datetime.now().isoformat(),
            'goal': '100% valid case protection',
            'strategies_tested': len(self.strategies),
            'results': self.exploration_results,
            'perfect_strategies': perfect_strategies,
            'recommendations': self.generate_recommendations()
        }
        
        with open('100_percent_exploration_report.json', 'w') as f:
            json.dump(report, f, indent=2)
        
        print(f"\n📄 Full report saved to: 100_percent_exploration_report.json")
    
    def generate_recommendations(self):
        """Generate recommendations based on exploration"""
        
        recommendations = []
        
        # Find best balanced strategy
        balanced_results = [r for r in self.exploration_results 
                          if r['metrics']['valid_protection'] >= 99 
                          and r['metrics']['fp_detection'] >= 60]
        
        if balanced_results:
            best_balanced = max(balanced_results, key=lambda x: x['metrics']['fp_detection'])
            recommendations.append({
                'type': 'balanced',
                'strategy': best_balanced['strategy'],
                'reasoning': f"Achieves {best_balanced['metrics']['valid_protection']:.1f}% valid protection with {best_balanced['metrics']['fp_detection']:.1f}% FP detection"
            })
        
        # Find absolute safety strategy
        perfect_strategies = [r for r in self.exploration_results 
                            if r['metrics']['valid_protection'] == 100.0]
        
        if perfect_strategies:
            best_perfect = max(perfect_strategies, key=lambda x: x['metrics']['fp_detection'])
            recommendations.append({
                'type': 'maximum_safety',
                'strategy': best_perfect['strategy'],
                'reasoning': f"Guarantees 100% valid protection but only {best_perfect['metrics']['fp_detection']:.1f}% FP detection"
            })
        
        recommendations.append({
            'type': 'production',
            'strategy': 'current_round3',
            'reasoning': "Current Round 3 solution (99.1% valid, 81.3% FP) offers best practical balance"
        })
        
        return recommendations

def main():
    explorer = ValidProtectionExplorer()
    explorer.explore_100_percent_protection()

if __name__ == "__main__":
    main()