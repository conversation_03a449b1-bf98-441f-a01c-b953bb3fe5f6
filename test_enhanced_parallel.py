#!/usr/bin/env python3
"""
Test enhanced prompt in parallel on remaining cases
Split workload across multiple processes
"""

import json
import base64
import requests
import os
from datetime import datetime
import time
import multiprocessing as mp
from functools import partial

VLM_API_URL = "http://**************:9500/v1/chat/completions"
VLM_MODEL = "VLM-38B-AWQ"

def encode_image(image_path):
    """Encode image to base64"""
    try:
        if os.path.exists(image_path):
            with open(image_path, 'rb') as f:
                return base64.b64encode(f.read()).decode('utf-8')
    except:
        pass
    return None

def process_case(case, enhanced_prompt):
    """Process a single case"""
    try:
        # Encode images
        source_b64 = encode_image(case['source_image'])
        cropped_b64 = encode_image(case['cropped_image'])
        
        if not source_b64 or not cropped_b64:
            return None
        
        # Create payload
        payload = {
            "model": VLM_MODEL,
            "messages": [{
                "role": "user",
                "content": [
                    {"type": "text", "text": enhanced_prompt},
                    {"type": "text", "text": "\n\nSOURCE IMAGE (full context):"},
                    {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{source_b64}"}},
                    {"type": "text", "text": "\n\nCROPPED IMAGE (area of concern):"},
                    {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{cropped_b64}"}}
                ]
            }],
            "temperature": 0.1,
            "max_tokens": 200
        }
        
        # Call VLM with retries
        for attempt in range(3):
            try:
                response = requests.post(VLM_API_URL, json=payload, timeout=20)
                if response.status_code == 200:
                    result = response.json()
                    vlm_response = result['choices'][0]['message']['content'].strip()
                    
                    # Parse response
                    is_fp = 'YES' in vlm_response.upper()
                    entity_type = 'UNKNOWN'
                    ppe_status = 'N/A'
                    confidence = 0
                    
                    # Extract entity
                    if 'Entity Type:' in vlm_response:
                        line = [l for l in vlm_response.split('\n') if 'Entity Type:' in l]
                        if line:
                            entity = line[0].split('Entity Type:')[1].strip().split()[0]
                            entity_type = entity
                    
                    # Extract PPE
                    if 'PPE Status:' in vlm_response:
                        line = [l for l in vlm_response.split('\n') if 'PPE Status:' in l]
                        if line:
                            ppe_status = line[0].split('PPE Status:')[1].strip()
                    
                    # Extract confidence
                    if 'Confidence:' in vlm_response:
                        line = [l for l in vlm_response.split('\n') if 'Confidence:' in l]
                        if line:
                            try:
                                conf_str = line[0].split('Confidence:')[1].strip()
                                confidence = int(conf_str.rstrip('%'))
                            except:
                                pass
                    
                    # Check if correct
                    correct = is_fp == case['is_false_positive']
                    
                    return {
                        'case_number': case['case_number'],
                        'infringement_type': case['infringement_type'],
                        'actual_fp': case['is_false_positive'],
                        'predicted_fp': is_fp,
                        'correct': correct,
                        'entity_type': entity_type,
                        'ppe_status': ppe_status,
                        'confidence': confidence,
                        'remarks': case.get('remarks', '')
                    }
                    
            except Exception as e:
                if attempt < 2:
                    time.sleep(2 ** attempt)
                continue
                    
    except Exception as e:
        print(f"Error processing {case['case_number']}: {e}")
    
    return None

def worker(cases_chunk, worker_id, enhanced_prompt):
    """Worker process to handle a chunk of cases"""
    results = []
    
    for i, case in enumerate(cases_chunk):
        print(f"Worker {worker_id}: Processing case {i+1}/{len(cases_chunk)}")
        result = process_case(case, enhanced_prompt)
        if result:
            results.append(result)
        time.sleep(0.5)  # Rate limit
    
    return results

def main():
    print("PARALLEL ENHANCED PROMPT TESTING")
    print("="*60)
    
    # Load enhanced prompt
    with open('intelligent_prompt_enhanced_structures.txt', 'r') as f:
        enhanced_prompt = f.read()
    
    # Load all data
    with open('valo_batch_round3_complete.json', 'r') as f:
        data = json.load(f)
    
    all_cases = data['results']
    
    # Load existing results
    completed_cases = set()
    existing_results = []
    
    if os.path.exists('enhanced_1250_progress.json'):
        with open('enhanced_1250_progress.json', 'r') as f:
            progress = json.load(f)
            existing_results = progress.get('results', [])
            completed_cases = set(r['case_number'] for r in existing_results)
    
    # Get remaining cases
    remaining_cases = [c for c in all_cases if c['case_number'] not in completed_cases]
    
    print(f"Total cases: {len(all_cases)}")
    print(f"Already completed: {len(completed_cases)}")
    print(f"Remaining: {len(remaining_cases)}")
    
    if not remaining_cases:
        print("All cases already completed!")
        return
    
    # Split into chunks for parallel processing
    num_workers = min(4, mp.cpu_count())  # Use up to 4 workers
    chunk_size = len(remaining_cases) // num_workers + 1
    chunks = [remaining_cases[i:i+chunk_size] for i in range(0, len(remaining_cases), chunk_size)]
    
    print(f"\nStarting {num_workers} parallel workers...")
    print(f"Each worker processing ~{chunk_size} cases")
    
    # Create pool and process
    with mp.Pool(num_workers) as pool:
        # Create partial function with enhanced prompt
        worker_func = partial(worker, enhanced_prompt=enhanced_prompt)
        
        # Map chunks to workers
        chunk_results = pool.starmap(worker_func, [(chunk, i+1) for i, chunk in enumerate(chunks)])
    
    # Combine all results
    all_new_results = []
    for chunk_result in chunk_results:
        all_new_results.extend(chunk_result)
    
    print(f"\n\nCompleted {len(all_new_results)} new cases")
    
    # Merge with existing results
    final_results = existing_results + all_new_results
    
    # Save final results
    final_data = {
        'timestamp': datetime.now().isoformat(),
        'total_cases': len(final_results),
        'results': final_results
    }
    
    with open('enhanced_1250_parallel_results.json', 'w') as f:
        json.dump(final_data, f, indent=2)
    
    # Generate summary
    correct = sum(r['correct'] for r in final_results)
    accuracy = correct / len(final_results) * 100
    
    fps = [r for r in final_results if r['actual_fp']]
    fp_detected = sum(r['predicted_fp'] for r in fps)
    fp_rate = fp_detected / len(fps) * 100 if fps else 0
    
    print(f"\nFINAL RESULTS:")
    print(f"Total tested: {len(final_results)}")
    print(f"Accuracy: {accuracy:.1f}%")
    print(f"FP Detection: {fp_rate:.1f}% (Baseline: 76.3%)")
    print(f"Improvement: {fp_rate - 76.3:+.1f}%")
    
    print(f"\nResults saved to: enhanced_1250_parallel_results.json")

if __name__ == "__main__":
    main()