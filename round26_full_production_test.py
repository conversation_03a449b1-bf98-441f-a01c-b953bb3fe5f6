#!/usr/bin/env python3
"""
Round 26: Full Production Test - Image Only Analysis
Testing 200 cases properly to understand true production performance
"""
import asyncio
import json
import aiohttp
import logging
from datetime import datetime
import base64
import os

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def analyze_image_only(session, case, vlm_endpoint):
    """Analyze using ONLY image - true production scenario"""
    try:
        # Read and encode image
        if not os.path.exists(case['cropped_image']):
            return None
            
        with open(case['cropped_image'], 'rb') as img_file:
            image_data = base64.b64encode(img_file.read()).decode('utf-8')
        
        # Production prompt - NO access to remarks
        prompt = """SAFETY VIOLATION ANALYSIS

This image was automatically flagged by our system for potential PPE non-compliance.

Your task:
1. Identify if there are any workers/people in the image
2. If workers are present, check if they are wearing proper PPE (helmet, safety vest, etc.)
3. Determine if this is a FALSE POSITIVE

Important: Workers wearing proper PPE are COMPLIANT and should not be flagged as violations.

Based on the image alone, is this a FALSE POSITIVE alert?
Answer: YES (false positive) or NO (valid violation)"""

        payload = {
            "model": "VLM-38B-AWQ",
            "messages": [{
                "role": "user",
                "content": [
                    {"type": "text", "text": prompt},
                    {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{image_data}"}}
                ]
            }],
            "temperature": 0.1,
            "max_tokens": 200
        }
        
        async with session.post(vlm_endpoint, json=payload) as response:
            if response.status == 200:
                result = await response.json()
                content = result['choices'][0]['message']['content']
                
                # Conservative parsing - look for clear YES
                decision = content.upper().startswith("YES") or "YES" in content.upper()[:20]
                
                return {
                    'case_number': case['case_number'],
                    'is_false_positive': case['is_false_positive'],
                    'predicted_fp': decision,
                    'confidence': 'high' if decision and "clearly" in content.lower() else 'medium',
                    'hidden_remarks': case.get('remarks', ''),  # For analysis only
                    'vlm_reasoning': content[:200]
                }
            return None
            
    except Exception as e:
        logger.error(f"Error: {e}")
        return None

async def main():
    vlm_endpoint = "http://100.106.127.35:9500/v1/chat/completions"
    
    # Load data
    with open('valo_batch_round3_complete.json', 'r') as f:
        data = json.load(f)
        all_cases = data['results']
    
    # Take 200 representative cases
    test_cases = all_cases[:200]
    
    logger.info("="*80)
    logger.info("ROUND 26: PRODUCTION REALITY TEST")
    logger.info("Image-Only Analysis (No Human Remarks)")
    logger.info("="*80)
    
    results = []
    ppe_pattern_cases = []
    
    async with aiohttp.ClientSession() as session:
        for i in range(0, len(test_cases), 5):
            batch = test_cases[i:i+5]
            tasks = [analyze_image_only(session, case, vlm_endpoint) for case in batch]
            batch_results = await asyncio.gather(*tasks)
            
            for result in batch_results:
                if result:
                    results.append(result)
                    # Track PPE cases for analysis
                    if any(kw in result['hidden_remarks'].upper() for kw in ['FULL PPE', 'PROPER PPE']):
                        ppe_pattern_cases.append(result)
            
            if len(results) % 20 == 0:
                tp = sum(1 for r in results if r['is_false_positive'] and r['predicted_fp'])
                fp_total = sum(1 for r in results if r['is_false_positive'])
                rate = (tp / fp_total * 100) if fp_total > 0 else 0
                logger.info(f"Progress: {len(results)}/200 | FP Detection: {rate:.1f}%")
                
            await asyncio.sleep(0.2)
    
    # Analysis
    tp = sum(1 for r in results if r['is_false_positive'] and r['predicted_fp'])
    tn = sum(1 for r in results if not r['is_false_positive'] and not r['predicted_fp'])
    fp = sum(1 for r in results if not r['is_false_positive'] and r['predicted_fp'])
    fn = sum(1 for r in results if r['is_false_positive'] and not r['predicted_fp'])
    
    fp_total = sum(1 for r in results if r['is_false_positive'])
    valid_total = sum(1 for r in results if not r['is_false_positive'])
    
    fp_rate = (tp / fp_total * 100) if fp_total > 0 else 0
    valid_rate = (tn / valid_total * 100) if valid_total > 0 else 100
    
    # PPE-specific analysis
    ppe_detected = sum(1 for r in ppe_pattern_cases if r['predicted_fp'])
    ppe_total = len(ppe_pattern_cases)
    ppe_detection_rate = (ppe_detected / ppe_total * 100) if ppe_total > 0 else 0
    
    logger.info("\n" + "="*80)
    logger.info("FINAL RESULTS - PRODUCTION SCENARIO")
    logger.info("="*80)
    logger.info(f"Overall FP Detection: {fp_rate:.1f}%")
    logger.info(f"Valid Protection: {valid_rate:.1f}%")
    logger.info(f"\nPPE-Specific Cases:")
    logger.info(f"  Total PPE cases: {ppe_total}")
    logger.info(f"  Correctly detected: {ppe_detected} ({ppe_detection_rate:.1f}%)")
    logger.info(f"\nConfusion Matrix:")
    logger.info(f"  True Positives: {tp}")
    logger.info(f"  True Negatives: {tn}")
    logger.info(f"  False Positives: {fp}")
    logger.info(f"  False Negatives: {fn}")
    
    # Sample analysis
    logger.info("\n" + "-"*40)
    logger.info("Sample PPE cases that FAILED without remarks:")
    failed_ppe = [r for r in ppe_pattern_cases if not r['predicted_fp']][:5]
    for case in failed_ppe:
        logger.info(f"\n{case['case_number']}: {case['hidden_remarks']}")
        logger.info(f"VLM said: {case['vlm_reasoning'][:100]}...")
    
    # Final verdict
    logger.info("\n" + "="*80)
    logger.info("PRODUCTION READINESS VERDICT:")
    if fp_rate >= 70:
        logger.info(f"✅ PASSES: {fp_rate:.1f}% exceeds 70% target")
        logger.info("Image-only analysis is production ready!")
    else:
        logger.info(f"❌ FAILS: {fp_rate:.1f}% below 70% target")
        logger.info("Round 6 success was inflated by human remarks")
        logger.info("Need different approach for production")
    logger.info("="*80)
    
    # Save results
    with open('round26_production_test_results.json', 'w') as f:
        json.dump({
            'round': 26,
            'test_type': 'production_image_only',
            'stats': {
                'fp_detection_rate': fp_rate,
                'valid_protection_rate': valid_rate,
                'ppe_detection_rate': ppe_detection_rate,
                'total_cases': len(results)
            },
            'comparison': {
                'round6_with_remarks': 92.6,
                'round26_no_remarks': fp_rate,
                'difference': 92.6 - fp_rate
            },
            'verdict': 'PASS' if fp_rate >= 70 else 'FAIL',
            'timestamp': datetime.now().isoformat()
        }, f, indent=2)

if __name__ == "__main__":
    asyncio.run(main())