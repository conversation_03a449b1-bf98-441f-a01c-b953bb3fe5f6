#!/usr/bin/env python3
"""
Create a SAFETY-FIRST prompt that prioritizes catching all violations
Better to have false positives than miss real safety issues
"""

def create_safety_first_prompt():
    """Create an ultra-conservative safety prompt"""
    
    prompt = """SAFETY VIOLATION DETECTION - ZERO TOLERANCE

⚠️ CRITICAL SAFETY SYSTEM ⚠️
When uncertain, ALWAYS flag as potential violation for human review.
Missing a real violation could result in injury or death.

ANALYZE BOTH IMAGES:
- SOURCE: Full context
- CROPPED: Specific area flagged by system

DETECTION RULES:

1. STRUCTURE DETECTION (Requires VERY HIGH confidence >95%):
   Only mark as structure if you are ABSOLUTELY CERTAIN:
   - NO human features visible anywhere
   - ONLY mechanical/industrial components
   - Clear geometric patterns with NO organic shapes
   - Confirmed as: crane beams, vessel rails, spreader frame, etc.

2. PERSON DETECTION (Any confidence):
   If ANY of these are present → PERSON:
   - Human-like shape (even partial)
   - Clothing or fabric texture
   - Helmet or vest shape
   - Any organic form
   - Movement blur suggesting human
   - ANYTHING that could be a person

3. VIOLATION ASSESSMENT:
   
   A) CLEAR STRUCTURE (>95% confidence):
      → FALSE POSITIVE only if 100% certain no person
   
   B) PERSON DETECTED:
      Check ALL violation types:
      
      PPE VIOLATIONS:
      □ Missing helmet/hard hat
      □ Missing safety vest
      □ Vest not properly worn/fastened
      □ Missing life jacket (vessel crew)
      □ Missing required equipment
      
      BEHAVIORAL VIOLATIONS:
      □ Mobile phone/device use
      □ Missing GO/STOP bat
      □ In restricted area
      □ Unsafe operations
      □ One person doing two-person job
      □ Not maintaining safe distance
      □ Spreader ride
      □ Any questionable behavior
   
   C) UNCLEAR/UNCERTAIN:
      → ALWAYS mark as VALID VIOLATION

CRITICAL DECISION RULES:

1. If person detected → Check EVERYTHING
2. If ANY violation found → VALID VIOLATION
3. If uncertain about anything → VALID VIOLATION
4. Only mark FALSE POSITIVE if:
   - 95%+ confident it's a structure AND
   - ZERO possibility of person present AND
   - No safety concerns whatsoever

Remember: This system protects lives. When in doubt, flag it.

OUTPUT:
FALSE POSITIVE: [YES/NO]
Entity: [PERSON/STRUCTURE/UNCERTAIN]
Confidence: [%]
Violations Found: [List any violations or concerns]
Safety Action: [Flag for review/Clear]"""

    return prompt

def create_violation_checklist():
    """Create comprehensive violation checklist"""
    
    checklist = {
        "PPE_violations": [
            "no_helmet",
            "no_vest", 
            "vest_not_fastened",
            "no_life_jacket",
            "missing_safety_equipment",
            "improper_ppe_wear"
        ],
        "behavioral_violations": [
            "mobile_phone_use",
            "missing_go_stop_bat",
            "restricted_area_entry",
            "unsafe_operations",
            "one_man_lashing",
            "insufficient_distance",
            "spreader_ride",
            "not_following_procedure",
            "distracted_behavior",
            "rushing_shortcuts"
        ],
        "context_violations": [
            "wrong_location",
            "wrong_time",
            "unauthorized_person",
            "missing_spotter",
            "no_safety_barriers"
        ]
    }
    
    return checklist

def main():
    # Create safety-first prompt
    safety_prompt = create_safety_first_prompt()
    
    # Save prompt
    with open('safety_first_prompt.txt', 'w') as f:
        f.write(safety_prompt)
    
    # Create violation checklist
    checklist = create_violation_checklist()
    
    import json
    with open('violation_checklist.json', 'w') as f:
        json.dump(checklist, f, indent=2)
    
    # Create configuration for safety-first approach
    config = {
        "mode": "safety_first",
        "structure_confidence_threshold": 95,
        "person_confidence_threshold": 20,
        "default_action": "flag_for_review",
        "violation_sensitivity": "maximum",
        "priorities": [
            "protect_all_valid_violations",
            "minimize_missed_violations",
            "accept_higher_false_positives"
        ]
    }
    
    with open('safety_first_config.json', 'w') as f:
        json.dump(config, f, indent=2)
    
    print("SAFETY-FIRST PROMPT CREATED")
    print("="*60)
    print("\nKey features:")
    print("1. 95% confidence required for structure detection")
    print("2. Any uncertainty → Valid violation")
    print("3. Comprehensive violation checklist")
    print("4. Zero tolerance approach")
    print("\nFiles created:")
    print("- safety_first_prompt.txt")
    print("- violation_checklist.json")
    print("- safety_first_config.json")
    
    print("\n⚠️  IMPORTANT:")
    print("This prompt prioritizes safety over false positive reduction.")
    print("Expected: Higher FP rate but near 100% valid violation protection")
    
    # Create test cases for validation
    test_cases = {
        "must_catch": [
            "Person without helmet",
            "Mobile phone use",
            "Missing GO/STOP bat",
            "Vest not fastened",
            "One person lashing",
            "Spreader ride",
            "No life jacket on vessel"
        ],
        "ok_to_flag": [
            "Unclear image",
            "Partial person visible",
            "Uncertain structure",
            "Mixed person/structure"
        ],
        "only_clear_structures": [
            "Crane beams only",
            "Empty spreader frame",
            "Vessel railings",
            "Camera structure"
        ]
    }
    
    with open('safety_validation_cases.json', 'w') as f:
        json.dump(test_cases, f, indent=2)

if __name__ == "__main__":
    main()