#!/usr/bin/env python3
"""
Advanced Auto-Learning System for VALO AI-FARM
Runs multiple rounds (5+) with progressive learning from results
"""

import json
import asyncio
import logging
from datetime import datetime
from typing import Dict, List
import pandas as pd
from collections import defaultdict
import sys
import os

sys.path.append('/home/<USER>/VALO_AI-FARM_2025/backend')
from app.services.valo_batch_processor import VALOBatchProcessor

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class AdvancedAutoLearningProcessor(VALOBatchProcessor):
    def __init__(self):
        super().__init__()
        # Override conservative defaults with more balanced approach
        self.chunk_size = 10  # Larger chunks for faster processing
        self.delay_between_chunks = 1  # Shorter delay
        
        # Learning history
        self.round_history = []
        self.best_round = None
        self.best_score = 0
        
    def calculate_round_score(self, stats: Dict) -> float:
        """Calculate a balanced score for the round"""
        # Prioritize valid protection but also value FP detection
        valid_protection = stats.get('valid_protection_rate', 0)
        fp_detection = stats.get('fp_detection_rate', 0)
        
        # 70% weight on valid protection, 30% on FP detection
        score = (valid_protection * 0.7) + (fp_detection * 0.3)
        return score
    
    def analyze_round_results(self, round_num: int, results: List[Dict]) -> Dict:
        """Deep analysis of round results to extract learning insights"""
        insights = {
            'camera_performance': defaultdict(lambda: {'total': 0, 'correct': 0, 'too_conservative': 0}),
            'infringement_performance': defaultdict(lambda: {'total': 0, 'correct': 0, 'too_conservative': 0}),
            'confidence_distribution': defaultdict(list),
            'person_detection_accuracy': {'total': 0, 'correct': 0},
            'false_negative_cases': [],
            'overly_conservative_cases': []
        }
        
        for result in results:
            camera = result['camera_id']
            inf_type = result['infringement_type']
            
            # Track camera performance
            insights['camera_performance'][camera]['total'] += 1
            if result.get('correct_prediction', False):
                insights['camera_performance'][camera]['correct'] += 1
            
            # Track infringement type performance
            insights['infringement_performance'][inf_type]['total'] += 1
            if result.get('correct_prediction', False):
                insights['infringement_performance'][inf_type]['correct'] += 1
            
            # Check if being too conservative on false positives
            if (result['is_false_positive'] and 
                not result.get('is_false_positive_predicted', False) and
                result.get('confidence', 0) < 95):
                insights['camera_performance'][camera]['too_conservative'] += 1
                insights['infringement_performance'][inf_type]['too_conservative'] += 1
                insights['overly_conservative_cases'].append(result['case_number'])
            
            # Track confidence distribution
            insights['confidence_distribution'][inf_type].append(result.get('confidence', 0))
            
            # Track person detection accuracy
            if result.get('person_detected') in ['yes', 'no']:
                insights['person_detection_accuracy']['total'] += 1
                # If it's a false positive and no person detected, that's correct
                if (result['is_false_positive'] and 
                    result.get('person_detected') == 'no'):
                    insights['person_detection_accuracy']['correct'] += 1
            
            # Track any false negatives (valid cases predicted as false positives)
            if (not result['is_false_positive'] and 
                result.get('is_false_positive_predicted', False)):
                insights['false_negative_cases'].append(result['case_number'])
        
        return insights
    
    def generate_improved_prompt(self, case: Dict, round_num: int, insights: Dict) -> str:
        """Generate progressively smarter prompts based on learning"""
        
        # Calculate dynamic thresholds based on round and performance
        base_threshold = self.learning_params['confidence_thresholds'].get(
            case['infringement_type'], 90
        )
        
        # After round 1, start being less conservative if no false negatives
        if round_num > 1 and not insights.get('false_negative_cases', []):
            # Gradually reduce threshold each round if performing well
            threshold_reduction = min((round_num - 1) * 5, 20)  # Max 20% reduction
            adjusted_threshold = max(base_threshold - threshold_reduction, 70)
        else:
            adjusted_threshold = base_threshold
        
        prompt = f"""🔍 AI-FARM INTELLIGENT ANALYSIS - ROUND {round_num}/5+

LEARNING STATUS: Processed {len(self.round_history)} rounds with continuous improvement

Case Information:
- Case: {case['case_number']}
- Terminal: {case['terminal']}
- Camera: {case['camera_id']}
- Violation Type: {case['infringement_type']}
- Human Assessment: {case['alert_status']}
- Human Notes: {case.get('remarks', 'None')}

DYNAMIC CONFIDENCE THRESHOLD: {adjusted_threshold}% (Adjusted from {base_threshold}%)
"""

        # Add camera-specific insights
        if case['camera_id'] in insights.get('camera_performance', {}):
            cam_perf = insights['camera_performance'][case['camera_id']]
            if cam_perf['too_conservative'] > cam_perf['total'] * 0.5:
                prompt += f"\n📷 CAMERA INSIGHT: This camera is being too conservative. Be more confident in equipment identification."
        
        # Add round-specific learning
        if round_num > 1:
            prev_insights = self.round_history[-1].get('insights', {})
            total_cases = self.round_history[-1].get('total_cases', 1)
            overly_conservative_rate = len(prev_insights.get('overly_conservative_cases', [])) / total_cases * 100
            
            if overly_conservative_rate > 50:
                prompt += f"""
                
⚡ LEARNING UPDATE: Previous round was {overly_conservative_rate:.1f}% too conservative.
- If equipment/structure is CLEARLY visible → Be confident it's a false positive
- If person is in FULL PPE (helmet + vest visible) → Likely false positive
- Trust the person detection more strongly this round
"""

        prompt += f"""

INTELLIGENT ANALYSIS PROTOCOL:

Step 1: PERSON DETECTION (Primary Signal)
- No person clearly visible → FALSE POSITIVE (High confidence)
- Person clearly visible → Check violation details
- Unclear/Partial → Default to review (but consider context)

Step 2: CONTEXTUAL ANALYSIS
- Equipment visible (crane, spreader, structure) → Likely FALSE POSITIVE
- Person in full PPE for PPE violation → Likely FALSE POSITIVE
- Match human notes pattern → Trust human assessment

Step 3: CONFIDENCE-BASED DECISION
- Confidence ≥ {adjusted_threshold}% → Make decisive call
- Confidence < {adjusted_threshold}% → Flag for review
- Round {round_num} adjustment: Be {10 + (round_num * 5)}% more decisive

DECISION PRIORITIES:
1. Never miss valid violations (but {len(insights.get('false_negative_cases', []))} so far)
2. Improve false positive detection (currently at {self.round_history[-1]['fp_detection_rate']:.1f}% - need improvement)

Output Format:
PERSON_DETECTED: [yes/no/possibly]
CONFIDENCE: [0-100] (be more confident this round)
IS_FALSE_POSITIVE: [true/false]
SAFETY_DECISION: [FLAG_FOR_REVIEW/DISMISS_WITH_CAUTION]
REASONING: [Clear explanation with confidence]
"""
        
        return prompt
    
    async def run_advanced_learning(self, num_rounds: int = 5):
        """Run advanced multi-round learning"""
        logger.info(f"🚀 STARTING ADVANCED AUTO-LEARNING WITH {num_rounds} ROUNDS")
        
        # Load all cases
        self.all_cases = self.load_all_cases()
        logger.info(f"Loaded {len(self.all_cases)} cases for processing")
        
        # Check if we have partial results from Round 1
        existing_results = None
        if os.path.exists("/home/<USER>/VALO_AI-FARM_2025/valo_batch_round1_intermediate.json"):
            with open("/home/<USER>/VALO_AI-FARM_2025/valo_batch_round1_intermediate.json", 'r') as f:
                existing_data = json.load(f)
                existing_results = existing_data.get('detailed_results', [])
                logger.info(f"Found existing Round 1 results with {len(existing_results)} cases")
        
        # Process multiple rounds
        for round_num in range(1, num_rounds + 1):
            logger.info(f"\n{'='*60}")
            logger.info(f"ROUND {round_num} OF {num_rounds}")
            logger.info(f"{'='*60}")
            
            # Use existing results for Round 1 if available
            if round_num == 1 and existing_results:
                round_results = existing_results
                # Process remaining cases
                processed_cases = {r['case_number'] for r in existing_results}
                remaining_cases = [c for c in self.all_cases if c['case_number'] not in processed_cases]
                
                if remaining_cases:
                    logger.info(f"Processing {len(remaining_cases)} remaining cases from Round 1")
                    for i in range(0, len(remaining_cases), self.chunk_size):
                        chunk = remaining_cases[i:i+self.chunk_size]
                        chunk_results = await self.process_chunk(chunk, round_num, i//self.chunk_size, len(remaining_cases)//self.chunk_size)
                        round_results.extend(chunk_results)
                        await asyncio.sleep(self.delay_between_chunks)
            else:
                # Fresh round processing
                round_results = []
                total_chunks = (len(self.all_cases) + self.chunk_size - 1) // self.chunk_size
                
                # Get insights from previous round
                insights = {}
                if self.round_history:
                    insights = self.analyze_round_results(round_num - 1, self.round_history[-1]['results'])
                
                for chunk_idx in range(total_chunks):
                    start_idx = chunk_idx * self.chunk_size
                    end_idx = min(start_idx + self.chunk_size, len(self.all_cases))
                    chunk = self.all_cases[start_idx:end_idx]
                    
                    # Process with improved prompts
                    chunk_results = []
                    for case in chunk:
                        prompt = self.generate_improved_prompt(case, round_num, insights)
                        result = await self.analyze_with_vlm(case['cropped_image'], prompt, case)
                        
                        # Combine with case info
                        full_result = {**case, **result, 'round': round_num}
                        
                        # Calculate accuracy
                        if case['is_false_positive']:
                            full_result['correct_prediction'] = result['is_false_positive_predicted']
                        else:
                            full_result['correct_prediction'] = not result['is_false_positive_predicted']
                            full_result['valid_case_protected'] = not result['is_false_positive_predicted']
                        
                        chunk_results.append(full_result)
                    
                    round_results.extend(chunk_results)
                    
                    # Progress update
                    logger.info(f"Round {round_num} - Chunk {chunk_idx + 1}/{total_chunks} completed")
                    
                    if chunk_idx < total_chunks - 1:
                        await asyncio.sleep(self.delay_between_chunks)
            
            # Calculate round statistics
            valid_cases = [r for r in round_results if not r['is_false_positive']]
            fp_cases = [r for r in round_results if r['is_false_positive']]
            
            valid_protected = sum(1 for r in valid_cases if r.get('valid_case_protected', True))
            fp_detected = sum(1 for r in fp_cases if r.get('is_false_positive_predicted', False))
            
            round_stats = {
                'round': round_num,
                'total_cases': len(round_results),
                'valid_protection_rate': (valid_protected / len(valid_cases) * 100) if valid_cases else 100,
                'fp_detection_rate': (fp_detected / len(fp_cases) * 100) if fp_cases else 0,
                'valid_protected': valid_protected,
                'fp_detected': fp_detected,
                'results': round_results,
                'insights': self.analyze_round_results(round_num, round_results)
            }
            
            # Calculate score
            score = self.calculate_round_score(round_stats)
            round_stats['score'] = score
            
            self.round_history.append(round_stats)
            
            # Track best round
            if score > self.best_score:
                self.best_score = score
                self.best_round = round_num
            
            # Save round results
            await self.save_round_results(round_num, round_stats)
            
            logger.info(f"\nROUND {round_num} COMPLETE:")
            logger.info(f"- Valid Protection: {round_stats['valid_protection_rate']:.1f}%")
            logger.info(f"- FP Detection: {round_stats['fp_detection_rate']:.1f}%")
            logger.info(f"- Score: {score:.2f}")
            
            # Early stopping if we achieve perfect valid protection and good FP detection
            if round_stats['valid_protection_rate'] == 100 and round_stats['fp_detection_rate'] > 70:
                logger.info("🎯 Achieved optimal performance - stopping early")
                break
        
        # Final results
        await self.save_final_results()
    
    async def save_round_results(self, round_num: int, stats: Dict):
        """Save results for each round"""
        filename = f"/home/<USER>/VALO_AI-FARM_2025/valo_advanced_round{round_num}_results.json"
        
        # Don't save full results in round files to avoid huge files
        save_data = {
            'round': round_num,
            'timestamp': datetime.now().isoformat(),
            'summary': {
                'total_cases': stats['total_cases'],
                'valid_protection_rate': stats['valid_protection_rate'],
                'fp_detection_rate': stats['fp_detection_rate'],
                'score': stats['score']
            },
            'insights': stats['insights']
        }
        
        with open(filename, 'w') as f:
            json.dump(save_data, f, indent=2)
        
        logger.info(f"Round {round_num} results saved to {filename}")
    
    async def save_final_results(self):
        """Save comprehensive final results"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"/home/<USER>/VALO_AI-FARM_2025/valo_advanced_final_results_{timestamp}.json"
        
        # Get best round results
        best_round_data = self.round_history[self.best_round - 1]
        
        # Calculate business impact from best round
        fp_rate = best_round_data['fp_detection_rate'] / 100
        annual_fp = 14484  # From dataset analysis
        
        final_results = {
            'processing_summary': {
                'total_rounds': len(self.round_history),
                'best_round': self.best_round,
                'best_score': self.best_score,
                'final_valid_protection_rate': best_round_data['valid_protection_rate'],
                'final_fp_detection_rate': best_round_data['fp_detection_rate']
            },
            'round_progression': [
                {
                    'round': r['round'],
                    'valid_protection': r['valid_protection_rate'],
                    'fp_detection': r['fp_detection_rate'],
                    'score': r['score']
                }
                for r in self.round_history
            ],
            'business_impact': {
                'annual_false_positives': annual_fp,
                'annual_fp_filtered': int(annual_fp * fp_rate),
                'annual_time_saved_minutes': int(annual_fp * fp_rate * 5),
                'annual_cost_savings': int(annual_fp * fp_rate * 5),
                'improvement_over_baseline': f"{best_round_data['fp_detection_rate'] / 1.27:.1f}x"
            },
            'key_learnings': best_round_data['insights'],
            'timestamp': timestamp
        }
        
        with open(filename, 'w') as f:
            json.dump(final_results, f, indent=2)
        
        # Also save to Redis for UI access
        self.redis_client.setex(
            'valo_advanced_results',
            3600,
            json.dumps(final_results)
        )
        
        logger.info(f"\n{'='*60}")
        logger.info(f"ADVANCED AUTO-LEARNING COMPLETE")
        logger.info(f"Best Round: {self.best_round} with score {self.best_score:.2f}")
        logger.info(f"Final Valid Protection: {best_round_data['valid_protection_rate']:.1f}%")
        logger.info(f"Final FP Detection: {best_round_data['fp_detection_rate']:.1f}%")
        logger.info(f"Results saved to: {filename}")
        logger.info(f"{'='*60}")
        
        return filename


async def main():
    """Run advanced auto-learning"""
    processor = AdvancedAutoLearningProcessor()
    
    try:
        # Run at least 5 rounds
        await processor.run_advanced_learning(num_rounds=5)
        
    except Exception as e:
        logger.error(f"Advanced learning failed: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(main())