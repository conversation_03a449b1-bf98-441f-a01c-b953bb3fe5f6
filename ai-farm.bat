@echo off
REM AI-FARM Application Control Script for Windows
REM One script to start/stop/restart the entire AI-FARM application

setlocal enabledelayedexpansion

REM Configuration
set SCRIPT_DIR=%~dp0
set BACKEND_DIR=%SCRIPT_DIR%backend
set FRONTEND_DIR=%SCRIPT_DIR%frontend
set LOGS_DIR=%SCRIPT_DIR%logs

REM Ensure logs directory exists
if not exist "%LOGS_DIR%" mkdir "%LOGS_DIR%"

REM Function to print colored output
:print_status
echo [AI-FARM] %~1
goto :eof

:print_success
echo [SUCCESS] %~1
goto :eof

:print_warning
echo [WARNING] %~1
goto :eof

:print_error
echo [ERROR] %~1
goto :eof

REM Function to check if command exists
:command_exists
where %~1 >nul 2>&1
goto :eof

REM Function to check if port is available
:port_available
netstat -an | findstr ":%~1 " >nul
if %errorlevel%==0 (
    exit /b 1
) else (
    exit /b 0
)

REM Function to wait for service
:wait_for_service
set url=%~1
set service_name=%~2
set max_attempts=30
set attempt=1

call :print_status "Waiting for %service_name% to be ready..."

:wait_loop
curl -s "%url%" >nul 2>&1
if %errorlevel%==0 (
    call :print_success "%service_name% is ready!"
    exit /b 0
)

echo|set /p="."
timeout /t 2 /nobreak >nul
set /a attempt+=1
if %attempt% leq %max_attempts% goto wait_loop

call :print_error "%service_name% failed to start within 60 seconds"
exit /b 1

REM Function to check prerequisites
:check_prerequisites
call :print_status "Checking prerequisites..."

set missing_deps=0

call :command_exists python
if %errorlevel% neq 0 (
    call :command_exists python3
    if %errorlevel% neq 0 (
        call :print_error "Python is not installed"
        set missing_deps=1
    )
)

call :command_exists node
if %errorlevel% neq 0 (
    call :print_error "Node.js is not installed"
    set missing_deps=1
)

call :command_exists npm
if %errorlevel% neq 0 (
    call :print_error "npm is not installed"
    set missing_deps=1
)

if %missing_deps%==1 (
    call :print_error "Please install missing dependencies"
    exit /b 1
)

call :print_success "All prerequisites are available"
exit /b 0

REM Function to setup environment
:setup_environment
call :print_status "Setting up environment..."

if not exist "%SCRIPT_DIR%.env" (
    call :print_warning ".env file not found, creating from template..."
    copy "%SCRIPT_DIR%.env.example" "%SCRIPT_DIR%.env" >nul
    call :print_warning "Please edit .env file with your VLM API settings before starting"
    exit /b 1
)

call :print_success "Environment is configured"
exit /b 0

REM Function to check if backend is running
:backend_running
curl -s http://localhost:8000/health >nul 2>&1
goto :eof

REM Function to start backend
:start_backend
call :backend_running
if %errorlevel%==0 (
    call :print_success "Backend is already running on http://localhost:8000"
    call :print_status "API Documentation: http://localhost:8000/docs"
    exit /b 0
)

call :print_status "Starting backend..."

cd /d "%BACKEND_DIR%"

REM Create virtual environment if it doesn't exist
if not exist "venv" (
    call :print_status "Creating Python virtual environment..."
    python -m venv venv
)

REM Activate virtual environment
call venv\Scripts\activate.bat

REM Install/update dependencies
call :print_status "Installing backend dependencies..."
pip install -q --upgrade pip

REM Try simple requirements first for Python 3.13 compatibility
pip install -q -r requirements-simple.txt >nul 2>&1
if %errorlevel% neq 0 (
    call :print_warning "Simplified requirements failed, trying full requirements..."
    pip install -q -r requirements.txt
) else (
    call :print_success "Dependencies installed successfully"
)

REM Start backend server
call :print_status "Starting FastAPI server..."
start /b python run.py > "%LOGS_DIR%\backend.log" 2>&1

REM Wait for backend to be ready
call :wait_for_service "http://localhost:8000/health" "Backend API"
if %errorlevel%==0 (
    call :print_success "Backend started successfully on http://localhost:8000"
    call :print_status "API Documentation: http://localhost:8000/docs"
) else (
    call :print_error "Backend failed to start"
    exit /b 1
)
exit /b 0

REM Function to start frontend
:start_frontend
call :print_status "Starting frontend..."

cd /d "%FRONTEND_DIR%"

REM Install dependencies if node_modules doesn't exist
if not exist "node_modules" (
    call :print_status "Installing frontend dependencies..."
    npm install
)

REM Start frontend server
call :print_status "Starting React development server..."
start /b npm start > "%LOGS_DIR%\frontend.log" 2>&1

REM Wait for frontend to be ready
call :wait_for_service "http://localhost:3000" "Frontend"
if %errorlevel%==0 (
    call :print_success "Frontend started successfully on http://localhost:3000"
) else (
    call :print_error "Frontend failed to start"
    exit /b 1
)
exit /b 0

REM Function to start with Docker
:start_docker
call :print_status "Starting with Docker..."

call :command_exists docker
if %errorlevel% neq 0 (
    call :print_error "Docker is not installed"
    exit /b 1
)

call :command_exists docker-compose
if %errorlevel% neq 0 (
    call :print_error "Docker Compose is not installed"
    exit /b 1
)

REM Build and start containers
call :print_status "Building and starting Docker containers..."
docker-compose up -d --build

REM Wait for services
call :print_status "Waiting for services to be ready..."
timeout /t 10 /nobreak >nul

call :wait_for_service "http://localhost:8000/health" "Backend API"
set backend_status=%errorlevel%
call :wait_for_service "http://localhost:3000" "Frontend"
set frontend_status=%errorlevel%

if %backend_status%==0 if %frontend_status%==0 (
    call :print_success "All services started successfully with Docker"
    docker-compose ps
) else (
    call :print_error "Some services failed to start"
    exit /b 1
)
exit /b 0

REM Function to stop services
:stop_services
call :print_status "Stopping AI-FARM services..."

REM Stop Docker containers if running
docker-compose ps 2>nul | findstr "Up" >nul
if %errorlevel%==0 (
    call :print_status "Stopping Docker containers..."
    docker-compose down
)

REM Kill processes
taskkill /f /im python.exe 2>nul >nul
taskkill /f /im node.exe 2>nul >nul

call :print_success "All services stopped"
exit /b 0

REM Function to show status
:show_status
call :print_status "AI-FARM Service Status:"
echo.

REM Check backend
curl -s http://localhost:8000/health >nul 2>&1
if %errorlevel%==0 (
    call :print_success "✓ Backend API running on http://localhost:8000"
    call :print_status "  API Docs: http://localhost:8000/docs"
) else (
    call :print_error "✗ Backend API not running"
)

REM Check frontend
curl -s http://localhost:3000 >nul 2>&1
if %errorlevel%==0 (
    call :print_success "✓ Frontend running on http://localhost:3000"
) else (
    call :print_error "✗ Frontend not running"
)

echo.
call :print_status "Log files located in: %LOGS_DIR%"
exit /b 0

REM Function to show help
:show_help
echo AI-FARM Application Control Script
echo.
echo Usage: %~nx0 [COMMAND]
echo.
echo Commands:
echo   start         Start the complete AI-FARM application
echo   start-docker  Start using Docker Compose
echo   stop          Stop all AI-FARM services
echo   restart       Restart all services
echo   status        Show service status
echo   setup         Setup environment and dependencies
echo   help          Show this help message
echo.
echo Examples:
echo   %~nx0 start                 # Start with native Python/Node.js
echo   %~nx0 start-docker         # Start with Docker
echo   %~nx0 setup               # Setup environment
echo.
echo Services will be available at:
echo   Frontend: http://localhost:3000
echo   Backend:  http://localhost:8000
echo   API Docs: http://localhost:8000/docs
exit /b 0

REM Main script logic
if "%~1"=="" goto show_help
if "%~1"=="help" goto show_help
if "%~1"=="--help" goto show_help
if "%~1"=="-h" goto show_help

if "%~1"=="start" (
    call :print_status "Starting AI-FARM Application"
    echo ==================
    
    call :setup_environment
    if %errorlevel% neq 0 exit /b 1
    
    call :check_prerequisites
    if %errorlevel% neq 0 exit /b 1
    
    call :backend_running
    if %errorlevel% neq 0 (
        call :port_available 8000
        if %errorlevel% neq 0 (
            call :print_error "Port 8000 is already in use by another service"
            exit /b 1
        )
    )
    
    call :port_available 3000
    if %errorlevel% neq 0 (
        call :print_error "Port 3000 is already in use"
        exit /b 1
    )
    
    call :start_backend
    if %errorlevel% neq 0 exit /b 1
    
    call :start_frontend
    if %errorlevel% neq 0 exit /b 1
    
    echo.
    call :print_success "🚀 AI-FARM Application is now running!"
    call :print_status "Frontend: http://localhost:3000"
    call :print_status "Backend:  http://localhost:8000"
    call :print_status "API Docs: http://localhost:8000/docs"
    echo.
    call :print_status "Use '%~nx0 stop' to stop all services"
    call :print_status "Use '%~nx0 status' to check service status"
    call :print_status "Use Ctrl+C to stop this script (services will continue running)"
    
) else if "%~1"=="start-docker" (
    call :print_status "Starting AI-FARM with Docker"
    echo =========================
    
    call :setup_environment
    if %errorlevel% neq 0 exit /b 1
    
    call :start_docker
    if %errorlevel% neq 0 exit /b 1
    
    echo.
    call :print_success "🚀 AI-FARM Application is now running with Docker!"
    call :print_status "Frontend: http://localhost:3000"
    call :print_status "Backend:  http://localhost:8000"
    call :print_status "API Docs: http://localhost:8000/docs"
    
) else if "%~1"=="stop" (
    call :stop_services
    
) else if "%~1"=="restart" (
    call :print_status "Restarting AI-FARM Application"
    call :stop_services
    timeout /t 3 /nobreak >nul
    call "%~f0" start
    
) else if "%~1"=="status" (
    call :show_status
    
) else if "%~1"=="setup" (
    call :check_prerequisites
    call :setup_environment
    call :print_success "Setup completed. Run '%~nx0 start' to start the application"
    
) else (
    call :print_error "Unknown command: %~1"
    echo.
    call :show_help
    exit /b 1
)