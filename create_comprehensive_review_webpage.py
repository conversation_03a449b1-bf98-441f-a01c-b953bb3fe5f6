#!/usr/bin/env python3
"""
Create comprehensive review webpage for all 1250 cases
Includes all VLM data, images, and human review interface
"""

import json
import re
import os
from datetime import datetime
import base64

class ComprehensiveReviewWebpageCreator:
    def __init__(self):
        self.base_dir = "valo_comprehensive_data"
        self.fp_file = f"{self.base_dir}/false_positives/false_positive_analysis_20250725_232934.md"
        self.tp_file = f"{self.base_dir}/true_positives/true_positive_analysis_20250725_232934.md"
        self.output_dir = "valo_review_webpage"
        
        # Create output directory
        os.makedirs(self.output_dir, exist_ok=True)
        
        # Load original CSV data for remarks
        self.original_data = self.load_original_data()
        
    def load_original_data(self):
        """Load original CSV data to get remarks"""
        original_data = {}
        
        # Load from the batch results
        with open('valo_batch_round3_complete.json', 'r') as f:
            batch_data = json.load(f)
            
        for case in batch_data['results']:
            original_data[case['case_number']] = {
                'remark': case.get('remark', 'N/A'),
                'source_image': case['source_image'],
                'cropped_image': case['cropped_image'],
                'is_false_positive': case['is_false_positive']
            }
        
        return original_data
    
    def extract_all_case_data(self, file_path, is_fp=True):
        """Extract comprehensive case data from markdown file"""
        cases = []
        
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        case_sections = content.split('## Case:')[1:]
        
        for section in case_sections:
            lines = section.strip().split('\n')
            if not lines:
                continue
                
            case_number = lines[0].strip()
            
            # Get original data
            orig_data = self.original_data.get(case_number, {})
            
            case_data = {
                'case_number': case_number,
                'is_false_positive': is_fp,
                'case_type': 'FALSE_POSITIVE' if is_fp else 'TRUE_POSITIVE',
                'remark': orig_data.get('remark', 'N/A'),
                'source_image': orig_data.get('source_image', ''),
                'cropped_image': orig_data.get('cropped_image', ''),
                'description': '',
                'confidence_response': '',
                'person_present': False,
                'main_subject': 'UNKNOWN',
                'ppe_compliance': 'NA',
                'helmet_status': 'NA',
                'vest_status': 'NA',
                'description_accuracy': 0,
                'subject_confidence': 0,
                'false_positive_likelihood': 0,
                'safety_violation': 'UNCERTAIN',
                'violation_description': 'NONE',
                'processing_time': 0,
                'timestamp': ''
            }
            
            # Extract description
            desc_start = False
            desc_lines = []
            for i, line in enumerate(lines):
                if '### Comprehensive Description' in line:
                    # Find the description block
                    for j in range(i, len(lines)):
                        if lines[j].strip() == '```' and not desc_start:
                            desc_start = True
                            continue
                        elif lines[j].strip() == '```' and desc_start:
                            break
                        elif desc_start:
                            desc_lines.append(lines[j])
                    break
            
            case_data['description'] = '\n'.join(desc_lines)
            case_data['description_length'] = len(case_data['description'])
            
            # Extract confidence response
            conf_start = False
            conf_lines = []
            for i, line in enumerate(lines):
                if '### Raw Confidence Response' in line or 'Click to expand' in line:
                    # Find the confidence block
                    for j in range(i, len(lines)):
                        if lines[j].strip() == '```' and not conf_start:
                            conf_start = True
                            continue
                        elif lines[j].strip() == '```' and conf_start:
                            break
                        elif conf_start:
                            conf_lines.append(lines[j])
                    break
            
            case_data['confidence_response'] = '\n'.join(conf_lines)
            
            # Extract metrics
            for line in lines:
                if '**Person Present**:' in line:
                    case_data['person_present'] = 'YES' in line
                elif '**Main Subject**:' in line:
                    case_data['main_subject'] = line.split(':', 1)[1].strip()
                elif '**Overall Compliance**:' in line:
                    case_data['ppe_compliance'] = line.split(':', 1)[1].strip()
                elif '**Helmet Status**:' in line:
                    case_data['helmet_status'] = line.split(':', 1)[1].strip()
                elif '**Vest Status**:' in line:
                    case_data['vest_status'] = line.split(':', 1)[1].strip()
                elif '**Description Accuracy**:' in line:
                    try:
                        case_data['description_accuracy'] = int(re.search(r'(\d+)%', line).group(1))
                    except:
                        pass
                elif '**Subject Confidence**:' in line:
                    try:
                        case_data['subject_confidence'] = int(re.search(r'(\d+)%', line).group(1))
                    except:
                        pass
                elif '**False Positive Likelihood**:' in line:
                    try:
                        case_data['false_positive_likelihood'] = int(re.search(r'(\d+)%', line).group(1))
                    except:
                        pass
                elif '**Violation Present**:' in line:
                    case_data['safety_violation'] = line.split(':', 1)[1].strip()
                elif '**Violation Description**:' in line:
                    case_data['violation_description'] = line.split(':', 1)[1].strip()
                elif '**Processing Time**:' in line:
                    try:
                        case_data['processing_time'] = float(re.search(r'([\d.]+)\s*seconds', line).group(1))
                    except:
                        pass
                elif '**Timestamp**:' in line:
                    case_data['timestamp'] = line.split(':', 1)[1].strip()
            
            cases.append(case_data)
        
        return cases
    
    def estimate_tokens(self, text):
        """Estimate token count (rough approximation)"""
        # Rough estimation: ~4 characters per token
        return len(text) // 4
    
    def create_webpage(self):
        """Create the comprehensive review webpage"""
        print("="*80)
        print("🌐 CREATING COMPREHENSIVE REVIEW WEBPAGE")
        print("="*80)
        
        # Extract all cases
        print("Extracting case data...")
        fp_cases = self.extract_all_case_data(self.fp_file, is_fp=True)
        tp_cases = self.extract_all_case_data(self.tp_file, is_fp=False)
        
        all_cases = fp_cases + tp_cases
        print(f"✓ Extracted {len(all_cases)} cases")
        
        # Prepare VLM query templates
        vlm_description_query = """Analyze this safety alert image in extreme detail. Provide a comprehensive description covering:

1. MAIN SUBJECT IDENTIFICATION
   - Primary subject type: Person/Equipment/Structure/Vehicle
   - If equipment: Exact type (crane/vessel/truck/spreader/container/machinery)
   - If person: Number of individuals, gender if visible, apparent role

2. PERSON DETAILS (if any visible)
   - Exact position and posture
   - Clothing description (colors, type, condition)
   - Safety equipment worn:
     * Head protection (helmet/hard hat) - color, style, properly worn?
     * High-visibility clothing - color, reflective strips visible?
     * Other PPE - gloves, safety shoes, harness, goggles
   - What safety equipment is MISSING?

3. ACTIVITY ANALYSIS
   - What specific action is being performed?
   - Tools or equipment being used
   - Body mechanics and positioning

4. ENVIRONMENT AND CONTEXT
   - Exact location (vessel deck/quay/yard/height/confined space)
   - Surrounding equipment and structures
   - Weather/lighting conditions
   - Potential hazards in vicinity

5. SAFETY ASSESSMENT
   - Primary safety concern visible
   - Violation type if apparent
   - Severity estimation

Provide the most detailed, factual description possible."""
        
        # Create HTML
        html_content = """<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VALO AI-FARM VLM Review System - All 1250 Cases</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
        }
        
        .header {
            background-color: #2c3e50;
            color: white;
            padding: 20px;
            text-align: center;
            position: fixed;
            width: 100%;
            top: 0;
            z-index: 1000;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .navigation {
            margin-top: 10px;
        }
        
        .nav-button {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 0 5px;
            cursor: pointer;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .nav-button:hover {
            background-color: #2980b9;
        }
        
        .nav-button:disabled {
            background-color: #7f8c8d;
            cursor: not-allowed;
        }
        
        .case-counter {
            display: inline-block;
            margin: 0 20px;
            font-size: 18px;
            font-weight: bold;
        }
        
        .container {
            max-width: 1400px;
            margin: 120px auto 40px;
            padding: 20px;
        }
        
        .case-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            padding: 30px;
            margin-bottom: 20px;
        }
        
        .case-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 20px;
            border-bottom: 2px solid #ecf0f1;
        }
        
        .case-title {
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
        }
        
        .case-type {
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: bold;
            font-size: 14px;
        }
        
        .type-fp {
            background-color: #e8f5e9;
            color: #2e7d32;
        }
        
        .type-tp {
            background-color: #ffebee;
            color: #c62828;
        }
        
        .images-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .image-container {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 10px;
            background: #f8f9fa;
        }
        
        .image-container h4 {
            margin: 0 0 10px 0;
            color: #34495e;
            font-size: 16px;
        }
        
        .image-container img {
            width: 100%;
            height: auto;
            border-radius: 4px;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        
        .info-item {
            display: flex;
            flex-direction: column;
        }
        
        .info-label {
            font-weight: bold;
            color: #7f8c8d;
            font-size: 12px;
            text-transform: uppercase;
            margin-bottom: 4px;
        }
        
        .info-value {
            color: #2c3e50;
            font-size: 14px;
        }
        
        .section {
            margin-bottom: 30px;
        }
        
        .section-title {
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #ecf0f1;
        }
        
        .description-box, .response-box {
            background: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            line-height: 1.6;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
            word-wrap: break-word;
        }
        
        .token-info {
            background: #e3f2fd;
            border: 1px solid #90caf9;
            border-radius: 4px;
            padding: 10px;
            margin-top: 10px;
            font-size: 14px;
        }
        
        .human-review-section {
            background: #fff3cd;
            border: 2px solid #ffc107;
            border-radius: 8px;
            padding: 20px;
            margin-top: 30px;
        }
        
        .human-review-section h3 {
            margin-top: 0;
            color: #856404;
        }
        
        .review-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
            margin-bottom: 15px;
        }
        
        .review-option {
            display: flex;
            align-items: center;
        }
        
        .review-option input {
            margin-right: 8px;
        }
        
        .review-textarea {
            width: 100%;
            min-height: 100px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-family: inherit;
            font-size: 14px;
            resize: vertical;
        }
        
        .save-button {
            background-color: #27ae60;
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 4px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            margin-top: 15px;
        }
        
        .save-button:hover {
            background-color: #229954;
        }
        
        .export-section {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
        }
        
        .export-button {
            background-color: #e74c3c;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
        }
        
        .export-button:hover {
            background-color: #c0392b;
        }
        
        .progress-bar {
            width: 100%;
            height: 4px;
            background: #ecf0f1;
            position: fixed;
            top: 0;
            left: 0;
            z-index: 1001;
        }
        
        .progress-fill {
            height: 100%;
            background: #3498db;
            transition: width 0.3s ease;
        }
        
        .stats-panel {
            background: #ecf0f1;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
        }
        
        .stat-item {
            text-align: center;
            padding: 10px;
            background: white;
            border-radius: 4px;
        }
        
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
        }
        
        .stat-label {
            font-size: 12px;
            color: #7f8c8d;
            text-transform: uppercase;
        }
        
        .search-bar {
            margin-bottom: 20px;
            display: flex;
            gap: 10px;
        }
        
        .search-input {
            flex: 1;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
        }
        
        .search-button {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
        }
        
        @media (max-width: 768px) {
            .images-section {
                grid-template-columns: 1fr;
            }
            
            .info-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="progress-bar">
        <div class="progress-fill" id="progressBar"></div>
    </div>
    
    <div class="header">
        <h1>VALO AI-FARM VLM Review System</h1>
        <div class="navigation">
            <button class="nav-button" onclick="previousCase()" id="prevBtn">← Previous</button>
            <span class="case-counter">
                Case <span id="currentCase">1</span> of <span id="totalCases">0</span>
            </span>
            <button class="nav-button" onclick="nextCase()" id="nextBtn">Next →</button>
            <button class="nav-button" onclick="jumpToCase()">Jump to Case</button>
        </div>
    </div>
    
    <div class="container">
        <div class="search-bar">
            <input type="text" class="search-input" id="searchInput" placeholder="Search by case number (e.g., V1250627132)">
            <button class="search-button" onclick="searchCase()">Search</button>
        </div>
        
        <div class="stats-panel">
            <div class="stat-item">
                <div class="stat-value" id="totalReviewed">0</div>
                <div class="stat-label">Reviewed</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="totalCorrect">0</div>
                <div class="stat-label">Correct</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="totalIssues">0</div>
                <div class="stat-label">Issues Found</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="reviewProgress">0%</div>
                <div class="stat-label">Progress</div>
            </div>
        </div>
        
        <div id="caseContainer"></div>
    </div>
    
    <div class="export-section">
        <button class="export-button" onclick="exportReviews()">Export All Reviews</button>
    </div>
    
    <script>
        // Case data
        const allCases = """ + json.dumps(all_cases, ensure_ascii=False) + """;
        
        // VLM query templates
        const vlmQueries = {
            description: """ + json.dumps(vlm_description_query) + """,
            confidence: "Based on description: [DYNAMIC - includes the actual description]\\n\\nHow accurately does this description match what you see in the image?..."
        };
        
        let currentCaseIndex = 0;
        let reviews = {};
        
        // Load saved reviews from localStorage
        const savedReviews = localStorage.getItem('valoReviews');
        if (savedReviews) {
            reviews = JSON.parse(savedReviews);
        }
        
        function updateProgress() {
            const progress = ((currentCaseIndex + 1) / allCases.length) * 100;
            document.getElementById('progressBar').style.width = progress + '%';
        }
        
        function updateStats() {
            const totalReviewed = Object.keys(reviews).length;
            let totalCorrect = 0;
            let totalIssues = 0;
            
            for (const caseNum in reviews) {
                if (reviews[caseNum].vlmCorrect) totalCorrect++;
                if (reviews[caseNum].hasIssue) totalIssues++;
            }
            
            document.getElementById('totalReviewed').textContent = totalReviewed;
            document.getElementById('totalCorrect').textContent = totalCorrect;
            document.getElementById('totalIssues').textContent = totalIssues;
            document.getElementById('reviewProgress').textContent = 
                Math.round((totalReviewed / allCases.length) * 100) + '%';
        }
        
        function renderCase(index) {
            const case_data = allCases[index];
            const existingReview = reviews[case_data.case_number] || {};
            
            const descriptionTokens = Math.round(case_data.description_length / 4);
            const confidenceTokens = Math.round(case_data.confidence_response.length / 4);
            
            const html = `
                <div class="case-card">
                    <div class="case-header">
                        <div class="case-title">Case: ${case_data.case_number}</div>
                        <div class="case-type ${case_data.is_false_positive ? 'type-fp' : 'type-tp'}">
                            ${case_data.case_type}
                        </div>
                    </div>
                    
                    <div class="images-section">
                        <div class="image-container">
                            <h4>Source Image</h4>
                            <img src="${case_data.source_image}" alt="Source Image" 
                                 onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjMwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iNDAwIiBoZWlnaHQ9IjMwMCIgZmlsbD0iI2RkZCIvPjx0ZXh0IHg9IjUwJSIgeT0iNTAlIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iLjNlbSIgZmlsbD0iIzk5OSI+SW1hZ2UgTm90IEZvdW5kPC90ZXh0Pjwvc3ZnPg=='">
                        </div>
                        <div class="image-container">
                            <h4>Cropped Image (Alert Region)</h4>
                            <img src="${case_data.cropped_image}" alt="Cropped Image"
                                 onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjMwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iNDAwIiBoZWlnaHQ9IjMwMCIgZmlsbD0iI2RkZCIvPjx0ZXh0IHg9IjUwJSIgeT0iNTAlIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iLjNlbSIgZmlsbD0iIzk5OSI+SW1hZ2UgTm90IEZvdW5kPC90ZXh0Pjwvc3ZnPg=='">
                        </div>
                    </div>
                    
                    <div class="info-grid">
                        <div class="info-item">
                            <span class="info-label">Case Number</span>
                            <span class="info-value">${case_data.case_number}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Ground Truth</span>
                            <span class="info-value">${case_data.case_type}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Original Remark</span>
                            <span class="info-value">${case_data.remark || 'N/A'}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Processing Time</span>
                            <span class="info-value">${case_data.processing_time.toFixed(1)}s</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Person Present</span>
                            <span class="info-value">${case_data.person_present ? 'YES' : 'NO'}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Main Subject</span>
                            <span class="info-value">${case_data.main_subject}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">PPE Compliance</span>
                            <span class="info-value">${case_data.ppe_compliance}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">FP Likelihood</span>
                            <span class="info-value">${case_data.false_positive_likelihood}%</span>
                        </div>
                    </div>
                    
                    <div class="section">
                        <h3 class="section-title">VLM Query (Description Request)</h3>
                        <div class="description-box">${vlmQueries.description}</div>
                        <div class="token-info">
                            <strong>Request Token Limit:</strong> 1200 tokens | 
                            <strong>Estimated Query Tokens:</strong> ~300 tokens
                        </div>
                    </div>
                    
                    <div class="section">
                        <h3 class="section-title">VLM Description Response</h3>
                        <div class="description-box">${case_data.description}</div>
                        <div class="token-info">
                            <strong>Response Length:</strong> ${case_data.description_length} characters | 
                            <strong>Estimated Tokens:</strong> ~${descriptionTokens} tokens
                        </div>
                    </div>
                    
                    <div class="section">
                        <h3 class="section-title">VLM Confidence Analysis Response</h3>
                        <div class="response-box">${case_data.confidence_response}</div>
                        <div class="token-info">
                            <strong>Response Length:</strong> ${case_data.confidence_response.length} characters | 
                            <strong>Estimated Tokens:</strong> ~${confidenceTokens} tokens
                        </div>
                    </div>
                    
                    <div class="section">
                        <h3 class="section-title">Extracted Metrics</h3>
                        <div class="info-grid">
                            <div class="info-item">
                                <span class="info-label">Description Accuracy</span>
                                <span class="info-value">${case_data.description_accuracy}%</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">Subject Confidence</span>
                                <span class="info-value">${case_data.subject_confidence}%</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">Safety Violation</span>
                                <span class="info-value">${case_data.safety_violation}</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">Violation Description</span>
                                <span class="info-value">${case_data.violation_description}</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="human-review-section">
                        <h3>Human Review & Ground Truth</h3>
                        
                        <div class="review-options">
                            <div class="review-option">
                                <input type="checkbox" id="vlmCorrect" ${existingReview.vlmCorrect ? 'checked' : ''}>
                                <label for="vlmCorrect">VLM Analysis Correct</label>
                            </div>
                            <div class="review-option">
                                <input type="checkbox" id="hasIssue" ${existingReview.hasIssue ? 'checked' : ''}>
                                <label for="hasIssue">Has Issue</label>
                            </div>
                            <div class="review-option">
                                <input type="checkbox" id="descriptionAccurate" ${existingReview.descriptionAccurate ? 'checked' : ''}>
                                <label for="descriptionAccurate">Description Accurate</label>
                            </div>
                            <div class="review-option">
                                <input type="checkbox" id="confidenceReliable" ${existingReview.confidenceReliable ? 'checked' : ''}>
                                <label for="confidenceReliable">Confidence Reliable</label>
                            </div>
                        </div>
                        
                        <div style="margin-top: 15px;">
                            <label for="issueType" style="font-weight: bold;">Issue Type (if any):</label>
                            <select id="issueType" style="width: 100%; padding: 8px; margin-top: 5px;">
                                <option value="">No Issue</option>
                                <option value="wrong_subject" ${existingReview.issueType === 'wrong_subject' ? 'selected' : ''}>Wrong Subject Identification</option>
                                <option value="missed_person" ${existingReview.issueType === 'missed_person' ? 'selected' : ''}>Missed Person</option>
                                <option value="wrong_ppe" ${existingReview.issueType === 'wrong_ppe' ? 'selected' : ''}>Wrong PPE Assessment</option>
                                <option value="hallucination" ${existingReview.issueType === 'hallucination' ? 'selected' : ''}>Hallucination</option>
                                <option value="token_cutoff" ${existingReview.issueType === 'token_cutoff' ? 'selected' : ''}>Token Limit Cutoff</option>
                                <option value="other" ${existingReview.issueType === 'other' ? 'selected' : ''}>Other</option>
                            </select>
                        </div>
                        
                        <textarea class="review-textarea" id="reviewComments" 
                                  placeholder="Add your review comments, ground truth observations, or any issues found...">${existingReview.comments || ''}</textarea>
                        
                        <button class="save-button" onclick="saveReview()">Save Review</button>
                    </div>
                </div>
            `;
            
            document.getElementById('caseContainer').innerHTML = html;
            document.getElementById('currentCase').textContent = index + 1;
            updateProgress();
        }
        
        function saveReview() {
            const caseNumber = allCases[currentCaseIndex].case_number;
            
            reviews[caseNumber] = {
                caseNumber: caseNumber,
                vlmCorrect: document.getElementById('vlmCorrect').checked,
                hasIssue: document.getElementById('hasIssue').checked,
                descriptionAccurate: document.getElementById('descriptionAccurate').checked,
                confidenceReliable: document.getElementById('confidenceReliable').checked,
                issueType: document.getElementById('issueType').value,
                comments: document.getElementById('reviewComments').value,
                reviewedAt: new Date().toISOString()
            };
            
            // Save to localStorage
            localStorage.setItem('valoReviews', JSON.stringify(reviews));
            
            updateStats();
            alert('Review saved successfully!');
        }
        
        function nextCase() {
            if (currentCaseIndex < allCases.length - 1) {
                currentCaseIndex++;
                renderCase(currentCaseIndex);
                window.scrollTo(0, 0);
            }
        }
        
        function previousCase() {
            if (currentCaseIndex > 0) {
                currentCaseIndex--;
                renderCase(currentCaseIndex);
                window.scrollTo(0, 0);
            }
        }
        
        function jumpToCase() {
            const caseNum = prompt('Enter case index (1 to ' + allCases.length + '):');
            if (caseNum) {
                const index = parseInt(caseNum) - 1;
                if (index >= 0 && index < allCases.length) {
                    currentCaseIndex = index;
                    renderCase(currentCaseIndex);
                    window.scrollTo(0, 0);
                } else {
                    alert('Invalid case number!');
                }
            }
        }
        
        function searchCase() {
            const searchTerm = document.getElementById('searchInput').value.trim();
            if (!searchTerm) return;
            
            const index = allCases.findIndex(c => c.case_number === searchTerm);
            if (index !== -1) {
                currentCaseIndex = index;
                renderCase(currentCaseIndex);
                window.scrollTo(0, 0);
            } else {
                alert('Case not found!');
            }
        }
        
        function exportReviews() {
            const exportData = {
                exportDate: new Date().toISOString(),
                totalCases: allCases.length,
                totalReviewed: Object.keys(reviews).length,
                reviews: reviews,
                summary: {
                    correct: 0,
                    withIssues: 0,
                    issueTypes: {}
                }
            };
            
            // Calculate summary
            for (const caseNum in reviews) {
                const review = reviews[caseNum];
                if (review.vlmCorrect) exportData.summary.correct++;
                if (review.hasIssue) exportData.summary.withIssues++;
                if (review.issueType) {
                    exportData.summary.issueTypes[review.issueType] = 
                        (exportData.summary.issueTypes[review.issueType] || 0) + 1;
                }
            }
            
            // Create downloadable file
            const blob = new Blob([JSON.stringify(exportData, null, 2)], 
                                {type: 'application/json'});
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'valo_vlm_reviews_' + new Date().toISOString().split('T')[0] + '.json';
            a.click();
        }
        
        // Keyboard navigation
        document.addEventListener('keydown', (e) => {
            if (e.key === 'ArrowLeft') previousCase();
            if (e.key === 'ArrowRight') nextCase();
            if (e.key === 's' && e.ctrlKey) {
                e.preventDefault();
                saveReview();
            }
        });
        
        // Initialize
        document.getElementById('totalCases').textContent = allCases.length;
        renderCase(0);
        updateStats();
    </script>
</body>
</html>"""
        
        # Save HTML file
        output_file = f"{self.output_dir}/valo_vlm_review_system.html"
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        print(f"\n✅ Review webpage created successfully!")
        print(f"📄 File: {output_file}")
        print(f"📊 Total cases: {len(all_cases)}")
        print(f"   - False Positives: {len(fp_cases)}")
        print(f"   - True Positives: {len(tp_cases)}")
        
        print("\n📋 Features:")
        print("   - Navigate through all 1250 cases")
        print("   - View source and cropped images")
        print("   - See VLM queries and responses")
        print("   - Token count information")
        print("   - Add human review comments")
        print("   - Track review progress")
        print("   - Export reviews as JSON")
        print("   - Search by case number")
        print("   - Keyboard navigation (← →)")
        
        print("\n🚀 To use:")
        print(f"   1. Open {output_file} in a web browser")
        print("   2. Review each case and add comments")
        print("   3. Export reviews when done")

if __name__ == "__main__":
    creator = ComprehensiveReviewWebpageCreator()
    creator.create_webpage()