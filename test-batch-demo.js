const puppeteer = require('puppeteer');
const fs = require('fs').promises;
const path = require('path');

async function testBatchDemo() {
    console.log('🚀 Starting AI-FARM Batch Processing Demo');
    
    // Create screenshots directory
    const screenshotsDir = path.join(__dirname, 'data', 'screenshots', 'batch-demo');
    await fs.mkdir(screenshotsDir, { recursive: true });
    
    const browser = await puppeteer.launch({
        headless: true,
        args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    try {
        const page = await browser.newPage();
        await page.setViewport({ width: 1920, height: 1080 });
        
        // Step 1: Main Page
        console.log('📍 Step 1: Capturing main page...');
        await page.goto('http://localhost:3000', { waitUntil: 'networkidle2' });
        await page.screenshot({ path: path.join(screenshotsDir, '01-main-page.png'), fullPage: true });
        
        // Step 2: Navigate to Batch Processing
        console.log('📍 Step 2: Looking for batch processing functionality...');
        
        // Look for batch processing link in navigation
        const navLinks = await page.$$('nav a, a[href*="batch"], button');
        let foundBatchLink = false;
        
        for (const link of navLinks) {
            const text = await page.evaluate(el => el.textContent, link);
            const href = await page.evaluate(el => el.href, link);
            
            if (text && (text.toLowerCase().includes('batch') || text.toLowerCase().includes('processing'))) {
                console.log(`Found link: "${text}" - ${href}`);
                await link.click();
                foundBatchLink = true;
                await page.waitForNavigation({ waitUntil: 'networkidle2' }).catch(() => {});
                break;
            }
        }
        
        if (!foundBatchLink) {
            console.log('⚠️ No batch processing link found in navigation');
        }
        
        await page.screenshot({ path: path.join(screenshotsDir, '02-batch-page.png'), fullPage: true });
        
        // Step 3: Check current batch status
        console.log('📍 Step 3: Checking batch processing status...');
        
        // Check if there's already a batch running
        const statusResponse = await page.evaluate(async () => {
            try {
                // Get the latest job
                const res = await fetch('http://localhost:8001/api/v1/batch/api/batch/stats/summary');
                return await res.json();
            } catch (error) {
                return { error: error.message };
            }
        });
        
        console.log('Current batch status:', statusResponse);
        
        // Step 4: Navigate to different pages to show the UI
        console.log('📍 Step 4: Exploring UI pages...');
        
        // Try to find Results page
        await page.goto('http://localhost:3000', { waitUntil: 'networkidle2' });
        const resultsLink = await page.$('a[href*="results"]');
        if (resultsLink) {
            await resultsLink.click();
            await page.waitForNavigation({ waitUntil: 'networkidle2' }).catch(() => {});
            await page.screenshot({ path: path.join(screenshotsDir, '03-results-page.png'), fullPage: true });
        }
        
        // Try to find Insights page
        await page.goto('http://localhost:3000', { waitUntil: 'networkidle2' });
        const insightsLink = await page.$('a[href*="insights"]');
        if (insightsLink) {
            await insightsLink.click();
            await page.waitForNavigation({ waitUntil: 'networkidle2' }).catch(() => {});
            await page.screenshot({ path: path.join(screenshotsDir, '04-insights-page.png'), fullPage: true });
        }
        
        // Try to find Upload page
        await page.goto('http://localhost:3000', { waitUntil: 'networkidle2' });
        const uploadLink = await page.$('a[href*="upload"]');
        if (uploadLink) {
            await uploadLink.click();
            await page.waitForNavigation({ waitUntil: 'networkidle2' }).catch(() => {});
            await page.screenshot({ path: path.join(screenshotsDir, '05-upload-page.png'), fullPage: true });
        }
        
        // Step 5: Test API directly
        console.log('📍 Step 5: Testing batch API endpoints...');
        
        // Get current job status if one is running
        const jobs = await page.evaluate(async () => {
            try {
                const res = await fetch('http://localhost:8001/api/v1/batch/api/batch/stats/summary');
                const data = await res.json();
                return data;
            } catch (error) {
                return { error: error.message };
            }
        });
        
        if (jobs && jobs.jobs && jobs.jobs.length > 0) {
            const latestJob = jobs.jobs[0];
            console.log(`Found job: ${latestJob.job_id}`);
            
            // Get detailed status
            const status = await page.evaluate(async (jobId) => {
                try {
                    const res = await fetch(`http://localhost:8001/api/v1/batch/api/batch/status/${jobId}`);
                    return await res.json();
                } catch (error) {
                    return { error: error.message };
                }
            }, latestJob.job_id);
            
            console.log('Job status:', status);
        }
        
        // Step 6: Get API documentation screenshot
        console.log('📍 Step 6: Capturing API documentation...');
        await page.goto('http://localhost:8001/docs', { waitUntil: 'networkidle2' });
        await page.screenshot({ path: path.join(screenshotsDir, '06-api-docs.png'), fullPage: true });
        
        // Create summary report
        const report = {
            timestamp: new Date().toISOString(),
            status: 'success',
            screenshots: [
                '01-main-page.png',
                '02-batch-page.png',
                '03-results-page.png',
                '04-insights-page.png',
                '05-upload-page.png',
                '06-api-docs.png'
            ],
            batchStatus: statusResponse,
            apiWorking: !statusResponse.error
        };
        
        await fs.writeFile(
            path.join(screenshotsDir, 'demo-report.json'),
            JSON.stringify(report, null, 2)
        );
        
        console.log('✅ Demo completed successfully!');
        console.log(`📸 Screenshots saved to: ${screenshotsDir}`);
        
    } catch (error) {
        console.error('❌ Demo failed:', error);
    } finally {
        await browser.close();
    }
}

// Run the demo
testBatchDemo().catch(console.error);