#!/bin/bash
# Launch Overnight Production Testing System

echo "=================================================="
echo "LAUNCHING OVERNIGHT PRODUCTION TESTING SYSTEM"
echo "Target: 70% FP Detection WITHOUT Remarks"
echo "=================================================="
echo ""
echo "Starting at: $(date)"
echo ""

# Clean up previous runs
rm -f overnight_progress.json
rm -f overnight_production_test.log
rm -f production_success_*.json
rm -f innovative_approaches_results.json

# Launch main testing system in background
echo "1. Starting main production testing system..."
nohup python3 production_overnight_70percent_system.py > main_test.out 2>&1 &
MAIN_PID=$!
echo "   Main system PID: $MAIN_PID"

# Wait a bit then launch innovative approaches
sleep 30
echo ""
echo "2. Starting innovative approaches testing..."
nohup python3 innovative_production_approaches.py > innovative.out 2>&1 &
INNOVATIVE_PID=$!
echo "   Innovative system PID: $INNOVATIVE_PID"

# Launch monitor in new terminal if possible
echo ""
echo "3. Launching monitor..."
if command -v gnome-terminal &> /dev/null; then
    gnome-terminal -- python3 monitor_overnight_production.py
elif command -v xterm &> /dev/null; then
    xterm -e python3 monitor_overnight_production.py &
else
    echo "   Run this in a new terminal: python3 monitor_overnight_production.py"
fi

echo ""
echo "=================================================="
echo "SYSTEMS LAUNCHED SUCCESSFULLY"
echo "=================================================="
echo ""
echo "Main testing PID: $MAIN_PID"
echo "Innovative testing PID: $INNOVATIVE_PID"
echo ""
echo "To monitor progress:"
echo "  - Watch overnight_progress.json"
echo "  - Run: python3 monitor_overnight_production.py"
echo "  - Check: tail -f overnight_production_test.log"
echo ""
echo "To stop all processes:"
echo "  kill $MAIN_PID $INNOVATIVE_PID"
echo ""
echo "System will run until tomorrow morning or 70% target achieved."
echo "=================================================="