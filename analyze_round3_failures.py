#!/usr/bin/env python3
"""
Analyze Round 3 failures - identify valid cases that were incorrectly dismissed
"""

import json
from datetime import datetime


def analyze_round3_failures():
    """Analyze Round 3 results to find incorrectly dismissed valid cases"""
    
    # Load Round 3 intermediate results
    with open("/home/<USER>/VALO_AI-FARM_2025/valo_batch_round3_intermediate.json", 'r') as f:
        data = json.load(f)
    
    results = data.get('detailed_results', [])
    
    # Find all valid cases that were incorrectly dismissed
    incorrectly_dismissed = []
    correctly_protected = []
    false_positives_detected = []
    false_positives_missed = []
    
    for result in results:
        if not result['is_false_positive']:  # Valid case
            if result['is_false_positive_predicted']:  # Incorrectly dismissed
                incorrectly_dismissed.append(result)
            else:  # Correctly protected
                correctly_protected.append(result)
        else:  # False positive case
            if result['is_false_positive_predicted']:  # Correctly detected
                false_positives_detected.append(result)
            else:  # Missed
                false_positives_missed.append(result)
    
    # Analyze patterns in incorrectly dismissed cases
    print("\n" + "="*80)
    print("ROUND 3 FAILURE ANALYSIS")
    print("="*80)
    
    print(f"\nTotal cases analyzed: {len(results)}")
    print(f"Valid cases (should NEVER be dismissed): {len(correctly_protected) + len(incorrectly_dismissed)}")
    print(f"False positive cases: {len(false_positives_detected) + len(false_positives_missed)}")
    
    print(f"\n🚨 CRITICAL FAILURES:")
    print(f"Valid cases incorrectly dismissed: {len(incorrectly_dismissed)}")
    print(f"Valid protection rate: {len(correctly_protected) / (len(correctly_protected) + len(incorrectly_dismissed)) * 100:.1f}%")
    
    print(f"\n📊 False Positive Detection:")
    print(f"FP correctly detected: {len(false_positives_detected)}")
    print(f"FP missed: {len(false_positives_missed)}")
    print(f"FP detection rate: {len(false_positives_detected) / (len(false_positives_detected) + len(false_positives_missed)) * 100:.1f}%")
    
    # Detailed analysis of failures
    print(f"\n\n🔍 INCORRECTLY DISMISSED VALID CASES (CRITICAL):")
    print("-"*80)
    
    for idx, case in enumerate(incorrectly_dismissed, 1):
        print(f"\n{idx}. Case {case['case_number']}:")
        print(f"   Terminal: {case['terminal']}")
        print(f"   Camera: {case['camera_id']}")
        print(f"   Violation: {case['infringement_type']}")
        print(f"   Human Assessment: {case['alert_status']} (VALID)")
        print(f"   Human Remarks: {case['remarks']}")
        print(f"   VLM Decision: {case['vlm_decision']} ❌")
        print(f"   Person Detected: {case['person_detected']}")
        print(f"   Confidence: {case['confidence']}%")
        print(f"   VLM Reasoning: {case['reasoning']}")
        print(f"   Image: {case['cropped_image']}")
    
    # Pattern analysis
    print(f"\n\n📈 PATTERN ANALYSIS OF FAILURES:")
    print("-"*80)
    
    # Analyze by infringement type
    infringement_failures = {}
    for case in incorrectly_dismissed:
        inf_type = case['infringement_type']
        if inf_type not in infringement_failures:
            infringement_failures[inf_type] = []
        infringement_failures[inf_type].append(case)
    
    print("\nFailures by Infringement Type:")
    for inf_type, cases in infringement_failures.items():
        print(f"  {inf_type}: {len(cases)} failures")
    
    # Analyze by person detection
    person_detection_stats = {
        'yes': 0,
        'no': 0,
        'possibly': 0,
        'other': 0
    }
    
    for case in incorrectly_dismissed:
        detection = case.get('person_detected', 'other')
        if detection in person_detection_stats:
            person_detection_stats[detection] += 1
        else:
            person_detection_stats['other'] += 1
    
    print("\nFailures by Person Detection:")
    for detection, count in person_detection_stats.items():
        if count > 0:
            print(f"  {detection}: {count} cases")
    
    # Save detailed report
    report = {
        'analysis_timestamp': datetime.now().isoformat(),
        'round': 3,
        'total_cases': len(results),
        'valid_cases_total': len(correctly_protected) + len(incorrectly_dismissed),
        'valid_cases_protected': len(correctly_protected),
        'valid_cases_dismissed': len(incorrectly_dismissed),
        'valid_protection_rate': len(correctly_protected) / (len(correctly_protected) + len(incorrectly_dismissed)) * 100,
        'fp_cases_total': len(false_positives_detected) + len(false_positives_missed),
        'fp_cases_detected': len(false_positives_detected),
        'fp_detection_rate': len(false_positives_detected) / (len(false_positives_detected) + len(false_positives_missed)) * 100,
        'critical_failures': incorrectly_dismissed,
        'failure_patterns': {
            'by_infringement_type': {k: len(v) for k, v in infringement_failures.items()},
            'by_person_detection': person_detection_stats
        }
    }
    
    with open('/home/<USER>/VALO_AI-FARM_2025/round3_failure_analysis.json', 'w') as f:
        json.dump(report, f, indent=2)
    
    print(f"\n\n💾 Detailed report saved to: round3_failure_analysis.json")
    
    # Recommendations
    print(f"\n\n💡 RECOMMENDATIONS FOR ROUND 4:")
    print("-"*80)
    print("1. NEVER dismiss cases when person_detected='no' if remarks mention a person")
    print("2. Pay special attention to human remarks - they contain critical safety info")
    print("3. Increase confidence threshold to 95% for all violation types")
    print("4. When in doubt, ALWAYS flag for review")
    print("5. Focus on safety keywords: 'not fasten', 'no helmet', 'without', etc.")
    
    return report


if __name__ == "__main__":
    analyze_round3_failures()