#!/usr/bin/env python3
"""
Quick VLM health check
"""

import requests
import time
import json

def check_vlm_health():
    print("VLM HEALTH CHECK")
    print("="*50)
    
    vlm_url = "http://100.106.127.35:9500/v1/chat/completions"
    
    # 1. Basic connectivity
    print("1. Checking connectivity...")
    try:
        response = requests.get("http://100.106.127.35:9500/", timeout=5)
        print(f"   ✓ Server reachable: {response.status_code}")
    except Exception as e:
        print(f"   ✗ Server unreachable: {e}")
        return
    
    # 2. Simple text query
    print("\n2. Testing simple text query...")
    payload = {
        "model": "VLM-38B-AWQ",
        "messages": [{
            "role": "user",
            "content": "Hello, are you working?"
        }],
        "temperature": 0.1,
        "max_tokens": 50
    }
    
    try:
        start = time.time()
        response = requests.post(vlm_url, json=payload, timeout=30)
        elapsed = time.time() - start
        
        if response.status_code == 200:
            result = response.json()
            print(f"   ✓ Text query successful in {elapsed:.1f}s")
            print(f"   Response: {result['choices'][0]['message']['content'][:50]}...")
        else:
            print(f"   ✗ Error {response.status_code}: {response.text[:100]}")
    except Exception as e:
        print(f"   ✗ Query failed: {e}")
    
    # 3. Check previous test results
    print("\n3. Previous test results:")
    
    files = [
        'simple_equipment_test_progress.json',
        'robust_test_progress.json'
    ]
    
    for file in files:
        try:
            with open(file, 'r') as f:
                data = json.load(f)
                print(f"   {file}: {data['total_cases']} cases, {data['accuracy']:.1f}% accuracy")
        except:
            pass
    
    print("\n4. Recommendations:")
    print("   - The VLM seems operational")
    print("   - Simple equipment detection is achieving 40-60% accuracy")
    print("   - This is BELOW the expected 70%+")
    print("   - Need to analyze why simple approach isn't working")

if __name__ == "__main__":
    check_vlm_health()