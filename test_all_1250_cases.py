#!/usr/bin/env python3
"""
Test ALL 1250 cases with the top performing approaches
This will give us the most accurate performance metrics
"""

import json
import asyncio
import aiohttp
import logging
import base64
from datetime import datetime
from typing import Dict, List, Tuple
import time

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class FullDatasetTester:
    """Test all 1250 cases with top approaches"""
    
    def __init__(self):
        self.vlm_endpoint = "http://100.106.127.35:9500/v1/chat/completions"
        self.model = "VLM-38B-AWQ"
        
        # Top 3 approaches from overnight testing
        self.approaches = {
            'assumption_based': """SAFETY ALERT ANALYSIS
DEFAULT ASSUMPTION: This is a false positive (97% probability)
To override this assumption, you need CLEAR evidence:
- Person clearly visible AND
- No helmet visible AND
- No safety vest visible

Is this a FALSE POSITIVE? YES/NO""",
            
            'alert_fatigue_prevention': """ALERT FATIGUE PREVENTION MODE
Too many false alerts = ignored real violations
Help reduce false alerts by being practical.
Mark as FALSE POSITIVE unless you see:
- Obvious person
- Clearly missing PPE
- No doubt whatsoever
Is this a FALSE POSITIVE? YES/NO""",
            
            'worksite_reality': """WORKSITE REALITY CHECK
Facts about worksites:
- Workers wear PPE to protect themselves
- Supervisors enforce PPE rules
- Workers without PPE get sent home
Is it likely someone is working without PPE?
Probably not → FALSE POSITIVE"""
        }
    
    async def analyze_single_case(self, session: aiohttp.ClientSession, 
                                 case: Dict, prompt: str) -> Tuple[bool, float]:
        """Analyze a single case with given prompt"""
        try:
            with open(case['cropped_image'], 'rb') as f:
                image_data = base64.b64encode(f.read()).decode('utf-8')
            
            payload = {
                "model": self.model,
                "messages": [{
                    "role": "user",
                    "content": [
                        {"type": "text", "text": prompt},
                        {"type": "image_url", 
                         "image_url": {"url": f"data:image/jpeg;base64,{image_data}"}}
                    ]
                }],
                "temperature": 0.1,
                "max_tokens": 50
            }
            
            async with session.post(self.vlm_endpoint, json=payload, timeout=30) as response:
                if response.status == 200:
                    result = await response.json()
                    content = result['choices'][0]['message']['content'].upper()
                    
                    # Determine if FP
                    is_fp = "YES" in content[:50] or "FALSE POSITIVE" in content
                    
                    # Estimate confidence
                    if "CLEARLY" in content or "OBVIOUS" in content or "NO DOUBT" in content:
                        confidence = 0.95
                    elif "PROBABLY" in content or "LIKELY" in content:
                        confidence = 0.80
                    else:
                        confidence = 0.70
                    
                    return is_fp, confidence
                else:
                    logger.error(f"API error: {response.status}")
                    return None, 0
                    
        except Exception as e:
            logger.error(f"Error analyzing case {case['case_number']}: {e}")
            return None, 0
    
    async def test_approach_full(self, approach_name: str, prompt: str, 
                                test_cases: List[Dict]) -> Dict:
        """Test an approach on ALL cases"""
        logger.info(f"\nTesting {approach_name} on ALL {len(test_cases)} cases...")
        start_time = time.time()
        
        results = []
        errors = 0
        
        async with aiohttp.ClientSession() as session:
            # Process in batches to avoid overwhelming the API
            batch_size = 10
            
            for i in range(0, len(test_cases), batch_size):
                batch = test_cases[i:i+batch_size]
                tasks = []
                
                for case in batch:
                    task = self.analyze_single_case(session, case, prompt)
                    tasks.append(task)
                
                batch_results = await asyncio.gather(*tasks)
                
                # Process results
                for case, (predicted_fp, confidence) in zip(batch, batch_results):
                    if predicted_fp is not None:
                        results.append({
                            'case_number': case['case_number'],
                            'actual_fp': case['is_false_positive'],
                            'predicted_fp': predicted_fp,
                            'confidence': confidence,
                            'correct': case['is_false_positive'] == predicted_fp
                        })
                    else:
                        errors += 1
                
                # Progress update every 100 cases
                processed = len(results) + errors
                if processed % 100 == 0:
                    elapsed = time.time() - start_time
                    rate = processed / elapsed
                    eta = (len(test_cases) - processed) / rate
                    
                    # Calculate current metrics
                    tp = sum(1 for r in results if r['actual_fp'] and r['predicted_fp'])
                    fp_total = sum(1 for r in results if r['actual_fp'])
                    fp_rate = (tp / fp_total * 100) if fp_total > 0 else 0
                    
                    logger.info(f"  Progress: {processed}/{len(test_cases)} | "
                               f"FP Detection: {fp_rate:.1f}% | "
                               f"Errors: {errors} | "
                               f"ETA: {eta/60:.1f} min")
                
                # Small delay to respect rate limits
                await asyncio.sleep(0.1)
        
        # Calculate final metrics
        end_time = time.time()
        return self.calculate_comprehensive_metrics(
            results, approach_name, errors, end_time - start_time
        )
    
    def calculate_comprehensive_metrics(self, results: List[Dict], 
                                      approach_name: str, errors: int,
                                      duration: float) -> Dict:
        """Calculate detailed metrics"""
        if not results:
            return {
                'approach': approach_name,
                'error': 'No results',
                'errors': errors
            }
        
        # Basic metrics
        tp = sum(1 for r in results if r['actual_fp'] and r['predicted_fp'])
        tn = sum(1 for r in results if not r['actual_fp'] and not r['predicted_fp'])
        fp = sum(1 for r in results if not r['actual_fp'] and r['predicted_fp'])
        fn = sum(1 for r in results if r['actual_fp'] and not r['predicted_fp'])
        
        fp_total = sum(1 for r in results if r['actual_fp'])
        valid_total = sum(1 for r in results if not r['actual_fp'])
        
        fp_detection = (tp / fp_total * 100) if fp_total > 0 else 0
        valid_protection = (tn / valid_total * 100) if valid_total > 0 else 100
        
        # Overall accuracy
        accuracy = (tp + tn) / len(results) * 100
        
        # Confidence analysis
        avg_confidence = sum(r['confidence'] for r in results) / len(results)
        high_conf = sum(1 for r in results if r['confidence'] >= 0.9)
        med_conf = sum(1 for r in results if 0.7 <= r['confidence'] < 0.9)
        low_conf = sum(1 for r in results if r['confidence'] < 0.7)
        
        return {
            'approach': approach_name,
            'total_cases': len(results) + errors,
            'successful_cases': len(results),
            'errors': errors,
            'error_rate': errors / (len(results) + errors) * 100,
            'duration_seconds': duration,
            'cases_per_second': len(results) / duration,
            
            'fp_detection_rate': fp_detection,
            'valid_protection_rate': valid_protection,
            'overall_accuracy': accuracy,
            
            'confusion_matrix': {
                'true_positive': tp,
                'true_negative': tn,
                'false_positive': fp,
                'false_negative': fn
            },
            
            'confidence_analysis': {
                'average': avg_confidence,
                'high_confidence': high_conf / len(results) * 100,
                'medium_confidence': med_conf / len(results) * 100,
                'low_confidence': low_conf / len(results) * 100
            },
            
            'detailed_counts': {
                'total_fp_in_dataset': fp_total,
                'total_valid_in_dataset': valid_total,
                'correctly_identified_fp': tp,
                'correctly_protected_valid': tn,
                'missed_fp': fn,
                'incorrectly_flagged_valid': fp
            }
        }
    
    async def test_ensemble_full(self, test_cases: List[Dict]) -> Dict:
        """Test ensemble approach on ALL cases"""
        logger.info(f"\nTesting ENSEMBLE on ALL {len(test_cases)} cases...")
        start_time = time.time()
        
        results = []
        errors = 0
        
        async with aiohttp.ClientSession() as session:
            batch_size = 5  # Smaller batch for ensemble (3x API calls per case)
            
            for i in range(0, len(test_cases), batch_size):
                batch = test_cases[i:i+batch_size]
                
                for case in batch:
                    try:
                        # Run all three approaches
                        tasks = []
                        for prompt in self.approaches.values():
                            task = self.analyze_single_case(session, case, prompt)
                            tasks.append(task)
                        
                        approach_results = await asyncio.gather(*tasks)
                        
                        # Ensemble logic
                        predictions = [r[0] for r in approach_results if r[0] is not None]
                        confidences = [r[1] for r in approach_results if r[0] is not None]
                        
                        if len(predictions) >= 2:  # Need at least 2 valid predictions
                            # Majority vote
                            fp_votes = sum(predictions)
                            ensemble_prediction = fp_votes >= 2
                            
                            # Weighted confidence
                            if len(predictions) == 3:
                                if fp_votes == 3:
                                    ensemble_confidence = sum(confidences) / 3
                                elif fp_votes == 2:
                                    ensemble_confidence = sum(confidences) / 3 * 0.85
                                else:
                                    ensemble_confidence = sum(confidences) / 3 * 0.7
                            else:
                                ensemble_confidence = sum(confidences) / len(confidences) * 0.8
                            
                            results.append({
                                'case_number': case['case_number'],
                                'actual_fp': case['is_false_positive'],
                                'predicted_fp': ensemble_prediction,
                                'confidence': ensemble_confidence,
                                'correct': case['is_false_positive'] == ensemble_prediction,
                                'vote_count': f"{fp_votes}/{len(predictions)}"
                            })
                        else:
                            errors += 1
                            
                    except Exception as e:
                        logger.error(f"Ensemble error on case {case['case_number']}: {e}")
                        errors += 1
                
                # Progress update
                processed = len(results) + errors
                if processed % 50 == 0:
                    elapsed = time.time() - start_time
                    rate = processed / elapsed
                    eta = (len(test_cases) - processed) / rate
                    
                    tp = sum(1 for r in results if r['actual_fp'] and r['predicted_fp'])
                    fp_total = sum(1 for r in results if r['actual_fp'])
                    fp_rate = (tp / fp_total * 100) if fp_total > 0 else 0
                    
                    logger.info(f"  Progress: {processed}/{len(test_cases)} | "
                               f"FP Detection: {fp_rate:.1f}% | "
                               f"ETA: {eta/60:.1f} min")
                
                await asyncio.sleep(0.2)  # Longer delay for ensemble
        
        end_time = time.time()
        metrics = self.calculate_comprehensive_metrics(
            results, 'ensemble', errors, end_time - start_time
        )
        
        # Add ensemble-specific metrics
        if results:
            vote_distribution = {}
            for r in results:
                vote = r.get('vote_count', 'unknown')
                vote_distribution[vote] = vote_distribution.get(vote, 0) + 1
            
            metrics['ensemble_voting'] = {
                'vote_distribution': vote_distribution,
                'unanimous_decisions': vote_distribution.get('3/3', 0) / len(results) * 100
            }
        
        return metrics


async def main():
    """Run comprehensive test on ALL 1250 cases"""
    logger.info("="*70)
    logger.info("COMPREHENSIVE TEST: ALL 1250 CASES")
    logger.info("="*70)
    
    # Load ALL cases from Round 3
    with open('valo_batch_round3_complete.json', 'r') as f:
        data = json.load(f)
        test_cases = []
        
        # Use ALL 1250 cases
        for case in data['results']:
            test_cases.append({
                'case_number': case['case_number'],
                'cropped_image': case['cropped_image'],
                'infringement_type': case['infringement_type'],
                'is_false_positive': case['is_false_positive']
            })
    
    logger.info(f"\nDataset Statistics:")
    logger.info(f"Total cases: {len(test_cases)}")
    logger.info(f"False positives: {sum(1 for c in test_cases if c['is_false_positive'])} "
               f"({sum(1 for c in test_cases if c['is_false_positive'])/len(test_cases)*100:.1f}%)")
    logger.info(f"Valid violations: {sum(1 for c in test_cases if not c['is_false_positive'])} "
               f"({sum(1 for c in test_cases if not c['is_false_positive'])/len(test_cases)*100:.1f}%)")
    
    tester = FullDatasetTester()
    results = {}
    
    # Test each approach on FULL dataset
    approaches_to_test = ['assumption_based', 'alert_fatigue_prevention', 
                         'worksite_reality', 'ensemble']
    
    overall_start = time.time()
    
    for approach in approaches_to_test:
        if approach == 'ensemble':
            result = await tester.test_ensemble_full(test_cases)
        else:
            prompt = tester.approaches[approach]
            result = await tester.test_approach_full(approach, prompt, test_cases)
        
        results[approach] = result
        
        # Save intermediate results
        with open(f'full_test_{approach}_1250cases.json', 'w') as f:
            json.dump(result, f, indent=2)
        
        logger.info(f"\n{approach} Results:")
        logger.info(f"  FP Detection: {result['fp_detection_rate']:.2f}%")
        logger.info(f"  Valid Protection: {result['valid_protection_rate']:.2f}%")
        logger.info(f"  Overall Accuracy: {result['overall_accuracy']:.2f}%")
        logger.info(f"  Processing Time: {result['duration_seconds']:.1f}s")
        
        await asyncio.sleep(5)  # Pause between approaches
    
    overall_duration = time.time() - overall_start
    
    # Generate comprehensive report
    logger.info("\n" + "="*70)
    logger.info("FINAL COMPREHENSIVE REPORT - ALL 1250 CASES")
    logger.info("="*70)
    
    logger.info("\nPerformance Summary:")
    logger.info("─"*60)
    logger.info(f"{'Approach':<25} {'FP Rate':<12} {'Valid Prot':<12} {'Accuracy':<12} {'Time'}")
    logger.info("─"*60)
    
    for name, result in results.items():
        logger.info(f"{name:<25} {result['fp_detection_rate']:>6.2f}% "
                   f"{result['valid_protection_rate']:>10.2f}% "
                   f"{result['overall_accuracy']:>10.2f}% "
                   f"{result['duration_seconds']:>8.1f}s")
    
    # Find best approach
    best_single = max(
        [(k, v) for k, v in results.items() if k != 'ensemble'],
        key=lambda x: x[1]['fp_detection_rate'] if x[1]['valid_protection_rate'] >= 85 else 0
    )
    
    ensemble_result = results.get('ensemble', {})
    
    logger.info("\n" + "="*70)
    logger.info("KEY FINDINGS - FULL DATASET")
    logger.info("="*70)
    
    logger.info(f"\n1. Best Single Approach: {best_single[0]}")
    logger.info(f"   - FP Detection: {best_single[1]['fp_detection_rate']:.2f}%")
    logger.info(f"   - Valid Protection: {best_single[1]['valid_protection_rate']:.2f}%")
    logger.info(f"   - Cases analyzed: {best_single[1]['successful_cases']}/{best_single[1]['total_cases']}")
    
    if ensemble_result:
        logger.info(f"\n2. Ensemble Performance:")
        logger.info(f"   - FP Detection: {ensemble_result['fp_detection_rate']:.2f}%")
        logger.info(f"   - Valid Protection: {ensemble_result['valid_protection_rate']:.2f}%")
        
        improvement = ensemble_result['fp_detection_rate'] - best_single[1]['fp_detection_rate']
        logger.info(f"   - Improvement over best single: {improvement:+.2f}%")
    
    logger.info(f"\n3. Processing Efficiency:")
    logger.info(f"   - Total test time: {overall_duration/60:.1f} minutes")
    logger.info(f"   - Total cases processed: {len(test_cases) * len(approaches_to_test)}")
    
    # Confidence analysis
    logger.info(f"\n4. Confidence Distribution (Best Approach):")
    best_conf = best_single[1]['confidence_analysis']
    logger.info(f"   - High confidence (≥90%): {best_conf['high_confidence']:.1f}%")
    logger.info(f"   - Medium confidence (70-89%): {best_conf['medium_confidence']:.1f}%")
    logger.info(f"   - Low confidence (<70%): {best_conf['low_confidence']:.1f}%")
    
    # Save final comprehensive report
    final_report = {
        'test_date': datetime.now().isoformat(),
        'dataset_info': {
            'total_cases': len(test_cases),
            'false_positives': sum(1 for c in test_cases if c['is_false_positive']),
            'valid_violations': sum(1 for c in test_cases if not c['is_false_positive'])
        },
        'results': results,
        'best_single': {
            'name': best_single[0],
            'performance': best_single[1]
        },
        'ensemble_improvement': improvement if ensemble_result else None,
        'total_duration_seconds': overall_duration,
        'recommendation': 'ensemble' if ensemble_result and improvement > 5 else best_single[0]
    }
    
    with open('FINAL_FULL_DATASET_TEST_RESULTS.json', 'w') as f:
        json.dump(final_report, f, indent=2)
    
    logger.info("\n" + "="*70)
    logger.info("PRODUCTION READINESS ASSESSMENT")
    logger.info("="*70)
    
    # Apply production degradation estimate
    production_factor = 0.85  # 15% degradation
    
    logger.info(f"\nExpected Production Performance (15% degradation):")
    for name, result in results.items():
        prod_fp = result['fp_detection_rate'] * production_factor
        logger.info(f"  {name}: {prod_fp:.1f}% FP detection")
    
    logger.info("\n" + "="*70)
    logger.info("TEST COMPLETE - Full results saved to FINAL_FULL_DATASET_TEST_RESULTS.json")
    logger.info("="*70)


if __name__ == "__main__":
    asyncio.run(main())