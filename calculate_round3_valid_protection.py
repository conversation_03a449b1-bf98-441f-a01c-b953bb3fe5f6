#!/usr/bin/env python3
"""
Calculate actual Round 3 valid protection rate
"""

import json

# Load Round 3 results
with open('valo_batch_round3_complete.json', 'r') as f:
    round3_data = json.load(f)

results = round3_data['results']

# Count by alert status and decision
valid_flagged = 0
valid_dismissed = 0
invalid_flagged = 0
invalid_dismissed = 0

for result in results:
    alert_status = result.get('alert_status', 'Invalid')
    
    # Check various decision fields
    decision = None
    if 'vlm_decision' in result:
        vlm_decision = result['vlm_decision'].upper()
        if 'FLAG' in vlm_decision:
            decision = 'flagged'
        elif 'DISMISS' in vlm_decision:
            decision = 'dismissed'
    
    # Also check the direct decision fields
    if 'round3_decision' in result:
        decision = result['round3_decision']
    elif 'is_false_positive_predicted' in result:
        # If predicted as false positive, it means dismissed
        if result['is_false_positive_predicted']:
            decision = 'dismissed'
        else:
            decision = 'flagged'
    
    # Count based on alert status and decision
    if alert_status == 'Valid':
        if decision == 'flagged' or decision == 'FLAG_FOR_REVIEW':
            valid_flagged += 1
        elif decision == 'dismissed' or decision == 'DISMISS_WITH_CAUTION':
            valid_dismissed += 1
    else:  # Invalid
        if decision == 'flagged' or decision == 'FLAG_FOR_REVIEW':
            invalid_flagged += 1
        elif decision == 'dismissed' or decision == 'DISMISS_WITH_CAUTION':
            invalid_dismissed += 1

total_valid = valid_flagged + valid_dismissed
total_invalid = invalid_flagged + invalid_dismissed

print("="*80)
print("ROUND 3 VALID PROTECTION ANALYSIS")
print("="*80)

print(f"\nValid Cases (Total: {total_valid}):")
print(f"  Correctly FLAGGED: {valid_flagged}")
print(f"  Incorrectly dismissed: {valid_dismissed}")
print(f"  Protection Rate: {(valid_flagged/total_valid*100 if total_valid > 0 else 0):.1f}%")

print(f"\nInvalid Cases (Total: {total_invalid}):")
print(f"  Correctly dismissed: {invalid_dismissed}")
print(f"  Incorrectly flagged: {invalid_flagged}")
print(f"  FP Detection Rate: {(invalid_dismissed/total_invalid*100 if total_invalid > 0 else 0):.1f}%")

# Show some examples of valid cases that were dismissed
print(f"\n\nExamples of Valid Cases That Were Dismissed:")
print("-"*80)

count = 0
for result in results:
    if result.get('alert_status') == 'Valid':
        vlm_decision = result.get('vlm_decision', '')
        is_fp_predicted = result.get('is_false_positive_predicted', False)
        
        if 'DISMISS' in vlm_decision.upper() or is_fp_predicted:
            count += 1
            print(f"\nCase {count}: {result['case_number']}")
            print(f"  VLM Decision: {vlm_decision}")
            print(f"  Remarks: {result.get('remarks', 'N/A')}")
            print(f"  Reasoning: {result.get('reasoning', 'N/A')[:150]}...")
            
            if count >= 5:
                break

# The reported stats
print(f"\n\nReported Stats vs Actual:")
print(f"  Reported Valid Protection: {round3_data['stats']['valid_protection_rate']:.1f}%")
print(f"  Actual Valid Protection: {(valid_flagged/total_valid*100 if total_valid > 0 else 0):.1f}%")
print(f"  Reported FP Detection: {round3_data['stats']['fp_detection_rate']:.1f}%")
print(f"  Actual FP Detection: {(invalid_dismissed/total_invalid*100 if total_invalid > 0 else 0):.1f}%")