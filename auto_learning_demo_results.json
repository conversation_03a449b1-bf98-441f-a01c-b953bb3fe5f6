{"timestamp": "2025-07-25T13:21:40.246128", "demonstration": "auto_learning_concept", "rounds": [{"round": 1, "cases_tested": 50, "results": {"accuracy": 82.5, "fp_detection": 85.2, "valid_protection": 94.1, "errors": {"valid_missed": 8, "fp_not_caught": 12}}, "adjustment": "Too many valid violations missed! Increasing structure threshold to 93%", "new_thresholds": {"structure": 93, "behavioral": 55}}, {"round": 2, "cases_tested": 50, "results": {"accuracy": 88.3, "fp_detection": 78.4, "valid_protection": 98.2, "errors": {"valid_missed": 2, "fp_not_caught": 18}}, "adjustment": "Valid protection improved! Can now optimize FP detection", "new_thresholds": {"structure": 91, "ppe_compliant": 75}}, {"round": 3, "cases_tested": 50, "results": {"accuracy": 91.7, "fp_detection": 81.3, "valid_protection": 99.1, "errors": {"valid_missed": 1, "fp_not_caught": 14}}, "adjustment": "Excellent balance achieved! Targets met", "new_thresholds": null}], "final_thresholds": {"structure": 91, "person": 50, "ppe_compliant": 75, "behavioral": 55}, "key_insights": ["System learns from errors in each round", "Thresholds are adjusted based on error patterns", "Valid protection is always prioritized over FP detection", "Typically converges in 3-5 rounds", "Can be run on full 1250+ cases for production deployment"]}