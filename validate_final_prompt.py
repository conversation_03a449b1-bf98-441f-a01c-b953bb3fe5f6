#!/usr/bin/env python3
"""
Final validation of balanced prompt
Test on diverse cases to ensure both safety and FP detection
"""

import json
import base64
import requests
import os

VLM_API_URL = "http://100.106.127.35:9500/v1/chat/completions"
VLM_MODEL = "VLM-38B-AWQ"

def validate_final_prompt():
    """Validate the final balanced prompt"""
    
    print("FINAL PROMPT VALIDATION")
    print("="*60)
    print("Testing balanced approach: Safety + FP Detection\n")
    
    # Load final prompt
    with open('final_balanced_safety_prompt.txt', 'r') as f:
        prompt = f.read()
    
    # Comprehensive test cases
    test_cases = {
        # Structure false positives (should be YES)
        "V1250627134": {"desc": "Crane structure", "expected": "YES"},
        "V1250627133": {"desc": "Vessel structure", "expected": "YES"},
        
        # Full PPE false positives (should be YES)
        "V1250627135": {"desc": "WOS FULL PPE at wharf", "expected": "YES"},
        "V1250627151": {"desc": "LS Full PPE at wharf", "expected": "YES"},
        "V1250627203": {"desc": "LS Full PPE at wharf", "expected": "YES"},
        
        # Valid violations (should be NO)
        "V1250627179": {"desc": "Missing GO/STOP bat", "expected": "NO"},
        "V1250627208": {"desc": "Mobile phone use", "expected": "NO"},
        "V1250627194": {"desc": "Vest not fastened", "expected": "NO"},
        "V1250628030": {"desc": "No life jacket", "expected": "NO"},
        
        # Multiple workers (should be YES)
        "V1250627168": {"desc": "2 LS doing lashing", "expected": "YES"}
    }
    
    # Load data
    with open('valo_batch_round3_complete.json', 'r') as f:
        data = json.load(f)
    
    session = requests.Session()
    results = []
    
    for case_num, info in test_cases.items():
        case = next((c for c in data['results'] if c['case_number'] == case_num), None)
        if not case:
            continue
        
        print(f"\n{'='*50}")
        print(f"Case: {case_num}")
        print(f"Type: {info['desc']}")
        print(f"Expected: FALSE POSITIVE: {info['expected']}")
        
        # Encode images
        try:
            with open(case['source_image'], 'rb') as f:
                source_b64 = base64.b64encode(f.read()).decode('utf-8')
            with open(case['cropped_image'], 'rb') as f:
                cropped_b64 = base64.b64encode(f.read()).decode('utf-8')
        except:
            print("Error encoding images")
            continue
        
        # Call VLM
        payload = {
            "model": VLM_MODEL,
            "messages": [{
                "role": "user",
                "content": [
                    {"type": "text", "text": prompt},
                    {"type": "text", "text": "\n\nSOURCE IMAGE:"},
                    {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{source_b64}"}},
                    {"type": "text", "text": "\n\nCROPPED IMAGE:"},
                    {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{cropped_b64}"}}
                ]
            }],
            "temperature": 0.1,
            "max_tokens": 200
        }
        
        try:
            response = session.post(VLM_API_URL, json=payload, timeout=25)
            if response.status_code == 200:
                result = response.json()
                vlm_response = result['choices'][0]['message']['content'].strip()
                
                print(f"\nVLM Response:")
                # Print key lines
                for line in vlm_response.split('\n'):
                    if any(key in line for key in ['FALSE POSITIVE:', 'Entity:', 'PPE Status:', 'Violations:']):
                        print(f"  {line}")
                
                # Check result
                predicted = "YES" if "FALSE POSITIVE: YES" in vlm_response else "NO"
                correct = predicted == info['expected']
                
                print(f"\nResult: {'✅ CORRECT' if correct else '❌ WRONG'}")
                print(f"Got: FALSE POSITIVE: {predicted}, Expected: {info['expected']}")
                
                # Categorize result
                category = 'valid' if info['expected'] == 'NO' else 'fp'
                subcategory = 'structure' if 'structure' in info['desc'].lower() else 'ppe' if 'PPE' in info['desc'] else 'other'
                
                results.append({
                    'case': case_num,
                    'correct': correct,
                    'category': category,
                    'subcategory': subcategory,
                    'expected': info['expected'],
                    'predicted': predicted
                })
                
        except Exception as e:
            print(f"Error: {e}")
    
    # Comprehensive summary
    print("\n" + "="*60)
    print("VALIDATION SUMMARY")
    print("="*60)
    
    if results:
        # Overall
        correct = sum(1 for r in results if r['correct'])
        print(f"\nOverall: {correct}/{len(results)} correct ({correct/len(results)*100:.0f}%)")
        
        # Valid protection (MOST IMPORTANT)
        valid_results = [r for r in results if r['category'] == 'valid']
        if valid_results:
            protected = sum(1 for r in valid_results if r['correct'])
            print(f"\n🛡️  VALID PROTECTION: {protected}/{len(valid_results)} ({protected/len(valid_results)*100:.0f}%)")
            if protected < len(valid_results):
                print("   ⚠️  WARNING: Missing valid violations!")
        
        # FP detection by type
        fp_results = [r for r in results if r['category'] == 'fp']
        if fp_results:
            detected = sum(1 for r in fp_results if r['correct'])
            print(f"\n🎯 FP DETECTION: {detected}/{len(fp_results)} ({detected/len(fp_results)*100:.0f}%)")
            
            # Breakdown by subcategory
            for subcat in ['structure', 'ppe', 'other']:
                subcat_results = [r for r in fp_results if r['subcategory'] == subcat]
                if subcat_results:
                    subcat_correct = sum(1 for r in subcat_results if r['correct'])
                    print(f"   - {subcat.upper()}: {subcat_correct}/{len(subcat_results)} ({subcat_correct/len(subcat_results)*100:.0f}%)")
        
        # Final verdict
        print("\n" + "="*60)
        print("FINAL VERDICT:")
        
        valid_rate = protected/len(valid_results)*100 if valid_results else 0
        fp_rate = detected/len(fp_results)*100 if fp_results else 0
        
        if valid_rate >= 95 and fp_rate >= 70:
            print("✅ PROMPT MEETS ALL REQUIREMENTS!")
            print(f"   - Valid protection: {valid_rate:.0f}%")
            print(f"   - FP detection: {fp_rate:.0f}%")
            print("   - Ready for production!")
        elif valid_rate >= 95:
            print("⚠️  Valid protection good, but FP detection needs improvement")
            print(f"   - Valid protection: {valid_rate:.0f}% ✓")
            print(f"   - FP detection: {fp_rate:.0f}% (target: 70%)")
        else:
            print("❌ Valid protection insufficient - safety at risk!")
            print(f"   - Valid protection: {valid_rate:.0f}% (MUST be 100%)")
        
        # Save results
        report = {
            'timestamp': datetime.now().isoformat(),
            'overall_accuracy': correct/len(results)*100,
            'valid_protection_rate': valid_rate,
            'fp_detection_rate': fp_rate,
            'meets_requirements': valid_rate >= 95 and fp_rate >= 70,
            'detailed_results': results
        }
        
        with open('final_prompt_validation_report.json', 'w') as f:
            json.dump(report, f, indent=2)
        
        print(f"\nReport saved: final_prompt_validation_report.json")

if __name__ == "__main__":
    from datetime import datetime
    validate_final_prompt()