#!/usr/bin/env python3
"""
Test script to verify local VLM API integration
Tests the connection to http://localhost:9500/v1/chat/completions
"""

import asyncio
import base64
import httpx
import json
import os
import sys
from pathlib import Path

# Add the backend directory to the Python path
backend_path = Path(__file__).parent / "backend"
sys.path.insert(0, str(backend_path))

try:
    from app.core.config import settings
    from app.services.vlm_service import vlm_service
except ImportError as e:
    print(f"Error importing backend modules: {e}")
    print("Make sure you're running this from the project root directory")
    sys.exit(1)


def create_test_image_base64():
    """Create a minimal test image as base64"""
    # 1x1 blue pixel PNG
    return "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="


async def test_direct_api_call():
    """Test direct API call to the VLM endpoint"""
    print("🔍 Testing direct API call to VLM endpoint...")
    
    base64_image = create_test_image_base64()
    
    payload = {
        "model": "VLM-38B-AWQ",
        "messages": [
            {
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": "Describe this image briefly"
                    },
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": f"data:image/png;base64,{base64_image}"
                        }
                    }
                ]
            }
        ],
        "temperature": 0,
        "max_tokens": 100
    }
    
    headers = {
        "Content-Type": "application/json",
        "Authorization": "Bearer placeholder-key"
    }
    
    try:
        async with httpx.AsyncClient(timeout=30) as client:
            response = await client.post(
                "http://localhost:9500/v1/chat/completions",
                headers=headers,
                json=payload
            )
            
            print(f"✅ Status Code: {response.status_code}")
            print(f"✅ Response Headers: {dict(response.headers)}")
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ Response: {json.dumps(result, indent=2)}")
                return True
            else:
                print(f"❌ Error Response: {response.text}")
                return False
                
    except Exception as e:
        print(f"❌ Direct API call failed: {str(e)}")
        return False


async def test_vlm_service():
    """Test the VLM service integration"""
    print("\n🔍 Testing VLM service integration...")
    
    try:
        # Create a temporary test image file
        test_image_path = "/tmp/test_image.png"
        
        # Create a minimal PNG file
        png_data = base64.b64decode(create_test_image_base64())
        with open(test_image_path, "wb") as f:
            f.write(png_data)
        
        # Test the VLM service
        result = await vlm_service.analyze_image(
            image_path=test_image_path,
            case_number="TEST_001",
            custom_prompt="Describe this test image briefly"
        )
        
        print(f"✅ VLM Service Result: {result}")
        
        # Clean up
        os.unlink(test_image_path)
        return True
        
    except Exception as e:
        print(f"❌ VLM service test failed: {str(e)}")
        return False


async def test_configuration():
    """Test the configuration settings"""
    print("\n🔍 Testing configuration settings...")
    
    print(f"✅ VLM API Base URL: {settings.vlm_api_base_url}")
    print(f"✅ VLM API Key: {'***' if settings.vlm_api_key else 'Not set'}")
    print(f"✅ VLM Model Name: {settings.vlm_model_name}")
    print(f"✅ VLM Max Tokens: {settings.vlm_max_tokens}")
    print(f"✅ VLM Temperature: {settings.vlm_temperature}")
    print(f"✅ VLM Timeout: {settings.vlm_timeout_seconds}")
    print(f"✅ VLM API Headers: {settings.vlm_api_headers}")
    
    return True


async def main():
    """Main test function"""
    print("🚀 Starting VALO AI-FARM Local VLM Integration Test")
    print("=" * 60)
    
    # Test configuration
    config_ok = await test_configuration()
    
    # Test direct API call
    direct_ok = await test_direct_api_call()
    
    # Test VLM service (only if direct call works)
    service_ok = False
    if direct_ok:
        service_ok = await test_vlm_service()
    else:
        print("\n⚠️  Skipping VLM service test due to direct API call failure")
    
    print("\n" + "=" * 60)
    print("📊 Test Results Summary:")
    print(f"✅ Configuration: {'PASS' if config_ok else 'FAIL'}")
    print(f"{'✅' if direct_ok else '❌'} Direct API Call: {'PASS' if direct_ok else 'FAIL'}")
    print(f"{'✅' if service_ok else '❌'} VLM Service: {'PASS' if service_ok else 'FAIL'}")
    
    if direct_ok and service_ok:
        print("\n🎉 All tests passed! Local VLM integration is working correctly.")
        return 0
    else:
        print("\n❌ Some tests failed. Please check the VLM API endpoint and configuration.")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
