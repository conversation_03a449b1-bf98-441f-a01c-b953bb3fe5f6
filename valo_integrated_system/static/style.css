/* VALO System Global Styles */

* {
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    margin: 0;
    padding: 0;
    background: #1a1a1a;
    color: #ffffff;
    line-height: 1.6;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.header {
    text-align: center;
    padding: 20px 0;
    border-bottom: 1px solid #333;
    margin-bottom: 30px;
}

.header h1 {
    margin: 0;
    font-size: 2.5em;
    background: linear-gradient(135deg, #00ff88, #00ccaa);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.header p {
    margin: 10px 0 0;
    color: #ccc;
    font-size: 1.1em;
}

/* Navigation */
.nav-buttons {
    display: flex;
    gap: 10px;
    justify-content: center;
    margin-bottom: 30px;
    flex-wrap: wrap;
}

.nav-button {
    padding: 12px 24px;
    background: #333;
    color: #fff;
    text-decoration: none;
    border-radius: 8px;
    border: 2px solid transparent;
    transition: all 0.3s ease;
    font-weight: 500;
}

.nav-button:hover {
    background: #444;
    border-color: #555;
}

.nav-button.active {
    background: #00ff88;
    color: #000;
    border-color: #00ff88;
}

/* Cards and Containers */
.card {
    background: #2d2d2d;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
    border: 1px solid #404040;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: #2d2d2d;
    padding: 20px;
    border-radius: 12px;
    border-left: 4px solid #00ff88;
    text-align: center;
    transition: transform 0.2s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
}

.stat-value {
    font-size: 2.5em;
    font-weight: bold;
    color: #00ff88;
    margin-bottom: 5px;
}

.stat-label {
    color: #ccc;
    font-size: 0.9em;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Status Indicators */
.status {
    padding: 10px 15px;
    border-radius: 6px;
    margin: 10px 0;
    border-left: 4px solid;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.status.online {
    background: rgba(0, 255, 136, 0.1);
    border-left-color: #00ff88;
}

.status.offline {
    background: rgba(255, 68, 68, 0.1);
    border-left-color: #ff4444;
}

.status.warning {
    background: rgba(255, 136, 0, 0.1);
    border-left-color: #ff8800;
}

/* Forms and Inputs */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #ccc;
}

.form-control {
    width: 100%;
    padding: 12px;
    background: #333;
    border: 1px solid #555;
    border-radius: 6px;
    color: #fff;
    font-size: 14px;
}

.form-control:focus {
    outline: none;
    border-color: #00ff88;
    box-shadow: 0 0 0 2px rgba(0, 255, 136, 0.2);
}

/* Buttons */
.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s ease;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn-primary {
    background: #00ff88;
    color: #000;
}

.btn-primary:hover {
    background: #00cc6a;
}

.btn-secondary {
    background: #555;
    color: #fff;
}

.btn-secondary:hover {
    background: #666;
}

.btn-danger {
    background: #ff4444;
    color: #fff;
}

.btn-danger:hover {
    background: #e63939;
}

/* Tables */
.table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 15px;
}

.table th,
.table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #444;
}

.table th {
    background: #333;
    font-weight: 600;
    color: #ccc;
    text-transform: uppercase;
    font-size: 0.85em;
    letter-spacing: 0.5px;
}

.table tr:hover {
    background: rgba(255, 255, 255, 0.05);
}

/* Loading States */
.loading {
    text-align: center;
    padding: 40px;
    color: #888;
    font-style: italic;
}

.spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid #333;
    border-radius: 50%;
    border-top-color: #00ff88;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .nav-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .header h1 {
        font-size: 2em;
    }
    
    .container {
        padding: 0 15px;
    }
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 10px; }
.mb-2 { margin-bottom: 20px; }
.mb-3 { margin-bottom: 30px; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 10px; }
.mt-2 { margin-top: 20px; }
.mt-3 { margin-top: 30px; }

.hidden { display: none; }
.visible { display: block; }

/* Animations */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}