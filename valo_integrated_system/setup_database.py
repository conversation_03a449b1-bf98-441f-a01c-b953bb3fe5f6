#!/usr/bin/env python3
"""
PostgreSQL Database Setup Script
Creates the valo_system database and initializes the schema
"""

import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT
import os
import sys

def create_database():
    """Create the valo_system database if it doesn't exist"""
    try:
        # Connect to PostgreSQL server (postgres database)
        conn = psycopg2.connect(
            host='localhost',
            port=5432,
            user='postgres',
            password='postgres',
            database='postgres'
        )
        conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
        cursor = conn.cursor()
        
        # Check if database exists
        cursor.execute("SELECT 1 FROM pg_catalog.pg_database WHERE datname = 'valo_system'")
        exists = cursor.fetchone()
        
        if not exists:
            cursor.execute('CREATE DATABASE valo_system')
            print('✓ Database valo_system created successfully')
        else:
            print('✓ Database valo_system already exists')
            
        cursor.close()
        conn.close()
        return True
        
    except Exception as e:
        print(f'✗ Error creating database: {e}')
        return False

def initialize_schema():
    """Initialize the database schema"""
    try:
        # Connect to valo_system database
        conn = psycopg2.connect(
            host='localhost',
            port=5432,
            user='postgres',
            password='postgres',
            database='valo_system'
        )
        cursor = conn.cursor()
        
        # Read and execute the PostgreSQL schema
        schema_file = 'database/postgresql_schema.sql'
        if os.path.exists(schema_file):
            with open(schema_file, 'r') as f:
                schema_sql = f.read()
            
            # Execute the schema
            cursor.execute(schema_sql)
            conn.commit()
            print('✓ Database schema initialized successfully')
        else:
            print(f'✗ Schema file not found: {schema_file}')
            return False
            
        cursor.close()
        conn.close()
        return True
        
    except Exception as e:
        print(f'✗ Error initializing schema: {e}')
        return False

def test_connection():
    """Test the database connection"""
    try:
        conn = psycopg2.connect(
            host='localhost',
            port=5432,
            user='postgres',
            password='postgres',
            database='valo_system'
        )
        cursor = conn.cursor()
        
        # Test query
        cursor.execute("SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public'")
        table_count = cursor.fetchone()[0]
        print(f'✓ Connection successful. Found {table_count} tables in the database.')
        
        cursor.close()
        conn.close()
        return True
        
    except Exception as e:
        print(f'✗ Connection test failed: {e}')
        return False

def main():
    """Main setup function"""
    print("PostgreSQL Database Setup for VALO System")
    print("=" * 50)
    
    # Step 1: Create database
    print("\n1. Creating database...")
    if not create_database():
        sys.exit(1)
    
    # Step 2: Initialize schema
    print("\n2. Initializing schema...")
    if not initialize_schema():
        sys.exit(1)
    
    # Step 3: Test connection
    print("\n3. Testing connection...")
    if not test_connection():
        sys.exit(1)
    
    print("\n✓ Database setup completed successfully!")
    print("\nConnection details:")
    print("  Host: localhost")
    print("  Port: 5432")
    print("  Database: valo_system")
    print("  User: postgres")
    print("  Password: postgres")

if __name__ == "__main__":
    main()
