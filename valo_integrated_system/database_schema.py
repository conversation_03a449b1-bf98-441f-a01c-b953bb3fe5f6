#!/usr/bin/env python3
"""
Database schema for VALO integrated system
"""

import sqlite3
from datetime import datetime
import json
import os

class VALODatabase:
    def __init__(self, db_path='data/valo_db.sqlite'):
        """Initialize database connection and create tables if needed"""
        os.makedirs(os.path.dirname(db_path), exist_ok=True)
        self.conn = sqlite3.connect(db_path, check_same_thread=False)
        self.conn.row_factory = sqlite3.Row
        self.create_tables()
    
    def create_tables(self):
        """Create all required tables"""
        cursor = self.conn.cursor()
        
        # Rounds table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS rounds (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT UNIQUE NOT NULL,
                start_time TIMESTAMP NOT NULL,
                end_time TIMESTAMP,
                total_cases INTEGER DEFAULT 0,
                processed_cases INTEGER DEFAULT 0,
                fp_detection_rate REAL DEFAULT 0,
                protection_rate REAL DEFAULT 0,
                status TEXT DEFAULT 'running',
                config_params TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Cases table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS cases (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                round_id INTEGER NOT NULL,
                case_number TEXT NOT NULL,
                infringement_type TEXT,
                csv_ground_truth TEXT,
                csv_remarks TEXT,
                camera_id TEXT,
                terminal TEXT,
                source_image_path TEXT,
                cropped_image_path TEXT,
                vlm_query TEXT,
                vlm_response TEXT,
                vlm_tokens_used INTEGER DEFAULT 0,
                person_present BOOLEAN,
                main_subject TEXT,
                subject_confidence REAL,
                helmet_status TEXT,
                vest_status TEXT,
                ppe_compliance TEXT,
                safety_violation TEXT,
                fp_likelihood REAL,
                fp_indicators TEXT,
                vlm_decision TEXT,  -- 'filtered' or 'retained'
                quadrant_category TEXT,  -- 'valid_passed', 'valid_failed', 'invalid_passed', 'invalid_failed'
                human_review_status TEXT,
                human_review_notes TEXT,
                processing_time REAL,
                attempts INTEGER DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (round_id) REFERENCES rounds (id),
                UNIQUE(round_id, case_number)
            )
        ''')
        
        # Analytics aggregates table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS analytics_cache (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                round_id INTEGER NOT NULL,
                metric_type TEXT NOT NULL,
                metric_value TEXT,
                computed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (round_id) REFERENCES rounds (id)
            )
        ''')
        
        # Processing logs table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS processing_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                round_id INTEGER,
                case_number TEXT,
                log_level TEXT,
                message TEXT,
                details TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (round_id) REFERENCES rounds (id)
            )
        ''')
        
        # Create indexes for performance
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_cases_round_id ON cases(round_id)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_cases_case_number ON cases(case_number)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_cases_quadrant ON cases(quadrant_category)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_logs_round_id ON processing_logs(round_id)')
        
        self.conn.commit()
    
    def create_round(self, name=None):
        """Create a new processing round"""
        if not name:
            name = f"Round_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        cursor = self.conn.cursor()
        cursor.execute('''
            INSERT INTO rounds (name, start_time, status)
            VALUES (?, ?, 'running')
        ''', (name, datetime.now()))
        self.conn.commit()
        return cursor.lastrowid
    
    def update_round(self, round_id, **kwargs):
        """Update round information"""
        allowed_fields = ['end_time', 'total_cases', 'processed_cases', 
                         'fp_detection_rate', 'protection_rate', 'status', 'config_params']
        
        updates = []
        values = []
        for field, value in kwargs.items():
            if field in allowed_fields:
                updates.append(f"{field} = ?")
                values.append(value)
        
        if updates:
            values.append(round_id)
            cursor = self.conn.cursor()
            cursor.execute(f'''
                UPDATE rounds 
                SET {', '.join(updates)}
                WHERE id = ?
            ''', values)
            self.conn.commit()
    
    def insert_case(self, round_id, case_data):
        """Insert or update a case"""
        cursor = self.conn.cursor()
        
        # Build insert query dynamically
        fields = []
        placeholders = []
        values = []
        
        for key, value in case_data.items():
            fields.append(key)
            placeholders.append('?')
            if isinstance(value, (dict, list)):
                values.append(json.dumps(value))
            else:
                values.append(value)
        
        fields.append('round_id')
        placeholders.append('?')
        values.append(round_id)
        
        query = f'''
            INSERT OR REPLACE INTO cases ({', '.join(fields)})
            VALUES ({', '.join(placeholders)})
        '''
        
        cursor.execute(query, values)
        self.conn.commit()
        return cursor.lastrowid
    
    def get_cases_by_quadrant(self, round_id, quadrant):
        """Get cases by quadrant category"""
        cursor = self.conn.cursor()
        cursor.execute('''
            SELECT * FROM cases
            WHERE round_id = ? AND quadrant_category = ?
            ORDER BY created_at DESC
        ''', (round_id, quadrant))
        return [dict(row) for row in cursor.fetchall()]
    
    def get_round_statistics(self, round_id):
        """Get comprehensive statistics for a round"""
        cursor = self.conn.cursor()
        
        # Basic counts
        cursor.execute('''
            SELECT 
                COUNT(*) as total,
                SUM(CASE WHEN csv_ground_truth = 'Valid' THEN 1 ELSE 0 END) as valid_total,
                SUM(CASE WHEN csv_ground_truth = 'Invalid' THEN 1 ELSE 0 END) as invalid_total,
                SUM(CASE WHEN quadrant_category = 'valid_passed' THEN 1 ELSE 0 END) as valid_passed,
                SUM(CASE WHEN quadrant_category = 'valid_failed' THEN 1 ELSE 0 END) as valid_failed,
                SUM(CASE WHEN quadrant_category = 'invalid_passed' THEN 1 ELSE 0 END) as invalid_passed,
                SUM(CASE WHEN quadrant_category = 'invalid_failed' THEN 1 ELSE 0 END) as invalid_failed
            FROM cases
            WHERE round_id = ?
        ''', (round_id,))
        
        stats = dict(cursor.fetchone())
        
        # Confidence score distributions
        cursor.execute('''
            SELECT 
                AVG(subject_confidence) as avg_subject_conf,
                AVG(fp_likelihood) as avg_fp_likelihood,
                AVG(CASE WHEN csv_ground_truth = 'Valid' AND person_present = 1 
                    THEN subject_confidence ELSE NULL END) as valid_person_conf,
                AVG(CASE WHEN csv_ground_truth = 'Invalid' AND main_subject LIKE '%Structure%' 
                    THEN subject_confidence ELSE NULL END) as invalid_structure_conf
            FROM cases
            WHERE round_id = ?
        ''', (round_id,))
        
        stats.update(dict(cursor.fetchone()))
        
        # By infringement type
        cursor.execute('''
            SELECT 
                infringement_type,
                COUNT(*) as count,
                SUM(CASE WHEN quadrant_category IN ('valid_passed', 'invalid_failed') 
                    THEN 1 ELSE 0 END) as correct_decisions
            FROM cases
            WHERE round_id = ?
            GROUP BY infringement_type
        ''', (round_id,))
        
        stats['by_infringement'] = [dict(row) for row in cursor.fetchall()]
        
        return stats
    
    def log_processing(self, round_id, case_number, level, message, details=None):
        """Log processing events"""
        cursor = self.conn.cursor()
        cursor.execute('''
            INSERT INTO processing_logs (round_id, case_number, log_level, message, details)
            VALUES (?, ?, ?, ?, ?)
        ''', (round_id, case_number, level, message, json.dumps(details) if details else None))
        self.conn.commit()
    
    def close(self):
        """Close database connection"""
        self.conn.close()

if __name__ == "__main__":
    # Test database creation
    db = VALODatabase()
    print("Database initialized successfully!")
    
    # Create test round
    round_id = db.create_round("Test_Round_20250728")
    print(f"Created round with ID: {round_id}")
    
    db.close()