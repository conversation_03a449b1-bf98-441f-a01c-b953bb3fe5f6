#!/usr/bin/env python3
"""
VALO Enhanced System Setup Script
Initializes PostgreSQL database, installs dependencies, and verifies system
"""
import os
import sys
import subprocess
import logging
from pathlib import Path

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def run_command(cmd, description):
    """Run shell command with error handling"""
    logger.info(f"Running: {description}")
    try:
        result = subprocess.run(cmd, shell=True, check=True, capture_output=True, text=True)
        logger.info(f"✅ {description} completed successfully")
        return result.stdout
    except subprocess.CalledProcessError as e:
        logger.error(f"❌ {description} failed: {e}")
        logger.error(f"Error output: {e.stderr}")
        return None

def check_prerequisites():
    """Check system prerequisites"""
    logger.info("🔍 Checking system prerequisites...")
    
    # Check Python version
    if sys.version_info < (3, 8):
        logger.error("❌ Python 3.8+ required")
        return False
    logger.info(f"✅ Python {sys.version}")
    
    # Check PostgreSQL
    pg_result = run_command("which psql", "PostgreSQL client check")
    if not pg_result:
        logger.error("❌ PostgreSQL not found. Install PostgreSQL first.")
        return False
    
    # Check pip
    pip_result = run_command("which pip3", "pip3 check")
    if not pip_result:
        logger.error("❌ pip3 not found")
        return False
    
    return True

def install_dependencies():
    """Install Python dependencies"""
    logger.info("📦 Installing Python dependencies...")
    
    requirements_file = Path(__file__).parent / "requirements_enhanced.txt"
    if not requirements_file.exists():
        logger.error(f"❌ Requirements file not found: {requirements_file}")
        return False
    
    cmd = f"pip3 install -r {requirements_file}"
    result = run_command(cmd, "Python dependencies installation")
    return result is not None

def setup_postgresql():
    """Setup PostgreSQL database"""
    logger.info("🗄️ Setting up PostgreSQL database...")
    
    # Load environment variables
    from dotenv import load_dotenv
    load_dotenv('config.env')
    
    db_name = os.getenv('POSTGRES_DB', 'valo_system')
    db_user = os.getenv('POSTGRES_USER', 'valo_user')
    db_password = os.getenv('POSTGRES_PASSWORD', 'valo_password')
    db_host = os.getenv('POSTGRES_HOST', 'localhost')
    db_port = os.getenv('POSTGRES_PORT', '5432')
    
    # Create database and user (requires PostgreSQL admin access)
    logger.info("Creating database and user (you may need to enter PostgreSQL admin password)...")
    
    # Create user
    create_user_cmd = f"sudo -u postgres createuser -s {db_user}"
    run_command(create_user_cmd, f"Create PostgreSQL user {db_user}")
    
    # Set password
    set_password_cmd = f"sudo -u postgres psql -c \"ALTER USER {db_user} PASSWORD '{db_password}';\""
    run_command(set_password_cmd, f"Set password for user {db_user}")
    
    # Create database
    create_db_cmd = f"sudo -u postgres createdb -O {db_user} {db_name}"
    run_command(create_db_cmd, f"Create database {db_name}")
    
    return True

def initialize_database_schema():
    """Initialize database schema"""
    logger.info("🏗️ Initializing database schema...")
    
    try:
        # Import database module
        sys.path.append(str(Path(__file__).parent))
        from database.connection import DatabaseManager
        
        # Initialize database
        db_manager = DatabaseManager()
        db_manager.initialize_schema()
        logger.info("✅ Database schema initialized successfully")
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to initialize database schema: {e}")
        return False

def verify_vlm_connectivity():
    """Verify VLM API connectivity"""
    logger.info("🔗 Verifying VLM API connectivity...")
    
    try:
        import asyncio
        from vlm.client import check_vlm_health
        
        # Check VLM health
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            health = loop.run_until_complete(check_vlm_health())
            if health['status'] == 'healthy':
                logger.info("✅ VLM API connectivity verified")
                return True
            else:
                logger.warning(f"⚠️ VLM API health check returned: {health}")
                return False
        finally:
            loop.close()
            
    except Exception as e:
        logger.error(f"❌ VLM connectivity check failed: {e}")
        return False

def create_directories():
    """Create necessary directories"""
    logger.info("📁 Creating necessary directories...")
    
    directories = [
        'logs',
        'uploads',
        'data/images',
        'data/reports',
        'dashboard/static/images',
        'dashboard/templates'
    ]
    
    for directory in directories:
        dir_path = Path(__file__).parent / directory
        dir_path.mkdir(parents=True, exist_ok=True)
        logger.info(f"✅ Created directory: {dir_path}")
    
    return True

def create_sample_config():
    """Create sample configuration files"""
    logger.info("⚙️ Checking configuration files...")
    
    config_path = Path(__file__).parent / 'config.env'
    if config_path.exists():
        logger.info("✅ Configuration file already exists")
    else:
        logger.warning("⚠️ Configuration file not found. Copy config.env and customize settings.")
    
    return True

def run_basic_tests():
    """Run basic system tests"""
    logger.info("🧪 Running basic system tests...")
    
    try:
        # Test database connection
        from database.connection import initialize_database
        db = initialize_database()
        rounds = db.get_rounds(limit=1)
        logger.info("✅ Database connection test passed")
        
        # Test CSV parser
        from utils.csv_parser import CSVParser
        parser = CSVParser()
        logger.info("✅ CSV parser test passed")
        
        # Test image handler
        from utils.image_handler import ImageHandler
        handler = ImageHandler()
        logger.info("✅ Image handler test passed")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ System tests failed: {e}")
        return False

def main():
    """Main setup function"""
    logger.info("🎯 VALO Enhanced System Setup")
    logger.info("=" * 50)
    
    success = True
    
    # Run setup steps
    steps = [
        ("Prerequisites Check", check_prerequisites),
        ("Install Dependencies", install_dependencies),
        ("Setup PostgreSQL", setup_postgresql),
        ("Initialize Database Schema", initialize_database_schema),
        ("Create Directories", create_directories),
        ("Create Configuration", create_sample_config),
        ("Verify VLM Connectivity", verify_vlm_connectivity),
        ("Run Basic Tests", run_basic_tests)
    ]
    
    for step_name, step_func in steps:
        logger.info(f"\n📋 Step: {step_name}")
        try:
            if not step_func():
                logger.error(f"❌ {step_name} failed")
                success = False
                break
        except Exception as e:
            logger.error(f"❌ {step_name} failed with exception: {e}")
            success = False
            break
    
    if success:
        logger.info("\n🎉 VALO Enhanced System setup completed successfully!")
        logger.info("\n📖 Next steps:")
        logger.info("1. Review and customize config.env file")
        logger.info("2. Place your CSV data files in the data directory")
        logger.info("3. Start the processor: python processor.py --csv your_data.csv")
        logger.info("4. Start the dashboard: python dashboard/app.py")
        logger.info("5. Access dashboard at http://localhost:5003")
    else:
        logger.error("\n❌ Setup failed. Please check the errors above and retry.")
        sys.exit(1)

if __name__ == "__main__":
    main()