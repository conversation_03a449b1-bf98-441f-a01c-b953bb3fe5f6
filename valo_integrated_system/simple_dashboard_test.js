const puppeteer = require('puppeteer');

async function testValoDashboardSimple() {
    console.log('🚀 Simple VALO Dashboard UI Test');
    console.log('==================================');
    
    let browser;
    try {
        // Launch browser
        browser = await puppeteer.launch({
            headless: false,
            args: ['--no-sandbox', '--disable-setuid-sandbox'],
            defaultViewport: { width: 1920, height: 1080 }
        });
        
        const page = await browser.newPage();
        
        // Navigate to dashboard
        console.log('📍 Navigating to http://localhost:5002...');
        await page.goto('http://localhost:5002', { waitUntil: 'networkidle2' });
        
        // Wait for page to fully load
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        // Take initial screenshot
        console.log('📸 Taking initial screenshot...');
        await page.screenshot({ 
            path: '/tmp/valo_dashboard_working.png', 
            fullPage: true 
        });
        console.log('✅ Initial screenshot saved to /tmp/valo_dashboard_working.png');
        
        // Check page title
        const title = await page.title();
        console.log(`📄 Page title: ${title}`);
        
        // 1. Check for basic button elements using simple selectors
        console.log('\n🔍 Checking for buttons...');
        
        const allButtons = await page.$$('button');
        console.log(`   Found ${allButtons.length} buttons total`);
        
        // Get text content of all buttons
        const buttonTexts = [];
        for (let i = 0; i < allButtons.length; i++) {
            try {
                const buttonText = await page.evaluate(el => el.textContent || el.innerText || '', allButtons[i]);
                if (buttonText.trim()) {
                    buttonTexts.push(buttonText.trim());
                }
            } catch (e) {
                // Skip if error reading button text
            }
        }
        
        console.log('📋 Button texts found:');
        buttonTexts.forEach((text, i) => {
            console.log(`   ${i+1}: "${text}"`);
        });
        
        // 2. Look for agent-related content
        console.log('\n🔍 Checking for agent-related content...');
        
        const pageContent = await page.content();
        const hasAgentContent = pageContent.toLowerCase().includes('agent');
        const hasStartContent = pageContent.toLowerCase().includes('start');
        const hasStopContent = pageContent.toLowerCase().includes('stop');
        const hasDemoContent = pageContent.toLowerCase().includes('demo') || pageContent.toLowerCase().includes('generate');
        
        console.log(`   Agent-related content: ${hasAgentContent}`);
        console.log(`   Start functionality: ${hasStartContent}`);
        console.log(`   Stop functionality: ${hasStopContent}`);
        console.log(`   Demo functionality: ${hasDemoContent}`);
        
        // 3. Try to click a start button if found
        console.log('\n🖱️  Testing button interaction...');
        
        let startButtonFound = false;
        for (let i = 0; i < Math.min(allButtons.length, 10); i++) {
            try {
                const buttonText = await page.evaluate(el => el.textContent || el.innerText || '', allButtons[i]);
                if (buttonText.toLowerCase().includes('start')) {
                    console.log(`   Clicking start button: "${buttonText}"`);
                    await allButtons[i].click();
                    await new Promise(resolve => setTimeout(resolve, 2000));
                    startButtonFound = true;
                    console.log('   ✅ Start button clicked successfully');
                    break;
                }
            } catch (e) {
                console.log(`   ⚠️  Error clicking button ${i}: ${e.message}`);
            }
        }
        
        if (!startButtonFound) {
            console.log('   ❌ No start button found or clickable');
        }
        
        // 4. Check for agent status information
        console.log('\n🔍 Checking for status information...');
        
        // Look for common status indicators
        const statusWords = ['online', 'offline', 'running', 'stopped', 'active', 'inactive'];
        let statusFound = false;
        
        for (const word of statusWords) {
            if (pageContent.toLowerCase().includes(word)) {
                console.log(`   Status indicator found: "${word}"`);
                statusFound = true;
            }
        }
        
        if (!statusFound) {
            console.log('   No clear status indicators found');
        }
        
        // 5. Check for specific agent names
        console.log('\n🔍 Checking for specific agents...');
        
        const agentNames = ['processing_agent', 'data_agent', 'web_agent'];
        for (const agentName of agentNames) {
            const hasAgent = pageContent.includes(agentName);
            console.log(`   ${agentName}: ${hasAgent ? '✅ Found' : '❌ Not found'}`);
        }
        
        // 6. Take final screenshot
        console.log('\n📸 Taking final screenshot...');
        await page.screenshot({ 
            path: '/tmp/valo_dashboard_final.png', 
            fullPage: true 
        });
        console.log('✅ Final screenshot saved to /tmp/valo_dashboard_final.png');
        
        // 7. Test API calls through browser console
        console.log('\n🔌 Testing API endpoints via browser...');
        
        try {
            const agentsResponse = await page.evaluate(async () => {
                const response = await fetch('/api/agents/status');
                return await response.json();
            });
            console.log('✅ /api/agents/status responded:', typeof agentsResponse);
        } catch (e) {
            console.log('❌ /api/agents/status failed:', e.message);
        }
        
        try {
            const roundsResponse = await page.evaluate(async () => {
                const response = await fetch('/api/rounds');
                return await response.json();
            });
            console.log('✅ /api/rounds responded with data');
        } catch (e) {
            console.log('❌ /api/rounds failed:', e.message);
        }
        
        // Summary
        console.log('\n📋 VALO Dashboard Test Summary');
        console.log('===============================');
        console.log('✅ Successfully navigated to http://localhost:5002');
        console.log(`✅ Page title: "${title}"`);
        console.log(`✅ Found ${allButtons.length} interactive buttons`);
        console.log(`✅ Agent content detected: ${hasAgentContent}`);
        console.log(`✅ Start/Stop controls detected: ${hasStartContent}/${hasStopContent}`);
        console.log(`✅ Demo functionality detected: ${hasDemoContent}`);
        console.log('✅ Screenshots captured successfully');
        console.log('\n🎯 Dashboard appears to be working correctly!');
        
    } catch (error) {
        console.error('❌ Error during dashboard test:', error);
        
        if (browser) {
            try {
                const page = (await browser.pages())[0];
                await page.screenshot({ 
                    path: '/tmp/valo_dashboard_simple_error.png', 
                    fullPage: true 
                });
                console.log('📸 Error screenshot saved to /tmp/valo_dashboard_simple_error.png');
            } catch (screenshotError) {
                console.log('❌ Could not take error screenshot');
            }
        }
    } finally {
        if (browser) {
            await browser.close();
        }
    }
}

// Run the test
testValoDashboardSimple().catch(console.error);