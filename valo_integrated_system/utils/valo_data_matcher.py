"""
VALO Data Matcher - Matches CSV cases with available images
Only processes cases that have both CSV entry AND corresponding image files
"""
import os
import pandas as pd
import logging
from pathlib import Path
from typing import Dict, List, Any, Tuple

logger = logging.getLogger(__name__)

class VALODataMatcher:
    """Matches CSV cases with available image files"""
    
    def __init__(self, csv_path: str, images_base_path: str):
        self.csv_path = csv_path
        self.images_base_path = Path(images_base_path)
        self.matched_cases = []
        self.csv_data = None
        
    def load_and_match_data(self) -> Tuple[int, int, List[Dict[str, Any]]]:
        """
        Load CSV and match with available images
        Groups by case_number: each case = case_number + source + cropped images
        Returns: (total_csv_rows, unique_cases_count, unique_cases_list)
        """
        logger.info(f"Loading CSV data from: {self.csv_path}")
        
        # Load CSV data
        try:
            self.csv_data = pd.read_csv(self.csv_path)
            total_csv_rows = len(self.csv_data)
            logger.info(f"Loaded {total_csv_rows} rows from CSV")
        except Exception as e:
            logger.error(f"Failed to load CSV: {e}")
            return 0, 0, []
        
        # Get all available image files
        logger.info(f"Scanning for images in: {self.images_base_path}")
        available_images = self._get_available_images()
        logger.info(f"Found {len(available_images)} image files")
        
        # Group CSV rows by case_number (each case should have 2 rows: source + cropped)
        cases_dict = {}
        
        for index, row in self.csv_data.iterrows():
            # Handle both CSV formats
            case_id = row.get('case_number', row.get('Case Int. ID', '')).strip()
            if not case_id:
                continue
            
            # Initialize case if not exists
            if case_id not in cases_dict:
                cases_dict[case_id] = {
                    'case_number': case_id,
                    'rows_data': [],
                    'csv_indices': [],
                    'source_url': None,
                    'cropped_url': None,
                    'pk_event': row.get('pk_event', ''),
                    'key': row.get('key', ''),
                    'csv_ground_truth': 'invalid' if row.get('key', '').lower() == 'invalid' else 'valid'
                }
            
            # Add row data
            cases_dict[case_id]['rows_data'].append(row)
            cases_dict[case_id]['csv_indices'].append(index)
            
            # Determine if this is source or cropped based on URL
            url = row.get('url', '')
            if 'source' in url:
                cases_dict[case_id]['source_url'] = url
            elif 'cropped' in url:
                cases_dict[case_id]['cropped_url'] = url
        
        # Now match unique cases with available images
        matched_cases = []
        unique_cases_with_images = 0
        
        for case_id, case_info in cases_dict.items():
            # Look for images for this case ID
            case_images = self._find_images_for_case(case_id, available_images)
            
            # Only include cases that have both CSV data AND actual image files
            if case_images and (case_images.get('source') or case_images.get('cropped')):
                unique_cases_with_images += 1
                
                # Create matched case data
                matched_case = {
                    'case_number': case_id,
                    'csv_indices': case_info['csv_indices'],
                    'pk_event': case_info['pk_event'],
                    'csv_ground_truth': case_info['csv_ground_truth'],
                    'alert_status': 'Invalid' if case_info['csv_ground_truth'] == 'invalid' else 'Valid',
                    'infringement_type': 'PPE Non-compliance',  # Default for this dataset
                    'follow_up': 'False Positive' if case_info['csv_ground_truth'] == 'invalid' else 'Valid Violation',
                    'remarks': f"Case {case_id} - {case_info['csv_ground_truth']}",
                    'alert_start_time': '',
                    'reviewed_by': '',
                    'camera': f"Camera for {case_id}",
                    'terminal': 'P2/P3',
                    'alert_id': case_info['pk_event'],
                    
                    # Image paths from actual files (not CSV URLs)
                    'available_images': case_images,
                    'source_image_path': case_images.get('source'),
                    'cropped_image_path': case_images.get('cropped'),  # This triggers the alert
                    'image_count': len([v for v in case_images.values() if v]),
                    
                    # CSV URL references
                    'csv_source_url': case_info['source_url'],
                    'csv_cropped_url': case_info['cropped_url'],
                    'has_both_images': bool(case_images.get('source') and case_images.get('cropped'))
                }
                
                matched_cases.append(matched_case)
        
        self.matched_cases = matched_cases
        unique_case_count = len(cases_dict)
        
        logger.info(f"Data matching completed:")
        logger.info(f"  Total CSV rows: {total_csv_rows}")
        logger.info(f"  Unique case numbers: {unique_case_count}")
        logger.info(f"  Cases with images: {unique_cases_with_images}")
        logger.info(f"  Match rate: {(unique_cases_with_images/unique_case_count)*100:.1f}%")
        
        return total_csv_rows, unique_cases_with_images, matched_cases
    
    def _get_available_images(self) -> Dict[str, str]:
        """Get all available image files mapped by case ID"""
        available_images = {}
        
        # Check invalid folder
        invalid_folder = self.images_base_path / 'invalid'
        if invalid_folder.exists():
            for image_file in invalid_folder.glob('*.JPEG'):
                # Extract case ID from filename (e.g., V1250623121_cropped_invalid.JPEG -> V1250623121)
                filename = image_file.stem
                if '_' in filename:
                    case_id = filename.split('_')[0]
                    if case_id not in available_images:
                        available_images[case_id] = []
                    available_images[case_id].append(str(image_file))
        
        # Check other potential folders (valid, mixed, etc.)
        for subfolder in self.images_base_path.iterdir():
            if subfolder.is_dir() and subfolder.name != 'invalid':
                for image_file in subfolder.glob('*.JPEG'):
                    filename = image_file.stem
                    if '_' in filename:
                        case_id = filename.split('_')[0]
                        if case_id not in available_images:
                            available_images[case_id] = []
                        available_images[case_id].append(str(image_file))
        
        return available_images
    
    def _find_images_for_case(self, case_id: str, available_images: Dict[str, List[str]]) -> Dict[str, str]:
        """Find available images for a specific case ID"""
        case_images = {'source': None, 'cropped': None}
        
        if case_id in available_images:
            for image_path in available_images[case_id]:
                filename = Path(image_path).stem
                if 'source' in filename:
                    case_images['source'] = image_path
                elif 'cropped' in filename:
                    case_images['cropped'] = image_path
                else:
                    # If no specific type, use as cropped (fallback)
                    if not case_images['cropped']:
                        case_images['cropped'] = image_path
        
        return case_images
    
    def get_processable_cases(self, start_index: int = 0, limit: int = None) -> List[Dict[str, Any]]:
        """Get cases that can be processed, starting from a specific index"""
        if not self.matched_cases:
            return []
        
        cases = self.matched_cases[start_index:]
        if limit:
            cases = cases[:limit]
        
        return cases
    
    def get_case_by_index(self, index: int) -> Dict[str, Any]:
        """Get a specific case by index"""
        if 0 <= index < len(self.matched_cases):
            return self.matched_cases[index]
        return None
    
    def get_total_processable_cases(self) -> int:
        """Get total number of processable cases"""
        return len(self.matched_cases)
    
    def get_matching_summary(self) -> Dict[str, Any]:
        """Get summary of data matching results"""
        if not hasattr(self, 'csv_data') or self.csv_data is None:
            return {}
        
        total_csv = len(self.csv_data)
        matched = len(self.matched_cases)
        
        # Count by ground truth
        invalid_cases = len([c for c in self.matched_cases if c['csv_ground_truth'] == 'invalid'])
        valid_cases = len([c for c in self.matched_cases if c['csv_ground_truth'] == 'valid'])
        
        # Count by image types
        with_source = len([c for c in self.matched_cases if c['source_image_path']])
        with_cropped = len([c for c in self.matched_cases if c['cropped_image_path']])
        
        return {
            'total_csv_cases': total_csv,
            'matched_cases': matched,
            'unmatched_cases': total_csv - matched,
            'match_rate_percent': round((matched / total_csv) * 100, 1) if total_csv > 0 else 0,
            'invalid_cases': invalid_cases,
            'valid_cases': valid_cases,
            'cases_with_source_images': with_source,
            'cases_with_cropped_images': with_cropped,
            'images_base_path': str(self.images_base_path),
            'csv_path': self.csv_path
        }

# Global data matcher instance
valo_data_matcher = None

def initialize_valo_data_matcher(csv_path: str = None, images_path: str = None) -> VALODataMatcher:
    """Initialize global VALO data matcher"""
    global valo_data_matcher
    
    if not csv_path:
        # Use the CSV that matches the available image dates (June 30th)
        csv_path = "/home/<USER>/VALO_AI-FARM_2025/ai_farm_images_fixed_250703/VALO_SQL_DATA_250630.csv"
    
    if not images_path:
        images_path = "/home/<USER>/VALO_AI-FARM_2025/ai_farm_images_fixed_250703/ai_farm_images_fixed"
    
    valo_data_matcher = VALODataMatcher(csv_path, images_path)
    logger.info("VALO data matcher initialized")
    
    return valo_data_matcher

def get_valo_data_matcher() -> VALODataMatcher:
    """Get the global VALO data matcher instance"""
    if not valo_data_matcher:
        raise RuntimeError("VALO data matcher not initialized. Call initialize_valo_data_matcher() first.")
    return valo_data_matcher