"""
Image Handler for VALO System
Manages image paths, validation, and processing
"""
import os
import logging
from pathlib import Path
from typing import Dict, Any, Optional, List
from PIL import Image
import hashlib

logger = logging.getLogger(__name__)

class ImageHandler:
    """Handle image operations for VALO violation processing"""
    
    def __init__(self, base_image_path: str = "/data/valo_images"):
        """Initialize image handler with base path"""
        self.base_image_path = Path(base_image_path)
        
        # Common image directories to search
        self.search_paths = [
            self.base_image_path,
            Path("../ai_farm_images_fixed_250703"),
            Path("./images"),
            Path("../images"),
            Path("../../images"),
            Path("./data/images"),
            Path("../data/images")
        ]
        
        # Supported image extensions
        self.supported_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp'}
        
        logger.info(f"Image handler initialized with base path: {self.base_image_path}")
    
    def get_image_path(self, case_data: Dict[str, Any]) -> Optional[str]:
        """Find the actual image path for a case"""
        # Get image path from case data
        image_url = case_data.get('url', '')
        case_number = case_data.get('case_number', '')
        
        if not image_url and not case_number:
            logger.warning("No image URL or case number provided")
            return None
        
        # List of potential image paths to try
        potential_paths = []
        
        # 1. Direct path if it exists
        if image_url:
            potential_paths.append(image_url)
            
            # 2. Relative to base path
            if not os.path.isabs(image_url):
                for search_path in self.search_paths:
                    potential_paths.append(search_path / image_url)
            
            # 3. Just the filename in search directories
            filename = Path(image_url).name
            for search_path in self.search_paths:
                potential_paths.append(search_path / filename)
        
        # 4. Case number-based paths
        if case_number:
            case_filename = f"{case_number}.jpg"
            for search_path in self.search_paths:
                potential_paths.append(search_path / case_filename)
                
            # Try with different extensions
            for ext in self.supported_extensions:
                case_filename_ext = f"{case_number}{ext}"
                for search_path in self.search_paths:
                    potential_paths.append(search_path / case_filename_ext)
        
        # 5. Pattern matching for complex filenames
        if case_number:
            for search_path in self.search_paths:
                if search_path.exists():
                    try:
                        # Look for files containing case number
                        for file_path in search_path.glob('*'):
                            if (file_path.is_file() and 
                                case_number in file_path.name and 
                                file_path.suffix.lower() in self.supported_extensions):
                                potential_paths.append(file_path)
                    except Exception as e:
                        logger.debug(f"Error searching in {search_path}: {e}")
        
        # Find first existing path
        for path in potential_paths:
            path_obj = Path(path)
            if path_obj.exists() and path_obj.is_file():
                # Validate it's actually an image
                if self._validate_image_file(path_obj):
                    logger.debug(f"Found image for case {case_number}: {path_obj}")
                    return str(path_obj.absolute())
        
        # Log missing image
        logger.warning(f"Image not found for case {case_number}. Tried paths: {[str(p) for p in potential_paths[:5]]}")
        return None
    
    def get_cropped_image_path(self, case_data: Dict[str, Any]) -> Optional[str]:
        """Get path to cropped violation region image"""
        case_number = case_data.get('case_number', '')
        
        if not case_number:
            return None
        
        # Look for cropped image variants
        cropped_patterns = [
            f"{case_number}_cropped.jpg",
            f"{case_number}_crop.jpg",
            f"{case_number}_region.jpg",
            f"cropped_{case_number}.jpg",
            f"crop_{case_number}.jpg"
        ]
        
        for search_path in self.search_paths:
            if search_path.exists():
                for pattern in cropped_patterns:
                    cropped_path = search_path / pattern
                    if cropped_path.exists() and self._validate_image_file(cropped_path):
                        return str(cropped_path.absolute())
        
        # If no cropped image found, return the main image
        return self.get_image_path(case_data)
    
    def _validate_image_file(self, image_path: Path) -> bool:
        """Validate that file is a valid image"""
        try:
            if image_path.suffix.lower() not in self.supported_extensions:
                return False
            
            # Try to open with PIL to verify it's a valid image
            with Image.open(image_path) as img:
                img.verify()
            return True
        except Exception:
            return False
    
    def get_image_info(self, image_path: str) -> Dict[str, Any]:
        """Get detailed information about an image"""
        try:
            path_obj = Path(image_path)
            
            if not path_obj.exists():
                return {'error': 'Image file not found', 'path': image_path}
            
            # Get file stats
            file_stats = path_obj.stat()
            
            # Get image properties
            with Image.open(path_obj) as img:
                info = {
                    'path': str(path_obj.absolute()),
                    'exists': True,
                    'size_bytes': file_stats.st_size,
                    'size_mb': round(file_stats.st_size / (1024 * 1024), 2),
                    'dimensions': img.size,
                    'width': img.width,
                    'height': img.height,
                    'format': img.format,
                    'mode': img.mode,
                    'has_transparency': img.mode in ('RGBA', 'LA') or 'transparency' in img.info,
                    'modified_time': file_stats.st_mtime,
                    'file_hash': self._calculate_file_hash(path_obj)
                }
                
                # Additional metadata if available
                if hasattr(img, '_getexif') and img._getexif():
                    info['has_exif'] = True
                else:
                    info['has_exif'] = False
                
                return info
                
        except Exception as e:
            return {
                'error': str(e),
                'path': image_path,
                'exists': Path(image_path).exists()
            }
    
    def _calculate_file_hash(self, file_path: Path) -> str:
        """Calculate SHA-256 hash of file for integrity checking"""
        try:
            hash_sha256 = hashlib.sha256()
            with open(file_path, 'rb') as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_sha256.update(chunk)
            return hash_sha256.hexdigest()[:16]  # First 16 chars for brevity
        except Exception:
            return 'unknown'
    
    def scan_image_directory(self, directory_path: str) -> Dict[str, Any]:
        """Scan directory for images and return statistics"""
        try:
            dir_path = Path(directory_path)
            
            if not dir_path.exists():
                return {'error': f'Directory not found: {directory_path}'}
            
            image_files = []
            total_size = 0
            format_counts = {}
            
            for file_path in dir_path.rglob('*'):
                if (file_path.is_file() and 
                    file_path.suffix.lower() in self.supported_extensions):
                    
                    if self._validate_image_file(file_path):
                        file_info = {
                            'name': file_path.name,
                            'path': str(file_path.absolute()),
                            'size_bytes': file_path.stat().st_size
                        }
                        
                        # Get format
                        try:
                            with Image.open(file_path) as img:
                                file_info['format'] = img.format
                                file_info['dimensions'] = img.size
                                
                                # Count formats
                                format_counts[img.format] = format_counts.get(img.format, 0) + 1
                        except Exception:
                            file_info['format'] = 'Unknown'
                        
                        image_files.append(file_info)
                        total_size += file_info['size_bytes']
            
            # Sort by name
            image_files.sort(key=lambda x: x['name'])
            
            return {
                'directory': str(dir_path.absolute()),
                'total_images': len(image_files),
                'total_size_mb': round(total_size / (1024 * 1024), 2),
                'format_distribution': format_counts,
                'images': image_files,
                'sample_images': image_files[:10] if image_files else []
            }
            
        except Exception as e:
            return {'error': str(e)}
    
    def find_missing_images(self, cases_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Find cases with missing images"""
        missing_images = []
        found_images = []
        
        for case_data in cases_data:
            case_number = case_data.get('case_number', '')
            image_path = self.get_image_path(case_data)
            
            if image_path:
                found_images.append({
                    'case_number': case_number,
                    'image_path': image_path,
                    'image_info': self.get_image_info(image_path)
                })
            else:
                missing_images.append({
                    'case_number': case_number,
                    'expected_url': case_data.get('url', ''),
                    'case_data': case_data
                })
        
        return {
            'total_cases': len(cases_data),
            'found_images': len(found_images),
            'missing_images': len(missing_images),
            'missing_percentage': round((len(missing_images) / len(cases_data)) * 100, 2) if cases_data else 0,
            'missing_cases': missing_images,
            'found_cases': found_images[:10],  # Sample of found cases
            'search_paths_used': [str(p) for p in self.search_paths]
        }
    
    def organize_images_by_case(self, cases_data: List[Dict[str, Any]], 
                               output_directory: str) -> Dict[str, Any]:
        """Organize images into case-based directory structure"""
        try:
            output_path = Path(output_directory)
            output_path.mkdir(parents=True, exist_ok=True)
            
            organized_count = 0
            failed_count = 0
            results = []
            
            for case_data in cases_data:
                case_number = case_data.get('case_number', '')
                if not case_number:
                    continue
                
                # Create case directory
                case_dir = output_path / case_number
                case_dir.mkdir(exist_ok=True)
                
                # Find and copy source image
                source_image = self.get_image_path(case_data)
                if source_image:
                    try:
                        source_path = Path(source_image)
                        dest_path = case_dir / f"source{source_path.suffix}"
                        
                        # Copy file
                        import shutil
                        shutil.copy2(source_path, dest_path)
                        
                        # Find and copy cropped image if exists
                        cropped_image = self.get_cropped_image_path(case_data)
                        if cropped_image and cropped_image != source_image:
                            cropped_path = Path(cropped_image)
                            cropped_dest = case_dir / f"cropped{cropped_path.suffix}"
                            shutil.copy2(cropped_path, cropped_dest)
                        
                        results.append({
                            'case_number': case_number,
                            'status': 'success',
                            'source_copied': str(dest_path),
                            'case_directory': str(case_dir)
                        })
                        organized_count += 1
                        
                    except Exception as e:
                        results.append({
                            'case_number': case_number,
                            'status': 'failed',
                            'error': str(e)
                        })
                        failed_count += 1
                else:
                    results.append({
                        'case_number': case_number,
                        'status': 'no_image',
                        'error': 'Source image not found'
                    })
                    failed_count += 1
            
            return {
                'output_directory': str(output_path.absolute()),
                'total_cases': len(cases_data),
                'organized_successfully': organized_count,
                'failed_to_organize': failed_count,
                'results': results
            }
            
        except Exception as e:
            return {'error': str(e)}

# Utility functions
def find_case_image(case_data: Dict[str, Any]) -> Optional[str]:
    """Convenience function to find image for a case"""
    handler = ImageHandler()
    return handler.get_image_path(case_data)

def validate_image_availability(cases_data: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Convenience function to validate image availability"""
    handler = ImageHandler()
    return handler.find_missing_images(cases_data)