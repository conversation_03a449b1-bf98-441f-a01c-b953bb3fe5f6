"""
CSV Parser for VALO System
Handles psa_valo_violation_report CSV format
"""
import csv
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional

logger = logging.getLogger(__name__)

class CSVParser:
    """Parse VALO violation CSV files with flexible field mapping"""
    
    def __init__(self):
        # Standard field mappings (case-insensitive)
        self.field_mappings = {
            # Primary key variations
            'case_number': ['case_number', 'pk_event', 'event_id', 'id'],
            
            # Infringement type variations
            'infringement_type': ['infringement_type', 'violation_type', 'type', 'category'],
            
            # Ground truth variations (valid/invalid)
            'key': ['key', 'ground_truth', 'validation', 'status', 'is_valid'],
            
            # Image path variations
            'url': ['url', 'image_path', 'image_url', 'file_path', 'cropped_image'],
            
            # Additional fields
            'timestamp': ['timestamp', 'datetime', 'created_at', 'event_time'],
            'location': ['location', 'area', 'zone', 'sector'],
            'description': ['description', 'remarks', 'notes', 'comment'],
            'source_image': ['source_image', 'original_image', 'full_image'],
            'confidence': ['confidence', 'score', 'probability']
        }
    
    def _normalize_field_name(self, field_name: str) -> str:
        """Normalize field name to standard format"""
        field_lower = field_name.lower().strip()
        
        for standard_name, variations in self.field_mappings.items():
            if field_lower in [v.lower() for v in variations]:
                return standard_name
        
        # Return original if no mapping found
        return field_name.lower().replace(' ', '_')
    
    def _clean_value(self, value: str) -> str:
        """Clean and normalize CSV values"""
        if not value:
            return ''
        
        # Strip whitespace
        cleaned = str(value).strip()
        
        # Normalize common variations
        if cleaned.lower() in ['invalid', 'false', '0', 'no', 'negative']:
            return 'Invalid'
        elif cleaned.lower() in ['valid', 'true', '1', 'yes', 'positive']:
            return 'Valid'
        
        return cleaned
    
    def parse_csv(self, csv_file_path: str) -> List[Dict[str, Any]]:
        """Parse CSV file and return normalized case data"""
        csv_path = Path(csv_file_path)
        
        if not csv_path.exists():
            raise FileNotFoundError(f"CSV file not found: {csv_file_path}")
        
        try:
            cases_data = []
            
            # Try different encodings
            encodings = ['utf-8', 'utf-8-sig', 'latin1', 'cp1252']
            
            for encoding in encodings:
                try:
                    with open(csv_path, 'r', encoding=encoding) as f:
                        # Detect delimiter
                        sample = f.read(1024)
                        f.seek(0)
                        
                        delimiter = ','
                        if sample.count(';') > sample.count(','):
                            delimiter = ';'
                        elif sample.count('\t') > sample.count(','):
                            delimiter = '\t'
                        
                        reader = csv.DictReader(f, delimiter=delimiter)
                        
                        # Process each row
                        for row_num, row in enumerate(reader, 1):
                            try:
                                # Normalize field names
                                normalized_row = {}
                                for field_name, value in row.items():
                                    if field_name:  # Skip empty column names
                                        normalized_name = self._normalize_field_name(field_name)
                                        normalized_row[normalized_name] = self._clean_value(value)
                                
                                # Ensure required fields exist
                                case_data = self._ensure_required_fields(normalized_row, row_num)
                                cases_data.append(case_data)
                                
                            except Exception as e:
                                logger.warning(f"Skipping row {row_num} due to error: {e}")
                                continue
                    
                    logger.info(f"Successfully parsed {len(cases_data)} cases from {csv_file_path} "
                              f"using {encoding} encoding")
                    return cases_data
                    
                except UnicodeDecodeError:
                    continue  # Try next encoding
                except Exception as e:
                    logger.error(f"Failed to parse CSV with {encoding}: {e}")
                    continue
            
            raise ValueError(f"Could not parse CSV file with any supported encoding: {csv_file_path}")
            
        except Exception as e:
            logger.error(f"CSV parsing failed: {e}")
            raise
    
    def _ensure_required_fields(self, row_data: Dict[str, Any], row_num: int) -> Dict[str, Any]:
        """Ensure required fields exist with default values"""
        case_data = row_data.copy()
        
        # Generate case_number if missing
        if not case_data.get('case_number'):
            # Try alternative fields
            case_number = (
                case_data.get('pk_event') or 
                case_data.get('event_id') or 
                case_data.get('id') or
                f"CASE_{row_num:06d}"
            )
            case_data['case_number'] = str(case_number)
        
        # Default infringement type
        if not case_data.get('infringement_type'):
            case_data['infringement_type'] = 'Safety Violation'
        
        # Normalize ground truth key
        if 'key' not in case_data:
            # Try to infer from other fields
            key_value = (
                case_data.get('ground_truth') or 
                case_data.get('validation') or 
                case_data.get('status') or
                'Valid'  # Default assumption
            )
            case_data['key'] = self._clean_value(key_value)
        else:
            case_data['key'] = self._clean_value(case_data['key'])
        
        # Map key to csv_ground_truth for database consistency
        case_data['csv_ground_truth'] = case_data['key']
        
        # Default image URL if missing
        if not case_data.get('url'):
            case_data['url'] = f"images/{case_data['case_number']}.jpg"
        
        # Add metadata
        case_data['row_number'] = row_num
        case_data['parsed_at'] = None  # Will be set during processing
        
        return case_data
    
    def validate_csv_structure(self, csv_file_path: str) -> Dict[str, Any]:
        """Validate CSV structure and return analysis"""
        try:
            cases_data = self.parse_csv(csv_file_path)
            
            if not cases_data:
                return {
                    'valid': False,
                    'error': 'No valid cases found in CSV',
                    'case_count': 0
                }
            
            # Analyze field coverage
            field_coverage = {}
            for case in cases_data:
                for field, value in case.items():
                    if field not in field_coverage:
                        field_coverage[field] = {'present': 0, 'missing': 0}
                    
                    if value and str(value).strip():
                        field_coverage[field]['present'] += 1
                    else:
                        field_coverage[field]['missing'] += 1
            
            # Calculate coverage percentages
            total_cases = len(cases_data)
            for field_stats in field_coverage.values():
                field_stats['coverage_percent'] = (field_stats['present'] / total_cases) * 100
            
            # Check for required fields
            required_fields = ['case_number', 'infringement_type', 'csv_ground_truth', 'url']
            missing_required = []
            
            for field in required_fields:
                if field not in field_coverage or field_coverage[field]['coverage_percent'] < 50:
                    missing_required.append(field)
            
            # Ground truth distribution
            gt_distribution = {}
            for case in cases_data:
                gt_value = case.get('csv_ground_truth', 'Unknown')
                gt_distribution[gt_value] = gt_distribution.get(gt_value, 0) + 1
            
            validation_result = {
                'valid': len(missing_required) == 0,
                'case_count': total_cases,
                'field_coverage': field_coverage,
                'missing_required_fields': missing_required,
                'ground_truth_distribution': gt_distribution,
                'sample_cases': cases_data[:3] if len(cases_data) >= 3 else cases_data,
                'warnings': []
            }
            
            # Add warnings
            if gt_distribution.get('Valid', 0) + gt_distribution.get('Invalid', 0) < total_cases * 0.8:
                validation_result['warnings'].append(
                    "Ground truth field has unusual values. Expected 'Valid' or 'Invalid'."
                )
            
            if field_coverage.get('url', {}).get('coverage_percent', 0) < 90:
                validation_result['warnings'].append(
                    "Many cases are missing image URLs. This may affect processing."
                )
            
            return validation_result
            
        except Exception as e:
            return {
                'valid': False,
                'error': str(e),
                'case_count': 0
            }
    
    def get_csv_preview(self, csv_file_path: str, max_rows: int = 10) -> Dict[str, Any]:
        """Get preview of CSV data"""
        try:
            cases_data = self.parse_csv(csv_file_path)
            
            preview_data = cases_data[:max_rows] if cases_data else []
            
            # Get field statistics
            all_fields = set()
            for case in cases_data:
                all_fields.update(case.keys())
            
            return {
                'total_cases': len(cases_data),
                'preview_cases': preview_data,
                'all_fields': sorted(list(all_fields)),
                'preview_count': len(preview_data)
            }
            
        except Exception as e:
            return {
                'error': str(e),
                'total_cases': 0,
                'preview_cases': [],
                'all_fields': []
            }

# Utility functions for common operations
def parse_valo_csv(csv_file_path: str) -> List[Dict[str, Any]]:
    """Convenience function to parse VALO CSV"""
    parser = CSVParser()
    return parser.parse_csv(csv_file_path)

def validate_valo_csv(csv_file_path: str) -> Dict[str, Any]:
    """Convenience function to validate VALO CSV"""
    parser = CSVParser()
    return parser.validate_csv_structure(csv_file_path)