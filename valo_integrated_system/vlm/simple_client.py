"""
Simple Synchronous VLM Client - Based on working enhanced_valo_processor.py
Fast and reliable processing without async overhead
"""
import requests
import base64
import json
import time
import logging
from typing import Dict, Any, Optional
from pathlib import Path

logger = logging.getLogger(__name__)

class SimpleVLMClient:
    """Simple synchronous VLM client - based on working processor"""
    
    def __init__(self, endpoint: str = "http://**************:9500/v1", model: str = "VLM-38B-AWQ"):
        self.vlm_url = f"{endpoint.rstrip('/')}/chat/completions"
        self.model = model
        logger.info(f"Simple VLM Client initialized: {self.vlm_url}")
    
    def encode_image(self, image_path: str) -> str:
        """Encode image to base64"""
        try:
            with open(image_path, 'rb') as f:
                return base64.b64encode(f.read()).decode('utf-8')
        except Exception as e:
            logger.error(f"Failed to encode image {image_path}: {e}")
            raise
    
    def create_prompt(self, case_data: Dict[str, Any]) -> str:
        """Create VLM prompt for case analysis"""
        infringement_type = case_data.get('infringement_type', 'PPE Non-compliance')
        case_number = case_data.get('case_number', 'Unknown')
        
        return f"""You are analyzing a safety violation image for case {case_number}.

Analyze this image for "{infringement_type}" violations and provide a detailed assessment:

1. VISUAL DESCRIPTION
   - Main subject(s) in the image
   - Location/environment context
   - Equipment or structures visible

2. PERSON DETECTION
   - Is there a person visible? YES/NO
   - If YES, describe their position and activity
   - If NO, what is the main subject?

3. PPE ASSESSMENT (if person present)
   - Helmet/Hard hat: YES/NO/PARTIAL
   - High-visibility vest: YES/NO/PARTIAL
   - Other safety equipment visible

4. SAFETY VIOLATION ANALYSIS
   - Is there a genuine safety violation? YES/NO
   - If YES, describe the specific violation
   - Severity: MINOR/MODERATE/SEVERE

5. FALSE POSITIVE DETECTION
   Apply these rules for "{infringement_type}":
   - If NO person detected → likely False Positive (70-85%)
   - If structure/equipment detected instead of person → False Positive (75-90%)
   - If person with proper PPE flagged → False Positive (80-95%)

Format your final assessment EXACTLY as:

PERSON_PRESENT: [YES/NO]
MAIN_SUBJECT: [Person/Structure/Equipment/Other]
HELMET_STATUS: [YES/NO/PARTIAL/NA]
VEST_STATUS: [YES/NO/PARTIAL/NA]
SAFETY_VIOLATION: [YES/NO/UNCERTAIN]
VIOLATION_SEVERITY: [MINOR/MODERATE/SEVERE/NA]
FALSE_POSITIVE_LIKELIHOOD: [0-100]%
CONFIDENCE_SCORE: [0-100]%"""

    def analyze_case(self, case_data: Dict[str, Any], image_path: str) -> Dict[str, Any]:
        """Analyze a case with VLM - synchronous version"""
        start_time = time.time()
        
        try:
            # Encode image
            image_base64 = self.encode_image(image_path)
            
            # Create prompt
            prompt = self.create_prompt(case_data)
            
            # Create content array
            content = [
                {"type": "text", "text": prompt},
                {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{image_base64}"}}
            ]
            
            # Create payload
            payload = {
                "model": self.model,
                "messages": [{"role": "user", "content": content}],
                "temperature": 0.1,
                "max_tokens": 600
            }
            
            # Send request with timeout
            response = requests.post(self.vlm_url, json=payload, timeout=60)
            
            if response.status_code == 200:
                vlm_response = response.json()['choices'][0]['message']['content']
                parsed_result = self.parse_vlm_response(vlm_response)
                
                processing_time = (time.time() - start_time) * 1000
                
                return {
                    'success': True,
                    'query': prompt,
                    'raw_response': vlm_response,
                    'decision': parsed_result.get('decision', 'uncertain'),
                    'reasoning': parsed_result.get('reasoning', ''),
                    'person_confidence': parsed_result.get('person_confidence', 0),
                    'structure_confidence': parsed_result.get('structure_confidence', 0),
                    'violation_confidence': parsed_result.get('violation_confidence', 0),
                    'tokens_used': self.estimate_tokens(prompt + vlm_response),
                    'processing_time_ms': processing_time,
                    'filtered': parsed_result.get('fp_likelihood', 0) >= 70
                }
            else:
                raise Exception(f"VLM API error: {response.status_code} - {response.text}")
                
        except Exception as e:
            processing_time = (time.time() - start_time) * 1000
            logger.error(f"VLM analysis failed for {case_data.get('case_number', 'unknown')}: {e}")
            
            return {
                'success': False,
                'error': str(e),
                'processing_time_ms': processing_time,
                'tokens_used': 0,
                'decision': 'error',
                'reasoning': f'Processing failed: {str(e)}',
                'person_confidence': 0,
                'structure_confidence': 0,
                'violation_confidence': 0,
                'filtered': False
            }
    
    def parse_vlm_response(self, response: str) -> Dict[str, Any]:
        """Parse VLM response into structured data"""
        result = {
            'person_present': False,
            'main_subject': 'unknown',
            'helmet_status': 'na',
            'vest_status': 'na',
            'safety_violation': False,
            'violation_severity': 'na',
            'fp_likelihood': 50,
            'person_confidence': 0,
            'structure_confidence': 0,
            'violation_confidence': 0,
            'decision': 'uncertain',
            'reasoning': response[:500]  # First 500 chars as reasoning
        }
        
        try:
            # Parse structured fields
            lines = response.upper().split('\n')
            
            for line in lines:
                if 'PERSON_PRESENT:' in line:
                    result['person_present'] = 'YES' in line
                elif 'MAIN_SUBJECT:' in line:
                    if 'PERSON' in line:
                        result['main_subject'] = 'person'
                    elif 'STRUCTURE' in line:
                        result['main_subject'] = 'structure'
                    elif 'EQUIPMENT' in line:
                        result['main_subject'] = 'equipment'
                    else:
                        result['main_subject'] = 'other'
                elif 'HELMET_STATUS:' in line:
                    if 'YES' in line:
                        result['helmet_status'] = 'yes'
                    elif 'NO' in line:
                        result['helmet_status'] = 'no'
                    elif 'PARTIAL' in line:
                        result['helmet_status'] = 'partial'
                elif 'VEST_STATUS:' in line:
                    if 'YES' in line:
                        result['vest_status'] = 'yes'
                    elif 'NO' in line:
                        result['vest_status'] = 'no'
                    elif 'PARTIAL' in line:
                        result['vest_status'] = 'partial'
                elif 'SAFETY_VIOLATION:' in line:
                    result['safety_violation'] = 'YES' in line
                elif 'FALSE_POSITIVE_LIKELIHOOD:' in line:
                    # Extract percentage
                    import re
                    match = re.search(r'(\d+)%', line)
                    if match:
                        result['fp_likelihood'] = int(match.group(1))
                elif 'CONFIDENCE_SCORE:' in line:
                    import re
                    match = re.search(r'(\d+)%', line)
                    if match:
                        confidence = int(match.group(1))
                        result['violation_confidence'] = confidence
            
            # Calculate confidence scores based on findings
            if result['person_present']:
                result['person_confidence'] = 85
                if result['helmet_status'] == 'yes' and result['vest_status'] == 'yes':
                    result['person_confidence'] = 95
            else:
                result['structure_confidence'] = 80
                
            # Determine decision
            if result['fp_likelihood'] >= 70:
                result['decision'] = 'false_positive'
            elif result['safety_violation']:
                result['decision'] = 'valid_violation'
            else:
                result['decision'] = 'uncertain'
                
        except Exception as e:
            logger.warning(f"Failed to parse VLM response: {e}")
        
        return result
    
    def estimate_tokens(self, text: str) -> int:
        """Estimate token count"""
        return len(text.split()) * 1.3  # Rough estimation