"""
Real VLM API Client for VALO System
Integrates with VLM-38B-AWQ endpoint: http://**************:9500/v1
NO MOCK DATA - Real API integration only
"""
import aiohttp
import asyncio
import json
import time
import logging
import base64
from typing import Dict, Any, Optional, List
from pathlib import Path
import tiktoken
from PIL import Image
import io

logger = logging.getLogger(__name__)

class VLMClient:
    """Real VLM API client - NO MOCKS"""
    
    def __init__(self, endpoint: str = "http://**************:9500/v1", model: str = "VLM-38B-AWQ"):
        self.endpoint = endpoint.rstrip('/')
        self.model = model
        self.session = None
        
        # Initialize token encoder
        try:
            self.tokenizer = tiktoken.encoding_for_model("gpt-4")
        except:
            self.tokenizer = tiktoken.get_encoding("cl100k_base")
        
        logger.info(f"VLM Client initialized: {self.endpoint}")
    
    async def __aenter__(self):
        """Async context manager entry"""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=120),  # 2 minute timeout
            connector=aiohttp.TCPConnector(limit=10)
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.session:
            await self.session.close()
    
    def _encode_image(self, image_path: str) -> str:
        """Encode image to base64 for VLM API"""
        try:
            with open(image_path, 'rb') as f:
                image_data = f.read()
            return base64.b64encode(image_data).decode('utf-8')
        except Exception as e:
            logger.error(f"Failed to encode image {image_path}: {e}")
            raise
    
    def _count_tokens(self, text: str) -> int:
        """Count tokens in text"""
        try:
            return len(self.tokenizer.encode(text))
        except Exception as e:
            logger.warning(f"Token counting failed: {e}")
            return len(text) // 4  # Rough estimate
    
    def _create_violation_prompt(self, case_data: Dict[str, Any], image_base64: str) -> str:
        """Create comprehensive violation detection prompt"""
        infringement_type = case_data.get('infringement_type', 'Safety Violation')
        case_number = case_data.get('case_number', 'Unknown')
        
        prompt = f"""Analyze this workplace safety image for violations.

CASE: {case_number}
VIOLATION TYPE: {infringement_type}

ANALYSIS REQUIREMENTS:
1. Person Detection: Identify if people are present and assess confidence (0-100%)
2. Structure Detection: Identify structures/equipment and assess confidence (0-100%) 
3. Safety Violation Assessment: Determine if a genuine safety violation exists
4. Confidence Score: Overall violation confidence (0-100%)

RESPONSE FORMAT (JSON):
{{
    "person_present": true/false,
    "person_confidence": 0-100,
    "person_details": "description of people identified",
    "structure_detected": true/false,
    "structure_confidence": 0-100,
    "structure_details": "description of structures/equipment",
    "violation_detected": true/false,
    "violation_confidence": 0-100,
    "violation_description": "detailed explanation of violation or why no violation",
    "decision": "VALID VIOLATION" or "FALSE POSITIVE",
    "reasoning": "comprehensive reasoning for decision",
    "recommended_action": "filter/retain with explanation"
}}

CRITICAL: Focus on genuine safety violations. Many alerts are false positives due to:
- Poor image quality
- Normal work activities misidentified as violations
- Equipment/structures misidentified as people
- Lighting/shadow artifacts

Analyze the image thoroughly and provide accurate, detailed assessment."""

        return prompt
    
    async def analyze_case(self, case_data: Dict[str, Any], image_path: str) -> Dict[str, Any]:
        """Analyze a single case through VLM API - REAL PROCESSING"""
        start_time = time.time()
        
        if not self.session:
            raise RuntimeError("VLM Client not initialized. Use async context manager.")
        
        try:
            # Validate image exists
            if not Path(image_path).exists():
                raise FileNotFoundError(f"Image not found: {image_path}")
            
            # Encode image
            image_base64 = self._encode_image(image_path)
            
            # Create prompt
            prompt = self._create_violation_prompt(case_data, image_base64)
            token_count = self._count_tokens(prompt)
            
            # Prepare API request
            payload = {
                "model": self.model,
                "messages": [
                    {
                        "role": "user",
                        "content": [
                            {"type": "text", "text": prompt},
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/jpeg;base64,{image_base64}"
                                }
                            }
                        ]
                    }
                ],
                "max_tokens": 1000,
                "temperature": 0.1  # Low temperature for consistent analysis
            }
            
            logger.info(f"Sending case {case_data.get('case_number')} to VLM API")
            
            # Make API request
            async with self.session.post(
                f"{self.endpoint}/chat/completions",
                json=payload,
                headers={"Content-Type": "application/json"}
            ) as response:
                
                if response.status != 200:
                    error_text = await response.text()
                    logger.error(f"VLM API error {response.status}: {error_text}")
                    raise Exception(f"VLM API returned {response.status}: {error_text}")
                
                result = await response.json()
                
                # Extract VLM response
                vlm_content = result['choices'][0]['message']['content']
                
                # Parse JSON response from VLM
                try:
                    vlm_analysis = json.loads(vlm_content)
                except json.JSONDecodeError:
                    # Fallback if VLM doesn't return valid JSON
                    logger.warning(f"VLM returned non-JSON response for {case_data.get('case_number')}")
                    vlm_analysis = {
                        "person_present": False,
                        "person_confidence": 0,
                        "structure_detected": False,
                        "structure_confidence": 0,
                        "violation_detected": False,
                        "violation_confidence": 0,
                        "decision": "PROCESSING ERROR",
                        "reasoning": f"VLM response parsing failed: {vlm_content[:200]}..."
                    }
                
                processing_time = int((time.time() - start_time) * 1000)
                
                # Structure response data
                response_data = {
                    'vlm_query': prompt,
                    'vlm_response': vlm_analysis,
                    'vlm_decision': vlm_analysis.get('decision', 'UNKNOWN'),
                    'vlm_reasoning': vlm_analysis.get('reasoning', 'No reasoning provided'),
                    'person_confidence': float(vlm_analysis.get('person_confidence', 0)),
                    'structure_confidence': float(vlm_analysis.get('structure_confidence', 0)),
                    'violation_confidence': float(vlm_analysis.get('violation_confidence', 0)),
                    'tokens_used': token_count + len(self.tokenizer.encode(vlm_content)),
                    'processing_time_ms': processing_time,
                    'vlm_filtered': vlm_analysis.get('decision') == 'FALSE POSITIVE',
                    'person_present': vlm_analysis.get('person_present', False),
                    'structure_detected': vlm_analysis.get('structure_detected', False),
                    'main_subject': vlm_analysis.get('person_details', '') or vlm_analysis.get('structure_details', ''),
                    'api_response_raw': result  # Store full API response for debugging
                }
                
                logger.info(f"Completed VLM analysis for {case_data.get('case_number')} in {processing_time}ms")
                return response_data
                
        except Exception as e:
            processing_time = int((time.time() - start_time) * 1000)
            logger.error(f"VLM analysis failed for {case_data.get('case_number')}: {e}")
            
            # Return error response instead of mock data
            return {
                'vlm_query': prompt if 'prompt' in locals() else '',
                'vlm_response': {'error': str(e)},
                'vlm_decision': 'API_ERROR',
                'vlm_reasoning': f'VLM API call failed: {str(e)}',
                'person_confidence': 0,
                'structure_confidence': 0,
                'violation_confidence': 0,
                'tokens_used': 0,
                'processing_time_ms': processing_time,
                'vlm_filtered': False,
                'person_present': False,
                'structure_detected': False,
                'main_subject': 'ERROR: Analysis failed',
                'api_error': str(e)
            }
    
    async def batch_analyze(self, cases_data: List[Dict[str, Any]], 
                          image_paths: List[str], 
                          batch_size: int = 5) -> List[Dict[str, Any]]:
        """Batch analyze multiple cases with rate limiting"""
        results = []
        
        for i in range(0, len(cases_data), batch_size):
            batch_cases = cases_data[i:i+batch_size]
            batch_images = image_paths[i:i+batch_size]
            
            # Process batch concurrently
            tasks = []
            for case_data, image_path in zip(batch_cases, batch_images):
                task = self.analyze_case(case_data, image_path)
                tasks.append(task)
            
            batch_results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Handle exceptions in batch
            for j, result in enumerate(batch_results):
                if isinstance(result, Exception):
                    logger.error(f"Batch analysis failed for case {i+j}: {result}")
                    # Create error response
                    error_result = {
                        'vlm_decision': 'BATCH_ERROR',
                        'vlm_reasoning': f'Batch processing failed: {str(result)}',
                        'person_confidence': 0,
                        'structure_confidence': 0,
                        'violation_confidence': 0,
                        'tokens_used': 0,
                        'processing_time_ms': 0,
                        'vlm_filtered': False,
                        'api_error': str(result)
                    }
                    results.append(error_result)
                else:
                    results.append(result)
            
            # Rate limiting between batches
            if i + batch_size < len(cases_data):
                await asyncio.sleep(1)  # 1 second between batches
                logger.info(f"Processed batch {i//batch_size + 1}, continuing...")
        
        return results
    
    async def health_check(self) -> Dict[str, Any]:
        """Check VLM API health and availability"""
        try:
            if not self.session:
                self.session = aiohttp.ClientSession()
            
            async with self.session.get(f"{self.endpoint}/models") as response:
                if response.status == 200:
                    models = await response.json()
                    return {
                        'status': 'healthy',
                        'endpoint': self.endpoint,
                        'available_models': models.get('data', []),
                        'response_time_ms': response.headers.get('X-Response-Time', 'unknown')
                    }
                else:
                    return {
                        'status': 'unhealthy',
                        'endpoint': self.endpoint,
                        'error': f"HTTP {response.status}",
                        'message': await response.text()
                    }
        except Exception as e:
            return {
                'status': 'error',
                'endpoint': self.endpoint,
                'error': str(e),
                'message': 'Failed to connect to VLM API'
            }

# Convenience functions for common operations
async def analyze_single_case(case_data: Dict[str, Any], image_path: str, 
                            endpoint: str = "http://**************:9500/v1") -> Dict[str, Any]:
    """Analyze single case - convenience function"""
    async with VLMClient(endpoint) as client:
        return await client.analyze_case(case_data, image_path)

async def check_vlm_health(endpoint: str = "http://**************:9500/v1") -> Dict[str, Any]:
    """Check VLM API health - convenience function"""
    async with VLMClient(endpoint) as client:
        return await client.health_check()