#!/bin/bash

# VALO Integrated System Setup Script
# This script automates the setup process for the VALO system

set -e  # Exit on error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[✓]${NC} $1"
}

print_error() {
    echo -e "${RED}[✗]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[!]${NC} $1"
}

echo "======================================"
echo "VALO Integrated System Setup"
echo "======================================"
echo

# Check Python version
print_status "Checking Python version..."
python_version=$(python3 --version 2>&1 | grep -oE '[0-9]+\.[0-9]+' | head -1)
required_version="3.8"

if [ "$(printf '%s\n' "$required_version" "$python_version" | sort -V | head -n1)" = "$required_version" ]; then 
    print_status "Python $python_version is installed (>= $required_version required)"
else
    print_error "Python $python_version is too old. Please install Python >= $required_version"
    exit 1
fi

# Check PostgreSQL
print_status "Checking PostgreSQL..."
if command -v psql &> /dev/null; then
    print_status "PostgreSQL client is installed"
else
    print_warning "PostgreSQL client not found. Installing..."
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        sudo apt-get update && sudo apt-get install -y postgresql-client
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        brew install postgresql
    else
        print_error "Please install PostgreSQL manually"
        exit 1
    fi
fi

# Create virtual environment
print_status "Creating Python virtual environment..."
if [ ! -d "venv" ]; then
    python3 -m venv venv
    print_status "Virtual environment created"
else
    print_warning "Virtual environment already exists"
fi

# Activate virtual environment
print_status "Activating virtual environment..."
source venv/bin/activate

# Upgrade pip
print_status "Upgrading pip..."
pip install --upgrade pip

# Install requirements
print_status "Installing Python dependencies..."
pip install -r requirements.txt

# Create necessary directories
print_status "Creating required directories..."
mkdir -p logs data/images templates static

# Copy configuration template
if [ ! -f "config.yaml" ]; then
    print_status "Creating configuration file..."
    cp config.yaml.template config.yaml
    print_warning "Please edit config.yaml with your database credentials"
else
    print_warning "config.yaml already exists, skipping..."
fi

# Database setup prompt
echo
read -p "Do you want to set up the PostgreSQL database now? (y/n) " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    print_status "Setting up PostgreSQL database..."
    
    read -p "PostgreSQL host [localhost]: " db_host
    db_host=${db_host:-localhost}
    
    read -p "PostgreSQL port [5432]: " db_port
    db_port=${db_port:-5432}
    
    read -p "PostgreSQL admin user [postgres]: " admin_user
    admin_user=${admin_user:-postgres}
    
    # Create database and user
    print_status "Creating database and user..."
    psql -h "$db_host" -p "$db_port" -U "$admin_user" <<EOF
-- Create database
CREATE DATABASE valo_system;

-- Create user
CREATE USER valo_user WITH PASSWORD 'valo_password';

-- Grant privileges
GRANT ALL PRIVILEGES ON DATABASE valo_system TO valo_user;

-- Connect to the database
\c valo_system

-- Create UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Grant schema permissions
GRANT ALL ON SCHEMA public TO valo_user;
EOF

    # Run schema migration
    print_status "Running database schema migration..."
    PGPASSWORD=valo_password psql -h "$db_host" -p "$db_port" -U valo_user -d valo_system -f database/postgresql_schema.sql
    
    print_status "Database setup complete!"
else
    print_warning "Skipping database setup. Run manually later with:"
    echo "  psql -U valo_user -d valo_system -f database/postgresql_schema.sql"
fi

# Docker setup prompt
echo
read -p "Do you want to set up Docker containers? (y/n) " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    if command -v docker &> /dev/null && command -v docker-compose &> /dev/null; then
        print_status "Building Docker containers..."
        docker-compose build
        print_status "Docker setup complete!"
        print_warning "Start with: docker-compose up"
    else
        print_error "Docker or docker-compose not found. Please install Docker first."
    fi
fi

# Create .env file if not exists
if [ ! -f ".env" ]; then
    print_status "Creating .env file..."
    cp .env.example .env
    print_warning "Please update .env with your configuration"
fi

# Final instructions
echo
echo "======================================"
echo "Setup Complete!"
echo "======================================"
echo
echo "Next steps:"
echo "1. Edit config.yaml with your settings"
echo "2. Update .env file if using Docker"
echo "3. Place your image data in: ai_farm_images_fixed_250703/"
echo
echo "To start the system:"
echo "  - With virtual env: python orchestrator.py dashboard"
echo "  - With Docker: docker-compose up"
echo
echo "For help: python orchestrator.py --help"
echo

# Create quick start script
cat > quickstart.sh << 'EOL'
#!/bin/bash
# Quick start script for VALO system

# Activate virtual environment
source venv/bin/activate

# Check if config exists
if [ ! -f "config.yaml" ]; then
    echo "Error: config.yaml not found. Run ./setup.sh first"
    exit 1
fi

# Start the dashboard
echo "Starting VALO Integrated System..."
python orchestrator.py dashboard
EOL

chmod +x quickstart.sh
print_status "Created quickstart.sh for easy startup"