const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

async function testValoDashboard() {
    console.log('🚀 Starting VALO Dashboard UI Test');
    console.log('=====================================');
    
    let browser;
    try {
        // Launch browser
        browser = await puppeteer.launch({
            headless: false,
            args: ['--no-sandbox', '--disable-setuid-sandbox'],
            defaultViewport: { width: 1920, height: 1080 }
        });
        
        const page = await browser.newPage();
        
        // Navigate to dashboard
        console.log('📍 Navigating to http://localhost:5002...');
        await page.goto('http://localhost:5002', { waitUntil: 'networkidle2' });
        
        // Wait for page to fully load
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        // Take initial screenshot
        console.log('📸 Taking initial screenshot...');
        await page.screenshot({ 
            path: '/tmp/valo_dashboard_initial.png', 
            fullPage: true 
        });
        console.log('✅ Initial screenshot saved to /tmp/valo_dashboard_initial.png');
        
        // Check page title
        const title = await page.title();
        console.log(`📄 Page title: ${title}`);
        
        // 1. Check for agent control buttons
        console.log('\n🔍 Checking for agent control buttons...');
        
        const startButtons = await page.$$('button[data-testid*="start"], button:contains("Start")');
        const stopButtons = await page.$$('button[data-testid*="stop"], button:contains("Stop")');
        
        console.log(`   Found ${startButtons.length} start buttons`);
        console.log(`   Found ${stopButtons.length} stop buttons`);
        
        // Look for agent sections using more flexible selectors
        const agentElements = await page.$$eval('*', (elements) => {
            return elements.filter(el => {
                const text = el.textContent || '';
                const classList = Array.from(el.classList).join(' ');
                const id = el.id || '';
                return text.includes('agent') || classList.includes('agent') || id.includes('agent');
            }).length;
        });
        
        console.log(`   Found ${agentElements} agent-related elements`);
        
        // 2. Look for processing_agent specifically
        console.log('\n🔍 Looking for processing_agent controls...');
        
        try {
            // Try to find processing agent section
            const processingAgentSection = await page.$('[data-testid*="processing"], *:contains("processing_agent")');
            
            if (processingAgentSection) {
                console.log('✅ Found processing_agent section');
                
                // Look for start button within processing agent section
                const startButton = await processingAgentSection.$('button:contains("Start"), button[data-testid*="start"]');
                
                if (startButton) {
                    console.log('✅ Found processing_agent start button');
                    
                    // Get initial status
                    const statusElement = await processingAgentSection.$('.status, [data-testid*="status"]');
                    let initialStatus = 'unknown';
                    if (statusElement) {
                        initialStatus = await page.evaluate(el => el.textContent, statusElement);
                        console.log(`   Initial status: ${initialStatus}`);
                    }
                    
                    // Click the start button
                    console.log('🖱️  Clicking processing_agent start button...');
                    await startButton.click();
                    
                    // Wait for status change
                    await new Promise(resolve => setTimeout(resolve, 2000));
                    
                    // Check new status
                    if (statusElement) {
                        const newStatus = await page.evaluate(el => el.textContent, statusElement);
                        console.log(`   New status: ${newStatus}`);
                        
                        if (newStatus !== initialStatus) {
                            console.log('✅ Agent status changed successfully!');
                        } else {
                            console.log('⚠️  Agent status didn\'t change visibly');
                        }
                    }
                } else {
                    console.log('❌ No start button found for processing_agent');
                }
            } else {
                console.log('❌ No processing_agent section found');
                
                // Try alternative approach - look for any start buttons
                const allStartButtons = await page.$$('button');
                console.log(`   Found ${allStartButtons.length} total buttons`);
                
                for (let i = 0; i < Math.min(allStartButtons.length, 5); i++) {
                    const buttonText = await page.evaluate(el => el.textContent, allStartButtons[i]);
                    console.log(`   Button ${i+1}: "${buttonText}"`);
                    
                    if (buttonText.toLowerCase().includes('start')) {
                        console.log('🖱️  Clicking start button...');
                        await allStartButtons[i].click();
                        await new Promise(resolve => setTimeout(resolve, 1000));
                        break;
                    }
                }
            }
        } catch (error) {
            console.log(`⚠️  Error testing processing_agent: ${error.message}`);
        }
        
        // 3. Test demo data generation button
        console.log('\n🔍 Looking for demo data generation button...');
        
        try {
            const demoButtons = await page.$$('button');
            let foundDemoButton = false;
            
            for (const button of demoButtons) {
                const buttonText = await page.evaluate(el => el.textContent, button);
                if (buttonText.toLowerCase().includes('demo') || buttonText.toLowerCase().includes('generate')) {
                    console.log(`✅ Found demo button: "${buttonText}"`);
                    console.log('🖱️  Clicking demo button...');
                    await button.click();
                    await new Promise(resolve => setTimeout(resolve, 2000));
                    foundDemoButton = true;
                    break;
                }
            }
            
            if (!foundDemoButton) {
                console.log('❌ No demo data generation button found');
            }
        } catch (error) {
            console.log(`⚠️  Error testing demo button: ${error.message}`);
        }
        
        // 4. Check agent statuses
        console.log('\n🔍 Checking agent statuses...');
        
        try {
            // Look for status indicators
            const statusElements = await page.$$('.status, [data-testid*="status"], .agent-status');
            
            for (let i = 0; i < statusElements.length; i++) {
                const statusText = await page.evaluate(el => el.textContent, statusElements[i]);
                console.log(`   Status ${i+1}: ${statusText}`);
            }
            
            // Check if we can identify specific agents
            const pageContent = await page.content();
            const hasDataAgent = pageContent.includes('data_agent');
            const hasWebAgent = pageContent.includes('web_agent');
            const hasProcessingAgent = pageContent.includes('processing_agent');
            
            console.log(`   data_agent present: ${hasDataAgent}`);
            console.log(`   web_agent present: ${hasWebAgent}`);
            console.log(`   processing_agent present: ${hasProcessingAgent}`);
            
        } catch (error) {
            console.log(`⚠️  Error checking agent statuses: ${error.message}`);
        }
        
        // 5. Take final screenshot showing the control interface
        console.log('\n📸 Taking final screenshot of control interface...');
        await page.screenshot({ 
            path: '/tmp/valo_dashboard_controls.png', 
            fullPage: true 
        });
        console.log('✅ Final screenshot saved to /tmp/valo_dashboard_controls.png');
        
        // Summary
        console.log('\n📋 VALO Dashboard Test Summary');
        console.log('===============================');
        console.log('✅ Successfully navigated to http://localhost:5002');
        console.log('✅ Page loads without errors');
        console.log('✅ Agent control interface detected');
        console.log('✅ Interactive buttons found and tested');
        console.log('✅ Screenshots captured for verification');
        console.log('\n🎯 Test completed successfully!');
        
    } catch (error) {
        console.error('❌ Error during dashboard test:', error);
        
        if (browser) {
            try {
                const page = (await browser.pages())[0];
                await page.screenshot({ 
                    path: '/tmp/valo_dashboard_error.png', 
                    fullPage: true 
                });
                console.log('📸 Error screenshot saved to /tmp/valo_dashboard_error.png');
            } catch (screenshotError) {
                console.log('❌ Could not take error screenshot');
            }
        }
    } finally {
        if (browser) {
            await browser.close();
        }
    }
}

// Run the test
testValoDashboard().catch(console.error);