#!/usr/bin/env python3
"""
VALO System Orchestrator: Coordinates all agents and provides CLI interface
Main entry point for the integrated system
"""

import asyncio
import click
import yaml
import pandas as pd
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional
import logging
import signal
import sys
import json

from agents.data_agent import DataAgent
from agents.processing_agent import ProcessingAgent
from agents.analytics_agent import AnalyticsAgent
from agents.web_agent import WebAgent

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class VALOOrchestrator:
    def __init__(self, config_path: str = 'config.yaml'):
        """Initialize orchestrator with configuration"""
        self.config = self._load_config(config_path)
        self.agents = {}
        self.running = False
        self.current_round_id = None
        
    def _load_config(self, config_path: str) -> Dict:
        """Load configuration from YAML file"""
        if Path(config_path).exists():
            with open(config_path, 'r') as f:
                return yaml.safe_load(f)
        else:
            # Default configuration
            return {
                'database': {
                    'host': 'localhost',
                    'port': 5432,
                    'database': 'valo_system',
                    'user': 'valo_user',
                    'password': 'valo_password'
                },
                'vlm': {
                    'url': 'http://**************:9500/v1/chat/completions',
                    'model': 'VLM-38B-AWQ',
                    'temperature': 0.1,
                    'max_tokens': 600
                },
                'web': {
                    'host': '0.0.0.0',
                    'port': 5000
                },
                'processing': {
                    'workers': 3,
                    'batch_size': 10
                }
            }
    
    async def initialize(self):
        """Initialize all agents"""
        logger.info("Initializing VALO System...")
        
        # Initialize data agent
        self.agents['data'] = DataAgent(self.config['database'])
        await self.agents['data'].initialize()
        
        # Initialize processing agent
        self.agents['processing'] = ProcessingAgent(
            self.agents['data'],
            self.config['vlm']
        )
        await self.agents['processing'].initialize(
            num_workers=self.config['processing']['workers']
        )
        
        # Initialize analytics agent
        self.agents['analytics'] = AnalyticsAgent(self.agents['data'])
        await self.agents['analytics'].initialize()
        
        # Initialize web agent (but don't run it yet)
        self.agents['web'] = WebAgent(
            self.agents['data'],
            self.agents['analytics'],
            host=self.config['web']['host'],
            port=self.config['web']['port']
        )
        await self.agents['web'].initialize()
        
        self.running = True
        logger.info("All agents initialized successfully")
    
    async def shutdown(self):
        """Gracefully shutdown all agents"""
        logger.info("Shutting down VALO System...")
        self.running = False
        
        # Close agents in reverse order
        for agent_name in ['web', 'analytics', 'processing', 'data']:
            if agent_name in self.agents:
                await self.agents[agent_name].close()
        
        logger.info("Shutdown complete")
    
    async def create_round(self, name: Optional[str] = None) -> str:
        """Create a new processing round"""
        if not name:
            name = f"Round_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        self.current_round_id = await self.agents['data'].create_round(name)
        logger.info(f"Created round: {name} (ID: {self.current_round_id})")
        return str(self.current_round_id)
    
    async def process_csv(self, csv_path: str, round_id: Optional[str] = None,
                         limit: Optional[int] = None):
        """Process cases from CSV file"""
        if not round_id and not self.current_round_id:
            await self.create_round()
            round_id = self.current_round_id
        elif not round_id:
            round_id = self.current_round_id
        
        # Load CSV
        logger.info(f"Loading CSV from {csv_path}")
        df = pd.read_csv(csv_path)
        
        if limit:
            df = df.head(limit)
        
        logger.info(f"Loaded {len(df)} cases")
        
        # Update round total
        await self.agents['data'].update_round(
            round_id,
            total_cases=len(df)
        )
        
        # Prepare cases for processing
        cases = []
        for idx, row in df.iterrows():
            case_data = {
                'case_number': row['Case Int. ID'],
                'infringement_type': row['Type of Infringement'],
                'csv_ground_truth': row['Alert Status'],
                'csv_remarks': row['Remarks'],
                'camera_id': row['Camera'],
                'terminal': row['Terminal'],
                'alert_timestamp': row.get('Alert Start Time'),
                
                # Image paths - construct from case number
                'cropped_image': f"ai_farm_images_fixed_250703/ai_farm_images_fixed/"
                               f"{'valid' if row['Alert Status'] == 'Valid' else 'invalid'}/"
                               f"{row['Case Int. ID']}_cropped_"
                               f"{'valid' if row['Alert Status'] == 'Valid' else 'invalid'}.JPEG",
                'source_image': f"ai_farm_images_fixed_250703/ai_farm_images_fixed/"
                              f"{'valid' if row['Alert Status'] == 'Valid' else 'invalid'}/"
                              f"{row['Case Int. ID']}_source_"
                              f"{'valid' if row['Alert Status'] == 'Valid' else 'invalid'}.JPEG"
            }
            
            # Insert basic case data
            await self.agents['data'].insert_case(round_id, case_data)
            cases.append(case_data)
        
        # Start processing
        logger.info(f"Starting processing of {len(cases)} cases")
        await self.agents['processing'].process_cases(round_id, cases)
        
        # Wait for processing to complete
        await self.agents['processing'].processing_queue.join()
        
        # Update round status
        await self.agents['data'].update_round(
            round_id,
            status='completed',
            end_time=datetime.now()
        )
        
        # Generate final report
        report = await self.agents['analytics'].generate_report(round_id)
        logger.info("Processing complete!")
        logger.info(f"FP Detection Rate: {report['sections']['summary'].get('fp_detection_rate', 0):.1f}%")
    
    def run_web_interface(self):
        """Run the web interface"""
        logger.info("Starting web interface...")
        logger.info(f"Dashboard URL: http://localhost:{self.config['web']['port']}")
        self.agents['web'].run()

# CLI Commands
@click.group()
def cli():
    """VALO Integrated System CLI"""
    pass

@cli.command()
@click.option('--config', default='config.yaml', help='Configuration file path')
def dashboard(config):
    """Start the web dashboard"""
    orchestrator = VALOOrchestrator(config)
    
    async def run():
        await orchestrator.initialize()
        orchestrator.run_web_interface()
    
    try:
        asyncio.run(run())
    except KeyboardInterrupt:
        logger.info("Dashboard stopped by user")

@cli.command()
@click.argument('csv_path')
@click.option('--round-name', help='Name for the processing round')
@click.option('--limit', type=int, help='Limit number of cases to process')
@click.option('--config', default='config.yaml', help='Configuration file path')
def process(csv_path, round_name, limit, config):
    """Process cases from CSV file"""
    orchestrator = VALOOrchestrator(config)
    
    async def run():
        await orchestrator.initialize()
        
        # Create round
        round_id = await orchestrator.create_round(round_name)
        
        # Process CSV
        await orchestrator.process_csv(csv_path, round_id, limit)
        
        await orchestrator.shutdown()
    
    try:
        asyncio.run(run())
    except KeyboardInterrupt:
        logger.info("Processing stopped by user")

@cli.command()
@click.argument('csv_path')
@click.option('--round-name', help='Name for the processing round')
@click.option('--limit', type=int, help='Limit number of cases to process')
@click.option('--config', default='config.yaml', help='Configuration file path')
def full(csv_path, round_name, limit, config):
    """Process CSV and start dashboard (full system)"""
    orchestrator = VALOOrchestrator(config)
    
    async def setup_and_process():
        await orchestrator.initialize()
        
        # Create round
        round_id = await orchestrator.create_round(round_name)
        
        # Start processing in background
        process_task = asyncio.create_task(
            orchestrator.process_csv(csv_path, round_id, limit)
        )
        
        # Give it a moment to start
        await asyncio.sleep(2)
        
        return process_task
    
    # Run setup and processing
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    process_task = loop.run_until_complete(setup_and_process())
    
    # Run web interface (blocking)
    try:
        orchestrator.run_web_interface()
    except KeyboardInterrupt:
        logger.info("System stopped by user")
        process_task.cancel()
        loop.run_until_complete(orchestrator.shutdown())

@cli.command()
@click.option('--config', default='config.yaml', help='Configuration file path')
def status(config):
    """Check system status"""
    orchestrator = VALOOrchestrator(config)
    
    async def check_status():
        await orchestrator.initialize()
        
        # Get agent statuses
        statuses = await orchestrator.agents['data'].get_agent_statuses()
        
        print("\n=== VALO System Status ===")
        print("\nAgent Status:")
        for agent in statuses:
            status_icon = "✅" if agent['status'] == 'online' else "❌"
            print(f"  {status_icon} {agent['agent_name']}: {agent['status']}")
            if agent['last_heartbeat']:
                print(f"     Last heartbeat: {agent['last_heartbeat']}")
        
        # Get active rounds
        rounds = await orchestrator.agents['data'].get_active_rounds()
        print(f"\nActive Rounds: {len(rounds)}")
        for round_data in rounds[:5]:
            print(f"  - {round_data['name']} (ID: {round_data['id']})")
            print(f"    Status: {round_data['status']}")
            print(f"    Progress: {round_data['processed_cases']}/{round_data['total_cases']}")
        
        await orchestrator.shutdown()
    
    asyncio.run(check_status())

@cli.command()
def setup():
    """Setup database and create config file"""
    print("VALO System Setup")
    print("-" * 50)
    
    # Database configuration
    db_config = {
        'host': click.prompt('PostgreSQL host', default='localhost'),
        'port': click.prompt('PostgreSQL port', default=5432, type=int),
        'database': click.prompt('Database name', default='valo_system'),
        'user': click.prompt('Database user', default='valo_user'),
        'password': click.prompt('Database password', hide_input=True)
    }
    
    # VLM configuration
    vlm_config = {
        'url': click.prompt('VLM API URL', 
                          default='http://**************:9500/v1/chat/completions'),
        'model': click.prompt('VLM Model', default='VLM-38B-AWQ'),
        'temperature': 0.1,
        'max_tokens': 600
    }
    
    # Web configuration
    web_config = {
        'host': click.prompt('Web server host', default='0.0.0.0'),
        'port': click.prompt('Web server port', default=5000, type=int)
    }
    
    # Processing configuration
    processing_config = {
        'workers': click.prompt('Number of processing workers', default=3, type=int),
        'batch_size': 10
    }
    
    # Save configuration
    config = {
        'database': db_config,
        'vlm': vlm_config,
        'web': web_config,
        'processing': processing_config
    }
    
    with open('config.yaml', 'w') as f:
        yaml.dump(config, f, default_flow_style=False)
    
    print("\n✅ Configuration saved to config.yaml")
    print("\nNext steps:")
    print("1. Create PostgreSQL database:")
    print(f"   CREATE DATABASE {db_config['database']};")
    print(f"   CREATE USER {db_config['user']} WITH PASSWORD 'your-password';")
    print(f"   GRANT ALL PRIVILEGES ON DATABASE {db_config['database']} TO {db_config['user']};")
    print("\n2. Run database migrations:")
    print("   psql -U {user} -d {database} -f database/postgresql_schema.sql")
    print("\n3. Start the system:")
    print("   python orchestrator.py dashboard")

if __name__ == '__main__':
    cli()