#!/usr/bin/env python3
"""
VALO System Health Check Script
Verifies all components are properly configured and accessible
"""

import asyncio
import yaml
import asyncpg
import aiohttp
import sys
import os
from pathlib import Path
from datetime import datetime

# ANSI color codes
GREEN = '\033[92m'
RED = '\033[91m'
YELLOW = '\033[93m'
BLUE = '\033[94m'
RESET = '\033[0m'

def print_status(status, message):
    """Print colored status message"""
    if status == "OK":
        print(f"{GREEN}[✓]{RESET} {message}")
    elif status == "ERROR":
        print(f"{RED}[✗]{RESET} {message}")
    elif status == "WARNING":
        print(f"{YELLOW}[!]{RESET} {message}")
    elif status == "INFO":
        print(f"{BLUE}[i]{RESET} {message}")

async def check_config():
    """Check if configuration file exists and is valid"""
    print("\n1. Checking configuration...")
    
    if not Path("config.yaml").exists():
        print_status("ERROR", "config.yaml not found. Run './setup.sh' first")
        return None
    
    try:
        with open("config.yaml", 'r') as f:
            config = yaml.safe_load(f)
        print_status("OK", "Configuration file loaded successfully")
        return config
    except Exception as e:
        print_status("ERROR", f"Failed to load config.yaml: {str(e)}")
        return None

async def check_database(config):
    """Check PostgreSQL connection and schema"""
    print("\n2. Checking PostgreSQL database...")
    
    try:
        # Test connection
        conn = await asyncpg.connect(
            host=config['database']['host'],
            port=config['database']['port'],
            user=config['database']['user'],
            password=config['database']['password'],
            database=config['database']['database']
        )
        print_status("OK", "Connected to PostgreSQL database")
        
        # Check if tables exist
        tables = await conn.fetch("""
            SELECT tablename FROM pg_tables 
            WHERE schemaname = 'public' 
            AND tablename IN ('rounds', 'cases', 'agent_status', 'processing_logs')
        """)
        
        if len(tables) == 4:
            print_status("OK", "All required tables exist")
        else:
            print_status("WARNING", f"Only {len(tables)}/4 tables found. Run schema migration")
        
        # Check UUID extension
        extensions = await conn.fetch("SELECT extname FROM pg_extension WHERE extname = 'uuid-ossp'")
        if extensions:
            print_status("OK", "UUID extension is installed")
        else:
            print_status("WARNING", "UUID extension not found. Run: CREATE EXTENSION \"uuid-ossp\";")
        
        await conn.close()
        return True
        
    except Exception as e:
        print_status("ERROR", f"Database connection failed: {str(e)}")
        return False

async def check_vlm_api(config):
    """Check VLM API endpoint"""
    print("\n3. Checking VLM API endpoint...")
    
    vlm_url = config['vlm']['url']
    
    try:
        async with aiohttp.ClientSession() as session:
            # Try a simple request to check if endpoint is reachable
            async with session.get(vlm_url.replace('/chat/completions', '/health'), 
                                 timeout=aiohttp.ClientTimeout(total=5)) as response:
                if response.status in [200, 404]:  # 404 is ok, means server is responding
                    print_status("OK", f"VLM endpoint is reachable: {vlm_url}")
                    return True
    except:
        pass
    
    # If health check fails, try the actual endpoint
    try:
        async with aiohttp.ClientSession() as session:
            test_payload = {
                "model": config['vlm']['model'],
                "messages": [{"role": "user", "content": "test"}],
                "max_tokens": 10
            }
            async with session.post(vlm_url, json=test_payload,
                                  timeout=aiohttp.ClientTimeout(total=10)) as response:
                if response.status == 200:
                    print_status("OK", f"VLM API is functional")
                    return True
                else:
                    print_status("WARNING", f"VLM API returned status {response.status}")
                    return True
    except Exception as e:
        print_status("ERROR", f"Cannot reach VLM API: {str(e)}")
        print_status("INFO", "Make sure the VLM service is running")
        return False

def check_directories():
    """Check if required directories exist"""
    print("\n4. Checking directories...")
    
    required_dirs = ['logs', 'data', 'templates', 'static', 'agents']
    missing = []
    
    for dir_name in required_dirs:
        if Path(dir_name).exists():
            print_status("OK", f"Directory '{dir_name}' exists")
        else:
            missing.append(dir_name)
            print_status("WARNING", f"Directory '{dir_name}' is missing")
    
    if missing:
        print_status("INFO", "Run 'mkdir -p " + " ".join(missing) + "' to create missing directories")
    
    return len(missing) == 0

def check_images():
    """Check if image directory is configured"""
    print("\n5. Checking image data...")
    
    image_path = Path("ai_farm_images_fixed_250703/ai_farm_images_fixed")
    
    if image_path.exists():
        valid_path = image_path / "valid"
        invalid_path = image_path / "invalid"
        
        if valid_path.exists() and invalid_path.exists():
            valid_count = len(list(valid_path.glob("*.JPEG")))
            invalid_count = len(list(invalid_path.glob("*.JPEG")))
            print_status("OK", f"Found {valid_count} valid and {invalid_count} invalid images")
            return True
        else:
            print_status("WARNING", "Image directories not properly structured")
            return False
    else:
        print_status("WARNING", "Image directory not found. Update path in config.yaml")
        print_status("INFO", f"Expected path: {image_path}")
        return False

def check_python_deps():
    """Check if Python dependencies are installed"""
    print("\n6. Checking Python dependencies...")
    
    try:
        import flask
        import pandas
        import asyncpg
        import aiohttp
        import matplotlib
        import tiktoken
        print_status("OK", "All core Python dependencies are installed")
        return True
    except ImportError as e:
        print_status("ERROR", f"Missing Python dependency: {str(e)}")
        print_status("INFO", "Run: pip install -r requirements.txt")
        return False

async def check_ports(config):
    """Check if required ports are available"""
    print("\n7. Checking port availability...")
    
    web_port = config['web']['port']
    
    import socket
    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    result = sock.connect_ex(('localhost', web_port))
    sock.close()
    
    if result != 0:
        print_status("OK", f"Port {web_port} is available for web server")
        return True
    else:
        print_status("WARNING", f"Port {web_port} is already in use")
        print_status("INFO", "Stop the service using this port or change it in config.yaml")
        return False

async def main():
    """Run all health checks"""
    print("=" * 50)
    print("VALO Integrated System Health Check")
    print("=" * 50)
    print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    all_ok = True
    
    # Check configuration
    config = await check_config()
    if not config:
        all_ok = False
    else:
        # Check database
        if not await check_database(config):
            all_ok = False
        
        # Check VLM API
        if not await check_vlm_api(config):
            all_ok = False
        
        # Check ports
        if not await check_ports(config):
            all_ok = False
    
    # Check directories
    if not check_directories():
        all_ok = False
    
    # Check images
    if not check_images():
        all_ok = False
    
    # Check Python dependencies
    if not check_python_deps():
        all_ok = False
    
    # Summary
    print("\n" + "=" * 50)
    if all_ok:
        print(f"{GREEN}All systems operational!{RESET}")
        print("\nYou can now start the system with:")
        print("  python orchestrator.py dashboard")
        return 0
    else:
        print(f"{RED}Some issues need to be resolved.{RESET}")
        print("\nPlease address the issues above before starting the system.")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)