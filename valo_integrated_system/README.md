# VALO Integrated System

A comprehensive multi-agent system for false positive detection in safety monitoring, featuring PostgreSQL database, VLM processing, real-time analytics, and web dashboard.

## System Architecture

The system consists of five coordinated agents:
- **Data Agent**: Handles all PostgreSQL database operations
- **Processing Agent**: Manages VLM API interactions and case processing
- **Analytics Agent**: Generates insights, visualizations, and reports
- **Web Agent**: Provides Flask-based dashboard and review interface
- **Orchestrator**: CLI interface that coordinates all agents

## Prerequisites

- Python 3.8+
- PostgreSQL 13+
- Node.js 14+ (for frontend assets)
- Docker (optional, for containerized deployment)

## Quick Start

### 1. Database Setup

First, create the PostgreSQL database and user:

```bash
sudo -u postgres psql
```

```sql
-- Create database and user
CREATE DATABASE valo_system;
CREATE USER valo_user WITH PASSWORD 'your-secure-password';
GRANT ALL PRIVILEGES ON DATABASE valo_system TO valo_user;
\q
```

Install required PostgreSQL extensions:
```bash
psql -U valo_user -d valo_system -c "CREATE EXTENSION IF NOT EXISTS \"uuid-ossp\";"
```

Run the schema migration:
```bash
psql -U valo_user -d valo_system -f database/postgresql_schema.sql
```

### 2. Python Environment Setup

Create and activate a virtual environment:
```bash
python3 -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

Install dependencies:
```bash
pip install -r requirements.txt
```

### 3. Configuration

Run the setup wizard to create your configuration:
```bash
python orchestrator.py setup
```

Or manually create `config.yaml`:
```yaml
database:
  host: localhost
  port: 5432
  database: valo_system
  user: valo_user
  password: your-secure-password

vlm:
  url: http://**************:9500/v1/chat/completions
  model: VLM-38B-AWQ
  temperature: 0.1
  max_tokens: 600

web:
  host: 0.0.0.0
  port: 5000

processing:
  workers: 3
  batch_size: 10
```

### 4. Running the System

#### Option A: Full System (Process CSV + Dashboard)
```bash
python orchestrator.py full path/to/your/data.csv --round-name "Production Test"
```

#### Option B: Process Only
```bash
python orchestrator.py process path/to/your/data.csv --limit 100
```

#### Option C: Dashboard Only
```bash
python orchestrator.py dashboard
```

Access the dashboard at: http://localhost:5000

## CSV File Format

The system expects CSV files with the following columns:
- `Case Int. ID`: Unique case identifier (e.g., V1250630118)
- `Type of Infringement`: Violation type (e.g., "PPE Non-compliance")
- `Alert Status`: Ground truth (Valid/Invalid)
- `Remarks`: Human validation remarks
- `Camera`: Camera identifier
- `Terminal`: Terminal location
- `Alert Start Time`: Timestamp of alert

Image files should be organized as:
```
ai_farm_images_fixed_250703/ai_farm_images_fixed/
├── valid/
│   ├── {case_id}_cropped_valid.JPEG
│   └── {case_id}_source_valid.JPEG
└── invalid/
    ├── {case_id}_cropped_invalid.JPEG
    └── {case_id}_source_invalid.JPEG
```

## Dashboard Features

### Main Dashboard (`/`)
- Real-time processing progress
- Quadrant distribution (valid_passed, valid_failed, invalid_passed, invalid_failed)
- Performance metrics and insights
- Live case processing feed

### Review Interface (`/review`)
- Filter cases by quadrant, infringement type, or search
- View case images (cropped and source)
- See VLM analysis results
- Add human review notes

### Analytics (`/analytics`)
- Confidence score distributions
- Accuracy by infringement type
- Token efficiency analysis
- Export reports

## API Endpoints

The web agent exposes RESTful APIs:

- `GET /api/rounds` - List all processing rounds
- `POST /api/rounds` - Create new round
- `GET /api/rounds/{round_id}/status` - Get round statistics
- `GET /api/rounds/{round_id}/cases` - Get cases with filtering
- `GET /api/cases/{case_id}` - Get case details
- `POST /api/cases/{case_id}/review` - Update human review
- `GET /api/analytics/{round_id}/{viz_type}` - Get visualizations
- `GET /api/analytics/{round_id}/report` - Get comprehensive report
- `GET /api/agents/status` - Check agent statuses

## Performance Tuning

### Database Optimization
- Indexes are created on frequently queried columns
- JSONB fields enable flexible data storage
- Connection pooling manages concurrent access

### Processing Optimization
- Adjust `workers` in config for parallel processing
- Token limits balance accuracy vs speed
- Retry logic handles VLM API timeouts

### Caching
- Analytics results are cached for 24 hours
- Visualizations are generated on-demand
- Round statistics use materialized views

## Monitoring

Check system status:
```bash
python orchestrator.py status
```

View processing logs:
```sql
SELECT * FROM processing_logs 
WHERE log_level = 'ERROR' 
ORDER BY created_at DESC;
```

Monitor agent health:
```sql
SELECT * FROM agent_status;
```

## Troubleshooting

### Database Connection Issues
- Verify PostgreSQL is running: `systemctl status postgresql`
- Check credentials in config.yaml
- Ensure database exists and user has permissions

### VLM API Timeouts
- Increase timeout in processing agent
- Reduce worker count if overwhelmed
- Check VLM endpoint availability

### Memory Issues
- Reduce batch_size in config
- Process smaller CSV chunks
- Increase system memory allocation

### Web Dashboard Not Loading
- Check Flask is running on correct port
- Verify no firewall blocking
- Check browser console for errors

## Docker Deployment

Build and run with Docker Compose:
```bash
docker-compose up --build
```

Or build individually:
```bash
docker build -t valo-system .
docker run -p 5000:5000 valo-system
```

## Production Deployment

1. Use environment variables for sensitive config
2. Enable SSL/TLS for web interface
3. Set up reverse proxy (nginx/Apache)
4. Configure log rotation
5. Set up monitoring (Prometheus/Grafana)
6. Regular database backups
7. Rate limiting for VLM API calls

## Development

Run tests:
```bash
pytest tests/
```

Code style:
```bash
black agents/ orchestrator.py
flake8 agents/ orchestrator.py
```

## License

Proprietary - VALO AI-FARM System