const puppeteer = require('puppeteer');

async function testVALODashboard() {
    let browser;
    let testResults = {
        passed: 0,
        failed: 0,
        details: []
    };

    try {
        console.log('🚀 Starting VALO Dashboard E2E Tests with Puppeteer...');
        console.log('=' * 60);
        
        browser = await puppeteer.launch({
            headless: true,
            args: ['--no-sandbox', '--disable-setuid-sandbox']
        });
        
        const page = await browser.newPage();
        await page.setViewport({ width: 1280, height: 720 });
        
        // Test 1: Main Dashboard Loading
        console.log('📊 Test 1: Main Dashboard Loading...');
        try {
            await page.goto('http://localhost:5003', { waitUntil: 'networkidle0', timeout: 10000 });
            
            // Check if main elements are present
            await page.waitForSelector('h1', { timeout: 5000 });
            const title = await page.$eval('h1', el => el.textContent);
            
            if (title.includes('VALO AI-FARM Dashboard')) {
                console.log('✅ Main dashboard loaded successfully');
                testResults.passed++;
                testResults.details.push('Main dashboard: PASSED');
            } else {
                throw new Error(`Unexpected title: ${title}`);
            }
            
            // Check for key components
            const components = [
                '.card',
                '#total-cases',
                '#rounds-table',
                '#refresh-btn'
            ];
            
            for (const selector of components) {
                const element = await page.$(selector);
                if (element) {
                    console.log(`✅ Component found: ${selector}`);
                } else {
                    console.log(`⚠️  Component missing: ${selector}`);
                }
            }
            
        } catch (error) {
            console.log(`❌ Main dashboard test failed: ${error.message}`);
            testResults.failed++;
            testResults.details.push(`Main dashboard: FAILED - ${error.message}`);
        }
        
        // Test 2: Navigation Menu
        console.log('\n🧭 Test 2: Navigation Menu...');
        try {
            const navLinks = await page.$$eval('.navbar-nav .nav-link', links => 
                links.map(link => ({
                    text: link.textContent.trim(),
                    href: link.getAttribute('href')
                }))
            );
            
            const expectedLinks = ['Dashboard', 'Review', 'Analytics', 'Upload CSV'];
            const foundLinks = navLinks.map(link => link.text);
            
            let navTestPassed = true;
            for (const expectedLink of expectedLinks) {
                if (foundLinks.includes(expectedLink)) {
                    console.log(`✅ Navigation link found: ${expectedLink}`);
                } else {
                    console.log(`❌ Navigation link missing: ${expectedLink}`);
                    navTestPassed = false;
                }
            }
            
            if (navTestPassed) {
                testResults.passed++;
                testResults.details.push('Navigation menu: PASSED');
            } else {
                testResults.failed++;
                testResults.details.push('Navigation menu: FAILED');
            }
            
        } catch (error) {
            console.log(`❌ Navigation test failed: ${error.message}`);
            testResults.failed++;
            testResults.details.push(`Navigation menu: FAILED - ${error.message}`);
        }
        
        // Test 3: Review Page
        console.log('\n🔍 Test 3: Review Page...');
        try {
            await page.goto('http://localhost:5003/review', { waitUntil: 'networkidle0', timeout: 10000 });
            
            // Check for review page elements
            const reviewTitle = await page.$eval('h1', el => el.textContent);
            if (reviewTitle.includes('Case Review Interface')) {
                console.log('✅ Review page loaded successfully');
                
                // Check for filter components
                const filterElements = [
                    '#quadrant-filter',
                    '#round-filter',
                    '#infringement-filter',
                    '#apply-filters'
                ];
                
                let filtersPresent = true;
                for (const selector of filterElements) {
                    const element = await page.$(selector);
                    if (element) {
                        console.log(`✅ Filter element found: ${selector}`);
                    } else {
                        console.log(`❌ Filter element missing: ${selector}`);
                        filtersPresent = false;
                    }
                }
                
                if (filtersPresent) {
                    testResults.passed++;
                    testResults.details.push('Review page: PASSED');
                } else {
                    testResults.failed++;
                    testResults.details.push('Review page: FAILED - Missing filter elements');
                }
                
            } else {
                throw new Error(`Unexpected review page title: ${reviewTitle}`);
            }
            
        } catch (error) {
            console.log(`❌ Review page test failed: ${error.message}`);
            testResults.failed++;
            testResults.details.push(`Review page: FAILED - ${error.message}`);
        }
        
        // Test 4: Analytics Page
        console.log('\n📈 Test 4: Analytics Page...');
        try {
            await page.goto('http://localhost:5003/analytics', { waitUntil: 'networkidle0', timeout: 10000 });
            
            const analyticsTitle = await page.$eval('h1', el => el.textContent);
            if (analyticsTitle.includes('Analytics Dashboard')) {
                console.log('✅ Analytics page loaded successfully');
                
                // Check for analytics components
                const analyticsElements = [
                    '#analytics-round-filter',
                    '#update-analytics',
                    '#total-accuracy',
                    '#quadrant-chart-container',
                    '#confidenceChart'
                ];
                
                let analyticsPresent = true;
                for (const selector of analyticsElements) {
                    const element = await page.$(selector);
                    if (element) {
                        console.log(`✅ Analytics element found: ${selector}`);
                    } else {
                        console.log(`❌ Analytics element missing: ${selector}`);
                        analyticsPresent = false;
                    }
                }
                
                if (analyticsPresent) {
                    testResults.passed++;
                    testResults.details.push('Analytics page: PASSED');
                } else {
                    testResults.failed++;
                    testResults.details.push('Analytics page: FAILED - Missing analytics elements');
                }
                
            } else {
                throw new Error(`Unexpected analytics page title: ${analyticsTitle}`);
            }
            
        } catch (error) {
            console.log(`❌ Analytics page test failed: ${error.message}`);
            testResults.failed++;
            testResults.details.push(`Analytics page: FAILED - ${error.message}`);
        }
        
        // Test 5: Upload Page
        console.log('\n📤 Test 5: Upload Page...');
        try {
            await page.goto('http://localhost:5003/upload', { waitUntil: 'networkidle0', timeout: 10000 });
            
            const uploadTitle = await page.$eval('h1', el => el.textContent);
            if (uploadTitle.includes('Upload CSV Data')) {
                console.log('✅ Upload page loaded successfully');
                
                // Check for upload form elements
                const uploadElements = [
                    '#uploadForm',
                    '#csvFile',
                    '#description',
                    '#autoProcess',
                    'button[type="submit"]'
                ];
                
                let uploadPresent = true;
                for (const selector of uploadElements) {
                    const element = await page.$(selector);
                    if (element) {
                        console.log(`✅ Upload element found: ${selector}`);
                    } else {
                        console.log(`❌ Upload element missing: ${selector}`);
                        uploadPresent = false;
                    }
                }
                
                if (uploadPresent) {
                    testResults.passed++;
                    testResults.details.push('Upload page: PASSED');
                } else {
                    testResults.failed++;
                    testResults.details.push('Upload page: FAILED - Missing upload elements');
                }
                
            } else {
                throw new Error(`Unexpected upload page title: ${uploadTitle}`);
            }
            
        } catch (error) {
            console.log(`❌ Upload page test failed: ${error.message}`);
            testResults.failed++;
            testResults.details.push(`Upload page: FAILED - ${error.message}`);
        }
        
        // Test 6: API Health Check
        console.log('\n🏥 Test 6: API Health Check...');
        try {
            const response = await page.goto('http://localhost:5003/api/system/health', { waitUntil: 'networkidle0' });
            
            if (response.status() === 200) {
                const content = await page.content();
                const healthData = JSON.parse(await page.evaluate(() => document.body.textContent));
                
                if (healthData.success === true) {
                    console.log('✅ API health check passed');
                    console.log(`   Database: ${healthData.health.database}`);
                    console.log(`   VLM API: ${healthData.health.vlm_api.status}`);
                    testResults.passed++;
                    testResults.details.push('API health check: PASSED');
                } else {
                    throw new Error(`Unhealthy status: ${healthData.success}`);
                }
            } else {
                throw new Error(`HTTP ${response.status()}`);
            }
            
        } catch (error) {
            console.log(`❌ API health check failed: ${error.message}`);
            testResults.failed++;
            testResults.details.push(`API health check: FAILED - ${error.message}`);
        }
        
        // Test 7: Responsive Design Check
        console.log('\n📱 Test 7: Responsive Design Check...');
        try {
            // Test mobile viewport
            await page.setViewport({ width: 375, height: 667 });
            await page.goto('http://localhost:5003', { waitUntil: 'networkidle0' });
            
            // Check if navbar collapses
            const navbarToggler = await page.$('.navbar-toggler');
            if (navbarToggler) {
                const isVisible = await page.evaluate(el => {
                    const style = window.getComputedStyle(el);
                    return style.display !== 'none';
                }, navbarToggler);
                
                if (isVisible) {
                    console.log('✅ Mobile navigation toggle visible');
                    testResults.passed++;
                    testResults.details.push('Responsive design: PASSED');
                } else {
                    throw new Error('Mobile navigation toggle not visible');
                }
            } else {
                throw new Error('Mobile navigation toggle not found');
            }
            
        } catch (error) {
            console.log(`❌ Responsive design test failed: ${error.message}`);
            testResults.failed++;
            testResults.details.push(`Responsive design: FAILED - ${error.message}`);
        }
        
    } catch (error) {
        console.log(`❌ Puppeteer setup failed: ${error.message}`);
        testResults.failed++;
        testResults.details.push(`Setup: FAILED - ${error.message}`);
    } finally {
        if (browser) {
            await browser.close();
        }
    }
    
    // Print final results
    console.log('\n' + '=' * 60);
    console.log('🎯 VALO Dashboard Test Results Summary');
    console.log('=' * 60);
    console.log(`✅ Tests Passed: ${testResults.passed}`);
    console.log(`❌ Tests Failed: ${testResults.failed}`);
    console.log(`📊 Total Tests: ${testResults.passed + testResults.failed}`);
    console.log(`🎖️  Success Rate: ${Math.round((testResults.passed / (testResults.passed + testResults.failed)) * 100)}%`);
    
    console.log('\n📋 Detailed Results:');
    testResults.details.forEach((detail, index) => {
        console.log(`${index + 1}. ${detail}`);
    });
    
    if (testResults.failed === 0) {
        console.log('\n🎉 All tests passed! The VALO dashboard is running perfectly.');
        return true;
    } else {
        console.log('\n⚠️  Some tests failed. Please check the dashboard configuration.');
        return false;
    }
}

// Run the tests
testVALODashboard().then(success => {
    process.exit(success ? 0 : 1);
}).catch(error => {
    console.error('❌ Test execution failed:', error);
    process.exit(1);
});