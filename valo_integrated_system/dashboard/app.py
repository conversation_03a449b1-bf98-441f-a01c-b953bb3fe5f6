"""
Enhanced VALO Web Dashboard
Flask application with comprehensive analytics and review interface
"""
import os
import sys
import json
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List, Optional

from flask import Flask, render_template, request, jsonify, send_file, abort
from flask_cors import CORS
import asyncio

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

from database.connection import initialize_database, get_database
from vlm.client import check_vlm_health
from utils.csv_parser import CSVParser, validate_valo_csv
from utils.image_handler import ImageHandler
from analytics.report_generator import AnalyticsReportGenerator
from analytics.visualizations import create_quadrant_chart, create_confidence_distribution
from utils.valo_data_matcher import initialize_valo_data_matcher, get_valo_data_matcher
from services.processing_controller import initialize_processing_controller, get_processing_controller

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize Flask app
app = Flask(__name__)
CORS(app)
app.config['SECRET_KEY'] = os.urandom(24)

# Global components
db = None
csv_parser = CSVParser()
image_handler = ImageHandler()
analytics_generator = AnalyticsReportGenerator()
data_matcher = None
processing_controller = None

def initialize_components():
    """Initialize database and other components"""
    global db, data_matcher, processing_controller
    try:
        db = initialize_database()
        data_matcher = initialize_valo_data_matcher()
        processing_controller = initialize_processing_controller()
        logger.info("Dashboard components initialized")
    except Exception as e:
        logger.error(f"Failed to initialize dashboard: {e}")
        raise

# Main Dashboard Routes
@app.route('/')
def dashboard():
    """Main dashboard view"""
    return render_template('dashboard.html')

@app.route('/review')
def review_interface():
    """Case review interface"""
    return render_template('review.html')

@app.route('/analytics')
def analytics_dashboard():
    """Analytics dashboard"""
    return render_template('analytics.html')

@app.route('/upload')
def upload_interface():
    """CSV upload interface"""
    return render_template('upload.html')

# API Routes - Rounds Management
@app.route('/api/rounds', methods=['GET'])
def get_rounds():
    """Get all processing rounds"""
    try:
        limit = request.args.get('limit', 50, type=int)
        rounds = db.get_rounds(limit=limit)
        return jsonify({
            'success': True,
            'rounds': [dict(round) for round in rounds]
        })
    except Exception as e:
        logger.error(f"Failed to get rounds: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/rounds/<round_id>', methods=['GET'])
def get_round(round_id):
    """Get specific round details"""
    try:
        round_info = db.get_round(round_id)
        if not round_info:
            return jsonify({'success': False, 'error': 'Round not found'}), 404
        
        return jsonify({
            'success': True,
            'round': dict(round_info)
        })
    except Exception as e:
        logger.error(f"Failed to get round {round_id}: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/rounds/<round_id>/status', methods=['PUT'])
def update_round_status(round_id):
    """Update round status"""
    try:
        data = request.get_json()
        status = data.get('status')
        
        if not status:
            return jsonify({'success': False, 'error': 'Status required'}), 400
        
        affected = db.update_round_status(round_id, status, **data)
        
        return jsonify({
            'success': True,
            'affected_rows': affected
        })
    except Exception as e:
        logger.error(f"Failed to update round status: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

# API Routes - Cases Management
@app.route('/api/cases', methods=['GET'])
def get_cases():
    """Get cases with filtering"""
    try:
        # Parse query parameters
        round_id = request.args.get('round_id')
        quadrant = request.args.get('quadrant')
        infringement_type = request.args.get('infringement_type')
        limit = request.args.get('limit', 50, type=int)
        offset = request.args.get('offset', 0, type=int)
        
        cases = db.get_cases(
            round_id=round_id,
            quadrant=quadrant,
            infringement_type=infringement_type,
            limit=limit,
            offset=offset
        )
        
        # Convert to dict and add image info
        cases_list = []
        for case in cases:
            case_dict = dict(case)
            
            # Add image availability
            if case_dict.get('source_image_path'):
                case_dict['image_available'] = Path(case_dict['source_image_path']).exists()
            else:
                case_dict['image_available'] = False
            
            cases_list.append(case_dict)
        
        return jsonify({
            'success': True,
            'cases': cases_list,
            'total': len(cases_list),
            'filters': {
                'round_id': round_id,
                'quadrant': quadrant,
                'infringement_type': infringement_type,
                'limit': limit,
                'offset': offset
            }
        })
        
    except Exception as e:
        logger.error(f"Failed to get cases: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/cases/<case_id>', methods=['GET'])
def get_case_details(case_id):
    """Get detailed case information"""
    try:
        cases = db.get_cases()
        case = next((c for c in cases if str(c['id']) == case_id), None)
        
        if not case:
            return jsonify({'success': False, 'error': 'Case not found'}), 404
        
        case_dict = dict(case)
        
        # Add image information
        if case_dict.get('source_image_path'):
            case_dict['image_info'] = image_handler.get_image_info(case_dict['source_image_path'])
        
        if case_dict.get('cropped_image_path'):
            case_dict['cropped_image_info'] = image_handler.get_image_info(case_dict['cropped_image_path'])
        
        return jsonify({
            'success': True,
            'case': case_dict
        })
        
    except Exception as e:
        logger.error(f"Failed to get case details: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/cases/<case_id>/review', methods=['POST'])
def update_case_review(case_id):
    """Update case human review"""
    try:
        data = request.get_json()
        reviewer_name = data.get('reviewer_name', 'Anonymous')
        new_status = data.get('status')
        remarks = data.get('remarks', '')
        
        if not new_status:
            return jsonify({'success': False, 'error': 'Status required'}), 400
        
        review_id = db.update_case_review(case_id, reviewer_name, new_status, remarks)
        
        return jsonify({
            'success': True,
            'review_id': review_id
        })
        
    except Exception as e:
        logger.error(f"Failed to update case review: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

# API Routes - Analytics
@app.route('/api/analytics/quadrant', methods=['GET'])
def get_quadrant_analysis():
    """Get quadrant analysis data"""
    try:
        round_id = request.args.get('round_id')
        data = db.get_quadrant_analysis(round_id)
        
        return jsonify({
            'success': True,
            'quadrant_data': [dict(d) for d in data]
        })
        
    except Exception as e:
        logger.error(f"Failed to get quadrant analysis: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/analytics/infringement', methods=['GET'])
def get_infringement_analysis():
    """Get infringement type analysis"""
    try:
        round_id = request.args.get('round_id')
        data = db.get_infringement_analysis(round_id)
        
        return jsonify({
            'success': True,
            'infringement_data': [dict(d) for d in data]
        })
        
    except Exception as e:
        logger.error(f"Failed to get infringement analysis: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/analytics/confidence', methods=['GET'])
def get_confidence_distributions():
    """Get confidence score distributions"""
    try:
        round_id = request.args.get('round_id')
        if not round_id:
            return jsonify({'success': False, 'error': 'round_id required'}), 400
        
        data = db.get_confidence_distributions(round_id)
        
        return jsonify({
            'success': True,
            'confidence_data': [dict(d) for d in data]
        })
        
    except Exception as e:
        logger.error(f"Failed to get confidence distributions: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/analytics/tokens', methods=['GET'])
def get_token_efficiency():
    """Get token efficiency analysis"""
    try:
        round_id = request.args.get('round_id')
        if not round_id:
            return jsonify({'success': False, 'error': 'round_id required'}), 400
        
        data = db.get_token_efficiency_analysis(round_id)
        
        return jsonify({
            'success': True,
            'token_data': [dict(d) for d in data]
        })
        
    except Exception as e:
        logger.error(f"Failed to get token efficiency: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/analytics/report/<round_id>', methods=['GET'])
def generate_analytics_report(round_id):
    """Generate comprehensive analytics report"""
    try:
        report = analytics_generator.generate_comprehensive_report(round_id, db)
        
        return jsonify({
            'success': True,
            'report': report
        })
        
    except Exception as e:
        logger.error(f"Failed to generate report: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

# API Routes - CSV Management
@app.route('/api/csv/validate', methods=['POST'])
def validate_csv():
    """Validate uploaded CSV file"""
    try:
        if 'file' not in request.files:
            return jsonify({'success': False, 'error': 'No file uploaded'}), 400
        
        file = request.files['file']
        if file.filename == '':
            return jsonify({'success': False, 'error': 'No file selected'}), 400
        
        # Save temporarily
        temp_path = f"/tmp/{file.filename}"
        file.save(temp_path)
        
        try:
            # Validate CSV structure
            validation_result = validate_valo_csv(temp_path)
            
            # Get preview
            preview = csv_parser.get_csv_preview(temp_path, max_rows=5)
            
            return jsonify({
                'success': True,
                'validation': validation_result,
                'preview': preview
            })
            
        finally:
            # Cleanup temp file
            if os.path.exists(temp_path):
                os.remove(temp_path)
        
    except Exception as e:
        logger.error(f"CSV validation failed: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/csv/upload', methods=['POST'])
def upload_csv():
    """Upload and process CSV file"""
    try:
        if 'file' not in request.files:
            return jsonify({'success': False, 'error': 'No file uploaded'}), 400
        
        file = request.files['file']
        description = request.form.get('description', '')
        
        if file.filename == '':
            return jsonify({'success': False, 'error': 'No file selected'}), 400
        
        # Save to permanent location
        upload_dir = Path('uploads')
        upload_dir.mkdir(exist_ok=True)
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        safe_filename = f"{timestamp}_{file.filename}"
        file_path = upload_dir / safe_filename
        
        file.save(str(file_path))
        
        # Create round
        round_name = f"Upload_{timestamp}"
        round_id = db.create_round(
            name=round_name,
            description=description or f"Uploaded CSV: {file.filename}",
            csv_file_path=str(file_path)
        )
        
        # Validate and get case count
        validation = validate_valo_csv(str(file_path))
        if validation['valid']:
            db.update_round_status(round_id, 'pending', total_cases=validation['case_count'])
        
        return jsonify({
            'success': True,
            'round_id': round_id,
            'round_name': round_name,
            'file_path': str(file_path),
            'validation': validation
        })
        
    except Exception as e:
        logger.error(f"CSV upload failed: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

# API Routes - Images
@app.route('/api/images/<case_id>')
def serve_case_image(case_id):
    """Serve case image"""
    try:
        image_type = request.args.get('type', 'source')  # source or cropped
        
        cases = db.get_cases()
        case = next((c for c in cases if str(c['id']) == case_id), None)
        
        if not case:
            abort(404)
        
        if image_type == 'cropped' and case.get('cropped_image_path'):
            image_path = case['cropped_image_path']
        else:
            image_path = case.get('source_image_path')
        
        if not image_path or not Path(image_path).exists():
            abort(404)
        
        return send_file(image_path)
        
    except Exception as e:
        logger.error(f"Failed to serve image: {e}")
        abort(500)

@app.route('/api/images/scan', methods=['POST'])
def scan_image_directory():
    """Scan directory for available images"""
    try:
        data = request.get_json()
        directory = data.get('directory')
        
        if not directory:
            return jsonify({'success': False, 'error': 'Directory path required'}), 400
        
        scan_result = image_handler.scan_image_directory(directory)
        
        return jsonify({
            'success': True,
            'scan_result': scan_result
        })
        
    except Exception as e:
        logger.error(f"Image directory scan failed: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

# API Routes - System
@app.route('/api/system/health', methods=['GET'])
def system_health():
    """Get system health status"""
    try:
        # Check database
        try:
            rounds = db.get_rounds(limit=1)
            db_status = 'healthy'
        except Exception as e:
            db_status = f'unhealthy: {str(e)}'
        
        # Check VLM API
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            vlm_health = loop.run_until_complete(check_vlm_health())
        finally:
            loop.close()
        
        return jsonify({
            'success': True,
            'health': {
                'database': db_status,
                'vlm_api': vlm_health,
                'dashboard': 'healthy',
                'timestamp': datetime.utcnow().isoformat()
            }
        })
        
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/system/info', methods=['GET'])
def system_info():
    """Get system information"""
    return jsonify({
        'success': True,
        'system': {
            'name': 'VALO Enhanced Processing System',
            'version': '2.0.0',
            'database': 'PostgreSQL',
            'vlm_model': 'VLM-38B-AWQ',
            'features': [
                'Real VLM Integration',
                'Quadrant Filtering',
                'Advanced Analytics',
                'Human Review Interface',
                'Batch Processing',
                'SGT Round Naming'
            ],
            'endpoints': [
                'Dashboard (/)',
                'Review Interface (/review)',
                'Analytics (/analytics)',
                'CSV Upload (/upload)'
            ]
        }
    })

# API Routes - Processing Control
@app.route('/api/processing/start', methods=['POST'])
def start_processing():
    """Start a new processing round"""
    try:
        data = request.get_json() or {}
        description = data.get('description', 'Manual processing start')
        
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            result = loop.run_until_complete(processing_controller.start_processing(description))
        finally:
            loop.close()
        
        if result['success']:
            return jsonify(result)
        else:
            return jsonify(result), 400
            
    except Exception as e:
        logger.error(f"Failed to start processing: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/processing/pause', methods=['POST'])
def pause_processing():
    """Pause current processing"""
    try:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            result = loop.run_until_complete(processing_controller.pause_processing())
        finally:
            loop.close()
        
        if result['success']:
            return jsonify(result)
        else:
            return jsonify(result), 400
            
    except Exception as e:
        logger.error(f"Failed to pause processing: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/processing/resume', methods=['POST'])
def resume_processing():
    """Resume paused processing"""
    try:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            result = loop.run_until_complete(processing_controller.resume_processing())
        finally:
            loop.close()
        
        if result['success']:
            return jsonify(result)
        else:
            return jsonify(result), 400
            
    except Exception as e:
        logger.error(f"Failed to resume processing: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/processing/stop', methods=['POST'])
def stop_processing():
    """Stop current processing"""
    try:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            result = loop.run_until_complete(processing_controller.stop_processing())
        finally:
            loop.close()
        
        if result['success']:
            return jsonify(result)
        else:
            return jsonify(result), 400
            
    except Exception as e:
        logger.error(f"Failed to stop processing: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/processing/status', methods=['GET'])
def get_processing_status():
    """Get current processing status"""
    try:
        status = processing_controller.get_processing_status()
        return jsonify({'success': True, 'status': status})
        
    except Exception as e:
        logger.error(f"Failed to get processing status: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/data/summary', methods=['GET'])
def get_data_summary():
    """Get data matching summary"""
    try:
        summary = processing_controller.get_data_matching_summary()
        return jsonify({'success': True, 'summary': summary})
        
    except Exception as e:
        logger.error(f"Failed to get data summary: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

# Error Handlers
@app.errorhandler(404)
def not_found(error):
    return jsonify({'success': False, 'error': 'Resource not found'}), 404

@app.errorhandler(500)
def internal_error(error):
    return jsonify({'success': False, 'error': 'Internal server error'}), 500

if __name__ == '__main__':
    # Initialize database and components
    initialize_components()
    
    port = int(os.environ.get('PORT', 5003))
    debug = os.environ.get('DEBUG', 'false').lower() == 'true'
    
    print(f"🎯 VALO Enhanced Dashboard")
    print(f"=" * 50)
    print(f"Dashboard: http://localhost:{port}")
    print(f"Review Interface: http://localhost:{port}/review")
    print(f"Analytics: http://localhost:{port}/analytics")
    print(f"CSV Upload: http://localhost:{port}/upload")
    print(f"API Health: http://localhost:{port}/api/system/health")
    print(f"=" * 50)
    
    app.run(host='0.0.0.0', port=port, debug=debug, threaded=True)