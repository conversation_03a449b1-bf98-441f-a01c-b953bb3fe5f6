{% extends "base.html" %}

{% block title %}VALO Dashboard - Upload CSV{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">Upload CSV Data</h1>
        <p class="lead">Upload new violation data for VLM processing and analysis</p>
    </div>
</div>

<div class="row">
    <!-- Upload Form -->
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-upload"></i> CSV File Upload</h5>
            </div>
            <div class="card-body">
                <form id="uploadForm" enctype="multipart/form-data">
                    <div class="mb-3">
                        <label for="csvFile" class="form-label">Select CSV File</label>
                        <input type="file" class="form-control" id="csvFile" name="file" accept=".csv" required>
                        <div class="form-text">Expected format: case_number, url, key columns</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">Description (Optional)</label>
                        <textarea class="form-control" id="description" name="description" rows="3" 
                                  placeholder="Describe this dataset or processing round..."></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="autoProcess" name="auto_process">
                            <label class="form-check-label" for="autoProcess">
                                Start processing immediately after upload
                            </label>
                        </div>
                    </div>
                    
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-upload"></i> Upload CSV
                        </button>
                    </div>
                </form>
                
                <!-- Progress Bar -->
                <div id="uploadProgress" class="mt-3" style="display: none;">
                    <div class="progress">
                        <div class="progress-bar progress-bar-striped progress-bar-animated" 
                             role="progressbar" style="width: 0%"></div>
                    </div>
                    <p class="text-center mt-2" id="progressText">Uploading...</p>
                </div>
                
                <!-- Results -->
                <div id="uploadResults" class="mt-3" style="display: none;">
                    <!-- Results will be populated here -->
                </div>
            </div>
        </div>
    </div>
    
    <!-- Instructions -->
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6><i class="fas fa-info-circle"></i> Upload Instructions</h6>
            </div>
            <div class="card-body">
                <h6>CSV Format Requirements:</h6>
                <ul class="small">
                    <li><strong>case_number:</strong> Unique case identifier (e.g., V1250630118)</li>
                    <li><strong>url:</strong> Path to violation image file</li>
                    <li><strong>key:</strong> Human validation result ('invalid' for false positives)</li>
                </ul>
                
                <h6 class="mt-3">Processing Information:</h6>
                <ul class="small">
                    <li>Each upload creates a new processing round</li>
                    <li>Round names are auto-generated with SGT timestamps</li>
                    <li>VLM analysis processes images in batches</li>
                    <li>Results are stored in PostgreSQL database</li>
                </ul>
                
                <div class="alert alert-info mt-3">
                    <small><i class="fas fa-lightbulb"></i> <strong>Tip:</strong> Large files may take several minutes to process. You can monitor progress in the main dashboard.</small>
                </div>
            </div>
        </div>
        
        <!-- Recent Uploads -->
        <div class="card mt-3">
            <div class="card-header">
                <h6><i class="fas fa-history"></i> Recent Uploads</h6>
            </div>
            <div class="card-body">
                <div id="recentUploads">
                    <div class="text-center">
                        <div class="spinner-border spinner-border-sm" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="small mt-2">Loading recent uploads...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    loadRecentUploads();
    
    // Form submission
    document.getElementById('uploadForm').addEventListener('submit', handleUpload);
});

async function handleUpload(event) {
    event.preventDefault();
    
    const form = event.target;
    const formData = new FormData(form);
    const progressDiv = document.getElementById('uploadProgress');
    const resultsDiv = document.getElementById('uploadResults');
    const progressBar = progressDiv.querySelector('.progress-bar');
    const progressText = document.getElementById('progressText');
    
    // Show progress
    progressDiv.style.display = 'block';
    resultsDiv.style.display = 'none';
    progressBar.style.width = '0%';
    progressText.textContent = 'Uploading...';
    
    try {
        // Simulate upload progress
        let progress = 0;
        const progressInterval = setInterval(() => {
            progress += Math.random() * 15;
            if (progress > 90) progress = 90;
            progressBar.style.width = progress + '%';
        }, 200);
        
        const response = await fetch('/api/upload', {
            method: 'POST',
            body: formData
        });
        
        clearInterval(progressInterval);
        progressBar.style.width = '100%';
        
        const result = await response.json();
        
        if (response.ok) {
            progressText.textContent = 'Upload completed successfully!';
            
            setTimeout(() => {
                progressDiv.style.display = 'none';
                showUploadResults(result);
            }, 1000);
        } else {
            throw new Error(result.error || 'Upload failed');
        }
        
    } catch (error) {
        console.error('Upload error:', error);
        progressDiv.style.display = 'none';
        showError(error.message);
    }
}

function showUploadResults(result) {
    const resultsDiv = document.getElementById('uploadResults');
    
    resultsDiv.innerHTML = `
        <div class="alert alert-success">
            <h6><i class="fas fa-check-circle"></i> Upload Successful!</h6>
            <p><strong>Round Created:</strong> ${result.round_name}</p>
            <p><strong>Cases Loaded:</strong> ${result.total_cases}</p>
            <p><strong>Round ID:</strong> ${result.round_id}</p>
            
            <div class="mt-3">
                <a href="/analytics?round_id=${result.round_id}" class="btn btn-success btn-sm">
                    <i class="fas fa-chart-line"></i> View Analytics
                </a>
                <a href="/review?round_id=${result.round_id}" class="btn btn-info btn-sm">
                    <i class="fas fa-eye"></i> Review Cases
                </a>
                <a href="/" class="btn btn-primary btn-sm">
                    <i class="fas fa-tachometer-alt"></i> Dashboard
                </a>
            </div>
        </div>
    `;
    
    resultsDiv.style.display = 'block';
    
    // Reset form
    document.getElementById('uploadForm').reset();
    
    // Refresh recent uploads
    loadRecentUploads();
}

function showError(message) {
    const resultsDiv = document.getElementById('uploadResults');
    
    resultsDiv.innerHTML = `
        <div class="alert alert-danger">
            <h6><i class="fas fa-exclamation-triangle"></i> Upload Failed</h6>
            <p>${message}</p>
            <p class="small">Please check your file format and try again.</p>
        </div>
    `;
    
    resultsDiv.style.display = 'block';
}

async function loadRecentUploads() {
    try {
        const response = await fetch('/api/rounds?limit=5');
        const rounds = await response.json();
        
        const container = document.getElementById('recentUploads');
        
        if (rounds.length === 0) {
            container.innerHTML = '<p class="text-muted small">No recent uploads</p>';
            return;
        }
        
        let html = '';
        rounds.forEach(round => {
            const status = round.status;
            const statusColor = getStatusColor(status);
            
            html += `
                <div class="border-bottom pb-2 mb-2">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <small class="fw-bold">${round.name}</small><br>
                            <small class="text-muted">${new Date(round.start_time).toLocaleDateString()}</small>
                        </div>
                        <span class="badge bg-${statusColor}">${status}</span>
                    </div>
                    <div class="mt-1">
                        <small class="text-muted">${round.total_cases || 0} cases</small>
                    </div>
                </div>
            `;
        });
        
        container.innerHTML = html;
        
    } catch (error) {
        console.error('Error loading recent uploads:', error);
        document.getElementById('recentUploads').innerHTML = 
            '<p class="text-danger small">Error loading uploads</p>';
    }
}

function getStatusColor(status) {
    switch(status) {
        case 'completed': return 'success';
        case 'processing': return 'primary';
        case 'pending': return 'warning';
        case 'failed': return 'danger';
        default: return 'secondary';
    }
}
</script>
{% endblock %}