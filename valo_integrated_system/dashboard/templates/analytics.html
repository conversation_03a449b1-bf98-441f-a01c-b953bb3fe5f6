{% extends "base.html" %}

{% block title %}VALO Dashboard - Analytics{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">Analytics Dashboard</h1>
        <p class="lead">Comprehensive analysis of VLM performance and false positive reduction</p>
    </div>
</div>

<!-- Round Selector -->
<div class="row mb-4">
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6><i class="fas fa-cog"></i> Analysis Settings</h6>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label for="analytics-round-filter" class="form-label">Select Round</label>
                    <select id="analytics-round-filter" class="form-select">
                        <option value="">All Rounds</option>
                    </select>
                </div>
                <button id="update-analytics" class="btn btn-primary w-100">Update Analytics</button>
            </div>
        </div>
    </div>
    
    <!-- Key Metrics Summary -->
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h6><i class="fas fa-chart-bar"></i> Key Performance Metrics</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 text-center">
                        <h4 id="total-accuracy" class="text-success">Loading...</h4>
                        <small>Overall Accuracy</small>
                    </div>
                    <div class="col-md-3 text-center">
                        <h4 id="fp-reduction" class="text-primary">Loading...</h4>
                        <small>FP Reduction</small>
                    </div>
                    <div class="col-md-3 text-center">
                        <h4 id="avg-confidence" class="text-info">Loading...</h4>
                        <small>Avg Confidence</small>
                    </div>
                    <div class="col-md-3 text-center">
                        <h4 id="processing-efficiency" class="text-warning">Loading...</h4>
                        <small>Efficiency</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Charts Row 1 -->
<div class="row mb-4">
    <!-- Quadrant Distribution -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6><i class="fas fa-pie-chart"></i> Quadrant Distribution</h6>
            </div>
            <div class="card-body">
                <div id="quadrant-chart-container" class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">Loading chart...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Confidence Distribution -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6><i class="fas fa-chart-line"></i> Confidence Score Distribution</h6>
            </div>
            <div class="card-body">
                <canvas id="confidenceChart" width="400" height="300"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Charts Row 2 -->
<div class="row mb-4">
    <!-- Infringement Type Analysis -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6><i class="fas fa-list"></i> Performance by Infringement Type</h6>
            </div>
            <div class="card-body">
                <canvas id="infringementChart" width="400" height="300"></canvas>
            </div>
        </div>
    </div>
    
    <!-- Token Efficiency -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6><i class="fas fa-coins"></i> Token Usage vs Accuracy</h6>
            </div>
            <div class="card-body">
                <canvas id="tokenEfficiencyChart" width="400" height="300"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Detailed Analysis Table -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6><i class="fas fa-table"></i> Detailed Analysis by Infringement Type</h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover" id="infringement-analysis-table">
                        <thead>
                            <tr>
                                <th>Infringement Type</th>
                                <th>Total Cases</th>
                                <th>Correct Classifications</th>
                                <th>Accuracy %</th>
                                <th>Avg Confidence</th>
                                <th>False Positives</th>
                                <th>False Negatives</th>
                            </tr>
                        </thead>
                        <tbody id="infringement-tbody">
                            <tr>
                                <td colspan="7" class="text-center">Loading analysis...</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let confidenceChart, infringementChart, tokenChart;

document.addEventListener('DOMContentLoaded', function() {
    loadRounds();
    loadAnalytics();
    
    document.getElementById('update-analytics').addEventListener('click', loadAnalytics);
});

async function loadRounds() {
    try {
        const response = await fetch('/api/rounds');
        const data = await response.json();
        
        if (data.success && data.rounds) {
            const select = document.getElementById('analytics-round-filter');
            data.rounds.forEach(round => {
                const option = document.createElement('option');
                option.value = round.id;
                option.textContent = round.name;
                select.appendChild(option);
            });
        }
    } catch (error) {
        console.error('Error loading rounds:', error);
    }
}

async function loadAnalytics() {
    const roundId = document.getElementById('analytics-round-filter').value;
    const params = roundId ? `?round_id=${roundId}` : '';
    
    try {
        // Load quadrant analysis
        const quadrantResponse = await fetch(`/api/analytics/quadrant${params}`);
        const quadrantData = await quadrantResponse.json();
        
        // Load infringement analysis
        const infringementResponse = await fetch(`/api/analytics/infringement${params}`);
        const infringementData = await infringementResponse.json();
        
        // Load confidence distributions
        let confidenceData = [];
        if (roundId) {
            const confidenceResponse = await fetch(`/api/analytics/confidence/${roundId}`);
            confidenceData = await confidenceResponse.json();
        }
        
        // Update dashboard
        updateAnalyticsDashboard(quadrantData, infringementData, confidenceData);
        
    } catch (error) {
        console.error('Error loading analytics:', error);
    }
}

function updateAnalyticsDashboard(quadrantData, infringementData, confidenceData) {
    // Update key metrics
    updateKeyMetrics(quadrantData, infringementData);
    
    // Update quadrant chart
    updateQuadrantChart(quadrantData);
    
    // Update other charts
    updateConfidenceChart(confidenceData);
    updateInfringementChart(infringementData);
    
    // Update infringement table
    updateInfringementTable(infringementData);
}

function updateKeyMetrics(quadrantData, infringementData) {
    // Calculate overall accuracy
    const totalCases = quadrantData.reduce((sum, q) => sum + q.case_count, 0);
    const correctCases = quadrantData
        .filter(q => q.quadrant === 'valid_passed' || q.quadrant === 'invalid_failed')
        .reduce((sum, q) => sum + q.case_count, 0);
    
    const accuracy = totalCases > 0 ? Math.round((correctCases / totalCases) * 100) : 0;
    document.getElementById('total-accuracy').textContent = `${accuracy}%`;
    
    // Calculate FP reduction
    const invalidFiltered = quadrantData
        .filter(q => q.quadrant === 'invalid_failed')
        .reduce((sum, q) => sum + q.case_count, 0);
    const totalInvalid = quadrantData
        .filter(q => q.quadrant.startsWith('invalid'))
        .reduce((sum, q) => sum + q.case_count, 0);
    
    const fpReduction = totalInvalid > 0 ? Math.round((invalidFiltered / totalInvalid) * 100) : 0;
    document.getElementById('fp-reduction').textContent = `${fpReduction}%`;
    
    // Average confidence (placeholder)
    document.getElementById('avg-confidence').textContent = '75%';
    
    // Processing efficiency (placeholder)
    document.getElementById('processing-efficiency').textContent = '2.3s';
}

function updateQuadrantChart(quadrantData) {
    const container = document.getElementById('quadrant-chart-container');
    
    if (quadrantData.length === 0) {
        container.innerHTML = '<p class="text-muted">No data available</p>';
        return;
    }
    
    // For now, show a simple table representation
    // In production, you might want to use the server-generated chart
    let html = '<div class="row">';
    quadrantData.forEach(quad => {
        const percentage = Math.round((quad.case_count / quadrantData.reduce((sum, q) => sum + q.case_count, 0)) * 100);
        html += `
            <div class="col-6 mb-2">
                <div class="card quadrant-${quad.quadrant}">
                    <div class="card-body text-center">
                        <h6>${quad.quadrant.replace('_', ' ')}</h6>
                        <h4>${quad.case_count}</h4>
                        <small>${percentage}%</small>
                    </div>
                </div>
            </div>
        `;
    });
    html += '</div>';
    
    container.innerHTML = html;
}

function updateConfidenceChart(confidenceData) {
    const ctx = document.getElementById('confidenceChart').getContext('2d');
    
    if (confidenceChart) {
        confidenceChart.destroy();
    }
    
    if (confidenceData.length === 0) {
        confidenceChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: ['No Data'],
                datasets: [{
                    label: 'No data available',
                    data: [0],
                    backgroundColor: 'rgba(128, 128, 128, 0.2)'
                }]
            }
        });
        return;
    }
    
    confidenceChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: confidenceData.map(d => d.quadrant.replace('_', ' ')),
            datasets: [{
                label: 'Average Person Confidence',
                data: confidenceData.map(d => d.avg_person_conf || 0),
                backgroundColor: 'rgba(255, 99, 132, 0.2)',
                borderColor: 'rgba(255, 99, 132, 1)',
                borderWidth: 1
            }, {
                label: 'Average Violation Confidence',
                data: confidenceData.map(d => d.avg_violation_conf || 0),
                backgroundColor: 'rgba(54, 162, 235, 0.2)',
                borderColor: 'rgba(54, 162, 235, 1)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true,
                    max: 100
                }
            }
        }
    });
}

function updateInfringementChart(infringementData) {
    const ctx = document.getElementById('infringementChart').getContext('2d');
    
    if (infringementChart) {
        infringementChart.destroy();
    }
    
    infringementChart = new Chart(ctx, {
        type: 'horizontalBar',
        data: {
            labels: infringementData.slice(0, 10).map(d => d.infringement_type || 'Unknown'),
            datasets: [{
                label: 'Accuracy %',
                data: infringementData.slice(0, 10).map(d => d.accuracy_percentage || 0),
                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                borderColor: 'rgba(75, 192, 192, 1)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            scales: {
                x: {
                    beginAtZero: true,
                    max: 100
                }
            }
        }
    });
}

function updateInfringementTable(infringementData) {
    const tbody = document.getElementById('infringement-tbody');
    tbody.innerHTML = '';
    
    if (infringementData.length === 0) {
        tbody.innerHTML = '<tr><td colspan="7" class="text-center">No data available</td></tr>';
        return;
    }
    
    infringementData.forEach(item => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${item.infringement_type || 'Unknown'}</td>
            <td>${item.total_cases || 0}</td>
            <td>${item.correct_cases || 0}</td>
            <td><span class="badge bg-${getAccuracyColor(item.accuracy_percentage)}">${(item.accuracy_percentage || 0).toFixed(1)}%</span></td>
            <td>${(item.avg_confidence || 0).toFixed(1)}%</td>
            <td class="text-danger">${item.false_positives || 0}</td>
            <td class="text-warning">${item.false_negatives || 0}</td>
        `;
        tbody.appendChild(row);
    });
}

function getAccuracyColor(accuracy) {
    if (accuracy >= 90) return 'success';
    if (accuracy >= 70) return 'warning';
    return 'danger';
}
</script>
{% endblock %}