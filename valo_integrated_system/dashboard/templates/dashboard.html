{% extends "base.html" %}

{% block title %}VALO Dashboard - Home{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">VALO AI-FARM Dashboard</h1>
        <p class="lead">Enhanced false positive reduction system with real VLM integration</p>
    </div>
</div>

<div class="row">
    <!-- System Status Card -->
    <div class="col-md-6 col-lg-3 mb-4">
        <div class="card border-success">
            <div class="card-header bg-success text-white">
                <i class="fas fa-server"></i> System Status
            </div>
            <div class="card-body">
                <h5 class="card-title text-success">OPERATIONAL</h5>
                <p class="card-text">
                    <small>VLM API: <span class="badge bg-success">Connected</span></small><br>
                    <small>Database: <span class="badge bg-success">Online</span></small>
                </p>
            </div>
        </div>
    </div>

    <!-- Data Summary -->
    <div class="col-md-6 col-lg-3 mb-4">
        <div class="card border-info">
            <div class="card-header bg-info text-white">
                <i class="fas fa-database"></i> Data Summary
            </div>
            <div class="card-body">
                <h5 class="card-title" id="matched-cases">Loading...</h5>
                <p class="card-text">
                    <small>Processable Cases (CSV + Images)</small><br>
                    <small id="match-rate">Match Rate: Loading...</small>
                </p>
            </div>
        </div>
    </div>

    <!-- Processing Status -->
    <div class="col-md-6 col-lg-3 mb-4">
        <div class="card border-warning" id="processing-status-card">
            <div class="card-header bg-warning text-dark">
                <i class="fas fa-cogs"></i> Processing Status
            </div>
            <div class="card-body">
                <h5 class="card-title" id="processing-state">IDLE</h5>
                <p class="card-text">
                    <small id="processing-progress">No active processing</small><br>
                    <small id="processing-speed">Speed: N/A</small>
                </p>
            </div>
        </div>
    </div>

    <!-- Current Round -->
    <div class="col-md-6 col-lg-3 mb-4">
        <div class="card border-primary">
            <div class="card-header bg-primary text-white">
                <i class="fas fa-play-circle"></i> Current Round
            </div>
            <div class="card-body">
                <h5 class="card-title" id="current-round-name">No Active Round</h5>
                <p class="card-text">
                    <small id="round-progress">0/0 cases</small><br>
                    <small id="round-eta">ETA: N/A</small>
                </p>
            </div>
        </div>
    </div>
</div>

<!-- Recent Rounds Table -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-history"></i> Recent Processing Rounds</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover" id="rounds-table">
                        <thead>
                            <tr>
                                <th>Round Name</th>
                                <th>Started</th>
                                <th>Status</th>
                                <th>Total Cases</th>
                                <th>Processed</th>
                                <th>Progress</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="rounds-tbody">
                            <tr>
                                <td colspan="7" class="text-center">Loading rounds...</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Processing Controls -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-play"></i> Processing Controls</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-2">
                        <button id="start-btn" class="btn btn-success btn-lg w-100" disabled>
                            <i class="fas fa-play"></i> START
                        </button>
                    </div>
                    <div class="col-md-2">
                        <button id="pause-btn" class="btn btn-warning btn-lg w-100" disabled>
                            <i class="fas fa-pause"></i> PAUSE
                        </button>
                    </div>
                    <div class="col-md-2">
                        <button id="resume-btn" class="btn btn-info btn-lg w-100" disabled>
                            <i class="fas fa-play"></i> RESUME
                        </button>
                    </div>
                    <div class="col-md-2">
                        <button id="stop-btn" class="btn btn-danger btn-lg w-100" disabled>
                            <i class="fas fa-stop"></i> STOP
                        </button>
                    </div>
                    <div class="col-md-2">
                        <a href="/review" class="btn btn-outline-primary btn-lg w-100">
                            <i class="fas fa-eye"></i> Review
                        </a>
                    </div>
                    <div class="col-md-2">
                        <a href="/analytics" class="btn btn-outline-success btn-lg w-100">
                            <i class="fas fa-chart-line"></i> Analytics
                        </a>
                    </div>
                </div>
                
                <!-- Processing Progress Bar -->
                <div class="row mt-3" id="progress-container" style="display: none;">
                    <div class="col-12">
                        <div class="progress" style="height: 25px;">
                            <div id="progress-bar" class="progress-bar progress-bar-striped" 
                                 role="progressbar" style="width: 0%">0%</div>
                        </div>
                        <div class="text-center mt-2">
                            <small id="progress-text">Ready to start processing</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Dashboard JavaScript with Processing Controls
let processingStatusInterval;

document.addEventListener('DOMContentLoaded', function() {
    loadDashboardData();
    
    // Auto-refresh every 5 seconds for real-time updates
    setInterval(loadDashboardData, 5000);
    
    // Processing control event listeners
    document.getElementById('start-btn').addEventListener('click', startProcessing);
    document.getElementById('pause-btn').addEventListener('click', pauseProcessing);
    document.getElementById('resume-btn').addEventListener('click', resumeProcessing);
    document.getElementById('stop-btn').addEventListener('click', stopProcessing);
});

async function loadDashboardData() {
    try {
        // Load data summary
        const dataSummaryResponse = await fetch('/api/data/summary');
        const dataSummary = await dataSummaryResponse.json();
        
        // Load processing status
        const statusResponse = await fetch('/api/processing/status');
        const statusData = await statusResponse.json();
        
        // Load rounds data
        const roundsResponse = await fetch('/api/rounds');
        const rounds = await roundsResponse.json();
        
        updateDashboard(dataSummary, statusData, rounds);
    } catch (error) {
        console.error('Error loading dashboard data:', error);
    }
}

function updateDashboard(dataSummary, statusData, rounds) {
    // Update data summary
    if (dataSummary.success) {
        const summary = dataSummary.summary;
        document.getElementById('matched-cases').textContent = summary.matched_cases?.toLocaleString() || '0';
        document.getElementById('match-rate').textContent = `Match Rate: ${summary.match_rate_percent || 0}%`;
    }
    
    // Update processing status
    if (statusData.success) {
        const status = statusData.status;
        updateProcessingStatus(status);
        updateProcessingControls(status.state);
    }
    
    // Update rounds table
    if (rounds.rounds) {
        updateRoundsTable(rounds.rounds);
    }
}

function updateProcessingStatus(status) {
    // Update processing state
    const stateElement = document.getElementById('processing-state');
    const progressElement = document.getElementById('processing-progress');
    const speedElement = document.getElementById('processing-speed');
    const cardElement = document.getElementById('processing-status-card');
    
    stateElement.textContent = status.state.toUpperCase();
    
    // Update card color based on state
    cardElement.className = 'card border-warning';
    if (status.state === 'running') {
        cardElement.className = 'card border-success';
    } else if (status.state === 'error') {
        cardElement.className = 'card border-danger';
    } else if (status.state === 'completed') {
        cardElement.className = 'card border-primary';
    }
    
    // Update progress info
    if (status.state !== 'idle') {
        progressElement.textContent = `${status.processed_cases}/${status.total_cases} (${status.progress_percent}%)`;
        speedElement.textContent = `Speed: ${status.cases_per_minute} cases/min`;
        
        // Update current round info
        document.getElementById('current-round-name').textContent = status.round_name || 'Unknown Round';
        document.getElementById('round-progress').textContent = `${status.processed_cases}/${status.total_cases} cases`;
        
        if (status.estimated_completion) {
            const eta = new Date(status.estimated_completion * 1000);
            document.getElementById('round-eta').textContent = `ETA: ${eta.toLocaleTimeString()}`;
        } else {
            document.getElementById('round-eta').textContent = 'ETA: Calculating...';
        }
        
        // Update progress bar
        updateProgressBar(status.progress_percent, status.state);
    } else {
        progressElement.textContent = 'No active processing';
        speedElement.textContent = 'Speed: N/A';
        document.getElementById('current-round-name').textContent = 'No Active Round';
        document.getElementById('round-progress').textContent = '0/0 cases';
        document.getElementById('round-eta').textContent = 'ETA: N/A';
        hideProgressBar();
    }
}

function updateProcessingControls(state) {
    const startBtn = document.getElementById('start-btn');
    const pauseBtn = document.getElementById('pause-btn');
    const resumeBtn = document.getElementById('resume-btn');
    const stopBtn = document.getElementById('stop-btn');
    
    // Reset all buttons
    [startBtn, pauseBtn, resumeBtn, stopBtn].forEach(btn => {
        btn.disabled = true;
        btn.classList.remove('btn-pulse');
    });
    
    switch (state) {
        case 'idle':
        case 'completed':
        case 'stopped':
        case 'error':
            startBtn.disabled = false;
            startBtn.classList.add('btn-pulse');
            break;
        case 'running':
            pauseBtn.disabled = false;
            stopBtn.disabled = false;
            break;
        case 'paused':
            resumeBtn.disabled = false;
            stopBtn.disabled = false;
            resumeBtn.classList.add('btn-pulse');
            break;
    }
}

function updateProgressBar(percentage, state) {
    const progressContainer = document.getElementById('progress-container');
    const progressBar = document.getElementById('progress-bar');
    const progressText = document.getElementById('progress-text');
    
    progressContainer.style.display = 'block';
    progressBar.style.width = `${percentage}%`;
    progressBar.textContent = `${percentage.toFixed(1)}%`;
    
    // Update progress bar color based on state
    progressBar.className = 'progress-bar progress-bar-striped';
    if (state === 'running') {
        progressBar.classList.add('progress-bar-animated', 'bg-success');
        progressText.textContent = 'Processing cases...';
    } else if (state === 'paused') {
        progressBar.classList.add('bg-warning');
        progressText.textContent = 'Processing paused';
    } else if (state === 'error') {
        progressBar.classList.add('bg-danger');
        progressText.textContent = 'Processing error occurred';
    } else if (state === 'completed') {
        progressBar.classList.add('bg-primary');
        progressText.textContent = 'Processing completed successfully';
    }
}

function hideProgressBar() {
    document.getElementById('progress-container').style.display = 'none';
}

// Processing control functions
async function startProcessing() {
    try {
        showProcessingLoader('start-btn', 'Starting...');
        
        const response = await fetch('/api/processing/start', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ description: 'Dashboard manual start' })
        });
        
        const result = await response.json();
        
        if (result.success) {
            showNotification('success', `Started processing: ${result.round_name}`);
        } else {
            showNotification('error', result.error || 'Failed to start processing');
        }
    } catch (error) {
        showNotification('error', 'Error starting processing');
        console.error('Start processing error:', error);
    } finally {
        hideProcessingLoader('start-btn', '<i class="fas fa-play"></i> START');
    }
}

async function pauseProcessing() {
    try {
        showProcessingLoader('pause-btn', 'Pausing...');
        
        const response = await fetch('/api/processing/pause', { method: 'POST' });
        const result = await response.json();
        
        if (result.success) {
            showNotification('info', result.message);
        } else {
            showNotification('error', result.error || 'Failed to pause processing');
        }
    } catch (error) {
        showNotification('error', 'Error pausing processing');
        console.error('Pause processing error:', error);
    } finally {
        hideProcessingLoader('pause-btn', '<i class="fas fa-pause"></i> PAUSE');
    }
}

async function resumeProcessing() {
    try {
        showProcessingLoader('resume-btn', 'Resuming...');
        
        const response = await fetch('/api/processing/resume', { method: 'POST' });
        const result = await response.json();
        
        if (result.success) {
            showNotification('success', result.message);
        } else {
            showNotification('error', result.error || 'Failed to resume processing');
        }
    } catch (error) {
        showNotification('error', 'Error resuming processing');
        console.error('Resume processing error:', error);
    } finally {
        hideProcessingLoader('resume-btn', '<i class="fas fa-play"></i> RESUME');
    }
}

async function stopProcessing() {
    try {
        if (!confirm('Are you sure you want to stop processing? Processed data will be kept.')) {
            return;
        }
        
        showProcessingLoader('stop-btn', 'Stopping...');
        
        const response = await fetch('/api/processing/stop', { method: 'POST' });
        const result = await response.json();
        
        if (result.success) {
            showNotification('warning', result.message);
        } else {
            showNotification('error', result.error || 'Failed to stop processing');
        }
    } catch (error) {
        showNotification('error', 'Error stopping processing');
        console.error('Stop processing error:', error);
    } finally {
        hideProcessingLoader('stop-btn', '<i class="fas fa-stop"></i> STOP');
    }
}

function updateRoundsTable(rounds) {
    const tbody = document.getElementById('rounds-tbody');
    tbody.innerHTML = '';
    
    if (rounds.length === 0) {
        tbody.innerHTML = '<tr><td colspan="7" class="text-center text-muted">No processing rounds yet</td></tr>';
        return;
    }
    
    rounds.forEach(round => {
        const progress = round.total_cases > 0 ? 
            Math.round((round.processed_cases / round.total_cases) * 100) : 0;
        
        const row = document.createElement('tr');
        row.innerHTML = `
            <td><strong>${round.name}</strong></td>
            <td>${new Date(round.start_time).toLocaleString()}</td>
            <td><span class="badge bg-${getStatusColor(round.status)}">${round.status}</span></td>
            <td>${(round.total_cases || 0).toLocaleString()}</td>
            <td>${(round.processed_cases || 0).toLocaleString()}</td>
            <td>
                <div class="progress" style="height: 20px;">
                    <div class="progress-bar" style="width: ${progress}%">${progress}%</div>
                </div>
            </td>
            <td>
                <a href="/review?round_id=${round.id}" class="btn btn-sm btn-outline-primary">Review</a>
                <a href="/analytics?round_id=${round.id}" class="btn btn-sm btn-outline-success">Analytics</a>
            </td>
        `;
        tbody.appendChild(row);
    });
}

// Utility functions
function getStatusColor(status) {
    switch(status) {
        case 'completed': return 'success';
        case 'processing': return 'primary';
        case 'pending': return 'warning';
        case 'failed': return 'danger';
        case 'stopped': return 'secondary';
        default: return 'secondary';
    }
}

function showProcessingLoader(buttonId, text) {
    const btn = document.getElementById(buttonId);
    btn.innerHTML = `<span class="spinner-border spinner-border-sm" role="status"></span> ${text}`;
    btn.disabled = true;
}

function hideProcessingLoader(buttonId, originalText) {
    const btn = document.getElementById(buttonId);
    btn.innerHTML = originalText;
}

function showNotification(type, message) {
    // Simple notification system
    const alertClass = type === 'success' ? 'alert-success' : 
                      type === 'error' ? 'alert-danger' : 
                      type === 'info' ? 'alert-info' : 'alert-warning';
    
    const notification = document.createElement('div');
    notification.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(notification);
    
    // Auto-dismiss after 5 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}
</script>

<style>
.btn-pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { box-shadow: 0 0 0 0 rgba(0, 123, 255, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(0, 123, 255, 0); }
    100% { box-shadow: 0 0 0 0 rgba(0, 123, 255, 0); }
}
</style>
{% endblock %}