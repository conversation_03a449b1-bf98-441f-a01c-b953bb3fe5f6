{% extends "base.html" %}

{% block title %}VALO Dashboard - Review Cases{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">Case Review Interface</h1>
        <p class="lead">Review and validate VLM analysis results with human ground truth</p>
    </div>
</div>

<!-- Filters -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-filter"></i> Filters</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <label for="quadrant-filter" class="form-label">Quadrant</label>
                        <select id="quadrant-filter" class="form-select">
                            <option value="">All Quadrants</option>
                            <option value="valid_passed">Valid Passed</option>
                            <option value="valid_failed">Valid Failed</option>
                            <option value="invalid_passed">Invalid Passed</option>
                            <option value="invalid_failed">Invalid Failed</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="round-filter" class="form-label">Round</label>
                        <select id="round-filter" class="form-select">
                            <option value="">All Rounds</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="infringement-filter" class="form-label">Infringement Type</label>
                        <input type="text" id="infringement-filter" class="form-control" placeholder="Filter by type...">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">&nbsp;</label>
                        <button id="apply-filters" class="btn btn-primary d-block">Apply Filters</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Cases Grid -->
<div class="row" id="cases-container">
    <div class="col-12 text-center">
        <div class="spinner-border" role="status">
            <span class="visually-hidden">Loading cases...</span>
        </div>
        <p>Loading cases...</p>
    </div>
</div>

<!-- Pagination -->
<div class="row mt-4">
    <div class="col-12">
        <nav aria-label="Cases pagination">
            <ul class="pagination justify-content-center" id="pagination">
                <!-- Pagination will be populated by JavaScript -->
            </ul>
        </nav>
    </div>
</div>

<!-- Review Modal -->
<div class="modal fade" id="reviewModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Review Case</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Case Image</h6>
                        <div id="case-image-container" class="text-center">
                            <img id="case-image" class="img-fluid" style="max-height: 400px;" alt="Case image">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6>Case Details</h6>
                        <div id="case-details">
                            <!-- Case details will be populated here -->
                        </div>
                        
                        <hr>
                        
                        <h6>Human Review</h6>
                        <form id="reviewForm">
                            <input type="hidden" id="case-id">
                            <div class="mb-3">
                                <label for="reviewer-name" class="form-label">Reviewer Name</label>
                                <input type="text" class="form-control" id="reviewer-name" required>
                            </div>
                            <div class="mb-3">
                                <label for="review-status" class="form-label">Ground Truth Status</label>
                                <select class="form-select" id="review-status" required>
                                    <option value="">Select status...</option>
                                    <option value="valid">Valid Violation</option>
                                    <option value="invalid">Invalid/False Positive</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label for="review-remarks" class="form-label">Remarks</label>
                                <textarea class="form-control" id="review-remarks" rows="3"></textarea>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="submit-review">Submit Review</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let currentPage = 1;
let currentFilters = {};

document.addEventListener('DOMContentLoaded', function() {
    loadRounds();
    loadCases();
    
    // Event listeners
    document.getElementById('apply-filters').addEventListener('click', applyFilters);
    document.getElementById('submit-review').addEventListener('click', submitReview);
});

async function loadRounds() {
    try {
        const response = await fetch('/api/rounds');
        const data = await response.json();
        
        if (data.success && data.rounds) {
            const select = document.getElementById('round-filter');
            data.rounds.forEach(round => {
                const option = document.createElement('option');
                option.value = round.id;
                option.textContent = round.name;
                select.appendChild(option);
            });
        }
    } catch (error) {
        console.error('Error loading rounds:', error);
    }
}

async function loadCases(page = 1) {
    try {
        const params = new URLSearchParams({
            page: page,
            limit: 12,
            ...currentFilters
        });
        
        const response = await fetch(`/api/cases?${params}`);
        const data = await response.json();
        
        if (data.success) {
            displayCases(data.cases || []);
            currentPage = page;
        } else {
            throw new Error(data.error || 'Failed to load cases');
        }
    } catch (error) {
        console.error('Error loading cases:', error);
        document.getElementById('cases-container').innerHTML = 
            `<div class="col-12"><div class="alert alert-danger">Error loading cases: ${error.message}</div></div>`;
    }
}

function displayCases(cases) {
    const container = document.getElementById('cases-container');
    container.innerHTML = '';
    
    if (cases.length === 0) {
        container.innerHTML = '<div class="col-12"><div class="alert alert-info">No cases found</div></div>';
        return;
    }
    
    cases.forEach(caseData => {
        const col = document.createElement('div');
        col.className = 'col-md-4 col-lg-3 mb-4';
        
        const quadrantClass = `quadrant-${caseData.quadrant}`;
        const reviewedBadge = caseData.human_reviewed ? 
            '<span class="badge bg-success">Reviewed</span>' : 
            '<span class="badge bg-warning">Pending</span>';
        
        col.innerHTML = `
            <div class="card h-100 ${quadrantClass}">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <small><strong>${caseData.case_number}</strong></small>
                    ${reviewedBadge}
                </div>
                <div class="card-body">
                    <h6 class="card-title">${caseData.quadrant.replace('_', ' ')}</h6>
                    <p class="card-text">
                        <small>VLM: ${caseData.vlm_decision || 'N/A'}</small><br>
                        <small>Confidence: ${caseData.violation_confidence || 0}%</small><br>
                        <small>Type: ${caseData.infringement_type || 'Unknown'}</small>
                    </p>
                </div>
                <div class="card-footer">
                    <button class="btn btn-sm btn-primary w-100" onclick="openReviewModal('${caseData.id}')">
                        ${caseData.human_reviewed ? 'View Review' : 'Review Case'}
                    </button>
                </div>
            </div>
        `;
        
        container.appendChild(col);
    });
}

function applyFilters() {
    currentFilters = {
        quadrant: document.getElementById('quadrant-filter').value,
        round_id: document.getElementById('round-filter').value,
        infringement_type: document.getElementById('infringement-filter').value
    };
    
    // Remove empty filters
    Object.keys(currentFilters).forEach(key => {
        if (!currentFilters[key]) delete currentFilters[key];
    });
    
    loadCases(1);
}

async function openReviewModal(caseId) {
    try {
        const response = await fetch(`/api/cases/${caseId}`);
        const caseData = await response.json();
        
        // Populate modal
        document.getElementById('case-id').value = caseId;
        document.getElementById('case-image').src = `/api/cases/${caseId}/image`;
        
        // Case details
        const details = document.getElementById('case-details');
        details.innerHTML = `
            <p><strong>Case Number:</strong> ${caseData.case_number}</p>
            <p><strong>Quadrant:</strong> ${caseData.quadrant.replace('_', ' ')}</p>
            <p><strong>VLM Decision:</strong> ${caseData.vlm_decision || 'N/A'}</p>
            <p><strong>VLM Reasoning:</strong> ${caseData.vlm_reasoning || 'N/A'}</p>
            <p><strong>Confidence Scores:</strong></p>
            <ul>
                <li>Person: ${caseData.person_confidence || 0}%</li>
                <li>Structure: ${caseData.structure_confidence || 0}%</li>
                <li>Violation: ${caseData.violation_confidence || 0}%</li>
            </ul>
        `;
        
        // If already reviewed, populate form
        if (caseData.human_reviewed) {
            document.getElementById('reviewer-name').value = caseData.reviewed_by || '';
            document.getElementById('review-status').value = caseData.human_ground_truth || '';
            document.getElementById('review-remarks').value = caseData.human_remarks || '';
        }
        
        // Show modal
        new bootstrap.Modal(document.getElementById('reviewModal')).show();
    } catch (error) {
        console.error('Error loading case details:', error);
        alert('Error loading case details');
    }
}

async function submitReview() {
    const caseId = document.getElementById('case-id').value;
    const reviewerName = document.getElementById('reviewer-name').value;
    const reviewStatus = document.getElementById('review-status').value;
    const remarks = document.getElementById('review-remarks').value;
    
    if (!reviewerName || !reviewStatus) {
        alert('Please fill in required fields');
        return;
    }
    
    try {
        const response = await fetch(`/api/cases/${caseId}/review`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                reviewer_name: reviewerName,
                new_status: reviewStatus,
                remarks: remarks
            })
        });
        
        if (response.ok) {
            alert('Review submitted successfully');
            bootstrap.Modal.getInstance(document.getElementById('reviewModal')).hide();
            loadCases(currentPage); // Refresh current page
        } else {
            alert('Error submitting review');
        }
    } catch (error) {
        console.error('Error submitting review:', error);
        alert('Error submitting review');
    }
}
</script>
{% endblock %}