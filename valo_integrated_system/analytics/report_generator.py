"""
Analytics Report Generator for VALO System
Generates comprehensive insights and performance reports
"""
import logging
from datetime import datetime
from typing import Dict, Any, List, Optional
import numpy as np
from database.connection import VALODatabaseORM

logger = logging.getLogger(__name__)

class AnalyticsReportGenerator:
    """Generate comprehensive analytics reports for VALO processing rounds"""
    
    def __init__(self):
        self.confidence_thresholds = {
            'person': 90,
            'structure': 85,
            'violation': 70
        }
    
    def generate_comprehensive_report(self, round_id: str, db: VALODatabaseORM) -> Dict[str, Any]:
        """Generate comprehensive analytics report for a round"""
        try:
            # Get round information
            round_info = db.get_round(round_id)
            if not round_info:
                raise ValueError(f"Round not found: {round_id}")
            
            # Generate all analysis components
            report = {
                'round_info': dict(round_info),
                'summary_metrics': self._generate_summary_metrics(round_id, db),
                'quadrant_analysis': self._generate_quadrant_analysis(round_id, db),
                'infringement_analysis': self._generate_infringement_analysis(round_id, db),
                'confidence_analysis': self._generate_confidence_analysis(round_id, db),
                'performance_metrics': self._generate_performance_metrics(round_id, db),
                'insights': self._generate_insights(round_id, db),
                'recommendations': self._generate_recommendations(round_id, db),
                'generated_at': datetime.utcnow().isoformat(),
                'report_version': '2.0.0'
            }
            
            return report
            
        except Exception as e:
            logger.error(f"Failed to generate report for round {round_id}: {e}")
            raise
    
    def _generate_summary_metrics(self, round_id: str, db: VALODatabaseORM) -> Dict[str, Any]:
        """Generate high-level summary metrics"""
        try:
            # Get basic round data
            round_info = db.get_round(round_id)
            quadrant_data = db.get_quadrant_analysis(round_id)
            
            if not quadrant_data:
                return {
                    'total_cases': 0,
                    'accuracy_percentage': 0,
                    'fp_reduction_rate': 0,
                    'valid_case_protection': 0,
                    'error': 'No processed cases found'
                }
            
            # Calculate totals
            total_cases = sum(q['case_count'] for q in quadrant_data)
            
            # Calculate accuracy (correct classifications)
            correct_cases = sum(
                q['case_count'] for q in quadrant_data 
                if q['quadrant'] in ['valid_passed', 'invalid_failed']
            )
            accuracy_percentage = (correct_cases / total_cases * 100) if total_cases > 0 else 0
            
            # Calculate false positive metrics
            total_invalid = sum(
                q['case_count'] for q in quadrant_data 
                if 'invalid' in q['quadrant']
            )
            invalid_filtered = sum(
                q['case_count'] for q in quadrant_data 
                if q['quadrant'] == 'invalid_failed'
            )
            fp_reduction_rate = (invalid_filtered / total_invalid * 100) if total_invalid > 0 else 0
            
            # Calculate valid case protection
            total_valid = sum(
                q['case_count'] for q in quadrant_data 
                if 'valid' in q['quadrant']
            )
            valid_protected = sum(
                q['case_count'] for q in quadrant_data 
                if q['quadrant'] == 'valid_passed'
            )
            valid_protection_rate = (valid_protected / total_valid * 100) if total_valid > 0 else 0
            
            # Token and performance metrics
            avg_tokens = round_info.get('avg_tokens_per_case', 0) or 0
            total_tokens = round_info.get('total_tokens_used', 0) or 0
            duration_minutes = round(round_info.get('duration_seconds', 0) / 60, 2) if round_info.get('duration_seconds') else 0
            
            # Calculate processing efficiency
            cases_per_minute = (total_cases / duration_minutes) if duration_minutes > 0 else 0
            tokens_per_minute = (total_tokens / duration_minutes) if duration_minutes > 0 else 0
            
            return {
                'total_cases': total_cases,
                'accuracy_percentage': round(accuracy_percentage, 2),
                'fp_reduction_rate': round(fp_reduction_rate, 2),
                'valid_case_protection': round(valid_protection_rate, 2),
                'avg_tokens_per_case': round(avg_tokens, 1),
                'total_tokens_used': total_tokens,
                'duration_minutes': duration_minutes,
                'cases_per_minute': round(cases_per_minute, 2),
                'tokens_per_minute': round(tokens_per_minute, 0),
                'cost_estimate_usd': round(total_tokens * 0.0001, 2),  # Rough estimate
                'target_achievement': {
                    'fp_reduction_target': 70.0,
                    'fp_reduction_achieved': round(fp_reduction_rate, 2),
                    'target_met': fp_reduction_rate >= 70.0
                }
            }
            
        except Exception as e:
            logger.error(f"Failed to generate summary metrics: {e}")
            return {'error': str(e)}
    
    def _generate_quadrant_analysis(self, round_id: str, db: VALODatabaseORM) -> Dict[str, Any]:
        """Generate detailed quadrant analysis"""
        try:
            quadrant_data = db.get_quadrant_analysis(round_id)
            
            if not quadrant_data:
                return {'error': 'No quadrant data available'}
            
            # Process quadrant data
            quadrants = {}
            total_cases = sum(q['case_count'] for q in quadrant_data)
            
            for q in quadrant_data:
                quadrant = q['quadrant']
                quadrants[quadrant] = {
                    'case_count': q['case_count'],
                    'percentage': round((q['case_count'] / total_cases * 100), 2) if total_cases > 0 else 0,
                    'avg_person_confidence': round(q['avg_person_conf'] or 0, 1),
                    'avg_structure_confidence': round(q['avg_structure_conf'] or 0, 1),
                    'avg_violation_confidence': round(q['avg_violation_conf'] or 0, 1),
                    'avg_tokens': round(q['avg_tokens'] or 0, 1),
                    'avg_processing_time': round(q['avg_processing_time'] or 0, 1)
                }
            
            # Ensure all quadrants are present
            all_quadrants = ['valid_passed', 'valid_failed', 'invalid_passed', 'invalid_failed']
            for quadrant in all_quadrants:
                if quadrant not in quadrants:
                    quadrants[quadrant] = {
                        'case_count': 0,
                        'percentage': 0,
                        'avg_person_confidence': 0,
                        'avg_structure_confidence': 0,
                        'avg_violation_confidence': 0,
                        'avg_tokens': 0,
                        'avg_processing_time': 0
                    }
            
            # Calculate cross-quadrant insights
            insights = {
                'classification_accuracy': round(
                    (quadrants['valid_passed']['case_count'] + quadrants['invalid_failed']['case_count']) / total_cases * 100, 2
                ) if total_cases > 0 else 0,
                
                'false_positive_rate': round(
                    quadrants['invalid_passed']['case_count'] / total_cases * 100, 2
                ) if total_cases > 0 else 0,
                
                'false_negative_rate': round(
                    quadrants['valid_failed']['case_count'] / total_cases * 100, 2
                ) if total_cases > 0 else 0,
                
                'confidence_trends': {
                    'high_confidence_correct': len([
                        q for q in quadrant_data 
                        if q['quadrant'] in ['valid_passed', 'invalid_failed'] and 
                        (q['avg_violation_conf'] or 0) > self.confidence_thresholds['violation']
                    ]),
                    'low_confidence_errors': len([
                        q for q in quadrant_data 
                        if q['quadrant'] in ['valid_failed', 'invalid_passed'] and 
                        (q['avg_violation_conf'] or 0) < self.confidence_thresholds['violation']
                    ])
                }
            }
            
            return {
                'quadrants': quadrants,
                'total_cases': total_cases,
                'insights': insights,
                'visualization_data': {
                    'labels': list(quadrants.keys()),
                    'values': [quadrants[q]['case_count'] for q in quadrants.keys()],
                    'percentages': [quadrants[q]['percentage'] for q in quadrants.keys()]
                }
            }
            
        except Exception as e:
            logger.error(f"Failed to generate quadrant analysis: {e}")
            return {'error': str(e)}
    
    def _generate_infringement_analysis(self, round_id: str, db: VALODatabaseORM) -> Dict[str, Any]:
        """Generate infringement type analysis"""
        try:
            infringement_data = db.get_infringement_analysis(round_id)
            
            if not infringement_data:
                return {'error': 'No infringement data available'}
            
            # Process infringement data
            infringement_types = {}
            
            for inf in infringement_data:
                infringement_type = inf['infringement_type']
                total_cases = inf['total_cases']
                
                infringement_types[infringement_type] = {
                    'total_cases': total_cases,
                    'valid_passed': inf['valid_passed'],
                    'valid_failed': inf['valid_failed'],
                    'invalid_passed': inf['invalid_passed'],
                    'invalid_failed': inf['invalid_failed'],
                    'accuracy_percentage': inf['accuracy_percentage'],
                    'fp_reduction_rate': round(
                        (inf['invalid_failed'] / (inf['invalid_passed'] + inf['invalid_failed']) * 100) 
                        if (inf['invalid_passed'] + inf['invalid_failed']) > 0 else 0, 2
                    ),
                    'valid_protection_rate': round(
                        (inf['valid_passed'] / (inf['valid_passed'] + inf['valid_failed']) * 100) 
                        if (inf['valid_passed'] + inf['valid_failed']) > 0 else 0, 2
                    )
                }
            
            # Sort by accuracy
            sorted_types = sorted(
                infringement_types.items(), 
                key=lambda x: x[1]['accuracy_percentage'], 
                reverse=True
            )
            
            # Generate insights
            best_performing = sorted_types[0] if sorted_types else None
            worst_performing = sorted_types[-1] if sorted_types else None
            
            insights = {
                'total_infringement_types': len(infringement_types),
                'best_performing_type': {
                    'type': best_performing[0] if best_performing else None,
                    'accuracy': best_performing[1]['accuracy_percentage'] if best_performing else 0
                },
                'worst_performing_type': {
                    'type': worst_performing[0] if worst_performing else None,
                    'accuracy': worst_performing[1]['accuracy_percentage'] if worst_performing else 0
                },
                'avg_accuracy_across_types': round(
                    sum(inf['accuracy_percentage'] for inf in infringement_data) / len(infringement_data), 2
                ) if infringement_data else 0
            }
            
            return {
                'infringement_types': infringement_types,
                'sorted_by_accuracy': sorted_types,
                'insights': insights,
                'visualization_data': {
                    'labels': list(infringement_types.keys()),
                    'accuracy_values': [infringement_types[t]['accuracy_percentage'] for t in infringement_types.keys()],
                    'case_counts': [infringement_types[t]['total_cases'] for t in infringement_types.keys()]
                }
            }
            
        except Exception as e:
            logger.error(f"Failed to generate infringement analysis: {e}")
            return {'error': str(e)}
    
    def _generate_confidence_analysis(self, round_id: str, db: VALODatabaseORM) -> Dict[str, Any]:
        """Generate confidence score analysis"""
        try:
            confidence_data = db.get_confidence_distributions(round_id)
            
            if not confidence_data:
                return {'error': 'No confidence data available'}
            
            # Process confidence distributions
            confidence_analysis = {}
            
            for conf in confidence_data:
                quadrant = conf['quadrant']
                
                # Calculate statistics for each confidence type
                person_conf = conf['person_conf_dist'] or []
                structure_conf = conf['structure_conf_dist'] or []
                violation_conf = conf['violation_conf_dist'] or []
                
                confidence_analysis[quadrant] = {
                    'person_confidence': {
                        'mean': round(conf['avg_person_conf'] or 0, 1),
                        'std': round(conf['std_person_conf'] or 0, 1),
                        'above_threshold': len([x for x in person_conf if x > self.confidence_thresholds['person']]),
                        'distribution': person_conf[:20] if len(person_conf) > 20 else person_conf  # Sample for visualization
                    },
                    'structure_confidence': {
                        'mean': round(conf['avg_structure_conf'] or 0, 1),
                        'std': round(conf['std_structure_conf'] or 0, 1),
                        'above_threshold': len([x for x in structure_conf if x > self.confidence_thresholds['structure']]),
                        'distribution': structure_conf[:20] if len(structure_conf) > 20 else structure_conf
                    },
                    'violation_confidence': {
                        'mean': round(conf['avg_violation_conf'] or 0, 1),
                        'std': round(conf['std_violation_conf'] or 0, 1),
                        'above_threshold': len([x for x in violation_conf if x > self.confidence_thresholds['violation']]),
                        'distribution': violation_conf[:20] if len(violation_conf) > 20 else violation_conf
                    }
                }
            
            # Generate insights
            insights = {
                'thresholds_used': self.confidence_thresholds,
                'confidence_patterns': {
                    'high_confidence_correct': 'valid_passed' in confidence_analysis and 
                                            confidence_analysis['valid_passed']['violation_confidence']['mean'] > self.confidence_thresholds['violation'],
                    'low_confidence_errors': any(
                        confidence_analysis.get(q, {}).get('violation_confidence', {}).get('mean', 100) < self.confidence_thresholds['violation']
                        for q in ['valid_failed', 'invalid_passed']
                    )
                }
            }
            
            return {
                'confidence_by_quadrant': confidence_analysis,
                'insights': insights,
                'thresholds': self.confidence_thresholds
            }
            
        except Exception as e:
            logger.error(f"Failed to generate confidence analysis: {e}")
            return {'error': str(e)}
    
    def _generate_performance_metrics(self, round_id: str, db: VALODatabaseORM) -> Dict[str, Any]:
        """Generate performance and efficiency metrics"""
        try:
            token_data = db.get_token_efficiency_analysis(round_id)
            round_info = db.get_round(round_id)
            
            if not token_data:
                return {'error': 'No performance data available'}
            
            # Calculate token efficiency metrics
            tokens_list = [t['tokens_used'] for t in token_data if t['tokens_used'] > 0]
            processing_times = [t['processing_time_ms'] for t in token_data if t['processing_time_ms'] > 0]
            accuracy_list = [t['correct_classification'] for t in token_data]
            
            token_stats = {
                'mean_tokens': round(np.mean(tokens_list), 1) if tokens_list else 0,
                'median_tokens': round(np.median(tokens_list), 1) if tokens_list else 0,
                'std_tokens': round(np.std(tokens_list), 1) if tokens_list else 0,
                'min_tokens': min(tokens_list) if tokens_list else 0,
                'max_tokens': max(tokens_list) if tokens_list else 0
            }
            
            processing_stats = {
                'mean_processing_time_ms': round(np.mean(processing_times), 1) if processing_times else 0,
                'median_processing_time_ms': round(np.median(processing_times), 1) if processing_times else 0,
                'std_processing_time_ms': round(np.std(processing_times), 1) if processing_times else 0
            }
            
            # Token vs accuracy correlation
            if len(tokens_list) > 1 and len(accuracy_list) > 1:
                correlation = round(np.corrcoef(tokens_list[:len(accuracy_list)], accuracy_list)[0,1], 3)
            else:
                correlation = 0
            
            # Efficiency metrics
            total_duration = round_info.get('duration_seconds', 0) or 1
            total_cases = len(token_data)
            total_tokens = sum(tokens_list)
            
            efficiency = {
                'cases_per_second': round(total_cases / total_duration, 3) if total_duration > 0 else 0,
                'tokens_per_second': round(total_tokens / total_duration, 1) if total_duration > 0 else 0,
                'tokens_per_case': round(total_tokens / total_cases, 1) if total_cases > 0 else 0,
                'accuracy_rate': round(sum(accuracy_list) / len(accuracy_list), 3) if accuracy_list else 0
            }
            
            return {
                'token_statistics': token_stats,
                'processing_statistics': processing_stats,
                'efficiency_metrics': efficiency,
                'token_accuracy_correlation': correlation,
                'cost_analysis': {
                    'total_tokens': total_tokens,
                    'estimated_cost_usd': round(total_tokens * 0.0001, 2),  # Rough estimate
                    'cost_per_case': round((total_tokens * 0.0001) / total_cases, 4) if total_cases > 0 else 0
                }
            }
            
        except Exception as e:
            logger.error(f"Failed to generate performance metrics: {e}")
            return {'error': str(e)}
    
    def _generate_insights(self, round_id: str, db: VALODatabaseORM) -> List[str]:
        """Generate actionable insights based on analysis"""
        insights = []
        
        try:
            # Get analysis data
            quadrant_data = db.get_quadrant_analysis(round_id)
            infringement_data = db.get_infringement_analysis(round_id)
            
            if not quadrant_data:
                return ["📊 No processed cases found for analysis"]
            
            # Calculate key metrics
            total_cases = sum(q['case_count'] for q in quadrant_data)
            correct_cases = sum(
                q['case_count'] for q in quadrant_data 
                if q['quadrant'] in ['valid_passed', 'invalid_failed']
            )
            accuracy = (correct_cases / total_cases * 100) if total_cases > 0 else 0
            
            # Generate insights based on performance
            if accuracy >= 90:
                insights.append("🎯 Excellent overall accuracy achieved (≥90%)")
            elif accuracy >= 80:
                insights.append("✅ Good overall accuracy achieved (80-90%)")
            elif accuracy >= 70:
                insights.append("⚠️ Moderate accuracy achieved (70-80%) - room for improvement")
            else:
                insights.append("❌ Low accuracy (<70%) - significant optimization needed")
            
            # False positive insights
            fp_cases = sum(q['case_count'] for q in quadrant_data if q['quadrant'] == 'invalid_passed')
            fp_rate = (fp_cases / total_cases * 100) if total_cases > 0 else 0
            
            if fp_rate <= 10:
                insights.append("🔥 Excellent false positive control (≤10%)")
            elif fp_rate <= 20:
                insights.append("✅ Good false positive control (10-20%)")
            else:
                insights.append(f"⚠️ High false positive rate ({fp_rate:.1f}%) - investigate threshold tuning")
            
            # Valid case protection
            valid_lost = sum(q['case_count'] for q in quadrant_data if q['quadrant'] == 'valid_failed')
            if valid_lost > 0:
                insights.append(f"🚨 {valid_lost} valid cases incorrectly filtered - review confidence thresholds")
            else:
                insights.append("✅ No valid cases lost to filtering")
            
            # Confidence analysis
            high_conf_quadrants = [q for q in quadrant_data if (q['avg_violation_conf'] or 0) > 80]
            if len(high_conf_quadrants) >= 2:
                insights.append("💪 VLM shows high confidence across multiple quadrants")
            
            # Infringement type insights
            if infringement_data:
                best_type = max(infringement_data, key=lambda x: x['accuracy_percentage'])
                worst_type = min(infringement_data, key=lambda x: x['accuracy_percentage'])
                
                if best_type['accuracy_percentage'] - worst_type['accuracy_percentage'] > 30:
                    insights.append(f"📈 Large accuracy variation: {best_type['infringement_type']} ({best_type['accuracy_percentage']:.1f}%) vs {worst_type['infringement_type']} ({worst_type['accuracy_percentage']:.1f}%)")
                
                # Identify problematic types
                poor_types = [inf for inf in infringement_data if inf['accuracy_percentage'] < 60]
                if poor_types:
                    insights.append(f"⚠️ {len(poor_types)} infringement types need attention: {', '.join([p['infringement_type'] for p in poor_types[:3]])}")
            
            # Token efficiency
            avg_tokens = sum(q.get('avg_tokens', 0) for q in quadrant_data) / len(quadrant_data) if quadrant_data else 0
            if avg_tokens > 800:
                insights.append("💸 High token usage detected - consider prompt optimization")
            elif avg_tokens < 200:
                insights.append("⚡ Efficient token usage - good prompt design")
            
            return insights
            
        except Exception as e:
            logger.error(f"Failed to generate insights: {e}")
            return [f"❌ Error generating insights: {str(e)}"]
    
    def _generate_recommendations(self, round_id: str, db: VALODatabaseORM) -> List[str]:
        """Generate actionable recommendations"""
        recommendations = []
        
        try:
            quadrant_data = db.get_quadrant_analysis(round_id)
            infringement_data = db.get_infringement_analysis(round_id)
            
            if not quadrant_data:
                return ["Continue processing more cases for comprehensive analysis"]
            
            # Calculate key metrics for recommendations
            total_cases = sum(q['case_count'] for q in quadrant_data)
            accuracy = sum(
                q['case_count'] for q in quadrant_data 
                if q['quadrant'] in ['valid_passed', 'invalid_failed']
            ) / total_cases * 100 if total_cases > 0 else 0
            
            # Accuracy-based recommendations
            if accuracy < 80:
                recommendations.extend([
                    "🔧 Review and optimize VLM prompts for better classification",
                    "📊 Analyze misclassified cases to identify patterns",
                    "⚙️ Consider adjusting confidence thresholds"
                ])
            
            # False positive recommendations
            fp_cases = sum(q['case_count'] for q in quadrant_data if q['quadrant'] == 'invalid_passed')
            if fp_cases > total_cases * 0.15:  # >15% FP rate
                recommendations.extend([
                    "🎯 Implement stricter filtering criteria for false positives",
                    "🔍 Review 'invalid_passed' cases for common patterns",
                    "⚖️ Lower confidence thresholds for violation detection"
                ])
            
            # Valid case protection
            valid_lost = sum(q['case_count'] for q in quadrant_data if q['quadrant'] == 'valid_failed')
            if valid_lost > 0:
                recommendations.extend([
                    f"🛡️ Urgent: Review {valid_lost} incorrectly filtered valid cases",
                    "📈 Increase sensitivity to avoid missing real violations",
                    "🔄 Implement human review for edge cases"
                ])
            
            # Infringement-specific recommendations
            if infringement_data:
                poor_performers = [inf for inf in infringement_data if inf['accuracy_percentage'] < 70]
                if poor_performers:
                    recommendations.append(
                        f"🎯 Focus optimization on: {', '.join([p['infringement_type'] for p in poor_performers[:3]])}"
                    )
                
                # Best performers as examples
                good_performers = [inf for inf in infringement_data if inf['accuracy_percentage'] >= 85]
                if good_performers and poor_performers:
                    recommendations.append(
                        f"📚 Study successful patterns from: {good_performers[0]['infringement_type']} ({good_performers[0]['accuracy_percentage']:.1f}% accuracy)"
                    )
            
            # Operational recommendations
            recommendations.extend([
                "📋 Implement regular human review sampling (5-10% of cases)",
                "📈 Set up automated alerts for accuracy drops below 80%",
                "💾 Archive successful prompt configurations for future use",
                "🔄 Schedule monthly system performance reviews"
            ])
            
            # Token optimization
            avg_tokens = sum(q.get('avg_tokens', 0) for q in quadrant_data) / len(quadrant_data) if quadrant_data else 0
            if avg_tokens > 600:
                recommendations.extend([
                    "✂️ Optimize prompts to reduce token usage",
                    "💰 Monitor costs and set budget alerts"
                ])
            
            return recommendations[:10]  # Limit to top 10 recommendations
            
        except Exception as e:
            logger.error(f"Failed to generate recommendations: {e}")
            return [f"Error generating recommendations: {str(e)}"]