"""
Visualization functions for VALO Analytics
Creates charts and graphs for the dashboard
"""
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
from typing import Dict, Any, List
import io
import base64

def create_quadrant_chart(quadrant_data: List[Dict[str, Any]]) -> str:
    """Create quadrant distribution pie chart"""
    if not quadrant_data:
        return ""
    
    labels = [q['quadrant'].replace('_', ' ').title() for q in quadrant_data]
    values = [q['case_count'] for q in quadrant_data]
    colors = ['#00ff88', '#ff8800', '#ff4444', '#4488ff']
    
    plt.figure(figsize=(8, 6))
    plt.pie(values, labels=labels, colors=colors, autopct='%1.1f%%', startangle=90)
    plt.title('Case Distribution by Quadrant')
    
    # Convert to base64 string
    buffer = io.BytesIO()
    plt.savefig(buffer, format='png', bbox_inches='tight')
    buffer.seek(0)
    chart_data = base64.b64encode(buffer.getvalue()).decode()
    plt.close()
    
    return f"data:image/png;base64,{chart_data}"

def create_confidence_distribution(confidence_data: List[Dict[str, Any]]) -> str:
    """Create confidence score distribution histogram"""
    if not confidence_data:
        return ""
    
    plt.figure(figsize=(10, 6))
    
    for i, data in enumerate(confidence_data):
        quadrant = data['quadrant']
        person_conf = data.get('person_conf_dist', [])
        if person_conf:
            plt.hist(person_conf, bins=20, alpha=0.7, label=f'{quadrant} (Person)', density=True)
    
    plt.xlabel('Confidence Score')
    plt.ylabel('Density')
    plt.title('Person Confidence Distribution by Quadrant')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # Convert to base64 string
    buffer = io.BytesIO()
    plt.savefig(buffer, format='png', bbox_inches='tight')
    buffer.seek(0)
    chart_data = base64.b64encode(buffer.getvalue()).decode()
    plt.close()
    
    return f"data:image/png;base64,{chart_data}"