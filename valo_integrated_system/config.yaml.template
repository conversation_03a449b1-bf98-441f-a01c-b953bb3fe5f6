# VALO Integrated System Configuration Template
# Copy this file to config.yaml and update with your values

# PostgreSQL Database Configuration
database:
  host: localhost              # Use 'postgres' if running in Docker
  port: 5432
  database: valo_system
  user: valo_user
  password: your-secure-password-here

# VLM API Configuration
vlm:
  url: http://**************:9500/v1/chat/completions
  model: VLM-38B-AWQ          # Vision Language Model identifier
  temperature: 0.1            # Lower = more deterministic (0.0-1.0)
  max_tokens: 600            # Max response tokens (affects cost)
  timeout: 90                # API timeout in seconds
  retry_attempts: 3          # Number of retry attempts
  retry_delay: 2             # Initial retry delay (exponential backoff)

# Web Dashboard Configuration
web:
  host: 0.0.0.0              # Bind to all interfaces
  port: 5000                 # Web server port
  debug: false               # Flask debug mode
  secret_key: generate-a-secure-secret-key-here
  
# Processing Configuration
processing:
  workers: 3                 # Number of parallel processing workers
  batch_size: 10             # Cases processed per batch
  queue_size: 100            # Max queue size
  
# Analytics Configuration
analytics:
  cache_ttl: 86400          # Cache TTL in seconds (24 hours)
  visualization_dpi: 100    # DPI for generated charts
  report_format: json       # Output format (json/html/pdf)
  
# Logging Configuration
logging:
  level: INFO               # DEBUG, INFO, WARNING, ERROR
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: logs/valo_system.log
  max_size: 10485760        # 10MB
  backup_count: 5
  
# Image Storage Configuration
storage:
  base_path: ./ai_farm_images_fixed_250703
  valid_dir: ai_farm_images_fixed/valid
  invalid_dir: ai_farm_images_fixed/invalid
  supported_formats: [JPEG, JPG, PNG]
  
# Performance Tuning
performance:
  db_pool_min_size: 5
  db_pool_max_size: 20
  api_rate_limit: 100       # Requests per minute
  cache_enabled: true
  
# Security Configuration
security:
  enable_cors: true
  allowed_origins: ["*"]    # Restrict in production
  enable_auth: false        # Set to true in production
  jwt_secret: generate-another-secure-secret
  session_timeout: 3600     # 1 hour
  
# Monitoring Configuration
monitoring:
  enable_metrics: true
  metrics_port: 9090
  health_check_interval: 30
  alert_email: <EMAIL>
  
# Feature Flags
features:
  enable_auto_learning: true
  enable_human_review: true
  enable_real_time_updates: true
  enable_export: true
  
# External Services (Optional)
external:
  redis:
    enabled: false
    url: redis://localhost:6379
    ttl: 3600
  sentry:
    enabled: false
    dsn: your-sentry-dsn-here
  smtp:
    enabled: false
    host: smtp.gmail.com
    port: 587
    user: <EMAIL>
    password: your-app-password