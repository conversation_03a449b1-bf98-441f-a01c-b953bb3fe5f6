# VALO Enhanced System Configuration

# PostgreSQL Database Configuration
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=valo_system
POSTGRES_USER=valo_user
POSTGRES_PASSWORD=valo_password

# VLM API Configuration
VLM_ENDPOINT=http://**************:9500/v1
VLM_MODEL=VLM-38B-AWQ
VLM_TIMEOUT=120
VLM_MAX_RETRIES=3

# Image Storage Configuration
IMAGE_BASE_PATH=/data/valo_images
IMAGE_SEARCH_PATHS=/data/valo_images,../ai_farm_images_fixed_250703,./images,../images

# Processing Configuration
BATCH_SIZE=5
MAX_PARALLEL_REQUESTS=10
PROCESSING_TIMEOUT=300

# Dashboard Configuration
DASHBOARD_HOST=0.0.0.0
DASHBOARD_PORT=5003
DASHBOARD_DEBUG=false

# Analytics Configuration
CONFIDENCE_THRESHOLD_PERSON=90
CONFIDENCE_THRESHOLD_STRUCTURE=85
CONFIDENCE_THRESHOLD_VIOLATION=70

# Security Configuration
SECRET_KEY=your-secret-key-here
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:5003

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=valo_system.log

# Development Settings
ENVIRONMENT=production
DEBUG_MODE=false