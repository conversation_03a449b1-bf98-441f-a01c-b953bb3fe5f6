#!/usr/bin/env python3
"""
Enhanced VALO Processor - Main CLI Controller
Real VLM Integration, PostgreSQL, Quadrant Analysis
NO MOCK DATA - Production Ready System
"""
import asyncio
import argparse
import csv
import json
import logging
import os
import sys
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List, Optional
import time

# Add project root to path
sys.path.append(str(Path(__file__).parent))

from database.connection import initialize_database, get_database
from vlm.client import VLMClient
from utils.image_handler import ImageHandler
from utils.csv_parser import CSVParser

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('valo_processor.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger('valo_processor')

class VALOProcessor:
    """Enhanced VALO Violation Processing System"""
    
    def __init__(self, vlm_endpoint: str = "http://**************:9500/v1"):
        """Initialize VALO processor with real VLM integration"""
        self.vlm_endpoint = vlm_endpoint
        self.db = None
        self.image_handler = ImageHandler()
        self.csv_parser = CSVParser()
        
        # Processing state
        self.current_round_id = None
        self.processing_stats = {
            'total_cases': 0,
            'processed_cases': 0,
            'successful_cases': 0,
            'failed_cases': 0,
            'start_time': None,
            'end_time': None
        }
        
        logger.info(f"VALO Processor initialized with VLM endpoint: {vlm_endpoint}")
    
    async def initialize(self):
        """Initialize database and check VLM connectivity"""
        try:
            # Initialize database
            self.db = initialize_database()
            logger.info("Database connection established")
            
            # Check VLM health
            async with VLMClient(self.vlm_endpoint) as vlm_client:
                health = await vlm_client.health_check()
                if health['status'] != 'healthy':
                    logger.warning(f"VLM API health check failed: {health}")
                else:
                    logger.info("VLM API connectivity verified")
            
        except Exception as e:
            logger.error(f"Initialization failed: {e}")
            raise
    
    def create_round_name(self) -> str:
        """Create SGT datetime-based round name"""
        # Singapore timezone offset (+8 hours)
        sgt_time = datetime.utcnow().replace(microsecond=0)
        # Format: Round_YYYYMMDD_HHMM
        return f"Round_{sgt_time.strftime('%Y%m%d_%H%M')}"
    
    async def create_processing_round(self, csv_file_path: str, description: str = None) -> str:
        """Create new processing round"""
        round_name = self.create_round_name()
        
        # Validate CSV file
        if not Path(csv_file_path).exists():
            raise FileNotFoundError(f"CSV file not found: {csv_file_path}")
        
        # Parse CSV to get case count
        cases_data = self.csv_parser.parse_csv(csv_file_path)
        total_cases = len(cases_data)
        
        # Create round in database
        round_id = self.db.create_round(
            name=round_name,
            description=description or f"Processing {total_cases} cases from {Path(csv_file_path).name}",
            csv_file_path=csv_file_path
        )
        
        # Update round with total cases
        self.db.update_round_status(round_id, 'pending', total_cases=total_cases)
        
        self.current_round_id = round_id
        self.processing_stats['total_cases'] = total_cases
        
        logger.info(f"Created processing round: {round_name} ({total_cases} cases)")
        return round_id
    
    async def process_single_case(self, case_data: Dict[str, Any], vlm_client: VLMClient) -> Dict[str, Any]:
        """Process a single case through VLM analysis"""
        case_number = case_data.get('case_number', 'Unknown')
        
        try:
            # Prepare image path
            image_path = self.image_handler.get_image_path(case_data)
            if not image_path or not Path(image_path).exists():
                logger.warning(f"Image not found for case {case_number}: {image_path}")
                # Create error result
                return {
                    **case_data,
                    'vlm_decision': 'IMAGE_NOT_FOUND',
                    'vlm_reasoning': f'Image file not accessible: {image_path}',
                    'person_confidence': 0,
                    'structure_confidence': 0,
                    'violation_confidence': 0,
                    'tokens_used': 0,
                    'processing_time_ms': 0,
                    'vlm_filtered': False,
                    'error': 'Image not found'
                }
            
            # Process through VLM
            vlm_result = await vlm_client.analyze_case(case_data, image_path)
            
            # Merge case data with VLM results
            processed_case = {**case_data, **vlm_result}
            
            # Add image paths
            processed_case['source_image_path'] = image_path
            processed_case['cropped_image_path'] = self.image_handler.get_cropped_image_path(case_data)
            
            logger.info(f"Successfully processed case {case_number}")
            return processed_case
            
        except Exception as e:
            logger.error(f"Failed to process case {case_number}: {e}")
            return {
                **case_data,
                'vlm_decision': 'PROCESSING_ERROR',
                'vlm_reasoning': f'Processing failed: {str(e)}',
                'person_confidence': 0,
                'structure_confidence': 0,
                'violation_confidence': 0,
                'tokens_used': 0,
                'processing_time_ms': 0,
                'vlm_filtered': False,
                'error': str(e)
            }
    
    async def process_all_cases(self, csv_file_path: str, batch_size: int = 5, 
                              max_cases: int = None) -> str:
        """Process all cases from CSV file"""
        try:
            # Create processing round
            round_id = await self.create_processing_round(csv_file_path)
            
            # Parse CSV data
            cases_data = self.csv_parser.parse_csv(csv_file_path)
            
            # Limit cases if specified
            if max_cases:
                cases_data = cases_data[:max_cases]
                logger.info(f"Limited processing to {max_cases} cases")
            
            # Update processing stats
            self.processing_stats['start_time'] = time.time()
            self.db.update_round_status(round_id, 'running')
            
            logger.info(f"Starting processing of {len(cases_data)} cases in batches of {batch_size}")
            
            # Process cases in batches
            async with VLMClient(self.vlm_endpoint) as vlm_client:
                for i in range(0, len(cases_data), batch_size):
                    batch = cases_data[i:i+batch_size]
                    batch_start = time.time()
                    
                    # Process batch
                    batch_tasks = []
                    for case_data in batch:
                        task = self.process_single_case(case_data, vlm_client)
                        batch_tasks.append(task)
                    
                    batch_results = await asyncio.gather(*batch_tasks, return_exceptions=True)
                    
                    # Save batch results to database
                    for j, result in enumerate(batch_results):
                        if isinstance(result, Exception):
                            logger.error(f"Batch processing exception: {result}")
                            self.processing_stats['failed_cases'] += 1
                        else:
                            try:
                                # Insert case into database
                                case_id = self.db.insert_case(round_id, result)
                                self.processing_stats['successful_cases'] += 1
                                logger.debug(f"Saved case {result.get('case_number')} to database: {case_id}")
                            except Exception as e:
                                logger.error(f"Failed to save case to database: {e}")
                                self.processing_stats['failed_cases'] += 1
                    
                    self.processing_stats['processed_cases'] += len(batch)
                    
                    # Update round progress
                    self.db.update_round_status(
                        round_id, 
                        'running', 
                        processed_cases=self.processing_stats['processed_cases']
                    )
                    
                    batch_time = time.time() - batch_start
                    progress = (self.processing_stats['processed_cases'] / len(cases_data)) * 100
                    
                    logger.info(f"Batch {i//batch_size + 1} completed in {batch_time:.2f}s "
                              f"({progress:.1f}% total progress)")
                    
                    # Rate limiting between batches
                    await asyncio.sleep(0.5)
            
            # Complete processing
            self.processing_stats['end_time'] = time.time()
            total_time = self.processing_stats['end_time'] - self.processing_stats['start_time']
            
            # Update round status
            self.db.update_round_status(
                round_id, 
                'completed', 
                processed_cases=self.processing_stats['processed_cases']
            )
            
            # Log completion stats
            logger.info(f"Processing completed for round {round_id}")
            logger.info(f"Total time: {total_time:.2f}s")
            logger.info(f"Successful cases: {self.processing_stats['successful_cases']}")
            logger.info(f"Failed cases: {self.processing_stats['failed_cases']}")
            logger.info(f"Average time per case: {total_time/len(cases_data):.2f}s")
            
            return round_id
            
        except Exception as e:
            logger.error(f"Processing failed: {e}")
            if self.current_round_id:
                self.db.update_round_status(self.current_round_id, 'failed')
            raise
    
    async def resume_processing(self, round_id: str) -> str:
        """Resume processing from a paused or failed round"""
        try:
            # Get round information
            round_info = self.db.get_round(round_id)
            if not round_info:
                raise ValueError(f"Round not found: {round_id}")
            
            csv_file_path = round_info['csv_file_path']
            if not csv_file_path or not Path(csv_file_path).exists():
                raise FileNotFoundError(f"CSV file not found: {csv_file_path}")
            
            # Get already processed cases
            processed_cases = self.db.get_cases(round_id=round_id)
            processed_case_numbers = {case['case_number'] for case in processed_cases}
            
            # Parse CSV and filter unprocessed cases
            all_cases = self.csv_parser.parse_csv(csv_file_path)
            remaining_cases = [
                case for case in all_cases 
                if case.get('case_number') not in processed_case_numbers
            ]
            
            if not remaining_cases:
                logger.info(f"No remaining cases to process for round {round_id}")
                self.db.update_round_status(round_id, 'completed')
                return round_id
            
            logger.info(f"Resuming processing: {len(remaining_cases)} cases remaining")
            
            # Update current round and continue processing
            self.current_round_id = round_id
            self.processing_stats['total_cases'] = len(all_cases)
            self.processing_stats['processed_cases'] = len(processed_cases)
            
            # Process remaining cases
            self.db.update_round_status(round_id, 'running')
            
            async with VLMClient(self.vlm_endpoint) as vlm_client:
                for case_data in remaining_cases:
                    processed_case = await self.process_single_case(case_data, vlm_client)
                    
                    # Save to database
                    try:
                        self.db.insert_case(round_id, processed_case)
                        self.processing_stats['successful_cases'] += 1
                        self.processing_stats['processed_cases'] += 1
                        
                        # Update progress
                        self.db.update_round_status(
                            round_id, 
                            'running', 
                            processed_cases=self.processing_stats['processed_cases']
                        )
                        
                        logger.info(f"Resumed case {processed_case.get('case_number')} "
                                  f"({self.processing_stats['processed_cases']}/{self.processing_stats['total_cases']})")
                        
                    except Exception as e:
                        logger.error(f"Failed to save resumed case: {e}")
                        self.processing_stats['failed_cases'] += 1
            
            # Complete processing
            self.db.update_round_status(round_id, 'completed')
            logger.info(f"Resume processing completed for round {round_id}")
            
            return round_id
            
        except Exception as e:
            logger.error(f"Resume processing failed: {e}")
            if round_id:
                self.db.update_round_status(round_id, 'failed')
            raise
    
    def get_processing_status(self) -> Dict[str, Any]:
        """Get current processing status"""
        if self.current_round_id:
            round_info = self.db.get_round(self.current_round_id)
            return {
                'round_id': self.current_round_id,
                'round_name': round_info['name'] if round_info else 'Unknown',
                'status': round_info['status'] if round_info else 'Unknown',
                **self.processing_stats
            }
        else:
            return {
                'round_id': None,
                'status': 'idle',
                **self.processing_stats
            }
    
    async def generate_round_report(self, round_id: str) -> Dict[str, Any]:
        """Generate comprehensive report for a round"""
        try:
            # Get round info
            round_info = self.db.get_round(round_id)
            if not round_info:
                raise ValueError(f"Round not found: {round_id}")
            
            # Get quadrant analysis
            quadrant_data = self.db.get_quadrant_analysis(round_id)
            
            # Get infringement analysis
            infringement_data = self.db.get_infringement_analysis(round_id)
            
            # Get confidence distributions
            confidence_data = self.db.get_confidence_distributions(round_id)
            
            # Get token efficiency
            token_data = self.db.get_token_efficiency_analysis(round_id)
            
            # Calculate summary metrics
            total_cases = round_info['actual_processed_cases']
            if total_cases > 0:
                valid_cases = sum(1 for q in quadrant_data if 'valid' in q['quadrant'])
                fp_cases = sum(1 for q in quadrant_data if 'invalid' in q['quadrant'])
                
                # Calculate accuracy metrics
                correct_classifications = sum(
                    q['case_count'] for q in quadrant_data 
                    if q['quadrant'] in ['valid_passed', 'invalid_failed']
                )
                accuracy_percentage = (correct_classifications / total_cases) * 100
                
                report = {
                    'round_info': dict(round_info),
                    'summary_metrics': {
                        'total_cases': total_cases,
                        'valid_cases': valid_cases,
                        'false_positive_cases': fp_cases,
                        'accuracy_percentage': round(accuracy_percentage, 2),
                        'avg_tokens_per_case': round(round_info.get('avg_tokens_per_case', 0), 2),
                        'total_tokens_used': round_info.get('total_tokens_used', 0),
                        'duration_minutes': round(round_info.get('duration_seconds', 0) / 60, 2)
                    },
                    'quadrant_analysis': [dict(q) for q in quadrant_data],
                    'infringement_analysis': [dict(i) for i in infringement_data],
                    'confidence_distributions': [dict(c) for c in confidence_data],
                    'token_efficiency': [dict(t) for t in token_data],
                    'generated_at': datetime.utcnow().isoformat()
                }
            else:
                report = {
                    'round_info': dict(round_info),
                    'error': 'No processed cases found',
                    'generated_at': datetime.utcnow().isoformat()
                }
            
            return report
            
        except Exception as e:
            logger.error(f"Failed to generate report for round {round_id}: {e}")
            raise

async def main():
    """Main CLI entry point"""
    parser = argparse.ArgumentParser(description='Enhanced VALO Violation Processing System')
    parser.add_argument('--csv', required=True, help='Path to CSV file with violation data')
    parser.add_argument('--vlm-endpoint', default='http://**************:9500/v1', 
                       help='VLM API endpoint')
    parser.add_argument('--batch-size', type=int, default=5, 
                       help='Batch size for parallel processing')
    parser.add_argument('--max-cases', type=int, help='Maximum number of cases to process')
    parser.add_argument('--resume', help='Resume processing for existing round ID')
    parser.add_argument('--report', help='Generate report for round ID')
    parser.add_argument('--status', action='store_true', help='Show processing status')
    
    args = parser.parse_args()
    
    # Initialize processor
    processor = VALOProcessor(args.vlm_endpoint)
    await processor.initialize()
    
    try:
        if args.status:
            # Show status
            status = processor.get_processing_status()
            print(json.dumps(status, indent=2))
            
        elif args.report:
            # Generate report
            report = await processor.generate_round_report(args.report)
            print(json.dumps(report, indent=2))
            
        elif args.resume:
            # Resume processing
            print(f"Resuming processing for round: {args.resume}")
            round_id = await processor.resume_processing(args.resume)
            print(f"Resume processing completed: {round_id}")
            
        else:
            # Start new processing
            print(f"Starting VALO processing: {args.csv}")
            print(f"VLM Endpoint: {args.vlm_endpoint}")
            print(f"Batch Size: {args.batch_size}")
            if args.max_cases:
                print(f"Max Cases: {args.max_cases}")
            
            round_id = await processor.process_all_cases(
                args.csv, 
                batch_size=args.batch_size,
                max_cases=args.max_cases
            )
            
            print(f"Processing completed successfully: {round_id}")
            
            # Generate and display summary report
            report = await processor.generate_round_report(round_id)
            print("\n" + "="*50)
            print("PROCESSING SUMMARY")
            print("="*50)
            print(f"Round: {report['round_info']['name']}")
            print(f"Total Cases: {report['summary_metrics']['total_cases']}")
            print(f"Accuracy: {report['summary_metrics']['accuracy_percentage']}%")
            print(f"Total Tokens: {report['summary_metrics']['total_tokens_used']}")
            print(f"Duration: {report['summary_metrics']['duration_minutes']:.1f} minutes")
            
    except KeyboardInterrupt:
        print("\nProcessing interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Processing failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())