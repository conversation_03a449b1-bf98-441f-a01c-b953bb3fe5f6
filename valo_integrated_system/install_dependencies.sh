#!/bin/bash

# Install all system dependencies for VALO Integrated System
# Supports Ubuntu/Debian, macOS, and RHEL/CentOS

set -e

echo "Installing VALO System Dependencies..."
echo "====================================="

# Detect OS
if [[ "$OSTYPE" == "linux-gnu"* ]]; then
    # Linux
    if [ -f /etc/debian_version ]; then
        # Debian/Ubuntu
        echo "Detected Debian/Ubuntu system"
        sudo apt-get update
        sudo apt-get install -y \
            python3-pip \
            python3-venv \
            postgresql \
            postgresql-client \
            libpq-dev \
            build-essential \
            curl \
            git \
            nginx \
            redis-server
            
    elif [ -f /etc/redhat-release ]; then
        # RHEL/CentOS
        echo "Detected RHEL/CentOS system"
        sudo yum install -y \
            python3-pip \
            python3-devel \
            postgresql \
            postgresql-devel \
            gcc \
            gcc-c++ \
            make \
            curl \
            git \
            nginx \
            redis
    fi
    
elif [[ "$OSTYPE" == "darwin"* ]]; then
    # macOS
    echo "Detected macOS system"
    
    # Check if Homebrew is installed
    if ! command -v brew &> /dev/null; then
        echo "Installing Homebrew..."
        /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
    fi
    
    # Install dependencies
    brew install \
        python@3.11 \
        postgresql@15 \
        redis \
        nginx
        
    # Start services
    brew services start postgresql@15
    brew services start redis
    
else
    echo "Unsupported operating system: $OSTYPE"
    exit 1
fi

# Install Python global packages
echo "Installing Python global packages..."
pip3 install --upgrade pip setuptools wheel

# Install Docker (optional)
read -p "Do you want to install Docker? (y/n) " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        # Install Docker on Linux
        curl -fsSL https://get.docker.com -o get-docker.sh
        sudo sh get-docker.sh
        sudo usermod -aG docker $USER
        
        # Install Docker Compose
        sudo curl -L "https://github.com/docker/compose/releases/download/v2.24.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
        sudo chmod +x /usr/local/bin/docker-compose
        
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        # Install Docker Desktop on macOS
        echo "Please install Docker Desktop from: https://www.docker.com/products/docker-desktop"
    fi
fi

echo
echo "====================================="
echo "System dependencies installed!"
echo "====================================="
echo
echo "Next steps:"
echo "1. Run ./setup.sh to set up the VALO system"
echo "2. Configure PostgreSQL if not already done"
echo "3. Start services:"
echo "   - PostgreSQL: sudo systemctl start postgresql"
echo "   - Redis: sudo systemctl start redis"
echo "   - Or use: docker-compose up"
echo