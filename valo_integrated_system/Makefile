# VALO Integrated System Makefile
# Simplifies common development and deployment tasks

.PHONY: help install setup clean test run dashboard process docker-up docker-down docker-build health-check

# Default target
help:
	@echo "VALO Integrated System - Available Commands:"
	@echo "============================================"
	@echo "  make install       - Install system dependencies"
	@echo "  make setup         - Run initial setup wizard"
	@echo "  make health-check  - Check system health"
	@echo "  make run           - Start the dashboard"
	@echo "  make process FILE=<csv> - Process a CSV file"
	@echo "  make test          - Run all tests"
	@echo "  make clean         - Clean temporary files"
	@echo "  make docker-up     - Start with Docker"
	@echo "  make docker-down   - Stop Docker containers"
	@echo "  make docker-build  - Build Docker images"

# Install system dependencies
install:
	@echo "Installing system dependencies..."
	@./install_dependencies.sh

# Run setup wizard
setup:
	@echo "Running VALO setup..."
	@./setup.sh

# Check system health
health-check:
	@echo "Checking system health..."
	@python3 health_check.py

# Create virtual environment
venv:
	@if [ ! -d "venv" ]; then \
		echo "Creating virtual environment..."; \
		python3 -m venv venv; \
	fi
	@echo "Activating virtual environment..."
	@. venv/bin/activate && pip install -r requirements.txt

# Run the dashboard
run: venv
	@echo "Starting VALO dashboard..."
	@. venv/bin/activate && python orchestrator.py dashboard

# Alias for run
dashboard: run

# Process a CSV file
process: venv
	@if [ -z "$(FILE)" ]; then \
		echo "Error: Please specify a CSV file with FILE=path/to/file.csv"; \
		exit 1; \
	fi
	@echo "Processing $(FILE)..."
	@. venv/bin/activate && python orchestrator.py process $(FILE) $(ARGS)

# Run tests
test: venv
	@echo "Running tests..."
	@. venv/bin/activate && pytest tests/ -v

# Run linting
lint: venv
	@echo "Running code linting..."
	@. venv/bin/activate && flake8 agents/ orchestrator.py
	@. venv/bin/activate && black --check agents/ orchestrator.py

# Format code
format: venv
	@echo "Formatting code..."
	@. venv/bin/activate && black agents/ orchestrator.py

# Clean temporary files
clean:
	@echo "Cleaning temporary files..."
	@find . -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true
	@find . -type f -name "*.pyc" -delete
	@find . -type f -name "*.pyo" -delete
	@find . -type f -name ".DS_Store" -delete
	@rm -rf .pytest_cache
	@rm -rf htmlcov
	@rm -rf .coverage
	@echo "Clean complete!"

# Deep clean (includes logs and cache)
deep-clean: clean
	@echo "Performing deep clean..."
	@rm -rf logs/*.log
	@rm -rf data/cache/*
	@echo "Deep clean complete!"

# Docker commands
docker-build:
	@echo "Building Docker images..."
	@docker-compose build

docker-up:
	@echo "Starting Docker containers..."
	@docker-compose up -d
	@echo "Dashboard available at: http://localhost:5000"

docker-down:
	@echo "Stopping Docker containers..."
	@docker-compose down

docker-logs:
	@docker-compose logs -f

docker-shell:
	@docker-compose exec valo-system /bin/bash

# Database commands
db-migrate:
	@echo "Running database migrations..."
	@psql -U valo_user -d valo_system -f database/postgresql_schema.sql

db-backup:
	@echo "Backing up database..."
	@pg_dump -U valo_user -d valo_system > backups/valo_backup_$(shell date +%Y%m%d_%H%M%S).sql

db-restore:
	@if [ -z "$(BACKUP)" ]; then \
		echo "Error: Please specify backup file with BACKUP=path/to/backup.sql"; \
		exit 1; \
	fi
	@echo "Restoring database from $(BACKUP)..."
	@psql -U valo_user -d valo_system < $(BACKUP)

# Development helpers
dev-server: venv
	@echo "Starting development server with auto-reload..."
	@. venv/bin/activate && python orchestrator.py dashboard --debug

create-round: venv
	@echo "Creating new processing round..."
	@. venv/bin/activate && python -c "from orchestrator import VALOOrchestrator; import asyncio; o = VALOOrchestrator(); asyncio.run(o.initialize()); asyncio.run(o.create_round())"

# Production deployment
deploy-prod:
	@echo "Deploying to production..."
	@docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d

# Generate requirements
freeze:
	@. venv/bin/activate && pip freeze > requirements.txt

# Show system status
status:
	@. venv/bin/activate && python orchestrator.py status