"""
SQLite Database Connection Manager
Enhanced VALO System
"""
import os
import sqlite3
import logging
from typing import Dict, Any, Optional, List
from contextlib import contextmanager
import json

logger = logging.getLogger(__name__)

class DatabaseManager:
    """PostgreSQL database connection manager with connection pooling"""
    
    def __init__(self, config: Dict[str, Any] = None):
        """Initialize database manager with configuration"""
        self.config = config or self._load_default_config()
        self.pool = None
        self._initialize_pool()
    
    def _load_default_config(self) -> Dict[str, Any]:
        """Load default database configuration"""
        return {
            'host': os.getenv('POSTGRES_HOST', 'localhost'),
            'port': int(os.getenv('POSTGRES_PORT', 5432)),
            'database': os.getenv('POSTGRES_DB', 'valo_system'),
            'user': os.getenv('POSTGRES_USER', 'postgres'),
            'password': os.getenv('POSTGRES_PASSWORD', 'postgres'),
            'min_connections': 2,
            'max_connections': 20
        }
    
    def _initialize_pool(self):
        """Initialize connection pool"""
        try:
            self.pool = ThreadedConnectionPool(
                minconn=self.config['min_connections'],
                maxconn=self.config['max_connections'],
                host=self.config['host'],
                port=self.config['port'],
                database=self.config['database'],
                user=self.config['user'],
                password=self.config['password'],
                cursor_factory=psycopg2.extras.RealDictCursor
            )
            logger.info(f"Database connection pool initialized: {self.config['host']}:{self.config['port']}")
        except Exception as e:
            logger.error(f"Failed to initialize database pool: {e}")
            raise
    
    @contextmanager
    def get_connection(self):
        """Get database connection from pool"""
        conn = None
        try:
            conn = self.pool.getconn()
            yield conn
        except Exception as e:
            if conn:
                conn.rollback()
            logger.error(f"Database connection error: {e}")
            raise
        finally:
            if conn:
                self.pool.putconn(conn)
    
    def execute_query(self, query: str, params: tuple = None) -> List[Dict[str, Any]]:
        """Execute SELECT query and return results"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(query, params)
            return cursor.fetchall()
    
    def execute_insert(self, query: str, params: tuple = None) -> str:
        """Execute INSERT query and return the inserted ID"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(query, params)
            result = cursor.fetchone()
            conn.commit()
            return result['id'] if result and 'id' in result else None
    
    def execute_update(self, query: str, params: tuple = None) -> int:
        """Execute UPDATE/DELETE query and return affected rows"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(query, params)
            affected = cursor.rowcount
            conn.commit()
            return affected
    
    def execute_batch(self, query: str, params_list: List[tuple]) -> int:
        """Execute batch INSERT/UPDATE"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            psycopg2.extras.execute_batch(cursor, query, params_list, page_size=100)
            affected = cursor.rowcount
            conn.commit()
            return affected
    
    def initialize_schema(self, schema_file: str = 'database/schema.sql'):
        """Initialize database schema from SQL file"""
        try:
            with open(schema_file, 'r') as f:
                schema_sql = f.read()
            
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(schema_sql)
                conn.commit()
                logger.info("Database schema initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize schema: {e}")
            raise
    
    def close_pool(self):
        """Close all connections in pool"""
        if self.pool:
            self.pool.closeall()
            logger.info("Database connection pool closed")

class VALODatabaseORM:
    """ORM-style interface for VALO database operations"""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager
    
    # Round Management
    def create_round(self, name: str, description: str = None, csv_file_path: str = None) -> str:
        """Create a new processing round"""
        query = """
        INSERT INTO rounds (name, description, csv_file_path, status)
        VALUES (%s, %s, %s, 'pending')
        RETURNING id
        """
        return self.db.execute_insert(query, (name, description, csv_file_path))
    
    def get_round(self, round_id: str) -> Optional[Dict[str, Any]]:
        """Get round by ID"""
        query = "SELECT * FROM round_summary WHERE id = %s"
        results = self.db.execute_query(query, (round_id,))
        return results[0] if results else None
    
    def get_rounds(self, limit: int = 50) -> List[Dict[str, Any]]:
        """Get all rounds with summary data"""
        query = """
        SELECT * FROM round_summary 
        ORDER BY start_time DESC 
        LIMIT %s
        """
        return self.db.execute_query(query, (limit,))
    
    def update_round_status(self, round_id: str, status: str, **kwargs) -> int:
        """Update round status and optional fields"""
        set_clauses = ["status = %s"]
        params = [status]
        
        if 'total_cases' in kwargs:
            set_clauses.append("total_cases = %s")
            params.append(kwargs['total_cases'])
        
        if 'processed_cases' in kwargs:
            set_clauses.append("processed_cases = %s")
            params.append(kwargs['processed_cases'])
        
        if status == 'completed':
            set_clauses.append("end_time = CURRENT_TIMESTAMP")
        
        params.append(round_id)
        
        query = f"""
        UPDATE rounds 
        SET {', '.join(set_clauses)}
        WHERE id = %s
        """
        return self.db.execute_update(query, tuple(params))
    
    # Case Management
    def insert_case(self, round_id: str, case_data: Dict[str, Any]) -> str:
        """Insert a new case with comprehensive data"""
        # Calculate quadrant based on csv_ground_truth and vlm_filtered
        is_valid = case_data.get('csv_ground_truth', '').lower() == 'valid'
        vlm_filtered = case_data.get('vlm_filtered', False)
        
        if is_valid and not vlm_filtered:
            quadrant = 'valid_passed'
        elif is_valid and vlm_filtered:
            quadrant = 'valid_failed'
        elif not is_valid and not vlm_filtered:
            quadrant = 'invalid_passed'
        else:
            quadrant = 'invalid_failed'
        
        query = """
        INSERT INTO cases (
            round_id, case_number, infringement_type, csv_ground_truth,
            source_image_path, cropped_image_path, vlm_query, vlm_response,
            vlm_decision, person_present, subject_confidence,
            fp_likelihood, vlm_tokens_used, processing_time, quadrant
        ) VALUES (
            %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
        ) RETURNING id
        """
        
        params = (
            round_id,
            case_data.get('case_number'),
            case_data.get('infringement_type'),
            case_data.get('csv_ground_truth'),
            case_data.get('source_image_path'),
            case_data.get('cropped_image_path'),
            case_data.get('vlm_query'),
            json.dumps(case_data.get('vlm_response', {})),
            case_data.get('vlm_decision'),
            case_data.get('vlm_reasoning'),
            case_data.get('person_confidence'),
            case_data.get('structure_confidence'),
            case_data.get('violation_confidence'),
            case_data.get('tokens_used', 0),
            case_data.get('processing_time_ms', 0),
            vlm_filtered,
            is_valid,
            quadrant
        )
        
        return self.db.execute_insert(query, params)
    
    def get_cases(self, round_id: str = None, quadrant: str = None, 
                  infringement_type: str = None, limit: int = 50, offset: int = 0) -> List[Dict[str, Any]]:
        """Get cases with filtering options"""
        where_clauses = []
        params = []
        
        if round_id:
            where_clauses.append("round_id = %s")
            params.append(round_id)
        
        if quadrant:
            where_clauses.append("quadrant = %s")
            params.append(quadrant)
        
        if infringement_type:
            where_clauses.append("infringement_type ILIKE %s")
            params.append(f"%{infringement_type}%")
        
        where_clause = "WHERE " + " AND ".join(where_clauses) if where_clauses else ""
        
        query = f"""
        SELECT c.*, r.name as round_name
        FROM cases c
        LEFT JOIN rounds r ON c.round_id = r.id
        {where_clause}
        ORDER BY c.processed_at DESC
        LIMIT %s OFFSET %s
        """
        
        params.extend([limit, offset])
        return self.db.execute_query(query, tuple(params))
    
    def update_case_review(self, case_id: str, reviewer_name: str,
                          new_status: str, remarks: str = None) -> str:
        """Add human review to a case"""
        # First, get current case status
        case = self.db.execute_query("SELECT human_review_notes FROM cases WHERE id = %s", (case_id,))
        previous_status = case[0]['human_review_notes'] if case else None

        # Update case with human review
        self.db.execute_update("""
        UPDATE cases
        SET human_review_status = 'reviewed',
            human_review_notes = %s,
            reviewed_by = %s,
            reviewed_at = CURRENT_TIMESTAMP
        WHERE id = %s
        """, (remarks, reviewer_name, case_id))

        # Log the review in processing_logs table instead of case_reviews
        log_query = """
        INSERT INTO processing_logs (round_id, case_number, agent_name, log_level, message, details)
        SELECT round_id, case_number, %s, 'INFO', 'Human review updated',
               jsonb_build_object('previous_status', %s, 'new_status', %s, 'remarks', %s)
        FROM cases WHERE id = %s
        RETURNING id
        """
        return self.db.execute_insert(log_query, (reviewer_name, previous_status, new_status, remarks, case_id))
    
    # Analytics Methods
    def get_quadrant_analysis(self, round_id: str = None) -> List[Dict[str, Any]]:
        """Get quadrant analysis data"""
        if round_id:
            query = """
            SELECT
                r.name as round_name,
                c.quadrant,
                COUNT(*) as case_count,
                AVG(c.vlm_tokens_used) as avg_tokens,
                AVG(c.fp_likelihood) as avg_fp_likelihood
            FROM rounds r
            JOIN cases c ON r.id = c.round_id
            WHERE r.id = %s
            GROUP BY r.name, c.quadrant
            ORDER BY c.quadrant
            """
            return self.db.execute_query(query, (round_id,))
        else:
            query = """
            SELECT
                r.name as round_name,
                c.quadrant,
                COUNT(*) as case_count,
                AVG(c.vlm_tokens_used) as avg_tokens,
                AVG(c.fp_likelihood) as avg_fp_likelihood
            FROM rounds r
            JOIN cases c ON r.id = c.round_id
            GROUP BY r.name, c.quadrant
            ORDER BY r.name DESC, c.quadrant
            """
            return self.db.execute_query(query)
    
    def get_infringement_analysis(self, round_id: str = None) -> List[Dict[str, Any]]:
        """Get infringement type analysis"""
        if round_id:
            query = """
            SELECT
                infringement_type,
                COUNT(*) as total_cases,
                COUNT(*) FILTER (WHERE quadrant IN ('valid_passed', 'invalid_failed')) as correct_decisions,
                COUNT(*) FILTER (WHERE csv_ground_truth = 'Invalid') as false_positives,
                AVG(fp_likelihood) as avg_fp_likelihood,
                PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY fp_likelihood) as median_fp_likelihood
            FROM cases
            WHERE round_id = %s
            GROUP BY infringement_type
            ORDER BY total_cases DESC
            """
            return self.db.execute_query(query, (round_id,))
        else:
            query = """
            SELECT
                infringement_type,
                COUNT(*) as total_cases,
                COUNT(*) FILTER (WHERE quadrant IN ('valid_passed', 'invalid_failed')) as correct_decisions,
                COUNT(*) FILTER (WHERE csv_ground_truth = 'Invalid') as false_positives,
                AVG(fp_likelihood) as avg_fp_likelihood,
                PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY fp_likelihood) as median_fp_likelihood
            FROM cases
            GROUP BY infringement_type
            ORDER BY total_cases DESC
            """
            return self.db.execute_query(query)
    
    def get_confidence_distributions(self, round_id: str) -> Dict[str, Any]:
        """Get confidence score distributions for analytics"""
        query = """
        SELECT
            quadrant,
            AVG(subject_confidence) as avg_person_conf,
            STDDEV(subject_confidence) as std_person_conf,
            AVG(fp_likelihood) as avg_structure_conf,
            STDDEV(fp_likelihood) as std_structure_conf,
            AVG(subject_confidence) as avg_violation_conf,
            STDDEV(subject_confidence) as std_violation_conf,
            array_agg(subject_confidence ORDER BY subject_confidence) as person_conf_dist,
            array_agg(fp_likelihood ORDER BY fp_likelihood) as structure_conf_dist,
            array_agg(subject_confidence ORDER BY subject_confidence) as violation_conf_dist
        FROM cases
        WHERE round_id = %s
        GROUP BY quadrant
        """
        return self.db.execute_query(query, (round_id,))
    
    def get_token_efficiency_analysis(self, round_id: str) -> List[Dict[str, Any]]:
        """Get token usage vs accuracy analysis"""
        query = """
        SELECT
            id as case_id,
            vlm_tokens_used as tokens_used,
            subject_confidence as violation_confidence,
            CASE
                WHEN quadrant IN ('valid_passed', 'invalid_failed') THEN 1
                ELSE 0
            END as correct_classification,
            processing_time
        FROM cases
        WHERE round_id = %s AND vlm_tokens_used > 0
        ORDER BY vlm_tokens_used
        """
        return self.db.execute_query(query, (round_id,))

# Global database manager instance
db_manager = None
valo_orm = None

def initialize_database(config: Dict[str, Any] = None) -> VALODatabaseORM:
    """Initialize global database connection"""
    global db_manager, valo_orm
    
    if not db_manager:
        db_manager = DatabaseManager(config)
        valo_orm = VALODatabaseORM(db_manager)
        logger.info("VALO database system initialized")
    
    return valo_orm

def get_database() -> VALODatabaseORM:
    """Get database ORM instance"""
    if not valo_orm:
        raise RuntimeError("Database not initialized. Call initialize_database() first.")
    return valo_orm