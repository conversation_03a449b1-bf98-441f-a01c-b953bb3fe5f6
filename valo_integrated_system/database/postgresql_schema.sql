-- VALO Integrated System PostgreSQL Schema
-- Optimized for concurrent access and analytics

-- Create database (run as superuser)
-- CREATE DATABASE valo_system;
-- \c valo_system;

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm"; -- For text search

-- Enum types for better data integrity
CREATE TYPE processing_status AS ENUM ('pending', 'running', 'completed', 'failed');
CREATE TYPE quadrant_category AS ENUM ('valid_passed', 'valid_failed', 'invalid_passed', 'invalid_failed');
CREATE TYPE review_status AS ENUM ('pending', 'reviewed', 'disputed', 'confirmed');

-- Rounds table: Each processing round with unique SGT datetime name
CREATE TABLE IF NOT EXISTS rounds (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) UNIQUE NOT NULL,
    start_time TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    end_time TIMESTAMP WITH TIME ZONE,
    total_cases INTEGER DEFAULT 0,
    processed_cases INTEGER DEFAULT 0,
    fp_detection_rate DECIMAL(5,2),
    protection_rate DECIMAL(5,2),
    status processing_status DEFAULT 'pending',
    config_params JSONB DEFAULT '{}',
    processing_stats JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Cases table: All violation cases with VLM analysis
CREATE TABLE IF NOT EXISTS cases (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    round_id UUID NOT NULL REFERENCES rounds(id) ON DELETE CASCADE,
    case_number VARCHAR(50) NOT NULL,
    
    -- CSV data
    infringement_type VARCHAR(100),
    csv_ground_truth VARCHAR(20),
    csv_remarks TEXT,
    camera_id VARCHAR(100),
    terminal VARCHAR(50),
    alert_timestamp TIMESTAMP WITH TIME ZONE,
    
    -- Image paths
    source_image_path TEXT,
    cropped_image_path TEXT,
    
    -- VLM processing
    vlm_query TEXT,
    vlm_response JSONB,
    vlm_tokens_used INTEGER DEFAULT 0,
    
    -- Parsed VLM results
    person_present BOOLEAN,
    main_subject VARCHAR(100),
    subject_confidence DECIMAL(5,2),
    helmet_status VARCHAR(20),
    vest_status VARCHAR(20),
    ppe_compliance VARCHAR(20),
    safety_violation VARCHAR(20),
    fp_likelihood DECIMAL(5,2),
    fp_indicators TEXT[],
    
    -- Decision and categorization
    vlm_decision VARCHAR(20), -- 'filtered' or 'retained'
    quadrant quadrant_category,
    
    -- Human review
    human_review_status review_status DEFAULT 'pending',
    human_review_notes TEXT,
    reviewed_by VARCHAR(100),
    reviewed_at TIMESTAMP WITH TIME ZONE,
    
    -- Processing metadata
    processing_time DECIMAL(10,3),
    attempts INTEGER DEFAULT 1,
    error_details JSONB,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT unique_round_case UNIQUE(round_id, case_number)
);

-- Analytics cache table: Pre-computed metrics for performance
CREATE TABLE IF NOT EXISTS analytics_cache (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    round_id UUID REFERENCES rounds(id) ON DELETE CASCADE,
    metric_type VARCHAR(100) NOT NULL,
    metric_data JSONB NOT NULL,
    time_range TSTZRANGE,
    computed_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP WITH TIME ZONE
);

-- Processing logs: Detailed logging for debugging
CREATE TABLE IF NOT EXISTS processing_logs (
    id BIGSERIAL PRIMARY KEY,
    round_id UUID REFERENCES rounds(id) ON DELETE CASCADE,
    case_number VARCHAR(50),
    agent_name VARCHAR(50),
    log_level VARCHAR(20),
    message TEXT,
    details JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Agent status: Track multi-agent system health
CREATE TABLE IF NOT EXISTS agent_status (
    agent_name VARCHAR(50) PRIMARY KEY,
    status VARCHAR(20) DEFAULT 'offline',
    last_heartbeat TIMESTAMP WITH TIME ZONE,
    current_task JSONB,
    metrics JSONB DEFAULT '{}'
);

-- Create indexes for performance
CREATE INDEX idx_cases_round_id ON cases(round_id);
CREATE INDEX idx_cases_case_number ON cases(case_number);
CREATE INDEX idx_cases_quadrant ON cases(quadrant);
CREATE INDEX idx_cases_infringement ON cases(infringement_type);
CREATE INDEX idx_cases_ground_truth ON cases(csv_ground_truth);
CREATE INDEX idx_cases_fp_likelihood ON cases(fp_likelihood);
CREATE INDEX idx_cases_review_status ON cases(human_review_status);

-- GIN indexes for JSONB search
CREATE INDEX idx_cases_vlm_response ON cases USING GIN (vlm_response);
CREATE INDEX idx_analytics_metrics ON analytics_cache USING GIN (metric_data);

-- Text search indexes
CREATE INDEX idx_cases_remarks ON cases USING GIN (to_tsvector('english', csv_remarks));

-- Partitioning for logs (by month)
CREATE TABLE processing_logs_template (LIKE processing_logs INCLUDING ALL);

-- Functions for analytics
CREATE OR REPLACE FUNCTION calculate_quadrant(
    ground_truth VARCHAR,
    vlm_filtered BOOLEAN
) RETURNS quadrant_category AS $$
BEGIN
    IF ground_truth = 'Valid' AND NOT vlm_filtered THEN
        RETURN 'valid_passed'::quadrant_category;
    ELSIF ground_truth = 'Valid' AND vlm_filtered THEN
        RETURN 'valid_failed'::quadrant_category;
    ELSIF ground_truth = 'Invalid' AND NOT vlm_filtered THEN
        RETURN 'invalid_passed'::quadrant_category;
    ELSE
        RETURN 'invalid_failed'::quadrant_category;
    END IF;
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Trigger to update timestamps
CREATE OR REPLACE FUNCTION update_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_rounds_timestamp
    BEFORE UPDATE ON rounds
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at();

CREATE TRIGGER update_cases_timestamp
    BEFORE UPDATE ON cases
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at();

-- Views for analytics
CREATE OR REPLACE VIEW round_analytics AS
SELECT 
    r.id,
    r.name,
    r.status,
    COUNT(c.id) as total_cases,
    COUNT(c.id) FILTER (WHERE c.quadrant = 'valid_passed') as valid_passed,
    COUNT(c.id) FILTER (WHERE c.quadrant = 'valid_failed') as valid_failed,
    COUNT(c.id) FILTER (WHERE c.quadrant = 'invalid_passed') as invalid_passed,
    COUNT(c.id) FILTER (WHERE c.quadrant = 'invalid_failed') as invalid_failed,
    AVG(c.fp_likelihood) FILTER (WHERE c.csv_ground_truth = 'Invalid') as avg_fp_likelihood,
    AVG(c.subject_confidence) FILTER (WHERE c.csv_ground_truth = 'Valid' AND c.person_present) as avg_valid_person_conf,
    AVG(c.processing_time) as avg_processing_time,
    SUM(c.vlm_tokens_used) as total_tokens_used
FROM rounds r
LEFT JOIN cases c ON r.id = c.round_id
GROUP BY r.id, r.name, r.status;

-- Infringement type performance view
CREATE OR REPLACE VIEW infringement_analytics AS
SELECT 
    round_id,
    infringement_type,
    COUNT(*) as total_cases,
    COUNT(*) FILTER (WHERE quadrant IN ('valid_passed', 'invalid_failed')) as correct_decisions,
    COUNT(*) FILTER (WHERE csv_ground_truth = 'Invalid') as false_positives,
    AVG(fp_likelihood) as avg_fp_likelihood,
    PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY fp_likelihood) as median_fp_likelihood
FROM cases
GROUP BY round_id, infringement_type;

-- Create roles for multi-agent access
CREATE ROLE valo_processor;
CREATE ROLE valo_analytics;
CREATE ROLE valo_web;

-- Grant appropriate permissions
GRANT ALL ON ALL TABLES IN SCHEMA public TO valo_processor;
GRANT SELECT ON ALL TABLES IN SCHEMA public TO valo_analytics;
GRANT SELECT, INSERT, UPDATE ON cases, analytics_cache TO valo_web;

-- Sample data for testing
INSERT INTO agent_status (agent_name, status) VALUES
    ('processing_agent', 'offline'),
    ('analytics_agent', 'offline'),
    ('web_agent', 'offline'),
    ('data_agent', 'offline'),
    ('orchestrator', 'offline');