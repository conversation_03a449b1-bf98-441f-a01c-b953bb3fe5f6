-- VALO Violation Processing System - PostgreSQL Schema
-- Enhanced system with comprehensive analytics and review capabilities

-- Drop existing tables if they exist
DROP TABLE IF EXISTS case_reviews CASCADE;
DROP TABLE IF EXISTS cases CASCADE;
DROP TABLE IF EXISTS rounds CASCADE;
DROP TABLE IF EXISTS system_config CASCAD<PERSON>;

-- Rounds table: SGT datetime-based processing rounds
CREATE TABLE rounds (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL UNIQUE, -- e.g., "Round_20250728_1430"
    description TEXT,
    status VARCHAR(50) DEFAULT 'pending', -- pending, running, paused, completed, failed
    total_cases INTEGER DEFAULT 0,
    processed_cases INTEGER DEFAULT 0,
    start_time TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    end_time TIMESTAMP WITH TIME ZONE,
    csv_file_path TEXT,
    config JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Cases table: Individual violation cases with comprehensive data
CREATE TABLE cases (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    round_id UUID NOT NULL REFERENCES rounds(id) ON DELETE CASCADE,
    case_number VARCHAR(255) NOT NULL, -- From CSV: case_number or pk_event
    infringement_type VARCHAR(255), -- From CSV: infringement_type
    
    -- CSV Ground Truth
    csv_ground_truth VARCHAR(50), -- 'valid' or 'invalid' from CSV key field
    
    -- Image Paths
    source_image_path TEXT, -- Path to original image
    cropped_image_path TEXT, -- Path to cropped violation region
    
    -- VLM Processing Data
    vlm_query TEXT, -- Full prompt sent to VLM
    vlm_response JSONB, -- Complete VLM response
    vlm_decision VARCHAR(255), -- VLM's violation decision
    vlm_reasoning TEXT, -- VLM's explanation
    
    -- Confidence Scores
    person_confidence DECIMAL(5,2), -- Person detection confidence (0-100)
    structure_confidence DECIMAL(5,2), -- Structure detection confidence (0-100)
    violation_confidence DECIMAL(5,2), -- Overall violation confidence (0-100)
    
    -- Processing Metadata
    tokens_used INTEGER DEFAULT 0,
    processing_time_ms INTEGER DEFAULT 0,
    vlm_filtered BOOLEAN DEFAULT false, -- Whether VLM filtered this case
    
    -- Classification (Quadrant System)
    is_valid BOOLEAN, -- Derived from csv_ground_truth
    quadrant VARCHAR(20), -- valid_passed, valid_failed, invalid_passed, invalid_failed
    
    -- Human Review
    human_reviewed BOOLEAN DEFAULT false,
    human_ground_truth VARCHAR(50), -- Human override of ground truth
    human_remarks TEXT,
    reviewed_by VARCHAR(255),
    reviewed_at TIMESTAMP WITH TIME ZONE,
    
    -- Timestamps
    processed_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Case Reviews table: Track human review history
CREATE TABLE case_reviews (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    case_id UUID NOT NULL REFERENCES cases(id) ON DELETE CASCADE,
    reviewer_name VARCHAR(255),
    previous_status VARCHAR(50),
    new_status VARCHAR(50),
    remarks TEXT,
    confidence_override JSONB, -- Override confidence scores if needed
    review_timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- System Configuration table
CREATE TABLE system_config (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    key VARCHAR(255) UNIQUE NOT NULL,
    value JSONB,
    description TEXT,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Indexes for performance
CREATE INDEX idx_rounds_name ON rounds(name);
CREATE INDEX idx_rounds_status ON rounds(status);
CREATE INDEX idx_rounds_start_time ON rounds(start_time);

CREATE INDEX idx_cases_round_id ON cases(round_id);
CREATE INDEX idx_cases_case_number ON cases(case_number);
CREATE INDEX idx_cases_infringement_type ON cases(infringement_type);
CREATE INDEX idx_cases_quadrant ON cases(quadrant);
CREATE INDEX idx_cases_csv_ground_truth ON cases(csv_ground_truth);
CREATE INDEX idx_cases_vlm_filtered ON cases(vlm_filtered);
CREATE INDEX idx_cases_human_reviewed ON cases(human_reviewed);
CREATE INDEX idx_cases_processed_at ON cases(processed_at);

CREATE INDEX idx_case_reviews_case_id ON case_reviews(case_id);
CREATE INDEX idx_case_reviews_timestamp ON case_reviews(review_timestamp);

-- Triggers for updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_rounds_updated_at BEFORE UPDATE ON rounds
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_cases_updated_at BEFORE UPDATE ON cases
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert default system configuration
INSERT INTO system_config (key, value, description) VALUES
('vlm_endpoint', '{"url": "http://**************:9500/v1", "model": "VLM-38B-AWQ"}', 'VLM API endpoint configuration'),
('processing_batch_size', '10', 'Number of cases to process in parallel'),
('confidence_thresholds', '{"person": 90, "structure": 85, "violation": 70}', 'Confidence score thresholds for analytics'),
('image_storage_path', '"/data/valo_images"', 'Base path for storing violation images'),
('analytics_refresh_interval', '300', 'Analytics cache refresh interval in seconds');

-- Views for analytics and reporting

-- Quadrant Analysis View
CREATE OR REPLACE VIEW quadrant_analysis AS
SELECT 
    r.name as round_name,
    c.quadrant,
    COUNT(*) as case_count,
    AVG(c.person_confidence) as avg_person_conf,
    AVG(c.structure_confidence) as avg_structure_conf,
    AVG(c.violation_confidence) as avg_violation_conf,
    AVG(c.tokens_used) as avg_tokens,
    AVG(c.processing_time_ms) as avg_processing_time
FROM cases c
JOIN rounds r ON c.round_id = r.id
GROUP BY r.name, c.quadrant;

-- Infringement Type Analysis View
CREATE OR REPLACE VIEW infringement_analysis AS
SELECT 
    r.name as round_name,
    c.infringement_type,
    COUNT(*) as total_cases,
    COUNT(CASE WHEN c.quadrant = 'valid_passed' THEN 1 END) as valid_passed,
    COUNT(CASE WHEN c.quadrant = 'valid_failed' THEN 1 END) as valid_failed,
    COUNT(CASE WHEN c.quadrant = 'invalid_passed' THEN 1 END) as invalid_passed,
    COUNT(CASE WHEN c.quadrant = 'invalid_failed' THEN 1 END) as invalid_failed,
    ROUND(
        COUNT(CASE WHEN c.quadrant IN ('invalid_failed', 'valid_passed') THEN 1 END)::DECIMAL / 
        COUNT(*)::DECIMAL * 100, 2
    ) as accuracy_percentage
FROM cases c
JOIN rounds r ON c.round_id = r.id
GROUP BY r.name, c.infringement_type;

-- Round Summary View
CREATE OR REPLACE VIEW round_summary AS
SELECT 
    r.id,
    r.name,
    r.status,
    r.total_cases,
    r.processed_cases,
    ROUND((r.processed_cases::DECIMAL / NULLIF(r.total_cases, 0)::DECIMAL * 100), 2) as progress_percentage,
    r.start_time,
    r.end_time,
    EXTRACT(EPOCH FROM (COALESCE(r.end_time, CURRENT_TIMESTAMP) - r.start_time)) as duration_seconds,
    COUNT(c.id) as actual_processed_cases,
    AVG(c.tokens_used) as avg_tokens_per_case,
    SUM(c.tokens_used) as total_tokens_used,
    COUNT(CASE WHEN c.human_reviewed THEN 1 END) as human_reviewed_count
FROM rounds r
LEFT JOIN cases c ON r.id = c.round_id
GROUP BY r.id, r.name, r.status, r.total_cases, r.processed_cases, r.start_time, r.end_time;

COMMENT ON TABLE rounds IS 'Processing rounds with SGT datetime-based naming';
COMMENT ON TABLE cases IS 'Individual violation cases with comprehensive VLM and human review data';
COMMENT ON TABLE case_reviews IS 'Audit trail for human reviews of cases';
COMMENT ON TABLE system_config IS 'System-wide configuration parameters';