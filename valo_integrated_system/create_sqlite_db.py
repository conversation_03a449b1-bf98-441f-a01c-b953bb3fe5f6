#!/usr/bin/env python3
"""
Create SQLite database for VALO system
"""
import sqlite3
import os

# Database file path
db_path = './valo_system.db'

# Remove existing database if it exists
if os.path.exists(db_path):
    os.remove(db_path)
    print(f"Removed existing database: {db_path}")

# Create SQLite database
conn = sqlite3.connect(db_path)
cursor = conn.cursor()

# Create tables
cursor.executescript("""
-- Create rounds table
CREATE TABLE rounds (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    total_cases INTEGER DEFAULT 0,
    processed_cases INTEGER DEFAULT 0,
    config TEXT
);

-- Create cases table  
CREATE TABLE cases (
    id TEXT PRIMARY KEY,
    round_id TEXT NOT NULL,
    case_number TEXT NOT NULL,
    infringement_type TEXT,
    csv_ground_truth TEXT,
    vlm_decision TEXT,
    vlm_response TEXT,
    fp_likelihood REAL,
    quadrant TEXT,
    cropped_image TEXT,
    source_image TEXT,
    person_present INTEGER DEFAULT 0,
    person_confidence REAL,
    main_subject TEXT,
    subject_confidence REAL,
    violation_detected INTEGER DEFAULT 0,
    violation_confidence REAL,
    structure_type TEXT,
    structure_confidence REAL,
    processing_time REAL,
    tokens_used INTEGER,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (round_id) REFERENCES rounds(id)
);

-- Create agent_status table
CREATE TABLE agent_status (
    agent_name TEXT PRIMARY KEY,
    status TEXT NOT NULL,
    last_heartbeat DATETIME DEFAULT CURRENT_TIMESTAMP,
    details TEXT
);

-- Create processing_logs table
CREATE TABLE processing_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    round_id TEXT,
    case_id TEXT,
    agent_name TEXT,
    log_level TEXT,
    message TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Create analytics_cache table
CREATE TABLE analytics_cache (
    cache_key TEXT PRIMARY KEY,
    data TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    expires_at DATETIME
);

-- Create indexes
CREATE INDEX idx_cases_round_id ON cases(round_id);
CREATE INDEX idx_cases_quadrant ON cases(quadrant);
CREATE INDEX idx_cases_case_number ON cases(case_number);
CREATE INDEX idx_processing_logs_round_id ON processing_logs(round_id);

-- Insert initial agent statuses
INSERT INTO agent_status (agent_name, status) VALUES 
    ('data_agent', 'offline'),
    ('processing_agent', 'offline'),
    ('analytics_agent', 'offline'), 
    ('web_agent', 'offline');
""")

conn.commit()
conn.close()

print(f"Created SQLite database: {db_path}")
print("Database schema initialized successfully!")