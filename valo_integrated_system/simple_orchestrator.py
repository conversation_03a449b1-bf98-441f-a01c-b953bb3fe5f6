#!/usr/bin/env python3
"""
Simple VALO Orchestrator - Traditional Setup
No Docker, uses SQLite, simple Flask web interface
"""
import yaml
import logging
import threading
import time
from flask import Flask, render_template, jsonify, request
from flask_cors import CORS
from agents.sqlite_data_agent import SQLiteDataAgent

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('valo_orchestrator')

class SimpleVALOOrchestrator:
    def __init__(self, config_path='config.yaml'):
        """Initialize the VALO orchestrator"""
        with open(config_path, 'r') as f:
            self.config = yaml.safe_load(f)
        
        self.data_agent = SQLiteDataAgent(self.config['database']['path'])
        self.app = Flask(__name__)
        CORS(self.app)
        
        # Setup routes
        self.setup_routes()
        
        # Update agent status
        self.data_agent.update_agent_status('orchestrator', 'online', 'System started')
        
        logger.info("VALO Simple Orchestrator initialized")
    
    def setup_routes(self):
        """Setup Flask routes"""
        
        @self.app.route('/')
        def dashboard():
            """Main dashboard"""
            return render_template('dashboard.html')
        
        @self.app.route('/review')
        def review():
            """Case review interface"""
            return render_template('review.html')
        
        @self.app.route('/analytics')
        def analytics():
            """Analytics interface"""  
            return render_template('analytics.html')
        
        @self.app.route('/api/rounds', methods=['GET'])
        def get_rounds():
            """Get all processing rounds"""
            rounds = self.data_agent.get_rounds()
            return jsonify(rounds)
        
        @self.app.route('/api/rounds', methods=['POST'])
        def create_round():
            """Create a new round"""
            data = request.get_json()
            round_id = self.data_agent.create_round(
                name=data['name'],
                config=data.get('config', {})
            )
            return jsonify({'round_id': round_id})
        
        @self.app.route('/api/rounds/<round_id>/status')
        def get_round_status(round_id):
            """Get round statistics"""
            status = self.data_agent.get_round_status(round_id)
            return jsonify(status)
        
        @self.app.route('/api/rounds/<round_id>/cases')
        def get_cases(round_id):
            """Get cases for a round"""
            quadrant = request.args.get('quadrant')
            infringement = request.args.get('infringement') 
            limit = int(request.args.get('limit', 50))
            
            cases = self.data_agent.get_cases(
                round_id=round_id,
                quadrant=quadrant,
                infringement=infringement,
                limit=limit
            )
            return jsonify(cases)
        
        @self.app.route('/api/agents/status')
        def get_agent_status():
            """Get agent statuses"""
            statuses = self.data_agent.get_agent_statuses()
            return jsonify(statuses)
        
        @self.app.route('/api/system/info')
        def system_info():
            """Get system information"""
            return jsonify({
                'system': 'VALO AI-FARM',
                'version': '1.0.0',
                'database': 'SQLite',
                'status': 'operational'
            })
    
    def start_dashboard(self):
        """Start the web dashboard"""
        web_config = self.config['web']
        
        logger.info(f"Starting VALO dashboard on {web_config['host']}:{web_config['port']}")
        logger.info(f"Access dashboard at: http://localhost:{web_config['port']}")
        
        # Update agent status
        self.data_agent.update_agent_status('web_agent', 'online', f"Dashboard running on port {web_config['port']}")
        
        self.app.run(
            host=web_config['host'],
            port=web_config['port'],
            debug=False,
            threaded=True
        )

def main():
    """Main entry point"""
    print("🎯 VALO AI-FARM System - Traditional Setup")
    print("=" * 50)
    
    try:
        orchestrator = SimpleVALOOrchestrator()
        
        # Create a demo round if none exist
        rounds = orchestrator.data_agent.get_rounds()
        if not rounds:
            round_id = orchestrator.data_agent.create_round("Demo Round", {"demo": True})
            logger.info(f"Created demo round: {round_id}")
        
        # Start the dashboard
        orchestrator.start_dashboard()
        
    except KeyboardInterrupt:
        logger.info("Shutting down VALO system...")
    except Exception as e:
        logger.error(f"Error: {e}")
        raise

if __name__ == "__main__":
    main()