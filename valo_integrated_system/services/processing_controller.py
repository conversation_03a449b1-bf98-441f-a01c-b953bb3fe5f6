"""
VALO Processing Controller - Manages START/PAUSE/STOP processing operations
Handles real-time processing state and background task management
"""
import asyncio
import threading
import time
import logging
from datetime import datetime
from enum import Enum
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
import json

from vlm.simple_client import <PERSON><PERSON><PERSON><PERSON>lient
from vlm.client import <PERSON><PERSON><PERSON><PERSON>
from database.connection import get_database
from utils.valo_data_matcher import get_valo_data_matcher

logger = logging.getLogger(__name__)

class ProcessingState(Enum):
    IDLE = "idle"
    RUNNING = "running"
    PAUSED = "paused"
    STOPPED = "stopped"
    COMPLETED = "completed"
    ERROR = "error"

@dataclass
class ProcessingStatus:
    state: ProcessingState
    round_id: str
    round_name: str
    total_cases: int
    processed_cases: int
    current_case_index: int
    start_time: datetime
    pause_time: Optional[datetime] = None
    last_update: Optional[datetime] = None
    error_message: Optional[str] = None
    cases_per_minute: float = 0.0
    estimated_completion: Optional[datetime] = None

class VALOProcessingController:
    """Controls VALO processing operations with START/PAUSE/STOP functionality"""
    
    def __init__(self, vlm_endpoint: str = "http://100.106.127.35:9500/v1"):
        self.vlm_endpoint = vlm_endpoint
        self.current_status: Optional[ProcessingStatus] = None
        self.processing_task: Optional[asyncio.Task] = None
        self.stop_event = asyncio.Event()
        self.pause_event = asyncio.Event()
        self.batch_size = 5
        self.processing_lock = asyncio.Lock()
        
        # Initialize components
        self.db = get_database()
        self.data_matcher = get_valo_data_matcher()
        
    async def start_processing(self, description: str = None) -> Dict[str, Any]:
        """Start a new processing round"""
        async with self.processing_lock:
            if self.current_status and self.current_status.state in [ProcessingState.RUNNING, ProcessingState.PAUSED]:
                return {'success': False, 'error': 'Processing already in progress'}
            
            try:
                # Initialize data matcher
                total_csv, matched_cases, cases_list = self.data_matcher.load_and_match_data()
                
                if matched_cases == 0:
                    return {'success': False, 'error': 'No processable cases found (no images match CSV entries)'}
                
                # Create new round
                round_name = self._create_round_name()
                round_id = self.db.create_round(
                    name=round_name,
                    description=description or f"Processing {matched_cases} matched cases",
                    csv_file_path=self.data_matcher.csv_path
                )
                
                # Update round with total cases
                self.db.update_round_status(round_id, 'processing', total_cases=matched_cases)
                
                # Initialize processing status
                self.current_status = ProcessingStatus(
                    state=ProcessingState.RUNNING,
                    round_id=round_id,
                    round_name=round_name,
                    total_cases=matched_cases,
                    processed_cases=0,
                    current_case_index=0,
                    start_time=datetime.utcnow(),
                    last_update=datetime.utcnow()
                )
                
                # Reset control events
                self.stop_event.clear()
                self.pause_event.clear()
                
                # Start background processing
                self.processing_task = asyncio.create_task(self._process_cases_background())
                
                logger.info(f"Started processing round: {round_name} ({matched_cases} cases)")
                
                return {
                    'success': True,
                    'round_id': round_id,
                    'round_name': round_name,
                    'total_cases': matched_cases,
                    'message': f'Started processing {matched_cases} cases'
                }
                
            except Exception as e:
                logger.error(f"Failed to start processing: {e}")
                return {'success': False, 'error': str(e)}
    
    async def pause_processing(self) -> Dict[str, Any]:
        """Pause current processing"""
        if not self.current_status or self.current_status.state != ProcessingState.RUNNING:
            return {'success': False, 'error': 'No active processing to pause'}
        
        self.pause_event.set()
        self.current_status.state = ProcessingState.PAUSED
        self.current_status.pause_time = datetime.utcnow()
        
        logger.info(f"Paused processing round: {self.current_status.round_name}")
        
        return {
            'success': True,
            'message': f'Paused processing at case {self.current_status.processed_cases}/{self.current_status.total_cases}'
        }
    
    async def resume_processing(self) -> Dict[str, Any]:
        """Resume paused processing"""
        if not self.current_status or self.current_status.state != ProcessingState.PAUSED:
            return {'success': False, 'error': 'No paused processing to resume'}
        
        self.pause_event.clear()
        self.current_status.state = ProcessingState.RUNNING
        self.current_status.pause_time = None
        
        logger.info(f"Resumed processing round: {self.current_status.round_name}")
        
        return {
            'success': True,
            'message': f'Resumed processing from case {self.current_status.processed_cases}/{self.current_status.total_cases}'
        }
    
    async def stop_processing(self) -> Dict[str, Any]:
        """Stop current processing and keep processed data"""
        if not self.current_status or self.current_status.state in [ProcessingState.IDLE, ProcessingState.STOPPED, ProcessingState.COMPLETED]:
            return {'success': False, 'error': 'No active processing to stop'}
        
        self.stop_event.set()
        self.current_status.state = ProcessingState.STOPPED
        
        # Update database
        self.db.update_round_status(
            self.current_status.round_id, 
            'stopped',
            processed_cases=self.current_status.processed_cases
        )
        
        processed_count = self.current_status.processed_cases
        total_count = self.current_status.total_cases
        
        logger.info(f"Stopped processing round: {self.current_status.round_name} ({processed_count}/{total_count} completed)")
        
        return {
            'success': True,
            'message': f'Stopped processing. Completed {processed_count}/{total_count} cases.',
            'processed_cases': processed_count,
            'total_cases': total_count
        }
    
    def get_processing_status(self) -> Dict[str, Any]:
        """Get current processing status"""
        if not self.current_status:
            return {
                'state': ProcessingState.IDLE.value,
                'message': 'No active processing'
            }
        
        # Calculate progress
        progress_percent = 0
        if self.current_status.total_cases > 0:
            progress_percent = (self.current_status.processed_cases / self.current_status.total_cases) * 100
        
        # Calculate processing speed
        elapsed_seconds = (datetime.utcnow() - self.current_status.start_time).total_seconds()
        if elapsed_seconds > 0 and self.current_status.processed_cases > 0:
            cases_per_minute = (self.current_status.processed_cases / elapsed_seconds) * 60
        else:
            cases_per_minute = 0
        
        # Estimate completion time
        estimated_completion = None
        if cases_per_minute > 0:
            remaining_cases = self.current_status.total_cases - self.current_status.processed_cases
            remaining_minutes = remaining_cases / cases_per_minute
            estimated_completion = datetime.utcnow().timestamp() + (remaining_minutes * 60)
        
        return {
            'state': self.current_status.state.value,
            'round_id': self.current_status.round_id,
            'round_name': self.current_status.round_name,
            'total_cases': self.current_status.total_cases,
            'processed_cases': self.current_status.processed_cases,
            'current_case_index': self.current_status.current_case_index,
            'progress_percent': round(progress_percent, 1),
            'cases_per_minute': round(cases_per_minute, 1),
            'elapsed_seconds': round(elapsed_seconds),
            'estimated_completion': estimated_completion,
            'start_time': self.current_status.start_time.isoformat(),
            'pause_time': self.current_status.pause_time.isoformat() if self.current_status.pause_time else None,
            'last_update': self.current_status.last_update.isoformat() if self.current_status.last_update else None,
            'error_message': self.current_status.error_message
        }
    
    async def _process_cases_background(self):
        """Background task for processing cases - using simple synchronous VLM client"""
        try:
            # Use simple synchronous VLM client (like working enhanced processor)
            vlm_client = SimpleVLMClient(self.vlm_endpoint)
            cases_to_process = self.data_matcher.get_processable_cases()
            
            for i, case_data in enumerate(cases_to_process):
                # Check for stop signal
                if self.stop_event.is_set():
                    logger.info("Processing stopped by user")
                    break
                
                # Check for pause signal
                while self.pause_event.is_set():
                    await asyncio.sleep(1)
                    if self.stop_event.is_set():
                        break
                
                if self.stop_event.is_set():
                    break
                
                # Update current case index
                self.current_status.current_case_index = i
                
                try:
                    # Process the case synchronously (fixed method)
                    processed_case = self._process_single_case_sync(case_data, vlm_client)
                    
                    # Save to database
                    case_id = self.db.insert_case(self.current_status.round_id, processed_case)
                    
                    # Update progress
                    self.current_status.processed_cases += 1
                    self.current_status.last_update = datetime.utcnow()
                    
                    # Update database progress
                    self.db.update_round_status(
                        self.current_status.round_id,
                        'processing',
                        processed_cases=self.current_status.processed_cases
                    )
                    
                    logger.info(f"Processed case {self.current_status.processed_cases}/{self.current_status.total_cases}: {case_data['case_number']}")
                    
                    # Small delay (like working processor)
                    await asyncio.sleep(0.5)
                    
                except Exception as e:
                    logger.error(f"Error processing case {case_data['case_number']}: {e}")
                    self.current_status.error_message = str(e)
                    continue
            
            # Check if completed or stopped
            if not self.stop_event.is_set():
                # Completed successfully
                self.current_status.state = ProcessingState.COMPLETED
                self.db.update_round_status(
                    self.current_status.round_id,
                    'completed',
                    processed_cases=self.current_status.processed_cases
                )
                logger.info(f"Processing completed: {self.current_status.round_name}")
            
        except Exception as e:
            logger.error(f"Background processing error: {e}")
            if self.current_status:
                self.current_status.state = ProcessingState.ERROR
                self.current_status.error_message = str(e)
    
    def _process_single_case_sync(self, case_data: Dict[str, Any], vlm_client: SimpleVLMClient) -> Dict[str, Any]:
        """Process a single case through synchronous VLM analysis"""
        start_time = time.time()
        
        # ALWAYS use cropped image for VLM analysis (this is what triggered the alert)
        image_path = case_data.get('cropped_image_path')
        
        if not image_path:
            # Fallback to source image if cropped not available
            image_path = case_data.get('source_image_path')
            if not image_path:
                raise ValueError(f"No image available for case {case_data['case_number']}")
            logger.warning(f"Case {case_data['case_number']}: Using source image instead of cropped")
        
        logger.info(f"Processing case {case_data['case_number']} with cropped image: {image_path}")
        
        # Analyze with VLM using the cropped image (synchronous call)
        vlm_result = vlm_client.analyze_case(case_data, image_path)
        
        processing_time = (time.time() - start_time) * 1000  # Convert to milliseconds
        
        # Combine case data with VLM results
        processed_case = {
            **case_data,
            'vlm_query': vlm_result.get('query', ''),
            'vlm_response': vlm_result.get('raw_response', ''),
            'vlm_decision': vlm_result.get('decision', ''),
            'vlm_reasoning': vlm_result.get('reasoning', ''),
            'person_confidence': vlm_result.get('person_confidence', 0),
            'structure_confidence': vlm_result.get('structure_confidence', 0),
            'violation_confidence': vlm_result.get('violation_confidence', 0),
            'tokens_used': vlm_result.get('tokens_used', 0),
            'processing_time_ms': processing_time,
            'vlm_filtered': vlm_result.get('filtered', False),
            'is_valid': case_data['csv_ground_truth'] == 'valid',
            'processed_image_type': 'cropped' if case_data.get('cropped_image_path') else 'source',
            'processed_image_path': image_path
        }
        
        return processed_case
    
    async def _process_single_case(self, case_data: Dict[str, Any], vlm_client: VLMClient) -> Dict[str, Any]:
        """Process a single case through VLM analysis"""
        start_time = time.time()
        
        # ALWAYS use cropped image for VLM analysis (this is what triggered the alert)
        image_path = case_data.get('cropped_image_path')
        
        if not image_path:
            # Fallback to source image if cropped not available
            image_path = case_data.get('source_image_path')
            if not image_path:
                raise ValueError(f"No image available for case {case_data['case_number']}")
            logger.warning(f"Case {case_data['case_number']}: Using source image instead of cropped")
        
        logger.info(f"Processing case {case_data['case_number']} with cropped image: {image_path}")
        
        # Analyze with VLM using the cropped image (alert trigger)
        vlm_result = await vlm_client.analyze_case(case_data, image_path)
        
        processing_time = (time.time() - start_time) * 1000  # Convert to milliseconds
        
        # Combine case data with VLM results
        processed_case = {
            **case_data,
            'vlm_query': vlm_result.get('query', ''),
            'vlm_response': vlm_result.get('raw_response', {}),
            'vlm_decision': vlm_result.get('decision', ''),
            'vlm_reasoning': vlm_result.get('reasoning', ''),
            'person_confidence': vlm_result.get('person_confidence', 0),
            'structure_confidence': vlm_result.get('structure_confidence', 0),
            'violation_confidence': vlm_result.get('violation_confidence', 0),
            'tokens_used': vlm_result.get('tokens_used', 0),
            'processing_time_ms': processing_time,
            'vlm_filtered': vlm_result.get('filtered', False),
            'is_valid': case_data['csv_ground_truth'] == 'valid',
            'processed_image_type': 'cropped' if case_data.get('cropped_image_path') else 'source',
            'processed_image_path': image_path
        }
        
        return processed_case
    
    def _create_round_name(self) -> str:
        """Create SGT-based round name"""
        sgt_time = datetime.utcnow()  # Using UTC for consistency
        return f"Round_{sgt_time.strftime('%Y%m%d_%H%M')}"
    
    def get_data_matching_summary(self) -> Dict[str, Any]:
        """Get summary of data matching for dashboard"""
        try:
            return self.data_matcher.get_matching_summary()
        except Exception as e:
            logger.error(f"Error getting data matching summary: {e}")
            return {'error': str(e)}

# Global processing controller instance
processing_controller = None

def initialize_processing_controller(vlm_endpoint: str = None) -> VALOProcessingController:
    """Initialize global processing controller"""
    global processing_controller
    
    if not vlm_endpoint:
        vlm_endpoint = "http://100.106.127.35:9500/v1"
    
    processing_controller = VALOProcessingController(vlm_endpoint)
    logger.info("VALO processing controller initialized")
    
    return processing_controller

def get_processing_controller() -> VALOProcessingController:
    """Get the global processing controller instance"""
    if not processing_controller:
        raise RuntimeError("Processing controller not initialized. Call initialize_processing_controller() first.")
    return processing_controller