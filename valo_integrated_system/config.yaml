# VALO Integrated System Configuration
# This file will be auto-generated from environment variables in Docker

database:
  type: postgresql
  host: localhost
  port: 5432
  database: valo_system
  user: postgres
  password: postgres

vlm:
  url: http://**************:9500/v1/chat/completions
  model: VLM-38B-AWQ
  temperature: 0.1
  max_tokens: 600

web:
  host: 0.0.0.0
  port: 5002

processing:
  workers: 3
  batch_size: 10