#!/usr/bin/env python3
"""
Enhanced VALO Orchestrator with Agent Controls
"""
import yaml
import logging
import threading
import time
import random
from flask import Flask, render_template, jsonify, request
from flask_cors import CORS
from agents.sqlite_data_agent import SQLiteDataAgent
from agents.simple_processing_agent import SimpleProcessingAgent
from agents.simple_analytics_agent import SimpleAnalyticsAgent

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('valo_orchestrator')

class EnhancedVALOOrchestrator:
    def __init__(self, config_path='config.yaml'):
        """Initialize the enhanced VALO orchestrator"""
        with open(config_path, 'r') as f:
            self.config = yaml.safe_load(f)
        
        self.data_agent = SQLiteDataAgent(self.config['database']['path'])
        self.processing_agent = SimpleProcessingAgent(self.config, self.data_agent)
        self.analytics_agent = SimpleAnalyticsAgent(self.data_agent)
        
        self.app = Flask(__name__)
        CORS(self.app)
        
        # Setup routes
        self.setup_routes()
        
        # Initialize data agent as online
        self.data_agent.update_agent_status('data_agent', 'online', 'Database ready')
        self.data_agent.update_agent_status('orchestrator', 'online', 'System started')
        
        logger.info("Enhanced VALO Orchestrator initialized")
    
    def setup_routes(self):
        """Setup Flask routes"""
        
        @self.app.route('/')
        def dashboard():
            """Main dashboard"""
            return render_template('dashboard.html')
        
        @self.app.route('/review')
        def review():
            """Case review interface"""
            return render_template('review.html')
        
        @self.app.route('/analytics')
        def analytics():
            """Analytics interface"""  
            return render_template('analytics.html')
        
        # Agent control endpoints
        @self.app.route('/api/agents/<agent_name>/start', methods=['POST'])
        def start_agent(agent_name):
            """Start an agent"""
            try:
                data = request.get_json() or {}
                
                if agent_name == 'processing_agent':
                    csv_file = data.get('csv_file')
                    self.processing_agent.start(csv_file)
                elif agent_name == 'analytics_agent':
                    self.analytics_agent.start()
                elif agent_name == 'data_agent':
                    self.data_agent.update_agent_status('data_agent', 'online', 'Database ready')
                else:
                    return jsonify({'error': f'Unknown agent: {agent_name}'}), 400
                
                return jsonify({'status': 'started', 'agent': agent_name})
            except Exception as e:
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/agents/<agent_name>/pause', methods=['POST'])
        def pause_agent(agent_name):
            """Pause an agent"""
            try:
                if agent_name == 'processing_agent':
                    self.processing_agent.pause()
                    return jsonify({'status': 'paused', 'agent': agent_name})
                else:
                    return jsonify({'error': f'Agent {agent_name} does not support pause'}), 400
            except Exception as e:
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/agents/<agent_name>/resume', methods=['POST'])
        def resume_agent(agent_name):
            """Resume an agent"""
            try:
                if agent_name == 'processing_agent':
                    self.processing_agent.resume()
                    return jsonify({'status': 'resumed', 'agent': agent_name})
                else:
                    return jsonify({'error': f'Agent {agent_name} does not support resume'}), 400
            except Exception as e:
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/agents/<agent_name>/stop', methods=['POST'])
        def stop_agent(agent_name):
            """Stop an agent"""
            try:
                if agent_name == 'processing_agent':
                    self.processing_agent.stop()
                elif agent_name == 'analytics_agent':
                    self.analytics_agent.stop()
                elif agent_name == 'data_agent':
                    self.data_agent.update_agent_status('data_agent', 'offline', 'Database stopped')
                else:
                    return jsonify({'error': f'Unknown agent: {agent_name}'}), 400
                
                return jsonify({'status': 'stopped', 'agent': agent_name})
            except Exception as e:
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/processing/status')
        def get_processing_status():
            """Get detailed processing status"""
            try:
                status = self.processing_agent.get_processing_status()
                return jsonify(status)
            except Exception as e:
                return jsonify({'error': str(e)}), 500
        
        # Demo data generation
        @self.app.route('/api/demo/generate', methods=['POST'])
        def generate_demo_data():
            """Generate demo data for testing"""
            try:
                data = request.get_json() or {}
                count = min(data.get('count', 10), 50)  # Limit to 50 cases
                
                rounds = self.data_agent.get_rounds()
                if not rounds:
                    return jsonify({'error': 'No rounds available'}), 400
                
                round_id = rounds[0]['id']
                
                # Generate sample cases
                violation_types = ['PPE Non-compliance', 'One man Lashing', 'Ex.Row Violation', 
                                 '2-Container Distance', 'STA Double-up', 'Spreader Ride']
                
                for i in range(count):
                    case_data = {
                        'case_number': f'DEMO{i+1:04d}',
                        'infringement_type': random.choice(violation_types),
                        'csv_ground_truth': 'Valid' if random.random() > 0.3 else 'Invalid',
                        'cropped_image': f'demo_{i+1}_cropped.jpg',
                        'source_image': f'demo_{i+1}_source.jpg'
                    }
                    
                    # Process through VLM (demo)
                    vlm_result = self.processing_agent.process_case(case_data)
                    case_data.update(vlm_result)
                    
                    # Insert to database
                    self.data_agent.insert_case(round_id, case_data)
                
                return jsonify({'message': f'Generated {count} demo cases', 'round_id': round_id})
            except Exception as e:
                return jsonify({'error': str(e)}), 500
        
        # Original API endpoints
        @self.app.route('/api/rounds', methods=['GET'])
        def get_rounds():
            """Get all processing rounds"""
            rounds = self.data_agent.get_rounds()
            return jsonify(rounds)
        
        @self.app.route('/api/rounds', methods=['POST'])
        def create_round():
            """Create a new round"""
            data = request.get_json()
            round_id = self.data_agent.create_round(
                name=data['name'],
                config=data.get('config', {})
            )
            return jsonify({'round_id': round_id})
        
        @self.app.route('/api/rounds/<round_id>/status')
        def get_round_status(round_id):
            """Get round statistics"""
            status = self.data_agent.get_round_status(round_id)
            return jsonify(status)
        
        @self.app.route('/api/rounds/<round_id>/cases')
        def get_cases(round_id):
            """Get cases for a round"""
            quadrant = request.args.get('quadrant')
            infringement = request.args.get('infringement') 
            limit = int(request.args.get('limit', 50))
            
            cases = self.data_agent.get_cases(
                round_id=round_id,
                quadrant=quadrant,
                infringement=infringement,
                limit=limit
            )
            return jsonify(cases)
        
        @self.app.route('/api/agents/status')
        def get_agent_status():
            """Get agent statuses"""
            statuses = self.data_agent.get_agent_statuses()
            return jsonify(statuses)
        
        @self.app.route('/api/analytics/<round_id>/report')
        def get_analytics_report(round_id):
            """Get analytics report"""
            report = self.analytics_agent.generate_report(round_id)
            return jsonify(report)
        
        @self.app.route('/api/system/info')
        def system_info():
            """Get system information"""
            return jsonify({
                'system': 'VALO AI-FARM Enhanced',
                'version': '2.0.0',
                'database': 'SQLite',
                'status': 'operational',
                'agents': ['data_agent', 'processing_agent', 'analytics_agent', 'web_agent']
            })
    
    def start_dashboard(self):
        """Start the web dashboard"""
        web_config = self.config['web']
        
        logger.info(f"Starting Enhanced VALO dashboard on {web_config['host']}:{web_config['port']}")
        logger.info(f"Access dashboard at: http://localhost:{web_config['port']}")
        
        # Update agent status
        self.data_agent.update_agent_status('web_agent', 'online', f"Enhanced dashboard running on port {web_config['port']}")
        
        self.app.run(
            host=web_config['host'],
            port=web_config['port'],
            debug=False,
            threaded=True
        )

def main():
    """Main entry point"""
    print("🎯 VALO AI-FARM Enhanced System")
    print("=" * 50)
    
    try:
        orchestrator = EnhancedVALOOrchestrator()
        
        # Create a demo round if none exist
        rounds = orchestrator.data_agent.get_rounds()
        if not rounds:
            round_id = orchestrator.data_agent.create_round("Enhanced Demo Round", {"demo": True, "enhanced": True})
            logger.info(f"Created enhanced demo round: {round_id}")
        
        # Start the dashboard
        orchestrator.start_dashboard()
        
    except KeyboardInterrupt:
        logger.info("Shutting down VALO system...")
    except Exception as e:
        logger.error(f"Error: {e}")
        raise

if __name__ == "__main__":
    main()