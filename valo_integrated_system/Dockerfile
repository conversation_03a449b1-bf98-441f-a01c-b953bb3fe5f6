# Multi-stage build for VALO Integrated System
FROM python:3.11-slim AS base

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    postgresql-client \
    libpq-dev \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Copy requirements first for better caching
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY agents/ ./agents/
COPY templates/ ./templates/
COPY static/ ./static/
COPY database/ ./database/
COPY orchestrator.py .

# Create necessary directories
RUN mkdir -p logs data/images

# Set environment variables
ENV PYTHONUNBUFFERED=1
ENV FLASK_APP=orchestrator.py

# Create non-root user
RUN useradd -m -u 1000 valo && chown -R valo:valo /app
USER valo

# Expose web port
EXPOSE 5000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD python -c "import requests; requests.get('http://localhost:5000/api/agents/status')"

# Default command - run the dashboard
CMD ["python", "orchestrator.py", "dashboard"]