#!/usr/bin/env python3
"""
Web Agent: Provides Flask-based web dashboard and review interface
Handles UI, case review, and real-time monitoring
"""

from flask import Flask, render_template, jsonify, request, send_from_directory
from flask_cors import CORS
from flask_socketio import So<PERSON><PERSON>, emit
import asyncio
from datetime import datetime
from typing import Dict, List, Optional
from uuid import UUID
import json
import logging
import os
from functools import wraps

logger = logging.getLogger(__name__)

def async_route(f):
    """Decorator to run async routes"""
    @wraps(f)
    def wrapper(*args, **kwargs):
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            return loop.run_until_complete(f(*args, **kwargs))
        finally:
            loop.close()
    return wrapper

class WebAgent:
    def __init__(self, data_agent, analytics_agent, host='0.0.0.0', port=5000):
        """Initialize web agent with Flask app"""
        self.data_agent = data_agent
        self.analytics_agent = analytics_agent
        self.agent_name = "web_agent"
        self.host = host
        self.port = port
        
        # Create Flask app
        self.app = Flask(__name__, 
                         template_folder='../templates',
                         static_folder='../static')
        self.app.config['SECRET_KEY'] = 'valo-secret-key'
        CORS(self.app)
        self.socketio = SocketIO(self.app, cors_allowed_origins="*", async_mode='threading')
        
        # Setup routes
        self._setup_routes()
        self._setup_socketio()
        
    def _setup_routes(self):
        """Setup all Flask routes"""
        
        @self.app.route('/')
        def index():
            """Main dashboard page"""
            return render_template('dashboard.html')
        
        @self.app.route('/review')
        def review():
            """Case review interface"""
            return render_template('review.html')
        
        @self.app.route('/analytics')
        def analytics():
            """Analytics dashboard"""
            return render_template('analytics.html')
        
        # API Routes
        @self.app.route('/api/rounds', methods=['GET'])
        @async_route
        async def get_rounds():
            """Get all rounds"""
            await self.data_agent.initialize()
            try:
                rounds = await self.data_agent.get_active_rounds()
                # Get all rounds, not just active
                all_rounds = await self.data_agent.pool.fetch(
                    "SELECT * FROM rounds ORDER BY created_at DESC LIMIT 50"
                )
                result = [dict(row) for row in all_rounds]
                # Convert UUIDs to strings
                for r in result:
                    r['id'] = str(r['id'])
                return jsonify(result)
            finally:
                await self.data_agent.close()
        
        @self.app.route('/api/rounds', methods=['POST'])
        @async_route
        async def create_round():
            """Create new round"""
            await self.data_agent.initialize()
            try:
                data = request.json
                name = data.get('name') or f"Round_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                round_id = await self.data_agent.create_round(name)
                return jsonify({'id': str(round_id), 'name': name})
            finally:
                await self.data_agent.close()
        
        @self.app.route('/api/rounds/<round_id>/status', methods=['GET'])
        @async_route
        async def get_round_status(round_id):
            """Get round status and statistics"""
            await self.data_agent.initialize()
            try:
                stats = await self.data_agent.get_round_statistics(UUID(round_id))
                return jsonify(stats)
            finally:
                await self.data_agent.close()
        
        @self.app.route('/api/rounds/<round_id>/cases', methods=['GET'])
        @async_route
        async def get_cases(round_id):
            """Get cases with filtering"""
            await self.data_agent.initialize()
            try:
                # Get query parameters
                quadrant = request.args.get('quadrant')
                infringement = request.args.get('infringement')
                search = request.args.get('search')
                limit = int(request.args.get('limit', 100))
                offset = int(request.args.get('offset', 0))
                
                if quadrant:
                    # Get by quadrant
                    cases = await self.data_agent.get_cases_by_quadrant(
                        UUID(round_id), quadrant, limit, offset
                    )
                elif search or infringement:
                    # Search cases
                    search_params = {}
                    if search:
                        search_params['case_number'] = search
                    if infringement:
                        search_params['infringement_type'] = infringement
                    cases = await self.data_agent.search_cases(UUID(round_id), search_params)
                else:
                    # Get all cases
                    async with self.data_agent.pool.acquire() as conn:
                        rows = await conn.fetch(
                            "SELECT * FROM cases WHERE round_id = $1 ORDER BY created_at DESC LIMIT $2 OFFSET $3",
                            UUID(round_id), limit, offset
                        )
                        cases = [dict(row) for row in rows]
                
                # Convert UUIDs and format response
                for case in cases:
                    case['id'] = str(case['id'])
                    case['round_id'] = str(case['round_id'])
                    if case.get('vlm_response') and isinstance(case['vlm_response'], str):
                        case['vlm_response'] = json.loads(case['vlm_response'])
                
                return jsonify(cases)
            finally:
                await self.data_agent.close()
        
        @self.app.route('/api/cases/<case_id>', methods=['GET'])
        @async_route
        async def get_case_detail(case_id):
            """Get detailed case information"""
            await self.data_agent.initialize()
            try:
                async with self.data_agent.pool.acquire() as conn:
                    case = await conn.fetchrow(
                        "SELECT * FROM cases WHERE id = $1",
                        UUID(case_id)
                    )
                    if case:
                        result = dict(case)
                        result['id'] = str(result['id'])
                        result['round_id'] = str(result['round_id'])
                        if result.get('vlm_response') and isinstance(result['vlm_response'], str):
                            result['vlm_response'] = json.loads(result['vlm_response'])
                        return jsonify(result)
                    return jsonify({'error': 'Case not found'}), 404
            finally:
                await self.data_agent.close()
        
        @self.app.route('/api/cases/<case_id>/review', methods=['POST'])
        @async_route
        async def update_review(case_id):
            """Update human review for a case"""
            await self.data_agent.initialize()
            try:
                data = request.json
                await self.data_agent.update_human_review(
                    UUID(case_id),
                    data['reviewer'],
                    data['status'],
                    data.get('notes')
                )
                return jsonify({'success': True})
            finally:
                await self.data_agent.close()
        
        @self.app.route('/api/analytics/<round_id>/<viz_type>', methods=['GET'])
        @async_route
        async def get_visualization(round_id, viz_type):
            """Get visualization image"""
            await self.data_agent.initialize()
            try:
                image_base64 = await self.analytics_agent.generate_visualization(
                    UUID(round_id), viz_type
                )
                return jsonify({'image': image_base64})
            finally:
                await self.data_agent.close()
        
        @self.app.route('/api/analytics/<round_id>/report', methods=['GET'])
        @async_route
        async def get_analytics_report(round_id):
            """Get comprehensive analytics report"""
            await self.data_agent.initialize()
            try:
                report = await self.analytics_agent.generate_report(UUID(round_id))
                return jsonify(report)
            finally:
                await self.data_agent.close()
        
        @self.app.route('/api/agents/status', methods=['GET'])
        @async_route
        async def get_agent_status():
            """Get status of all agents"""
            await self.data_agent.initialize()
            try:
                statuses = await self.data_agent.get_agent_statuses()
                return jsonify(statuses)
            finally:
                await self.data_agent.close()
        
        @self.app.route('/images/<path:filename>')
        def serve_image(filename):
            """Serve case images"""
            # Security check
            if '..' in filename or filename.startswith('/'):
                return "Invalid path", 403
            
            # Try different image directories
            image_dirs = [
                'ai_farm_images_fixed_250703/ai_farm_images_fixed',
                'data/images',
                'valo_comprehensive_data'
            ]
            
            for image_dir in image_dirs:
                full_path = os.path.join(image_dir, filename)
                if os.path.exists(full_path):
                    return send_from_directory(image_dir, filename)
            
            return "Image not found", 404
    
    def _setup_socketio(self):
        """Setup Socket.IO events for real-time updates"""
        
        @self.socketio.on('connect')
        def handle_connect():
            logger.info(f"Client connected: {request.sid}")
            emit('connected', {'data': 'Connected to VALO system'})
        
        @self.socketio.on('disconnect')
        def handle_disconnect():
            logger.info(f"Client disconnected: {request.sid}")
        
        @self.socketio.on('subscribe_round')
        def handle_subscribe(data):
            """Subscribe to round updates"""
            round_id = data.get('round_id')
            if round_id:
                # In production, would join a room for this round
                logger.info(f"Client {request.sid} subscribed to round {round_id}")
    
    async def initialize(self):
        """Initialize web agent"""
        await self.data_agent.update_agent_status(self.agent_name, 'online')
        logger.info("Web Agent initialized")
    
    def run(self):
        """Run the Flask application"""
        logger.info(f"Starting web server on {self.host}:{self.port}")
        self.socketio.run(self.app, host=self.host, port=self.port, debug=False)
    
    async def broadcast_update(self, round_id: str, update_type: str, data: Dict):
        """Broadcast real-time updates to connected clients"""
        self.socketio.emit('round_update', {
            'round_id': round_id,
            'type': update_type,
            'data': data,
            'timestamp': datetime.now().isoformat()
        }, broadcast=True)