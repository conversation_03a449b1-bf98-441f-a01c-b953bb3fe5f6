#!/usr/bin/env python3
"""
Processing Agent: Handles VLM API interactions and case processing
Processes cases asynchronously with retry logic and token counting
"""

import asyncio
import aiohttp
import base64
import json
import tiktoken
from datetime import datetime
from typing import Dict, List, Optional, Any
from uuid import UUID
import logging
import re
import os
from pathlib import Path

logger = logging.getLogger(__name__)

class ProcessingAgent:
    def __init__(self, data_agent, vlm_config: Dict[str, Any]):
        """Initialize processing agent"""
        self.data_agent = data_agent
        self.vlm_url = vlm_config.get('url', 'http://100.106.127.35:9500/v1/chat/completions')
        self.vlm_model = vlm_config.get('model', 'VLM-38B-AWQ')
        self.temperature = vlm_config.get('temperature', 0.1)
        self.max_tokens = vlm_config.get('max_tokens', 600)
        self.agent_name = "processing_agent"
        self.encoder = tiktoken.encoding_for_model("gpt-4")  # For token counting
        self.session = None
        self.processing_queue = asyncio.Queue()
        self.workers = []
        
    async def initialize(self, num_workers: int = 3):
        """Initialize the processing agent with worker tasks"""
        self.session = aiohttp.ClientSession()
        await self.data_agent.update_agent_status(self.agent_name, 'online')
        
        # Start worker tasks
        for i in range(num_workers):
            worker = asyncio.create_task(self._process_worker(i))
            self.workers.append(worker)
        
        logger.info(f"Processing Agent initialized with {num_workers} workers")
    
    async def close(self):
        """Cleanup resources"""
        # Cancel all workers
        for worker in self.workers:
            worker.cancel()
        
        # Wait for workers to finish
        await asyncio.gather(*self.workers, return_exceptions=True)
        
        if self.session:
            await self.session.close()
        
        await self.data_agent.update_agent_status(self.agent_name, 'offline')
    
    async def _process_worker(self, worker_id: int):
        """Worker task that processes cases from the queue"""
        logger.info(f"Worker {worker_id} started")
        
        while True:
            try:
                # Get task from queue
                task = await self.processing_queue.get()
                round_id = task['round_id']
                case_data = task['case_data']
                
                # Update agent status
                await self.data_agent.update_agent_status(
                    self.agent_name, 
                    'processing',
                    {'worker_id': worker_id, 'case': case_data['case_number']}
                )
                
                # Process the case
                result = await self._process_single_case(round_id, case_data)
                
                # Mark task as done
                self.processing_queue.task_done()
                
            except asyncio.CancelledError:
                logger.info(f"Worker {worker_id} cancelled")
                break
            except Exception as e:
                logger.error(f"Worker {worker_id} error: {str(e)}")
                await asyncio.sleep(1)
    
    async def process_cases(self, round_id: UUID, cases: List[Dict]):
        """Queue cases for processing"""
        for case_data in cases:
            await self.processing_queue.put({
                'round_id': round_id,
                'case_data': case_data
            })
        
        logger.info(f"Queued {len(cases)} cases for processing")
    
    async def _process_single_case(self, round_id: UUID, case_data: Dict) -> Dict:
        """Process a single case with VLM"""
        start_time = datetime.now()
        case_number = case_data['case_number']
        
        try:
            # Log start
            await self.data_agent.log(
                round_id, case_number, self.agent_name,
                'INFO', 'Starting case processing'
            )
            
            # Prepare images
            images_data = await self._prepare_images(case_data)
            if not images_data['cropped']:
                raise ValueError(f"Cropped image not found for {case_number}")
            
            # Create VLM query
            query = self._create_enhanced_query(case_data)
            
            # Count tokens in query
            query_tokens = self._count_tokens(query)
            
            # Call VLM API with retry
            vlm_response = await self._call_vlm_with_retry(
                query, images_data, max_retries=3
            )
            
            # Parse response
            parsed_results = self._parse_vlm_response(vlm_response)
            
            # Count response tokens
            response_tokens = self._count_tokens(vlm_response)
            total_tokens = query_tokens + response_tokens
            
            # Determine VLM decision
            vlm_decision = 'filtered' if parsed_results['fp_likelihood'] >= 70 else 'retained'
            
            # Calculate processing time
            processing_time = (datetime.now() - start_time).total_seconds()
            
            # Prepare case update
            case_update = {
                'case_number': case_number,
                'vlm_query': query,
                'vlm_response': {'raw': vlm_response, 'parsed': parsed_results},
                'vlm_tokens_used': total_tokens,
                'vlm_decision': vlm_decision,
                'processing_time': processing_time,
                **parsed_results
            }
            
            # Save to database
            await self.data_agent.insert_case(round_id, case_update)
            
            # Log success
            await self.data_agent.log(
                round_id, case_number, self.agent_name,
                'INFO', 'Case processed successfully',
                {'tokens': total_tokens, 'decision': vlm_decision, 'time': processing_time}
            )
            
            return case_update
            
        except Exception as e:
            # Log error
            await self.data_agent.log(
                round_id, case_number, self.agent_name,
                'ERROR', f'Processing failed: {str(e)}'
            )
            
            # Update case with error
            await self.data_agent.insert_case(round_id, {
                'case_number': case_number,
                'error_details': {'error': str(e), 'timestamp': datetime.now().isoformat()}
            })
            
            raise
    
    async def _prepare_images(self, case_data: Dict) -> Dict[str, str]:
        """Load and encode images"""
        result = {'source': None, 'cropped': None}
        
        # Get image paths
        cropped_path = case_data.get('cropped_image', '')
        source_path = case_data.get('source_image', '')
        
        # Try to load cropped image
        if cropped_path and os.path.exists(cropped_path):
            with open(cropped_path, 'rb') as f:
                result['cropped'] = base64.b64encode(f.read()).decode('utf-8')
        
        # Try to load source image
        if source_path and os.path.exists(source_path):
            with open(source_path, 'rb') as f:
                result['source'] = base64.b64encode(f.read()).decode('utf-8')
        
        return result
    
    def _create_enhanced_query(self, case_data: Dict) -> str:
        """Create the enhanced VLM query with all context"""
        infringement_type = case_data.get('infringement_type', 'Unknown')
        
        return f"""Analyze this safety alert image in extreme detail. The reported violation type is: {infringement_type}

1. MAIN SUBJECT IDENTIFICATION
   - Primary subject type: Person/Vessel Structure/Crane Structure/Spreader Structure/PM Structure/Others/Equipment
   - If Person: Number of individuals, gender if visible, apparent role
   - If Vessel Structure: Ship deck, cargo holds, vessel railings, bulkheads, hatch covers
   - If Crane Structure: Ship-to-shore cranes, gantry cranes, mobile cranes, crane booms, jib arms, crane cabins
   - If Spreader Structure: Container spreaders, twist locks, spreader bars, lifting attachments, hydraulic components
   - If PM Structure: Prime mover equipment, terminal tractors, yard trucks, chassis handling equipment
   - If Others/Equipment: Winches, cable drums, wire ropes, slings, sacks/bags, bulk handling equipment, machinery, vehicles, tools

2. PERSON DETAILS (if any visible)
   - Exact position and posture
   - Clothing description (colors, type, condition)
   - Safety equipment worn:
     * Head protection (helmet/hard hat) - color, style, properly worn?
     * High-visibility clothing - color, reflective strips visible?

3. ACTIVITY ANALYSIS
   - What specific action is being performed?
   - Tools or equipment being used
   - Body mechanics and positioning

4. ENVIRONMENT AND CONTEXT
   - Exact location (vessel deck/quay/yard/height/confined space)
   - Surrounding equipment and structures
   - Weather/lighting conditions
   - Potential hazards in vicinity

5. SAFETY ASSESSMENT
   - Primary safety concern visible
   - Violation type if apparent
   - Severity estimation

Provide the most detailed, factual description possible.

---

After completing the above detailed description, now analyze the same image for accuracy and confidence assessment:

6. DESCRIPTION ACCURACY (0-100%):
   How accurately does the above description match what you see in the image?

7. SUBJECT CONFIRMATION:
   - Is there definitely a PERSON in this image? YES/NO
   - If NO, what is the main subject?
   - Confidence in subject identification: (0-100%)

8. PPE COMPLIANCE CHECK (if person present):
   - Helmet/Hard hat present? YES/NO/PARTIAL/NA
   - Helmet confidence: (0-100%)
   - High-visibility vest present? YES/NO/PARTIAL/NA
   - Vest confidence: (0-100%)
   - Overall PPE compliance: COMPLETE/INCOMPLETE/NONE/NA
   - Overall PPE confidence: (0-100%)

9. SAFETY VIOLATION ASSESSMENT:
   - Is there a genuine safety violation visible? YES/NO/UNCERTAIN
   - If YES, describe the specific violation
   - Violation severity: MINOR/MODERATE/SEVERE/NA
   - Violation confidence: (0-100%)

10. FALSE POSITIVE DETECTION:
    Apply these core rules for "{infringement_type}" violations:
    - If NO person detected → likely False Positive (60-80%)
    - If ANY structure/others/equipment detected → likely False Positive (60-80%)
    - If NO person detected + ANY structure/others/equipment detected → high False Positive (80-95%)
    - If person detected with helmet/hardhat + vest BUT flagged for "PPE Non-compliance" → False Positive (85-95%)
    - List any additional factors suggesting false positive
    - Final false positive likelihood: (0-100%)

Format your final assessment EXACTLY as:

DESCRIPTION_ACCURACY: [X]%
PERSON_PRESENT: [YES/NO]
MAIN_SUBJECT: [Person/Vessel Structure/Crane Structure/Spreader Structure/PM Structure/Others/Equipment]
SUBJECT_CONFIDENCE: [X]%
HELMET_STATUS: [YES/NO/PARTIAL/NA]
HELMET_CONFIDENCE: [X]%
VEST_STATUS: [YES/NO/PARTIAL/NA]
VEST_CONFIDENCE: [X]%
PPE_COMPLIANCE: [COMPLETE/INCOMPLETE/NONE/NA]
PPE_CONFIDENCE: [X]%
SAFETY_VIOLATION: [YES/NO/UNCERTAIN]
VIOLATION_DESCRIPTION: [Description or NONE]
VIOLATION_SEVERITY: [MINOR/MODERATE/SEVERE/NA]
VIOLATION_CONFIDENCE: [X]%
FALSE_POSITIVE_LIKELIHOOD: [X]%
FP_INDICATORS: [List or NONE]"""
    
    async def _call_vlm_with_retry(self, query: str, images: Dict[str, str], 
                                  max_retries: int = 3) -> str:
        """Call VLM API with retry logic"""
        content = [
            {"type": "text", "text": query},
            {"type": "text", "text": "\nANALYZING CROPPED ALERT IMAGE:"},
            {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{images['cropped']}"}}
        ]
        
        if images.get('source'):
            content.extend([
                {"type": "text", "text": "\nFULL CONTEXT IMAGE FOR REFERENCE:"},
                {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{images['source']}"}}
            ])
        
        payload = {
            "model": self.vlm_model,
            "messages": [{"role": "user", "content": content}],
            "temperature": self.temperature,
            "max_tokens": self.max_tokens
        }
        
        for attempt in range(max_retries):
            try:
                async with self.session.post(
                    self.vlm_url,
                    json=payload,
                    timeout=aiohttp.ClientTimeout(total=90)
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        return result['choices'][0]['message']['content']
                    else:
                        error_text = await response.text()
                        raise Exception(f"VLM API error {response.status}: {error_text}")
                        
            except asyncio.TimeoutError:
                if attempt < max_retries - 1:
                    wait_time = 2 ** attempt
                    logger.warning(f"VLM timeout, retrying in {wait_time}s...")
                    await asyncio.sleep(wait_time)
                else:
                    raise
            except Exception as e:
                if attempt < max_retries - 1:
                    wait_time = 2 ** attempt
                    logger.warning(f"VLM error: {str(e)}, retrying in {wait_time}s...")
                    await asyncio.sleep(wait_time)
                else:
                    raise
    
    def _parse_vlm_response(self, response: str) -> Dict:
        """Parse the structured VLM response"""
        parsed = {
            'person_present': False,
            'main_subject': 'Unknown',
            'subject_confidence': 0,
            'helmet_status': 'NA',
            'vest_status': 'NA',
            'ppe_compliance': 'NA',
            'safety_violation': 'UNCERTAIN',
            'violation_description': None,
            'violation_severity': 'NA',
            'fp_likelihood': 50,
            'fp_indicators': []
        }
        
        patterns = {
            'person_present': r'PERSON_PRESENT:\s*(YES|NO)',
            'main_subject': r'MAIN_SUBJECT:\s*([^\n]+)',
            'subject_confidence': r'SUBJECT_CONFIDENCE:\s*(\d+)%',
            'helmet_status': r'HELMET_STATUS:\s*(\w+)',
            'vest_status': r'VEST_STATUS:\s*(\w+)',
            'ppe_compliance': r'PPE_COMPLIANCE:\s*(\w+)',
            'safety_violation': r'SAFETY_VIOLATION:\s*(YES|NO|UNCERTAIN)',
            'violation_description': r'VIOLATION_DESCRIPTION:\s*([^\n]+)',
            'violation_severity': r'VIOLATION_SEVERITY:\s*(\w+)',
            'fp_likelihood': r'FALSE_POSITIVE_LIKELIHOOD:\s*(\d+)%',
            'fp_indicators': r'FP_INDICATORS:\s*([^\n]+)'
        }
        
        for field, pattern in patterns.items():
            match = re.search(pattern, response, re.IGNORECASE)
            if match:
                value = match.group(1).strip()
                if field in ['subject_confidence', 'fp_likelihood']:
                    parsed[field] = float(value)
                elif field == 'person_present':
                    parsed[field] = value.upper() == 'YES'
                elif field == 'fp_indicators':
                    indicators = [i.strip() for i in value.split(',') if i.strip() and i.strip() != 'NONE']
                    parsed[field] = indicators
                else:
                    parsed[field] = value if value != 'NONE' else None
        
        return parsed
    
    def _count_tokens(self, text: str) -> int:
        """Count tokens in text using tiktoken"""
        try:
            return len(self.encoder.encode(text))
        except:
            # Fallback to rough estimation
            return len(text) // 4