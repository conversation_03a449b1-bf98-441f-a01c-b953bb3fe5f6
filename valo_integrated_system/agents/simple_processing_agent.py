"""
Simple Processing Agent for VALO System
Handles VLM API calls and case processing
"""
import asyncio
import aiohttp
import json
import time
import logging
from typing import Dict, Any, Optional, List
import threading

class SimpleProcessingAgent:
    def __init__(self, config: Dict, data_agent):
        self.config = config
        self.data_agent = data_agent
        self.is_running = False
        self.is_paused = False
        self.current_round = None
        self.processed_count = 0
        self.total_cases = 0
        self.logger = logging.getLogger('processing_agent')
        self._thread = None
        
    def start(self, csv_file_path: str = None):
        """Start processing all cases in a new round"""
        if not self.is_running:
            self.is_running = True
            self.is_paused = False
            
            # Create new round
            timestamp = time.strftime('%Y-%m-%d_%H-%M-%S')
            round_name = f"Processing Round {timestamp}"
            self.current_round = self.data_agent.create_round(round_name, {"timestamp": timestamp, "csv_file": csv_file_path})
            
            # Start processing thread
            self._thread = threading.Thread(target=self._process_all_cases, daemon=True)
            self._thread.start()
            
            self.data_agent.update_agent_status('processing_agent', 'running', f'Processing round {self.current_round}')
            self.logger.info(f"Started processing round: {self.current_round}")
    
    def pause(self):
        """Pause processing"""
        self.is_paused = True
        self.data_agent.update_agent_status('processing_agent', 'paused', f'Round {self.current_round} paused')
        self.logger.info("Processing paused")
    
    def resume(self):
        """Resume processing"""
        self.is_paused = False
        self.data_agent.update_agent_status('processing_agent', 'running', f'Round {self.current_round} resumed')
        self.logger.info("Processing resumed")
    
    def stop(self):
        """Stop processing agent"""
        self.is_running = False
        self.is_paused = False
        if self.current_round:
            self.data_agent.update_agent_status('processing_agent', 'stopped', f'Round {self.current_round} stopped')
        else:
            self.data_agent.update_agent_status('processing_agent', 'offline', 'Processing agent stopped')
        self.logger.info("Processing agent stopped")
    
    def _process_all_cases(self):
        """Process all cases from CSV data"""
        try:
            # Load CSV data - check for existing test data first
            csv_data = self._load_csv_data()
            if not csv_data:
                self.logger.warning("No CSV data found, generating demo data")
                csv_data = self._generate_demo_cases(50)  # Generate 50 demo cases
            
            self.total_cases = len(csv_data)
            self.processed_count = 0
            
            self.logger.info(f"Starting to process {self.total_cases} cases in round {self.current_round}")
            
            for i, case_data in enumerate(csv_data):
                # Check if we should stop
                if not self.is_running:
                    break
                
                # Wait while paused
                while self.is_paused and self.is_running:
                    time.sleep(1)
                
                if not self.is_running:
                    break
                
                # Process case
                case_result = asyncio.run(self.process_case(case_data))
                case_data.update(case_result)
                
                # Save to database
                self.data_agent.insert_case(self.current_round, case_data)
                
                self.processed_count += 1
                progress = (self.processed_count / self.total_cases) * 100
                
                # Update status every 10 cases
                if self.processed_count % 10 == 0:
                    status_msg = f"Processing: {self.processed_count}/{self.total_cases} ({progress:.1f}%)"
                    self.data_agent.update_agent_status('processing_agent', 'running', status_msg)
                    self.logger.info(status_msg)
                
                # Small delay between cases
                time.sleep(0.1)
            
            # Processing completed
            if self.is_running:
                final_msg = f"Round {self.current_round} completed: {self.processed_count}/{self.total_cases} cases processed"
                self.data_agent.update_agent_status('processing_agent', 'completed', final_msg)
                self.logger.info(final_msg)
            
        except Exception as e:
            error_msg = f"Error processing cases: {str(e)}"
            self.logger.error(error_msg)
            self.data_agent.update_agent_status('processing_agent', 'error', error_msg)
        finally:
            self.is_running = False
    
    async def process_case(self, case_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process a single case through VLM"""
        start_time = time.time()
        
        try:
            # Mock VLM processing for demo
            vlm_response = {
                'decision': 'No violation detected',
                'confidence': 85.0,
                'reasoning': 'Person wearing proper PPE, no safety violations observed',
                'person_present': True,
                'person_confidence': 90.0,
                'main_subject': 'Worker with safety equipment',
                'structure_detected': False
            }
            
            # Calculate processing metrics
            processing_time = time.time() - start_time
            tokens_used = len(str(vlm_response)) // 4  # Rough token estimate
            
            result = {
                'vlm_decision': vlm_response['decision'],
                'vlm_response': json.dumps(vlm_response),
                'fp_likelihood': 100 - vlm_response['confidence'],
                'person_present': vlm_response['person_present'],
                'person_confidence': vlm_response['person_confidence'],
                'main_subject': vlm_response['main_subject'],
                'processing_time': processing_time,
                'tokens_used': tokens_used,
                'violation_detected': 'violation' in vlm_response['decision'].lower(),
                'violation_confidence': vlm_response['confidence']
            }
            
            self.logger.info(f"Processed case {case_data.get('case_number', 'unknown')} in {processing_time:.2f}s")
            return result
            
        except Exception as e:
            self.logger.error(f"Error processing case: {e}")
            return {
                'vlm_decision': 'Error',
                'vlm_response': f'Processing failed: {str(e)}',
                'fp_likelihood': 0,
                'processing_time': time.time() - start_time,
                'tokens_used': 0
            }
    
    def _load_csv_data(self) -> List[Dict[str, Any]]:
        """Load cases from CSV file or existing data"""
        import csv
        import os
        
        # Check for existing CSV files
        csv_files = [
            '../test_batch_data.csv',
            '../backend/test_batch_data.csv', 
            './test_batch_data.csv',
            '../ai_farm_images_fixed_250703/psa_valo_violation_report_without_images_csv_2025-07-03_14-14-01.CSV'
        ]
        
        for csv_file in csv_files:
            if os.path.exists(csv_file):
                try:
                    cases = []
                    with open(csv_file, 'r', encoding='utf-8') as f:
                        reader = csv.DictReader(f)
                        for row in reader:
                            cases.append({
                                'case_number': row.get('case_number', row.get('Case_number', f'CASE_{len(cases)+1:04d}')),
                                'infringement_type': row.get('infringement_type', row.get('Infringement_type', 'Safety Violation')),
                                'csv_ground_truth': row.get('key', row.get('Key', 'Valid')).replace('invalid', 'Invalid').replace('valid', 'Valid'),
                                'cropped_image': row.get('url', row.get('URL', f'image_{len(cases)+1}.jpg')),
                                'source_image': row.get('source_image', f'source_{len(cases)+1}.jpg')
                            })
                    self.logger.info(f"Loaded {len(cases)} cases from {csv_file}")
                    return cases
                except Exception as e:
                    self.logger.error(f"Error loading {csv_file}: {e}")
                    continue
        
        return []
    
    def _generate_demo_cases(self, count: int = 50) -> List[Dict[str, Any]]:
        """Generate demo cases for testing"""
        import random
        
        violation_types = [
            'PPE Non-compliance', 'One man Lashing', 'Ex.Row Violation', 
            '2-Container Distance', 'STA Double-up', 'Spreader Ride',
            'Safety Zone Violation', 'Equipment Misuse', 'Procedure Violation'
        ]
        
        cases = []
        for i in range(count):
            cases.append({
                'case_number': f'DEMO{i+1:04d}',
                'infringement_type': random.choice(violation_types),
                'csv_ground_truth': 'Valid' if random.random() > 0.3 else 'Invalid',
                'cropped_image': f'demo_{i+1}_cropped.jpg',
                'source_image': f'demo_{i+1}_source.jpg'
            })
        
        return cases
    
    def get_processing_status(self) -> Dict[str, Any]:
        """Get current processing status"""
        return {
            'is_running': self.is_running,
            'is_paused': self.is_paused,
            'current_round': self.current_round,
            'processed_count': self.processed_count,
            'total_cases': self.total_cases,
            'progress_percent': (self.processed_count / self.total_cases * 100) if self.total_cases > 0 else 0
        }