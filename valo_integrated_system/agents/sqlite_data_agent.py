"""
SQLite Data Agent for VALO System
Simplified version using SQLite instead of PostgreSQL
"""
import sqlite3
import json
import asyncio
from datetime import datetime
from typing import Dict, List, Optional, Any
import uuid
import logging

class SQLiteDataAgent:
    def __init__(self, db_path: str = './valo_system.db'):
        self.db_path = db_path
        self.logger = logging.getLogger('data_agent')
        
    def get_connection(self):
        """Get SQLite connection"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row  # Enable dict-like access
        return conn
    
    def create_round(self, name: str, config: Dict = None) -> str:
        """Create a new processing round"""
        round_id = str(uuid.uuid4())
        conn = self.get_connection()
        
        try:
            cursor = conn.cursor()
            cursor.execute("""
                INSERT INTO rounds (id, name, config)
                VALUES (?, ?, ?)
            """, (round_id, name, json.dumps(config) if config else None))
            conn.commit()
            
            self.logger.info(f"Created round {round_id}: {name}")
            return round_id
        finally:
            conn.close()
    
    def insert_case(self, round_id: str, case_data: Dict[str, Any]) -> str:
        """Insert or update a case"""
        case_id = str(uuid.uuid4())
        conn = self.get_connection()
        
        try:
            # Calculate quadrant
            csv_ground_truth = case_data.get('csv_ground_truth', '').lower()
            vlm_decision = case_data.get('vlm_decision', '').lower()
            
            is_valid = csv_ground_truth != 'invalid'
            vlm_passed = 'invalid' not in vlm_decision and 'no violation' not in vlm_decision
            
            if is_valid and vlm_passed:
                quadrant = 'valid_passed'
            elif is_valid and not vlm_passed:
                quadrant = 'valid_failed'
            elif not is_valid and vlm_passed:
                quadrant = 'invalid_passed'
            else:
                quadrant = 'invalid_failed'
            
            cursor = conn.cursor()
            cursor.execute("""
                INSERT INTO cases (
                    id, round_id, case_number, infringement_type, csv_ground_truth,
                    vlm_decision, vlm_response, fp_likelihood, quadrant,
                    cropped_image, source_image, person_present, person_confidence,
                    main_subject, subject_confidence, violation_detected, violation_confidence,
                    structure_type, structure_confidence, processing_time, tokens_used
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                case_id, round_id, case_data.get('case_number'),
                case_data.get('infringement_type'), case_data.get('csv_ground_truth'),
                case_data.get('vlm_decision'), case_data.get('vlm_response'),
                case_data.get('fp_likelihood', 0), quadrant,
                case_data.get('cropped_image'), case_data.get('source_image'),
                1 if case_data.get('person_present') else 0, case_data.get('person_confidence', 0),
                case_data.get('main_subject'), case_data.get('subject_confidence', 0),
                1 if case_data.get('violation_detected') else 0, case_data.get('violation_confidence', 0),
                case_data.get('structure_type'), case_data.get('structure_confidence', 0),
                case_data.get('processing_time', 0), case_data.get('tokens_used', 0)
            ))
            conn.commit()
            
            return case_id
        finally:
            conn.close()
    
    def get_rounds(self) -> List[Dict]:
        """Get all processing rounds"""
        conn = self.get_connection()
        try:
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM rounds ORDER BY created_at DESC")
            rounds = []
            for row in cursor.fetchall():
                round_dict = dict(row)
                if round_dict['config']:
                    round_dict['config'] = json.loads(round_dict['config'])
                rounds.append(round_dict)
            return rounds
        finally:
            conn.close()
    
    def get_round_status(self, round_id: str) -> Dict:
        """Get round statistics"""
        conn = self.get_connection()
        try:
            cursor = conn.cursor()
            
            # Get basic round info
            cursor.execute("SELECT * FROM rounds WHERE id = ?", (round_id,))
            round_info = cursor.fetchone()
            if not round_info:
                return {}
            
            # Get case statistics
            cursor.execute("""
                SELECT 
                    COUNT(*) as total_cases,
                    COUNT(CASE WHEN quadrant = 'valid_passed' THEN 1 END) as valid_passed,
                    COUNT(CASE WHEN quadrant = 'valid_failed' THEN 1 END) as valid_failed,
                    COUNT(CASE WHEN quadrant = 'invalid_passed' THEN 1 END) as invalid_passed,
                    COUNT(CASE WHEN quadrant = 'invalid_failed' THEN 1 END) as invalid_failed,
                    AVG(fp_likelihood) as avg_fp_likelihood,
                    AVG(processing_time) as avg_processing_time
                FROM cases WHERE round_id = ?
            """, (round_id,))
            
            stats = dict(cursor.fetchone())
            
            # Calculate metrics
            total_invalid = stats['invalid_passed'] + stats['invalid_failed']
            fp_detected = stats['invalid_failed']
            protection_rate = (stats['valid_passed'] / (stats['valid_passed'] + stats['valid_failed'])) * 100 if (stats['valid_passed'] + stats['valid_failed']) > 0 else 0
            
            return {
                'round_info': dict(round_info),
                'total_cases': stats['total_cases'],
                'valid_passed': stats['valid_passed'],
                'valid_failed': stats['valid_failed'], 
                'invalid_passed': stats['invalid_passed'],
                'invalid_failed': stats['invalid_failed'],
                'avg_fp_likelihood': stats['avg_fp_likelihood'] or 0,
                'avg_processing_time': stats['avg_processing_time'] or 0,
                'protection_rate': protection_rate,
                'fp_detection_rate': (fp_detected / total_invalid) * 100 if total_invalid > 0 else 0
            }
        finally:
            conn.close()
    
    def get_cases(self, round_id: str, quadrant: Optional[str] = None, 
                  infringement: Optional[str] = None, limit: int = 50) -> List[Dict]:
        """Get cases with optional filtering"""
        conn = self.get_connection()
        try:
            query = "SELECT * FROM cases WHERE round_id = ?"
            params = [round_id]
            
            if quadrant:
                query += " AND quadrant = ?"
                params.append(quadrant)
            
            if infringement:
                query += " AND infringement_type = ?"
                params.append(infringement)
            
            query += " ORDER BY created_at DESC LIMIT ?"
            params.append(limit)
            
            cursor = conn.cursor()
            cursor.execute(query, params)
            
            cases = []
            for row in cursor.fetchall():
                cases.append(dict(row))
            return cases
        finally:
            conn.close()
    
    def update_agent_status(self, agent_name: str, status: str, details: str = None):
        """Update agent status"""
        conn = self.get_connection()
        try:
            cursor = conn.cursor()
            cursor.execute("""
                INSERT OR REPLACE INTO agent_status (agent_name, status, last_heartbeat, details)
                VALUES (?, ?, CURRENT_TIMESTAMP, ?)
            """, (agent_name, status, details))
            conn.commit()
        finally:
            conn.close()
    
    def get_agent_statuses(self) -> List[Dict]:
        """Get all agent statuses"""
        conn = self.get_connection()
        try:
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM agent_status")
            return [dict(row) for row in cursor.fetchall()]
        finally:
            conn.close()