"""
Simple Analytics Agent for VALO System
Generates insights and visualizations
"""
import time
import logging
import threading
from typing import Dict, Any
import json

class SimpleAnalyticsAgent:
    def __init__(self, data_agent):
        self.data_agent = data_agent
        self.is_running = False
        self.logger = logging.getLogger('analytics_agent')
        self._thread = None
        
    def start(self):
        """Start the analytics agent"""
        if not self.is_running:
            self.is_running = True
            self._thread = threading.Thread(target=self._run_loop, daemon=True)
            self._thread.start()
            self.data_agent.update_agent_status('analytics_agent', 'online', 'Analytics agent started')
            self.logger.info("Analytics agent started")
    
    def stop(self):
        """Stop the analytics agent"""
        self.is_running = False
        self.data_agent.update_agent_status('analytics_agent', 'offline', 'Analytics agent stopped')
        self.logger.info("Analytics agent stopped")
    
    def _run_loop(self):
        """Main analytics loop"""
        while self.is_running:
            try:
                # Simulate analytics processing
                time.sleep(10)
                if self.is_running:
                    self.data_agent.update_agent_status('analytics_agent', 'online', 'Analytics agent idle')
            except Exception as e:
                self.logger.error(f"Error in analytics loop: {e}")
                time.sleep(10)
    
    def generate_report(self, round_id: str) -> Dict[str, Any]:
        """Generate analytics report for a round"""
        try:
            status = self.data_agent.get_round_status(round_id)
            
            # Generate insights based on data
            insights = []
            
            if status.get('total_cases', 0) == 0:
                insights.append("📊 No cases processed yet. Upload data to begin analysis.")
            else:
                fp_rate = status.get('fp_detection_rate', 0)
                if fp_rate >= 70:
                    insights.append(f"✅ Excellent FP detection rate: {fp_rate:.1f}% (Target: 70%)")
                elif fp_rate >= 50:
                    insights.append(f"⚠️ Good FP detection rate: {fp_rate:.1f}% (Target: 70%)")
                else:
                    insights.append(f"❌ FP detection rate below target: {fp_rate:.1f}% (Target: 70%)")
                
                protection_rate = status.get('protection_rate', 0)
                if protection_rate >= 95:
                    insights.append(f"✅ Excellent valid case protection: {protection_rate:.1f}%")
                else:
                    insights.append(f"⚠️ Valid case protection needs improvement: {protection_rate:.1f}%")
            
            report = {
                'round_id': round_id,
                'generated_at': time.strftime('%Y-%m-%d %H:%M:%S'),
                'summary': status,
                'insights': insights,
                'recommendations': [
                    "Continue processing more cases for better accuracy",
                    "Monitor false positive patterns",
                    "Adjust confidence thresholds if needed"
                ]
            }
            
            self.logger.info(f"Generated analytics report for round {round_id}")
            return report
            
        except Exception as e:
            self.logger.error(f"Error generating report: {e}")
            return {
                'error': str(e),
                'generated_at': time.strftime('%Y-%m-%d %H:%M:%S')
            }