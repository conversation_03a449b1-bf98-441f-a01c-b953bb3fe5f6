#!/usr/bin/env python3
"""
Analytics Agent: Generates insights, visualizations, and reports
Handles real-time analytics computation and caching
"""

import asyncio
import json
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, List, Optional, Any, Tuple
from uuid import UUID
import logging
from io import BytesIO
import base64

logger = logging.getLogger(__name__)

class AnalyticsAgent:
    def __init__(self, data_agent):
        """Initialize analytics agent"""
        self.data_agent = data_agent
        self.agent_name = "analytics_agent"
        self.update_interval = 60  # Update analytics every 60 seconds
        self.running = False
        self.update_task = None
        
        # Configure matplotlib for better visuals
        plt.style.use('dark_background')
        sns.set_palette("husl")
        
    async def initialize(self):
        """Start the analytics agent"""
        await self.data_agent.update_agent_status(self.agent_name, 'online')
        self.running = True
        self.update_task = asyncio.create_task(self._periodic_update())
        logger.info("Analytics Agent initialized")
    
    async def close(self):
        """Stop the analytics agent"""
        self.running = False
        if self.update_task:
            self.update_task.cancel()
            await asyncio.gather(self.update_task, return_exceptions=True)
        await self.data_agent.update_agent_status(self.agent_name, 'offline')
    
    async def _periodic_update(self):
        """Periodically update analytics for active rounds"""
        while self.running:
            try:
                # Get active rounds
                active_rounds = await self.data_agent.get_active_rounds()
                
                for round_data in active_rounds:
                    round_id = round_data['id']
                    await self.update_round_analytics(round_id)
                
                # Wait before next update
                await asyncio.sleep(self.update_interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Analytics update error: {str(e)}")
                await asyncio.sleep(10)
    
    async def update_round_analytics(self, round_id: UUID):
        """Update all analytics for a round"""
        try:
            # Get basic statistics
            stats = await self.data_agent.get_round_statistics(round_id)
            
            # Update round summary
            if stats.get('total_cases', 0) > 0:
                fp_rate = (stats.get('invalid_failed', 0) / 
                          (stats.get('invalid_failed', 0) + stats.get('invalid_passed', 0)) * 100) \
                          if (stats.get('invalid_failed', 0) + stats.get('invalid_passed', 0)) > 0 else 0
                
                protection_rate = (stats.get('valid_passed', 0) / 
                                 (stats.get('valid_passed', 0) + stats.get('valid_failed', 0)) * 100) \
                                 if (stats.get('valid_passed', 0) + stats.get('valid_failed', 0)) > 0 else 0
                
                await self.data_agent.update_round(
                    round_id,
                    processed_cases=stats['total_cases'],
                    fp_detection_rate=fp_rate,
                    protection_rate=protection_rate,
                    processing_stats=json.dumps(stats)
                )
            
            # Generate and cache advanced analytics
            await self._generate_confidence_analytics(round_id)
            await self._generate_infringement_analytics(round_id)
            await self._generate_token_efficiency_analytics(round_id)
            
            logger.info(f"Updated analytics for round {round_id}")
            
        except Exception as e:
            logger.error(f"Error updating analytics for round {round_id}: {str(e)}")
    
    async def _generate_confidence_analytics(self, round_id: UUID):
        """Generate confidence score analytics"""
        # Check cache first
        cached = await self.data_agent.get_cached_analytics(round_id, 'confidence_distribution')
        if cached:
            return cached
        
        # Get all cases for the round
        cases = []
        for quadrant in ['valid_passed', 'valid_failed', 'invalid_passed', 'invalid_failed']:
            cases.extend(await self.data_agent.get_cases_by_quadrant(round_id, quadrant, limit=10000))
        
        if not cases:
            return None
        
        # Convert to DataFrame for analysis
        df = pd.DataFrame(cases)
        
        analytics = {
            'timestamp': datetime.now().isoformat(),
            'total_cases': len(df),
            'distributions': {}
        }
        
        # Valid cases with high person confidence (>90%)
        valid_high_conf = df[
            (df['csv_ground_truth'] == 'Valid') & 
            (df['person_present'] == True) & 
            (df['subject_confidence'] > 90)
        ]
        analytics['valid_high_person_conf'] = {
            'count': len(valid_high_conf),
            'percentage': (len(valid_high_conf) / len(df[df['csv_ground_truth'] == 'Valid']) * 100) 
                         if len(df[df['csv_ground_truth'] == 'Valid']) > 0 else 0
        }
        
        # False positives with high structure confidence (>85%)
        fp_high_struct = df[
            (df['csv_ground_truth'] == 'Invalid') & 
            (df['main_subject'].str.contains('Structure', na=False)) & 
            (df['subject_confidence'] > 85)
        ]
        analytics['fp_high_structure_conf'] = {
            'count': len(fp_high_struct),
            'percentage': (len(fp_high_struct) / len(df[df['csv_ground_truth'] == 'Invalid']) * 100)
                         if len(df[df['csv_ground_truth'] == 'Invalid']) > 0 else 0
        }
        
        # Distribution plots
        analytics['distributions']['fp_likelihood'] = {
            'bins': [0, 10, 20, 30, 40, 50, 60, 70, 80, 90, 100],
            'valid_counts': df[df['csv_ground_truth'] == 'Valid']['fp_likelihood'].value_counts(bins=10).to_dict(),
            'invalid_counts': df[df['csv_ground_truth'] == 'Invalid']['fp_likelihood'].value_counts(bins=10).to_dict()
        }
        
        # Cache the results
        await self.data_agent.cache_analytics(round_id, 'confidence_distribution', analytics)
        
        return analytics
    
    async def _generate_infringement_analytics(self, round_id: UUID):
        """Generate analytics by infringement type"""
        # Get statistics from database
        stats = await self.data_agent.get_round_statistics(round_id)
        infringement_data = stats.get('infringement_breakdown', [])
        
        if not infringement_data:
            return None
        
        analytics = {
            'timestamp': datetime.now().isoformat(),
            'by_type': {}
        }
        
        for item in infringement_data:
            inf_type = item['infringement_type']
            total = item['total_cases']
            correct = item['correct_decisions']
            accuracy = (correct / total * 100) if total > 0 else 0
            
            analytics['by_type'][inf_type] = {
                'total_cases': total,
                'correct_decisions': correct,
                'accuracy': accuracy,
                'median_fp_likelihood': item.get('median_fp_likelihood', 0)
            }
        
        # Sort by accuracy
        analytics['ranked_performance'] = sorted(
            [(k, v['accuracy']) for k, v in analytics['by_type'].items()],
            key=lambda x: x[1],
            reverse=True
        )
        
        # Cache the results
        await self.data_agent.cache_analytics(round_id, 'infringement_analytics', analytics)
        
        return analytics
    
    async def _generate_token_efficiency_analytics(self, round_id: UUID):
        """Analyze token usage vs accuracy"""
        # Get cases with token data
        cases = []
        for quadrant in ['valid_passed', 'valid_failed', 'invalid_passed', 'invalid_failed']:
            cases.extend(await self.data_agent.get_cases_by_quadrant(round_id, quadrant, limit=10000))
        
        if not cases:
            return None
        
        df = pd.DataFrame(cases)
        
        # Calculate accuracy per case
        df['is_correct'] = (
            ((df['csv_ground_truth'] == 'Valid') & (df['quadrant'].isin(['valid_passed']))) |
            ((df['csv_ground_truth'] == 'Invalid') & (df['quadrant'].isin(['invalid_failed'])))
        )
        
        # Group by token ranges
        token_ranges = [(0, 300), (300, 500), (500, 700), (700, 1000), (1000, float('inf'))]
        
        analytics = {
            'timestamp': datetime.now().isoformat(),
            'token_efficiency': []
        }
        
        for min_tokens, max_tokens in token_ranges:
            mask = (df['vlm_tokens_used'] >= min_tokens) & (df['vlm_tokens_used'] < max_tokens)
            subset = df[mask]
            
            if len(subset) > 0:
                range_data = {
                    'range': f"{min_tokens}-{max_tokens if max_tokens != float('inf') else '+'}",
                    'count': len(subset),
                    'accuracy': (subset['is_correct'].sum() / len(subset) * 100),
                    'avg_tokens': subset['vlm_tokens_used'].mean(),
                    'avg_processing_time': subset['processing_time'].mean()
                }
                analytics['token_efficiency'].append(range_data)
        
        # Cache the results
        await self.data_agent.cache_analytics(round_id, 'token_efficiency', analytics)
        
        return analytics
    
    async def generate_visualization(self, round_id: UUID, viz_type: str) -> str:
        """Generate visualization and return as base64 encoded image"""
        plt.figure(figsize=(10, 6))
        
        try:
            if viz_type == 'confidence_distribution':
                await self._plot_confidence_distribution(round_id)
            elif viz_type == 'accuracy_heatmap':
                await self._plot_accuracy_heatmap(round_id)
            elif viz_type == 'token_scatter':
                await self._plot_token_scatter(round_id)
            elif viz_type == 'quadrant_pie':
                await self._plot_quadrant_distribution(round_id)
            else:
                raise ValueError(f"Unknown visualization type: {viz_type}")
            
            # Save to buffer
            buffer = BytesIO()
            plt.tight_layout()
            plt.savefig(buffer, format='png', dpi=100, bbox_inches='tight')
            buffer.seek(0)
            
            # Encode to base64
            image_base64 = base64.b64encode(buffer.read()).decode('utf-8')
            
            plt.close()
            return image_base64
            
        except Exception as e:
            plt.close()
            logger.error(f"Error generating visualization: {str(e)}")
            raise
    
    async def _plot_confidence_distribution(self, round_id: UUID):
        """Plot FP likelihood distribution"""
        # Get cases
        cases = []
        for quadrant in ['valid_passed', 'valid_failed', 'invalid_passed', 'invalid_failed']:
            cases.extend(await self.data_agent.get_cases_by_quadrant(round_id, quadrant, limit=10000))
        
        df = pd.DataFrame(cases)
        
        # Create subplots
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(14, 6))
        
        # FP Likelihood distribution
        valid_fp = df[df['csv_ground_truth'] == 'Valid']['fp_likelihood']
        invalid_fp = df[df['csv_ground_truth'] == 'Invalid']['fp_likelihood']
        
        ax1.hist([valid_fp, invalid_fp], bins=20, label=['Valid Cases', 'Invalid Cases'], 
                alpha=0.7, color=['green', 'red'])
        ax1.axvline(x=70, color='yellow', linestyle='--', label='Decision Threshold (70%)')
        ax1.set_xlabel('False Positive Likelihood (%)')
        ax1.set_ylabel('Count')
        ax1.set_title('FP Likelihood Distribution by Ground Truth')
        ax1.legend()
        
        # Subject confidence by type
        person_conf = df[df['main_subject'] == 'Person']['subject_confidence']
        struct_conf = df[df['main_subject'].str.contains('Structure', na=False)]['subject_confidence']
        
        ax2.hist([person_conf, struct_conf], bins=20, label=['Person', 'Structure'], 
                alpha=0.7, color=['blue', 'orange'])
        ax2.set_xlabel('Subject Confidence (%)')
        ax2.set_ylabel('Count')
        ax2.set_title('Subject Confidence by Type')
        ax2.legend()
        
        plt.suptitle(f'Confidence Score Analysis - Round {round_id}')
    
    async def _plot_accuracy_heatmap(self, round_id: UUID):
        """Plot accuracy heatmap by infringement type"""
        stats = await self.data_agent.get_round_statistics(round_id)
        infringement_data = stats.get('infringement_breakdown', [])
        
        if not infringement_data:
            return
        
        # Prepare data for heatmap
        inf_types = []
        accuracies = []
        
        for item in infringement_data:
            inf_types.append(item['infringement_type'])
            total = item['total_cases']
            correct = item['correct_decisions']
            accuracy = (correct / total * 100) if total > 0 else 0
            accuracies.append(accuracy)
        
        # Create horizontal bar chart (better than heatmap for 1D data)
        y_pos = np.arange(len(inf_types))
        colors = ['green' if acc >= 80 else 'orange' if acc >= 60 else 'red' for acc in accuracies]
        
        plt.barh(y_pos, accuracies, color=colors)
        plt.yticks(y_pos, inf_types)
        plt.xlabel('Accuracy (%)')
        plt.title('Detection Accuracy by Infringement Type')
        plt.xlim(0, 100)
        
        # Add value labels
        for i, (acc, inf) in enumerate(zip(accuracies, inf_types)):
            plt.text(acc + 1, i, f'{acc:.1f}%', va='center')
    
    async def _plot_token_scatter(self, round_id: UUID):
        """Plot token usage vs accuracy scatter plot"""
        cases = []
        for quadrant in ['valid_passed', 'valid_failed', 'invalid_passed', 'invalid_failed']:
            cases.extend(await self.data_agent.get_cases_by_quadrant(round_id, quadrant, limit=10000))
        
        df = pd.DataFrame(cases)
        
        # Calculate correctness
        df['is_correct'] = (
            ((df['csv_ground_truth'] == 'Valid') & (df['quadrant'].isin(['valid_passed']))) |
            ((df['csv_ground_truth'] == 'Invalid') & (df['quadrant'].isin(['invalid_failed'])))
        )
        
        # Create scatter plot
        correct = df[df['is_correct']]
        incorrect = df[~df['is_correct']]
        
        plt.scatter(correct['vlm_tokens_used'], correct['processing_time'], 
                   alpha=0.5, c='green', label='Correct', s=30)
        plt.scatter(incorrect['vlm_tokens_used'], incorrect['processing_time'], 
                   alpha=0.5, c='red', label='Incorrect', s=30)
        
        plt.xlabel('Tokens Used')
        plt.ylabel('Processing Time (seconds)')
        plt.title('Token Usage vs Processing Time')
        plt.legend()
        
        # Add trend line
        z = np.polyfit(df['vlm_tokens_used'], df['processing_time'], 1)
        p = np.poly1d(z)
        plt.plot(df['vlm_tokens_used'].sort_values(), 
                p(df['vlm_tokens_used'].sort_values()), 
                "y--", alpha=0.8, label='Trend')
    
    async def _plot_quadrant_distribution(self, round_id: UUID):
        """Plot quadrant distribution pie chart"""
        stats = await self.data_agent.get_round_statistics(round_id)
        
        quadrants = ['valid_passed', 'valid_failed', 'invalid_passed', 'invalid_failed']
        sizes = [stats.get(q, 0) for q in quadrants]
        colors = ['green', 'orange', 'red', 'blue']
        labels = [
            f"Valid Passed\n({sizes[0]})",
            f"Valid Failed\n({sizes[1]})",
            f"Invalid Passed\n({sizes[2]})",
            f"Invalid Failed\n({sizes[3]})"
        ]
        
        # Create pie chart
        plt.pie(sizes, labels=labels, colors=colors, autopct='%1.1f%%', startangle=90)
        plt.title(f'Case Distribution by Quadrant - Round {round_id}')
        plt.axis('equal')
    
    async def generate_report(self, round_id: UUID) -> Dict:
        """Generate comprehensive analytics report"""
        report = {
            'round_id': str(round_id),
            'generated_at': datetime.now().isoformat(),
            'sections': {}
        }
        
        # Get basic statistics
        stats = await self.data_agent.get_round_statistics(round_id)
        report['sections']['summary'] = {
            'total_cases': stats.get('total_cases', 0),
            'fp_detection_rate': stats.get('avg_fp_likelihood', 0),
            'protection_rate': 0,  # Calculate from quadrants
            'processing_metrics': {
                'avg_processing_time': stats.get('avg_processing_time', 0),
                'total_tokens': stats.get('total_tokens_used', 0)
            }
        }
        
        # Get detailed analytics
        confidence_analytics = await self._generate_confidence_analytics(round_id)
        if confidence_analytics:
            report['sections']['confidence_analysis'] = confidence_analytics
        
        infringement_analytics = await self._generate_infringement_analytics(round_id)
        if infringement_analytics:
            report['sections']['infringement_analysis'] = infringement_analytics
        
        token_analytics = await self._generate_token_efficiency_analytics(round_id)
        if token_analytics:
            report['sections']['token_efficiency'] = token_analytics
        
        # Generate insights
        report['sections']['insights'] = await self._generate_insights(stats, confidence_analytics)
        
        return report
    
    async def _generate_insights(self, stats: Dict, confidence_data: Dict) -> List[str]:
        """Generate actionable insights from analytics"""
        insights = []
        
        # FP detection performance
        fp_rate = stats.get('avg_fp_likelihood', 0)
        if fp_rate >= 70:
            insights.append(f"✅ Excellent false positive detection rate: {fp_rate:.1f}%")
        elif fp_rate >= 50:
            insights.append(f"⚠️ Moderate false positive detection rate: {fp_rate:.1f}% - Consider threshold adjustment")
        else:
            insights.append(f"❌ Low false positive detection rate: {fp_rate:.1f}% - Review detection logic")
        
        # Valid case protection
        if confidence_data:
            valid_high_conf = confidence_data.get('valid_high_person_conf', {})
            if valid_high_conf.get('percentage', 0) >= 90:
                insights.append(f"✅ Strong valid case protection: {valid_high_conf['percentage']:.1f}% with high confidence")
            else:
                insights.append(f"⚠️ Valid case protection needs improvement: Only {valid_high_conf['percentage']:.1f}% with high confidence")
        
        # Structure detection
        if confidence_data:
            fp_struct = confidence_data.get('fp_high_structure_conf', {})
            if fp_struct.get('percentage', 0) >= 80:
                insights.append(f"✅ Excellent structure detection in false positives: {fp_struct['percentage']:.1f}%")
        
        return insights