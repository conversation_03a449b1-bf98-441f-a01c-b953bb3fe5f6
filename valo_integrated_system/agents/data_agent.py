#!/usr/bin/env python3
"""
Data Agent: Handles all PostgreSQL database operations
Provides async interface for other agents
"""

import asyncio
import asyncpg
import json
from datetime import datetime
from typing import Dict, List, Optional, Any
from uuid import UUID
import logging

logger = logging.getLogger(__name__)

class DataAgent:
    def __init__(self, db_config: Dict[str, str]):
        """Initialize data agent with PostgreSQL connection config"""
        self.db_config = db_config
        self.pool = None
        self.agent_name = "data_agent"
        
    async def initialize(self):
        """Create connection pool"""
        self.pool = await asyncpg.create_pool(
            host=self.db_config.get('host', 'localhost'),
            port=self.db_config.get('port', 5432),
            user=self.db_config.get('user', 'valo_user'),
            password=self.db_config.get('password'),
            database=self.db_config.get('database', 'valo_system'),
            min_size=5,
            max_size=20
        )
        await self.update_agent_status('online')
        logger.info("Data Agent initialized with connection pool")
    
    async def close(self):
        """Close connection pool"""
        if self.pool:
            await self.update_agent_status('offline')
            await self.pool.close()
    
    async def update_agent_status(self, status: str, task: Dict = None):
        """Update agent status in database"""
        async with self.pool.acquire() as conn:
            await conn.execute('''
                INSERT INTO agent_status (agent_name, status, last_heartbeat, current_task)
                VALUES ($1, $2, $3, $4)
                ON CONFLICT (agent_name) DO UPDATE
                SET status = $2, last_heartbeat = $3, current_task = $4
            ''', self.agent_name, status, datetime.now(), json.dumps(task) if task else None)
    
    # Round operations
    async def create_round(self, name: str = None) -> UUID:
        """Create a new processing round"""
        if not name:
            name = f"Round_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        async with self.pool.acquire() as conn:
            round_id = await conn.fetchval('''
                INSERT INTO rounds (name, start_time, status)
                VALUES ($1, $2, 'running')
                RETURNING id
            ''', name, datetime.now())
            
            logger.info(f"Created round {name} with ID {round_id}")
            return round_id
    
    async def update_round(self, round_id: UUID, **kwargs):
        """Update round information"""
        allowed_fields = {
            'end_time', 'total_cases', 'processed_cases',
            'fp_detection_rate', 'protection_rate', 'status',
            'config_params', 'processing_stats'
        }
        
        updates = []
        values = [round_id]
        param_count = 1
        
        for field, value in kwargs.items():
            if field in allowed_fields:
                param_count += 1
                updates.append(f"{field} = ${param_count}")
                values.append(value)
        
        if updates:
            query = f"UPDATE rounds SET {', '.join(updates)} WHERE id = $1"
            async with self.pool.acquire() as conn:
                await conn.execute(query, *values)
    
    # Case operations
    async def insert_case(self, round_id: UUID, case_data: Dict[str, Any]) -> UUID:
        """Insert or update a case"""
        # Calculate quadrant if we have the necessary data
        if 'csv_ground_truth' in case_data and 'vlm_decision' in case_data:
            vlm_filtered = case_data['vlm_decision'] == 'filtered'
            case_data['quadrant'] = self._calculate_quadrant(
                case_data['csv_ground_truth'], 
                vlm_filtered
            )
        
        # Convert lists/dicts to appropriate PostgreSQL types
        if 'fp_indicators' in case_data and isinstance(case_data['fp_indicators'], list):
            case_data['fp_indicators'] = case_data['fp_indicators']
        
        if 'vlm_response' in case_data and isinstance(case_data['vlm_response'], (dict, str)):
            if isinstance(case_data['vlm_response'], str):
                case_data['vlm_response'] = json.dumps({'raw': case_data['vlm_response']})
            else:
                case_data['vlm_response'] = json.dumps(case_data['vlm_response'])
        
        # Build insert query
        fields = ['round_id'] + list(case_data.keys())
        values = [round_id] + list(case_data.values())
        placeholders = [f'${i+1}' for i in range(len(values))]
        
        query = f'''
            INSERT INTO cases ({', '.join(fields)})
            VALUES ({', '.join(placeholders)})
            ON CONFLICT (round_id, case_number) DO UPDATE SET
            {', '.join([f"{f} = EXCLUDED.{f}" for f in fields if f not in ['round_id', 'case_number']])}
            RETURNING id
        '''
        
        async with self.pool.acquire() as conn:
            case_id = await conn.fetchval(query, *values)
            return case_id
    
    async def get_case(self, case_number: str, round_id: UUID = None) -> Dict:
        """Get a specific case"""
        async with self.pool.acquire() as conn:
            if round_id:
                row = await conn.fetchrow('''
                    SELECT * FROM cases 
                    WHERE case_number = $1 AND round_id = $2
                ''', case_number, round_id)
            else:
                row = await conn.fetchrow('''
                    SELECT * FROM cases 
                    WHERE case_number = $1
                    ORDER BY created_at DESC LIMIT 1
                ''', case_number)
            
            return dict(row) if row else None
    
    async def get_cases_by_quadrant(self, round_id: UUID, quadrant: str, 
                                   limit: int = 100, offset: int = 0) -> List[Dict]:
        """Get cases by quadrant category with pagination"""
        async with self.pool.acquire() as conn:
            rows = await conn.fetch('''
                SELECT * FROM cases
                WHERE round_id = $1 AND quadrant = $2
                ORDER BY created_at DESC
                LIMIT $3 OFFSET $4
            ''', round_id, quadrant, limit, offset)
            
            return [dict(row) for row in rows]
    
    async def search_cases(self, round_id: UUID, search_params: Dict) -> List[Dict]:
        """Search cases with multiple filters"""
        conditions = ["round_id = $1"]
        values = [round_id]
        param_count = 1
        
        # Build search conditions
        if 'case_number' in search_params:
            param_count += 1
            conditions.append(f"case_number ILIKE ${param_count}")
            values.append(f"%{search_params['case_number']}%")
        
        if 'infringement_type' in search_params:
            param_count += 1
            conditions.append(f"infringement_type = ${param_count}")
            values.append(search_params['infringement_type'])
        
        if 'min_fp_likelihood' in search_params:
            param_count += 1
            conditions.append(f"fp_likelihood >= ${param_count}")
            values.append(search_params['min_fp_likelihood'])
        
        if 'text_search' in search_params:
            param_count += 1
            conditions.append(f"to_tsvector('english', csv_remarks) @@ plainto_tsquery('english', ${param_count})")
            values.append(search_params['text_search'])
        
        query = f'''
            SELECT * FROM cases
            WHERE {' AND '.join(conditions)}
            ORDER BY created_at DESC
            LIMIT 1000
        '''
        
        async with self.pool.acquire() as conn:
            rows = await conn.fetch(query, *values)
            return [dict(row) for row in rows]
    
    # Analytics operations
    async def get_round_statistics(self, round_id: UUID) -> Dict:
        """Get comprehensive statistics for a round"""
        async with self.pool.acquire() as conn:
            # Use the pre-defined view
            stats = await conn.fetchrow('''
                SELECT * FROM round_analytics WHERE id = $1
            ''', round_id)
            
            result = dict(stats) if stats else {}
            
            # Get infringement breakdown
            infringement_stats = await conn.fetch('''
                SELECT * FROM infringement_analytics 
                WHERE round_id = $1
                ORDER BY total_cases DESC
            ''', round_id)
            
            result['infringement_breakdown'] = [dict(row) for row in infringement_stats]
            
            # Get confidence distributions
            conf_dist = await conn.fetchrow('''
                SELECT 
                    percentile_cont(ARRAY[0.25, 0.5, 0.75]) WITHIN GROUP (ORDER BY fp_likelihood) as fp_quartiles,
                    percentile_cont(ARRAY[0.25, 0.5, 0.75]) WITHIN GROUP (ORDER BY subject_confidence) as conf_quartiles
                FROM cases WHERE round_id = $1
            ''', round_id)
            
            if conf_dist:
                result['confidence_distributions'] = {
                    'fp_likelihood_quartiles': conf_dist['fp_quartiles'],
                    'subject_confidence_quartiles': conf_dist['conf_quartiles']
                }
            
            return result
    
    async def cache_analytics(self, round_id: UUID, metric_type: str, 
                            metric_data: Dict, expires_hours: int = 24):
        """Cache computed analytics for performance"""
        expires_at = datetime.now().timestamp() + (expires_hours * 3600)
        
        async with self.pool.acquire() as conn:
            await conn.execute('''
                INSERT INTO analytics_cache (round_id, metric_type, metric_data, expires_at)
                VALUES ($1, $2, $3, to_timestamp($4))
                ON CONFLICT (round_id, metric_type) DO UPDATE
                SET metric_data = $3, expires_at = to_timestamp($4), computed_at = CURRENT_TIMESTAMP
            ''', round_id, metric_type, json.dumps(metric_data), expires_at)
    
    async def get_cached_analytics(self, round_id: UUID, metric_type: str) -> Optional[Dict]:
        """Retrieve cached analytics if not expired"""
        async with self.pool.acquire() as conn:
            row = await conn.fetchrow('''
                SELECT metric_data FROM analytics_cache
                WHERE round_id = $1 AND metric_type = $2 AND expires_at > CURRENT_TIMESTAMP
            ''', round_id, metric_type)
            
            return json.loads(row['metric_data']) if row else None
    
    # Logging operations
    async def log(self, round_id: UUID, case_number: str, agent_name: str,
                  level: str, message: str, details: Dict = None):
        """Log processing events"""
        async with self.pool.acquire() as conn:
            await conn.execute('''
                INSERT INTO processing_logs 
                (round_id, case_number, agent_name, log_level, message, details)
                VALUES ($1, $2, $3, $4, $5, $6)
            ''', round_id, case_number, agent_name, level, message, 
                json.dumps(details) if details else None)
    
    # Human review operations
    async def update_human_review(self, case_id: UUID, reviewer: str, 
                                status: str, notes: str = None):
        """Update human review for a case"""
        async with self.pool.acquire() as conn:
            await conn.execute('''
                UPDATE cases 
                SET human_review_status = $2, 
                    human_review_notes = $3,
                    reviewed_by = $4,
                    reviewed_at = $5
                WHERE id = $1
            ''', case_id, status, notes, reviewer, datetime.now())
    
    # Utility methods
    def _calculate_quadrant(self, ground_truth: str, vlm_filtered: bool) -> str:
        """Calculate quadrant category"""
        if ground_truth == 'Valid' and not vlm_filtered:
            return 'valid_passed'
        elif ground_truth == 'Valid' and vlm_filtered:
            return 'valid_failed'
        elif ground_truth == 'Invalid' and not vlm_filtered:
            return 'invalid_passed'
        else:
            return 'invalid_failed'
    
    async def get_active_rounds(self) -> List[Dict]:
        """Get all active (running) rounds"""
        async with self.pool.acquire() as conn:
            rows = await conn.fetch('''
                SELECT * FROM rounds 
                WHERE status = 'running'
                ORDER BY created_at DESC
            ''')
            return [dict(row) for row in rows]
    
    async def get_agent_statuses(self) -> List[Dict]:
        """Get status of all agents"""
        async with self.pool.acquire() as conn:
            rows = await conn.fetch('''
                SELECT * FROM agent_status
                ORDER BY agent_name
            ''')
            return [dict(row) for row in rows]