<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VALO System Dashboard</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.7.2/socket.io.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #1a1a1a; color: #fff; }
        .header { text-align: center; margin-bottom: 30px; }
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .stat-card { background: #2d2d2d; padding: 20px; border-radius: 8px; border-left: 4px solid #00ff88; }
        .stat-value { font-size: 2em; font-weight: bold; color: #00ff88; }
        .stat-label { color: #ccc; margin-top: 5px; }
        .chart-container { background: #2d2d2d; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .nav-buttons { display: flex; gap: 10px; justify-content: center; margin-bottom: 20px; }
        .nav-button { padding: 10px 20px; background: #444; color: #fff; text-decoration: none; border-radius: 5px; border: none; cursor: pointer; }
        .nav-button:hover { background: #555; }
        .nav-button.active { background: #00ff88; color: #000; }
        .status { padding: 10px; border-radius: 5px; margin: 10px 0; }
        .status.online { background: #2d5016; border-left: 4px solid #00ff88; }
        .status.offline { background: #501616; border-left: 4px solid #ff4444; }
        .control-btn { padding: 8px 16px; border: none; border-radius: 5px; cursor: pointer; font-size: 12px; margin: 2px; }
        .start-btn { background: #00ff88; color: #000; }
        .start-btn:hover { background: #00cc6a; }
        .stop-btn { background: #ff4444; color: #fff; }
        .stop-btn:hover { background: #cc3333; }
        .demo-btn { background: #4488ff; color: #fff; }
        .demo-btn:hover { background: #3366cc; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🎯 VALO Integrated System</h1>
        <p>False Positive Reduction Dashboard</p>
    </div>

    <div class="nav-buttons">
        <a href="/" class="nav-button active">Dashboard</a>
        <a href="/review" class="nav-button">Case Review</a>
        <a href="/analytics" class="nav-button">Analytics</a>
    </div>

    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-value" id="total-cases">0</div>
            <div class="stat-label">Total Cases Processed</div>
        </div>
        <div class="stat-card">
            <div class="stat-value" id="fp-rate">0%</div>
            <div class="stat-label">False Positive Detection</div>
        </div>
        <div class="stat-card">
            <div class="stat-value" id="protection-rate">0%</div>
            <div class="stat-label">Valid Case Protection</div>
        </div>
        <div class="stat-card">
            <div class="stat-value" id="processing-time">0s</div>
            <div class="stat-label">Avg Processing Time</div>
        </div>
    </div>

    <div class="chart-container">
        <h3>Processing Progress</h3>
        <canvas id="progress-chart" width="400" height="200"></canvas>
    </div>

    <div class="chart-container">
        <h3>System Agents Status & Controls</h3>
        <div id="agent-status">
            <div class="status online" id="data-agent-status">
                <strong>Data Agent:</strong> <span>Online</span>
                <div style="float: right;">
                    <button class="control-btn start-btn" onclick="startAgent('data_agent')">Start</button>
                    <button class="control-btn stop-btn" onclick="stopAgent('data_agent')">Stop</button>
                </div>
            </div>
            <div class="status offline" id="processing-agent-status">
                <strong>Processing Agent:</strong> <span>Offline</span>
                <div style="float: right;">
                    <button class="control-btn start-btn" onclick="startProcessing()">Start Round</button>
                    <button class="control-btn" onclick="pauseProcessing()" style="background: #ff8800; color: #fff;">Pause</button>
                    <button class="control-btn" onclick="resumeProcessing()" style="background: #00aaff; color: #fff;">Resume</button>
                    <button class="control-btn stop-btn" onclick="stopAgent('processing_agent')">Stop</button>
                </div>
            </div>
            <div class="status offline" id="analytics-agent-status">
                <strong>Analytics Agent:</strong> <span>Offline</span>
                <div style="float: right;">
                    <button class="control-btn start-btn" onclick="startAgent('analytics_agent')">Start</button>
                    <button class="control-btn stop-btn" onclick="stopAgent('analytics_agent')">Stop</button>
                </div>
            </div>
            <div class="status online" id="web-agent-status">
                <strong>Web Agent:</strong> <span>Online</span>
                <div style="float: right;">
                    <span style="color: #888; font-size: 12px;">Always Running</span>
                </div>
            </div>
        </div>
        
        <div style="margin-top: 20px; padding: 15px; background: #333; border-radius: 8px;">
            <h4 style="margin-top: 0; color: #00ff88;">📊 Processing Progress</h4>
            <div id="processing-progress" style="display: none;">
                <div style="background: #555; border-radius: 10px; height: 20px; margin: 10px 0;">
                    <div id="progress-bar" style="background: linear-gradient(90deg, #00ff88, #00cc6a); height: 100%; border-radius: 10px; width: 0%; transition: width 0.5s;"></div>
                </div>
                <div id="progress-text" style="text-align: center; color: #ccc;">0 / 0 cases processed (0%)</div>
            </div>
            <div style="text-align: center; margin-top: 15px;">
                <button class="control-btn demo-btn" onclick="generateDemoData()">Generate Demo Data</button>
                <input type="number" id="demo-count" value="10" min="1" max="50" style="width: 60px; margin-left: 10px; padding: 5px; background: #444; color: #fff; border: 1px solid #666; border-radius: 3px;">
                <span style="color: #ccc; margin-left: 5px;">cases</span>
            </div>
        </div>
    </div>

    <script>
        // Initialize Socket.IO connection
        const socket = io();
        
        // Initialize chart
        const ctx = document.getElementById('progress-chart').getContext('2d');
        const progressChart = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['Valid Passed', 'Valid Failed', 'Invalid Passed', 'Invalid Failed'],
                datasets: [{
                    data: [0, 0, 0, 0],
                    backgroundColor: ['#00ff88', '#ff8800', '#ff4444', '#4488ff']
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        labels: { color: '#fff' }
                    }
                }
            }
        });

        // Update dashboard data
        function updateDashboard() {
            fetch('/api/rounds')
                .then(response => response.json())
                .then(rounds => {
                    if (rounds.length > 0) {
                        const latestRound = rounds[0];
                        fetch(`/api/rounds/${latestRound.id}/status`)
                            .then(response => response.json())
                            .then(stats => {
                                document.getElementById('total-cases').textContent = stats.total_cases || 0;
                                document.getElementById('fp-rate').textContent = (stats.avg_fp_likelihood || 0).toFixed(1) + '%';
                                document.getElementById('protection-rate').textContent = (stats.protection_rate || 0).toFixed(1) + '%';
                                document.getElementById('processing-time').textContent = (stats.avg_processing_time || 0).toFixed(1) + 's';
                                
                                // Update chart
                                progressChart.data.datasets[0].data = [
                                    stats.valid_passed || 0,
                                    stats.valid_failed || 0,
                                    stats.invalid_passed || 0,
                                    stats.invalid_failed || 0
                                ];
                                progressChart.update();
                            });
                    }
                });
        }

        // Update agent status
        function updateAgentStatus() {
            fetch('/api/agents/status')
                .then(response => response.json())
                .then(agents => {
                    agents.forEach(agent => {
                        const statusEl = document.getElementById(`${agent.agent_name.replace('_', '-')}-status`);
                        if (statusEl) {
                            statusEl.className = `status ${agent.status}`;
                            statusEl.querySelector('span').textContent = agent.status.charAt(0).toUpperCase() + agent.status.slice(1);
                        }
                    });
                });
        }

        // Agent control functions
        function startAgent(agentName) {
            fetch(`/api/agents/${agentName}/start`, {method: 'POST'})
                .then(response => response.json())
                .then(data => {
                    console.log('Agent started:', data);
                    updateAgentStatus();
                    showNotification(`${agentName} started successfully`, 'success');
                })
                .catch(error => {
                    console.error('Error starting agent:', error);
                    showNotification(`Failed to start ${agentName}`, 'error');
                });
        }

        function startProcessing() {
            fetch('/api/agents/processing_agent/start', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({})
            })
            .then(response => response.json())
            .then(data => {
                console.log('Processing started:', data);
                updateAgentStatus();
                showProgressBar();
                showNotification('Processing round started', 'success');
            })
            .catch(error => {
                console.error('Error starting processing:', error);
                showNotification('Failed to start processing', 'error');
            });
        }

        function pauseProcessing() {
            fetch('/api/agents/processing_agent/pause', {method: 'POST'})
                .then(response => response.json())
                .then(data => {
                    console.log('Processing paused:', data);
                    updateAgentStatus();
                    showNotification('Processing paused', 'success');
                })
                .catch(error => {
                    console.error('Error pausing processing:', error);
                    showNotification('Failed to pause processing', 'error');
                });
        }

        function resumeProcessing() {
            fetch('/api/agents/processing_agent/resume', {method: 'POST'})
                .then(response => response.json())
                .then(data => {
                    console.log('Processing resumed:', data);
                    updateAgentStatus();
                    showNotification('Processing resumed', 'success');
                })
                .catch(error => {
                    console.error('Error resuming processing:', error);
                    showNotification('Failed to resume processing', 'error');
                });
        }

        function stopAgent(agentName) {
            fetch(`/api/agents/${agentName}/stop`, {method: 'POST'})
                .then(response => response.json())
                .then(data => {
                    console.log('Agent stopped:', data);
                    updateAgentStatus();
                    showNotification(`${agentName} stopped successfully`, 'success');
                })
                .catch(error => {
                    console.error('Error stopping agent:', error);
                    showNotification(`Failed to stop ${agentName}`, 'error');
                });
        }

        function generateDemoData() {
            const count = document.getElementById('demo-count').value;
            fetch('/api/demo/generate', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({count: parseInt(count)})
            })
            .then(response => response.json())
            .then(data => {
                console.log('Demo data generated:', data);
                updateDashboard();
                showNotification(`Generated ${count} demo cases`, 'success');
            })
            .catch(error => {
                console.error('Error generating demo data:', error);
                showNotification('Failed to generate demo data', 'error');
            });
        }

        function showNotification(message, type) {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed; top: 20px; right: 20px; z-index: 1000;
                padding: 15px 20px; border-radius: 5px; color: white;
                background: ${type === 'success' ? '#00ff88' : '#ff4444'};
                font-weight: bold; box-shadow: 0 4px 6px rgba(0,0,0,0.3);
            `;
            notification.textContent = message;
            document.body.appendChild(notification);
            setTimeout(() => document.body.removeChild(notification), 3000);
        }

        function showProgressBar() {
            document.getElementById('processing-progress').style.display = 'block';
        }

        function updateProcessingProgress() {
            fetch('/api/processing/status')
                .then(response => response.json())
                .then(status => {
                    if (status.is_running || status.processed_count > 0) {
                        showProgressBar();
                        const progressBar = document.getElementById('progress-bar');
                        const progressText = document.getElementById('progress-text');
                        const progressPercent = status.progress_percent || 0;
                        
                        progressBar.style.width = progressPercent + '%';
                        progressText.textContent = `${status.processed_count} / ${status.total_cases} cases processed (${progressPercent.toFixed(1)}%)`;
                        
                        if (status.is_paused) {
                            progressText.textContent += ' - PAUSED';
                        }
                    }
                })
                .catch(error => console.error('Error updating progress:', error));
        }

        // Initialize and set up periodic updates
        updateDashboard();
        updateAgentStatus();
        updateProcessingProgress();
        setInterval(updateDashboard, 5000);
        setInterval(updateAgentStatus, 10000);
        setInterval(updateProcessingProgress, 2000);

        // Socket.IO event handlers
        socket.on('connect', function() {
            console.log('Connected to VALO system');
        });

        socket.on('round_update', function(data) {
            console.log('Round update:', data);
            updateDashboard();
        });
    </script>
</body>
</html>