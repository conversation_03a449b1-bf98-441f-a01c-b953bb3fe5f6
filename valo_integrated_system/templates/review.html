<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VALO Case Review</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #1a1a1a; color: #fff; }
        .header { text-align: center; margin-bottom: 30px; }
        .nav-buttons { display: flex; gap: 10px; justify-content: center; margin-bottom: 20px; }
        .nav-button { padding: 10px 20px; background: #444; color: #fff; text-decoration: none; border-radius: 5px; }
        .nav-button:hover { background: #555; }
        .nav-button.active { background: #00ff88; color: #000; }
        .filters { background: #2d2d2d; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .filter-group { display: flex; gap: 15px; align-items: center; margin-bottom: 10px; }
        .filter-group label { min-width: 100px; }
        .filter-group select, .filter-group input { padding: 8px; background: #444; color: #fff; border: 1px solid #666; border-radius: 4px; }
        .case-grid { display: grid; grid-template-columns: repeat(auto-fill, minmax(400px, 1fr)); gap: 20px; }
        .case-card { background: #2d2d2d; border-radius: 8px; padding: 20px; }
        .case-header { display: flex; justify-content: between; align-items: center; margin-bottom: 15px; }
        .case-id { font-size: 1.2em; font-weight: bold; }
        .case-status { padding: 4px 8px; border-radius: 4px; font-size: 0.8em; }
        .status-valid-passed { background: #2d5016; color: #00ff88; }
        .status-valid-failed { background: #502d16; color: #ff8800; }
        .status-invalid-passed { background: #501616; color: #ff4444; }
        .status-invalid-failed { background: #162d50; color: #4488ff; }
        .case-images { display: flex; gap: 10px; margin-bottom: 15px; }
        .case-image { width: 150px; height: 100px; object-fit: cover; border-radius: 4px; cursor: pointer; }
        .case-details { font-size: 0.9em; }
        .detail-row { margin: 5px 0; }
        .loading { text-align: center; padding: 40px; color: #888; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔍 VALO Case Review</h1>
        <p>Review and analyze processed cases</p>
    </div>

    <div class="nav-buttons">
        <a href="/" class="nav-button">Dashboard</a>
        <a href="/review" class="nav-button active">Case Review</a>
        <a href="/analytics" class="nav-button">Analytics</a>
    </div>

    <div class="filters">
        <div class="filter-group">
            <label>Round:</label>
            <select id="round-filter">
                <option value="">Select Round...</option>
            </select>
        </div>
        <div class="filter-group">
            <label>Quadrant:</label>
            <select id="quadrant-filter">
                <option value="">All Quadrants</option>
                <option value="valid_passed">Valid Passed</option>
                <option value="valid_failed">Valid Failed</option>
                <option value="invalid_passed">Invalid Passed</option>
                <option value="invalid_failed">Invalid Failed</option>
            </select>
        </div>
        <div class="filter-group">
            <label>Infringement:</label>
            <select id="infringement-filter">
                <option value="">All Types</option>
                <option value="PPE Non-compliance">PPE Non-compliance</option>
                <option value="One man Lashing">One man Lashing</option>
                <option value="Ex.Row Violation">Ex.Row Violation</option>
                <option value="2-Container Distance">2-Container Distance</option>
                <option value="STA Double-up">STA Double-up</option>
                <option value="Spreader Ride">Spreader Ride</option>
            </select>
        </div>
        <div class="filter-group">
            <label>Search:</label>
            <input type="text" id="search-filter" placeholder="Case number...">
            <button onclick="loadCases()" style="padding: 8px 16px; background: #00ff88; color: #000; border: none; border-radius: 4px; cursor: pointer;">Apply</button>
        </div>
    </div>

    <div id="case-container">
        <div class="loading">Loading cases...</div>
    </div>

    <script>
        let currentRound = null;

        // Load available rounds
        function loadRounds() {
            fetch('/api/rounds')
                .then(response => response.json())
                .then(rounds => {
                    const select = document.getElementById('round-filter');
                    select.innerHTML = '<option value="">Select Round...</option>';
                    rounds.forEach(round => {
                        const option = document.createElement('option');
                        option.value = round.id;
                        option.textContent = `${round.name} (${round.total_cases || 0} cases)`;
                        select.appendChild(option);
                    });
                    if (rounds.length > 0) {
                        select.value = rounds[0].id;
                        currentRound = rounds[0].id;
                        loadCases();
                    }
                });
        }

        // Load cases based on filters
        function loadCases() {
            const roundId = document.getElementById('round-filter').value;
            const quadrant = document.getElementById('quadrant-filter').value;
            const infringement = document.getElementById('infringement-filter').value;
            const search = document.getElementById('search-filter').value;

            if (!roundId) {
                document.getElementById('case-container').innerHTML = '<div class="loading">Please select a round</div>';
                return;
            }

            const params = new URLSearchParams();
            if (quadrant) params.append('quadrant', quadrant);
            if (infringement) params.append('infringement', infringement);
            if (search) params.append('search', search);
            params.append('limit', '50');

            const url = `/api/rounds/${roundId}/cases?${params.toString()}`;
            
            fetch(url)
                .then(response => response.json())
                .then(cases => displayCases(cases))
                .catch(error => {
                    console.error('Error loading cases:', error);
                    document.getElementById('case-container').innerHTML = '<div class="loading">Error loading cases</div>';
                });
        }

        // Display cases in grid
        function displayCases(cases) {
            const container = document.getElementById('case-container');
            
            if (cases.length === 0) {
                container.innerHTML = '<div class="loading">No cases found</div>';
                return;
            }

            container.innerHTML = '';
            container.className = 'case-grid';

            cases.forEach(case_ => {
                const caseDiv = document.createElement('div');
                caseDiv.className = 'case-card';
                
                const quadrantClass = `status-${case_.quadrant || 'unknown'}`;
                
                caseDiv.innerHTML = `
                    <div class="case-header">
                        <div class="case-id">${case_.case_number}</div>
                        <div class="case-status ${quadrantClass}">${case_.quadrant || 'Unknown'}</div>
                    </div>
                    <div class="case-images">
                        ${case_.cropped_image ? `<img src="/images/${case_.cropped_image.split('/').pop()}" alt="Cropped" class="case-image" onclick="showImage(this.src)">` : ''}
                        ${case_.source_image ? `<img src="/images/${case_.source_image.split('/').pop()}" alt="Source" class="case-image" onclick="showImage(this.src)">` : ''}
                    </div>
                    <div class="case-details">
                        <div class="detail-row"><strong>Type:</strong> ${case_.infringement_type || 'N/A'}</div>
                        <div class="detail-row"><strong>Ground Truth:</strong> ${case_.csv_ground_truth || 'N/A'}</div>
                        <div class="detail-row"><strong>VLM Decision:</strong> ${case_.vlm_decision || 'N/A'}</div>
                        <div class="detail-row"><strong>FP Likelihood:</strong> ${case_.fp_likelihood || 0}%</div>
                        <div class="detail-row"><strong>Subject:</strong> ${case_.main_subject || 'N/A'}</div>
                        <div class="detail-row"><strong>Person Present:</strong> ${case_.person_present ? 'Yes' : 'No'}</div>
                        ${case_.processing_time ? `<div class="detail-row"><strong>Processing Time:</strong> ${case_.processing_time.toFixed(2)}s</div>` : ''}
                    </div>
                `;
                
                container.appendChild(caseDiv);
            });
        }

        // Show image in full size (simple implementation)
        function showImage(src) {
            const img = new Image();
            img.src = src;
            img.style.maxWidth = '90vw';
            img.style.maxHeight = '90vh';
            
            const overlay = document.createElement('div');
            overlay.style.cssText = 'position:fixed;top:0;left:0;width:100%;height:100%;background:rgba(0,0,0,0.9);display:flex;align-items:center;justify-content:center;z-index:1000;cursor:pointer;';
            overlay.appendChild(img);
            overlay.onclick = () => document.body.removeChild(overlay);
            
            document.body.appendChild(overlay);
        }

        // Event listeners
        document.getElementById('round-filter').addEventListener('change', loadCases);

        // Initialize
        loadRounds();
    </script>
</body>
</html>