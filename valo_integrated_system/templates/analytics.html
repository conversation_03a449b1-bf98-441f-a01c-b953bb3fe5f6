<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VALO Analytics</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #1a1a1a; color: #fff; }
        .header { text-align: center; margin-bottom: 30px; }
        .nav-buttons { display: flex; gap: 10px; justify-content: center; margin-bottom: 20px; }
        .nav-button { padding: 10px 20px; background: #444; color: #fff; text-decoration: none; border-radius: 5px; }
        .nav-button:hover { background: #555; }
        .nav-button.active { background: #00ff88; color: #000; }
        .analytics-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)); gap: 20px; }
        .chart-card { background: #2d2d2d; padding: 20px; border-radius: 8px; }
        .chart-title { font-size: 1.2em; margin-bottom: 15px; text-align: center; }
        .metrics-table { width: 100%; border-collapse: collapse; margin-top: 15px; }
        .metrics-table th, .metrics-table td { padding: 10px; text-align: left; border-bottom: 1px solid #444; }
        .metrics-table th { background: #333; }
        .insight { background: #2d4a2d; border-left: 4px solid #00ff88; padding: 15px; margin: 10px 0; border-radius: 4px; }
        .warning { background: #4a2d2d; border-left: 4px solid #ff8800; }
        .error { background: #4a1f1f; border-left: 4px solid #ff4444; }
    </style>
</head>
<body>
    <div class="header">
        <h1>📊 VALO Analytics</h1>
        <p>Performance insights and visualizations</p>
    </div>

    <div class="nav-buttons">
        <a href="/" class="nav-button">Dashboard</a>
        <a href="/review" class="nav-button">Case Review</a>
        <a href="/analytics" class="nav-button active">Analytics</a>
    </div>

    <div class="analytics-grid">
        <div class="chart-card">
            <div class="chart-title">Confidence Distribution</div>
            <canvas id="confidence-chart" width="400" height="300"></canvas>
        </div>

        <div class="chart-card">
            <div class="chart-title">Accuracy by Infringement Type</div>
            <canvas id="accuracy-chart" width="400" height="300"></canvas>
        </div>

        <div class="chart-card">
            <div class="chart-title">Token Usage vs Processing Time</div>
            <canvas id="token-chart" width="400" height="300"></canvas>
        </div>

        <div class="chart-card">
            <div class="chart-title">System Insights</div>
            <div id="insights-container">
                <div class="insight">System is initializing analytics...</div>
            </div>
        </div>
    </div>

    <div class="chart-card" style="margin-top: 20px;">
        <div class="chart-title">Performance Metrics</div>
        <table class="metrics-table" id="metrics-table">
            <thead>
                <tr>
                    <th>Metric</th>
                    <th>Value</th>
                    <th>Target</th>
                    <th>Status</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>False Positive Detection Rate</td>
                    <td id="fp-rate">-</td>
                    <td>70%</td>
                    <td id="fp-status">-</td>
                </tr>
                <tr>
                    <td>Valid Case Protection Rate</td>
                    <td id="protection-rate">-</td>
                    <td>95%</td>
                    <td id="protection-status">-</td>
                </tr>
                <tr>
                    <td>Average Processing Time</td>
                    <td id="avg-time">-</td>
                    <td>&lt; 5s</td>
                    <td id="time-status">-</td>
                </tr>
                <tr>
                    <td>Total Cases Processed</td>
                    <td id="total-cases">-</td>
                    <td>-</td>
                    <td>-</td>
                </tr>
            </tbody>
        </table>
    </div>

    <script>
        let currentRound = null;
        let charts = {};

        // Initialize charts
        function initializeCharts() {
            // Confidence Distribution Chart
            const confCtx = document.getElementById('confidence-chart').getContext('2d');
            charts.confidence = new Chart(confCtx, {
                type: 'bar',
                data: {
                    labels: ['0-20%', '20-40%', '40-60%', '60-80%', '80-100%'],
                    datasets: [{
                        label: 'Valid Cases',
                        data: [0, 0, 0, 0, 0],
                        backgroundColor: '#00ff88'
                    }, {
                        label: 'Invalid Cases',
                        data: [0, 0, 0, 0, 0],
                        backgroundColor: '#ff4444'
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: { labels: { color: '#fff' } }
                    },
                    scales: {
                        x: { ticks: { color: '#fff' } },
                        y: { ticks: { color: '#fff' } }
                    }
                }
            });

            // Accuracy Chart
            const accCtx = document.getElementById('accuracy-chart').getContext('2d');
            charts.accuracy = new Chart(accCtx, {
                type: 'horizontalBar',
                data: {
                    labels: [],
                    datasets: [{
                        label: 'Accuracy %',
                        data: [],
                        backgroundColor: '#4488ff'
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: { labels: { color: '#fff' } }
                    },
                    scales: {
                        x: { ticks: { color: '#fff' }, max: 100 },
                        y: { ticks: { color: '#fff' } }
                    }
                }
            });

            // Token Chart
            const tokenCtx = document.getElementById('token-chart').getContext('2d');
            charts.token = new Chart(tokenCtx, {
                type: 'scatter',
                data: {
                    datasets: [{
                        label: 'Correct Predictions',
                        data: [],
                        backgroundColor: '#00ff88'
                    }, {
                        label: 'Incorrect Predictions',
                        data: [],
                        backgroundColor: '#ff4444'
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: { labels: { color: '#fff' } }
                    },
                    scales: {
                        x: { 
                            title: { display: true, text: 'Tokens Used', color: '#fff' },
                            ticks: { color: '#fff' }
                        },
                        y: { 
                            title: { display: true, text: 'Processing Time (s)', color: '#fff' },
                            ticks: { color: '#fff' }
                        }
                    }
                }
            });
        }

        // Load analytics data
        function loadAnalytics() {
            fetch('/api/rounds')
                .then(response => response.json())
                .then(rounds => {
                    if (rounds.length > 0) {
                        currentRound = rounds[0].id;
                        loadRoundAnalytics(currentRound);
                    }
                });
        }

        // Load specific round analytics
        function loadRoundAnalytics(roundId) {
            // Load comprehensive report
            fetch(`/api/analytics/${roundId}/report`)
                .then(response => response.json())
                .then(report => {
                    updateMetricsTable(report);
                    updateInsights(report);
                })
                .catch(error => console.error('Error loading analytics:', error));

            // Load visualizations
            ['confidence_distribution', 'accuracy_heatmap', 'token_scatter'].forEach(vizType => {
                fetch(`/api/analytics/${roundId}/${vizType}`)
                    .then(response => response.json())
                    .then(data => {
                        // Display visualization (simplified for demo)
                        console.log(`${vizType} data:`, data);
                    })
                    .catch(error => console.error(`Error loading ${vizType}:`, error));
            });
        }

        // Update metrics table
        function updateMetricsTable(report) {
            const summary = report.sections?.summary || {};
            const fpRate = summary.fp_detection_rate || 0;
            const protectionRate = summary.protection_rate || 0;
            const avgTime = summary.processing_metrics?.avg_processing_time || 0;
            const totalCases = summary.total_cases || 0;

            document.getElementById('fp-rate').textContent = fpRate.toFixed(1) + '%';
            document.getElementById('protection-rate').textContent = protectionRate.toFixed(1) + '%';
            document.getElementById('avg-time').textContent = avgTime.toFixed(2) + 's';
            document.getElementById('total-cases').textContent = totalCases;

            // Update status indicators
            document.getElementById('fp-status').textContent = fpRate >= 70 ? '✅ Good' : '⚠️ Below Target';
            document.getElementById('protection-status').textContent = protectionRate >= 95 ? '✅ Good' : '⚠️ Below Target';
            document.getElementById('time-status').textContent = avgTime <= 5 ? '✅ Good' : '⚠️ Slow';
        }

        // Update insights
        function updateInsights(report) {
            const insights = report.sections?.insights || ['No insights available'];
            const container = document.getElementById('insights-container');
            
            container.innerHTML = '';
            insights.forEach(insight => {
                const div = document.createElement('div');
                let className = 'insight';
                if (insight.includes('⚠️')) className += ' warning';
                if (insight.includes('❌')) className += ' error';
                
                div.className = className;
                div.textContent = insight;
                container.appendChild(div);
            });
        }

        // Initialize everything
        initializeCharts();
        loadAnalytics();

        // Auto-refresh every 30 seconds
        setInterval(loadAnalytics, 30000);
    </script>
</body>
</html>