#!/usr/bin/env python3
"""
Live monitoring of 70% FP reduction achievement progress
"""

import json
import time
import os
from datetime import datetime
import subprocess

def get_round_status(round_num):
    """Get status for a specific round"""
    # Check progress files
    if round_num == 3:
        progress_file = 'valo_round3_safety_first_progress.json'
        complete_file = 'valo_batch_round3_complete.json'
    else:
        progress_file = f'round{round_num}_progress.json'
        complete_file = f'valo_round{round_num}_safe_complete.json'
    
    if os.path.exists(complete_file):
        with open(complete_file, 'r') as f:
            data = json.load(f)
            stats = data.get('stats', {})
            return {
                'status': 'complete',
                'cases': stats.get('total_cases', 0),
                'valid_prot': stats.get('valid_protection_rate', 0),
                'fp_det': stats.get('fp_detection_rate', 0)
            }
    elif os.path.exists(progress_file):
        with open(progress_file, 'r') as f:
            data = json.load(f)
            return {
                'status': 'in_progress',
                'cases': data.get('cases_processed', 0),
                'valid_prot': data.get('valid_protection_rate', 0),
                'fp_det': data.get('fp_detection_rate', 0)
            }
    
    return {'status': 'pending'}

def check_final_achievement():
    """Check if final target achieved"""
    files = [
        'VALO_70_PERCENT_ACHIEVEMENT_FINAL.json',
        'VALO_70_PERCENT_SAFE_FINAL.json'
    ]
    
    for file in files:
        if os.path.exists(file):
            with open(file, 'r') as f:
                data = json.load(f)
                if data.get('success') or data.get('target_achieved'):
                    return data
    return None

def display_live_status():
    """Display live status dashboard"""
    os.system('clear')
    
    print("="*80)
    print(f"VALO AI-FARM - LIVE 70% ACHIEVEMENT MONITOR")
    print(f"Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("="*80)
    
    # Check each round
    best_fp = 0
    current_round = 3
    
    for round_num in range(3, 11):
        status = get_round_status(round_num)
        
        if status['status'] == 'complete':
            print(f"✅ Round {round_num}: Complete | Valid: {status['valid_prot']:.1f}% | FP: {status['fp_det']:.1f}%")
            best_fp = max(best_fp, status['fp_det'])
            current_round = round_num + 1
        elif status['status'] == 'in_progress':
            progress = status['cases'] / 1250 * 100 if round_num == 3 else 100
            print(f"⏳ Round {round_num}: {progress:.0f}% | Valid: {status['valid_prot']:.1f}% | FP: {status['fp_det']:.1f}%")
            best_fp = max(best_fp, status['fp_det'])
            current_round = round_num
        else:
            print(f"⏸  Round {round_num}: Pending")
    
    print("-"*80)
    
    # Progress visualization
    print(f"\nFP Detection Progress:")
    progress_bar = int(best_fp / 70 * 50)
    filled = "█" * progress_bar
    empty = "░" * (50 - progress_bar)
    
    if best_fp >= 70:
        color = "\033[92m"  # Green
    elif best_fp >= 50:
        color = "\033[93m"  # Yellow
    else:
        color = "\033[97m"  # White
    
    print(f"{color}[{filled}{empty}] {best_fp:.1f}% / 70%\033[0m")
    
    # Estimated time
    if current_round > 3 and best_fp > 0:
        rounds_per_percent = (current_round - 3) / (best_fp - 20)  # Starting from ~20% at Round 3
        remaining_percent = max(0, 70 - best_fp)
        estimated_rounds = int(remaining_percent * rounds_per_percent) + 1
        print(f"\nEstimated rounds remaining: {estimated_rounds}")
        print(f"Estimated time to completion: {estimated_rounds * 45} minutes")
    
    # Check final achievement
    final = check_final_achievement()
    if final:
        print("\n" + "="*80)
        print("🎯 TARGET ACHIEVED!")
        print(f"Final FP Detection: {final.get('final_stats', {}).get('fp_detection_rate', 0):.1f}%")
        print(f"Rounds Required: {final.get('rounds_completed', 'N/A')}")
        print("="*80)
        return True
    
    # Check orchestration log
    if os.path.exists('orchestration_master.log'):
        # Get last 5 lines
        try:
            result = subprocess.run(['tail', '-5', 'orchestration_master.log'], 
                                  capture_output=True, text=True)
            print("\nLatest activity:")
            print(result.stdout)
        except:
            pass
    
    return False

def main():
    """Main monitoring loop"""
    print("Starting live achievement monitor...")
    print("This will track progress until 70% FP reduction is achieved")
    print("Press Ctrl+C to stop monitoring")
    
    try:
        achieved = False
        while not achieved:
            achieved = display_live_status()
            if not achieved:
                time.sleep(10)  # Update every 10 seconds
        
        print("\n✅ Monitoring complete - Target achieved!")
        print("Check VALO_70_PERCENT_ACHIEVEMENT_REPORT.md for full details")
        
    except KeyboardInterrupt:
        print("\n\nMonitoring stopped by user")
        print("The orchestration will continue running in background")
        print("Check orchestration_master.log for details")

if __name__ == "__main__":
    main()