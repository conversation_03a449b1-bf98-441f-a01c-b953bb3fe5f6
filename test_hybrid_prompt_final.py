#!/usr/bin/env python3
"""
Test hybrid prompt to achieve optimal balance:
- 100% valid violation protection (priority #1)
- 70%+ false positive detection (priority #2)
"""

import json
import base64
import requests
import os
from datetime import datetime
import random

VLM_API_URL = "http://100.106.127.35:9500/v1/chat/completions"
VLM_MODEL = "VLM-38B-AWQ"

class HybridPromptTester:
    def __init__(self):
        self.session = requests.Session()
        self.results = []
        
    def encode_image(self, image_path):
        """Encode image to base64"""
        try:
            if os.path.exists(image_path):
                with open(image_path, 'rb') as f:
                    return base64.b64encode(f.read()).decode('utf-8')
        except:
            pass
        return None
    
    def load_hybrid_prompt(self):
        """Load the hybrid safety prompt"""
        with open('hybrid_safety_prompt.txt', 'r') as f:
            return f.read()
    
    def call_vlm(self, source_b64, cropped_b64, prompt, timeout=20):
        """Call VLM with dual images"""
        payload = {
            "model": VLM_MODEL,
            "messages": [{
                "role": "user",
                "content": [
                    {"type": "text", "text": prompt},
                    {"type": "text", "text": "\n\nSOURCE IMAGE:"},
                    {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{source_b64}"}},
                    {"type": "text", "text": "\n\nCROPPED IMAGE:"},
                    {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{cropped_b64}"}}
                ]
            }],
            "temperature": 0.1,
            "max_tokens": 200
        }
        
        try:
            response = self.session.post(VLM_API_URL, json=payload, timeout=timeout)
            if response.status_code == 200:
                result = response.json()
                return result['choices'][0]['message']['content'].strip()
        except:
            pass
        return None
    
    def test_sample_cases(self, sample_size=100):
        """Test on a balanced sample of cases"""
        
        print("TESTING HYBRID PROMPT ON BALANCED SAMPLE")
        print("="*60)
        
        # Load all data
        with open('valo_batch_round3_complete.json', 'r') as f:
            batch_data = json.load(f)
        
        # Separate valid and FP cases
        valid_cases = [c for c in batch_data['results'] if not c['is_false_positive']]
        fp_cases = [c for c in batch_data['results'] if c['is_false_positive']]
        
        print(f"Total valid violations: {len(valid_cases)}")
        print(f"Total false positives: {len(fp_cases)}")
        
        # Sample balanced dataset
        sample_valid = random.sample(valid_cases, min(sample_size//2, len(valid_cases)))
        sample_fp = random.sample(fp_cases, min(sample_size//2, len(fp_cases)))
        
        test_cases = sample_valid + sample_fp
        random.shuffle(test_cases)
        
        print(f"\nTesting {len(test_cases)} cases:")
        print(f"- {len(sample_valid)} valid violations")
        print(f"- {len(sample_fp)} false positives")
        
        # Load hybrid prompt
        hybrid_prompt = self.load_hybrid_prompt()
        
        # Test each case
        for i, case in enumerate(test_cases):
            if i % 10 == 0:
                print(f"\nProgress: {i}/{len(test_cases)}")
            
            # Encode images
            source_b64 = self.encode_image(case['source_image'])
            cropped_b64 = self.encode_image(case['cropped_image'])
            
            if not source_b64 or not cropped_b64:
                continue
            
            # Call VLM
            response = self.call_vlm(source_b64, cropped_b64, hybrid_prompt)
            
            if response:
                # Parse response
                is_fp = 'YES' in response.upper().split('FALSE POSITIVE:')[1][:10] if 'FALSE POSITIVE:' in response.upper() else False
                
                # Check if correct
                correct = is_fp == case['is_false_positive']
                
                self.results.append({
                    'case_number': case['case_number'],
                    'actual_fp': case['is_false_positive'],
                    'predicted_fp': is_fp,
                    'correct': correct,
                    'violation_type': case['infringement_type'],
                    'remarks': case.get('remarks', '')
                })
    
    def analyze_results(self):
        """Analyze test results"""
        
        print("\n\n" + "="*60)
        print("HYBRID PROMPT TEST RESULTS")
        print("="*60)
        
        if not self.results:
            print("No results to analyze")
            return
        
        # Overall accuracy
        correct = sum(1 for r in self.results if r['correct'])
        accuracy = correct / len(self.results) * 100
        
        # Valid violation protection
        valid_results = [r for r in self.results if not r['actual_fp']]
        if valid_results:
            valid_protected = sum(1 for r in valid_results if not r['predicted_fp'])
            protection_rate = valid_protected / len(valid_results) * 100
            
            print(f"\nVALID VIOLATION PROTECTION:")
            print(f"Total valid cases: {len(valid_results)}")
            print(f"Correctly identified: {valid_protected}")
            print(f"Protection rate: {protection_rate:.1f}%")
            
            if protection_rate < 100:
                print("\nMissed valid violations:")
                for r in valid_results[:5]:
                    if r['predicted_fp']:
                        print(f"  - {r['case_number']}: {r['remarks'][:60]}...")
        
        # False positive detection
        fp_results = [r for r in self.results if r['actual_fp']]
        if fp_results:
            fp_detected = sum(1 for r in fp_results if r['predicted_fp'])
            fp_rate = fp_detected / len(fp_results) * 100
            
            print(f"\nFALSE POSITIVE DETECTION:")
            print(f"Total FP cases: {len(fp_results)}")
            print(f"Correctly identified: {fp_detected}")
            print(f"Detection rate: {fp_rate:.1f}%")
        
        print(f"\nOVERALL PERFORMANCE:")
        print(f"Total accuracy: {accuracy:.1f}%")
        print(f"Target achieved: {'YES' if protection_rate >= 95 and fp_rate >= 70 else 'NO'}")
        
        # Save results
        report = {
            'timestamp': datetime.now().isoformat(),
            'sample_size': len(self.results),
            'overall_accuracy': accuracy,
            'valid_protection_rate': protection_rate if valid_results else 0,
            'fp_detection_rate': fp_rate if fp_results else 0,
            'target_achieved': protection_rate >= 95 and fp_rate >= 70,
            'results': self.results
        }
        
        with open('hybrid_prompt_test_results.json', 'w') as f:
            json.dump(report, f, indent=2)
        
        print(f"\nResults saved to: hybrid_prompt_test_results.json")
        
        # Recommendations
        print("\n\nRECOMMENDATIONS:")
        if protection_rate < 95:
            print("❌ Valid protection too low - need to adjust prompt")
            print("   - Lower structure confidence threshold")
            print("   - Add more safety checks")
        elif fp_rate < 70:
            print("⚠️  FP detection below target - can optimize")
            print("   - Current prompt is good for safety")
            print("   - May need violation-specific tuning")
        else:
            print("✅ Prompt achieves both targets!")
            print("   - Ready for full 1250 case validation")

if __name__ == "__main__":
    tester = HybridPromptTester()
    tester.test_sample_cases(sample_size=100)
    tester.analyze_results()