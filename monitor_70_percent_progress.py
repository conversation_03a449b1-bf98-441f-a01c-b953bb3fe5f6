#!/usr/bin/env python3
"""
Monitor progress to 70% FP reduction with 100% valid protection
"""

import json
import time
import os
from datetime import datetime
from colorama import init, Fore, Style

init()  # Initialize colorama

def get_all_progress():
    """Get progress across all rounds"""
    progress = {}
    
    # Check Round 3 safety-first
    if os.path.exists('valo_round3_safety_first_progress.json'):
        with open('valo_round3_safety_first_progress.json', 'r') as f:
            data = json.load(f)
            progress['round3'] = {
                'cases': data.get('cases_processed', 0),
                'valid_prot': data.get('valid_protection_rate', 0),
                'fp_det': data.get('fp_detection_rate', 0),
                'status': 'in_progress' if data.get('remaining_cases', 0) > 0 else 'complete'
            }
    
    # Check completed rounds
    for round_num in range(3, 11):
        complete_file = f'valo_round{round_num}_safe_complete.json'
        if os.path.exists(complete_file):
            with open(complete_file, 'r') as f:
                data = json.load(f)
                stats = data.get('stats', {})
                progress[f'round{round_num}'] = {
                    'cases': 1250,
                    'valid_prot': stats.get('valid_protection_rate', 0),
                    'fp_det': stats.get('fp_detection_rate', 0),
                    'status': 'complete'
                }
    
    # Check final results
    if os.path.exists('VALO_70_PERCENT_SAFE_FINAL.json'):
        with open('VALO_70_PERCENT_SAFE_FINAL.json', 'r') as f:
            data = json.load(f)
            progress['final'] = data
    
    return progress

def display_dashboard():
    """Display progress dashboard"""
    os.system('clear' if os.name == 'posix' else 'cls')
    
    print(f"{Fore.CYAN}{'='*70}{Style.RESET_ALL}")
    print(f"{Fore.CYAN}VALO AI-FARM - 70% FP REDUCTION WITH 100% SAFETY{Style.RESET_ALL}")
    print(f"{Fore.CYAN}Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}{Style.RESET_ALL}")
    print(f"{Fore.CYAN}{'='*70}{Style.RESET_ALL}")
    
    progress = get_all_progress()
    
    # Display rounds
    best_fp = 0
    for round_num in range(3, 11):
        round_key = f'round{round_num}'
        if round_key in progress:
            data = progress[round_key]
            
            # Status icon
            if data['status'] == 'complete':
                status = f"{Fore.GREEN}✓{Style.RESET_ALL}"
            else:
                status = f"{Fore.YELLOW}⏳{Style.RESET_ALL}"
            
            # Valid protection color
            if data['valid_prot'] == 100:
                valid_color = Fore.GREEN
            elif data['valid_prot'] >= 95:
                valid_color = Fore.YELLOW
            else:
                valid_color = Fore.RED
            
            # FP detection color
            if data['fp_det'] >= 70:
                fp_color = Fore.GREEN
            elif data['fp_det'] >= 50:
                fp_color = Fore.YELLOW
            else:
                fp_color = Fore.WHITE
            
            print(f"{status} Round {round_num}: "
                  f"Valid Protection: {valid_color}{data['valid_prot']:.1f}%{Style.RESET_ALL} | "
                  f"FP Detection: {fp_color}{data['fp_det']:.1f}%{Style.RESET_ALL}")
            
            if data['fp_det'] > best_fp:
                best_fp = data['fp_det']
        else:
            print(f"⏸  Round {round_num}: Pending")
    
    print(f"{Fore.CYAN}{'-'*70}{Style.RESET_ALL}")
    
    # Progress bars
    print(f"\nValid Protection Target: 100%")
    valid_bar = "█" * int(100/2) if best_fp > 0 else ""
    print(f"[{Fore.GREEN}{valid_bar:<50}{Style.RESET_ALL}] 100%")
    
    print(f"\nFP Detection Target: 70%")
    fp_progress = int(best_fp / 70 * 50)
    fp_bar = "█" * fp_progress
    remaining = "░" * (50 - fp_progress)
    color = Fore.GREEN if best_fp >= 70 else Fore.YELLOW
    print(f"[{color}{fp_bar}{Style.RESET_ALL}{remaining}] {best_fp:.1f}% / 70%")
    
    # Final status
    print(f"\n{Fore.CYAN}{'-'*70}{Style.RESET_ALL}")
    if 'final' in progress:
        final = progress['final']
        if final['target_achieved'] and final['safety_maintained']:
            print(f"{Fore.GREEN}🎯 SUCCESS! Target achieved with 100% safety!{Style.RESET_ALL}")
            print(f"Final FP Detection: {final['final_stats']['fp_detection_rate']:.1f}%")
            print(f"Rounds needed: {final['rounds_completed']}")
        else:
            print(f"{Fore.RED}Target not yet achieved{Style.RESET_ALL}")
    else:
        gap = 70 - best_fp
        print(f"Current Best: {best_fp:.1f}% | Gap to target: {gap:.1f}%")
        print(f"Estimated rounds remaining: {int(gap/15) + 1}")

def main():
    """Main monitoring loop"""
    print("Starting 70% FP reduction monitor...")
    print("Press Ctrl+C to stop")
    
    try:
        while True:
            display_dashboard()
            time.sleep(5)
            
            # Check if target achieved
            if os.path.exists('VALO_70_PERCENT_SAFE_FINAL.json'):
                with open('VALO_70_PERCENT_SAFE_FINAL.json', 'r') as f:
                    final = json.load(f)
                    if final['target_achieved']:
                        display_dashboard()
                        print(f"\n{Fore.GREEN}✅ Monitoring complete - Target achieved!{Style.RESET_ALL}")
                        break
    except KeyboardInterrupt:
        print(f"\n{Fore.YELLOW}Monitoring stopped by user{Style.RESET_ALL}")

if __name__ == "__main__":
    main()