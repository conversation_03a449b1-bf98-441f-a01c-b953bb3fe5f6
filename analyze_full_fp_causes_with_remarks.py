#!/usr/bin/env python3
"""
Analyze full dataset for top 5 root causes of false positives with example remarks
"""

import pandas as pd
import re
from collections import Counter, defaultdict

# Read the entire CSV file
df = pd.read_csv('./ai_farm_images_fixed_250703/psa_valo_violation_report_without_images_csv_2025-07-03_14-14-01.CSV')

print('ANALYZING FULL DATASET:')
print('='*80)
print(f'Total cases analyzed: {len(df):,}')
print(f'Total false positives: {len(df[df["Alert Status"] == "Invalid"]):,}')
print(f'False positive rate: {len(df[df["Alert Status"] == "Invalid"])/len(df)*100:.1f}%')

# Analyze ALL false positive cases
fp_df = df[df['Alert Status'] == 'Invalid'].copy()

# Extract all remarks for comprehensive analysis
all_remarks = []
violation_type_remarks = defaultdict(list)
remark_to_original = {}  # Map uppercase to original remarks

for idx, row in fp_df.iterrows():
    remark = str(row['Remarks']).strip()
    violation_type = row['Type of Infringement']
    if remark and remark != 'nan':
        all_remarks.append(remark.upper())
        violation_type_remarks[violation_type].append(remark.upper())
        remark_to_original[remark.upper()] = remark  # Store original

print(f'\nTotal remarks analyzed: {len(all_remarks):,}')

# Define comprehensive patterns for root cause analysis
root_cause_patterns = {
    'Workers in Full PPE': [
        r'FULL PPE', r'PROPER PPE', r'IN FULL PPE', r'WEARING PPE', 
        r'WITH PPE', r'COMPLETE PPE', r'PPE AT WHARF'
    ],
    'Equipment/Structure Misidentification': [
        r'STRUCTURE CAPTURED', r'CRANE STRUCTURE', r'VESSEL STRUCTURE', 
        r'PM STRUCTURE', r'SPREADER', r'EQUIPMENT', r'MACHINE', 
        r'FLIPPER', r'WRONGLY CAPTURED', r'CAPTURED AS LS', r'BARGE STRUCTURE'
    ],
    'Multiple Workers Present': [
        r'2 LS', r'TWO LS', r'BOTH LS', r'2 PERSON', r'TWO PERSON',
        r'MULTIPLE', r'TEAM', r'TOGETHER', r'PARTNER'
    ],
    'Location Context Issues': [
        r'AT WHARF', r'STANDING', r'WALKING', r'GOING TO', r'NOT DOING',
        r'NOT AT', r'AWAY FROM', r'BOARD', r'WORKING BAY'
    ],
    'Camera/System Issues': [
        r'NO CAMERA', r'NO FOOTAGE', r'CAMERA ANGLE', r'BLURRED', 
        r'UNCLEAR', r'POOR QUALITY', r'VISIBILITY', r'OBSTRUCT'
    ]
}

# Count occurrences and collect examples for each root cause
root_cause_data = {}
for cause, patterns in root_cause_patterns.items():
    matched_remarks = set()
    example_remarks = []
    for pattern in patterns:
        for remark in all_remarks:
            if re.search(pattern, remark) and remark not in matched_remarks:
                matched_remarks.add(remark)
                # Get original remark
                original = remark_to_original.get(remark, remark)
                if len(example_remarks) < 5 and original not in example_remarks:  # Collect up to 5 unique examples
                    example_remarks.append(original)
    
    root_cause_data[cause] = {
        'count': len(matched_remarks),
        'examples': example_remarks
    }

print('\n' + '='*80)
print('TOP 5 ROOT CAUSES OF FALSE POSITIVES (ENTIRE DATASET):')
print('='*80)

sorted_causes = sorted(root_cause_data.items(), key=lambda x: x[1]['count'], reverse=True)
for i, (cause, data) in enumerate(sorted_causes[:5], 1):
    count = data['count']
    percentage = (count / len(fp_df)) * 100
    print(f'\n{i}. {cause.upper()}')
    print(f'   Occurrences: {count:,} cases ({percentage:.1f}% of all false positives)')
    
    # Show example remarks
    print(f'   Example remarks:')
    for j, example in enumerate(data['examples'][:5], 1):
        print(f'     {j}. "{example}"')
    
    # Show breakdown by violation type
    print(f'   Breakdown by violation type:')
    for vtype in violation_type_remarks.keys():
        type_count = 0
        type_remarks = violation_type_remarks[vtype]
        matched_type_remarks = set()
        for pattern in root_cause_patterns[cause]:
            for remark in type_remarks:
                if re.search(pattern, remark) and remark not in matched_type_remarks:
                    type_count += 1
                    matched_type_remarks.add(remark)
        if type_count > 0:
            type_fp_count = len([r for _, r in fp_df.iterrows() if r['Type of Infringement'] == vtype])
            type_percentage = (type_count / type_fp_count) * 100
            print(f'     - {vtype}: {type_count} cases ({type_percentage:.1f}%)')

# Additional detailed analysis
print('\n\n' + '='*80)
print('DETAILED INSIGHTS FROM FULL DATASET ANALYSIS:')
print('='*80)

# Analyze specific patterns with examples
specific_patterns_data = {
    'Full PPE compliance': {
        'pattern': r'FULL PPE|PROPER PPE',
        'examples': []
    },
    'Crane/vessel structure': {
        'pattern': r'CRANE|VESSEL|STRUCTURE',
        'examples': []
    },
    'LS/Worker at wharf': {
        'pattern': r'AT WHARF',
        'examples': []
    },
    'Life jacket worn': {
        'pattern': r'LIFE JACKET|LIFEJACKET',
        'examples': []
    },
    'No actual violation': {
        'pattern': r'NO .* DOING|NOT DOING',
        'examples': []
    }
}

# Collect examples for specific patterns
for pattern_name, pattern_data in specific_patterns_data.items():
    pattern = pattern_data['pattern']
    count = 0
    for remark in all_remarks:
        if re.search(pattern, remark):
            count += 1
            original = remark_to_original.get(remark, remark)
            if len(pattern_data['examples']) < 3 and original not in pattern_data['examples']:
                pattern_data['examples'].append(original)
    pattern_data['count'] = count

print('\nSpecific Pattern Analysis:')
for pattern_name, data in sorted(specific_patterns_data.items(), key=lambda x: x[1]['count'], reverse=True):
    count = data['count']
    print(f'\n- {pattern_name}: {count:,} cases ({count/len(fp_df)*100:.1f}%)')
    print(f'  Examples:')
    for j, example in enumerate(data['examples'], 1):
        print(f'    {j}. "{example}"')

# Time-based analysis
print('\n\nTIME-BASED ANALYSIS:')
print('-'*50)
fp_df['Alert Start Time'] = pd.to_datetime(fp_df['Alert Start Time'], errors='coerce')
fp_df['Hour'] = fp_df['Alert Start Time'].dt.hour

hour_counts = fp_df['Hour'].value_counts().sort_index()
print('False positives by hour of day:')
peak_hours = hour_counts.nlargest(5)
for hour, count in peak_hours.items():
    print(f'- {hour:02d}:00: {count} cases ({count/len(fp_df)*100:.1f}%)')

# Violation-specific insights with examples
print('\n\nVIOLATION-SPECIFIC ROOT CAUSES WITH EXAMPLE REMARKS:')
print('='*80)

for vtype in ['PPE Non-compliance', 'One man Lashing', 'Ex.Row Violation', '2-Container Distance']:
    print(f'\n{vtype}:')
    type_df = fp_df[fp_df['Type of Infringement'] == vtype]
    type_remarks = type_df['Remarks'].dropna().tolist()
    
    # Count specific issues and collect examples
    issues_examples = {
        'Workers wearing full PPE': [],
        'Equipment misidentified': [],
        'Multiple workers': [],
        'Location context': [],
        'Camera issues': []
    }
    
    patterns_map = {
        'Workers wearing full PPE': r'FULL PPE|PROPER PPE',
        'Equipment misidentified': r'STRUCTURE|EQUIPMENT|CRANE|VESSEL',
        'Multiple workers': r'2 LS|TWO|BOTH',
        'Location context': r'AT WHARF|STANDING|NOT DOING',
        'Camera issues': r'NO CAMERA|FOOTAGE'
    }
    
    for issue, pattern in patterns_map.items():
        count = 0
        for remark in type_remarks:
            if re.search(pattern, str(remark).upper()):
                count += 1
                if len(issues_examples[issue]) < 2:  # Collect 2 examples per issue
                    issues_examples[issue].append(remark)
        
        total = len(type_remarks)
        if count > 0:
            percentage = count/total*100
            print(f'  - {issue}: {count} ({percentage:.1f}%)')
            for ex in issues_examples[issue]:
                print(f'    Example: "{ex}"')

# Most common exact remarks
print('\n\nMOST COMMON EXACT REMARKS:')
print('='*80)
remark_counts = Counter(fp_df['Remarks'].dropna())
for remark, count in remark_counts.most_common(10):
    print(f'- "{remark}": {count} occurrences ({count/len(fp_df)*100:.1f}%)')

print('\n' + '='*80)