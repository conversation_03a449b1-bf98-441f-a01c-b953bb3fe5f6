#!/usr/bin/env python3
"""
Get detailed test progress
"""

import subprocess
import time

# Get process PID
result = subprocess.run(['pgrep', '-f', 'test_multiple_approaches'], capture_output=True, text=True)
if result.stdout:
    pid = result.stdout.strip()
    print(f"Test process PID: {pid}")
    
    # Check process status
    subprocess.run(['ps', '-p', pid, '-o', 'pid,etime,pcpu,pmem,cmd'])
    
    # Get recent output if possible
    print("\nChecking for any output files...")
    subprocess.run(['ls', '-la', '*.json', '*.txt'], shell=True)
else:
    print("Test process not found")

# Show current time
print(f"\nCurrent time: {time.strftime('%H:%M:%S')}")
print("The test should save progress every 10 cases.")
print("Estimated time per approach: 30-45 minutes")
print("\nRun 'python3 check_test_status.py' periodically to check progress.")