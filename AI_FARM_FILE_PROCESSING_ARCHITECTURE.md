# AI-FARM File Upload and Processing Architecture

## Executive Summary

AI-FARM implements a sophisticated file upload and processing architecture designed to handle large-scale safety violation data analysis. The system processes CSV case data and violation images through a Vision Language Model (VLM) pipeline to reduce false positive alerts by 70%.

### Key Architectural Strengths
- **Scalable Design**: Handles files up to 1GB with efficient memory management
- **Production-Ready**: Comprehensive error handling, logging, and monitoring
- **Real-time Processing**: Asynchronous processing with live progress tracking
- **Security-First**: Multiple validation layers and secure file handling
- **Cache-Optimized**: Redis caching for VLM results and duplicate detection

## System Architecture Overview

### Technology Stack

#### Frontend (React TypeScript)
- **React 18.2.0** with TypeScript for type safety
- **React Dropzone** for drag-and-drop file uploads
- **Axios** for API communication with extended timeout support
- **Recharts** for data visualization
- **Tailwind CSS** for responsive design

#### Backend (FastAPI Python)
- **FastAPI 0.115.0** for high-performance API
- **Pandas 2.2.3** for CSV processing and analysis
- **Pillow 11.0.0** for image processing and compression
- **Redis 5.2.0** for caching and session management
- **SQLAlchemy 2.0.36** for database operations

#### Infrastructure
- **PostgreSQL/SQLite** for data persistence
- **Redis** for caching and real-time data
- **Docker** for containerized deployment
- **Uvicorn** for ASGI server implementation

### Data Flow Architecture

```
1. File Upload (Frontend)
   ├── Drag & Drop Interface (react-dropzone)
   ├── File Validation (type, size, format)
   ├── Progress Tracking (simulated + real)
   └── FormData Construction

2. API Processing (Backend)
   ├── Multipart Upload Handler (/api/v1/batch/upload)
   ├── Temporary File Storage (/tmp/ai_farm_uploads)
   ├── Background Task Spawning
   └── Immediate Response (batch_id)

3. Data Processing Pipeline
   ├── CSV Parsing (Pandas)
   ├── Image Extraction (ZIP handling)
   ├── VLM Analysis (Mini-batch processing)
   ├── Redis Caching (Result optimization)
   └── Database Storage (PostgreSQL)

4. Results Presentation
   ├── Status Polling (Real-time updates)
   ├── Data Visualization (Charts/graphs)
   ├── Export Functionality (JSON/CSV)
   └── ROI Calculations
```

## File Processing Components

### 1. Frontend Upload System

#### FileUpload Component (`/frontend/src/components/ui/FileUpload.tsx`)
- **Dual Upload Interface**: Supports both CSV case data and ZIP image archives
- **Real-time Validation**: Client-side validation with immediate feedback
- **Progress Tracking**: Visual progress bars with status indicators
- **Error Handling**: Comprehensive error messages and recovery guidance

**Key Features**:
- File size limits: CSV (50MB), ZIP (1GB)
- Format validation: CSV text files, ZIP archives
- Drag-and-drop with click-to-browse fallback
- Preview functionality with file metadata

### 2. Backend Processing Pipeline

#### Batch Processing Service (`/backend/app/services/batch_processor.py`)
- **CSV Processing**: Pandas-based parsing with error recovery
- **Image Processing**: PIL compression and optimization
- **VLM Integration**: Asynchronous API calls with rate limiting
- **Database Operations**: Transactional data storage

**Processing Flow**:
1. **Input Validation**: File type, size, and format checks
2. **Temporary Storage**: Secure isolated directory creation
3. **CSV Analysis**: Column validation and data quality checks
4. **Image Extraction**: ZIP file handling with security validation
5. **VLM Analysis**: Batch processing with confidence scoring
6. **Result Storage**: Database persistence with caching

### 3. Data Transformation Engine

#### CSV Data Processing
- **Required Columns**: `pk_event`, `case_number`, `url`, `key`
- **Data Validation**: Type checking, format validation, integrity checks
- **Error Recovery**: Malformed row skipping with detailed logging
- **Mathematical Processing**: Case number to pk_event conversion

#### Image Processing Pipeline
- **Format Support**: JPG, PNG, BMP, TIFF formats
- **Compression**: Automatic optimization (max 1920px, 85% quality)
- **Base64 Encoding**: VLM API transmission format
- **Metadata Extraction**: Size, dimensions, processing time

### 4. VLM Integration Service

#### External API Integration (`/backend/app/services/vlm_service.py`)
- **OpenAI-Compatible API**: VLM-38B-AWQ model integration
- **Concurrent Processing**: Semaphore-based rate limiting (3 concurrent)
- **Result Caching**: Redis-based duplicate detection
- **Error Handling**: Comprehensive retry and fallback mechanisms

**Cache Strategy**:
- **Key Generation**: SHA256(image + model + prompt)
- **TTL Management**: 1-hour default expiration
- **Hit Rate Optimization**: Duplicate image detection

## Security and Validation

### File Upload Security
- **MIME Type Validation**: Server-side file type verification
- **Size Limits**: Configurable maximum file sizes
- **Path Security**: Directory traversal prevention
- **Temporary Storage**: Isolated processing directories

### Data Validation
- **CSV Structure**: Required column validation
- **Image Formats**: Whitelist-based format checking
- **ZIP Security**: Content inspection and filtering
- **Input Sanitization**: SQL injection and XSS prevention

### Error Handling Strategy
- **Multi-layer Validation**: Client and server-side checks
- **Graceful Degradation**: Continued processing despite individual failures
- **Comprehensive Logging**: Detailed error tracking and monitoring
- **Recovery Mechanisms**: Retry logic and fallback procedures

## Performance Optimization

### Memory Management
- **Mini-batch Processing**: Configurable batch sizes (default: 10 cases)
- **Image Compression**: Automatic size optimization
- **Streaming Processing**: Large file handling without full memory loading
- **Automatic Cleanup**: Temporary file removal after processing

### Caching Strategy
- **Redis Implementation**: VLM result caching with TTL
- **Database Optimization**: Indexed queries for fast retrieval
- **Client-side Caching**: React Query for API response caching
- **CDN Integration**: Static asset optimization

### Concurrency Control
- **Async Processing**: FastAPI async/await pattern
- **Rate Limiting**: VLM API request throttling
- **Background Tasks**: Non-blocking processing with Celery-style tasks
- **Database Pooling**: Connection management for high throughput

## Monitoring and Observability

### Health Checks
- **Comprehensive Monitoring**: Database, Redis, VLM API status
- **Uptime Tracking**: Service availability metrics
- **Performance Metrics**: Processing time, throughput, error rates
- **Resource Monitoring**: Memory, CPU, disk usage

### Logging Strategy
- **Structured Logging**: JSON-formatted logs with correlation IDs
- **Error Tracking**: Comprehensive exception handling
- **Performance Logging**: Request timing and bottleneck identification
- **Audit Trails**: User action and data processing logs

## Configuration Management

### Environment Variables (`/backend/app/core/config.py`)
- **VLM API Configuration**: Endpoint, credentials, model settings
- **Processing Parameters**: Batch sizes, timeouts, thresholds
- **Storage Configuration**: Paths, retention policies, cleanup settings
- **Security Settings**: Authentication, validation, encryption

### Deployment Configuration
- **Docker Compose**: Multi-service orchestration
- **Environment Profiles**: Development, staging, production
- **Scaling Configuration**: Worker processes, concurrency limits
- **Resource Limits**: Memory, CPU, storage constraints

## Integration Points

### External Services
- **VLM API**: Vision Language Model for image analysis
- **Redis**: Caching and session management
- **Database**: PostgreSQL for production, SQLite for development
- **File Storage**: Local filesystem with optional cloud integration

### API Endpoints
- **Upload**: `/api/v1/batch/upload` - File upload and processing
- **Status**: `/api/v1/batch/{batch_id}` - Processing status
- **Results**: `/api/v1/batch/{batch_id}/results` - Paginated results
- **Export**: `/api/v1/metrics/export` - Data export functionality

## Development Workflows

### Local Development
```bash
# Install dependencies
npm run install:all

# Start development servers
npm run dev  # Starts both frontend and backend

# Run tests
npm run test:backend  # Python tests
npm run test:frontend  # React tests
npm run test:e2e  # End-to-end tests
```

### Production Deployment
```bash
# Build for production
npm run build

# Start production services
npm run start:production

# Health check
curl http://localhost:8001/health
```

## Best Practices and Recommendations

### Code Quality
- **Type Safety**: Comprehensive TypeScript and Python type hints
- **Error Handling**: Defensive programming with detailed error messages
- **Testing**: Unit, integration, and end-to-end test coverage
- **Documentation**: Inline comments and comprehensive API docs

### Security
- **Input Validation**: Multi-layer validation with sanitization
- **Authentication**: JWT-based API authentication
- **Authorization**: Role-based access control
- **Data Protection**: Encryption at rest and in transit

### Performance
- **Caching**: Multi-level caching strategy
- **Database Optimization**: Indexed queries and connection pooling
- **Image Processing**: Efficient compression and format conversion
- **API Design**: RESTful endpoints with proper HTTP methods

## Future Enhancements

### Scalability Improvements
- **Horizontal Scaling**: Multi-instance deployment
- **Message Queues**: Redis/RabbitMQ for background processing
- **Database Sharding**: Horizontal database scaling
- **CDN Integration**: Global content delivery

### Feature Enhancements
- **Real-time Updates**: WebSocket implementation
- **Advanced Analytics**: ML-powered insights
- **Multi-format Support**: Additional file format support
- **Cloud Integration**: AWS S3, Google Cloud Storage

This architecture provides a robust, scalable foundation for AI-FARM's file processing capabilities while maintaining security, performance, and maintainability standards.