# MCP (Model Context Protocol) Setup Guide for AI-FARM

This guide explains how to configure and use MCP servers with Claude Code for the AI-FARM project.

## 🚀 Quick Setup

Run the automated setup script:
```bash
./setup-mcp-servers.sh
```

This will configure three MCP servers:
1. **AI-FARM Puppeteer** - Web automation for testing
2. **Sequential Thinking** - Step-by-step reasoning
3. **Context7** - Context management

## 📦 Configured MCP Servers

### 1. AI-FARM Puppeteer MCP Server
**Purpose**: Web automation, testing, and screenshot capture for AI-FARM

**Available Tools**:
- `screenshot_url` - Capture webpage screenshots
- `scrape_violation_data` - Extract violation data from web pages
- `automate_valo_interface` - Automate VALO UI interactions
- `generate_demo_pdf` - Generate PDF reports from web pages
- `capture_violation_images` - Batch capture violation images

**Usage Example**:
```
Use the screenshot_url tool to capture the AI-FARM dashboard at http://localhost:3000/dashboard
```

### 2. Sequential Thinking MCP Server
**Purpose**: Enhanced reasoning and step-by-step problem solving

**Capabilities**:
- Break down complex problems
- Plan multi-step solutions
- Analyze dependencies
- Create structured approaches

**Usage Example**:
```
Think through the steps needed to implement a new feature in AI-FARM
```

### 3. Context7 MCP Server
**Purpose**: Advanced context management and memory

**Capabilities**:
- Maintain conversation context
- Track project state
- Remember previous decisions
- Provide relevant historical information

## 🔧 Manual Configuration

If you need to configure manually, create/edit `~/.config/claude/claude_desktop_config.json`:

```json
{
  "mcpServers": {
    "ai-farm-puppeteer": {
      "command": "node",
      "args": ["/home/<USER>/VALO_AI-FARM_2025/VALO_AI-FARM_2025/mcp-server/dist/index.js"],
      "env": {
        "NODE_ENV": "production"
      }
    },
    "sequential-thinking": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"]
    },
    "context7": {
      "command": "npx",
      "args": ["-y", "@context-labs/context-mcp"]
    }
  }
}
```

## 🧪 Testing with MCP Puppeteer

### Using MCP Tools in Claude Code

After configuration, you can use MCP tools directly in Claude Code:

1. **Take Screenshots**:
   ```
   Use the ai-farm-puppeteer screenshot_url tool to capture http://localhost:3000
   ```

2. **Scrape Data**:
   ```
   Use the ai-farm-puppeteer scrape_violation_data tool on http://localhost:8000/api/metrics
   ```

3. **Generate Reports**:
   ```
   Use the ai-farm-puppeteer generate_demo_pdf tool to create a PDF report of the dashboard
   ```

### Running E2E Tests with MCP

The AI-FARM project includes MCP-integrated tests:

```bash
# Start the application
./ai-farm.sh start

# Run MCP-based tests
./run-mcp-tests.sh
```

## 📁 Project Structure

```
VALO_AI-FARM_2025/
├── mcp-server/              # AI-FARM Puppeteer MCP server
│   ├── src/
│   │   └── index.ts         # MCP server implementation
│   ├── dist/
│   │   └── index.js         # Compiled server
│   └── package.json
├── setup-mcp-servers.sh     # Automated setup script
├── test-with-mcp.js         # MCP-based test suite
├── run-mcp-tests.sh         # MCP test runner
└── MCP_SETUP_GUIDE.md       # This file
```

## 🔍 Verifying Configuration

1. **Check if MCP servers are configured**:
   ```bash
   # In Claude Code terminal
   claude mcp
   ```

2. **View current configuration**:
   ```bash
   cat ~/.config/claude/claude_desktop_config.json
   ```

3. **Test AI-FARM Puppeteer server**:
   ```bash
   # The setup script offers to test the server
   ./setup-mcp-servers.sh
   ```

## 🛠️ Troubleshooting

### MCP Server Not Available
1. Restart Claude Code after configuration
2. Ensure the MCP server is built:
   ```bash
   cd mcp-server
   npm install
   npm run build
   ```

### Tools Not Working
1. Check if services are running:
   ```bash
   ./ai-farm.sh status
   ```
2. Verify MCP server path in configuration
3. Check logs for errors

### Configuration Not Loading
1. Ensure config file is in correct location: `~/.config/claude/claude_desktop_config.json`
2. Validate JSON syntax
3. Restart Claude Code

## 🎯 Use Cases

### 1. Automated Testing
Use AI-FARM Puppeteer MCP to:
- Capture screenshots of all pages
- Verify UI elements exist
- Test user workflows
- Generate test reports

### 2. Documentation
- Generate PDF documentation of the live application
- Capture UI states for documentation
- Create visual test reports

### 3. Monitoring
- Scrape metrics data
- Monitor application health
- Capture error states

### 4. Development Workflow
- Use Sequential Thinking for planning features
- Use Context7 to remember project decisions
- Use AI-FARM Puppeteer for visual regression testing

## 📚 Additional Resources

- [MCP Documentation](https://modelcontextprotocol.io/)
- [Puppeteer Documentation](https://pptr.dev/)
- [AI-FARM E2E Testing Guide](./E2E_TESTING_GUIDE.md)
- [AI-FARM MCP Server README](./mcp-server/README.md)

## ✅ Next Steps

1. Restart Claude Code to load the MCP servers
2. Try using the AI-FARM Puppeteer tools
3. Run the E2E tests with `./run-mcp-tests.sh`
4. Explore Sequential Thinking and Context7 capabilities

Happy testing with MCP! 🚀