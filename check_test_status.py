#!/usr/bin/env python3
"""
Quick status check for comprehensive test
"""

import json
import os
from datetime import datetime

def check_status():
    print("\n" + "="*60)
    print(f"TEST STATUS - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("="*60)
    
    # Check if test is running
    if os.system("pgrep -f test_multiple_approaches > /dev/null") == 0:
        print("✓ Test is RUNNING")
    else:
        print("✗ Test is NOT running")
    
    # Check progress
    if os.path.exists('multi_approach_progress.json'):
        with open('multi_approach_progress.json', 'r') as f:
            progress = json.load(f)
        
        results = progress.get('results', {})
        
        if results:
            print(f"\nCompleted approaches: {len(results)}/4")
            
            for approach_key, result in results.items():
                print(f"\n✓ {result['name']}:")
                metrics = result['metrics']
                print(f"   Accuracy: {metrics['accuracy']:.1f}%")
                print(f"   FP Detection: {metrics['fp_detection']:.1f}%")
                print(f"   Valid Protection: {metrics['valid_protection']:.1f}%")
                print(f"   Token Limit: {result['config']['max_tokens']}")
        else:
            print("\nNo approaches completed yet. Test in progress...")
            
            # Try to estimate progress from log
            if os.path.exists('comprehensive_test_log.txt'):
                os.system('tail -5 comprehensive_test_log.txt | grep Progress')
    
    # Check for completion
    if os.path.exists('multi_approach_comparison_report.json'):
        print("\n" + "="*60)
        print("✅ TEST COMPLETE!")
        print("="*60)
        
        with open('multi_approach_comparison_report.json', 'r') as f:
            report = json.load(f)
        
        print(f"\nBest approach: {report['best_approach']}")
        print("\nConclusions:")
        for conclusion in report['conclusions']:
            print(f"  • {conclusion}")

if __name__ == "__main__":
    check_status()