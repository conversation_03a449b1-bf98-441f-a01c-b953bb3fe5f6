# Contributing to AI-FARM

Thank you for your interest in contributing to AI-FARM! This document provides guidelines for contributing to the project.

## 🚀 Quick Start for Contributors

### Development Setup
1. **Clone and install**:
   ```bash
   git clone <repository-url>
   cd ai-farm
   npm run install:all
   ```

2. **Configure environment**:
   ```bash
   cp .env.example .env
   # Edit .env with your VLM API credentials
   ```

3. **Start development environment**:
   ```bash
   npm run dev
   ```

## 📋 Development Workflow

### Branch Strategy
- `main` - Production-ready code
- `develop` - Integration branch for features
- `feature/*` - New features
- `bugfix/*` - Bug fixes
- `hotfix/*` - Critical production fixes

### Making Changes
1. **Create a feature branch**:
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. **Make your changes** following our coding standards

3. **Test your changes**:
   ```bash
   npm run test              # All tests
   npm run test:backend      # Backend only
   npm run test:frontend     # Frontend only
   npm run test:e2e          # End-to-end tests
   ```

4. **Commit your changes**:
   ```bash
   git commit -m "feat: add new feature description"
   ```

### Commit Message Convention
We follow [Conventional Commits](https://www.conventionalcommits.org/):

- `feat:` - New features
- `fix:` - Bug fixes
- `docs:` - Documentation changes
- `style:` - Code style changes (no logic changes)
- `refactor:` - Code refactoring
- `test:` - Adding or updating tests
- `chore:` - Maintenance tasks

Examples:
```bash
feat: add VLM confidence threshold adjustment
fix: resolve image upload timeout issue
docs: update API documentation for batch processing
refactor: reorganize backend service layer
```

## 🧪 Testing Requirements

### Before Submitting PR
- [ ] All existing tests pass (`npm run test`)
- [ ] New features include unit tests
- [ ] Integration tests pass for API changes
- [ ] E2E tests pass for UI changes
- [ ] No TypeScript/ESLint errors
- [ ] Code follows project formatting standards

### Test Coverage
- Backend: Maintain >80% test coverage
- Frontend: Test all new components
- Integration: Test all new API endpoints
- E2E: Test critical user workflows

## 📝 Code Standards

### Backend (Python)
- Follow PEP 8 style guide
- Use type hints for all functions
- Document all functions with docstrings
- Use Pydantic models for data validation
- Follow SQLAlchemy best practices

### Frontend (TypeScript/React)
- Use TypeScript for type safety
- Follow React functional component patterns
- Use custom hooks for reusable logic
- Follow Tailwind CSS conventions
- Write component tests for new UI

### API Design
- Follow RESTful conventions
- Use appropriate HTTP status codes
- Validate all inputs
- Document all endpoints in OpenAPI
- Handle errors gracefully

## 🏗️ Architecture Guidelines

### Backend Structure
```
backend/app/
├── api/           # REST endpoints
├── core/          # Configuration & database
├── models/        # SQLAlchemy models
├── schemas/       # Pydantic schemas
├── services/      # Business logic
└── utils/         # Utilities
```

### Frontend Structure
```
frontend/src/
├── components/    # Reusable UI components
├── pages/         # Application pages
├── services/      # API integration
├── hooks/         # Custom React hooks
├── types/         # TypeScript definitions
└── utils/         # Frontend utilities
```

## 🚀 Development Commands

### Most Common Commands
```bash
npm run dev                 # Start development environment
npm run test               # Run all tests
npm run build              # Build for production
npm run docker:up          # Start with Docker
```

### Utility Commands
```bash
npm run install:all        # Install all dependencies
npm run clean              # Clean caches and node_modules
npm run mcp:setup          # Setup MCP servers
```

## 🔍 Code Review Process

### Before Requesting Review
1. **Self-review your changes**
2. **Test thoroughly** - run all relevant tests
3. **Update documentation** if needed
4. **Check for breaking changes**
5. **Ensure clean commit history**

### Review Criteria
- [ ] Code follows project standards
- [ ] Tests are comprehensive
- [ ] Documentation is updated
- [ ] No security vulnerabilities
- [ ] Performance considerations addressed
- [ ] Backwards compatibility maintained

## 🐛 Issue Reporting

### Bug Reports
Include:
- Clear description of the issue
- Steps to reproduce
- Expected vs actual behavior
- Environment details (OS, Node version, etc.)
- Screenshots/logs if applicable

### Feature Requests
Include:
- Clear description of the feature
- Use case and business value
- Proposed implementation approach
- Alternatives considered

## 📚 Documentation

### Required Documentation
- All new features need documentation updates
- API changes require OpenAPI spec updates
- Complex algorithms need inline comments
- Update CLAUDE.md for development guidance changes

### Documentation Locations
- `README.md` - Project overview and quick start
- `CLAUDE.md` - Development guidance and standards
- `docs/` - Comprehensive project documentation
- Inline code comments for complex logic

## 🛡️ Security Guidelines

### Security Considerations
- Never commit API keys or secrets
- Validate all user inputs
- Use parameterized queries for database operations
- Follow OWASP security practices
- Report security issues privately

### Sensitive Data
- Customer data must be handled securely
- Temporary storage only for demo purposes
- Implement data cleanup procedures
- Follow privacy compliance requirements

## 🚀 Deployment

### Environment Requirements
- **Development**: Local setup with hot reload
- **Staging**: Docker-based testing environment
- **Production**: Full Docker Compose deployment

### Deployment Process
1. All tests must pass
2. Security scan completed
3. Performance benchmarks met
4. Documentation updated
5. Release notes prepared

## 💬 Getting Help

### Resources
- Check existing [documentation](docs/)
- Review [CLAUDE.md](CLAUDE.md) for development standards
- Look at existing code examples
- Check [GitHub Issues](issues) for similar problems

### Community
- Create GitHub issues for bugs or questions
- Follow project coding standards
- Be respectful and constructive
- Help other contributors when possible

## 📄 License

By contributing to AI-FARM, you agree that your contributions will be licensed under the same license as the project.

---

Thank you for contributing to AI-FARM! Your contributions help improve workplace safety through AI technology.