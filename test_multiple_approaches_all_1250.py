#!/usr/bin/env python3
"""
Test Multiple Approaches on ALL 1250 Cases
Compare simple vs complex with proper token limits
"""

import json
import base64
import requests
import os
from datetime import datetime
import time

class ComprehensiveApproachTester:
    def __init__(self):
        self.vlm_url = "http://**************:9500/v1/chat/completions"
        self.vlm_model = "VLM-38B-AWQ"
        self.session = requests.Session()
        
        # Test configurations
        self.approaches = {
            'simple_equipment': {
                'name': 'Simple Equipment Detection',
                'max_tokens': 50,
                'temperature': 0.1,
                'prompt': """Is this image primarily showing industrial equipment (crane, vessel, truck, or spreader) with no people visible? 
Answer only YES or NO."""
            },
            
            'simple_two_step': {
                'name': 'Simple Two-Step Check',
                'max_tokens': 100,
                'temperature': 0.1,
                'prompt': """Answer these questions:
1. Is this industrial equipment (crane/vessel/truck/spreader)? YES/NO
2. If NO, is there a person wearing BOTH helmet AND vest? YES/NO

Format:
Equipment: [YES/NO]
PPE Complete: [YES/NO/NA]
Decision: [FALSE POSITIVE/CHECK FURTHER]"""
            },
            
            'complex_original_fixed': {
                'name': 'Original Complex Prompt (Fixed Token Limit)',
                'max_tokens': 800,  # Much higher!
                'temperature': 0.1,
                'prompt': None  # Will load from file
            },
            
            'medium_structured': {
                'name': 'Medium Complexity Structured',
                'max_tokens': 400,
                'temperature': 0.1,
                'prompt': """Analyze this safety alert image:

STEP 1: What do you see?
a) Industrial structure/equipment only (crane, vessel, PM truck, spreader)
b) Person(s) present
c) Both or unclear

STEP 2: If person detected, check:
- Wearing helmet? YES/NO
- Wearing high-visibility vest? YES/NO
- Any unsafe behavior visible? YES/NO

STEP 3: Decision
- If (a) equipment only → FALSE POSITIVE
- If (b) person with helmet AND vest AND no unsafe behavior → FALSE POSITIVE  
- Otherwise → VALID VIOLATION

Output:
Main Entity: [EQUIPMENT/PERSON/UNCLEAR]
PPE Status: [COMPLETE/INCOMPLETE/NA]
Unsafe Behavior: [YES/NO/NA]
FINAL DECISION: [FALSE POSITIVE/VALID VIOLATION]"""
            }
        }
        
        # Load complex prompt
        if os.path.exists('FINAL_PRODUCTION_PROMPT.txt'):
            with open('FINAL_PRODUCTION_PROMPT.txt', 'r') as f:
                self.approaches['complex_original_fixed']['prompt'] = f.read()
        
        # Results storage
        self.results = {}
        self.progress_file = 'multi_approach_progress.json'
        self.load_progress()
        
    def load_progress(self):
        """Load previous progress if exists"""
        if os.path.exists(self.progress_file):
            with open(self.progress_file, 'r') as f:
                saved = json.load(f)
                self.results = saved.get('results', {})
                print(f"Loaded previous progress: {len(self.results)} approaches tested")
    
    def save_progress(self):
        """Save current progress"""
        with open(self.progress_file, 'w') as f:
            json.dump({
                'timestamp': datetime.now().isoformat(),
                'results': self.results
            }, f, indent=2)
    
    def encode_image(self, image_path):
        """Encode image to base64"""
        try:
            if os.path.exists(image_path):
                with open(image_path, 'rb') as f:
                    return base64.b64encode(f.read()).decode('utf-8')
        except:
            pass
        return None
    
    def parse_response(self, response, approach_key):
        """Parse response based on approach type"""
        response = response.strip()
        
        if approach_key == 'simple_equipment':
            # Simple YES/NO
            if 'YES' in response.upper()[:10]:
                return {'decision': 'FALSE_POSITIVE', 'details': 'Equipment detected'}
            else:
                return {'decision': 'NEEDS_REVIEW', 'details': 'Not equipment'}
        
        elif approach_key == 'simple_two_step':
            # Parse two-step response
            is_equipment = 'Equipment: YES' in response
            ppe_complete = 'PPE Complete: YES' in response
            
            if is_equipment or ppe_complete:
                return {'decision': 'FALSE_POSITIVE', 'details': response}
            else:
                return {'decision': 'VALID_VIOLATION', 'details': response}
        
        elif approach_key == 'complex_original_fixed':
            # Parse complex response
            if 'FALSE POSITIVE: YES' in response:
                return {'decision': 'FALSE_POSITIVE', 'details': response[:200]}
            else:
                return {'decision': 'VALID_VIOLATION', 'details': response[:200]}
        
        elif approach_key == 'medium_structured':
            # Parse structured response
            if 'FINAL DECISION: FALSE POSITIVE' in response:
                return {'decision': 'FALSE_POSITIVE', 'details': response}
            else:
                return {'decision': 'VALID_VIOLATION', 'details': response}
        
        return {'decision': 'ERROR', 'details': 'Could not parse response'}
    
    def test_single_case(self, case, approach_key, approach_config):
        """Test a single case with specific approach"""
        
        # Encode images
        source_b64 = self.encode_image(case['source_image'])
        cropped_b64 = self.encode_image(case['cropped_image'])
        
        if not source_b64 or not cropped_b64:
            return None
        
        payload = {
            "model": self.vlm_model,
            "messages": [{
                "role": "user",
                "content": [
                    {"type": "text", "text": approach_config['prompt']},
                    {"type": "text", "text": "\n\nSOURCE IMAGE:"},
                    {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{source_b64}"}},
                    {"type": "text", "text": "\n\nCROPPED IMAGE:"},
                    {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{cropped_b64}"}}
                ]
            }],
            "temperature": approach_config['temperature'],
            "max_tokens": approach_config['max_tokens']
        }
        
        try:
            response = self.session.post(self.vlm_url, json=payload, timeout=30)
            if response.status_code == 200:
                vlm_response = response.json()['choices'][0]['message']['content']
                parsed = self.parse_response(vlm_response, approach_key)
                
                # Determine if correct
                actual_fp = case['is_false_positive']
                predicted_fp = parsed['decision'] == 'FALSE_POSITIVE'
                
                return {
                    'case_number': case['case_number'],
                    'actual_fp': actual_fp,
                    'predicted_fp': predicted_fp,
                    'correct': predicted_fp == actual_fp,
                    'decision': parsed['decision'],
                    'response_length': len(vlm_response),
                    'details': parsed['details']
                }
        except Exception as e:
            return None
    
    def test_approach(self, approach_key, approach_config):
        """Test one approach on all cases"""
        
        # Skip if already tested
        if approach_key in self.results:
            print(f"\n✓ {approach_config['name']} already tested")
            return
        
        print(f"\n{'='*80}")
        print(f"TESTING: {approach_config['name']}")
        print(f"{'='*80}")
        print(f"Max Tokens: {approach_config['max_tokens']}")
        print(f"Temperature: {approach_config['temperature']}")
        
        # Load test data
        with open('valo_batch_round3_complete.json', 'r') as f:
            all_cases = json.load(f)['results']
        
        print(f"Testing {len(all_cases)} cases...")
        
        results = []
        start_time = time.time()
        
        for i, case in enumerate(all_cases):
            if i % 10 == 0:  # Save more frequently
                elapsed = time.time() - start_time
                rate = i / elapsed if elapsed > 0 and i > 0 else 0
                eta = (len(all_cases) - i) / rate / 60 if rate > 0 else 0
                print(f"Progress: {i}/{len(all_cases)} ({i/len(all_cases)*100:.1f}%) - ETA: {eta:.1f} min")
                
                # Save intermediate progress
                self.save_progress()
            
            # Show sample outputs for first few cases
            show_detail = i < 3
            
            if show_detail:
                print(f"\n  Testing {case['case_number']}...", end='', flush=True)
            
            result = self.test_single_case(case, approach_key, approach_config)
            
            if result:
                results.append(result)
                if show_detail:
                    status = "✓" if result['correct'] else "✗"
                    print(f" [{status}] {result['decision']} (Response: {result['response_length']} chars)")
                    if not result['correct']:
                        print(f"    Details: {result['details'][:100]}...")
            elif show_detail:
                print(" [SKIP]")
            
            # Rate limit
            time.sleep(0.5)
        
        # Calculate final metrics
        if results:
            metrics = self.calculate_metrics(results)
            
            print(f"\n{'='*60}")
            print(f"RESULTS: {approach_config['name']}")
            print(f"{'='*60}")
            print(f"├─ Total Cases: {len(results)}")
            print(f"├─ Overall Accuracy: {metrics['accuracy']:.2f}%")
            print(f"├─ FP Detection: {metrics['fp_detection']:.2f}% ({metrics['fp_detected']}/{metrics['fp_total']})")
            print(f"├─ Valid Protection: {metrics['valid_protection']:.2f}% ({metrics['valid_protected']}/{metrics['valid_total']})")
            print(f"├─ Avg Response Length: {metrics['avg_response_length']:.0f} chars")
            print(f"└─ Processing Time: {time.time() - start_time:.1f} seconds")
            
            # Store results
            self.results[approach_key] = {
                'name': approach_config['name'],
                'config': approach_config,
                'metrics': metrics,
                'sample_results': results[:10],  # Save first 10 for analysis
                'timestamp': datetime.now().isoformat()
            }
            
            self.save_progress()
    
    def calculate_metrics(self, results):
        """Calculate comprehensive metrics"""
        
        total = len(results)
        correct = sum(r['correct'] for r in results)
        
        # Separate by actual type
        actual_fps = [r for r in results if r['actual_fp']]
        actual_valid = [r for r in results if not r['actual_fp']]
        
        # FP metrics
        fp_detected = sum(r['predicted_fp'] for r in actual_fps) if actual_fps else 0
        
        # Valid metrics  
        valid_protected = sum(not r['predicted_fp'] for r in actual_valid) if actual_valid else 0
        
        # Response length
        avg_response_length = sum(r['response_length'] for r in results) / len(results) if results else 0
        
        return {
            'accuracy': correct / total * 100 if total > 0 else 0,
            'fp_detection': fp_detected / len(actual_fps) * 100 if actual_fps else 0,
            'fp_detected': fp_detected,
            'fp_total': len(actual_fps),
            'valid_protection': valid_protected / len(actual_valid) * 100 if actual_valid else 0,
            'valid_protected': valid_protected,
            'valid_total': len(actual_valid),
            'avg_response_length': avg_response_length
        }
    
    def run_all_tests(self):
        """Run all approach tests"""
        
        print("="*80)
        print("COMPREHENSIVE APPROACH TESTING ON ALL 1250 CASES")
        print("="*80)
        print(f"Testing {len(self.approaches)} different approaches")
        
        # Test each approach
        for approach_key, approach_config in self.approaches.items():
            self.test_approach(approach_key, approach_config)
        
        # Generate comparison report
        self.generate_comparison_report()
    
    def generate_comparison_report(self):
        """Generate final comparison report"""
        
        print("\n" + "="*80)
        print("FINAL COMPARISON REPORT")
        print("="*80)
        
        # Summary table
        print("\n📊 PERFORMANCE COMPARISON:")
        print("┌─────────────────────────┬──────────┬───────────┬──────────────┬─────────┐")
        print("│ Approach                │ Accuracy │ FP Det.   │ Valid Prot.  │ Tokens  │")
        print("├─────────────────────────┼──────────┼───────────┼──────────────┼─────────┤")
        
        for key, result in self.results.items():
            name = result['name'][:23]
            metrics = result['metrics']
            tokens = result['config']['max_tokens']
            
            print(f"│ {name:23} │ {metrics['accuracy']:6.1f}%  │ {metrics['fp_detection']:7.1f}%  │ {metrics['valid_protection']:10.1f}%  │ {tokens:7} │")
        
        print("└─────────────────────────┴──────────┴───────────┴──────────────┴─────────┘")
        
        # Find best approach
        best_balanced = max(self.results.items(), 
                          key=lambda x: x[1]['metrics']['accuracy'])
        
        print(f"\n🏆 BEST OVERALL: {best_balanced[1]['name']}")
        print(f"   Accuracy: {best_balanced[1]['metrics']['accuracy']:.1f}%")
        
        # Key insights
        print("\n💡 KEY INSIGHTS:")
        
        # Check if token limit was the issue
        if 'complex_original_fixed' in self.results:
            complex_metrics = self.results['complex_original_fixed']['metrics']
            print(f"\n1. Token Limit Impact on Complex Prompt:")
            print(f"   - With 800 tokens: {complex_metrics['accuracy']:.1f}% accuracy")
            print(f"   - Previous (300 tokens): 22.5% accuracy")
            
            if complex_metrics['accuracy'] > 50:
                print(f"   ✅ TOKEN LIMIT WAS THE ISSUE!")
            else:
                print(f"   ❌ Complex prompt still struggles even with more tokens")
        
        # Compare simple vs complex
        if 'simple_equipment' in self.results:
            simple_metrics = self.results['simple_equipment']['metrics']
            print(f"\n2. Simple vs Complex:")
            print(f"   - Simple (50 tokens): {simple_metrics['accuracy']:.1f}% accuracy")
            print(f"   - Best approach: {best_balanced[1]['metrics']['accuracy']:.1f}% accuracy")
        
        # Save full report
        report = {
            'test_date': datetime.now().isoformat(),
            'approaches_tested': len(self.results),
            'total_cases': 1250,
            'results': self.results,
            'best_approach': best_balanced[0],
            'conclusions': self.generate_conclusions()
        }
        
        with open('multi_approach_comparison_report.json', 'w') as f:
            json.dump(report, f, indent=2)
        
        print(f"\n📄 Full report saved to: multi_approach_comparison_report.json")
    
    def generate_conclusions(self):
        """Generate conclusions based on results"""
        
        conclusions = []
        
        # Check if complex improved with tokens
        if 'complex_original_fixed' in self.results:
            complex_acc = self.results['complex_original_fixed']['metrics']['accuracy']
            if complex_acc > 70:
                conclusions.append("Token limit was indeed constraining the complex prompt")
                conclusions.append("Complex prompts need adequate token allowance")
            else:
                conclusions.append("Complex prompt issues go beyond token limits")
        
        # Check simple performance
        if 'simple_equipment' in self.results:
            simple_acc = self.results['simple_equipment']['metrics']['accuracy']
            if simple_acc > 60:
                conclusions.append("Simple approaches can be highly effective")
            
        # Best approach
        best = max(self.results.items(), key=lambda x: x[1]['metrics']['accuracy'])
        conclusions.append(f"Best approach: {best[1]['name']} with {best[1]['metrics']['accuracy']:.1f}% accuracy")
        
        return conclusions

def main():
    tester = ComprehensiveApproachTester()
    tester.run_all_tests()

if __name__ == "__main__":
    main()