#!/usr/bin/env python3
"""
Analyze early results from Round 3 real test
"""

import json

def analyze_early_results():
    print("="*70)
    print("ROUND 3 REAL TEST - EARLY RESULTS ANALYSIS")
    print("="*70)
    
    # Load progress
    with open('round3_production_real_progress.json', 'r') as f:
        progress = json.load(f)
    
    results = progress['results']
    print(f"\nAnalyzing first {len(results)} cases...")
    
    # Calculate metrics
    total = len(results)
    correct = sum(r['correct'] for r in results)
    
    actual_fps = [r for r in results if r['actual_fp']]
    actual_valid = [r for r in results if not r['actual_fp']]
    
    fp_detected = sum(r['predicted_fp'] for r in actual_fps) if actual_fps else 0
    fp_missed = len(actual_fps) - fp_detected
    
    valid_protected = sum(not r['predicted_fp'] for r in actual_valid) if actual_valid else 0
    valid_missed = len(actual_valid) - valid_protected
    
    print(f"\n📊 EARLY METRICS:")
    print(f"├─ Overall Accuracy: {correct}/{total} = {correct/total*100:.1f}%")
    print(f"├─ FP Detection: {fp_detected}/{len(actual_fps)} = {fp_detected/len(actual_fps)*100:.1f}%" if actual_fps else "├─ FP Detection: No FP cases yet")
    print(f"├─ Valid Protection: {valid_protected}/{len(actual_valid)} = {valid_protected/len(actual_valid)*100:.1f}%" if actual_valid else "├─ Valid Protection: No valid cases yet")
    
    print(f"\n🔍 DETAILED BREAKDOWN:")
    print(f"├─ Total False Positives: {len(actual_fps)}")
    print(f"│  ├─ Correctly detected: {fp_detected}")
    print(f"│  └─ Missed (marked as valid): {fp_missed}")
    print(f"└─ Total Valid Violations: {len(actual_valid)}")
    print(f"   ├─ Correctly protected: {valid_protected}")
    print(f"   └─ Wrongly filtered: {valid_missed}")
    
    print(f"\n📝 CASE-BY-CASE ANALYSIS:")
    for r in results:
        status = "✓" if r['correct'] else "✗"
        actual = "FP" if r['actual_fp'] else "Valid"
        predicted = "FP" if r['predicted_fp'] else "Valid"
        print(f"{status} {r['case_number']} - Actual: {actual}, Predicted: {predicted}, Entity: {r['entity']}")
        if not r['correct']:
            print(f"  → Issue: {r['violations'] if r['violations'] != 'None' else 'Marked as valid despite being FP'}")
    
    print(f"\n⚠️  OBSERVATIONS:")
    
    # Count entity misidentifications
    structure_cases = [r for r in results if r['entity'] == 'STRUCTURE']
    person_cases = [r for r in results if r['entity'] == 'PERSON']
    
    print(f"\nEntity Detection:")
    print(f"├─ Identified as STRUCTURE: {len(structure_cases)}")
    print(f"└─ Identified as PERSON: {len(person_cases)}")
    
    # Check if threshold is working
    structure_fps = [r for r in structure_cases if r['actual_fp']]
    structure_correct = sum(r['predicted_fp'] for r in structure_fps)
    
    print(f"\nStructure Detection Performance:")
    print(f"├─ Total structures identified: {len(structure_cases)}")
    print(f"├─ Were actual FPs: {len(structure_fps)}")
    print(f"└─ Correctly marked as FP: {structure_correct}/{len(structure_fps)} = {structure_correct/len(structure_fps)*100:.1f}%" if structure_fps else "└─ No FP structures")
    
    print(f"\n💡 EARLY INSIGHTS:")
    print("1. The system is being MORE conservative than expected")
    print("2. Many FPs are being marked as valid violations")
    print("3. Even when detecting STRUCTURE, not always marking as FP")
    print("4. The 91% threshold may be too strict in practice")
    
    print("\n⚠️  CRITICAL FINDING:")
    print("The Round 3 'optimal' thresholds are NOT performing as projected!")
    print("Early results suggest ~30% accuracy vs projected 91.7%")
    print("="*70)

if __name__ == "__main__":
    analyze_early_results()