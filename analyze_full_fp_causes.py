#!/usr/bin/env python3
"""
Analyze full dataset for top 5 root causes of false positives
"""

import pandas as pd
import re
from collections import Counter, defaultdict

# Read the entire CSV file
df = pd.read_csv('./ai_farm_images_fixed_250703/psa_valo_violation_report_without_images_csv_2025-07-03_14-14-01.CSV')

print('ANALYZING FULL DATASET:')
print('='*80)
print(f'Total cases analyzed: {len(df):,}')
print(f'Total false positives: {len(df[df["Alert Status"] == "Invalid"]):,}')
print(f'False positive rate: {len(df[df["Alert Status"] == "Invalid"])/len(df)*100:.1f}%')

# Analyze ALL false positive cases
fp_df = df[df['Alert Status'] == 'Invalid'].copy()

# Extract all remarks for comprehensive analysis
all_remarks = []
violation_type_remarks = defaultdict(list)

for idx, row in fp_df.iterrows():
    remark = str(row['Remarks']).strip()
    violation_type = row['Type of Infringement']
    if remark and remark != 'nan':
        all_remarks.append(remark.upper())
        violation_type_remarks[violation_type].append(remark.upper())

print(f'\nTotal remarks analyzed: {len(all_remarks):,}')

# Define comprehensive patterns for root cause analysis
root_cause_patterns = {
    'Workers in Full PPE': [
        r'FULL PPE', r'PROPER PPE', r'IN FULL PPE', r'WEARING PPE', 
        r'WITH PPE', r'COMPLETE PPE', r'PPE AT WHARF'
    ],
    'Equipment/Structure Misidentification': [
        r'STRUCTURE CAPTURED', r'CRANE STRUCTURE', r'VESSEL STRUCTURE', 
        r'PM STRUCTURE', r'SPREADER', r'EQUIPMENT', r'MACHINE', 
        r'FLIPPER', r'WRONGLY CAPTURED', r'CAPTURED AS LS', r'BARGE STRUCTURE'
    ],
    'Multiple Workers Present': [
        r'2 LS', r'TWO LS', r'BOTH LS', r'2 PERSON', r'TWO PERSON',
        r'MULTIPLE', r'TEAM', r'TOGETHER', r'PARTNER'
    ],
    'Location Context Issues': [
        r'AT WHARF', r'STANDING', r'WALKING', r'GOING TO', r'NOT DOING',
        r'NOT AT', r'AWAY FROM', r'BOARD', r'WORKING BAY'
    ],
    'Camera/System Issues': [
        r'NO CAMERA', r'NO FOOTAGE', r'CAMERA ANGLE', r'BLURRED', 
        r'UNCLEAR', r'POOR QUALITY', r'VISIBILITY', r'OBSTRUCT'
    ]
}

# Count occurrences for each root cause across ALL remarks
root_cause_counts = {}
for cause, patterns in root_cause_patterns.items():
    count = 0
    matched_remarks = set()
    for pattern in patterns:
        for remark in all_remarks:
            if re.search(pattern, remark) and remark not in matched_remarks:
                count += 1
                matched_remarks.add(remark)
    root_cause_counts[cause] = count

print('\n' + '='*80)
print('TOP 5 ROOT CAUSES OF FALSE POSITIVES (ENTIRE DATASET):')
print('='*80)

sorted_causes = sorted(root_cause_counts.items(), key=lambda x: x[1], reverse=True)
for i, (cause, count) in enumerate(sorted_causes[:5], 1):
    percentage = (count / len(fp_df)) * 100
    print(f'\n{i}. {cause.upper()}')
    print(f'   Occurrences: {count:,} cases ({percentage:.1f}% of all false positives)')
    
    # Show breakdown by violation type
    print(f'   Breakdown by violation type:')
    for vtype in violation_type_remarks.keys():
        type_count = 0
        type_remarks = violation_type_remarks[vtype]
        matched_type_remarks = set()
        for pattern in root_cause_patterns[cause]:
            for remark in type_remarks:
                if re.search(pattern, remark) and remark not in matched_type_remarks:
                    type_count += 1
                    matched_type_remarks.add(remark)
        if type_count > 0:
            type_fp_count = len([r for _, r in fp_df.iterrows() if r['Type of Infringement'] == vtype])
            type_percentage = (type_count / type_fp_count) * 100
            print(f'     - {vtype}: {type_count} cases ({type_percentage:.1f}%)')

# Additional detailed analysis
print('\n\n' + '='*80)
print('DETAILED INSIGHTS FROM FULL DATASET ANALYSIS:')
print('='*80)

# Analyze specific patterns
specific_patterns = {
    'LS/Worker at wharf': len([r for r in all_remarks if 'AT WHARF' in r]),
    'Full PPE compliance': len([r for r in all_remarks if re.search(r'FULL PPE|PROPER PPE', r)]),
    'Crane/vessel structure': len([r for r in all_remarks if re.search(r'CRANE|VESSEL|STRUCTURE', r)]),
    'Life jacket worn': len([r for r in all_remarks if re.search(r'LIFE JACKET|LIFEJACKET', r)]),
    'No actual violation': len([r for r in all_remarks if re.search(r'NO .* DOING|NOT DOING', r)])
}

print('\nSpecific Pattern Analysis:')
for pattern, count in sorted(specific_patterns.items(), key=lambda x: x[1], reverse=True):
    print(f'- {pattern}: {count:,} cases ({count/len(fp_df)*100:.1f}%)')

# Time-based analysis
print('\n\nTIME-BASED ANALYSIS:')
print('-'*50)
fp_df['Alert Start Time'] = pd.to_datetime(fp_df['Alert Start Time'], errors='coerce')
fp_df['Hour'] = fp_df['Alert Start Time'].dt.hour

hour_counts = fp_df['Hour'].value_counts().sort_index()
print('False positives by hour of day:')
peak_hours = hour_counts.nlargest(5)
for hour, count in peak_hours.items():
    print(f'- {hour:02d}:00: {count} cases ({count/len(fp_df)*100:.1f}%)')

# Violation-specific insights
print('\n\nVIOLATION-SPECIFIC ROOT CAUSES:')
print('='*80)

for vtype in ['PPE Non-compliance', 'One man Lashing', 'Ex.Row Violation', '2-Container Distance']:
    print(f'\n{vtype}:')
    type_remarks = [r for r, v in zip(fp_df['Remarks'], fp_df['Type of Infringement']) if v == vtype and pd.notna(r)]
    
    # Count specific issues
    issues = {
        'Workers wearing full PPE': len([r for r in type_remarks if re.search(r'FULL PPE|PROPER PPE', str(r).upper())]),
        'Equipment misidentified': len([r for r in type_remarks if re.search(r'STRUCTURE|EQUIPMENT|CRANE|VESSEL', str(r).upper())]),
        'Multiple workers': len([r for r in type_remarks if re.search(r'2 LS|TWO|BOTH', str(r).upper())]),
        'Location context': len([r for r in type_remarks if re.search(r'AT WHARF|STANDING|NOT DOING', str(r).upper())]),
        'Camera issues': len([r for r in type_remarks if re.search(r'NO CAMERA|FOOTAGE', str(r).upper())])
    }
    
    total = len(type_remarks)
    for issue, count in sorted(issues.items(), key=lambda x: x[1], reverse=True)[:3]:
        if count > 0:
            print(f'  - {issue}: {count} ({count/total*100:.1f}%)')

print('\n' + '='*80)