#!/usr/bin/env python3
"""Test VLM API connectivity"""
import aiohttp
import asyncio
import json

async def test_vlm():
    vlm_endpoint = "http://100.106.127.35:9500/v1/chat/completions"
    
    # Test with a simple text prompt
    payload = {
        "messages": [
            {
                "role": "user",
                "content": "Hello, can you respond? Just say yes."
            }
        ],
        "temperature": 0.1,
        "max_tokens": 50
    }
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(vlm_endpoint, json=payload, timeout=30) as response:
                print(f"Status: {response.status}")
                if response.status == 200:
                    result = await response.json()
                    print("Success! Response:", result['choices'][0]['message']['content'])
                else:
                    print("Error:", await response.text())
    except Exception as e:
        print(f"Connection error: {e}")

if __name__ == "__main__":
    asyncio.run(test_vlm())