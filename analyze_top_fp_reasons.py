#!/usr/bin/env python3
"""
Analyze top 10 false positive reasons and their violation types
"""

import pandas as pd
from collections import Counter, defaultdict

# Read the CSV file
csv_path = 'ai_farm_images_fixed_250703/psa_valo_violation_report_without_images_csv_2025-07-03_14-14-01.CSV'
df = pd.read_csv(csv_path)

print(f"Total cases: {len(df)}")

# Filter for false positives
false_positives = df[df['Alert Status'] == 'Invalid'].copy()
print(f"Total false positives: {len(false_positives)}")

# Standardize remarks for counting (uppercase and strip)
false_positives['Remarks_Clean'] = false_positives['Remarks'].str.strip().str.upper()

# Count remarks and track violation types
remark_counts = Counter()
remark_violations = defaultdict(Counter)

for idx, row in false_positives.iterrows():
    remark = row['Remarks_Clean']
    violation_type = row['Type of Infringement']
    
    remark_counts[remark] += 1
    remark_violations[remark][violation_type] += 1

# Get top 10 reasons
top_10_reasons = remark_counts.most_common(10)

print("\n" + "="*100)
print("TOP 10 FALSE POSITIVE REASONS AND THEIR VIOLATION TYPES")
print("="*100)
print(f"{'Rank':<5} {'Count':<7} {'%':<6} {'Reason':<60} {'Violation Types'}")
print("-"*100)

for rank, (reason, count) in enumerate(top_10_reasons, 1):
    percentage = (count / len(false_positives)) * 100
    
    # Get violation types for this reason
    violations = remark_violations[reason]
    violation_breakdown = []
    for vtype, vcount in violations.most_common():
        vpercent = (vcount / count) * 100
        violation_breakdown.append(f"{vtype} ({vcount}, {vpercent:.1f}%)")
    
    # Format reason (truncate if too long)
    reason_display = reason[:57] + "..." if len(reason) > 60 else reason
    
    print(f"{rank:<5} {count:<7} {percentage:<6.1f} {reason_display:<60} {violation_breakdown[0]}")
    
    # Print additional violation types if multiple
    if len(violation_breakdown) > 1:
        for vb in violation_breakdown[1:]:
            print(f"{'':<18} {'':<60} {vb}")
    print()

# Detailed breakdown of top 5
print("\n" + "="*100)
print("DETAILED BREAKDOWN OF TOP 5 FALSE POSITIVE REASONS")
print("="*100)

for rank, (reason, count) in enumerate(top_10_reasons[:5], 1):
    print(f"\n{rank}. {reason}")
    print(f"   Total Count: {count} ({count/len(false_positives)*100:.1f}%)")
    print(f"   Violation Type Breakdown:")
    
    violations = remark_violations[reason]
    for vtype, vcount in violations.most_common():
        print(f"      - {vtype}: {vcount} cases ({vcount/count*100:.1f}%)")

# Summary by violation type
print("\n" + "="*100)
print("FALSE POSITIVE DISTRIBUTION BY VIOLATION TYPE")
print("="*100)

violation_fp_counts = defaultdict(int)
for idx, row in false_positives.iterrows():
    violation_fp_counts[row['Type of Infringement']] += 1

for vtype, count in sorted(violation_fp_counts.items(), key=lambda x: x[1], reverse=True):
    percentage = (count / len(false_positives)) * 100
    print(f"{vtype:<25} {count:>5} cases ({percentage:>5.1f}%)")

# Most problematic violation types
print("\n" + "="*100)
print("MOST COMMON REASONS BY VIOLATION TYPE")
print("="*100)

for vtype in ['PPE Non-compliance', 'One man Lashing', 'Ex.Row Violation', '2-Container Distance']:
    print(f"\n{vtype}:")
    vtype_fps = false_positives[false_positives['Type of Infringement'] == vtype]
    vtype_reasons = Counter(vtype_fps['Remarks_Clean'])
    
    for reason, count in vtype_reasons.most_common(3):
        percentage = (count / len(vtype_fps)) * 100
        reason_display = reason[:70] + "..." if len(reason) > 73 else reason
        print(f"   {count:>4} ({percentage:>4.1f}%) - {reason_display}")