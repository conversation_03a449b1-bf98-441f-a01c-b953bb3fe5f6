#!/usr/bin/env python3
"""
Debug why so many TRUE POSITIVE cases are marked as "no person"
"""

import re
from pathlib import Path

def check_specific_cases():
    """Check specific TP cases that were marked as no-person"""
    tp_file = Path('valo_comprehensive_data/true_positives/true_positive_analysis_20250725_232934.md')
    
    # Cases to check (from the no-person TP list)
    check_cases = ['V1250627194', 'V1250627204', 'V1250628030', 'V1250628042', 'V1250628101']
    
    with open(tp_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    for case_num in check_cases:
        print(f"\n{'='*60}")
        print(f"Checking Case: {case_num}")
        print("="*60)
        
        # Find the case section
        case_pattern = f"## Case: {case_num}.*?(?=## Case:|$)"
        case_match = re.search(case_pattern, content, re.DOTALL)
        
        if case_match:
            case_content = case_match.group(0)
            
            # Extract person-related info
            # Look for "Visible Individuals" or "Person Details"
            person_lines = []
            lines = case_content.split('\n')
            
            for i, line in enumerate(lines):
                line_lower = line.lower()
                if any(keyword in line_lower for keyword in ['person', 'individual', 'people', 'worker', 'crew']):
                    person_lines.append(line.strip())
                    
            print("Person-related lines found:")
            for line in person_lines[:10]:  # Show first 10
                print(f"  - {line}")
                
            # Check confidence section specifically
            conf_start = case_content.find('### Confidence Analysis')
            if conf_start != -1:
                conf_section = case_content[conf_start:conf_start+500]
                print("\nConfidence section:")
                print(conf_section)
                
def analyze_detection_patterns():
    """Analyze why detection is failing"""
    tp_file = Path('valo_comprehensive_data/true_positives/true_positive_analysis_20250725_232934.md')
    
    with open(tp_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Split into cases
    cases = re.split(r'\n## Case:', content)
    
    # Count different person indicators
    person_present_yes = 0
    person_present_no = 0
    ambiguous = 0
    
    for case in cases[1:20]:  # Check first 20 cases
        case_lower = case.lower()
        
        if '**person present**: yes' in case_lower:
            person_present_yes += 1
        elif '**person present**: no' in case_lower:
            person_present_no += 1
        else:
            ambiguous += 1
            # Check what format is used
            if 'person present' in case_lower:
                # Find the actual line
                for line in case.split('\n'):
                    if 'person present' in line.lower() and len(line) < 100:
                        print(f"Ambiguous format: {line.strip()}")
                        break
    
    print(f"\n📊 Person Detection Format Analysis (first 20 TP cases):")
    print(f"- Clear YES: {person_present_yes}")
    print(f"- Clear NO: {person_present_no}")
    print(f"- Ambiguous/Other format: {ambiguous}")

# Run checks
print("🔍 Debugging No-Person Detection in TRUE POSITIVE Cases\n")
check_specific_cases()
print("\n" + "="*60)
analyze_detection_patterns()