#!/bin/bash

echo "🎯 Safe VALO-Only Restart"
echo "========================"
echo "This script ONLY affects VALO containers, not Nextcloud!"
echo

# Stop only VALO containers by name
echo "1. Stopping VALO containers only..."
sudo docker stop valo-system valo-postgres 2>/dev/null || echo "VALO containers not running"

# Remove only VALO containers
echo "2. Removing VALO containers only..."
sudo docker rm valo-system valo-postgres 2>/dev/null || echo "VALO containers already removed"

# Remove only VALO images (not all images!)
echo "3. Removing VALO images only..."
sudo docker rmi ai-farm-valo-system postgres:15-alpine 2>/dev/null || echo "VALO images already removed"

echo "4. Starting VALO system on port 5001..."
echo "   - Dashboard: http://localhost:5001"
echo "   - PostgreSQL: localhost:5433"
echo

sudo docker-compose up --build