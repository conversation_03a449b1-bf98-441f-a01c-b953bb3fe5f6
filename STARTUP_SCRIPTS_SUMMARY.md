# AI-FARM Startup Scripts Summary

## Overview
The AI-FARM project now includes comprehensive startup scripts that provide a one-command solution to launch both the backend API and frontend dashboard together.

## Available Startup Options

### 1. Cross-Platform Python Launcher
- **File**: `launch-ai-farm.py`
- **Usage**: `python3 launch-ai-farm.py [command]`
- **Benefits**: Works on any platform with Python 3

### 2. Native Shell Scripts
- **macOS/Linux**: `ai-farm.sh`
- **Windows**: `ai-farm.bat`
- **Usage**: `./ai-farm.sh start` or `ai-farm.bat start`

### 3. Simple Launchers
- **macOS/Linux**: `start.sh`
- **Windows**: `start.bat`
- **Usage**: Just double-click or run directly

## Key Features Implemented

✅ **Backend Detection**: Checks if backend is already running on localhost:8000
✅ **Port Management**: Verifies ports are available before starting services  
✅ **Dependency Management**: Automatically installs Python and npm dependencies
✅ **Service Readiness**: Waits for both services to be fully operational
✅ **Health Monitoring**: Provides real-time service status checking
✅ **Graceful Shutdown**: <PERSON>perly terminates all processes with signal handling
✅ **Cross-Platform**: Works on Windows, macOS, and Linux
✅ **Error Handling**: Comprehensive error checking and user feedback
✅ **Log Management**: Centralized logging with easy access to service logs

## Service Management Commands

| Command | Description |
|---------|-------------|
| `start` | Start both backend (localhost:8000) and frontend (localhost:3000) |
| `stop` | Stop all AI-FARM services gracefully |
| `restart` | Restart all services |
| `status` | Check current service status |
| `logs [service]` | View logs (backend/frontend/docker) |
| `setup` | Setup environment and dependencies |
| `start-docker` | Start using Docker Compose |

## Enhanced Features

### Backend Management
- Detects if FastAPI backend is already running
- Creates Python virtual environment automatically
- Handles Python 3.13 compatibility with requirements-simple.txt fallback
- Manages process IDs for reliable service control

### Frontend Management  
- Starts React development server on port 3000
- Installs npm dependencies automatically
- Proxies API requests to backend via package.json configuration

### Error Handling
- Port conflict detection and resolution
- Service startup timeout handling (60 seconds)
- Clear error messages with suggested solutions
- Prerequisite checking (Python 3, Node.js, npm)

### Signal Handling (Unix)
- Graceful shutdown on Ctrl+C
- SIGTERM followed by SIGKILL for unresponsive processes
- Process cleanup to prevent orphaned services

## File Structure
```
/Users/<USER>/Documents/Sensen/General/VALO_AI-FARM_2025/
├── ai-farm.sh                 # Main startup script (macOS/Linux)
├── ai-farm.bat               # Main startup script (Windows)  
├── launch-ai-farm.py         # Cross-platform Python launcher
├── start.sh                  # Simple launcher (macOS/Linux)
├── start.bat                 # Simple launcher (Windows)
├── STARTUP_GUIDE.md          # Detailed usage guide
└── logs/                     # Service logs directory
    ├── backend.log
    ├── frontend.log
    ├── backend.pid
    └── frontend.pid
```

## Quick Start Examples

```bash
# Easiest - works everywhere
python3 launch-ai-farm.py

# Platform-specific simple launchers
./start.sh          # macOS/Linux
start.bat           # Windows

# Full control with main scripts
./ai-farm.sh start      # macOS/Linux
ai-farm.bat start       # Windows

# Check what's running
./ai-farm.sh status

# Stop everything
./ai-farm.sh stop
```

## Services Available After Startup

- **Frontend Dashboard**: http://localhost:3000  
- **Backend API**: http://localhost:8000
- **API Documentation**: http://localhost:8000/docs
- **Health Check**: http://localhost:8000/health

## Deployment Ready
The scripts are production-ready and include:
- Environment validation
- Dependency management  
- Service orchestration
- Health monitoring
- Log management
- Error recovery
- Cross-platform compatibility

This provides a professional, reliable way to start the complete AI-FARM web application with a single command.