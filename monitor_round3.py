#!/usr/bin/env python3
"""Monitor Round 3 processing progress"""

import json
import time
import os
from datetime import datetime

def monitor_progress():
    """Monitor Round 3 progress"""
    print(f"\n{'='*60}")
    print(f"ROUND 3 PROGRESS MONITOR - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"{'='*60}\n")
    
    while True:
        try:
            # Check progress file
            if os.path.exists('valo_intelligent_round3_progress.json'):
                with open('valo_intelligent_round3_progress.json', 'r') as f:
                    progress = json.load(f)
                
                print(f"\r[{datetime.now().strftime('%H:%M:%S')}] "
                      f"Cases: {progress['cases_processed']}/1250 "
                      f"({progress['cases_processed']/12.5:.1f}%) | "
                      f"Valid Protection: {progress['valid_protection_rate']:.1f}% | "
                      f"FP Detection: {progress['fp_detection_rate']:.1f}% | "
                      f"Remaining: {progress.get('remaining_cases', 'N/A')}", 
                      end='', flush=True)
                
                # Check if complete
                if progress.get('remaining_cases', 1) == 0:
                    print("\n\n✅ ROUND 3 COMPLETE!")
                    break
            else:
                print("\rWaiting for processing to start...", end='', flush=True)
            
            time.sleep(5)
            
        except KeyboardInterrupt:
            print("\n\nMonitoring stopped.")
            break
        except Exception as e:
            print(f"\rError reading progress: {e}", end='', flush=True)
            time.sleep(5)

if __name__ == "__main__":
    monitor_progress()