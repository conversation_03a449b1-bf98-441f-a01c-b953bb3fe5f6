#!/usr/bin/env python3
"""
Comprehensive Performance Testing for AI-FARM Application
Tests backend, frontend, VLM processing, and resource utilization
"""

import time
import json
import requests
import psutil
import subprocess
import statistics
from datetime import datetime
from typing import Dict, List, Any
import concurrent.futures
import os
import sys

class PerformanceTestSuite:
    def __init__(self):
        self.backend_url = "http://localhost:8000"
        self.frontend_url = "http://localhost:3000"
        self.results = {
            "test_date": datetime.now().isoformat(),
            "backend": {},
            "frontend": {},
            "vlm_processing": {},
            "resource_utilization": {}
        }
        
    def measure_response_time(self, url: str, method: str = "GET", 
                            json_data: Dict = None, iterations: int = 10) -> Dict[str, float]:
        """Measure response times for an endpoint"""
        times = []
        errors = 0
        
        for _ in range(iterations):
            try:
                start = time.time()
                if method == "GET":
                    response = requests.get(url, timeout=30)
                elif method == "POST":
                    response = requests.post(url, json=json_data, timeout=30)
                end = time.time()
                
                if response.status_code == 200:
                    times.append((end - start) * 1000)  # Convert to ms
                else:
                    errors += 1
            except Exception as e:
                errors += 1
                print(f"Error accessing {url}: {e}")
        
        if times:
            return {
                "min_ms": min(times),
                "max_ms": max(times),
                "avg_ms": statistics.mean(times),
                "median_ms": statistics.median(times),
                "std_dev_ms": statistics.stdev(times) if len(times) > 1 else 0,
                "success_rate": (len(times) / iterations) * 100,
                "errors": errors
            }
        else:
            return {"error": "No successful requests", "errors": errors}
    
    def test_backend_performance(self):
        """Test backend API performance"""
        print("\n=== Testing Backend Performance ===")
        
        # Test health endpoint
        print("Testing health endpoint...")
        health_results = self.measure_response_time(f"{self.backend_url}/health")
        self.results["backend"]["health_endpoint"] = health_results
        if 'avg_ms' in health_results:
            print(f"Health endpoint avg response time: {health_results['avg_ms']:.2f}ms")
        else:
            print(f"Health endpoint: {health_results.get('error', 'Failed')}")
        
        # Test metrics endpoint
        print("Testing metrics endpoint...")
        metrics_results = self.measure_response_time(f"{self.backend_url}/api/v1/metrics")
        self.results["backend"]["metrics_endpoint"] = metrics_results
        if 'avg_ms' in metrics_results:
            print(f"Metrics endpoint avg response time: {metrics_results['avg_ms']:.2f}ms")
        else:
            print(f"Metrics endpoint: {metrics_results.get('error', 'Failed')}")
        
        # Test status endpoint
        print("Testing status endpoint...")
        status_results = self.measure_response_time(f"{self.backend_url}/api/v1/status")
        self.results["backend"]["status_endpoint"] = status_results
        if 'avg_ms' in status_results:
            print(f"Status endpoint avg response time: {status_results['avg_ms']:.2f}ms")
        else:
            print(f"Status endpoint: {status_results.get('error', 'Failed')}")
        
        # Test backend startup time
        # self.test_backend_startup_time()  # Commented out to avoid killing the running backend
        
        # Test concurrent requests
        self.test_backend_concurrent_requests()
        
    def test_backend_startup_time(self):
        """Measure backend startup time"""
        print("\nMeasuring backend startup time...")
        
        # Kill existing backend process
        subprocess.run(["pkill", "-f", "uvicorn"], capture_output=True)
        time.sleep(2)
        
        # Start backend and measure time
        start_time = time.time()
        process = subprocess.Popen(
            ["python3", "backend/run.py"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            cwd="/home/<USER>/VALO_AI-FARM_2025/VALO_AI-FARM_2025"
        )
        
        # Wait for backend to be ready
        max_wait = 30
        ready = False
        while time.time() - start_time < max_wait:
            try:
                response = requests.get(f"{self.backend_url}/health", timeout=1)
                if response.status_code == 200:
                    ready = True
                    break
            except:
                pass
            time.sleep(0.5)
        
        startup_time = time.time() - start_time
        
        if ready:
            self.results["backend"]["startup_time_seconds"] = startup_time
            print(f"Backend startup time: {startup_time:.2f} seconds")
        else:
            self.results["backend"]["startup_time_seconds"] = "Failed to start"
            print("Backend failed to start within 30 seconds")
        
        # Keep backend running for other tests
        
    def test_backend_concurrent_requests(self):
        """Test backend performance under concurrent load"""
        print("\nTesting concurrent request handling...")
        
        def make_request():
            start = time.time()
            try:
                response = requests.get(f"{self.backend_url}/health", timeout=10)
                return time.time() - start, response.status_code == 200
            except:
                return time.time() - start, False
        
        # Test with different concurrency levels
        for concurrency_level in [10, 50, 100]:
            print(f"\nTesting with {concurrency_level} concurrent requests...")
            with concurrent.futures.ThreadPoolExecutor(max_workers=concurrency_level) as executor:
                futures = [executor.submit(make_request) for _ in range(concurrency_level)]
                results = [f.result() for f in concurrent.futures.as_completed(futures)]
            
            times = [r[0] for r in results]
            successes = sum(1 for r in results if r[1])
            
            self.results["backend"][f"concurrent_{concurrency_level}"] = {
                "total_requests": concurrency_level,
                "successful_requests": successes,
                "avg_response_time_ms": statistics.mean(times) * 1000,
                "max_response_time_ms": max(times) * 1000,
                "success_rate": (successes / concurrency_level) * 100
            }
            
            print(f"Success rate: {(successes / concurrency_level) * 100:.1f}%")
            print(f"Avg response time: {statistics.mean(times) * 1000:.2f}ms")
    
    def test_frontend_performance(self):
        """Test frontend performance metrics"""
        print("\n=== Testing Frontend Performance ===")
        
        # Test page load times
        print("Testing frontend page load time...")
        page_load_results = self.measure_response_time(self.frontend_url)
        self.results["frontend"]["page_load"] = page_load_results
        print(f"Frontend page load avg time: {page_load_results.get('avg_ms', 'N/A'):.2f}ms")
        
        # Check bundle size
        self.check_frontend_bundle_size()
        
        # Test build time
        self.test_frontend_build_time()
        
    def check_frontend_bundle_size(self):
        """Check frontend bundle sizes"""
        print("\nChecking frontend bundle sizes...")
        
        build_dir = "/home/<USER>/VALO_AI-FARM_2025/VALO_AI-FARM_2025/frontend/build"
        
        if os.path.exists(build_dir):
            total_size = 0
            js_size = 0
            css_size = 0
            
            for root, dirs, files in os.walk(build_dir):
                for file in files:
                    filepath = os.path.join(root, file)
                    size = os.path.getsize(filepath)
                    total_size += size
                    
                    if file.endswith('.js'):
                        js_size += size
                    elif file.endswith('.css'):
                        css_size += size
            
            self.results["frontend"]["bundle_sizes"] = {
                "total_size_mb": total_size / (1024 * 1024),
                "js_size_mb": js_size / (1024 * 1024),
                "css_size_mb": css_size / (1024 * 1024)
            }
            
            print(f"Total bundle size: {total_size / (1024 * 1024):.2f} MB")
            print(f"JavaScript size: {js_size / (1024 * 1024):.2f} MB")
            print(f"CSS size: {css_size / (1024 * 1024):.2f} MB")
        else:
            print("Build directory not found. Running development build...")
            self.results["frontend"]["bundle_sizes"] = "Build directory not found"
    
    def test_frontend_build_time(self):
        """Test frontend build time"""
        print("\nTesting frontend build time...")
        
        start_time = time.time()
        result = subprocess.run(
            ["npm", "run", "build"],
            cwd="/home/<USER>/VALO_AI-FARM_2025/VALO_AI-FARM_2025/frontend",
            capture_output=True,
            text=True
        )
        
        build_time = time.time() - start_time
        
        if result.returncode == 0:
            self.results["frontend"]["build_time_seconds"] = build_time
            print(f"Frontend build time: {build_time:.2f} seconds")
        else:
            self.results["frontend"]["build_time_seconds"] = "Build failed"
            print(f"Frontend build failed: {result.stderr}")
    
    def test_vlm_processing_performance(self):
        """Test VLM processing performance based on documentation"""
        print("\n=== Testing VLM Processing Performance ===")
        
        # Check documented processing capabilities
        vlm_service_path = "/home/<USER>/VALO_AI-FARM_2025/VALO_AI-FARM_2025/backend/app/services/vlm_service.py"
        
        try:
            with open(vlm_service_path, 'r') as f:
                content = f.read()
                
            # Extract configuration from VLM service
            if "BATCH_SIZE" in content:
                batch_size_line = [line for line in content.split('\n') if "BATCH_SIZE" in line and "=" in line]
                if batch_size_line:
                    batch_size = batch_size_line[0].split('=')[-1].strip()
                    self.results["vlm_processing"]["configured_batch_size"] = batch_size
            
            if "MAX_WORKERS" in content:
                workers_line = [line for line in content.split('\n') if "MAX_WORKERS" in line and "=" in line]
                if workers_line:
                    max_workers = workers_line[0].split('=')[-1].strip()
                    self.results["vlm_processing"]["configured_max_workers"] = max_workers
            
            # Test batch processing endpoint if available
            print("Testing batch processing capabilities...")
            batch_test_data = {
                "images": ["test1.jpg", "test2.jpg", "test3.jpg"],
                "batch_size": 3
            }
            
            try:
                response = requests.post(
                    f"{self.backend_url}/api/batch/process",
                    json=batch_test_data,
                    timeout=30
                )
                if response.status_code == 200:
                    self.results["vlm_processing"]["batch_endpoint_available"] = True
                else:
                    self.results["vlm_processing"]["batch_endpoint_available"] = False
            except:
                self.results["vlm_processing"]["batch_endpoint_available"] = False
                
        except Exception as e:
            print(f"Error checking VLM configuration: {e}")
            
        # Document expected performance based on VLM model
        self.results["vlm_processing"]["expected_performance"] = {
            "single_image_processing": "1-3 seconds (depends on model and hardware)",
            "batch_processing": "Supports concurrent processing based on MAX_WORKERS",
            "throughput": "Varies based on GPU/CPU availability",
            "memory_per_image": "200-500MB (model dependent)"
        }
        
        print("VLM processing configuration documented")
    
    def test_resource_utilization(self):
        """Test system resource utilization"""
        print("\n=== Testing Resource Utilization ===")
        
        # Get current process info
        backend_pids = []
        frontend_pids = []
        
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                cmdline = ' '.join(proc.info['cmdline'] or [])
                if 'uvicorn' in cmdline or 'backend/run.py' in cmdline:
                    backend_pids.append(proc.info['pid'])
                elif 'react-scripts' in cmdline or 'npm start' in cmdline:
                    frontend_pids.append(proc.info['pid'])
            except:
                pass
        
        # Monitor resources for 10 seconds
        print("Monitoring resource usage for 10 seconds...")
        cpu_samples = []
        memory_samples = []
        disk_io_start = psutil.disk_io_counters()
        
        for i in range(10):
            # CPU usage
            cpu_percent = psutil.cpu_percent(interval=1)
            cpu_samples.append(cpu_percent)
            
            # Memory usage
            memory = psutil.virtual_memory()
            memory_samples.append(memory.percent)
            
            # Process-specific metrics
            backend_cpu = 0
            backend_memory = 0
            frontend_cpu = 0
            frontend_memory = 0
            
            for pid in backend_pids:
                try:
                    proc = psutil.Process(pid)
                    backend_cpu += proc.cpu_percent()
                    backend_memory += proc.memory_info().rss / (1024 * 1024)  # MB
                except:
                    pass
                    
            for pid in frontend_pids:
                try:
                    proc = psutil.Process(pid)
                    frontend_cpu += proc.cpu_percent()
                    frontend_memory += proc.memory_info().rss / (1024 * 1024)  # MB
                except:
                    pass
            
            if i == 9:  # Last iteration
                self.results["resource_utilization"]["backend_processes"] = {
                    "cpu_percent": backend_cpu,
                    "memory_mb": backend_memory
                }
                self.results["resource_utilization"]["frontend_processes"] = {
                    "cpu_percent": frontend_cpu,
                    "memory_mb": frontend_memory
                }
        
        disk_io_end = psutil.disk_io_counters()
        
        self.results["resource_utilization"]["system"] = {
            "cpu_avg_percent": statistics.mean(cpu_samples),
            "cpu_max_percent": max(cpu_samples),
            "memory_avg_percent": statistics.mean(memory_samples),
            "memory_max_percent": max(memory_samples),
            "disk_read_mb": (disk_io_end.read_bytes - disk_io_start.read_bytes) / (1024 * 1024),
            "disk_write_mb": (disk_io_end.write_bytes - disk_io_start.write_bytes) / (1024 * 1024)
        }
        
        print(f"Average CPU usage: {statistics.mean(cpu_samples):.1f}%")
        print(f"Average Memory usage: {statistics.mean(memory_samples):.1f}%")
    
    def generate_report(self):
        """Generate comprehensive performance report"""
        report = f"""
# AI-FARM Performance Test Report

**Test Date**: {self.results['test_date']}

## Executive Summary

This report presents comprehensive performance testing results for the AI-FARM application, including backend API performance, frontend loading times, VLM processing capabilities, and resource utilization metrics.

## 1. Backend Performance

### API Endpoint Response Times

#### Health Endpoint (/api/health)
"""
        
        if "health_endpoint" in self.results["backend"]:
            health = self.results["backend"]["health_endpoint"]
            if "avg_ms" in health:
                report += f"""
- **Average Response Time**: {health['avg_ms']:.2f}ms
- **Median Response Time**: {health['median_ms']:.2f}ms
- **Min/Max**: {health['min_ms']:.2f}ms / {health['max_ms']:.2f}ms
- **Standard Deviation**: {health['std_dev_ms']:.2f}ms
- **Success Rate**: {health['success_rate']:.1f}%
"""
            else:
                report += f"\n- **Error**: {health.get('error', 'Unknown error')}\n"
        
        report += """
#### Metrics Endpoint (/api/metrics)
"""
        
        if "metrics_endpoint" in self.results["backend"]:
            metrics = self.results["backend"]["metrics_endpoint"]
            if "avg_ms" in metrics:
                report += f"""
- **Average Response Time**: {metrics['avg_ms']:.2f}ms
- **Median Response Time**: {metrics['median_ms']:.2f}ms
- **Min/Max**: {metrics['min_ms']:.2f}ms / {metrics['max_ms']:.2f}ms
- **Success Rate**: {metrics['success_rate']:.1f}%
"""
            else:
                report += f"\n- **Error**: {metrics.get('error', 'Unknown error')}\n"

        report += """
### Backend Startup Performance
"""
        
        if "startup_time_seconds" in self.results["backend"]:
            startup = self.results["backend"]["startup_time_seconds"]
            if isinstance(startup, (int, float)):
                report += f"\n- **Startup Time**: {startup:.2f} seconds\n"
            else:
                report += f"\n- **Startup Status**: {startup}\n"
                
        report += """
### Concurrent Request Handling
"""
        
        for concurrent in [10, 50, 100]:
            key = f"concurrent_{concurrent}"
            if key in self.results["backend"]:
                data = self.results["backend"][key]
                report += f"""
#### {concurrent} Concurrent Requests
- **Success Rate**: {data['success_rate']:.1f}%
- **Average Response Time**: {data['avg_response_time_ms']:.2f}ms
- **Max Response Time**: {data['max_response_time_ms']:.2f}ms
"""

        report += """
## 2. Frontend Performance

### Page Load Time
"""
        
        if "page_load" in self.results["frontend"]:
            page_load = self.results["frontend"]["page_load"]
            if "avg_ms" in page_load:
                report += f"""
- **Average Load Time**: {page_load['avg_ms']:.2f}ms
- **Median Load Time**: {page_load['median_ms']:.2f}ms
- **Min/Max**: {page_load['min_ms']:.2f}ms / {page_load['max_ms']:.2f}ms
"""

        report += """
### Bundle Sizes
"""
        
        if "bundle_sizes" in self.results["frontend"]:
            bundles = self.results["frontend"]["bundle_sizes"]
            if isinstance(bundles, dict):
                report += f"""
- **Total Bundle Size**: {bundles['total_size_mb']:.2f} MB
- **JavaScript Size**: {bundles['js_size_mb']:.2f} MB
- **CSS Size**: {bundles['css_size_mb']:.2f} MB
"""
            else:
                report += f"\n- **Status**: {bundles}\n"

        report += """
### Build Performance
"""
        
        if "build_time_seconds" in self.results["frontend"]:
            build_time = self.results["frontend"]["build_time_seconds"]
            if isinstance(build_time, (int, float)):
                report += f"\n- **Build Time**: {build_time:.2f} seconds\n"
            else:
                report += f"\n- **Build Status**: {build_time}\n"

        report += """
## 3. VLM Processing Performance

### Configuration
"""
        
        vlm = self.results.get("vlm_processing", {})
        if "configured_batch_size" in vlm:
            report += f"\n- **Configured Batch Size**: {vlm['configured_batch_size']}\n"
        if "configured_max_workers" in vlm:
            report += f"- **Configured Max Workers**: {vlm['configured_max_workers']}\n"
        if "batch_endpoint_available" in vlm:
            report += f"- **Batch Endpoint Available**: {'Yes' if vlm['batch_endpoint_available'] else 'No'}\n"
            
        if "expected_performance" in vlm:
            perf = vlm["expected_performance"]
            report += f"""
### Expected Performance Characteristics
- **Single Image Processing**: {perf['single_image_processing']}
- **Batch Processing**: {perf['batch_processing']}
- **Throughput**: {perf['throughput']}
- **Memory per Image**: {perf['memory_per_image']}
"""

        report += """
## 4. Resource Utilization

### System Resources
"""
        
        if "system" in self.results["resource_utilization"]:
            system = self.results["resource_utilization"]["system"]
            report += f"""
- **Average CPU Usage**: {system['cpu_avg_percent']:.1f}%
- **Peak CPU Usage**: {system['cpu_max_percent']:.1f}%
- **Average Memory Usage**: {system['memory_avg_percent']:.1f}%
- **Peak Memory Usage**: {system['memory_max_percent']:.1f}%
- **Disk Read (10s)**: {system['disk_read_mb']:.2f} MB
- **Disk Write (10s)**: {system['disk_write_mb']:.2f} MB
"""

        report += """
### Process-Specific Resources
"""
        
        if "backend_processes" in self.results["resource_utilization"]:
            backend = self.results["resource_utilization"]["backend_processes"]
            report += f"""
#### Backend Processes
- **CPU Usage**: {backend['cpu_percent']:.1f}%
- **Memory Usage**: {backend['memory_mb']:.1f} MB
"""
            
        if "frontend_processes" in self.results["resource_utilization"]:
            frontend = self.results["resource_utilization"]["frontend_processes"]
            report += f"""
#### Frontend Processes
- **CPU Usage**: {frontend['cpu_percent']:.1f}%
- **Memory Usage**: {frontend['memory_mb']:.1f} MB
"""

        report += """
## 5. Performance Recommendations

Based on the test results, here are specific recommendations:

### Backend Optimizations
"""
        
        # Generate recommendations based on results
        recommendations = []
        
        # Check backend response times
        if "health_endpoint" in self.results["backend"] and "avg_ms" in self.results["backend"]["health_endpoint"]:
            avg_response = self.results["backend"]["health_endpoint"]["avg_ms"]
            if avg_response > 100:
                recommendations.append("- **High Response Times**: Consider implementing caching for frequently accessed endpoints")
            elif avg_response < 10:
                recommendations.append("- **Excellent Response Times**: Backend performs well under normal load")
                
        # Check concurrent handling
        if "concurrent_100" in self.results["backend"]:
            success_rate = self.results["backend"]["concurrent_100"]["success_rate"]
            if success_rate < 90:
                recommendations.append("- **Concurrent Request Issues**: Consider increasing worker processes or implementing connection pooling")
                
        # Check startup time
        if "startup_time_seconds" in self.results["backend"] and isinstance(self.results["backend"]["startup_time_seconds"], (int, float)):
            if self.results["backend"]["startup_time_seconds"] > 10:
                recommendations.append("- **Slow Startup**: Consider lazy loading of heavy dependencies or using application preloading")
                
        report += "\n".join(recommendations) if recommendations else "- Backend performance is satisfactory"
        
        report += """

### Frontend Optimizations
"""
        
        frontend_recs = []
        
        # Check bundle sizes
        if "bundle_sizes" in self.results["frontend"] and isinstance(self.results["frontend"]["bundle_sizes"], dict):
            total_size = self.results["frontend"]["bundle_sizes"]["total_size_mb"]
            if total_size > 5:
                frontend_recs.append("- **Large Bundle Size**: Implement code splitting and lazy loading for better initial load times")
            if total_size > 10:
                frontend_recs.append("- **Critical Bundle Size**: Consider tree shaking and removing unused dependencies")
                
        # Check build time
        if "build_time_seconds" in self.results["frontend"] and isinstance(self.results["frontend"]["build_time_seconds"], (int, float)):
            if self.results["frontend"]["build_time_seconds"] > 60:
                frontend_recs.append("- **Slow Build Times**: Consider using faster build tools like Vite or implementing build caching")
                
        report += "\n".join(frontend_recs) if frontend_recs else "- Frontend performance is satisfactory"
        
        report += """

### Resource Utilization Recommendations
"""
        
        resource_recs = []
        
        if "system" in self.results["resource_utilization"]:
            cpu_avg = self.results["resource_utilization"]["system"]["cpu_avg_percent"]
            memory_avg = self.results["resource_utilization"]["system"]["memory_avg_percent"]
            
            if cpu_avg > 70:
                resource_recs.append("- **High CPU Usage**: Consider optimizing CPU-intensive operations or scaling horizontally")
            if memory_avg > 80:
                resource_recs.append("- **High Memory Usage**: Implement memory profiling and optimize data structures")
                
        report += "\n".join(resource_recs) if resource_recs else "- Resource utilization is within acceptable limits"
        
        report += """

### VLM Processing Recommendations

- **Batch Processing**: Utilize batch processing for multiple images to improve throughput
- **GPU Acceleration**: If not already enabled, consider GPU acceleration for VLM processing
- **Queue Management**: Implement a queue system for large-scale processing tasks
- **Memory Management**: Monitor memory usage during VLM processing and implement cleanup routines

## Conclusion

The AI-FARM application shows generally good performance characteristics with room for optimization in specific areas. Priority should be given to addressing any identified bottlenecks in concurrent request handling and bundle size optimization.

---
*Generated by AI-FARM Performance Test Suite*
"""
        
        return report
    
    def run_all_tests(self):
        """Run all performance tests"""
        print("Starting AI-FARM Performance Test Suite...")
        print("=" * 50)
        
        # Run all test categories
        self.test_backend_performance()
        self.test_frontend_performance()
        self.test_vlm_processing_performance()
        self.test_resource_utilization()
        
        # Generate and save report
        report = self.generate_report()
        
        # Save JSON results
        with open('performance_test_results.json', 'w') as f:
            json.dump(self.results, f, indent=2)
            
        # Save markdown report
        with open('PERFORMANCE_REPORT.md', 'w') as f:
            f.write(report)
            
        print("\n" + "=" * 50)
        print("Performance testing completed!")
        print("Results saved to:")
        print("- performance_test_results.json (raw data)")
        print("- PERFORMANCE_REPORT.md (formatted report)")
        
        return report


if __name__ == "__main__":
    tester = PerformanceTestSuite()
    report = tester.run_all_tests()
    print("\n" + report)