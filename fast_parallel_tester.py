#!/usr/bin/env python3
"""
Fast parallel testing using concurrent requests
"""

import json
import base64
import requests
import os
import time
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading

class FastParallelTester:
    def __init__(self):
        self.vlm_url = "http://**************:9500/v1/chat/completions"
        self.vlm_model = "VLM-38B-AWQ"
        self.results = []
        self.results_lock = threading.Lock()
        self.progress_lock = threading.Lock()
        self.processed_count = 0
        
        # Simple equipment prompt
        self.prompt = "Is this image primarily showing industrial equipment (crane, vessel, truck, or spreader) with no people visible? Answer only YES or NO."
        
    def encode_image(self, image_path):
        """Encode image to base64"""
        try:
            if os.path.exists(image_path):
                with open(image_path, 'rb') as f:
                    return base64.b64encode(f.read()).decode('utf-8')
        except:
            pass
        return None
    
    def process_case(self, case, session):
        """Process a single case"""
        try:
            source_b64 = self.encode_image(case['source_image'])
            cropped_b64 = self.encode_image(case['cropped_image'])
            
            if not source_b64 or not cropped_b64:
                return None
            
            payload = {
                "model": self.vlm_model,
                "messages": [{
                    "role": "user",
                    "content": [
                        {"type": "text", "text": self.prompt},
                        {"type": "text", "text": "\n\nSOURCE IMAGE:"},
                        {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{source_b64}"}},
                        {"type": "text", "text": "\n\nCROPPED IMAGE:"},
                        {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{cropped_b64}"}}
                    ]
                }],
                "temperature": 0.1,
                "max_tokens": 50
            }
            
            # Try up to 3 times with exponential backoff
            for attempt in range(3):
                try:
                    response = session.post(self.vlm_url, json=payload, timeout=30)
                    
                    if response.status_code == 200:
                        vlm_response = response.json()['choices'][0]['message']['content']
                        is_equipment = "YES" in vlm_response.upper()[:10]
                        predicted_fp = is_equipment
                        actual_fp = case['is_false_positive']
                        
                        result = {
                            'case_number': case['case_number'],
                            'actual_fp': actual_fp,
                            'predicted_fp': predicted_fp,
                            'correct': predicted_fp == actual_fp,
                            'response': vlm_response.strip()
                        }
                        
                        # Update progress
                        with self.progress_lock:
                            self.processed_count += 1
                            if self.processed_count % 10 == 0:
                                print(f"Processed: {self.processed_count}")
                        
                        return result
                    
                    elif response.status_code == 429:
                        # Rate limit - wait and retry
                        time.sleep(2 ** attempt)
                    else:
                        return None
                        
                except requests.exceptions.Timeout:
                    if attempt < 2:
                        time.sleep(1)
                except Exception as e:
                    if attempt < 2:
                        time.sleep(1)
            
            return None
            
        except Exception as e:
            return None
    
    def run_fast_test(self, max_workers=5):
        """Run test with parallel processing"""
        print("="*80)
        print("FAST PARALLEL TEST - SIMPLE EQUIPMENT DETECTION")
        print("="*80)
        
        # Load test data
        with open('valo_batch_round3_complete.json', 'r') as f:
            all_cases = json.load(f)['results']
        
        print(f"Testing {len(all_cases)} cases with {max_workers} parallel workers")
        
        start_time = time.time()
        
        # Create a session for each worker
        sessions = [requests.Session() for _ in range(max_workers)]
        
        # Process in parallel
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Submit all tasks
            future_to_case = {}
            for i, case in enumerate(all_cases):
                session = sessions[i % max_workers]
                future = executor.submit(self.process_case, case, session)
                future_to_case[future] = case
            
            # Collect results
            for future in as_completed(future_to_case):
                result = future.result()
                if result:
                    with self.results_lock:
                        self.results.append(result)
        
        # Calculate final metrics
        elapsed = time.time() - start_time
        
        print("\n" + "="*80)
        print("FINAL RESULTS - FAST PARALLEL TEST")
        print("="*80)
        
        if self.results:
            total = len(self.results)
            correct = sum(r['correct'] for r in self.results)
            
            # Separate by type
            actual_fps = [r for r in self.results if r['actual_fp']]
            actual_valid = [r for r in self.results if not r['actual_fp']]
            
            fp_detected = sum(r['predicted_fp'] for r in actual_fps) if actual_fps else 0
            valid_protected = sum(not r['predicted_fp'] for r in actual_valid) if actual_valid else 0
            
            print(f"Total processed: {total}/{len(all_cases)}")
            print(f"Overall Accuracy: {correct/total*100:.1f}%")
            print(f"FP Detection: {fp_detected}/{len(actual_fps)} ({fp_detected/len(actual_fps)*100:.1f}%)")
            print(f"Valid Protection: {valid_protected}/{len(actual_valid)} ({valid_protected/len(actual_valid)*100:.1f}%)")
            print(f"Time taken: {elapsed/60:.1f} minutes")
            print(f"Processing rate: {total/elapsed:.1f} cases/second")
            
            # Save results
            report = {
                'test_name': 'Simple Equipment Detection (Fast Parallel)',
                'timestamp': datetime.now().isoformat(),
                'total_cases': len(all_cases),
                'processed_cases': total,
                'accuracy': correct/total*100,
                'fp_detection_rate': fp_detected/len(actual_fps)*100 if actual_fps else 0,
                'valid_protection_rate': valid_protected/len(actual_valid)*100 if actual_valid else 0,
                'time_minutes': elapsed/60,
                'processing_rate': total/elapsed,
                'prompt': self.prompt,
                'sample_results': self.results[:50]
            }
            
            # Conclusion
            acc = correct/total*100
            if acc < 50:
                report['conclusion'] = f"Simple equipment detection achieved only {acc:.1f}% accuracy. This is BELOW expectations."
                report['recommendation'] = "The simple approach is not working. Need to investigate why or try different approaches."
            elif acc < 60:
                report['conclusion'] = f"Moderate performance at {acc:.1f}%. Needs improvement."
                report['recommendation'] = "Consider adding PPE detection or refining the equipment detection."
            else:
                report['conclusion'] = f"Good performance at {acc:.1f}%. Simple approach is viable."
                report['recommendation'] = "Simple equipment detection works. Can be refined further."
            
            with open('fast_parallel_test_results.json', 'w') as f:
                json.dump(report, f, indent=2)
            
            print(f"\nResults saved to: fast_parallel_test_results.json")
            print(f"\n{report['conclusion']}")
            print(f"Recommendation: {report['recommendation']}")

if __name__ == "__main__":
    tester = FastParallelTester()
    tester.run_fast_test(max_workers=3)  # Conservative to avoid rate limits