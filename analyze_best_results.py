#!/usr/bin/env python3
"""
Analyze best results from multi-round learning
"""

import json
import os
from datetime import datetime
import pandas as pd

def analyze_learning_results():
    """Analyze all available learning results"""
    
    print("🔍 VALO AI-FARM LEARNING RESULTS ANALYSIS")
    print("=" * 60)
    
    # Round 1 - Baseline (from previous runs)
    round1_stats = {
        'round': 1,
        'valid_protection_rate': 100.0,
        'fp_detection_rate': 1.24,
        'total_cases': 1250
    }
    
    # Round 2 - Intelligent Learning
    if os.path.exists("/home/<USER>/VALO_AI-FARM_2025/valo_intelligent_round2_stats.json"):
        with open("/home/<USER>/VALO_AI-FARM_2025/valo_intelligent_round2_stats.json", 'r') as f:
            round2_stats = json.load(f)
    else:
        round2_stats = None
    
    # Round 3 - In Progress
    if os.path.exists("/home/<USER>/VALO_AI-FARM_2025/valo_intelligent_round3_progress.json"):
        with open("/home/<USER>/VALO_AI-FARM_2025/valo_intelligent_round3_progress.json", 'r') as f:
            round3_progress = json.load(f)
    else:
        round3_progress = None
    
    # Display results
    print("\n📊 ROUND-BY-ROUND PERFORMANCE:")
    print("-" * 60)
    
    # Round 1
    print(f"\nROUND 1 (Baseline - Ultra Conservative):")
    print(f"  Valid Protection: {round1_stats['valid_protection_rate']:.1f}%")
    print(f"  FP Detection: {round1_stats['fp_detection_rate']:.1f}%")
    print(f"  Approach: Safety-first, flag everything uncertain")
    
    # Round 2
    if round2_stats:
        print(f"\nROUND 2 (Intelligent Learning Applied):")
        print(f"  Valid Protection: {round2_stats['valid_protection_rate']:.1f}%")
        print(f"  FP Detection: {round2_stats['fp_detection_rate']:.1f}%")
        print(f"  Improvement: {round2_stats['fp_detection_rate'] / round1_stats['fp_detection_rate']:.1f}x better FP detection")
        print(f"  Equipment Keywords Learned: {', '.join(round2_stats['reasoning_analysis']['equipment_keywords_identified'][:5])}")
        print(f"  Common Mistakes Identified: {sum(round2_stats['reasoning_analysis']['common_mistakes_found'].values())}")
        
        # Issue identified
        if round2_stats['valid_protection_rate'] < 90:
            print(f"  ⚠️  WARNING: Valid protection dropped to {round2_stats['valid_protection_rate']:.1f}%")
            print(f"  ⚠️  Analysis: Over-corrected from Round 1, became too aggressive")
    
    # Round 3
    if round3_progress:
        print(f"\nROUND 3 (Balanced Approach - In Progress):")
        print(f"  Cases Processed: {round3_progress['cases_processed']}/1250 ({round3_progress['cases_processed']/12.5:.1f}%)")
        print(f"  Valid Protection: {round3_progress['valid_protection_rate']:.1f}%")
        print(f"  FP Detection: {round3_progress['fp_detection_rate']:.1f}%")
        print(f"  Status: Correcting Round 2's over-aggressiveness")
    
    # Best results analysis
    print("\n🏆 BEST RESULTS ANALYSIS:")
    print("-" * 60)
    
    # Determine best approach
    if round2_stats and round2_stats['valid_protection_rate'] > 90:
        best_round = 2
        best_valid = round2_stats['valid_protection_rate']
        best_fp = round2_stats['fp_detection_rate']
    else:
        # Use Round 1 as baseline
        best_round = 1
        best_valid = round1_stats['valid_protection_rate']
        best_fp = round1_stats['fp_detection_rate']
    
    print(f"\nCurrent Best Approach: Round {best_round}")
    print(f"  Valid Protection: {best_valid:.1f}%")
    print(f"  FP Detection: {best_fp:.1f}%")
    
    # Calculate business impact
    annual_fp = 14484  # From dataset analysis
    fp_detected = int(annual_fp * (best_fp / 100))
    time_saved = fp_detected * 5  # 5 minutes per case
    cost_saved = time_saved  # $1/minute
    
    print(f"\n💰 BUSINESS IMPACT (Based on Best Results):")
    print(f"  Annual False Positives: {annual_fp:,}")
    print(f"  FPs Detected by AI: {fp_detected:,} ({best_fp:.1f}%)")
    print(f"  Annual Time Saved: {time_saved:,} minutes")
    print(f"  Annual Cost Savings: ${cost_saved:,}")
    
    # Key learnings
    print(f"\n📚 KEY LEARNINGS:")
    print("1. Round 1: Ultra-conservative approach protects all valid cases but misses FPs")
    print("2. Round 2: Intelligent learning dramatically improves FP detection (59x)")
    print("3. Equipment keywords are powerful: crane, spreader, container, vessel")
    print("4. Balance is critical: Can't sacrifice valid case protection for FP detection")
    
    # Recommendations
    print(f"\n🎯 RECOMMENDATIONS:")
    print("1. Complete Round 3 with balanced approach")
    print("2. Set minimum valid protection threshold at 95%")
    print("3. Use equipment detection as primary signal")
    print("4. Apply conservative approach only for unclear cases")
    
    # Save summary
    summary = {
        'timestamp': datetime.now().isoformat(),
        'rounds_analyzed': 3,
        'best_results': {
            'round': best_round,
            'valid_protection_rate': best_valid,
            'fp_detection_rate': best_fp,
            'annual_savings': cost_saved
        },
        'round_comparison': {
            'round1': round1_stats,
            'round2': round2_stats if round2_stats else {},
            'round3_partial': round3_progress if round3_progress else {}
        },
        'key_insights': [
            "Equipment keywords enable 73%+ FP detection",
            "Must maintain 95%+ valid protection",
            "Intelligent learning from reasoning is effective",
            "Balance between safety and efficiency is achievable"
        ]
    }
    
    output_file = "/home/<USER>/VALO_AI-FARM_2025/valo_best_results_analysis.json"
    with open(output_file, 'w') as f:
        json.dump(summary, f, indent=2)
    
    print(f"\n💾 Analysis saved to: {output_file}")
    
    return summary

if __name__ == "__main__":
    analyze_learning_results()