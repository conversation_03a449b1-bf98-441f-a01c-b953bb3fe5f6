#!/usr/bin/env python3
"""
Test enhanced prompt with structure descriptions on full 1250 dataset
Compare against 76.3% baseline to measure improvement
"""

import json
import base64
import requests
import os
from datetime import datetime
import time

VLM_API_URL = "http://**************:9500/v1/chat/completions"
VLM_MODEL = "VLM-38B-AWQ"

class EnhancedFullTester:
    def __init__(self):
        self.session = requests.Session()
        self.results = []
        self.progress_file = 'enhanced_1250_progress.json'
        self.completed_cases = set()
        
        # Load progress if exists
        if os.path.exists(self.progress_file):
            with open(self.progress_file, 'r') as f:
                progress = json.load(f)
                self.results = progress.get('results', [])
                self.completed_cases = set(r['case_number'] for r in self.results)
                print(f"Resuming from {len(self.completed_cases)} completed cases")
    
    def encode_image(self, image_path):
        """Encode image to base64"""
        try:
            if os.path.exists(image_path):
                with open(image_path, 'rb') as f:
                    return base64.b64encode(f.read()).decode('utf-8')
        except:
            pass
        return None
    
    def load_enhanced_prompt(self):
        """Load the enhanced prompt"""
        with open('intelligent_prompt_enhanced_structures.txt', 'r') as f:
            return f.read()
    
    def call_vlm_robust(self, source_b64, cropped_b64, prompt, max_retries=3):
        """Call VLM with retry logic"""
        
        for attempt in range(max_retries):
            try:
                payload = {
                    "model": VLM_MODEL,
                    "messages": [{
                        "role": "user",
                        "content": [
                            {"type": "text", "text": prompt},
                            {"type": "text", "text": "\n\nSOURCE IMAGE (full context):"},
                            {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{source_b64}"}},
                            {"type": "text", "text": "\n\nCROPPED IMAGE (area of concern):"},
                            {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{cropped_b64}"}}
                        ]
                    }],
                    "temperature": 0.1,
                    "max_tokens": 200
                }
                
                timeout = 15 + (attempt * 5)  # Progressive timeout
                response = self.session.post(VLM_API_URL, json=payload, timeout=timeout)
                
                if response.status_code == 200:
                    result = response.json()
                    return result['choices'][0]['message']['content'].strip()
                    
            except Exception as e:
                if attempt < max_retries - 1:
                    wait_time = 2 ** attempt
                    print(f"  Retry {attempt + 1} after {wait_time}s...")
                    time.sleep(wait_time)
                else:
                    print(f"  Failed after {max_retries} attempts")
        
        return None
    
    def parse_response(self, response):
        """Parse VLM response"""
        if not response:
            return None
            
        result = {
            'is_fp': 'YES' in response.upper(),
            'entity_type': 'UNKNOWN',
            'ppe_status': 'N/A',
            'confidence': 0
        }
        
        # Extract entity type
        if 'Entity Type:' in response:
            line = [l for l in response.split('\n') if 'Entity Type:' in l]
            if line:
                entity = line[0].split('Entity Type:')[1].strip().split()[0]
                result['entity_type'] = entity
        
        # Extract PPE status
        if 'PPE Status:' in response:
            line = [l for l in response.split('\n') if 'PPE Status:' in l]
            if line:
                result['ppe_status'] = line[0].split('PPE Status:')[1].strip()
        
        # Extract confidence
        if 'Confidence:' in response:
            line = [l for l in response.split('\n') if 'Confidence:' in l]
            if line:
                try:
                    conf_str = line[0].split('Confidence:')[1].strip()
                    result['confidence'] = int(conf_str.rstrip('%'))
                except:
                    pass
        
        return result
    
    def save_progress(self):
        """Save progress to file"""
        progress = {
            'timestamp': datetime.now().isoformat(),
            'total_cases': len(self.results),
            'results': self.results
        }
        
        with open(self.progress_file, 'w') as f:
            json.dump(progress, f, indent=2)
    
    def test_all_cases(self):
        """Test all 1250 cases with enhanced prompt"""
        
        print("\nTESTING ENHANCED PROMPT ON FULL 1250 DATASET")
        print("="*60)
        
        # Load data
        with open('valo_batch_round3_complete.json', 'r') as f:
            data = json.load(f)
        
        all_cases = data['results']
        total_cases = len(all_cases)
        
        # Load enhanced prompt
        enhanced_prompt = self.load_enhanced_prompt()
        
        print(f"Total cases: {total_cases}")
        print(f"Already completed: {len(self.completed_cases)}")
        print(f"Remaining: {total_cases - len(self.completed_cases)}\n")
        
        start_time = time.time()
        
        for i, case in enumerate(all_cases):
            case_num = case['case_number']
            
            # Skip if already processed
            if case_num in self.completed_cases:
                continue
            
            print(f"\nCase {len(self.results) + 1}/{total_cases}: {case_num}")
            print(f"Type: {case['infringement_type']}")
            if case.get('remarks'):
                print(f"Remark: {case['remarks'][:50]}...")
            
            # Encode images
            source_b64 = self.encode_image(case['source_image'])
            cropped_b64 = self.encode_image(case['cropped_image'])
            
            if not source_b64 or not cropped_b64:
                print("  ERROR: Could not encode images")
                continue
            
            # Call VLM with enhanced prompt
            response = self.call_vlm_robust(source_b64, cropped_b64, enhanced_prompt)
            
            if response:
                parsed = self.parse_response(response)
                if parsed:
                    correct = parsed['is_fp'] == case['is_false_positive']
                    
                    result = {
                        'case_number': case_num,
                        'infringement_type': case['infringement_type'],
                        'actual_fp': case['is_false_positive'],
                        'predicted_fp': parsed['is_fp'],
                        'correct': correct,
                        'entity_type': parsed['entity_type'],
                        'ppe_status': parsed['ppe_status'],
                        'confidence': parsed['confidence'],
                        'remarks': case.get('remarks', '')
                    }
                    
                    self.results.append(result)
                    self.completed_cases.add(case_num)
                    
                    print(f"  Entity: {parsed['entity_type']}")
                    print(f"  Result: {'✓ CORRECT' if correct else '✗ INCORRECT'}")
                    
                    # Save progress every 10 cases
                    if len(self.results) % 10 == 0:
                        self.save_progress()
                        self.print_stats()
            
            # Rate limit
            time.sleep(0.5)
        
        # Final save
        self.save_progress()
        
        elapsed = time.time() - start_time
        print(f"\n\nCompleted in {elapsed/60:.1f} minutes")
        
        self.generate_final_report()
    
    def print_stats(self):
        """Print current statistics"""
        if not self.results:
            return
            
        correct = sum(r['correct'] for r in self.results)
        accuracy = correct / len(self.results) * 100
        
        # FP detection rate
        actual_fps = [r for r in self.results if r['actual_fp']]
        if actual_fps:
            fp_detected = sum(r['predicted_fp'] for r in actual_fps)
            fp_rate = fp_detected / len(actual_fps) * 100
        else:
            fp_rate = 0
        
        print(f"\n  Current stats ({len(self.results)} cases):")
        print(f"  - Accuracy: {accuracy:.1f}%")
        print(f"  - FP Detection: {fp_rate:.1f}%")
    
    def generate_final_report(self):
        """Generate comprehensive report"""
        
        print("\n\n" + "="*60)
        print("ENHANCED PROMPT FINAL REPORT")
        print("="*60)
        
        if not self.results:
            print("No results to report")
            return
        
        # Overall metrics
        correct = sum(r['correct'] for r in self.results)
        accuracy = correct / len(self.results) * 100
        
        # FP detection
        actual_fps = [r for r in self.results if r['actual_fp']]
        fp_detected = sum(r['predicted_fp'] for r in actual_fps)
        fp_rate = fp_detected / len(actual_fps) * 100 if actual_fps else 0
        
        # Valid case protection
        actual_valid = [r for r in self.results if not r['actual_fp']]
        valid_protected = sum(not r['predicted_fp'] for r in actual_valid)
        protection_rate = valid_protected / len(actual_valid) * 100 if actual_valid else 0
        
        print(f"\nOVERALL PERFORMANCE:")
        print(f"Total cases tested: {len(self.results)}")
        print(f"Overall accuracy: {accuracy:.1f}%")
        print(f"False positive detection: {fp_rate:.1f}% (Baseline: 76.3%)")
        print(f"Valid case protection: {protection_rate:.1f}%")
        print(f"Improvement over baseline: {fp_rate - 76.3:+.1f}%")
        
        # Entity detection breakdown
        entity_counts = {}
        for r in self.results:
            entity = r['entity_type']
            entity_counts[entity] = entity_counts.get(entity, 0) + 1
        
        print(f"\nENTITY DETECTION BREAKDOWN:")
        for entity, count in sorted(entity_counts.items(), key=lambda x: x[1], reverse=True):
            pct = count / len(self.results) * 100
            print(f"- {entity}: {count} ({pct:.1f}%)")
        
        # Performance by violation type
        print(f"\nPERFORMANCE BY VIOLATION TYPE:")
        violation_stats = {}
        for r in self.results:
            vtype = r['infringement_type']
            if vtype not in violation_stats:
                violation_stats[vtype] = {'total': 0, 'correct': 0, 'fp_detected': 0, 'fp_total': 0}
            
            violation_stats[vtype]['total'] += 1
            if r['correct']:
                violation_stats[vtype]['correct'] += 1
            if r['actual_fp']:
                violation_stats[vtype]['fp_total'] += 1
                if r['predicted_fp']:
                    violation_stats[vtype]['fp_detected'] += 1
        
        for vtype, stats in sorted(violation_stats.items(), key=lambda x: x[1]['total'], reverse=True):
            acc = stats['correct'] / stats['total'] * 100
            fp_rate = stats['fp_detected'] / stats['fp_total'] * 100 if stats['fp_total'] > 0 else 0
            print(f"\n{vtype}:")
            print(f"  Cases: {stats['total']}")
            print(f"  Accuracy: {acc:.1f}%")
            print(f"  FP Detection: {fp_rate:.1f}%")
        
        # Save comprehensive report
        report = {
            'timestamp': datetime.now().isoformat(),
            'summary': {
                'total_cases': len(self.results),
                'accuracy': accuracy,
                'fp_detection_rate': fp_rate,
                'valid_protection_rate': protection_rate,
                'baseline_fp_rate': 76.3,
                'improvement': fp_rate - 76.3
            },
            'entity_breakdown': entity_counts,
            'violation_breakdown': violation_stats,
            'results': self.results
        }
        
        with open('enhanced_prompt_1250_final_report.json', 'w') as f:
            json.dump(report, f, indent=2)
        
        print(f"\n\nDetailed report saved to: enhanced_prompt_1250_final_report.json")
        
        # Recommendations
        print("\nRECOMMENDATIONS:")
        if fp_rate > 80:
            print("✓ Enhanced prompt significantly improves false positive detection!")
            print("✓ Ready for production deployment")
        elif fp_rate > 76.3:
            print("✓ Enhanced prompt shows improvement over baseline")
            print("→ Consider fine-tuning confidence thresholds")
        else:
            print("⚠ Enhanced prompt needs further optimization")

if __name__ == "__main__":
    tester = EnhancedFullTester()
    tester.test_all_cases()