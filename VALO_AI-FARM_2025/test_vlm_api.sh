#!/bin/bash

# Minimal 1x1 blue pixel PNG as base64
BASE64_IMAGE="iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="

# Create the JSON payload
cat > request.json << EOF
{
  "model": "VLM-38B-AWQ",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "Describe this image"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/png;base64,${BASE64_IMAGE}"
          }
        }
      ]
    }
  ],
  "temperature": 0
}
EOF

# Send the request
curl -X POST http://localhost:9500/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer placeholder-key" \
  -d @request.json \
  --max-time 30 \
  -v

# Clean up
rm -f request.json