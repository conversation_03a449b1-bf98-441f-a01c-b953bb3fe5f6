# VLM-38B-AWQ API Integration Guide

## API Endpoint Information

### Base Configuration
- **Base URL**: `http://localhost:9500/v1` (configured for local deployment)
- **API Key**: `placeholder-key` (placeholder for local development)
- **Model Name**: `VLM-38B-AWQ`
- **Protocol**: OpenAI-compatible REST API

### External Configuration (for reference)
- **External Base URL**: `http://**************:9500/v1`
- **External API Key**: `token-abc123`

### Available Endpoints

#### 1. Chat Completions (Main Vision Endpoint)
- **URL**: `http://localhost:9500/v1/chat/completions`
- **Method**: POST
- **Headers**:
  ```
  Content-Type: application/json
  Authorization: Bearer placeholder-key
  ```

#### 2. Models List (Optional)
- **URL**: `http://localhost:9500/v1/models`
- **Method**: GET
- **Headers**:
  ```
  Authorization: Bearer placeholder-key
  ```

## Integration Examples

### Python (Using OpenAI Library)

```python
from openai import OpenAI
import base64

def image_to_base64(image_path):
    """Convert an image to a Base64-encoded string."""
    with open(image_path, "rb") as image_file:
        encoded_string = base64.b64encode(image_file.read()).decode("utf-8")
    return encoded_string

# Initialize client
client = OpenAI(
    base_url="http://**************:9500/v1",
    api_key="token-abc123",
)

# Prepare image
image_path = "path/to/your/image.jpg"
base64_image = image_to_base64(image_path)

# Make request
completion = client.chat.completions.create(
    model="VLM-38B-AWQ",
    messages=[{
        "role": "user",
        "content": [
            {"type": "text", "text": "Describe this image"},
            {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{base64_image}"}},
        ],
    }],
    temperature=0,
)

print(completion.choices[0].message.content)
```

### cURL Command

```bash
#!/bin/bash

# Convert image to base64
BASE64_IMAGE=$(base64 -w 0 < "path/to/image.jpg")

# Create request payload
cat > request.json << EOF
{
  "model": "VLM-38B-AWQ",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "Describe this image"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,${BASE64_IMAGE}"
          }
        }
      ]
    }
  ],
  "temperature": 0
}
EOF

# Send request
curl -X POST http://**************:9500/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer token-abc123" \
  -d @request.json
```

### JavaScript/Node.js

```javascript
const fs = require('fs');
const axios = require('axios');

// Convert image to base64
function imageToBase64(imagePath) {
    const imageBuffer = fs.readFileSync(imagePath);
    return imageBuffer.toString('base64');
}

async function analyzeImage(imagePath, prompt) {
    const base64Image = imageToBase64(imagePath);
    
    const response = await axios.post(
        'http://**************:9500/v1/chat/completions',
        {
            model: 'VLM-38B-AWQ',
            messages: [{
                role: 'user',
                content: [
                    { type: 'text', text: prompt },
                    { 
                        type: 'image_url', 
                        image_url: { 
                            url: `data:image/jpeg;base64,${base64Image}` 
                        } 
                    }
                ]
            }],
            temperature: 0
        },
        {
            headers: {
                'Content-Type': 'application/json',
                'Authorization': 'Bearer token-abc123'
            }
        }
    );
    
    return response.data.choices[0].message.content;
}

// Usage
analyzeImage('path/to/image.jpg', 'Describe this image')
    .then(result => console.log(result))
    .catch(error => console.error(error));
```

## Request Format

### Request Body Structure
```json
{
  "model": "VLM-38B-AWQ",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "Your prompt here"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,{base64_encoded_image}"
          }
        }
      ]
    }
  ],
  "temperature": 0,
  "max_tokens": 1000,  // Optional
  "top_p": 1.0,        // Optional
  "n": 1               // Optional
}
```

### Response Format
```json
{
  "id": "unique_id",
  "object": "chat.completion",
  "created": 1751544527,
  "model": "VLM-38B-AWQ",
  "choices": [
    {
      "index": 0,
      "message": {
        "role": "assistant",
        "content": "The AI's response describing the image",
        "reasoning_content": null,
        "tool_calls": null
      },
      "logprobs": null,
      "finish_reason": "stop"
    }
  ],
  "usage": {
    "prompt_tokens": 307,
    "total_tokens": 329,
    "completion_tokens": 22
  }
}
```

## Important Notes

1. **Image Formats**: Supports JPEG, PNG, GIF, and WebP formats
2. **Image Size**: Base64 encoding increases data size by ~33%. Consider image compression
3. **Timeout**: Set appropriate timeouts (30+ seconds recommended for complex images)
4. **Rate Limiting**: Check with system admin for rate limits
5. **Error Handling**: Implement retry logic for network failures
6. **Authentication**: Always include the Bearer token in the Authorization header

## Common Use Cases

### Safety Violation Detection
```python
prompt = """Analyze this image for safety violations. 
Look for:
- PPE compliance (hard hats, safety vests, gloves)
- Unsafe behaviors or positions
- Equipment hazards
- Environmental dangers

Provide a detailed analysis with confidence scores."""
```

### Image Classification
```python
prompt = "Classify this image into one of these categories: genuine_violation, false_positive, unclear. Explain your reasoning."
```

### Batch Processing
```python
async def process_batch(image_paths):
    results = []
    for path in image_paths:
        result = await analyze_image(path)
        results.append(result)
    return results
```

## Troubleshooting

### Connection Refused
- Verify you're on the same network as the server
- Check if the service is running on port 9500
- Confirm firewall rules allow access

### Invalid Response
- Ensure image is properly base64 encoded
- Check Content-Type header is set correctly
- Verify the model name is exactly "VLM-38B-AWQ"

### Timeout Errors
- Increase client timeout to 60+ seconds
- Reduce image size/quality if possible
- Consider implementing async requests for batch processing

## Contact Information
For technical issues or access requests, contact the AI-FARM system administrator.