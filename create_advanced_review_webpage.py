#!/usr/bin/env python3
"""
Create advanced review webpage with:
1. 4-category filtering (Valid/Invalid × Passed/Failed)
2. Violation type filtering
3. Combined filtering capabilities
"""

import json
import re
from pathlib import Path
from datetime import datetime
import pandas as pd
from collections import defaultdict

class AdvancedReviewWebpageCreator:
    def __init__(self):
        self.cases = {
            'valid_passed': [],    # TP correctly kept
            'valid_failed': [],    # TP wrongly filtered (CRITICAL)
            'invalid_passed': [],  # FP correctly filtered
            'invalid_failed': []   # FP not filtered
        }
        self.all_cases = []
        self.violation_types = defaultdict(int)
        self.violation_type_accuracy = defaultdict(lambda: {'total': 0, 'correct': 0})
        
    def load_ground_truth_with_violations(self):
        """Load ground truth and violation types from CSV"""
        csv_file = 'ai_farm_images_fixed_250703/psa_valo_violation_report_without_images_csv_2025-07-03_14-14-01.CSV'
        df = pd.read_csv(csv_file)
        
        ground_truth = {}
        violation_types = {}
        
        for _, row in df.iterrows():
            case_number = row['Case Int. ID']
            status = str(row['Alert Status']).strip() if pd.notna(row['Alert Status']) else ''
            violation_type = str(row['Type of Infringement']).strip() if pd.notna(row['Type of Infringement']) else 'Unknown'
            
            # Invalid = False Positive, everything else = True Positive
            if status.lower() == 'invalid':
                ground_truth[case_number] = 'FALSE_POSITIVE'
            else:
                ground_truth[case_number] = 'TRUE_POSITIVE'
                
            violation_types[case_number] = violation_type
            self.violation_types[violation_type] += 1
                
        return ground_truth, violation_types
    
    def extract_vlm_prediction(self, confidence_content):
        """Extract VLM's prediction from confidence analysis"""
        if not confidence_content:
            return 'UNCERTAIN', {}
            
        content_lower = confidence_content.lower()
        
        metrics = {
            'fp_likelihood': None,
            'person_present': None,
            'ppe_compliance': None,
            'violation_confidence': None
        }
        
        # Extract FP likelihood
        fp_match = re.search(r'\*\*false positive likelihood\*\*[:\s]+(\d+)%', content_lower)
        if fp_match:
            metrics['fp_likelihood'] = int(fp_match.group(1))
            
        # Extract person presence
        if re.search(r'\*\*person present\*\*:\s*yes', content_lower):
            metrics['person_present'] = 'YES'
        elif re.search(r'\*\*person present\*\*:\s*no', content_lower):
            metrics['person_present'] = 'NO'
            
        # Extract PPE compliance
        ppe_match = re.search(r'ppe[_\s]compliance:\s*(\w+)', content_lower)
        if ppe_match:
            metrics['ppe_compliance'] = ppe_match.group(1).upper()
            
        # Determine VLM prediction
        if metrics['fp_likelihood'] is not None:
            if metrics['fp_likelihood'] > 50:
                return 'FALSE_POSITIVE', metrics
            else:
                return 'TRUE_POSITIVE', metrics
                
        # Fallback rules
        if metrics['person_present'] == 'NO':
            return 'FALSE_POSITIVE', metrics
        elif metrics['ppe_compliance'] == 'COMPLETE':
            return 'FALSE_POSITIVE', metrics
        elif metrics['ppe_compliance'] in ['INCOMPLETE', 'NONE']:
            return 'TRUE_POSITIVE', metrics
            
        return 'UNCERTAIN', metrics
    
    def parse_case_data(self, case_content, case_number):
        """Parse a single case from markdown"""
        case_data = {
            'case_number': case_number,
            'description': '',
            'confidence_response': '',
            'processing_time': '',
            'cropped_image': '',
            'metrics': {}
        }
        
        # Extract processing time
        time_match = re.search(r'\*\*Processing Time\*\*:\s*([\d.]+)\s*seconds', case_content)
        if time_match:
            case_data['processing_time'] = time_match.group(1)
            
        # Extract image path
        img_match = re.search(r'\*\*Cropped Image\*\*:\s*`([^`]+)`', case_content)
        if img_match:
            case_data['cropped_image'] = img_match.group(1)
            
        # Extract description section
        desc_start = case_content.find('### Comprehensive Description')
        desc_end = case_content.find('### Confidence Analysis')
        if desc_start != -1 and desc_end != -1:
            desc_section = case_content[desc_start:desc_end]
            # Extract content between ``` markers
            desc_match = re.search(r'```\n(.*?)```', desc_section, re.DOTALL)
            if desc_match:
                case_data['description'] = desc_match.group(1).strip()
                
        # Extract confidence analysis
        conf_start = case_content.find('### Confidence Analysis')
        if conf_start != -1:
            conf_end = case_content.find('### Raw Confidence Response', conf_start)
            if conf_end == -1:
                conf_end = case_content.find('\n---', conf_start)
            if conf_end == -1:
                conf_end = len(case_content)
            case_data['confidence_response'] = case_content[conf_start:conf_end].strip()
            
        return case_data
    
    def load_all_cases(self):
        """Load all cases from VLM analysis files"""
        ground_truth, violation_types = self.load_ground_truth_with_violations()
        
        # Load FALSE POSITIVE cases
        fp_file = Path('valo_comprehensive_data/false_positives/false_positive_analysis_20250725_232934.md')
        if fp_file.exists():
            with open(fp_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
            cases = re.split(r'\n## Case: ', content)
            for case in cases[1:]:  # Skip header
                case_num = case.split('\n')[0].strip()
                case_data = self.parse_case_data(case, case_num)
                
                # Get VLM prediction
                vlm_pred, metrics = self.extract_vlm_prediction(case_data['confidence_response'])
                case_data['vlm_prediction'] = vlm_pred
                case_data['metrics'] = metrics
                
                # Get ground truth and violation type
                case_data['ground_truth'] = ground_truth.get(case_num, 'UNKNOWN')
                case_data['violation_type'] = violation_types.get(case_num, 'Unknown')
                
                # Categorize
                self.categorize_case(case_data)
                self.all_cases.append(case_data)
                
        # Load TRUE POSITIVE cases  
        tp_file = Path('valo_comprehensive_data/true_positives/true_positive_analysis_20250725_232934.md')
        if tp_file.exists():
            with open(tp_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
            cases = re.split(r'\n## Case: ', content)
            for case in cases[1:]:
                case_num = case.split('\n')[0].strip()
                case_data = self.parse_case_data(case, case_num)
                
                # Get VLM prediction
                vlm_pred, metrics = self.extract_vlm_prediction(case_data['confidence_response'])
                case_data['vlm_prediction'] = vlm_pred
                case_data['metrics'] = metrics
                
                # Get ground truth and violation type
                case_data['ground_truth'] = ground_truth.get(case_num, 'UNKNOWN')
                case_data['violation_type'] = violation_types.get(case_num, 'Unknown')
                
                # Categorize
                self.categorize_case(case_data)
                self.all_cases.append(case_data)
                
        # Calculate violation type accuracy
        for case in self.all_cases:
            vtype = case['violation_type']
            self.violation_type_accuracy[vtype]['total'] += 1
            if ((case['ground_truth'] == 'TRUE_POSITIVE' and case['vlm_prediction'] == 'TRUE_POSITIVE') or
                (case['ground_truth'] == 'FALSE_POSITIVE' and case['vlm_prediction'] == 'FALSE_POSITIVE')):
                self.violation_type_accuracy[vtype]['correct'] += 1
    
    def categorize_case(self, case_data):
        """Categorize case into one of 4 categories"""
        gt = case_data['ground_truth']
        vlm = case_data['vlm_prediction']
        
        if gt == 'TRUE_POSITIVE':
            if vlm == 'TRUE_POSITIVE':
                self.cases['valid_passed'].append(case_data)
            elif vlm == 'FALSE_POSITIVE':
                self.cases['valid_failed'].append(case_data)  # CRITICAL!
        elif gt == 'FALSE_POSITIVE':
            if vlm == 'FALSE_POSITIVE':
                self.cases['invalid_passed'].append(case_data)
            elif vlm == 'TRUE_POSITIVE':
                self.cases['invalid_failed'].append(case_data)
    
    def create_webpage(self):
        """Create the advanced review webpage"""
        total_cases = len(self.all_cases)
        
        # Calculate stats for each violation type
        violation_stats = {}
        for vtype, acc_data in self.violation_type_accuracy.items():
            accuracy = acc_data['correct'] / acc_data['total'] * 100 if acc_data['total'] > 0 else 0
            violation_stats[vtype] = {
                'total': acc_data['total'],
                'accuracy': accuracy
            }
        
        html = f"""<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VALO Advanced Review System - Category & Violation Type Analysis</title>
    <style>
        * {{
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }}
        
        body {{
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f7fa;
            line-height: 1.6;
            color: #333;
        }}
        
        .header {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }}
        
        .header h1 {{
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
        }}
        
        .summary-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            padding: 2rem;
            max-width: 1400px;
            margin: 0 auto;
        }}
        
        .summary-card {{
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            transition: transform 0.2s, box-shadow 0.2s;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }}
        
        .summary-card:hover {{
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(0,0,0,0.15);
        }}
        
        .summary-card.valid-passed {{
            border-top: 4px solid #10b981;
        }}
        
        .summary-card.valid-failed {{
            border-top: 4px solid #ef4444;
            background: #fef2f2;
        }}
        
        .summary-card.invalid-passed {{
            border-top: 4px solid #3b82f6;
        }}
        
        .summary-card.invalid-failed {{
            border-top: 4px solid #f59e0b;
        }}
        
        .summary-card h3 {{
            font-size: 1.25rem;
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }}
        
        .count {{
            font-size: 3rem;
            font-weight: bold;
            margin: 1rem 0;
        }}
        
        .percentage {{
            font-size: 0.875rem;
            color: #6b7280;
        }}
        
        .filter-controls {{
            background: white;
            padding: 2rem;
            margin: 0 2rem 2rem;
            border-radius: 12px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            max-width: 1400px;
            margin-left: auto;
            margin-right: auto;
        }}
        
        .filter-section {{
            margin-bottom: 1.5rem;
        }}
        
        .filter-section h4 {{
            color: #374151;
            margin-bottom: 0.75rem;
            font-size: 1.125rem;
        }}
        
        .filter-buttons {{
            display: flex;
            gap: 0.75rem;
            flex-wrap: wrap;
        }}
        
        .filter-btn {{
            padding: 0.5rem 1rem;
            border: 2px solid #e5e7eb;
            background: white;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s;
            font-size: 0.875rem;
            font-weight: 500;
            position: relative;
        }}
        
        .filter-btn:hover {{
            background: #f3f4f6;
        }}
        
        .filter-btn.active {{
            background: #4f46e5;
            color: white;
            border-color: #4f46e5;
        }}
        
        .filter-btn.category-filter.active {{
            background: #7c3aed;
            border-color: #7c3aed;
        }}
        
        .filter-btn.violation-filter.active {{
            background: #2563eb;
            border-color: #2563eb;
        }}
        
        .filter-btn .badge {{
            position: absolute;
            top: -8px;
            right: -8px;
            background: #ef4444;
            color: white;
            border-radius: 10px;
            padding: 2px 6px;
            font-size: 0.75rem;
            font-weight: bold;
        }}
        
        .active-filters {{
            margin-top: 1rem;
            padding: 0.75rem;
            background: #f3f4f6;
            border-radius: 8px;
            font-size: 0.875rem;
        }}
        
        .violation-stats {{
            background: white;
            padding: 2rem;
            margin: 0 2rem 2rem;
            border-radius: 12px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            max-width: 1400px;
            margin-left: auto;
            margin-right: auto;
        }}
        
        .violation-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }}
        
        .violation-stat {{
            background: #f9fafb;
            padding: 1rem;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
            text-align: center;
        }}
        
        .violation-stat h5 {{
            font-size: 0.875rem;
            color: #6b7280;
            margin-bottom: 0.5rem;
        }}
        
        .violation-stat .stat-value {{
            font-size: 1.5rem;
            font-weight: bold;
            color: #374151;
        }}
        
        .violation-stat .accuracy {{
            font-size: 0.875rem;
            color: #9ca3af;
        }}
        
        .case-viewer {{
            background: white;
            margin: 0 2rem 2rem;
            padding: 2rem;
            border-radius: 12px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            max-width: 1400px;
            margin-left: auto;
            margin-right: auto;
        }}
        
        .case {{
            display: none;
            animation: fadeIn 0.3s;
        }}
        
        @keyframes fadeIn {{
            from {{ opacity: 0; }}
            to {{ opacity: 1; }}
        }}
        
        .case-header {{
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid #e5e7eb;
        }}
        
        .case-info {{
            flex: 1;
        }}
        
        .case-number {{
            font-size: 1.5rem;
            font-weight: bold;
        }}
        
        .case-meta {{
            margin-top: 0.5rem;
            color: #6b7280;
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
        }}
        
        .meta-item {{
            display: flex;
            align-items: center;
            gap: 0.25rem;
        }}
        
        .badges {{
            display: flex;
            gap: 0.5rem;
            align-items: center;
        }}
        
        .category-badge, .violation-badge {{
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: 500;
            font-size: 0.875rem;
        }}
        
        .violation-badge {{
            background: #e0e7ff;
            color: #3730a3;
        }}
        
        .valid-passed .category-badge {{
            background: #d1fae5;
            color: #065f46;
        }}
        
        .valid-failed .category-badge {{
            background: #fee2e2;
            color: #991b1b;
        }}
        
        .invalid-passed .category-badge {{
            background: #dbeafe;
            color: #1e40af;
        }}
        
        .invalid-failed .category-badge {{
            background: #fed7aa;
            color: #92400e;
        }}
        
        .case-content {{
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
        }}
        
        @media (max-width: 1024px) {{
            .case-content {{
                grid-template-columns: 1fr;
            }}
        }}
        
        .images-section {{
            display: grid;
            grid-template-columns: 1fr;
            gap: 1rem;
        }}
        
        .image-container {{
            background: #f3f4f6;
            border-radius: 8px;
            padding: 1rem;
            text-align: center;
        }}
        
        .image-container img {{
            max-width: 100%;
            height: 300px;
            object-fit: contain;
            border-radius: 4px;
        }}
        
        .analysis-section {{
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
        }}
        
        .metrics-grid {{
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 1rem;
        }}
        
        .metric {{
            background: #f9fafb;
            padding: 1rem;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
        }}
        
        .metric-label {{
            font-size: 0.875rem;
            color: #6b7280;
            margin-bottom: 0.25rem;
        }}
        
        .metric-value {{
            font-size: 1.125rem;
            font-weight: 600;
        }}
        
        .description-box {{
            background: #f9fafb;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 1.5rem;
            max-height: 400px;
            overflow-y: auto;
        }}
        
        .description-box h4 {{
            margin-bottom: 1rem;
            color: #374151;
        }}
        
        .description-content {{
            font-family: 'Courier New', monospace;
            font-size: 0.875rem;
            line-height: 1.5;
            white-space: pre-wrap;
        }}
        
        .navigation {{
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 2px solid #e5e7eb;
        }}
        
        .nav-btn {{
            padding: 0.75rem 1.5rem;
            background: #4f46e5;
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            transition: background 0.2s;
        }}
        
        .nav-btn:hover {{
            background: #4338ca;
        }}
        
        .nav-btn:disabled {{
            background: #9ca3af;
            cursor: not-allowed;
        }}
        
        .case-counter {{
            font-weight: 500;
            color: #374151;
        }}
        
        .stats-section {{
            background: white;
            margin: 0 2rem 2rem;
            padding: 2rem;
            border-radius: 12px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            max-width: 1400px;
            margin-left: auto;
            margin-right: auto;
        }}
        
        .insights {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-top: 1.5rem;
        }}
        
        .insight-card {{
            background: #fafbfc;
            padding: 1.5rem;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
        }}
        
        .insight-card h4 {{
            color: #374151;
            margin-bottom: 0.5rem;
        }}
        
        .icon {{
            width: 24px;
            height: 24px;
            display: inline-block;
            vertical-align: middle;
        }}
        
        .clear-filters {{
            padding: 0.5rem 1rem;
            background: #ef4444;
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 0.875rem;
            font-weight: 500;
            margin-left: 1rem;
        }}
        
        .clear-filters:hover {{
            background: #dc2626;
        }}
    </style>
</head>
<body>
    <div class="header">
        <h1>VALO Advanced Review System</h1>
        <p>Category & Violation Type Analysis</p>
    </div>

    <div class="summary-grid">
        <div class="summary-card valid-passed" onclick="filterByCategory('valid_passed')">
            <h3><span class="icon">✅</span> Valid Cases Passed</h3>
            <div class="count">{len(self.cases['valid_passed'])}</div>
            <div class="percentage">{len(self.cases['valid_passed'])/total_cases*100:.1f}% of all cases</div>
            <p>Valid violations correctly identified</p>
        </div>
        
        <div class="summary-card valid-failed" onclick="filterByCategory('valid_failed')">
            <h3><span class="icon">🚨</span> Valid Cases Failed</h3>
            <div class="count">{len(self.cases['valid_failed'])}</div>
            <div class="percentage">{len(self.cases['valid_failed'])/total_cases*100:.1f}% of all cases</div>
            <p>CRITICAL: Valid violations wrongly filtered!</p>
        </div>
        
        <div class="summary-card invalid-passed" onclick="filterByCategory('invalid_passed')">
            <h3><span class="icon">✅</span> Invalid Cases Passed</h3>
            <div class="count">{len(self.cases['invalid_passed'])}</div>
            <div class="percentage">{len(self.cases['invalid_passed'])/total_cases*100:.1f}% of all cases</div>
            <p>False positives correctly filtered</p>
        </div>
        
        <div class="summary-card invalid-failed" onclick="filterByCategory('invalid_failed')">
            <h3><span class="icon">⚠️</span> Invalid Cases Failed</h3>
            <div class="count">{len(self.cases['invalid_failed'])}</div>
            <div class="percentage">{len(self.cases['invalid_failed'])/total_cases*100:.1f}% of all cases</div>
            <p>False positives not filtered</p>
        </div>
    </div>

    <div class="violation-stats">
        <h3>Violation Type Performance</h3>
        <div class="violation-grid">
"""
        
        # Add violation type statistics
        for vtype, stats in sorted(violation_stats.items(), key=lambda x: x[1]['total'], reverse=True):
            html += f"""
            <div class="violation-stat">
                <h5>{vtype}</h5>
                <div class="stat-value">{stats['total']}</div>
                <div class="accuracy">Accuracy: {stats['accuracy']:.1f}%</div>
            </div>
"""
        
        html += """
        </div>
    </div>

    <div class="filter-controls">
        <div class="filter-section">
            <h4>Filter by Category</h4>
            <div class="filter-buttons">
                <button class="filter-btn category-filter active" data-category="all" onclick="filterByCategory('all')">
                    All Categories ({total})
                </button>
                <button class="filter-btn category-filter" data-category="valid_passed" onclick="filterByCategory('valid_passed')">
                    Valid Passed ({})
                </button>
                <button class="filter-btn category-filter" data-category="valid_failed" onclick="filterByCategory('valid_failed')">
                    Valid Failed ({})
                    <span class="badge">{}</span>
                </button>
                <button class="filter-btn category-filter" data-category="invalid_passed" onclick="filterByCategory('invalid_passed')">
                    Invalid Passed ({})
                </button>
                <button class="filter-btn category-filter" data-category="invalid_failed" onclick="filterByCategory('invalid_failed')">
                    Invalid Failed ({})
                </button>
            </div>
        </div>
        
        <div class="filter-section">
            <h4>Filter by Violation Type</h4>
            <div class="filter-buttons" id="violation-filters">
                <button class="filter-btn violation-filter active" data-violation="all" onclick="filterByViolation('all')">
                    All Types ({total})
                </button>
""".format(
    total,
    len(self.cases['valid_passed']),
    len(self.cases['valid_failed']),
    len(self.cases['valid_failed']) if len(self.cases['valid_failed']) > 0 else '',
    len(self.cases['invalid_passed']),
    len(self.cases['invalid_failed']),
    total
)
        
        # Add violation type filter buttons
        for vtype in sorted(self.violation_types.keys()):
            count = self.violation_types[vtype]
            html += f"""
                <button class="filter-btn violation-filter" data-violation="{vtype}" onclick="filterByViolation('{vtype}')">
                    {vtype} ({count})
                </button>
"""
        
        html += """
            </div>
        </div>
        
        <div class="active-filters" id="active-filters" style="display: none;">
            <strong>Active Filters:</strong> 
            <span id="filter-info"></span>
            <button class="clear-filters" onclick="clearAllFilters()">Clear All</button>
        </div>
    </div>

    <div class="case-viewer">
        <div id="cases-container">
"""
        
        # Add all cases
        case_index = 0
        for category, cases in self.cases.items():
            for case in cases:
                # Determine image paths
                img_type = 'invalid' if case['ground_truth'] == 'FALSE_POSITIVE' else 'valid'
                source_img = f"/home/<USER>/VALO_AI-FARM_2025/ai_farm_images_fixed_250703/ai_farm_images_fixed/{img_type}/{case['case_number']}_source_{img_type}.JPEG"
                cropped_img = case['cropped_image'] if case['cropped_image'] else f"/home/<USER>/VALO_AI-FARM_2025/ai_farm_images_fixed_250703/ai_farm_images_fixed/{img_type}/{case['case_number']}_cropped_{img_type}.JPEG"
                
                html += f"""
            <div class="case {category}" 
                 data-category="{category}" 
                 data-violation="{case['violation_type']}"
                 data-index="{case_index}" 
                 style="{'display: block;' if case_index == 0 else ''}">
                <div class="case-header">
                    <div class="case-info">
                        <div class="case-number">Case: {case['case_number']}</div>
                        <div class="case-meta">
                            <div class="meta-item">
                                <strong>Ground Truth:</strong> {case['ground_truth']}
                            </div>
                            <div class="meta-item">
                                <strong>VLM:</strong> {case['vlm_prediction']}
                            </div>
                        </div>
                    </div>
                    <div class="badges">
                        <div class="category-badge">{self.get_category_label(category)}</div>
                        <div class="violation-badge">{case['violation_type']}</div>
                    </div>
                </div>
                
                <div class="case-content">
                    <div class="images-section">
                        <div class="image-container">
                            <h4>Source Image</h4>
                            <img src="file://{source_img}" alt="Source Image">
                        </div>
                        <div class="image-container">
                            <h4>Cropped Image (Alert Region)</h4>
                            <img src="file://{cropped_img}" alt="Cropped Image">
                        </div>
                    </div>
                    
                    <div class="analysis-section">
                        <div>
                            <h4>VLM Analysis Metrics</h4>
                            <div class="metrics-grid">
                                <div class="metric">
                                    <div class="metric-label">FP Likelihood</div>
                                    <div class="metric-value">{case['metrics'].get('fp_likelihood', 'N/A')}%</div>
                                </div>
                                <div class="metric">
                                    <div class="metric-label">Person Present</div>
                                    <div class="metric-value">{case['metrics'].get('person_present', 'N/A')}</div>
                                </div>
                                <div class="metric">
                                    <div class="metric-label">PPE Compliance</div>
                                    <div class="metric-value">{case['metrics'].get('ppe_compliance', 'N/A')}</div>
                                </div>
                                <div class="metric">
                                    <div class="metric-label">Processing Time</div>
                                    <div class="metric-value">{case.get('processing_time', 'N/A')}s</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="description-box">
                            <h4>VLM Description</h4>
                            <div class="description-content">{case['description'][:800]}...</div>
                        </div>
                        
                        <div class="description-box">
                            <h4>Confidence Analysis</h4>
                            <div class="description-content">{case['confidence_response']}</div>
                        </div>
                    </div>
                </div>
                
                <div class="navigation">
                    <button class="nav-btn" onclick="previousCase()">← Previous</button>
                    <div class="case-counter">
                        <span id="current-index">{case_index + 1}</span> / 
                        <span id="total-filtered">{total_cases}</span>
                    </div>
                    <button class="nav-btn" onclick="nextCase()">Next →</button>
                </div>
            </div>
"""
                case_index += 1
        
        html += """
        </div>
    </div>

    <div class="stats-section">
        <h3>Performance Insights</h3>
        <div class="insights">
            <div class="insight-card">
                <h4>Overall Accuracy</h4>
                <p>VLM correctly classified <strong>{:.1f}%</strong> of cases</p>
            </div>
            <div class="insight-card">
                <h4>Critical Error Rate</h4>
                <p><strong>{}</strong> valid violations would be wrongly filtered ({:.1f}%)</p>
            </div>
            <div class="insight-card">
                <h4>FP Detection Rate</h4>
                <p><strong>{:.1f}%</strong> of false positives correctly identified</p>
            </div>
        </div>
    </div>

    <script>
        let currentIndex = 0;
        let allCases = [];
        let filteredCases = [];
        let currentCategoryFilter = 'all';
        let currentViolationFilter = 'all';

        function initializeCases() {{
            allCases = Array.from(document.querySelectorAll('.case'));
            applyFilters();
        }}

        function filterByCategory(category) {{
            currentCategoryFilter = category;
            
            // Update button states
            document.querySelectorAll('.category-filter').forEach(btn => {{
                btn.classList.remove('active');
                if (btn.getAttribute('data-category') === category) {{
                    btn.classList.add('active');
                }}
            }});
            
            applyFilters();
        }}

        function filterByViolation(violation) {{
            currentViolationFilter = violation;
            
            // Update button states
            document.querySelectorAll('.violation-filter').forEach(btn => {{
                btn.classList.remove('active');
                if (btn.getAttribute('data-violation') === violation) {{
                    btn.classList.add('active');
                }}
            }});
            
            applyFilters();
        }}

        function applyFilters() {{
            filteredCases = allCases.filter(caseEl => {{
                const categoryMatch = currentCategoryFilter === 'all' || 
                                    caseEl.getAttribute('data-category') === currentCategoryFilter;
                const violationMatch = currentViolationFilter === 'all' || 
                                     caseEl.getAttribute('data-violation') === currentViolationFilter;
                return categoryMatch && violationMatch;
            }});
            
            currentIndex = 0;
            updateDisplay();
            updateActiveFilters();
        }}

        function updateActiveFilters() {{
            const filterInfo = document.getElementById('filter-info');
            const activeFiltersDiv = document.getElementById('active-filters');
            
            let filters = [];
            if (currentCategoryFilter !== 'all') {{
                filters.push(`Category: ${{currentCategoryFilter.replace('_', ' ')}}`);
            }}
            if (currentViolationFilter !== 'all') {{
                filters.push(`Violation: ${{currentViolationFilter}}`);
            }}
            
            if (filters.length > 0) {{
                filterInfo.textContent = filters.join(', ');
                activeFiltersDiv.style.display = 'block';
            }} else {{
                activeFiltersDiv.style.display = 'none';
            }}
        }}

        function clearAllFilters() {{
            currentCategoryFilter = 'all';
            currentViolationFilter = 'all';
            
            document.querySelectorAll('.filter-btn').forEach(btn => {{
                btn.classList.remove('active');
                if (btn.getAttribute('data-category') === 'all' || 
                    btn.getAttribute('data-violation') === 'all') {{
                    btn.classList.add('active');
                }}
            }});
            
            applyFilters();
        }}

        function updateDisplay() {{
            // Hide all cases
            allCases.forEach(c => {{
                c.style.display = 'none';
            }});
            
            // Show current case
            if (filteredCases.length > 0 && currentIndex < filteredCases.length) {{
                filteredCases[currentIndex].style.display = 'block';
                document.getElementById('current-index').textContent = currentIndex + 1;
                document.getElementById('total-filtered').textContent = filteredCases.length;
            }} else {{
                // No cases match filters
                document.getElementById('current-index').textContent = '0';
                document.getElementById('total-filtered').textContent = '0';
            }}
            
            // Update navigation buttons
            const prevBtn = document.querySelector('.nav-btn');
            const nextBtn = document.querySelectorAll('.nav-btn')[1];
            
            if (currentIndex === 0 || filteredCases.length === 0) {{
                prevBtn.disabled = true;
            }} else {{
                prevBtn.disabled = false;
            }}
            
            if (currentIndex >= filteredCases.length - 1 || filteredCases.length === 0) {{
                nextBtn.disabled = true;
            }} else {{
                nextBtn.disabled = false;
            }}
        }}

        function nextCase() {{
            if (currentIndex < filteredCases.length - 1) {{
                currentIndex++;
                updateDisplay();
            }}
        }}

        function previousCase() {{
            if (currentIndex > 0) {{
                currentIndex--;
                updateDisplay();
            }}
        }}

        // Keyboard navigation
        document.addEventListener('keydown', (e) => {{
            if (e.key === 'ArrowLeft') previousCase();
            if (e.key === 'ArrowRight') nextCase();
        }});

        // Initialize on load
        window.onload = initializeCases;
    </script>
</body>
</html>
""".format(
            (len(self.cases['valid_passed']) + len(self.cases['invalid_passed'])) / total_cases * 100,
            len(self.cases['valid_failed']),
            len(self.cases['valid_failed']) / total_cases * 100 if total_cases > 0 else 0,
            len(self.cases['invalid_passed']) / (len(self.cases['invalid_passed']) + len(self.cases['invalid_failed'])) * 100 if (len(self.cases['invalid_passed']) + len(self.cases['invalid_failed'])) > 0 else 0
        )
        
        # Save webpage
        output_path = 'valo_review_webpage/valo_advanced_review.html'
        Path('valo_review_webpage').mkdir(exist_ok=True)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(html)
            
        return output_path
    
    def get_category_label(self, category):
        """Get human-readable category label"""
        labels = {
            'valid_passed': '✅ Valid Passed',
            'valid_failed': '🚨 Valid Failed',
            'invalid_passed': '✅ Invalid Passed',
            'invalid_failed': '⚠️ Invalid Failed'
        }
        return labels.get(category, category)
    
    def save_summary(self):
        """Save summary statistics"""
        total = len(self.all_cases)
        
        # Calculate per-violation-type breakdown
        violation_breakdown = defaultdict(lambda: {
            'valid_passed': 0,
            'valid_failed': 0,
            'invalid_passed': 0,
            'invalid_failed': 0,
            'total': 0
        })
        
        for category, cases in self.cases.items():
            for case in cases:
                vtype = case['violation_type']
                violation_breakdown[vtype][category] += 1
                violation_breakdown[vtype]['total'] += 1
        
        summary = {
            'timestamp': datetime.now().isoformat(),
            'total_cases': total,
            'categories': {
                'valid_passed': {
                    'count': len(self.cases['valid_passed']),
                    'percentage': len(self.cases['valid_passed'])/total*100 if total > 0 else 0,
                    'description': 'Valid violations correctly kept as valid'
                },
                'valid_failed': {
                    'count': len(self.cases['valid_failed']),
                    'percentage': len(self.cases['valid_failed'])/total*100 if total > 0 else 0,
                    'description': 'CRITICAL - Valid violations wrongly filtered'
                },
                'invalid_passed': {
                    'count': len(self.cases['invalid_passed']),
                    'percentage': len(self.cases['invalid_passed'])/total*100 if total > 0 else 0,
                    'description': 'False positives correctly filtered'
                },
                'invalid_failed': {
                    'count': len(self.cases['invalid_failed']),
                    'percentage': len(self.cases['invalid_failed'])/total*100 if total > 0 else 0,
                    'description': 'False positives not filtered'
                }
            },
            'violation_types': dict(self.violation_types),
            'violation_type_accuracy': dict(self.violation_type_accuracy),
            'violation_breakdown': dict(violation_breakdown),
            'performance': {
                'overall_accuracy': (len(self.cases['valid_passed']) + len(self.cases['invalid_passed']))/total*100 if total > 0 else 0,
                'critical_error_rate': len(self.cases['valid_failed'])/total*100 if total > 0 else 0,
                'fp_detection_rate': len(self.cases['invalid_passed'])/(len(self.cases['invalid_passed']) + len(self.cases['invalid_failed']))*100 if (len(self.cases['invalid_passed']) + len(self.cases['invalid_failed'])) > 0 else 0
            }
        }
        
        with open('valo_review_webpage/advanced_review_summary.json', 'w') as f:
            json.dump(summary, f, indent=2)
            
        return summary

if __name__ == "__main__":
    print("🔄 Creating advanced review webpage with violation type filtering...")
    
    creator = AdvancedReviewWebpageCreator()
    creator.load_all_cases()
    
    webpage_path = creator.create_webpage()
    summary = creator.save_summary()
    
    print("\n✅ Advanced review webpage created!")
    print(f"\n📁 Files created:")
    print(f"   - {webpage_path}")
    print(f"   - valo_review_webpage/advanced_review_summary.json")
    
    print(f"\n📊 Summary:")
    for category, data in summary['categories'].items():
        print(f"   - {category}: {data['count']} cases ({data['percentage']:.1f}%)")
    
    print(f"\n🎯 Performance:")
    print(f"   - Overall accuracy: {summary['performance']['overall_accuracy']:.1f}%")
    print(f"   - Critical errors: {summary['categories']['valid_failed']['count']} cases")
    print(f"   - FP detection: {summary['performance']['fp_detection_rate']:.1f}%")
    
    print(f"\n📋 Violation Types:")
    for vtype, acc_data in summary['violation_type_accuracy'].items():
        accuracy = acc_data['correct'] / acc_data['total'] * 100 if acc_data['total'] > 0 else 0
        print(f"   - {vtype}: {acc_data['total']} cases, {accuracy:.1f}% accuracy")