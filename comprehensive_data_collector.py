#!/usr/bin/env python3
"""
Comprehensive Data Collection System
Collects descriptions and confidence scores for all cases
"""

import json
import base64
import requests
import os
import time
from datetime import datetime
import re

class ComprehensiveDataCollector:
    def __init__(self):
        self.vlm_url = "http://**************:9500/v1/chat/completions"
        self.vlm_model = "VLM-38B-AWQ"
        self.session = requests.Session()
        
        # Data directories
        self.base_dir = "valo_data_collection"
        self.fp_dir = f"{self.base_dir}/false_positives"
        self.tp_dir = f"{self.base_dir}/true_positives"
        self.structure_dir = f"{self.base_dir}/structures"
        self.analysis_dir = f"{self.base_dir}/analysis"
        
        # Ensure directories exist
        for dir_path in [self.fp_dir, self.tp_dir, self.structure_dir, self.analysis_dir]:
            os.makedirs(dir_path, exist_ok=True)
        
        # Statistics
        self.stats = {
            'total_processed': 0,
            'fp_processed': 0,
            'tp_processed': 0,
            'errors': 0
        }
        
    def encode_image(self, image_path):
        """Encode image to base64"""
        try:
            if os.path.exists(image_path):
                with open(image_path, 'rb') as f:
                    return base64.b64encode(f.read()).decode('utf-8')
        except Exception as e:
            print(f"Error encoding {image_path}: {str(e)}")
        return None
    
    def get_description(self, cropped_image_path):
        """Get VLM description of the cropped image"""
        cropped_b64 = self.encode_image(cropped_image_path)
        if not cropped_b64:
            return None
            
        prompt = """What do you see in this safety monitoring alert image? 
Please describe:
1. The main subject (person, equipment, structure)
2. If people are present, describe their safety equipment
3. Any activities or behaviors visible
4. The environment/location

Be detailed and specific about what you observe."""
        
        payload = {
            "model": self.vlm_model,
            "messages": [{
                "role": "user",
                "content": [
                    {"type": "text", "text": prompt},
                    {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{cropped_b64}"}}
                ]
            }],
            "temperature": 0.1,
            "max_tokens": 500
        }
        
        try:
            response = self.session.post(self.vlm_url, json=payload, timeout=60)
            if response.status_code == 200:
                return response.json()['choices'][0]['message']['content']
        except Exception as e:
            print(f"Error getting description: {str(e)}")
        
        return None
    
    def get_confidence_score(self, cropped_image_path, description):
        """Get confidence score by re-querying with the description"""
        cropped_b64 = self.encode_image(cropped_image_path)
        if not cropped_b64:
            return None
            
        prompt = f"""Based on this description:
"{description}"

How accurately does this description match what you see in the image? 
Provide a confidence percentage (0-100%) for how well the description matches the image content.

Also identify:
- Is there a person in this image? (YES/NO)
- If yes, are they wearing complete PPE (helmet AND high-visibility vest)? (YES/NO/PARTIAL)
- What type of safety concern, if any, is shown?

Format your response as:
CONFIDENCE: [X]%
PERSON_PRESENT: [YES/NO]
PPE_STATUS: [COMPLETE/INCOMPLETE/NONE/NA]
SAFETY_CONCERN: [Description or NONE]"""
        
        payload = {
            "model": self.vlm_model,
            "messages": [{
                "role": "user",
                "content": [
                    {"type": "text", "text": prompt},
                    {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{cropped_b64}"}}
                ]
            }],
            "temperature": 0.1,
            "max_tokens": 300
        }
        
        try:
            response = self.session.post(self.vlm_url, json=payload, timeout=60)
            if response.status_code == 200:
                return response.json()['choices'][0]['message']['content']
        except Exception as e:
            print(f"Error getting confidence: {str(e)}")
        
        return None
    
    def parse_confidence_response(self, response):
        """Parse the confidence response"""
        if not response:
            return {}
            
        parsed = {
            'confidence': 0,
            'person_present': False,
            'ppe_status': 'UNKNOWN',
            'safety_concern': 'UNKNOWN'
        }
        
        # Extract confidence
        conf_match = re.search(r'CONFIDENCE:\s*(\d+)%?', response)
        if conf_match:
            parsed['confidence'] = int(conf_match.group(1))
        
        # Extract person present
        person_match = re.search(r'PERSON_PRESENT:\s*(YES|NO)', response, re.IGNORECASE)
        if person_match:
            parsed['person_present'] = person_match.group(1).upper() == 'YES'
        
        # Extract PPE status
        ppe_match = re.search(r'PPE_STATUS:\s*(\w+)', response)
        if ppe_match:
            parsed['ppe_status'] = ppe_match.group(1).upper()
        
        # Extract safety concern
        concern_match = re.search(r'SAFETY_CONCERN:\s*(.+?)(?:\n|$)', response)
        if concern_match:
            parsed['safety_concern'] = concern_match.group(1).strip()
        
        return parsed
    
    def process_single_case(self, case):
        """Process a single case - get description and confidence"""
        case_number = case['case_number']
        is_fp = case['is_false_positive']
        
        print(f"\nProcessing {case_number} ({'FP' if is_fp else 'TP'})...")
        
        # Get description
        description = self.get_description(case['cropped_image'])
        if not description:
            print(f"  ❌ Failed to get description")
            self.stats['errors'] += 1
            return None
        
        print(f"  ✓ Got description ({len(description)} chars)")
        
        # Get confidence score
        time.sleep(1)  # Rate limiting
        confidence_response = self.get_confidence_score(case['cropped_image'], description)
        if not confidence_response:
            print(f"  ❌ Failed to get confidence")
            self.stats['errors'] += 1
            return None
        
        confidence_data = self.parse_confidence_response(confidence_response)
        print(f"  ✓ Confidence: {confidence_data['confidence']}%")
        print(f"  ✓ Person: {confidence_data['person_present']}, PPE: {confidence_data['ppe_status']}")
        
        # Compile results
        result = {
            'case_number': case_number,
            'is_false_positive': is_fp,
            'source_image': case['source_image'],
            'cropped_image': case['cropped_image'],
            'description': description,
            'confidence_response': confidence_response,
            'parsed_confidence': confidence_data,
            'timestamp': datetime.now().isoformat()
        }
        
        return result
    
    def save_to_markdown(self, result, file_path):
        """Append result to markdown file"""
        with open(file_path, 'a', encoding='utf-8') as f:
            f.write(f"\n## Case: {result['case_number']}\n")
            f.write(f"**Timestamp**: {result['timestamp']}\n")
            f.write(f"**Type**: {'FALSE POSITIVE' if result['is_false_positive'] else 'TRUE POSITIVE (Valid Violation)'}\n")
            f.write(f"**Cropped Image**: `{result['cropped_image']}`\n\n")
            
            f.write("### Description\n")
            f.write("```\n")
            f.write(result['description'])
            f.write("\n```\n\n")
            
            f.write("### Confidence Analysis\n")
            f.write(f"**Confidence Score**: {result['parsed_confidence']['confidence']}%\n")
            f.write(f"**Person Present**: {result['parsed_confidence']['person_present']}\n")
            f.write(f"**PPE Status**: {result['parsed_confidence']['ppe_status']}\n")
            f.write(f"**Safety Concern**: {result['parsed_confidence']['safety_concern']}\n\n")
            
            f.write("### Raw Confidence Response\n")
            f.write("```\n")
            f.write(result['confidence_response'])
            f.write("\n```\n")
            f.write("\n---\n")
    
    def run_comprehensive_collection(self):
        """Run the comprehensive data collection"""
        print("="*80)
        print("🔍 COMPREHENSIVE DATA COLLECTION SYSTEM")
        print("="*80)
        print("This will collect descriptions and confidence scores for all cases")
        print("Output will be organized into FALSE POSITIVES and TRUE POSITIVES")
        print("="*80)
        
        # Load all cases
        with open('valo_batch_round3_complete.json', 'r') as f:
            all_cases = json.load(f)['results']
        
        print(f"\n📊 Total cases to process: {len(all_cases)}")
        
        # Separate FP and TP
        fp_cases = [c for c in all_cases if c['is_false_positive']]
        tp_cases = [c for c in all_cases if not c['is_false_positive']]
        
        print(f"  - False Positives: {len(fp_cases)}")
        print(f"  - True Positives: {len(tp_cases)}")
        
        # Initialize markdown files
        fp_file = f"{self.fp_dir}/false_positive_descriptions.md"
        tp_file = f"{self.tp_dir}/true_positive_descriptions.md"
        summary_file = f"{self.analysis_dir}/collection_summary.md"
        
        # Create headers
        with open(fp_file, 'w') as f:
            f.write("# False Positive Cases - VLM Descriptions and Confidence Scores\n")
            f.write(f"Generated: {datetime.now().isoformat()}\n\n")
        
        with open(tp_file, 'w') as f:
            f.write("# True Positive Cases (Valid Violations) - VLM Descriptions and Confidence Scores\n")
            f.write(f"Generated: {datetime.now().isoformat()}\n\n")
        
        # Process cases
        all_results = []
        
        # Process FALSE POSITIVES
        print("\n📁 Processing FALSE POSITIVE cases...")
        for i, case in enumerate(fp_cases):
            if i % 10 == 0:
                print(f"\nProgress: {i}/{len(fp_cases)} FP cases")
            
            result = self.process_single_case(case)
            if result:
                self.save_to_markdown(result, fp_file)
                all_results.append(result)
                self.stats['fp_processed'] += 1
                self.stats['total_processed'] += 1
            
            time.sleep(0.5)  # Rate limiting
        
        # Process TRUE POSITIVES
        print("\n📁 Processing TRUE POSITIVE cases...")
        for i, case in enumerate(tp_cases):
            if i % 10 == 0:
                print(f"\nProgress: {i}/{len(tp_cases)} TP cases")
            
            result = self.process_single_case(case)
            if result:
                self.save_to_markdown(result, tp_file)
                all_results.append(result)
                self.stats['tp_processed'] += 1
                self.stats['total_processed'] += 1
            
            time.sleep(0.5)  # Rate limiting
        
        # Generate summary
        self.generate_summary(all_results, summary_file)
        
        print("\n" + "="*80)
        print("✅ DATA COLLECTION COMPLETE!")
        print("="*80)
        print(f"Total processed: {self.stats['total_processed']}")
        print(f"False Positives: {self.stats['fp_processed']}")
        print(f"True Positives: {self.stats['tp_processed']}")
        print(f"Errors: {self.stats['errors']}")
        print(f"\n📁 Output files:")
        print(f"  - {fp_file}")
        print(f"  - {tp_file}")
        print(f"  - {summary_file}")
        print("\nReady for data analysis and pattern discovery!")
        
    def generate_summary(self, all_results, summary_file):
        """Generate summary statistics"""
        with open(summary_file, 'w') as f:
            f.write("# Data Collection Summary\n\n")
            f.write(f"Generated: {datetime.now().isoformat()}\n\n")
            
            f.write("## Statistics\n")
            f.write(f"- Total cases processed: {len(all_results)}\n")
            f.write(f"- False Positives: {self.stats['fp_processed']}\n")
            f.write(f"- True Positives: {self.stats['tp_processed']}\n")
            f.write(f"- Errors: {self.stats['errors']}\n\n")
            
            # Analyze patterns
            f.write("## Pattern Analysis\n\n")
            
            # Person presence in FP vs TP
            fp_with_person = sum(1 for r in all_results 
                               if r['is_false_positive'] and r['parsed_confidence']['person_present'])
            tp_with_person = sum(1 for r in all_results 
                               if not r['is_false_positive'] and r['parsed_confidence']['person_present'])
            
            f.write("### Person Presence\n")
            f.write(f"- FP cases with person: {fp_with_person}/{self.stats['fp_processed']} "
                   f"({fp_with_person/self.stats['fp_processed']*100:.1f}%)\n")
            f.write(f"- TP cases with person: {tp_with_person}/{self.stats['tp_processed']} "
                   f"({tp_with_person/self.stats['tp_processed']*100:.1f}%)\n\n")
            
            # PPE Status distribution
            f.write("### PPE Status Distribution\n")
            ppe_stats = {'FP': {}, 'TP': {}}
            for r in all_results:
                category = 'FP' if r['is_false_positive'] else 'TP'
                ppe = r['parsed_confidence']['ppe_status']
                ppe_stats[category][ppe] = ppe_stats[category].get(ppe, 0) + 1
            
            for category in ['FP', 'TP']:
                f.write(f"\n{category} Cases:\n")
                for ppe, count in sorted(ppe_stats[category].items()):
                    f.write(f"  - {ppe}: {count}\n")
            
            # Average confidence scores
            f.write("\n### Average Confidence Scores\n")
            fp_confidences = [r['parsed_confidence']['confidence'] 
                            for r in all_results if r['is_false_positive']]
            tp_confidences = [r['parsed_confidence']['confidence'] 
                            for r in all_results if not r['is_false_positive']]
            
            if fp_confidences:
                f.write(f"- FP average confidence: {sum(fp_confidences)/len(fp_confidences):.1f}%\n")
            if tp_confidences:
                f.write(f"- TP average confidence: {sum(tp_confidences)/len(tp_confidences):.1f}%\n")

if __name__ == "__main__":
    collector = ComprehensiveDataCollector()
    collector.run_comprehensive_collection()