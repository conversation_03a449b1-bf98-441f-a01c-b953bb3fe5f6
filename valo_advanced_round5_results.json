{"round": 5, "timestamp": "2025-07-16T09:10:44.486980", "summary": {"total_cases": 1250, "valid_protection_rate": 90.69767441860465, "fp_detection_rate": 81.6072908036454, "score": 87.97055933411687}, "insights": {"camera_performance": {"QC605 WOS Cam (C) (VATO)": {"total": 110, "correct": 101, "too_conservative": 9}, "QC519 WOS Cam (C) (VALO)": {"total": 25, "correct": 19, "too_conservative": 6}, "QC320 WOS Cam (C) (VATO/VALO)": {"total": 77, "correct": 47, "too_conservative": 30}, "QC109F (VALO)": {"total": 85, "correct": 70, "too_conservative": 15}, "QC108F (VALO)": {"total": 29, "correct": 25, "too_conservative": 4}, "QC506 WOS Cam (C) (VATO)": {"total": 101, "correct": 96, "too_conservative": 5}, "QC106F (VALO)": {"total": 18, "correct": 16, "too_conservative": 2}, "QC614F (VALO)": {"total": 16, "correct": 16, "too_conservative": 0}, "QC523 WOS Cam (C) (VALO)": {"total": 44, "correct": 27, "too_conservative": 17}, "QC504F (VALO)": {"total": 5, "correct": 4, "too_conservative": 1}, "QC601 WOS Cam (C) (VATO/VALO)": {"total": 156, "correct": 141, "too_conservative": 15}, "QC313F (VALO)": {"total": 21, "correct": 20, "too_conservative": 1}, "QC307F (VALO)": {"total": 29, "correct": 21, "too_conservative": 8}, "QC528 WOS Cam (C) (VATO/VALO)": {"total": 41, "correct": 37, "too_conservative": 4}, "QC507F (VALO)": {"total": 11, "correct": 10, "too_conservative": 1}, "QC512 WOS Cam (C) (VALO)": {"total": 31, "correct": 20, "too_conservative": 11}, "QC510 WOS Cam (C) (VATO/VALO)": {"total": 86, "correct": 82, "too_conservative": 4}, "QC611 WOS Cam (C) (VATO/VALO)": {"total": 28, "correct": 16, "too_conservative": 10}, "QC308F (VALO)": {"total": 21, "correct": 20, "too_conservative": 1}, "QC531F": {"total": 34, "correct": 24, "too_conservative": 9}, "QC530F (VALO)": {"total": 7, "correct": 3, "too_conservative": 4}, "QC318F (VALO)": {"total": 8, "correct": 4, "too_conservative": 4}, "QC104F (VALO)": {"total": 82, "correct": 52, "too_conservative": 30}, "QC526F (VALO)": {"total": 3, "correct": 3, "too_conservative": 0}, "QC602F (VALO)": {"total": 8, "correct": 7, "too_conservative": 1}, "QC520F (VALO)": {"total": 2, "correct": 0, "too_conservative": 2}, "QC509F (VALO)": {"total": 5, "correct": 4, "too_conservative": 1}, "QC302F (VALO)": {"total": 8, "correct": 8, "too_conservative": 0}, "QC525F (VALO)": {"total": 3, "correct": 2, "too_conservative": 1}, "QC521F (VALO)": {"total": 5, "correct": 4, "too_conservative": 1}, "QC306F (VALO)": {"total": 17, "correct": 14, "too_conservative": 3}, "QC610F (VALO)": {"total": 8, "correct": 7, "too_conservative": 1}, "QC517F (VALO)": {"total": 1, "correct": 1, "too_conservative": 0}, "QC505F (VALO)": {"total": 14, "correct": 11, "too_conservative": 2}, "QC107F (VALO)": {"total": 9, "correct": 8, "too_conservative": 1}, "QC110F (VALO)": {"total": 23, "correct": 19, "too_conservative": 4}, "QC604F (VALO)": {"total": 13, "correct": 13, "too_conservative": 0}, "QC603F (VALO)": {"total": 1, "correct": 0, "too_conservative": 1}, "QC615F (VALO)": {"total": 7, "correct": 5, "too_conservative": 2}, "QC516F (VALO)": {"total": 6, "correct": 6, "too_conservative": 0}, "QC316F (VALO)": {"total": 26, "correct": 19, "too_conservative": 7}, "QC510F (VALO)": {"total": 1, "correct": 1, "too_conservative": 0}, "QC518F (VALO)": {"total": 3, "correct": 1, "too_conservative": 2}, "QC508F (VALO)": {"total": 1, "correct": 1, "too_conservative": 0}, "QC514F (VALO)": {"total": 6, "correct": 5, "too_conservative": 1}, "QC111F (VALO)": {"total": 6, "correct": 6, "too_conservative": 0}, "QC515F (VALO)": {"total": 3, "correct": 3, "too_conservative": 0}, "QC606F (VALO)": {"total": 6, "correct": 5, "too_conservative": 1}}, "infringement_performance": {"PPE Non-compliance": {"total": 894, "correct": 728, "too_conservative": 163}, "STA Double-up": {"total": 27, "correct": 25, "too_conservative": 2}, "One man Lashing": {"total": 189, "correct": 151, "too_conservative": 37}, "2-Container Distance": {"total": 60, "correct": 58, "too_conservative": 2}, "Ex.Row Violation": {"total": 79, "correct": 61, "too_conservative": 18}, "Spreader Ride": {"total": 1, "correct": 1, "too_conservative": 0}}, "confidence_distribution": {"PPE Non-compliance": [98, 85, 98, 95, 95, 98, 95, 95, 95, 98, 95, 98, 98, 98, 95, 95, 95, 90, 95, 98, 95, 95, 85, 95, 98, 95, 98, 95, 85, 85, 85, 85, 85, 85, 98, 98, 98, 98, 98, 98, 90, 85, 90, 95, 85, 98, 95, 90, 98, 90, 95, 98, 98, 95, 98, 95, 95, 98, 85, 98, 98, 98, 95, 95, 95, 85, 98, 85, 95, 85, 85, 98, 85, 98, 98, 95, 85, 95, 98, 85, 98, 85, 85, 95, 98, 98, 95, 90, 98, 95, 85, 90, 95, 95, 90, 95, 90, 90, 98, 98, 95, 98, 95, 98, 98, 90, 85, 98, 90, 98, 98, 98, 98, 95, 95, 95, 98, 95, 98, 95, 98, 98, 98, 90, 85, 98, 98, 98, 98, 98, 98, 98, 98, 98, 98, 98, 98, 98, 98, 98, 95, 98, 98, 85, 98, 90, 95, 90, 95, 98, 95, 98, 98, 98, 98, 95, 98, 98, 98, 95, 95, 95, 90, 98, 90, 95, 90, 95, 95, 85, 95, 90, 85, 95, 90, 95, 98, 98, 85, 98, 95, 95, 98, 98, 95, 98, 95, 98, 98, 85, 90, 98, 98, 95, 98, 98, 98, 98, 85, 90, 95, 98, 95, 98, 90, 98, 98, 98, 90, 85, 85, 98, 95, 85, 98, 85, 85, 98, 95, 95, 98, 90, 95, 98, 98, 90, 98, 98, 95, 98, 98, 95, 98, 98, 98, 95, 98, 98, 98, 95, 95, 98, 98, 98, 98, 90, 98, 95, 85, 98, 95, 98, 95, 95, 95, 85, 98, 98, 98, 95, 95, 95, 98, 98, 98, 98, 98, 98, 95, 98, 98, 98, 95, 95, 98, 98, 98, 98, 98, 98, 98, 98, 90, 98, 95, 90, 98, 98, 90, 98, 98, 95, 95, 85, 95, 98, 95, 98, 95, 85, 90, 98, 98, 98, 85, 98, 95, 98, 98, 98, 95, 90, 95, 90, 90, 98, 95, 95, 98, 98, 95, 95, 95, 98, 95, 98, 98, 98, 90, 95, 90, 98, 90, 98, 98, 98, 98, 85, 98, 98, 90, 98, 85, 98, 98, 98, 95, 98, 95, 95, 98, 95, 85, 95, 98, 98, 95, 90, 98, 95, 95, 95, 98, 95, 90, 98, 98, 98, 95, 95, 95, 98, 95, 98, 98, 98, 95, 95, 95, 90, 98, 98, 98, 95, 90, 95, 98, 98, 98, 95, 95, 95, 98, 95, 95, 98, 95, 95, 95, 98, 98, 98, 95, 95, 98, 95, 98, 98, 98, 90, 98, 98, 85, 98, 85, 95, 98, 98, 98, 85, 95, 95, 90, 98, 98, 98, 98, 85, 95, 98, 95, 95, 85, 95, 90, 90, 95, 98, 85, 95, 95, 90, 98, 98, 98, 98, 98, 98, 98, 95, 85, 98, 98, 98, 98, 98, 95, 95, 98, 95, 95, 98, 95, 95, 95, 98, 98, 95, 98, 85, 90, 95, 95, 98, 98, 98, 95, 95, 98, 98, 95, 95, 90, 98, 98, 95, 98, 98, 85, 95, 98, 90, 85, 98, 98, 98, 98, 85, 98, 98, 98, 85, 85, 95, 95, 98, 95, 95, 95, 98, 98, 85, 98, 95, 98, 98, 95, 98, 98, 85, 85, 98, 98, 95, 85, 95, 98, 98, 85, 95, 95, 85, 95, 98, 95, 98, 98, 95, 95, 98, 95, 98, 85, 95, 98, 98, 98, 98, 98, 95, 98, 98, 95, 95, 95, 98, 98, 98, 98, 95, 98, 98, 98, 98, 85, 98, 98, 95, 98, 98, 95, 98, 98, 85, 90, 95, 85, 98, 95, 85, 98, 85, 85, 85, 95, 95, 98, 95, 95, 95, 90, 85, 95, 95, 95, 85, 85, 90, 95, 85, 85, 85, 98, 90, 95, 98, 90, 98, 98, 98, 98, 98, 98, 95, 90, 95, 98, 98, 90, 98, 95, 95, 85, 95, 98, 97, 95, 98, 95, 90, 98, 98, 98, 95, 98, 95, 98, 95, 95, 98, 98, 98, 95, 95, 95, 98, 98, 90, 98, 98, 98, 95, 98, 95, 85, 98, 98, 98, 98, 95, 98, 95, 98, 95, 98, 98, 98, 98, 90, 98, 98, 98, 95, 98, 95, 90, 95, 90, 98, 98, 95, 98, 95, 95, 85, 98, 98, 98, 95, 85, 95, 85, 90, 98, 98, 98, 98, 90, 90, 98, 95, 95, 95, 90, 98, 98, 95, 98, 98, 95, 98, 90, 85, 90, 95, 95, 98, 95, 85, 98, 90, 98, 98, 98, 95, 95, 85, 98, 95, 98, 85, 95, 98, 98, 95, 98, 98, 98, 95, 95, 95, 95, 95, 98, 95, 95, 98, 95, 95, 95, 98, 98, 95, 98, 95, 98, 95, 95, 98, 98, 98, 98, 85, 98, 98, 85, 85, 85, 85, 95, 90, 98, 95, 95, 95, 95, 95, 95, 98, 95, 95, 98, 98, 90, 98, 85, 98, 98, 95, 98, 98, 95, 98, 95, 95, 98, 98, 95, 95, 90, 90, 85, 90, 85, 98, 98, 85, 98, 95, 95, 98, 95, 95, 95, 98, 98, 85, 90, 98, 98, 98, 98, 98, 85, 95, 98, 98, 95, 98, 98, 95, 98, 85, 98, 98, 98, 95, 95, 98, 95, 95, 98, 90, 98, 95, 95, 98, 90, 98, 95, 95, 95, 90, 98, 98, 95, 95, 98, 95, 95, 98, 95, 98, 98, 98, 98, 95, 98, 98, 98, 85, 95, 98, 98, 98, 98, 98, 90, 95, 98, 95, 90, 98, 95, 85, 98, 95, 98, 98, 95, 95, 98, 98, 98], "STA Double-up": [95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 85, 85, 95, 95, 95, 95, 95, 95, 95, 95], "One man Lashing": [95, 85, 95, 95, 95, 95, 95, 95, 85, 95, 95, 85, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 85, 95, 95, 95, 85, 95, 95, 85, 95, 95, 85, 95, 95, 85, 95, 95, 95, 95, 95, 95, 85, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 85, 85, 85, 95, 85, 95, 85, 95, 75, 95, 95, 95, 95, 95, 95, 95, 85, 75, 75, 75, 95, 95, 95, 95, 95, 75, 85, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 85, 95, 95, 95, 95, 95, 95, 95, 85, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 85, 85, 95, 95, 95, 95, 95, 95, 95, 95, 75, 95, 95, 75, 75, 75, 85, 95, 75, 95, 75, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 85, 95, 95, 95, 95, 95, 95, 95, 85, 95, 95, 95, 95, 95, 95, 95, 95, 85, 95, 95, 95, 85, 95, 85, 95, 95, 75, 95, 95, 95, 95, 95, 95, 95, 95, 95, 85, 95, 95], "2-Container Distance": [92, 92, 90, 90, 90, 90, 90, 95, 92, 92, 95, 90, 95, 90, 90, 90, 75, 90, 90, 90, 92, 92, 95, 90, 90, 92, 90, 90, 92, 90, 90, 75, 92, 92, 95, 92, 92, 92, 95, 95, 92, 92, 90, 90, 90, 90, 90, 95, 90, 92, 90, 90, 92, 90, 90, 92, 92, 90, 90, 95], "Ex.Row Violation": [90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 75, 90, 90, 75, 90, 90, 90, 90, 95, 92, 90, 90, 90, 95, 90, 90, 90, 75, 90, 75, 75, 90, 95, 90, 90, 75, 90, 75, 75, 90, 90, 90, 90, 90, 90, 90, 90, 90, 70, 90, 70, 75, 70, 75, 90, 75, 90, 90, 90, 95, 90, 90, 75, 90, 75, 90, 90, 90, 90, 90, 75, 75, 90, 95], "Spreader Ride": [98]}, "person_detection_accuracy": {"total": 1100, "correct": 429}, "false_negative_cases": ["V1250626060", "V1250628042", "V1250628101", "V1250630019"], "overly_conservative_cases": ["V1250623122", "V1250623126", "V1250623136", "V1250623149", "V1250623165", "V1250623171", "V1250623172", "V1250623175", "V1250623176", "V1250623177", "V1250623184", "V1250623185", "V1250623186", "V1250623189", "V1250624003", "V1250624008", "V1250624020", "V1250624027", "V1250624029", "V1250624031", "V1250624032", "V1250624035", "V1250624039", "V1250624043", "V1250624052", "V1250624053", "V1250624054", "V1250624060", "V1250624063", "V1250624064", "V1250624067", "V1250624084", "V1250624086", "V1250624097", "V1250624106", "V1250624107", "V1250624117", "V1250624130", "V1250624134", "V1250624136", "V1250624137", "V1250624158", "V1250624162", "V1250624166", "V1250624168", "V1250624169", "V1250624171", "V1250624178", "V1250625013", "V1250625016", "V1250625024", "V1250625026", "V1250625031", "V1250625032", "V1250625036", "V1250625037", "V1250625038", "V1250625041", "V1250625043", "V1250625044", "V1250625051", "V1250625055", "V1250625089", "V1250625093", "V1250625100", "V1250625126", "V1250625129", "V1250625130", "V1250625131", "V1250625136", "V1250625139", "V1250625144", "V1250625150", "V1250625151", "V1250625155", "V1250626005", "V1250626007", "V1250626008", "V1250626015", "V1250626016", "V1250626022", "V1250626026", "V1250626027", "V1250626040", "V1250626042", "V1250626044", "V1250626049", "V1250626052", "V1250626064", "V1250626082", "V1250626104", "V1250626138", "V1250626140", "V1250626141", "V1250626148", "V1250626151", "V1250626161", "V1250626163", "V1250626164", "V1250626165", "V1250626168", "V1250626169", "V1250626170", "V1250626173", "V1250627011", "V1250627026", "V1250627037", "V1250627038", "V1250627045", "V1250627046", "V1250627048", "V1250627049", "V1250627062", "V1250627077", "V1250627080", "V1250627082", "V1250627083", "V1250627089", "V1250627092", "V1250627094", "V1250627097", "V1250627098", "V1250627100", "V1250627111", "V1250627122", "V1250627123", "V1250627129", "V1250627130", "V1250627134", "V1250627139", "V1250627156", "V1250627176", "V1250627181", "V1250627211", "V1250627212", "V1250627214", "V1250627217", "V1250627219", "V1250627220", "V1250627221", "V1250627222", "V1250627226", "V1250627228", "V1250627229", "V1250627234", "V1250627237", "V1250627238", "V1250627239", "V1250627243", "V1250627244", "V1250627245", "V1250628001", "V1250628002", "V1250628003", "V1250628004", "V1250628007", "V1250628010", "V1250628011", "V1250628012", "V1250628017", "V1250628018", "V1250628026", "V1250628034", "V1250628039", "V1250628043", "V1250628046", "V1250628072", "V1250628086", "V1250628106", "V1250628117", "V1250628118", "V1250628127", "V1250628132", "V1250628135", "V1250628136", "V1250628139", "V1250628141", "V1250628143", "V1250628144", "V1250629004", "V1250629013", "V1250629014", "V1250629015", "V1250629020", "V1250629021", "V1250629023", "V1250629029", "V1250629032", "V1250629035", "V1250629047", "V1250629069", "V1250629085", "V1250629091", "V1250629092", "V1250629096", "V1250629097", "V1250629099", "V1250629120", "V1250629125", "V1250629126", "V1250629142", "V1250629147", "V1250629148", "V1250629149", "V1250629150", "V1250629151", "V1250629154", "V1250630005", "V1250630006", "V1250630014", "V1250630022", "V1250630023", "V1250630030", "V1250630037", "V1250630043", "V1250630048", "V1250630054", "V1250630078", "V1250630089", "V1250630096", "V1250630099", "V1250630110"]}}