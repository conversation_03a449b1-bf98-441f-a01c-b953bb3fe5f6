#!/usr/bin/env python3
"""
Find the optimal balance between FP detection and safety protection
Test multiple prompt strategies to achieve:
1. 100% valid violation protection (or very close)
2. Maximum possible FP detection while maintaining safety
"""

import json
import os
from collections import defaultdict

def analyze_dataset_composition():
    """Analyze the full dataset to understand the challenge"""
    
    print("ANALYZING DATASET COMPOSITION")
    print("="*60)
    
    # Load results
    with open('enhanced_1250_progress.json', 'r') as f:
        data = json.load(f)
    
    results = data['results']
    total = len(results)
    
    # Basic stats
    fps = [r for r in results if r['actual_fp']]
    valid = [r for r in results if not r['actual_fp']]
    
    print(f"Total cases: {total}")
    print(f"False positives: {len(fps)} ({len(fps)/total*100:.1f}%)")
    print(f"Valid violations: {len(valid)} ({len(valid)/total*100:.1f}%)")
    
    # Violation type breakdown
    print("\nVIOLATION TYPE DISTRIBUTION:")
    violation_counts = defaultdict(lambda: {'total': 0, 'fp': 0, 'valid': 0})
    
    for r in results:
        vtype = r['infringement_type']
        violation_counts[vtype]['total'] += 1
        if r['actual_fp']:
            violation_counts[vtype]['fp'] += 1
        else:
            violation_counts[vtype]['valid'] += 1
    
    for vtype, counts in sorted(violation_counts.items(), key=lambda x: x[1]['total'], reverse=True):
        print(f"\n{vtype}:")
        print(f"  Total: {counts['total']}")
        print(f"  FPs: {counts['fp']} ({counts['fp']/counts['total']*100:.1f}%)")
        print(f"  Valid: {counts['valid']} ({counts['valid']/counts['total']*100:.1f}%)")
    
    return fps, valid

def calculate_optimal_targets(fps, valid):
    """Calculate what we need to achieve"""
    
    print("\n\nOPTIMAL TARGET CALCULATION")
    print("="*60)
    
    total = len(fps) + len(valid)
    
    # For 70% FP reduction
    target_fp_detection = len(fps) * 0.70
    
    print(f"To achieve 70% FP reduction:")
    print(f"- Must correctly identify {target_fp_detection:.0f}/{len(fps)} false positives")
    print(f"- Must protect ALL {len(valid)} valid violations")
    
    # Calculate maximum acceptable error
    print(f"\nMaximum acceptable valid violation errors:")
    print(f"- 0% error rate: {0} missed violations (IDEAL)")
    print(f"- 1% error rate: {len(valid)*0.01:.0f} missed violations")
    print(f"- 5% error rate: {len(valid)*0.05:.0f} missed violations")
    
    # What this means for overall accuracy
    min_correct = target_fp_detection + len(valid)  # All valid + 70% FP
    min_accuracy = min_correct / total * 100
    
    print(f"\nMinimum required performance:")
    print(f"- Overall accuracy: {min_accuracy:.1f}%")
    print(f"- FP detection: 70%")
    print(f"- Valid protection: 100%")

def create_hybrid_prompt():
    """Create a hybrid prompt that balances safety and FP detection"""
    
    prompt = """SAFETY VIOLATION ANALYSIS - HYBRID APPROACH

This is a critical safety system. Valid violations must be protected.

ANALYZE BOTH IMAGES:
- SOURCE: Full context view  
- CROPPED: Area of concern

STEP 1 - INITIAL CLASSIFICATION:

A) DEFINITE STRUCTURE (>90% confidence):
   Clear industrial equipment with NO human features:
   - Crane beams, vessel rails, spreader frames
   - Camera structures, cell guides
   - Pure mechanical/geometric forms
   
B) DEFINITE PERSON (any confidence):
   Any human characteristics visible:
   - Human shape, clothing, PPE
   - Organic forms, movement
   
C) MIXED/UNCERTAIN:
   - Both person and structure
   - Cannot determine clearly

STEP 2 - VIOLATION ASSESSMENT:

For DEFINITE STRUCTURE:
→ FALSE POSITIVE (unless person visible anywhere)

For DEFINITE PERSON:
→ Check ALL violations:

PPE CHECKS:
- Helmet present and worn properly?
- Vest present and fastened?
- Required equipment (life jacket, GO/STOP bat)?

BEHAVIORAL CHECKS:
- Mobile device use?
- Proper location/authorization?
- Following safety procedures?
- Maintaining safe distances?

For MIXED/UNCERTAIN:
→ Default to VALID VIOLATION

DECISION MATRIX:
┌─────────────────────┬────────────────┬──────────┐
│ Detection           │ Violations     │ Result   │
├─────────────────────┼────────────────┼──────────┤
│ Structure (>90%)    │ None possible  │ FALSE POS│
│ Person              │ Any violation  │ VALID    │
│ Person              │ None found     │ FALSE POS│
│ Uncertain           │ Any concern    │ VALID    │
└─────────────────────┴────────────────┴──────────┘

OUTPUT:
FALSE POSITIVE: [YES/NO]
Detection: [STRUCTURE/PERSON/UNCERTAIN]
Violations: [List any found]
Confidence: [%]"""

    return prompt

def recommend_testing_strategy():
    """Recommend a comprehensive testing strategy"""
    
    print("\n\nRECOMMENDED TESTING STRATEGY")
    print("="*60)
    
    strategy = {
        "phase1": {
            "name": "Safety Validation",
            "goal": "Ensure 100% valid violation protection",
            "approach": "Test on all valid violations with safety-first prompt",
            "success_criteria": "≥98% valid protection rate"
        },
        "phase2": {
            "name": "Balance Optimization", 
            "goal": "Maximize FP detection while maintaining safety",
            "approach": "Test hybrid prompt with different thresholds",
            "success_criteria": "≥70% FP detection with ≥95% valid protection"
        },
        "phase3": {
            "name": "Production Validation",
            "goal": "Verify performance on full dataset",
            "approach": "Run optimized prompt on all 1250 cases",
            "success_criteria": "Meet both FP and safety targets"
        }
    }
    
    print("\nTesting phases:")
    for phase, details in strategy.items():
        print(f"\n{phase}: {details['name']}")
        print(f"  Goal: {details['goal']}")
        print(f"  Approach: {details['approach']}")
        print(f"  Success: {details['success_criteria']}")
    
    # Save strategy
    with open('safety_optimization_strategy.json', 'w') as f:
        json.dump(strategy, f, indent=2)
    
    # Create prompt configurations to test
    configs = [
        {
            "name": "safety_first",
            "structure_threshold": 95,
            "person_threshold": 20,
            "default_action": "valid_violation"
        },
        {
            "name": "balanced_90",
            "structure_threshold": 90,
            "person_threshold": 40,
            "default_action": "valid_violation"
        },
        {
            "name": "balanced_85",
            "structure_threshold": 85,
            "person_threshold": 50,
            "default_action": "review"
        },
        {
            "name": "hybrid_smart",
            "structure_threshold": 90,
            "person_threshold": 40,
            "default_action": "context_based"
        }
    ]
    
    with open('prompt_test_configs.json', 'w') as f:
        json.dump(configs, f, indent=2)
    
    print("\n\nNEXT STEPS:")
    print("1. Test safety-first prompt on valid violations")
    print("2. If protection >98%, test on false positives")
    print("3. Find optimal threshold configuration")
    print("4. Run full validation on 1250 cases")
    print("5. Deploy configuration that meets both targets")

def main():
    # Analyze dataset
    fps, valid = analyze_dataset_composition()
    
    # Calculate targets
    calculate_optimal_targets(fps, valid)
    
    # Create hybrid prompt
    hybrid_prompt = create_hybrid_prompt()
    with open('hybrid_safety_prompt.txt', 'w') as f:
        f.write(hybrid_prompt)
    
    # Recommend strategy
    recommend_testing_strategy()
    
    print("\n\n" + "="*60)
    print("SUMMARY")
    print("="*60)
    print(f"\nDataset: {len(fps)} FPs, {len(valid)} valid violations")
    print(f"Target: Detect 70% of FPs while protecting 100% of valid")
    print(f"Challenge: Valid violations are harder to detect than FPs")
    print(f"\nCreated multiple prompt strategies to test")
    print(f"Ready to find optimal balance!")

if __name__ == "__main__":
    main()