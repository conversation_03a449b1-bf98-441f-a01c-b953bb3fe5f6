#!/usr/bin/env python3
"""
Quick Ensemble Test - Uses 200 cases for faster results
"""

import json
import asyncio
import aiohttp
import logging
import base64
from datetime import datetime
from typing import Dict, List, Tuple

logging.basicConfig(level=logging.INFO, format='%(message)s')
logger = logging.getLogger(__name__)


class QuickEnsembleTest:
    """Quick test of ensemble vs single approaches"""
    
    def __init__(self):
        self.vlm_endpoint = "http://100.106.127.35:9500/v1/chat/completions"
        self.model = "VLM-38B-AWQ"
        
        self.approaches = {
            'assumption_based': """SAFETY ALERT ANALYSIS
DEFAULT ASSUMPTION: This is a false positive (97% probability)
To override this assumption, you need CLEAR evidence:
- Person clearly visible AND
- No helmet visible AND
- No safety vest visible

Is this a FALSE POSITIVE? YES/NO""",
            
            'alert_fatigue': """ALERT FATIGUE PREVENTION MODE
Too many false alerts = ignored real violations
Help reduce false alerts by being practical.
Mark as <PERSON>LSE POSITIVE unless you see:
- Obvious person
- Clearly missing PPE
- No doubt whatsoever
Is this a FALSE POSITIVE? YES/NO""",
            
            'worksite_reality': """WORKSITE REALITY CHECK
Facts about worksites:
- Workers wear PPE to protect themselves
- Supervisors enforce PPE rules
- Workers without PPE get sent home
Is it likely someone is working without PPE?
Probably not → FALSE POSITIVE"""
        }
    
    async def analyze_single(self, session: aiohttp.ClientSession, 
                           image_path: str, prompt: str) -> Tuple[bool, float]:
        """Single approach analysis"""
        try:
            with open(image_path, 'rb') as f:
                image_data = base64.b64encode(f.read()).decode('utf-8')
            
            payload = {
                "model": self.model,
                "messages": [{
                    "role": "user",
                    "content": [
                        {"type": "text", "text": prompt},
                        {"type": "image_url", 
                         "image_url": {"url": f"data:image/jpeg;base64,{image_data}"}}
                    ]
                }],
                "temperature": 0.1,
                "max_tokens": 50
            }
            
            async with session.post(self.vlm_endpoint, json=payload, timeout=30) as response:
                if response.status == 200:
                    result = await response.json()
                    content = result['choices'][0]['message']['content'].upper()
                    
                    # Simple confidence estimation
                    confidence = 0.9 if "CLEARLY" in content or "NO DOUBT" in content else 0.7
                    is_fp = "YES" in content[:50] or "FALSE POSITIVE" in content
                    
                    return is_fp, confidence
                    
        except Exception as e:
            return False, 0.0
        
        return False, 0.0
    
    async def ensemble_analyze(self, session: aiohttp.ClientSession, case: Dict) -> Dict:
        """Ensemble analysis"""
        # Run all three in parallel for speed
        tasks = []
        for name, prompt in self.approaches.items():
            task = self.analyze_single(session, case['cropped_image'], prompt)
            tasks.append(task)
        
        results = await asyncio.gather(*tasks)
        
        # Extract results
        is_fp_1, conf_1 = results[0]  # assumption_based
        is_fp_2, conf_2 = results[1]  # alert_fatigue  
        is_fp_3, conf_3 = results[2]  # worksite_reality
        
        # Fast path - if assumption_based is highly confident
        if is_fp_1 and conf_1 > 0.9:
            return {'is_fp': True, 'confidence': conf_1, 'method': 'fast_path'}
        
        # Voting logic
        fp_votes = sum([is_fp_1, is_fp_2, is_fp_3])
        avg_conf = (conf_1 + conf_2 + conf_3) / 3
        
        # Adjust confidence based on agreement
        if fp_votes == 3:
            final_conf = avg_conf
        elif fp_votes == 2:
            final_conf = avg_conf * 0.85
        else:
            final_conf = avg_conf * 0.7
        
        return {
            'is_fp': fp_votes >= 2,
            'confidence': final_conf,
            'method': f'vote_{fp_votes}_of_3'
        }
    
    async def test_approach(self, approach: str, cases: List[Dict]) -> Dict:
        """Test an approach on cases"""
        correct_fp = 0
        correct_valid = 0
        total_fp = sum(1 for c in cases if c['is_false_positive'])
        total_valid = sum(1 for c in cases if not c['is_false_positive'])
        
        async with aiohttp.ClientSession() as session:
            # Process in batches
            batch_size = 10
            for i in range(0, len(cases), batch_size):
                batch = cases[i:i+batch_size]
                tasks = []
                
                for case in batch:
                    if approach == 'ensemble':
                        task = self.ensemble_analyze(session, case)
                    else:
                        prompt = self.approaches[approach]
                        task = self.analyze_single(session, case['cropped_image'], prompt)
                    tasks.append(task)
                
                results = await asyncio.gather(*tasks)
                
                # Count correct predictions
                for case, result in zip(batch, results):
                    if approach == 'ensemble':
                        predicted_fp = result['is_fp']
                    else:
                        predicted_fp = result[0]  # First element of tuple
                    
                    if case['is_false_positive'] and predicted_fp:
                        correct_fp += 1
                    elif not case['is_false_positive'] and not predicted_fp:
                        correct_valid += 1
                
                # Progress
                processed = min(i + batch_size, len(cases))
                if processed % 50 == 0:
                    current_fp_rate = (correct_fp / total_fp * 100) if total_fp > 0 else 0
                    logger.info(f"  {approach}: {processed}/{len(cases)} | FP: {current_fp_rate:.1f}%")
        
        fp_rate = (correct_fp / total_fp * 100) if total_fp > 0 else 0
        valid_rate = (correct_valid / total_valid * 100) if total_valid > 0 else 100
        
        return {
            'approach': approach,
            'fp_detection': fp_rate,
            'valid_protection': valid_rate,
            'correct_fp': correct_fp,
            'correct_valid': correct_valid,
            'total_fp': total_fp,
            'total_valid': total_valid
        }


async def main():
    """Run quick comparison test"""
    print("\n" + "="*60)
    print("QUICK ENSEMBLE TEST - 200 CASES")
    print("="*60 + "\n")
    
    # Load test data
    with open('valo_batch_round3_complete.json', 'r') as f:
        data = json.load(f)
        test_cases = []
        
        # Use 200 cases for quick test
        for case in data['results'][:200]:
            test_cases.append({
                'case_number': case['case_number'],
                'cropped_image': case['cropped_image'],
                'is_false_positive': case['is_false_positive']
            })
    
    total_fp = sum(1 for c in test_cases if c['is_false_positive'])
    total_valid = sum(1 for c in test_cases if not c['is_false_positive'])
    
    logger.info(f"Dataset: {len(test_cases)} cases ({total_fp} FP, {total_valid} Valid)\n")
    
    tester = QuickEnsembleTest()
    approaches = ['assumption_based', 'alert_fatigue', 'worksite_reality', 'ensemble']
    
    results = {}
    
    for approach in approaches:
        logger.info(f"Testing {approach}...")
        start = datetime.now()
        result = await tester.test_approach(approach, test_cases)
        duration = (datetime.now() - start).total_seconds()
        result['duration'] = duration
        results[approach] = result
        logger.info(f"  Complete: {result['fp_detection']:.1f}% FP, {result['valid_protection']:.1f}% Valid\n")
        await asyncio.sleep(2)
    
    # Final comparison
    print("\n" + "="*60)
    print("FINAL RESULTS COMPARISON")
    print("="*60)
    print(f"\n{'Approach':<20} {'FP Detection':<15} {'Valid Protection':<18} {'Overall'}")
    print("-"*70)
    
    for name, result in results.items():
        overall = "✅" if result['fp_detection'] >= 70 and result['valid_protection'] >= 85 else "❌"
        print(f"{name:<20} {result['fp_detection']:>6.1f}% "
              f"({result['correct_fp']}/{result['total_fp']:<3}) "
              f"{result['valid_protection']:>8.1f}% "
              f"({result['correct_valid']}/{result['total_valid']:<2}) "
              f"  {overall}")
    
    # Key insights
    ensemble = results['ensemble']
    best_single = max([results[k] for k in ['assumption_based', 'alert_fatigue', 'worksite_reality']], 
                     key=lambda x: x['fp_detection'] if x['valid_protection'] >= 85 else 0)
    
    print("\n" + "="*60)
    print("KEY FINDINGS")
    print("="*60)
    
    print(f"\n1. Ensemble Performance:")
    print(f"   - FP Detection: {ensemble['fp_detection']:.1f}%")
    print(f"   - Valid Protection: {ensemble['valid_protection']:.1f}%")
    
    improvement = ensemble['fp_detection'] - best_single['fp_detection']
    print(f"\n2. Ensemble vs Best Single ({best_single['approach']}):")
    print(f"   - Improvement: {improvement:+.1f}% FP detection")
    print(f"   - Both maintain {ensemble['valid_protection']:.0f}%+ valid protection")
    
    print(f"\n3. Production Readiness:")
    if ensemble['fp_detection'] >= 70 and ensemble['valid_protection'] >= 85:
        print(f"   ✅ Ensemble is production ready!")
        print(f"   - Exceeds 70% FP target by {ensemble['fp_detection']-70:.1f}%")
    else:
        print(f"   ❌ More optimization needed")
    
    # Save results
    with open('quick_ensemble_test_results.json', 'w') as f:
        json.dump({
            'test_date': datetime.now().isoformat(),
            'test_cases': len(test_cases),
            'results': results,
            'winner': 'ensemble' if improvement > 0 else best_single['approach'],
            'production_ready': ensemble['fp_detection'] >= 70 and ensemble['valid_protection'] >= 85
        }, f, indent=2)
    
    print(f"\n4. Processing Speed:")
    print(f"   - Single approaches: ~{sum(r['duration'] for k,r in results.items() if k != 'ensemble')/3:.1f}s")
    print(f"   - Ensemble: {ensemble['duration']:.1f}s")
    print(f"   - Overhead: Minimal due to parallel processing")
    
    print("\n" + "="*60)
    print("RECOMMENDATION")
    print("="*60)
    
    if improvement > 5:
        print("\n✅ USE ENSEMBLE")
        print(f"   - {improvement:.1f}% better than best single approach")
        print("   - More robust through multiple validation")
        print("   - Worth the minimal complexity increase")
    elif improvement > 0:
        print("\n⚠️  CONSIDER ENSEMBLE")
        print(f"   - Only {improvement:.1f}% improvement")
        print("   - Evaluate if complexity is worth it")
    else:
        print("\n❌ USE SINGLE APPROACH")
        print(f"   - {best_single['approach']} performs as well or better")
        print("   - Simpler implementation")


if __name__ == "__main__":
    asyncio.run(main())