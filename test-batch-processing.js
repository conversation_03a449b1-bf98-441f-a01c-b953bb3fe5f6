const puppeteer = require('puppeteer');
const fs = require('fs').promises;
const path = require('path');

async function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

async function testBatchProcessing() {
    console.log('🚀 Starting AI-FARM Batch Processing Test');
    
    // Create screenshots directory
    const screenshotsDir = path.join(__dirname, 'data', 'screenshots', 'batch-processing');
    await fs.mkdir(screenshotsDir, { recursive: true });
    
    const browser = await puppeteer.launch({
        headless: true,
        args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    try {
        const page = await browser.newPage();
        await page.setViewport({ width: 1920, height: 1080 });
        
        // Step 1: Navigate to the main page
        console.log('📍 Step 1: Navigating to main page...');
        await page.goto('http://localhost:3000', { waitUntil: 'networkidle2' });
        await page.screenshot({ path: path.join(screenshotsDir, '01-main-page.png'), fullPage: true });
        
        // Step 2: Navigate to batch processing page
        console.log('📍 Step 2: Looking for batch processing link...');
        
        // Try multiple possible selectors for the batch processing link
        const possibleSelectors = [
            'a[href*="batch"]',
            'button',
            '[data-testid="batch-processing-link"]',
            'nav a'
        ];
        
        let batchLink = null;
        for (const selector of possibleSelectors) {
            try {
                const elements = await page.$$(selector);
                for (const element of elements) {
                    const text = await page.evaluate(el => el.textContent, element);
                    if (text && text.toLowerCase().includes('batch')) {
                        batchLink = element;
                        break;
                    }
                }
                if (batchLink) break;
            } catch (e) {
                // Continue trying other selectors
            }
        }
        
        if (!batchLink) {
            // If no batch link found, check if we're already on a page with batch functionality
            console.log('⚠️ No batch processing link found, checking current page...');
            await page.screenshot({ path: path.join(screenshotsDir, '02-navigation-debug.png'), fullPage: true });
            
            // Check if there's a file upload or start button on current page
            const uploadButton = await page.$('input[type="file"]');
            const buttons = await page.$$('button');
            let startButton = null;
            
            for (const button of buttons) {
                const text = await page.evaluate(el => el.textContent, button);
                if (text && text.toLowerCase().includes('start')) {
                    startButton = button;
                    break;
                }
            }
            
            if (!uploadButton && !startButton) {
                console.log('❌ Could not find batch processing functionality');
                console.log('📍 Proceeding with direct API testing...');
            }
        } else {
            await batchLink.click();
            await page.waitForNavigation({ waitUntil: 'networkidle2' });
        }
        
        await page.screenshot({ path: path.join(screenshotsDir, '02-batch-page.png'), fullPage: true });
        
        // Step 3: Start batch processing via API
        console.log('📍 Step 3: Starting batch processing via API...');
        
        // Call the API directly to start batch processing
        const response = await page.evaluate(async () => {
            try {
                const res = await fetch('http://localhost:8001/api/v1/batch/api/batch/start', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        num_rounds: 3,
                        chunk_size: 5,
                        test_mode: true  // Use test mode with 50 cases
                    })
                });
                return await res.json();
            } catch (error) {
                return { error: error.message };
            }
        });
        
        console.log('API Response:', response);
        
        if (response.error) {
            console.error('❌ Error starting batch processing:', response.error);
            return;
        }
        
        const jobId = response.job_id;
        console.log(`✅ Batch processing started with job ID: ${jobId}`);
        
        // Step 4: Monitor progress
        console.log('📍 Step 4: Monitoring batch processing progress...');
        
        let isComplete = false;
        let attempts = 0;
        const maxAttempts = 60; // 5 minutes with 5-second intervals
        
        while (!isComplete && attempts < maxAttempts) {
            await sleep(5000); // Wait 5 seconds between checks
            
            const status = await page.evaluate(async (jobId) => {
                try {
                    const res = await fetch(`http://localhost:8001/api/v1/batch/api/batch/status/${jobId}`);
                    return await res.json();
                } catch (error) {
                    return { error: error.message };
                }
            }, jobId);
            
            if (status.error) {
                console.error('❌ Error checking status:', status.error);
                break;
            }
            
            console.log(`Progress: ${status.status} - ${status.progress_percentage?.toFixed(1) || 0}% (${status.cases_processed || 0}/${status.total_cases || 0} cases)`);
            
            // Refresh page to see UI updates
            await page.reload({ waitUntil: 'networkidle2' });
            await page.screenshot({ 
                path: path.join(screenshotsDir, `03-progress-${attempts}.png`), 
                fullPage: true 
            });
            
            if (status.status === 'completed') {
                isComplete = true;
            } else if (status.status === 'failed') {
                console.error('❌ Batch processing failed');
                break;
            }
            
            attempts++;
        }
        
        if (!isComplete) {
            console.error('❌ Batch processing did not complete within timeout');
            return;
        }
        
        console.log('✅ Batch processing completed!');
        
        // Step 5: Get and display results
        console.log('📍 Step 5: Fetching results...');
        
        const results = await page.evaluate(async () => {
            try {
                const res = await fetch('http://localhost:8001/api/v1/batch/api/batch/results/latest');
                return await res.json();
            } catch (error) {
                return { error: error.message };
            }
        });
        
        if (results.error) {
            console.error('❌ Error fetching results:', results.error);
        } else {
            console.log('\n📊 Processing Summary:');
            console.log(`   - Valid Protection Rate: ${results.processing_summary.valid_protection_rate}%`);
            console.log(`   - False Positive Detection Rate: ${results.processing_summary.fp_detection_rate}%`);
            console.log(`   - Annual Cost Savings: $${results.business_impact.annual_cost_savings.toLocaleString()}`);
            
            // Navigate to results page if available
            const resultsLink = await page.$('a[href*="results"]');
            if (resultsLink) {
                await resultsLink.click();
                await page.waitForNavigation({ waitUntil: 'networkidle2' });
                await page.screenshot({ 
                    path: path.join(screenshotsDir, '04-results-page.png'), 
                    fullPage: true 
                });
            }
        }
        
        // Step 6: Check detailed results
        console.log('📍 Step 6: Checking detailed results...');
        
        const detailedResults = await page.evaluate(async () => {
            try {
                const res = await fetch('http://localhost:8001/api/v1/batch/api/batch/results/details?limit=10');
                return await res.json();
            } catch (error) {
                return { error: error.message };
            }
        });
        
        if (!detailedResults.error) {
            console.log(`\n📋 Sample Results (${detailedResults.total} total cases):`);
            detailedResults.results.slice(0, 5).forEach((result, index) => {
                console.log(`   ${index + 1}. Case ${result.case_number}: ${result.is_false_positive ? 'False Positive' : 'Valid'} (Confidence: ${result.confidence}%)`);
            });
        }
        
        console.log('\n✅ Batch processing test completed successfully!');
        
        // Create summary report
        const report = {
            timestamp: new Date().toISOString(),
            status: 'success',
            jobId: jobId,
            results: results,
            screenshotCount: await fs.readdir(screenshotsDir).then(files => files.length),
            testDuration: attempts * 5 + ' seconds'
        };
        
        await fs.writeFile(
            path.join(screenshotsDir, 'test-report.json'),
            JSON.stringify(report, null, 2)
        );
        
    } catch (error) {
        console.error('❌ Test failed:', error);
        try {
            const page = await browser.newPage();
            await page.screenshot({ 
                path: path.join(screenshotsDir, 'error-screenshot.png'), 
                fullPage: true 
            });
        } catch (screenshotError) {
            console.error('Could not capture error screenshot:', screenshotError);
        }
    } finally {
        await browser.close();
    }
}

// Run the test
testBatchProcessing().catch(console.error);