#!/usr/bin/env python3
"""Test different model names for VLM API"""
import aiohttp
import asyncio
import json

async def test_models():
    vlm_endpoint = "http://100.106.127.35:9500/v1/chat/completions"
    
    # Try different model names
    models_to_try = [
        "internvl2",
        "internvl",
        "internvl-38b", 
        "internvl3-38b",
        "internvl3",
        "VLM-38B-AWQ",
        "vlm-38b-awq",
        "default",
        None  # Try without model
    ]
    
    for model in models_to_try:
        print(f"\nTrying model: {model}")
        
        payload = {
            "messages": [
                {
                    "role": "user",
                    "content": "Hello, test response."
                }
            ],
            "temperature": 0.1,
            "max_tokens": 50
        }
        
        if model is not None:
            payload["model"] = model
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(vlm_endpoint, json=payload, timeout=10) as response:
                    print(f"  Status: {response.status}")
                    if response.status == 200:
                        result = await response.json()
                        print(f"  Success! Model '{model}' works")
                        print(f"  Response: {result['choices'][0]['message']['content'][:50]}")
                        return model
                    else:
                        error = await response.text()
                        print(f"  Error: {error[:100]}")
        except Exception as e:
            print(f"  Exception: {e}")
    
    print("\nNo working model found!")
    return None

if __name__ == "__main__":
    working_model = asyncio.run(test_models())
    if working_model:
        print(f"\n✅ Use model: {working_model}")
    else:
        print("\n❌ No working model found")