#!/usr/bin/env python3
"""
Complete Round 3 processing with robust error handling
Continues from case 760 with optimized chunk size for system
"""

import json
import asyncio
import logging
from datetime import datetime
import sys
import os
import httpx
import base64

sys.path.append('/home/<USER>/VALO_AI-FARM_2025/backend')
from app.services.valo_batch_processor import VALOBatchProcessor

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class RobustRound3Processor(VALOBatchProcessor):
    """Enhanced processor with better error handling"""
    
    def __init__(self):
        super().__init__()
        self.round3_results = []
        
    async def analyze_with_vlm_robust(self, image_path: str, prompt: str, case_info: dict) -> dict:
        """Robust VLM analysis with retries and fallback"""
        max_retries = 3
        retry_delay = 2.0
        
        for attempt in range(max_retries):
            try:
                # Read and encode image
                with open(image_path, 'rb') as f:
                    image_data = f.read()
                base64_image = base64.b64encode(image_data).decode('utf-8')
                
                # Use primary endpoint only
                async with httpx.AsyncClient(timeout=45.0) as client:
                    response = await client.post(
                        "http://100.106.127.35:9500/v1/chat/completions",
                        headers={"Content-Type": "application/json"},
                        json={
                            "model": "VLM-38B-AWQ",
                            "messages": [{
                                "role": "user",
                                "content": [
                                    {"type": "text", "text": prompt},
                                    {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{base64_image}"}}
                                ]
                            }],
                            "max_tokens": 300,
                            "temperature": 0.1
                        }
                    )
                    
                    if response.status_code == 200:
                        result = response.json()
                        return self.parse_vlm_response(
                            result['choices'][0]['message']['content'],
                            case_info
                        )
                    else:
                        logger.warning(f"VLM returned {response.status_code} for {case_info['case_number']}, attempt {attempt+1}")
                        
            except Exception as e:
                logger.warning(f"Attempt {attempt+1} failed for {case_info['case_number']}: {str(e)}")
                if attempt < max_retries - 1:
                    await asyncio.sleep(retry_delay * (attempt + 1))
        
        # Safe default if all attempts fail
        return {
            'case_number': case_info['case_number'],
            'vlm_decision': 'FLAG_FOR_REVIEW',
            'person_detected': 'unclear',
            'confidence': 0,
            'is_false_positive_predicted': False,
            'reasoning': 'VLM temporarily unavailable - defaulting to safety',
            'processing_error': True
        }


async def complete_round3():
    """Complete Round 3 from case 760"""
    
    processor = RobustRound3Processor()
    
    # Load all cases
    all_cases = processor.load_all_cases()
    logger.info(f"Total cases available: {len(all_cases)}")
    
    # Load previous progress
    start_index = 760  # Resume from where we stopped
    
    # Load previous results
    try:
        # Load from intermediate file
        if os.path.exists('valo_batch_round3_intermediate.json'):
            with open('valo_batch_round3_intermediate.json', 'r') as f:
                previous_results = json.load(f)
                if isinstance(previous_results, list):
                    processor.round3_results = previous_results
                    logger.info(f"Loaded {len(processor.round3_results)} previous results")
                else:
                    processor.round3_results = []
    except Exception as e:
        logger.error(f"Error loading previous results: {e}")
        processor.round3_results = []
    
    # Get remaining cases
    remaining_cases = all_cases[start_index:]
    logger.info(f"Processing {len(remaining_cases)} remaining cases")
    
    # Round 3 balanced prompt
    round3_prompt = """CRITICAL SAFETY SYSTEM - FALSE POSITIVE ANALYSIS
    
This is a safety-critical system. Our #1 priority is to NEVER filter out a valid safety violation.
Only dismiss cases when you are VERY confident (>90%) it's a false positive.

UPDATED ANALYSIS RULES:
1. Person Detection is KEY - ALL safety violations require a person
2. If you see ANY person in the cropped area, it's likely VALID
3. Equipment-only images are FALSE POSITIVES
4. When uncertain, always FLAG_FOR_REVIEW

Analyze this safety alert image:
- PERSON_DETECTED: [yes/no/possibly] - Any human visible in cropped area?
- EQUIPMENT_ONLY: [yes/no] - Only machinery/equipment visible?
- SAFETY_DECISION: [FLAG_FOR_REVIEW/DISMISS_WITH_CAUTION]
- CONFIDENCE: [0-100] - How confident in false positive assessment?
- REASONING: Brief explanation

CRITICAL: Only use DISMISS_WITH_CAUTION when:
- NO person visible (confidence >90%)
- ONLY equipment/machinery in frame
- Clear misidentification"""

    # Process in optimal chunks for 12-core system
    chunk_size = 8  # Optimal for system resources
    delay_between_chunks = 2.0
    
    for chunk_idx in range(0, len(remaining_cases), chunk_size):
        chunk = remaining_cases[chunk_idx:chunk_idx + chunk_size]
        chunk_start_time = datetime.now()
        
        logger.info(f"Processing chunk {chunk_idx//chunk_size + 1}/{len(remaining_cases)//chunk_size + 1}")
        
        # Process chunk with concurrent requests
        tasks = []
        for case in chunk:
            task = processor.analyze_with_vlm_robust(
                case['cropped_image'],
                round3_prompt,
                case
            )
            tasks.append(task)
        
        # Get results
        chunk_results = await asyncio.gather(*tasks)
        
        # Process results
        for case, vlm_result in zip(chunk, chunk_results):
            full_result = {**case, **vlm_result, 'round': 3}
            
            # Calculate accuracy
            if case['is_false_positive']:
                full_result['correct_prediction'] = vlm_result['is_false_positive_predicted']
            else:
                full_result['correct_prediction'] = not vlm_result['is_false_positive_predicted']
                full_result['valid_case_protected'] = not vlm_result['is_false_positive_predicted']
            
            processor.round3_results.append(full_result)
        
        # Calculate current stats
        total_processed = start_index + chunk_idx + len(chunk)
        all_results = processor.round3_results
        
        valid_cases = [r for r in all_results if not r.get('is_false_positive', True)]
        fp_cases = [r for r in all_results if r.get('is_false_positive', False)]
        
        valid_protected = sum(1 for r in valid_cases if r.get('valid_case_protected', True))
        fp_detected = sum(1 for r in fp_cases if r.get('is_false_positive_predicted', False))
        
        valid_protection_rate = (valid_protected / len(valid_cases) * 100) if valid_cases else 100
        fp_detection_rate = (fp_detected / len(fp_cases) * 100) if fp_cases else 0
        
        # Save progress
        progress = {
            'round': 3,
            'cases_processed': total_processed,
            'timestamp': datetime.now().isoformat(),
            'valid_protection_rate': valid_protection_rate,
            'fp_detection_rate': fp_detection_rate,
            'remaining_cases': len(all_cases) - total_processed
        }
        
        with open('valo_intelligent_round3_progress.json', 'w') as f:
            json.dump(progress, f, indent=2)
        
        # Save intermediate results
        with open('valo_batch_round3_intermediate.json', 'w') as f:
            json.dump(processor.round3_results, f, indent=2)
        
        # Log progress
        chunk_time = (datetime.now() - chunk_start_time).total_seconds()
        logger.info(f"Processed {len(chunk)} cases in {chunk_time:.1f}s")
        logger.info(f"Total: {total_processed}/{len(all_cases)} | Valid Protection: {valid_protection_rate:.1f}% | FP Detection: {fp_detection_rate:.1f}%")
        
        # Delay between chunks
        if chunk_idx + chunk_size < len(remaining_cases):
            await asyncio.sleep(delay_between_chunks)
    
    # Final summary
    logger.info("\n" + "="*60)
    logger.info("ROUND 3 COMPLETE!")
    logger.info(f"Total cases processed: {len(processor.round3_results)}")
    logger.info(f"Valid protection rate: {valid_protection_rate:.1f}%")
    logger.info(f"False positive detection rate: {fp_detection_rate:.1f}%")
    logger.info("="*60)
    
    # Save final results
    final_stats = {
        'round': 3,
        'total_cases': len(processor.round3_results),
        'valid_cases_total': len(valid_cases),
        'fp_cases_total': len(fp_cases),
        'valid_protected': valid_protected,
        'fp_detected': fp_detected,
        'valid_protection_rate': valid_protection_rate,
        'fp_detection_rate': fp_detection_rate,
        'timestamp': datetime.now().isoformat()
    }
    
    with open('valo_batch_round3_complete.json', 'w') as f:
        json.dump({
            'stats': final_stats,
            'results': processor.round3_results
        }, f, indent=2)
    
    logger.info(f"Saved final results to valo_batch_round3_complete.json")
    
    return final_stats


if __name__ == "__main__":
    asyncio.run(complete_round3())