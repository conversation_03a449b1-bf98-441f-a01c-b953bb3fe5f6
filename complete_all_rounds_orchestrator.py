#!/usr/bin/env python3
"""
Modified orchestrator to complete specific rounds only
"""
import json
import asyncio
import subprocess
import os
import time
import logging
from datetime import datetime

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(message)s',
    handlers=[
        logging.FileHandler('complete_all_rounds.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

ROUNDS_TO_COMPLETE = [7, 8, 9, 10, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25]

# Import the original orchestrator's round definitions
import sys
sys.path.append('/home/<USER>/VALO_AI-FARM_2025')
from overnight_orchestrator_25rounds import OvernightOrchestrator25

class CompleteAllRoundsOrchestrator(OvernightOrchestrator25):
    async def run_all_rounds(self):
        """Run only the remaining rounds"""
        self.log_status("="*80)
        self.log_status("COMPLETING ALL REMAINING ROUNDS")
        self.log_status(f"Rounds to complete: {ROUNDS_TO_COMPLETE}")
        self.log_status("="*80)
        
        # Start health monitor if not running
        if not any('health_monitor' in line for line in subprocess.run(['ps', 'aux'], capture_output=True, text=True).stdout.splitlines()):
            subprocess.Popen(['python3', 'health_monitor.py'])
            self.log_status("Health monitor started")
        
        # Run each remaining round
        for round_num in ROUNDS_TO_COMPLETE:
            # Skip if already complete
            import glob
            if glob.glob(f'valo_round{round_num}_*_complete.json'):
                self.log_status(f"Round {round_num} already complete, skipping")
                continue
            
            # Run the round
            self.log_status(f"\nStarting Round {round_num}")
            success = await self.run_round(round_num)
            
            if not success:
                self.log_status(f"Round {round_num} failed, continuing anyway")
            else:
                self.log_status(f"Round {round_num} complete")
            
            # Brief pause
            await asyncio.sleep(5)
        
        # Generate final report
        self.generate_comprehensive_report()
    
    def generate_comprehensive_report(self):
        """Generate report of all rounds"""
        all_results = {}
        
        # Load all completed rounds
        import glob
        for i in range(3, 26):
            files = glob.glob(f'valo_round{i}_*_complete.json')
            if files:
                with open(files[0], 'r') as f:
                    data = json.load(f)
                    stats = data.get('stats', {})
                    all_results[i] = {
                        'fp_rate': stats.get('fp_detection_rate', 0),
                        'valid_protection': stats.get('valid_protection_rate', 100),
                        'strategy': data.get('strategy', 'Unknown')
                    }
        
        # Find best round
        best_round = max(all_results.items(), key=lambda x: x[1]['fp_rate'])
        
        report = {
            'completion_time': datetime.now().isoformat(),
            'rounds_completed': list(all_results.keys()),
            'best_round': {
                'number': best_round[0],
                'fp_rate': best_round[1]['fp_rate'],
                'strategy': best_round[1]['strategy']
            },
            'all_results': all_results,
            'recommendation': f"Use Round {best_round[0]} ({best_round[1]['strategy']}) for production with {best_round[1]['fp_rate']:.1f}% FP reduction"
        }
        
        with open('ALL_ROUNDS_COMPLETE_REPORT.json', 'w') as f:
            json.dump(report, f, indent=2)
        
        self.log_status("\n" + "="*80)
        self.log_status("ALL ROUNDS COMPLETE!")
        self.log_status(f"Best performance: Round {best_round[0]} with {best_round[1]['fp_rate']:.1f}% FP reduction")
        self.log_status("Full report: ALL_ROUNDS_COMPLETE_REPORT.json")
        self.log_status("="*80)

async def main():
    orchestrator = CompleteAllRoundsOrchestrator()
    await orchestrator.run_all_rounds()

if __name__ == "__main__":
    asyncio.run(main())
