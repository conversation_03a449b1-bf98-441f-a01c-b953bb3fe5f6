#!/usr/bin/env python3
"""Monitor Round 4 progress"""

import time
import os
import json
from datetime import datetime

print("Monitoring Round 4 Equipment Pattern Recognition...")
print("Target: 40% FP Detection with 100% Valid Protection")
print("-" * 60)

while True:
    # Check if Round 4 is complete
    if os.path.exists('valo_round4_equipment_complete.json'):
        with open('valo_round4_equipment_complete.json', 'r') as f:
            data = json.load(f)
            stats = data['stats']
        
        print(f"\n✅ ROUND 4 COMPLETE!")
        print(f"Valid Protection: {stats['valid_protection_rate']:.1f}%")
        print(f"FP Detection: {stats['fp_detection_rate']:.1f}%")
        print(f"Improvement: +{stats['improvement_over_round3']:.1f}%")
        
        if stats['fp_detection_rate'] >= 70:
            print("\n🎯 TARGET ACHIEVED!")
        else:
            print(f"\nGap to 70%: {70 - stats['fp_detection_rate']:.1f}%")
        break
    
    # Check log file
    if os.path.exists('round4_output.log'):
        with open('round4_output.log', 'r') as f:
            lines = f.readlines()
            if lines:
                # Find latest progress line
                for line in reversed(lines):
                    if 'Progress:' in line:
                        print(f"\r{datetime.now().strftime('%H:%M:%S')} - {line.strip()}", end='', flush=True)
                        break
    
    # Check if 70% achieved
    if os.path.exists('VALO_70_PERCENT_ACHIEVED.json'):
        print("\n\n🎯 70% TARGET ACHIEVED!")
        with open('VALO_70_PERCENT_ACHIEVED.json', 'r') as f:
            achievement = json.load(f)
        print(f"Rounds needed: {achievement['rounds_completed']}")
        break
    
    time.sleep(5)