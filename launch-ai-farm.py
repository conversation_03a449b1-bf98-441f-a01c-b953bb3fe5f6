#!/usr/bin/env python3
"""
Cross-platform launcher for AI-FARM application
Automatically detects OS and runs the appropriate startup script
"""

import os
import sys
import subprocess
import platform

def main():
    # Get the directory where this script is located
    script_dir = os.path.dirname(os.path.abspath(__file__))
    
    # Detect the operating system
    system = platform.system().lower()
    
    if system == "windows":
        script_name = "ai-farm.bat"
    else:  # Unix-like systems (Linux, macOS, etc.)
        script_name = "ai-farm.sh"
    
    script_path = os.path.join(script_dir, script_name)
    
    # Check if the script exists
    if not os.path.exists(script_path):
        print(f"Error: {script_name} not found in {script_dir}")
        sys.exit(1)
    
    # Make the script executable on Unix-like systems
    if system != "windows":
        os.chmod(script_path, 0o755)
    
    # Prepare the command
    cmd = [script_path]
    
    # Add any command line arguments
    if len(sys.argv) > 1:
        cmd.extend(sys.argv[1:])
    else:
        cmd.append("start")  # Default to start command
    
    print(f"🚀 Launching AI-FARM on {platform.system()}...")
    print(f"Running: {' '.join(cmd)}")
    print()
    
    try:
        # Execute the appropriate startup script
        if system == "windows":
            subprocess.run(cmd, shell=True, check=True)
        else:
            subprocess.run(cmd, check=True)
    except subprocess.CalledProcessError as e:
        print(f"Error: Script execution failed with exit code {e.returncode}")
        sys.exit(e.returncode)
    except KeyboardInterrupt:
        print("\n👋 Shutdown requested by user")
        sys.exit(0)

if __name__ == "__main__":
    main()