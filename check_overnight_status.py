#!/usr/bin/env python3
"""Quick status check for overnight systems"""
import json
import os
from datetime import datetime

print("="*70)
print(f"OVERNIGHT PRODUCTION TEST STATUS - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
print("="*70)

# Check main system
if os.path.exists('overnight_progress.json'):
    with open('overnight_progress.json', 'r') as f:
        data = json.load(f)
    print(f"\nMAIN SYSTEM:")
    print(f"  Current Round: {data.get('current_round', 'N/A')}")
    best = data.get('best_result', {})
    print(f"  Best Result: {best.get('approach', 'None')} - {best.get('fp_rate', 0):.1f}%")
    print(f"  Target: {'✅ ACHIEVED' if best.get('fp_rate', 0) >= 70 else '❌ Not yet'}")

# Check specialized system  
if os.path.exists('specialized_progress.json'):
    with open('specialized_progress.json', 'r') as f:
        data = json.load(f)
    print(f"\nSPECIALIZED SYSTEM:")
    print(f"  Progress: {data.get('current_approach', 0)}/{data.get('total_approaches', 0)}")
    best = data.get('best_result', {})
    print(f"  Best Result: {best.get('approach', 'None')} - {best.get('fp_rate', 0):.1f}%")

# Check innovative system
if os.path.exists('innovative_approaches_results.json'):
    with open('innovative_approaches_results.json', 'r') as f:
        data = json.load(f)
    print(f"\nINNOVATIVE SYSTEM:")
    best = data.get('best_approach', {})
    print(f"  Best Result: {best.get('approach', 'None')} - {best.get('fp_rate', 0):.1f}%")

# Check processes
import subprocess
result = subprocess.run(['ps', 'aux'], capture_output=True, text=True)
processes = result.stdout.split('\n')
python_processes = [p for p in processes if 'python3' in p and 'production' in p]
print(f"\n{len(python_processes)} production test processes running")

print("\n" + "="*70)