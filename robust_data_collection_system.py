#!/usr/bin/env python3
"""
Robust Data Collection System with Optimal Token Limits
Collects detailed descriptions and confidence scores for all cases
"""

import json
import base64
import requests
import os
import time
from datetime import datetime
import re
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
import threading

class RobustDataCollectionSystem:
    def __init__(self):
        self.vlm_url = "http://**************:9500/v1/chat/completions"
        self.vlm_model = "VLM-38B-AWQ"
        
        # Create session pool for better performance
        self.sessions = [requests.Session() for _ in range(3)]
        self.session_index = 0
        self.session_lock = threading.Lock()
        
        # Optimal token limits (reasonable, not too low)
        self.description_tokens = 1200  # Enough for comprehensive descriptions
        self.confidence_tokens = 600    # Enough for detailed confidence analysis
        
        # Data directories
        self.base_dir = "valo_comprehensive_data"
        self.dirs = {
            'fp': f"{self.base_dir}/false_positives",
            'tp': f"{self.base_dir}/true_positives", 
            'analysis': f"{self.base_dir}/analysis",
            'structures': f"{self.base_dir}/structures",
            'raw': f"{self.base_dir}/raw_responses"
        }
        
        # Create all directories
        for dir_path in self.dirs.values():
            os.makedirs(dir_path, exist_ok=True)
        
        # Progress tracking
        self.progress = {
            'total': 0,
            'processed': 0,
            'fp_processed': 0,
            'tp_processed': 0,
            'errors': 0,
            'start_time': None
        }
        
        # Results cache
        self.results_cache = []
        
    def get_session(self):
        """Get next available session (round-robin)"""
        with self.session_lock:
            session = self.sessions[self.session_index]
            self.session_index = (self.session_index + 1) % len(self.sessions)
            return session
    
    def encode_image(self, image_path):
        """Encode image to base64"""
        try:
            if os.path.exists(image_path):
                with open(image_path, 'rb') as f:
                    return base64.b64encode(f.read()).decode('utf-8')
        except Exception as e:
            print(f"Error encoding {image_path}: {str(e)}")
        return None
    
    def get_comprehensive_description(self, case):
        """Get comprehensive description of the cropped image"""
        cropped_b64 = self.encode_image(case['cropped_image'])
        if not cropped_b64:
            return None
            
        # Comprehensive prompt for maximum information extraction
        prompt = """Analyze this safety alert image in extreme detail. Provide a comprehensive description covering:

1. MAIN SUBJECT IDENTIFICATION
   - Primary subject type: Person/Equipment/Structure/Vehicle
   - If equipment: Exact type (crane/vessel/truck/spreader/container/machinery)
   - If person: Number of individuals, gender if visible, apparent role

2. PERSON DETAILS (if any visible)
   - Exact position and posture
   - Clothing description (colors, type, condition)
   - Safety equipment worn:
     * Head protection (helmet/hard hat) - color, style, properly worn?
     * High-visibility clothing - color, reflective strips visible?
     * Other PPE - gloves, safety shoes, harness, goggles
   - What safety equipment is MISSING?

3. ACTIVITY ANALYSIS
   - What specific action is being performed?
   - Tools or equipment being used
   - Body mechanics and positioning
   - Duration indicators (static position vs movement)

4. ENVIRONMENT AND CONTEXT
   - Exact location (vessel deck/quay/yard/height/confined space)
   - Surrounding equipment and structures
   - Weather/lighting conditions
   - Time of day indicators
   - Potential hazards in vicinity

5. SAFETY ASSESSMENT
   - Primary safety concern visible
   - Secondary safety risks
   - Violation type if apparent
   - Severity estimation (minor/moderate/severe)

6. ADDITIONAL OBSERVATIONS
   - Image quality and clarity
   - Viewing angle and distance
   - Any text or signage visible
   - Unusual or noteworthy elements

Provide the most detailed, factual description possible. Every detail matters for safety analysis."""
        
        payload = {
            "model": self.vlm_model,
            "messages": [{
                "role": "user",
                "content": [
                    {"type": "text", "text": prompt},
                    {"type": "text", "text": "\nCROPPED ALERT IMAGE:"},
                    {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{cropped_b64}"}}
                ]
            }],
            "temperature": 0.15,  # Slightly higher for more detailed descriptions
            "max_tokens": self.description_tokens
        }
        
        try:
            session = self.get_session()
            response = session.post(self.vlm_url, json=payload, timeout=90)
            
            if response.status_code == 200:
                return response.json()['choices'][0]['message']['content']
            else:
                print(f"API Error {response.status_code}: {response.text[:100]}")
                
        except Exception as e:
            print(f"Error getting description: {str(e)}")
        
        return None
    
    def get_confidence_analysis(self, case, description):
        """Get confidence score by comparing description with image"""
        cropped_b64 = self.encode_image(case['cropped_image'])
        if not cropped_b64:
            return None
            
        # Create a structured prompt for confidence analysis
        prompt = f"""You previously provided this description of a safety alert image:

"{description}"

Now, looking at the same image again, please analyze:

1. DESCRIPTION ACCURACY (0-100%):
   How accurately does the above description match what you see in the image?
   Consider: completeness, accuracy of details, any missing elements

2. SUBJECT CONFIRMATION:
   - Is there definitely a PERSON in this image? YES/NO
   - If NO, what is the main subject? (Equipment/Structure/Vehicle/Other)
   - Confidence in subject identification: (0-100%)

3. PPE COMPLIANCE CHECK (if person present):
   - Helmet/Hard hat present? YES/NO/PARTIAL/NA
   - High-visibility vest present? YES/NO/PARTIAL/NA
   - Overall PPE compliance: COMPLETE/INCOMPLETE/NONE/NA
   - PPE confidence: (0-100%)

4. SAFETY VIOLATION ASSESSMENT:
   - Is there a genuine safety violation visible? YES/NO/UNCERTAIN
   - If YES, describe the specific violation
   - Violation severity: MINOR/MODERATE/SEVERE/NA
   - Violation confidence: (0-100%)

5. FALSE POSITIVE INDICATORS:
   - List any factors suggesting this might be a false positive
   - False positive likelihood: (0-100%)

Format your response EXACTLY as:
DESCRIPTION_ACCURACY: [X]%
PERSON_PRESENT: [YES/NO]
MAIN_SUBJECT: [Person/Equipment/Structure/Vehicle/Other]
SUBJECT_CONFIDENCE: [X]%
HELMET_STATUS: [YES/NO/PARTIAL/NA]
VEST_STATUS: [YES/NO/PARTIAL/NA]
PPE_COMPLIANCE: [COMPLETE/INCOMPLETE/NONE/NA]
PPE_CONFIDENCE: [X]%
SAFETY_VIOLATION: [YES/NO/UNCERTAIN]
VIOLATION_DESCRIPTION: [Description or NONE]
VIOLATION_SEVERITY: [MINOR/MODERATE/SEVERE/NA]
VIOLATION_CONFIDENCE: [X]%
FALSE_POSITIVE_LIKELIHOOD: [X]%
FP_INDICATORS: [List or NONE]"""
        
        payload = {
            "model": self.vlm_model,
            "messages": [{
                "role": "user",
                "content": [
                    {"type": "text", "text": prompt},
                    {"type": "text", "text": "\nANALYZING THE SAME IMAGE:"},
                    {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{cropped_b64}"}}
                ]
            }],
            "temperature": 0.1,  # Low temperature for consistent analysis
            "max_tokens": self.confidence_tokens
        }
        
        try:
            session = self.get_session()
            response = session.post(self.vlm_url, json=payload, timeout=90)
            
            if response.status_code == 200:
                return response.json()['choices'][0]['message']['content']
                
        except Exception as e:
            print(f"Error getting confidence: {str(e)}")
        
        return None
    
    def parse_confidence_data(self, response):
        """Parse the structured confidence response"""
        parsed = {
            'description_accuracy': 0,
            'person_present': False,
            'main_subject': 'UNKNOWN',
            'subject_confidence': 0,
            'helmet_status': 'NA',
            'vest_status': 'NA',
            'ppe_compliance': 'NA',
            'ppe_confidence': 0,
            'safety_violation': 'UNCERTAIN',
            'violation_description': 'NONE',
            'violation_severity': 'NA',
            'violation_confidence': 0,
            'false_positive_likelihood': 0,
            'fp_indicators': []
        }
        
        if not response:
            return parsed
            
        # Parse each field
        patterns = {
            'description_accuracy': r'DESCRIPTION_ACCURACY:\s*(\d+)%',
            'person_present': r'PERSON_PRESENT:\s*(YES|NO)',
            'main_subject': r'MAIN_SUBJECT:\s*([^\n]+)',
            'subject_confidence': r'SUBJECT_CONFIDENCE:\s*(\d+)%',
            'helmet_status': r'HELMET_STATUS:\s*(\w+)',
            'vest_status': r'VEST_STATUS:\s*(\w+)',
            'ppe_compliance': r'PPE_COMPLIANCE:\s*(\w+)',
            'ppe_confidence': r'PPE_CONFIDENCE:\s*(\d+)%',
            'safety_violation': r'SAFETY_VIOLATION:\s*(YES|NO|UNCERTAIN)',
            'violation_description': r'VIOLATION_DESCRIPTION:\s*([^\n]+)',
            'violation_severity': r'VIOLATION_SEVERITY:\s*(\w+)',
            'violation_confidence': r'VIOLATION_CONFIDENCE:\s*(\d+)%',
            'false_positive_likelihood': r'FALSE_POSITIVE_LIKELIHOOD:\s*(\d+)%',
            'fp_indicators': r'FP_INDICATORS:\s*([^\n]+)'
        }
        
        for field, pattern in patterns.items():
            match = re.search(pattern, response, re.IGNORECASE)
            if match:
                value = match.group(1).strip()
                if field in ['description_accuracy', 'subject_confidence', 'ppe_confidence', 
                           'violation_confidence', 'false_positive_likelihood']:
                    parsed[field] = int(value)
                elif field == 'person_present':
                    parsed[field] = value.upper() == 'YES'
                elif field == 'fp_indicators':
                    parsed[field] = [i.strip() for i in value.split(',') if i.strip() and i.strip() != 'NONE']
                else:
                    parsed[field] = value
        
        return parsed
    
    def process_single_case(self, case, case_index, total_cases):
        """Process a single case with progress tracking"""
        case_number = case['case_number']
        is_fp = case['is_false_positive']
        case_type = 'FP' if is_fp else 'TP'
        
        print(f"\n[{case_index}/{total_cases}] Processing {case_number} ({case_type})...")
        
        # Step 1: Get comprehensive description
        start_time = time.time()
        description = self.get_comprehensive_description(case)
        
        if not description:
            print(f"  ❌ Failed to get description")
            self.progress['errors'] += 1
            return None
        
        desc_time = time.time() - start_time
        print(f"  ✓ Description obtained ({len(description)} chars in {desc_time:.1f}s)")
        
        # Step 2: Get confidence analysis
        time.sleep(1)  # Rate limiting
        confidence_response = self.get_confidence_analysis(case, description)
        
        if not confidence_response:
            print(f"  ❌ Failed to get confidence analysis")
            self.progress['errors'] += 1
            return None
        
        conf_time = time.time() - start_time - desc_time
        confidence_data = self.parse_confidence_data(confidence_response)
        
        print(f"  ✓ Confidence analysis complete ({conf_time:.1f}s)")
        print(f"    - Description Accuracy: {confidence_data['description_accuracy']}%")
        print(f"    - Person Present: {confidence_data['person_present']}")
        print(f"    - FP Likelihood: {confidence_data['false_positive_likelihood']}%")
        
        # Compile complete result
        result = {
            'case_number': case_number,
            'case_type': case_type,
            'is_false_positive': is_fp,
            'source_image': case['source_image'],
            'cropped_image': case['cropped_image'],
            'description': description,
            'description_length': len(description),
            'confidence_response': confidence_response,
            'confidence_data': confidence_data,
            'processing_time': time.time() - start_time,
            'timestamp': datetime.now().isoformat()
        }
        
        # Update progress
        self.progress['processed'] += 1
        if is_fp:
            self.progress['fp_processed'] += 1
        else:
            self.progress['tp_processed'] += 1
        
        return result
    
    def save_to_markdown(self, result, file_path):
        """Save result to markdown file with rich formatting"""
        with open(file_path, 'a', encoding='utf-8') as f:
            f.write(f"\n## Case: {result['case_number']}\n")
            f.write(f"**Timestamp**: {result['timestamp']}\n")
            f.write(f"**Type**: {'FALSE POSITIVE' if result['is_false_positive'] else 'TRUE POSITIVE (Valid Violation)'}\n")
            f.write(f"**Processing Time**: {result['processing_time']:.1f} seconds\n")
            f.write(f"**Cropped Image**: `{result['cropped_image']}`\n\n")
            
            # Description section
            f.write("### Comprehensive Description\n")
            f.write(f"*Length: {result['description_length']} characters*\n\n")
            f.write("```\n")
            f.write(result['description'])
            f.write("\n```\n\n")
            
            # Confidence Analysis section
            f.write("### Confidence Analysis\n")
            conf = result['confidence_data']
            
            f.write("#### Accuracy Metrics\n")
            f.write(f"- **Description Accuracy**: {conf['description_accuracy']}%\n")
            f.write(f"- **Subject Confidence**: {conf['subject_confidence']}%\n")
            f.write(f"- **False Positive Likelihood**: {conf['false_positive_likelihood']}%\n\n")
            
            f.write("#### Subject Identification\n")
            f.write(f"- **Person Present**: {'YES' if conf['person_present'] else 'NO'}\n")
            f.write(f"- **Main Subject**: {conf['main_subject']}\n\n")
            
            if conf['person_present']:
                f.write("#### PPE Compliance\n")
                f.write(f"- **Helmet Status**: {conf['helmet_status']}\n")
                f.write(f"- **Vest Status**: {conf['vest_status']}\n")
                f.write(f"- **Overall Compliance**: {conf['ppe_compliance']}\n")
                f.write(f"- **PPE Confidence**: {conf['ppe_confidence']}%\n\n")
            
            f.write("#### Safety Violation Assessment\n")
            f.write(f"- **Violation Present**: {conf['safety_violation']}\n")
            f.write(f"- **Violation Description**: {conf['violation_description']}\n")
            f.write(f"- **Severity**: {conf['violation_severity']}\n")
            f.write(f"- **Violation Confidence**: {conf['violation_confidence']}%\n\n")
            
            if conf['fp_indicators']:
                f.write("#### False Positive Indicators\n")
                for indicator in conf['fp_indicators']:
                    f.write(f"- {indicator}\n")
                f.write("\n")
            
            f.write("### Raw Confidence Response\n")
            f.write("<details>\n<summary>Click to expand</summary>\n\n")
            f.write("```\n")
            f.write(result['confidence_response'])
            f.write("\n```\n")
            f.write("</details>\n\n")
            
            f.write("---\n")
    
    def run_comprehensive_collection(self):
        """Run the comprehensive data collection on all cases"""
        print("="*80)
        print("🚀 ROBUST DATA COLLECTION SYSTEM")
        print("="*80)
        print(f"Description Token Limit: {self.description_tokens}")
        print(f"Confidence Token Limit: {self.confidence_tokens}")
        print("="*80)
        
        self.progress['start_time'] = time.time()
        
        # Load all cases
        with open('valo_batch_round3_complete.json', 'r') as f:
            all_cases = json.load(f)['results']
        
        self.progress['total'] = len(all_cases)
        
        # Separate FP and TP
        fp_cases = [c for c in all_cases if c['is_false_positive']]
        tp_cases = [c for c in all_cases if not c['is_false_positive']]
        
        print(f"\n📊 Dataset Overview:")
        print(f"  Total Cases: {len(all_cases)}")
        print(f"  False Positives: {len(fp_cases)}")
        print(f"  True Positives: {len(tp_cases)}")
        
        # Initialize markdown files
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        fp_file = f"{self.dirs['fp']}/false_positive_analysis_{timestamp}.md"
        tp_file = f"{self.dirs['tp']}/true_positive_analysis_{timestamp}.md"
        
        # Create file headers
        with open(fp_file, 'w') as f:
            f.write("# False Positive Cases - Comprehensive Analysis\n")
            f.write(f"Generated: {datetime.now().isoformat()}\n")
            f.write(f"Total FP Cases: {len(fp_cases)}\n\n")
            f.write("---\n")
        
        with open(tp_file, 'w') as f:
            f.write("# True Positive Cases (Valid Violations) - Comprehensive Analysis\n")
            f.write(f"Generated: {datetime.now().isoformat()}\n")
            f.write(f"Total TP Cases: {len(tp_cases)}\n\n")
            f.write("---\n")
        
        # Process all cases
        all_results = []
        case_index = 0
        
        # Process FALSE POSITIVES
        print("\n📁 PROCESSING FALSE POSITIVE CASES...")
        print("="*60)
        
        for case in fp_cases:
            case_index += 1
            result = self.process_single_case(case, case_index, len(all_cases))
            
            if result:
                self.save_to_markdown(result, fp_file)
                all_results.append(result)
                self.results_cache.append(result)
            
            # Progress update every 10 cases
            if case_index % 10 == 0:
                elapsed = time.time() - self.progress['start_time']
                rate = case_index / elapsed if elapsed > 0 else 0
                eta = (len(all_cases) - case_index) / rate / 60 if rate > 0 else 0
                
                print(f"\n📊 Progress Update:")
                print(f"  Processed: {case_index}/{len(all_cases)} ({case_index/len(all_cases)*100:.1f}%)")
                print(f"  Rate: {rate:.1f} cases/min")
                print(f"  ETA: {eta:.1f} minutes")
            
            time.sleep(0.5)  # Rate limiting
        
        # Process TRUE POSITIVES
        print("\n📁 PROCESSING TRUE POSITIVE CASES...")
        print("="*60)
        
        for case in tp_cases:
            case_index += 1
            result = self.process_single_case(case, case_index, len(all_cases))
            
            if result:
                self.save_to_markdown(result, tp_file)
                all_results.append(result)
                self.results_cache.append(result)
            
            # Progress update
            if case_index % 10 == 0:
                elapsed = time.time() - self.progress['start_time']
                rate = case_index / elapsed if elapsed > 0 else 0
                eta = (len(all_cases) - case_index) / rate / 60 if rate > 0 else 0
                
                print(f"\n📊 Progress Update:")
                print(f"  Processed: {case_index}/{len(all_cases)} ({case_index/len(all_cases)*100:.1f}%)")
                print(f"  Rate: {rate:.1f} cases/min")
                print(f"  ETA: {eta:.1f} minutes")
            
            time.sleep(0.5)  # Rate limiting
        
        # Generate comprehensive analysis
        self.generate_comprehensive_analysis(all_results, timestamp)
        
        # Final summary
        total_time = time.time() - self.progress['start_time']
        print("\n" + "="*80)
        print("✅ DATA COLLECTION COMPLETE!")
        print("="*80)
        print(f"Total Time: {total_time/60:.1f} minutes")
        print(f"Total Processed: {self.progress['processed']}/{self.progress['total']}")
        print(f"False Positives: {self.progress['fp_processed']}")
        print(f"True Positives: {self.progress['tp_processed']}")
        print(f"Errors: {self.progress['errors']}")
        print(f"\n📁 Output Files:")
        print(f"  - {fp_file}")
        print(f"  - {tp_file}")
        print(f"  - {self.dirs['analysis']}/comprehensive_analysis_{timestamp}.md")
        print(f"\n🎯 Ready for data science analysis!")
        
    def generate_comprehensive_analysis(self, results, timestamp):
        """Generate comprehensive analysis of all collected data"""
        analysis_file = f"{self.dirs['analysis']}/comprehensive_analysis_{timestamp}.md"
        
        with open(analysis_file, 'w') as f:
            f.write("# Comprehensive Data Analysis\n\n")
            f.write(f"Generated: {datetime.now().isoformat()}\n")
            f.write(f"Total Cases Analyzed: {len(results)}\n\n")
            
            # Split results
            fp_results = [r for r in results if r['is_false_positive']]
            tp_results = [r for r in results if not r['is_false_positive']]
            
            # Overall statistics
            f.write("## Overall Statistics\n\n")
            f.write(f"- False Positives: {len(fp_results)}\n")
            f.write(f"- True Positives: {len(tp_results)}\n\n")
            
            # Person presence analysis
            f.write("## Person Presence Analysis\n\n")
            
            fp_with_person = sum(1 for r in fp_results if r['confidence_data']['person_present'])
            tp_with_person = sum(1 for r in tp_results if r['confidence_data']['person_present'])
            
            f.write("### False Positives\n")
            f.write(f"- With Person: {fp_with_person} ({fp_with_person/len(fp_results)*100:.1f}%)\n")
            f.write(f"- Without Person: {len(fp_results)-fp_with_person} ({(len(fp_results)-fp_with_person)/len(fp_results)*100:.1f}%)\n\n")
            
            f.write("### True Positives\n")
            f.write(f"- With Person: {tp_with_person} ({tp_with_person/len(tp_results)*100:.1f}%)\n")
            f.write(f"- Without Person: {len(tp_results)-tp_with_person} ({(len(tp_results)-tp_with_person)/len(tp_results)*100:.1f}%)\n\n")
            
            # PPE Compliance analysis
            f.write("## PPE Compliance Analysis\n\n")
            
            # For cases with people
            fp_people = [r for r in fp_results if r['confidence_data']['person_present']]
            tp_people = [r for r in tp_results if r['confidence_data']['person_present']]
            
            if fp_people:
                f.write("### False Positives with People\n")
                ppe_complete = sum(1 for r in fp_people if r['confidence_data']['ppe_compliance'] == 'COMPLETE')
                ppe_incomplete = sum(1 for r in fp_people if r['confidence_data']['ppe_compliance'] == 'INCOMPLETE')
                ppe_none = sum(1 for r in fp_people if r['confidence_data']['ppe_compliance'] == 'NONE')
                
                f.write(f"- Complete PPE: {ppe_complete} ({ppe_complete/len(fp_people)*100:.1f}%)\n")
                f.write(f"- Incomplete PPE: {ppe_incomplete} ({ppe_incomplete/len(fp_people)*100:.1f}%)\n")
                f.write(f"- No PPE: {ppe_none} ({ppe_none/len(fp_people)*100:.1f}%)\n\n")
            
            if tp_people:
                f.write("### True Positives with People\n")
                ppe_complete = sum(1 for r in tp_people if r['confidence_data']['ppe_compliance'] == 'COMPLETE')
                ppe_incomplete = sum(1 for r in tp_people if r['confidence_data']['ppe_compliance'] == 'INCOMPLETE')
                ppe_none = sum(1 for r in tp_people if r['confidence_data']['ppe_compliance'] == 'NONE')
                
                f.write(f"- Complete PPE: {ppe_complete} ({ppe_complete/len(tp_people)*100:.1f}%)\n")
                f.write(f"- Incomplete PPE: {ppe_incomplete} ({ppe_incomplete/len(tp_people)*100:.1f}%)\n")
                f.write(f"- No PPE: {ppe_none} ({ppe_none/len(tp_people)*100:.1f}%)\n\n")
            
            # Confidence score analysis
            f.write("## Confidence Score Analysis\n\n")
            
            fp_desc_acc = [r['confidence_data']['description_accuracy'] for r in fp_results]
            tp_desc_acc = [r['confidence_data']['description_accuracy'] for r in tp_results]
            
            fp_fp_likelihood = [r['confidence_data']['false_positive_likelihood'] for r in fp_results]
            tp_fp_likelihood = [r['confidence_data']['false_positive_likelihood'] for r in tp_results]
            
            f.write("### Average Description Accuracy\n")
            f.write(f"- False Positives: {sum(fp_desc_acc)/len(fp_desc_acc):.1f}%\n")
            f.write(f"- True Positives: {sum(tp_desc_acc)/len(tp_desc_acc):.1f}%\n\n")
            
            f.write("### Average False Positive Likelihood\n")
            f.write(f"- Actual False Positives: {sum(fp_fp_likelihood)/len(fp_fp_likelihood):.1f}%\n")
            f.write(f"- Actual True Positives: {sum(tp_fp_likelihood)/len(tp_fp_likelihood):.1f}%\n\n")
            
            # Subject type distribution
            f.write("## Subject Type Distribution\n\n")
            
            fp_subjects = {}
            tp_subjects = {}
            
            for r in fp_results:
                subject = r['confidence_data']['main_subject']
                fp_subjects[subject] = fp_subjects.get(subject, 0) + 1
                
            for r in tp_results:
                subject = r['confidence_data']['main_subject']
                tp_subjects[subject] = tp_subjects.get(subject, 0) + 1
            
            f.write("### False Positives\n")
            for subject, count in sorted(fp_subjects.items(), key=lambda x: x[1], reverse=True):
                f.write(f"- {subject}: {count} ({count/len(fp_results)*100:.1f}%)\n")
            
            f.write("\n### True Positives\n")
            for subject, count in sorted(tp_subjects.items(), key=lambda x: x[1], reverse=True):
                f.write(f"- {subject}: {count} ({count/len(tp_results)*100:.1f}%)\n")
            
            # Key insights
            f.write("\n## Key Insights\n\n")
            
            f.write("### 1. Person Presence Correlation\n")
            if (len(fp_results)-fp_with_person)/len(fp_results) > 0.7:
                f.write("- ✅ STRONG PATTERN: Most false positives ({:.1f}%) have NO person present\n".format(
                    (len(fp_results)-fp_with_person)/len(fp_results)*100))
            
            if tp_with_person/len(tp_results) > 0.8:
                f.write("- ✅ STRONG PATTERN: Most true positives ({:.1f}%) have a person present\n".format(
                    tp_with_person/len(tp_results)*100))
            
            f.write("\n### 2. PPE Compliance Patterns\n")
            if fp_people:
                fp_complete_rate = sum(1 for r in fp_people if r['confidence_data']['ppe_compliance'] == 'COMPLETE') / len(fp_people)
                if fp_complete_rate > 0.5:
                    f.write(f"- Many FPs ({fp_complete_rate*100:.1f}%) show people with COMPLETE PPE\n")
            
            f.write("\n### 3. False Positive Likelihood Accuracy\n")
            fp_avg_likelihood = sum(fp_fp_likelihood)/len(fp_fp_likelihood)
            tp_avg_likelihood = sum(tp_fp_likelihood)/len(tp_fp_likelihood)
            
            if fp_avg_likelihood > tp_avg_likelihood + 20:
                f.write("- ✅ Model correctly identifies FP likelihood (FP avg: {:.1f}%, TP avg: {:.1f}%)\n".format(
                    fp_avg_likelihood, tp_avg_likelihood))
            
            # Raw data for further analysis
            f.write("\n## Raw Data Export\n\n")
            f.write(f"All raw data saved to: `{self.dirs['raw']}/all_results_{timestamp}.json`\n")
            
            # Save raw data
            with open(f"{self.dirs['raw']}/all_results_{timestamp}.json", 'w') as raw_file:
                json.dump(results, raw_file, indent=2)

if __name__ == "__main__":
    system = RobustDataCollectionSystem()
    system.run_comprehensive_collection()