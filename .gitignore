# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Dependencies
node_modules/
__pycache__/
*.py[cod]
*$py.class
venv/
env/
.venv/

# Build outputs
build/
dist/
dist-ssr/
*.tgz
*.tar.gz

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
performance_test.log
*.pid

# Database
*.db
*.sqlite
*.sqlite3

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Testing
coverage/
.pytest_cache/
.coverage
htmlcov/
test-screenshots*/
test-results*/

# Temporary files
tmp/
temp/
*.tmp

# Backup files
backup/
*.bak
*.backup

# Archive files
*.zip
*.tar
*.tar.gz
*.rar

# Python virtual environments
venv/
env/
.venv/
.env/

# macOS
__MACOSX/