#!/usr/bin/env python3
"""
Simple overnight monitoring - shows current status
"""
import glob
import json
import time
from datetime import datetime

print("VALO AI-FARM OVERNIGHT MONITOR")
print("="*60)
print("Monitoring all rounds... (Ctrl+C to exit)")
print("")

while True:
    try:
        # Find latest round
        latest_round = 5
        latest_fp = 52.7
        
        for i in range(25, 5, -1):
            files = glob.glob(f'valo_round{i}_*_complete.json')
            if files:
                with open(files[0], 'r') as f:
                    data = json.load(f)
                    latest_round = i
                    latest_fp = data['stats']['fp_detection_rate']
                break
        
        # Check Round 6 progress
        round6_running = False
        if latest_round == 5:
            try:
                with open('round6_full_ppe.log', 'r') as f:
                    lines = f.readlines()
                    for line in reversed(lines[-5:]):
                        if 'Progress:' in line:
                            round6_running = True
                            print(f"\r[{datetime.now().strftime('%H:%M:%S')}] Round 6 RUNNING: {line.strip()}", end='', flush=True)
                            break
            except:
                pass
        
        if not round6_running:
            print(f"\r[{datetime.now().strftime('%H:%M:%S')}] Latest: Round {latest_round} - FP: {latest_fp:.1f}% | Gap to 70%: {max(0, 70-latest_fp):.1f}% | Gap to 90%: {max(0, 90-latest_fp):.1f}%", end='', flush=True)
        
        time.sleep(5)
        
    except KeyboardInterrupt:
        print("\n\nMonitoring stopped.")
        break
    except Exception as e:
        print(f"\nError: {e}")
        time.sleep(5)