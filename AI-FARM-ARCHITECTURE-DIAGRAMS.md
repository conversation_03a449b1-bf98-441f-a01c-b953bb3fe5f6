# AI-FARM System Architecture and Logic Flow Diagrams

This document contains comprehensive Mermaid diagrams showing the complete architecture and logic flow of the AI-FARM system.

## 1. High-Level System Architecture

```mermaid
graph TB
    subgraph "Frontend Layer"
        UI1[Main Frontend<br/>React + TypeScript]
        UI2[Surveillance Frontend<br/>React + TypeScript]
    end
    
    subgraph "API Gateway"
        API[FastAPI REST API<br/>Port 8001]
    end
    
    subgraph "Backend Services"
        BS1[Batch Processor<br/>Service]
        BS2[VLM Service<br/>OpenAI Compatible]
        BS3[Auto-Learning<br/>Engine]
        BS4[Background Task<br/>Manager]
    end
    
    subgraph "Data Layer"
        DB[(SQLite/PostgreSQL<br/>Database)]
        FS[File Storage<br/>Images/CSVs]
    end
    
    subgraph "External Services"
        VLM[VLM API<br/>InternVL3 38B]
    end
    
    UI1 -->|HTTP/REST| API
    UI2 -->|HTTP/WebSocket| API
    
    API --> BS1
    API --> BS3
    API --> BS4
    
    BS1 --> BS2
    BS1 --> DB
    BS1 --> FS
    
    BS2 -->|API Calls| VLM
    
    BS3 --> DB
    BS4 --> BS1
    
    classDef frontend fill:#2196F3,stroke:#1976D2,color:#fff
    classDef backend fill:#4CAF50,stroke:#388E3C,color:#fff
    classDef data fill:#FF9800,stroke:#F57C00,color:#fff
    classDef external fill:#9C27B0,stroke:#7B1FA2,color:#fff
    
    class UI1,UI2 frontend
    class API,BS1,BS2,BS3,BS4 backend
    class DB,FS data
    class VLM external
```

## 2. Backend Processing Flow

```mermaid
flowchart TD
    Start([User Uploads CSV/Images])
    
    Upload[upload_and_process_batch<br/>API Endpoint]
    Validate[Validate Input Files]
    CreateBatch[Create Batch ID<br/>& Temp Directory]
    
    BG[Background Task:<br/>process_batch_background]
    
    Parse[CSV Processor:<br/>parse_csv_file]
    ValidateImg[Validate Image Paths]
    
    CreateDB[Create Database Record<br/>BatchProcessing]
    
    ProcessBatch[Process Cases in<br/>Mini-Batches]
    
    subgraph "VLM Processing"
        EncodeImg[Encode Image<br/>to Base64]
        CompressImg{Image > 10MB?}
        Compress[Compress Image]
        CallVLM[Call VLM API]
        ParseResp[Parse VLM Response]
    end
    
    CreateResult[Create Processing Result]
    SaveResult[Save to Database]
    
    UpdateProgress[Update Batch Progress]
    
    MoreCases{More Cases?}
    
    AutoLearn[Auto-Learning Engine:<br/>Learn from Results]
    
    subgraph "Auto-Learning"
        Pattern[Pattern Detection]
        Threshold[Threshold Optimization]
        Calibrate[Confidence Calibration]
        GenRec[Generate Recommendations]
    end
    
    Complete[Mark Batch Complete]
    
    End([Results Available])
    
    Start --> Upload
    Upload --> Validate
    Validate -->|Valid| CreateBatch
    Validate -->|Invalid| End
    
    CreateBatch --> BG
    BG --> Parse
    Parse --> ValidateImg
    ValidateImg --> CreateDB
    CreateDB --> ProcessBatch
    
    ProcessBatch --> EncodeImg
    EncodeImg --> CompressImg
    CompressImg -->|Yes| Compress
    CompressImg -->|No| CallVLM
    Compress --> CallVLM
    CallVLM --> ParseResp
    
    ParseResp --> CreateResult
    CreateResult --> SaveResult
    SaveResult --> UpdateProgress
    
    UpdateProgress --> MoreCases
    MoreCases -->|Yes| ProcessBatch
    MoreCases -->|No| AutoLearn
    
    AutoLearn --> Pattern
    Pattern --> Threshold
    Threshold --> Calibrate
    Calibrate --> GenRec
    
    GenRec --> Complete
    Complete --> End
    
    classDef api fill:#2196F3,stroke:#1976D2,color:#fff
    classDef process fill:#4CAF50,stroke:#388E3C,color:#fff
    classDef decision fill:#FF9800,stroke:#F57C00,color:#fff
    classDef external fill:#9C27B0,stroke:#7B1FA2,color:#fff
    
    class Upload,BG api
    class Parse,ValidateImg,ProcessBatch,EncodeImg,Compress,CreateResult,SaveResult,UpdateProgress,Pattern,Threshold,Calibrate,GenRec process
    class CompressImg,MoreCases decision
    class CallVLM,ParseResp external
```

## 3. Frontend Component Architecture

```mermaid
graph TD
    subgraph "Main Frontend"
        App1[App.tsx<br/>Router & Layout]
        
        subgraph "Pages"
            Landing[LandingPage]
            Upload[UploadPage]
            Processing[ProcessingPage]
            Dashboard[DashboardPage]
            Results[ResultsPage]
            ROI[ROIPage]
            Insights[InsightsPage]
        end
        
        subgraph "Components"
            FileUp[FileUpload]
            Progress[ProcessingProgress]
            MetricCards[MetricCard]
            Charts[ResultsChart]
        end
        
        subgraph "Services"
            API1[ApiClient]
            BatchSvc[BatchService]
            MetricsSvc[MetricsService]
        end
    end
    
    subgraph "Surveillance Frontend"
        App2[App.tsx<br/>State-based Navigation]
        
        subgraph "Surveillance Pages"
            SLanding[LandingPage]
            SUpload[UploadPage]
            SDashboard[DashboardPage]
            SAnalytics[AnalyticsPage]
        end
        
        subgraph "Surveillance Components"
            AlertFeed[AlertFeed]
            TerminalStatus[TerminalStatus]
            MetricsDisplay[MetricsDisplay]
        end
        
        API2[API Service<br/>+ WebSocket]
    end
    
    App1 --> Landing
    App1 --> Upload
    App1 --> Processing
    App1 --> Dashboard
    
    Upload --> FileUp
    Processing --> Progress
    Dashboard --> MetricCards
    Results --> Charts
    
    Landing --> BatchSvc
    Upload --> BatchSvc
    Processing --> BatchSvc
    Dashboard --> MetricsSvc
    
    BatchSvc --> API1
    MetricsSvc --> API1
    
    App2 --> SLanding
    App2 --> SUpload
    App2 --> SDashboard
    
    SDashboard --> AlertFeed
    SDashboard --> TerminalStatus
    SAnalytics --> MetricsDisplay
    
    AlertFeed --> API2
    TerminalStatus --> API2
    
    classDef app fill:#E91E63,stroke:#C2185B,color:#fff
    classDef page fill:#2196F3,stroke:#1976D2,color:#fff
    classDef component fill:#4CAF50,stroke:#388E3C,color:#fff
    classDef service fill:#FF9800,stroke:#F57C00,color:#fff
    
    class App1,App2 app
    class Landing,Upload,Processing,Dashboard,Results,ROI,Insights,SLanding,SUpload,SDashboard,SAnalytics page
    class FileUp,Progress,MetricCards,Charts,AlertFeed,TerminalStatus,MetricsDisplay component
    class API1,API2,BatchSvc,MetricsSvc service
```

## 4. Data Flow and State Management

```mermaid
stateDiagram-v2
    [*] --> UserUpload: User initiates upload
    
    UserUpload --> ValidationState: CSV + Images
    
    ValidationState --> ProcessingQueue: Valid data
    ValidationState --> ErrorState: Invalid data
    
    ProcessingQueue --> ActiveProcessing: Background task starts
    
    ActiveProcessing --> VLMAnalysis: Process mini-batch
    
    VLMAnalysis --> ResultsAccumulation: Store results
    
    ResultsAccumulation --> ProgressUpdate: Update progress
    
    ProgressUpdate --> ActiveProcessing: More cases
    ProgressUpdate --> AutoLearning: All cases done
    
    AutoLearning --> PatternAnalysis: Analyze patterns
    PatternAnalysis --> ThresholdOptimization: Optimize thresholds
    ThresholdOptimization --> RecommendationGeneration: Generate insights
    
    RecommendationGeneration --> CompletedState: Processing complete
    
    CompletedState --> ResultsDisplay: User views results
    
    ErrorState --> [*]: Error handling
    ResultsDisplay --> [*]: Process complete
    
    note right of ValidationState
        Validates:
        - CSV format
        - Required columns
        - Image paths exist
        - Case number format
    end note
    
    note right of VLMAnalysis
        For each image:
        - Encode to base64
        - Call VLM API
        - Parse response
        - Apply thresholds
    end note
    
    note right of AutoLearning
        Learns from:
        - VLM reasoning
        - Detection patterns
        - Confidence scores
        - Historical data
    end note
```

## 5. API Endpoint Flow

```mermaid
sequenceDiagram
    participant User
    participant Frontend
    participant API
    participant BatchProcessor
    participant VLMService
    participant Database
    participant BackgroundTasks
    
    User->>Frontend: Upload CSV + Images
    Frontend->>API: POST /api/v1/batch/upload
    
    API->>API: Validate files
    API->>API: Create batch_id
    API->>BackgroundTasks: Queue processing task
    API-->>Frontend: Return batch_id
    
    Frontend->>Frontend: Navigate to processing page
    
    BackgroundTasks->>BatchProcessor: process_batch_from_csv()
    
    BatchProcessor->>Database: Create batch record
    
    loop For each mini-batch
        BatchProcessor->>VLMService: analyze_batch()
        
        loop For each image
            VLMService->>VLMService: encode_image()
            VLMService->>VLMService: make_api_request()
            VLMService-->>BatchProcessor: VLM result
        end
        
        BatchProcessor->>Database: Save results
        BatchProcessor->>Database: Update progress
    end
    
    Frontend->>API: GET /api/v1/batch/{batch_id}
    API->>Database: Query batch status
    Database-->>API: Batch data
    API-->>Frontend: Status update
    
    BatchProcessor->>BatchProcessor: Calculate summary
    BatchProcessor->>Database: Mark complete
    
    Frontend->>API: GET /api/v1/batch/{batch_id}/results
    API->>Database: Query results
    Database-->>API: Processing results
    API-->>Frontend: Results data
    
    Frontend->>User: Display results
```

## 6. Function Call Hierarchy

```mermaid
graph TD
    subgraph "Entry Points"
        Main[main.py:lifespan]
        Upload[/api/v1/batch/upload]
        Status[/api/v1/batch/status]
        Health[/health]
    end
    
    subgraph "Core Processing Functions"
        ProcessBG[process_batch_background]
        ProcessCSV[process_batch_from_csv]
        ProcessMini[_process_mini_batch]
        AnalyzeBatch[analyze_batch]
        AnalyzeImg[analyze_image]
    end
    
    subgraph "Support Functions"
        ParseCSV[parse_csv_file]
        ValidatePaths[validate_image_paths]
        EncodeImg[_encode_image]
        MakeReq[_make_api_request]
        ParseResp[_parse_vlm_response]
        CreateResult[_create_processing_result]
    end
    
    subgraph "Database Operations"
        CreateBatch[create_batch_processing_record]
        RecordResult[record_case_result]
        UpdateStatus[update_batch_status]
        GetProgress[get_batch_progress]
    end
    
    subgraph "Auto-Learning"
        LearnResults[learn_from_processing_results]
        AnalyzePatterns[analyze_customer_patterns]
        OptimizeThresh[optimize_thresholds]
        CalibrateConf[calibrate_confidence_scores]
    end
    
    Main --> |startup| CreateTables[create_tables]
    Main --> |startup| StartTaskMgr[start_task_manager]
    
    Upload --> ProcessBG
    ProcessBG --> ProcessCSV
    
    ProcessCSV --> ParseCSV
    ProcessCSV --> ValidatePaths
    ProcessCSV --> CreateBatch
    ProcessCSV --> ProcessMini
    
    ProcessMini --> AnalyzeBatch
    AnalyzeBatch --> AnalyzeImg
    
    AnalyzeImg --> EncodeImg
    AnalyzeImg --> MakeReq
    AnalyzeImg --> ParseResp
    
    ProcessMini --> CreateResult
    ProcessMini --> RecordResult
    
    ProcessCSV --> UpdateStatus
    
    ProcessBG --> LearnResults
    LearnResults --> AnalyzePatterns
    LearnResults --> OptimizeThresh
    LearnResults --> CalibrateConf
    
    Status --> GetProgress
    Health --> HealthCheck[health_check]
    
    classDef entry fill:#E91E63,stroke:#C2185B,color:#fff
    classDef core fill:#2196F3,stroke:#1976D2,color:#fff
    classDef support fill:#4CAF50,stroke:#388E3C,color:#fff
    classDef db fill:#FF9800,stroke:#F57C00,color:#fff
    classDef learn fill:#9C27B0,stroke:#7B1FA2,color:#fff
    
    class Main,Upload,Status,Health entry
    class ProcessBG,ProcessCSV,ProcessMini,AnalyzeBatch,AnalyzeImg core
    class ParseCSV,ValidatePaths,EncodeImg,MakeReq,ParseResp,CreateResult support
    class CreateBatch,RecordResult,UpdateStatus,GetProgress db
    class LearnResults,AnalyzePatterns,OptimizeThresh,CalibrateConf learn
```

## 7. VLM Integration Flow

```mermaid
flowchart LR
    subgraph "Image Processing"
        Image[Image File]
        Load[Load with PIL]
        Check{Size > 10MB?}
        Compress[Compress Image]
        Encode[Base64 Encode]
    end
    
    subgraph "API Request"
        Prepare[Prepare Request]
        Headers[Add Auth Headers]
        Body[Create JSON Body]
        Send[HTTP POST]
    end
    
    subgraph "VLM API"
        VLM[OpenAI-Compatible<br/>Endpoint]
        Process[Process Image]
        Generate[Generate Response]
    end
    
    subgraph "Response Handling"
        Receive[Receive Response]
        Parse[Parse JSON]
        Extract[Extract Fields]
        Validate[Validate Response]
        Create[Create Result Object]
    end
    
    Image --> Load
    Load --> Check
    Check -->|Yes| Compress
    Check -->|No| Encode
    Compress --> Encode
    
    Encode --> Prepare
    Prepare --> Headers
    Headers --> Body
    Body --> Send
    
    Send --> VLM
    VLM --> Process
    Process --> Generate
    
    Generate --> Receive
    Receive --> Parse
    Parse --> Extract
    Extract --> Validate
    Validate --> Create
    
    classDef input fill:#2196F3,stroke:#1976D2,color:#fff
    classDef process fill:#4CAF50,stroke:#388E3C,color:#fff
    classDef external fill:#9C27B0,stroke:#7B1FA2,color:#fff
    classDef output fill:#FF9800,stroke:#F57C00,color:#fff
    
    class Image input
    class Load,Check,Compress,Encode,Prepare,Headers,Body,Send process
    class VLM,Process,Generate external
    class Receive,Parse,Extract,Validate,Create output
```

## 8. Error Handling and Recovery Flow

```mermaid
flowchart TD
    Operation[Any Operation]
    
    TryCatch{Try/Catch<br/>Block}
    
    Success[Continue Processing]
    
    Error[Exception Caught]
    
    ErrorType{Error Type?}
    
    VLMError[VLM API Error]
    DBError[Database Error]
    FileError[File Error]
    ValidationError[Validation Error]
    UnknownError[Unknown Error]
    
    RetryVLM{Retry<br/>Available?}
    RetryDB{Transaction<br/>Rollback?}
    
    LogError[Log Error Details]
    UpdateStatus[Update Batch Status]
    NotifyUser[Return Error Response]
    
    Retry[Retry Operation]
    Rollback[Rollback Transaction]
    
    Recovery[Recovery Complete]
    Failed[Mark as Failed]
    
    Operation --> TryCatch
    TryCatch -->|Success| Success
    TryCatch -->|Exception| Error
    
    Error --> ErrorType
    
    ErrorType --> VLMError
    ErrorType --> DBError
    ErrorType --> FileError
    ErrorType --> ValidationError
    ErrorType --> UnknownError
    
    VLMError --> RetryVLM
    RetryVLM -->|Yes| Retry
    RetryVLM -->|No| LogError
    
    DBError --> RetryDB
    RetryDB -->|Yes| Rollback
    RetryDB -->|No| LogError
    
    FileError --> LogError
    ValidationError --> LogError
    UnknownError --> LogError
    
    Retry --> TryCatch
    Rollback --> Recovery
    
    LogError --> UpdateStatus
    UpdateStatus --> NotifyUser
    NotifyUser --> Failed
    
    Recovery --> Success
    
    classDef normal fill:#2196F3,stroke:#1976D2,color:#fff
    classDef error fill:#F44336,stroke:#D32F2F,color:#fff
    classDef decision fill:#FF9800,stroke:#F57C00,color:#fff
    classDef recovery fill:#4CAF50,stroke:#388E3C,color:#fff
    
    class Operation,Success,Recovery normal
    class Error,VLMError,DBError,FileError,ValidationError,UnknownError,LogError,UpdateStatus,NotifyUser,Failed error
    class TryCatch,ErrorType,RetryVLM,RetryDB decision
    class Retry,Rollback recovery
```

## Summary

These diagrams comprehensively illustrate the AI-FARM system architecture showing:

1. **High-level architecture** with all major components and their relationships
2. **Backend processing flow** from upload to results
3. **Frontend component hierarchy** for both UI implementations
4. **Data flow and state transitions** throughout the system
5. **API interaction sequences** between components
6. **Function call hierarchies** showing exact execution paths
7. **VLM integration details** with the external API
8. **Error handling and recovery** mechanisms

The system follows a layered architecture with clear separation of concerns, robust error handling, and scalable batch processing capabilities suitable for handling large-scale safety violation image analysis.

## 9. Detailed Backend Service Interactions

```mermaid
graph TB
    subgraph "FastAPI Application"
        Lifespan[app.main.lifespan<br/>Startup/Shutdown]
        
        subgraph "API Routers"
            BatchAPI[batch_processing.py]
            StatusAPI[status.py]
            HealthAPI[health.py]
            MetricsAPI[metrics.py]
        end
        
        subgraph "Middleware"
            CORS[CORSMiddleware]
            GZip[GZipMiddleware]
            RequestTrack[Request Tracking]
        end
    end
    
    subgraph "Service Layer"
        subgraph "Batch Processor"
            BP[BatchProcessor]
            CSV[CSVProcessor]
            CaseNum[CaseNumberProcessor]
        end
        
        subgraph "VLM Service"
            VLM[VLMService]
            ImgEnc[Image Encoder]
            APIClient[HTTP Client]
        end
        
        subgraph "Auto Learning"
            ALE[AutoLearningEngine]
            PD[PatternDetector]
            TO[ThresholdOptimizer]
            CC[ConfidenceCalibrator]
        end
        
        TaskMgr[TaskManager]
    end
    
    subgraph "Core Layer"
        Config[Settings/Config]
        DBMgr[DatabaseManager]
        Logger[Logging]
    end
    
    subgraph "Data Access"
        DBOps[DatabaseOperations]
        Models[SQLAlchemy Models]
        Schemas[Pydantic Schemas]
    end
    
    Lifespan --> DBMgr
    Lifespan --> TaskMgr
    
    BatchAPI --> BP
    BatchAPI --> TaskMgr
    StatusAPI --> BP
    StatusAPI --> DBOps
    HealthAPI --> VLM
    HealthAPI --> DBMgr
    MetricsAPI --> DBOps
    
    BP --> CSV
    BP --> CaseNum
    BP --> VLM
    BP --> DBOps
    BP --> ALE
    
    VLM --> ImgEnc
    VLM --> APIClient
    
    ALE --> PD
    ALE --> TO
    ALE --> CC
    ALE --> DBOps
    
    DBOps --> Models
    DBOps --> DBMgr
    
    All --> Config
    All --> Logger
    
    classDef api fill:#2196F3,stroke:#1976D2,color:#fff
    classDef service fill:#4CAF50,stroke:#388E3C,color:#fff
    classDef core fill:#FF9800,stroke:#F57C00,color:#fff
    classDef data fill:#9C27B0,stroke:#7B1FA2,color:#fff
    
    class BatchAPI,StatusAPI,HealthAPI,MetricsAPI api
    class BP,CSV,CaseNum,VLM,ImgEnc,APIClient,ALE,PD,TO,CC,TaskMgr service
    class Config,DBMgr,Logger core
    class DBOps,Models,Schemas data
```

## 10. Detailed Auto-Learning Process Flow

```mermaid
flowchart TD
    Start[Processing Results Available]
    
    Load[Load Processing Results<br/>from Database]
    
    subgraph "Pattern Detection"
        ExtractPatterns[Extract Patterns from<br/>VLM Reasoning]
        
        GroupByType[Group by<br/>Detection Type]
        
        CalcFreq[Calculate Pattern<br/>Frequencies]
        
        FilterSignificant[Filter Significant<br/>Patterns > 5%]
        
        StorePatterns[Store Detected<br/>Patterns]
    end
    
    subgraph "Threshold Optimization"
        PrepData[Prepare Optimization<br/>Data]
        
        loop1[For Each Detection Type]
        
        TestThresholds[Test Threshold<br/>Values 1-100]
        
        CalcF1[Calculate F1 Score<br/>for Each Threshold]
        
        SelectBest[Select Optimal<br/>Threshold]
        
        CompareDefault[Compare with<br/>Default Threshold]
        
        StoreOptimal[Store Optimal<br/>Thresholds]
    end
    
    subgraph "Confidence Calibration"
        ExtractScores[Extract Confidence<br/>Scores]
        
        BinScores[Bin Scores into<br/>10 Buckets]
        
        CalcAccuracy[Calculate Accuracy<br/>per Bucket]
        
        CreateCurve[Create Calibration<br/>Curve]
        
        CalcECE[Calculate Expected<br/>Calibration Error]
        
        GenMapping[Generate Calibration<br/>Mapping]
    end
    
    subgraph "Recommendation Generation"
        AnalyzeMetrics[Analyze All<br/>Metrics]
        
        GenInsights[Generate Key<br/>Insights]
        
        PriorityRecs[Prioritize<br/>Recommendations]
        
        CreateReport[Create Learning<br/>Report]
    end
    
    SaveResults[Save Auto-Learning<br/>Results to DB]
    
    End[Learning Complete]
    
    Start --> Load
    Load --> ExtractPatterns
    
    ExtractPatterns --> GroupByType
    GroupByType --> CalcFreq
    CalcFreq --> FilterSignificant
    FilterSignificant --> StorePatterns
    
    StorePatterns --> PrepData
    PrepData --> loop1
    loop1 --> TestThresholds
    TestThresholds --> CalcF1
    CalcF1 --> SelectBest
    SelectBest --> CompareDefault
    CompareDefault --> StoreOptimal
    StoreOptimal --> loop1
    
    StoreOptimal --> ExtractScores
    ExtractScores --> BinScores
    BinScores --> CalcAccuracy
    CalcAccuracy --> CreateCurve
    CreateCurve --> CalcECE
    CalcECE --> GenMapping
    
    GenMapping --> AnalyzeMetrics
    AnalyzeMetrics --> GenInsights
    GenInsights --> PriorityRecs
    PriorityRecs --> CreateReport
    
    CreateReport --> SaveResults
    SaveResults --> End
    
    classDef process fill:#4CAF50,stroke:#388E3C,color:#fff
    classDef calc fill:#2196F3,stroke:#1976D2,color:#fff
    classDef store fill:#FF9800,stroke:#F57C00,color:#fff
    
    class ExtractPatterns,GroupByType,PrepData,TestThresholds,ExtractScores,BinScores,AnalyzeMetrics process
    class CalcFreq,CalcF1,CalcAccuracy,CalcECE calc
    class StorePatterns,StoreOptimal,SaveResults store
```

## 11. Database Schema and Relationships

```mermaid
erDiagram
    BatchProcessing ||--o{ CaseProcessingResult : contains
    CaseProcessingResult ||--|| VLMAnalysisResult : has
    CaseProcessingResult ||--o| ProcessedImage : may_have
    
    BatchProcessing {
        string batch_id PK
        datetime created_at
        string status
        int total_cases
        int processed_cases
        json processing_config
        string data_path
        json summary_metrics
        string error_message
        datetime completed_at
    }
    
    CaseProcessingResult {
        int id PK
        string batch_id FK
        string case_number
        int pk_event
        string image_path
        string validation_status
        datetime processed_at
        float processing_time_seconds
        string vlm_status
        string error_message
    }
    
    VLMAnalysisResult {
        int id PK
        int case_result_id FK
        string detection_type
        int false_positive_likelihood
        string reasoning
        string specific_issue
        string recommendation
        float confidence_score
        datetime created_at
        json raw_response
    }
    
    ProcessedImage {
        int id PK
        int case_result_id FK
        string original_path
        string processed_path
        int original_size_bytes
        int processed_size_bytes
        string processing_type
        datetime created_at
    }
    
    AutoLearningMetrics {
        int id PK
        string batch_id FK
        json detected_patterns
        json optimized_thresholds
        json calibration_data
        json recommendations
        float overall_confidence
        datetime created_at
    }
    
    SystemMetrics {
        int id PK
        datetime timestamp
        string metric_type
        float value
        json metadata
    }
```

## 12. Detailed Frontend User Flow

```mermaid
flowchart TD
    Start([User Opens App])
    
    Landing[Landing Page<br/>LandingPage.tsx]
    
    ClickDemo{Click<br/>'Start Live Demo'}
    
    Upload[Upload Page<br/>UploadPage.tsx]
    
    SelectFiles[Select CSV &<br/>Optional ZIP]
    
    FileValidation{Client-side<br/>Validation}
    
    UploadAPI[Call uploadAndProcess<br/>API]
    
    ShowProgress[Navigate to<br/>ProcessingPage]
    
    Processing[Processing Page<br/>Shows Progress]
    
    PollStatus[Poll Batch Status<br/>Every 2 seconds]
    
    CheckStatus{Status?}
    
    UpdateUI[Update Progress Bar<br/>& Statistics]
    
    Complete[Processing Complete]
    
    ShowResults[Navigate to<br/>ResultsPage]
    
    Results[Results Page<br/>Shows Analysis]
    
    ViewOptions{User Action}
    
    Dashboard[Dashboard Page<br/>Analytics View]
    
    ROI[ROI Calculator<br/>Cost Analysis]
    
    Insights[Insights Page<br/>AI Recommendations]
    
    Download[Download Results]
    
    NewBatch[Start New Batch]
    
    End([Session Complete])
    
    Start --> Landing
    Landing --> ClickDemo
    ClickDemo --> Upload
    
    Upload --> SelectFiles
    SelectFiles --> FileValidation
    
    FileValidation -->|Valid| UploadAPI
    FileValidation -->|Invalid| SelectFiles
    
    UploadAPI --> ShowProgress
    ShowProgress --> Processing
    
    Processing --> PollStatus
    PollStatus --> CheckStatus
    
    CheckStatus -->|Processing| UpdateUI
    UpdateUI --> PollStatus
    
    CheckStatus -->|Completed| Complete
    CheckStatus -->|Failed| ShowResults
    
    Complete --> ShowResults
    ShowResults --> Results
    
    Results --> ViewOptions
    
    ViewOptions -->|View Dashboard| Dashboard
    ViewOptions -->|Calculate ROI| ROI
    ViewOptions -->|View Insights| Insights
    ViewOptions -->|Download| Download
    ViewOptions -->|New Batch| NewBatch
    
    Dashboard --> ViewOptions
    ROI --> ViewOptions
    Insights --> ViewOptions
    Download --> End
    NewBatch --> Upload
    
    classDef page fill:#2196F3,stroke:#1976D2,color:#fff
    classDef action fill:#4CAF50,stroke:#388E3C,color:#fff
    classDef decision fill:#FF9800,stroke:#F57C00,color:#fff
    classDef api fill:#9C27B0,stroke:#7B1FA2,color:#fff
    
    class Landing,Upload,Processing,Results,Dashboard,ROI,Insights page
    class SelectFiles,UpdateUI,Complete,Download,NewBatch action
    class ClickDemo,FileValidation,CheckStatus,ViewOptions decision
    class UploadAPI,PollStatus api
```

## 13. Frontend Component State Flow

```mermaid
stateDiagram-v2
    [*] --> AppInitialized: App.tsx loaded
    
    AppInitialized --> LandingPage: Router renders
    
    state LandingPage {
        [*] --> ShowHero
        ShowHero --> ShowFeatures
        ShowFeatures --> ShowCTA
    }
    
    LandingPage --> UploadPage: Navigate
    
    state UploadPage {
        [*] --> NoFiles
        NoFiles --> FilesSelected: onFileSelect
        FilesSelected --> Validating: Validate
        Validating --> ReadyToUpload: Valid
        Validating --> ShowError: Invalid
        ShowError --> FilesSelected: Fix & Retry
        ReadyToUpload --> Uploading: Submit
        Uploading --> UploadComplete: Success
        Uploading --> UploadError: Failure
    }
    
    UploadPage --> ProcessingPage: After upload
    
    state ProcessingPage {
        [*] --> LoadingInitial
        LoadingInitial --> Polling: Start polling
        Polling --> UpdateProgress: New status
        UpdateProgress --> Polling: Continue
        Polling --> ProcessingComplete: Status = COMPLETED
        Polling --> ProcessingFailed: Status = FAILED
    }
    
    ProcessingPage --> ResultsPage: Complete/Failed
    
    state ResultsPage {
        [*] --> LoadingResults
        LoadingResults --> ShowingSummary: Data loaded
        ShowingSummary --> ShowingDetails: Tab change
        ShowingDetails --> ShowingCharts: Tab change
        ShowingCharts --> ShowingSummary: Tab change
    }
    
    state DashboardPage {
        [*] --> LoadingMetrics
        LoadingMetrics --> DisplayingMetrics
        DisplayingMetrics --> InteractiveMode: User interaction
        InteractiveMode --> DisplayingMetrics: Update view
    }
    
    state ROIPage {
        [*] --> DefaultCalculation
        DefaultCalculation --> CustomCalculation: Edit params
        CustomCalculation --> UpdatedResults: Recalculate
    }
```

## 14. API Client Service Architecture

```mermaid
graph TB
    subgraph "Frontend Services"
        subgraph "API Client Core"
            AC[ApiClient<br/>api-client.ts]
            Interceptors[Request/Response<br/>Interceptors]
            ErrorHandler[Error Handler]
            RetryLogic[Retry with<br/>Exponential Backoff]
        end
        
        subgraph "Service Classes"
            BatchSvc[BatchService]
            MetricsSvc[MetricsService]
            HealthSvc[HealthService]
        end
        
        subgraph "Utilities"
            FormData[Form Data Builder]
            Pagination[Pagination Handler]
            Polling[Status Polling]
        end
    end
    
    subgraph "API Endpoints"
        Upload[POST /upload]
        Status[GET /status]
        Results[GET /results]
        Metrics[GET /metrics]
        Health[GET /health]
    end
    
    subgraph "Components Using Services"
        UploadPage[UploadPage.tsx]
        ProcessingPage[ProcessingPage.tsx]
        ResultsPage[ResultsPage.tsx]
        DashboardPage[DashboardPage.tsx]
    end
    
    AC --> Interceptors
    Interceptors --> ErrorHandler
    ErrorHandler --> RetryLogic
    
    BatchSvc --> AC
    MetricsSvc --> AC
    HealthSvc --> AC
    
    BatchSvc --> FormData
    BatchSvc --> Polling
    MetricsSvc --> Pagination
    
    UploadPage --> BatchSvc
    ProcessingPage --> BatchSvc
    ResultsPage --> BatchSvc
    ResultsPage --> MetricsSvc
    DashboardPage --> MetricsSvc
    DashboardPage --> HealthSvc
    
    BatchSvc --> Upload
    BatchSvc --> Status
    BatchSvc --> Results
    MetricsSvc --> Metrics
    HealthSvc --> Health
    
    classDef core fill:#2196F3,stroke:#1976D2,color:#fff
    classDef service fill:#4CAF50,stroke:#388E3C,color:#fff
    classDef util fill:#FF9800,stroke:#F57C00,color:#fff
    classDef component fill:#E91E63,stroke:#C2185B,color:#fff
    classDef api fill:#9C27B0,stroke:#7B1FA2,color:#fff
    
    class AC,Interceptors,ErrorHandler,RetryLogic core
    class BatchSvc,MetricsSvc,HealthSvc service
    class FormData,Pagination,Polling util
    class UploadPage,ProcessingPage,ResultsPage,DashboardPage component
    class Upload,Status,Results,Metrics,Health api
```

## 15. Complete Request-Response Lifecycle

```mermaid
sequenceDiagram
    participant User
    participant Component
    participant Service
    participant ApiClient
    participant Interceptor
    participant Backend
    participant Database
    
    User->>Component: Interact (e.g., upload)
    Component->>Service: Call service method
    
    Service->>Service: Prepare request data
    Service->>ApiClient: Make API call
    
    ApiClient->>Interceptor: Request interceptor
    Interceptor->>Interceptor: Add headers
    Interceptor->>Interceptor: Add request ID
    
    Interceptor->>Backend: HTTP Request
    
    Backend->>Backend: Validate request
    Backend->>Backend: Process business logic
    Backend->>Database: Query/Update data
    Database-->>Backend: Data result
    Backend->>Backend: Format response
    
    Backend-->>Interceptor: HTTP Response
    
    Interceptor->>Interceptor: Response interceptor
    
    alt Success Response
        Interceptor->>ApiClient: Return data
        ApiClient->>Service: Return typed data
        Service->>Component: Update state
        Component->>User: Show success UI
    else Error Response
        Interceptor->>Interceptor: Check retry-able
        alt Can Retry
            Interceptor->>Backend: Retry request
            Backend-->>Interceptor: Response
        else Cannot Retry
            Interceptor->>ErrorHandler: Handle error
            ErrorHandler->>ApiClient: Throw ApiError
            ApiClient->>Service: Propagate error
            Service->>Component: Handle error
            Component->>User: Show error UI
        end
    end
    
    Note over Component,Service: State updates trigger re-renders
    Note over Backend,Database: Async processing for large batches
```