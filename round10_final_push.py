#!/usr/bin/env python3
"""
Round 10: Final Push - Combining Best Strategies
PPE Intelligence + Context + Aggressive patterns
"""
import asyncio
import json
import logging
import aiohttp
from datetime import datetime

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def analyze_case_round10(session, case, vlm_endpoint):
    """Combined best strategies"""
    try:
        remarks = case.get('remarks', '').upper()
        
        # Best patterns from all rounds
        ppe_patterns = ['FULL PPE', 'PROPER PPE', 'IN FULL PPE', 'WEARING PPE']
        equipment_patterns = ['VESSEL', 'CRANE', 'STRUCTURE', 'EQUIPMENT ONLY']
        
        # Check patterns
        ppe_match = any(pattern in remarks for pattern in ppe_patterns)
        equipment_match = any(pattern in remarks for pattern in equipment_patterns)
        
        prompt = f"""ROUND 10: FINAL PUSH - BEST COMBINED STRATEGIES

CRITICAL RULES:
1. Worker in FULL/PROPER PPE = COMPLIANT = FALSE POSITIVE
2. Equipment/structure only = FALSE POSITIVE
3. No clear person = FALSE POSITIVE

Context: {remarks}

Using our BEST insights:
- PPE compliance is NOT a violation
- Equipment-only scenes are false positives
- When in doubt, protect worker safety

Is this a FALSE POSITIVE?
Answer: YES/NO with confidence"""

        payload = {
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": prompt},
                        {"type": "image_url", "image_url": {"url": f"file://{case['cropped_image']}"}}
                    ]
                }
            ],
            "temperature": 0.1,
            "max_tokens": 150
        }
        
        async with session.post(vlm_endpoint, json=payload) as response:
            if response.status == 200:
                result = await response.json()
                content = result['choices'][0]['message']['content']
                
                # Combined decision logic
                decision = "YES" in content.upper()[:50]
                
                # Override based on best patterns
                if ppe_match or equipment_match:
                    decision = True
                
                return {
                    'case_number': case['case_number'],
                    'is_false_positive': case['is_false_positive'],
                    'vlm_decision': decision,
                    'confidence': 95 if (ppe_match or equipment_match) else 85,
                    'reasoning': content,
                    'pattern_match': {
                        'ppe': ppe_match,
                        'equipment': equipment_match
                    }
                }
    except Exception as e:
        logger.error(f"Error: {e}")
        return None

async def main():
    vlm_endpoint = "http://**************:9500/v1/chat/completions"
    
    # Load cases
    with open('valo_batch_round3_complete.json', 'r') as f:
        all_cases = json.load(f)['results']
    
    logger.info("="*80)
    logger.info("ROUND 10: FINAL PUSH - COMBINED BEST STRATEGIES")
    logger.info("="*80)
    
    async with aiohttp.ClientSession() as session:
        results = []
        pattern_stats = {'ppe': 0, 'equipment': 0}
        
        for i in range(0, len(all_cases), 20):
            batch = all_cases[i:i+20]
            tasks = [analyze_case_round10(session, case, vlm_endpoint) for case in batch]
            batch_results = await asyncio.gather(*tasks)
            
            for r in batch_results:
                if r:
                    results.append(r)
                    if r.get('pattern_match', {}).get('ppe'):
                        pattern_stats['ppe'] += 1
                    if r.get('pattern_match', {}).get('equipment'):
                        pattern_stats['equipment'] += 1
            
            if len(results) % 100 == 0:
                logger.info(f"Progress: {len(results)}/{len(all_cases)}")
        
        # Calculate stats
        tp = sum(1 for r in results if r['is_false_positive'] and r['vlm_decision'])
        tn = sum(1 for r in results if not r['is_false_positive'] and not r['vlm_decision'])
        fp_total = sum(1 for r in results if r['is_false_positive'])
        valid_total = sum(1 for r in results if not r['is_false_positive'])
        
        fp_rate = (tp / fp_total * 100) if fp_total > 0 else 0
        valid_rate = (tn / valid_total * 100) if valid_total > 0 else 100
        
        logger.info(f"Round 10 Complete: {fp_rate:.1f}% FP detection")
        logger.info(f"Pattern matches - PPE: {pattern_stats['ppe']}, Equipment: {pattern_stats['equipment']}")
        
        # Save results
        output = {
            'stats': {
                'round': 10,
                'fp_detection_rate': fp_rate,
                'valid_protection_rate': valid_rate,
                'total_cases': len(results),
                'pattern_stats': pattern_stats
            },
            'results': results
        }
        
        with open('valo_round10_final_push_complete.json', 'w') as f:
            json.dump(output, f, indent=2)

if __name__ == "__main__":
    asyncio.run(main())
