#!/usr/bin/env python3
"""
Create enhanced prompt with detailed structure descriptions
Based on VLM analysis of actual false positive structures
"""

def create_enhanced_prompt_with_structures():
    """Create the enhanced prompt with visual structure descriptions"""
    
    prompt = """INTELLIGENT SAFETY ANALYSIS WITH ADVANCED STRUCTURE RECOGNITION

CRITICAL: Analyze BOTH images provided:
- SOURCE IMAGE: Full context view showing work environment
- CROPPED IMAGE: Zoomed area that should contain a PERSON for valid violation

⚠️ IMPORTANT: 34.4% of false alerts are structures misidentified as people

STEP 1 - ADVANCED ENTITY DETECTION:
Carefully examine the CROPPED image to determine if it shows:

A) PERSON - Human characteristics:
   ✓ Organic, curved body shape with distinguishable head, torso, limbs
   ✓ Visible clothing/PPE (vests, helmets, coveralls) on human form
   ✓ Natural human proportions and posture
   ✓ Soft contours and varied textures of skin/fabric
   ✓ Human scale relative to surroundings

B) STRUCTURE - Industrial equipment characteristics:

   🏗️ CRANE STRUCTURE:
   - Large geometric shapes: rectangles, circles, angular frameworks
   - Metallic/reflective surfaces with industrial sheen
   - Sharp right angles and straight rigid lines
   - Repetitive patterns: beams, slats, structural supports
   - Industrial colors: gray, yellow safety markings
   - Massive scale beyond human proportions
   - Circular/gear-like components, wheels

   🚢 VESSEL STRUCTURE:
   - Ship hull components, railings, deck structures
   - Painted metal surfaces (often white, gray, red)
   - Nautical features: portholes, cleats, bollards
   - Horizontal lines and maritime equipment
   - Large scale industrial marine elements

   🔧 PM (PREVENTIVE MAINTENANCE) STRUCTURE:
   - Boxy mechanical equipment with wheels
   - Vehicle-like components with industrial labels/numbers
   - Rectangular shapes with utilitarian design
   - Yellow/gray industrial colors
   - Mechanical components: hydraulics, controls

   📦 SPREADER STRUCTURE:
   - Container handling equipment
   - Rectangular frame with corner guides
   - Mechanical gripping mechanisms
   - Industrial yellow color typical
   - Grid-like patterns and lifting components

C) UNCLEAR - Cannot determine with >80% confidence

VISUAL DIFFERENTIATION KEYS:
• Humans have ORGANIC shapes vs structures have GEOMETRIC patterns
• Humans show FABRIC/SKIN textures vs structures show METAL/PAINT
• Humans have IRREGULAR forms vs structures have REPETITIVE patterns
• Humans are PERSON-SIZED vs structures are INDUSTRIAL-SCALE

⚡ DECISION RULE: If <80% confident it's a PERSON → FALSE POSITIVE

STEP 2 - PPE COMPLIANCE CHECK (only if PERSON with >80% confidence):
Check for proper PPE. COMPLIANT combinations include:
✓ High-visibility vest (orange/yellow/green) + Hard hat (any color)
✓ Coveralls (yellow/white/orange) + Safety gear
✓ Life vest/jacket + Hard hat
✓ Full PPE with harness/lanyard

PPE COMPLIANT = FALSE POSITIVE

STEP 3 - CONTEXT VALIDATION:
From SOURCE image, evaluate:
- Location: At wharf/safe zone? → Likely FALSE POSITIVE
- Multiple workers present? → Often FALSE POSITIVE (safer)
- Just standing/walking? → Likely FALSE POSITIVE

DECISION MATRIX:
┌─────────────────────┬────────────┬──────────────┐
│ Cropped Content     │ PPE Status │ Result       │
├─────────────────────┼────────────┼──────────────┤
│ Structure           │ N/A        │ FALSE POS    │
│ Person              │ Compliant  │ FALSE POS    │
│ Person              │ No Clear   │ FALSE POS    │
│ Person              │ Violation  │ VALID        │
│ Unclear             │ N/A        │ FALSE POS    │
└─────────────────────┴────────────┴──────────────┘

OUTPUT FORMAT:
FALSE POSITIVE: [YES/NO]
Entity Type: [PERSON/CRANE/VESSEL/PM/SPREADER/UNCLEAR]
If PERSON - PPE Status: [COMPLIANT/NON-COMPLIANT/UNCLEAR]
Confidence: [0-100]%
Key Observation: [One line - what you see and why decision]"""

    return prompt

def main():
    # Create the enhanced prompt
    enhanced_prompt = create_enhanced_prompt_with_structures()
    
    # Save to file
    with open('intelligent_prompt_enhanced_structures.txt', 'w') as f:
        f.write(enhanced_prompt)
    
    # Also create a JSON version with structure patterns
    structure_patterns = {
        "crane_structure": {
            "visual_cues": [
                "Large geometric shapes: rectangles, circles",
                "Metallic/reflective surfaces", 
                "Sharp right angles and rigid lines",
                "Repetitive beams and supports",
                "Industrial gray/yellow colors",
                "Circular gear-like components"
            ],
            "common_remarks": [
                "CRANE STRUCTURE CAPTURED AS LS",
                "Crane structure captured as LS"
            ]
        },
        "vessel_structure": {
            "visual_cues": [
                "Ship hull components and railings",
                "Painted metal (white, gray, red)",
                "Nautical features: portholes, cleats",
                "Horizontal maritime lines",
                "Large scale marine elements"
            ],
            "common_remarks": [
                "VESSEL STRUCTURE CAPTURED AS LS",
                "Vessel structure captured as LS"
            ]
        },
        "pm_structure": {
            "visual_cues": [
                "Boxy mechanical equipment",
                "Vehicle components with wheels",
                "Industrial labels and numbers",
                "Yellow/gray utilitarian design",
                "Hydraulic components"
            ],
            "common_remarks": [
                "PM STRUCTURE CAPTURED AS LS",
                "PM structure captured as LS"
            ]
        },
        "spreader_structure": {
            "visual_cues": [
                "Container handling frame",
                "Corner guides and grippers",
                "Mechanical lifting mechanisms",
                "Industrial yellow color",
                "Grid-like patterns"
            ],
            "common_remarks": [
                "Spreader structure captured as LS",
                "Spreader flipper captured as LS"
            ]
        }
    }
    
    import json
    with open('structure_visual_patterns.json', 'w') as f:
        json.dump(structure_patterns, f, indent=2)
    
    print("ENHANCED PROMPT CREATED WITH STRUCTURE DESCRIPTIONS")
    print("="*60)
    print("\nFiles created:")
    print("1. intelligent_prompt_enhanced_structures.txt - Full enhanced prompt")
    print("2. structure_visual_patterns.json - Structure pattern reference")
    
    print("\n\nKey improvements:")
    print("- Detailed visual descriptions of each structure type")
    print("- Specific differentiation between human and structure features")
    print("- 80% confidence threshold for person detection")
    print("- Visual cues based on actual false positive analysis")
    
    print("\n\nStructure types covered:")
    for struct_type in structure_patterns:
        print(f"- {struct_type.replace('_', ' ').title()}")

if __name__ == "__main__":
    main()