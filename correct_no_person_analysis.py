#!/usr/bin/env python3
"""
Correctly analyze how many false positives can be filtered by "no person = FP" rule
"""

import json
import re
from pathlib import Path
from datetime import datetime

class CorrectNoPersonAnalyzer:
    def __init__(self):
        self.results = {
            'false_positives': {
                'total': 0,
                'no_person': 0,
                'with_person': 0,
                'no_person_cases': []
            },
            'true_positives': {
                'total': 0,
                'no_person': 0,
                'with_person': 0,
                'no_person_cases': []
            }
        }
        
    def extract_person_status(self, case_content):
        """Extract person present status from confidence section"""
        # Look for confidence section
        conf_start = case_content.find('### Confidence Analysis')
        if conf_start == -1:
            return None
            
        conf_section = case_content[conf_start:conf_start+1000]
        
        # Look for Person Present in confidence section (most reliable)
        if re.search(r'\*\*Person Present\*\*:\s*YES', conf_section):
            return True
        elif re.search(r'\*\*Person Present\*\*:\s*NO', conf_section):
            return False
            
        # Fallback to other indicators
        case_lower = case_content.lower()
        
        # Strong NO indicators
        no_indicators = [
            'no person visible',
            'no individuals are visible',
            'none visible',
            'no one is visible',
            'nobody visible'
        ]
        
        # Strong YES indicators  
        yes_indicators = [
            'one person',
            'two people',
            'individuals are visible',
            'worker visible',
            'person is standing',
            'person is walking'
        ]
        
        for indicator in no_indicators:
            if indicator in case_lower:
                return False
                
        for indicator in yes_indicators:
            if indicator in case_lower:
                return True
                
        return None
    
    def analyze_files(self):
        """Analyze both FP and TP files"""
        # Analyze FALSE POSITIVES
        fp_file = Path('valo_comprehensive_data/false_positives/false_positive_analysis_20250725_232934.md')
        if fp_file.exists():
            with open(fp_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
            cases = re.split(r'\n## Case: ', content)
            print(f"Analyzing {len(cases)-1} FALSE POSITIVE cases...")
            
            for case in cases[1:]:  # Skip header
                case_num = case.split('\n')[0].strip()
                person_present = self.extract_person_status(case)
                
                if person_present is None:
                    continue  # Skip ambiguous cases
                    
                self.results['false_positives']['total'] += 1
                
                if person_present:
                    self.results['false_positives']['with_person'] += 1
                else:
                    self.results['false_positives']['no_person'] += 1
                    self.results['false_positives']['no_person_cases'].append(case_num)
                    
        # Analyze TRUE POSITIVES
        tp_file = Path('valo_comprehensive_data/true_positives/true_positive_analysis_20250725_232934.md')
        if tp_file.exists():
            with open(tp_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
            cases = re.split(r'\n## Case: ', content)
            print(f"Analyzing {len(cases)-1} TRUE POSITIVE cases...")
            
            for case in cases[1:]:  # Skip header
                case_num = case.split('\n')[0].strip()
                person_present = self.extract_person_status(case)
                
                if person_present is None:
                    continue
                    
                self.results['true_positives']['total'] += 1
                
                if person_present:
                    self.results['true_positives']['with_person'] += 1
                else:
                    self.results['true_positives']['no_person'] += 1
                    self.results['true_positives']['no_person_cases'].append(case_num)
    
    def display_results(self):
        """Display analysis results"""
        fp = self.results['false_positives']
        tp = self.results['true_positives']
        
        print(f"\n{'='*60}")
        print("📊 CORRECT No-Person Filtering Analysis")
        print(f"{'='*60}")
        
        print(f"\n🔴 FALSE POSITIVES (Invalid alerts):")
        print(f"  - Total analyzed: {fp['total']}")
        print(f"  - NO person: {fp['no_person']} ({fp['no_person']/fp['total']*100:.1f}%)")
        print(f"  - With person: {fp['with_person']} ({fp['with_person']/fp['total']*100:.1f}%)")
        
        print(f"\n🟢 TRUE POSITIVES (Valid violations):")
        print(f"  - Total analyzed: {tp['total']}")
        print(f"  - NO person: {tp['no_person']} ({tp['no_person']/tp['total']*100:.1f}%)")
        print(f"  - With person: {tp['with_person']} ({tp['with_person']/tp['total']*100:.1f}%)")
        
        print(f"\n📈 FILTERING EFFECTIVENESS:")
        print(f"  ✅ Can filter: {fp['no_person']} false positives")
        print(f"  ⚠️  Would lose: {tp['no_person']} valid violations")
        print(f"  📊 Net benefit: {fp['no_person'] - tp['no_person']} cases")
        
        if tp['no_person'] > 0:
            print(f"\n⚠️  WARNING: {tp['no_person']} valid violations have no person!")
            print(f"  Sample cases: {tp['no_person_cases'][:5]}")
            print(f"  These might be:")
            print(f"  - Equipment/structural violations")
            print(f"  - Cases where person left the frame")
            print(f"  - Poor image quality missing the person")
        
        # Calculate improvement
        total_cases = fp['total'] + tp['total']
        current_fp_rate = fp['total'] / total_cases * 100
        
        # After filtering no-person cases
        remaining_fp = fp['with_person']
        remaining_tp = tp['with_person']  
        remaining_total = remaining_fp + remaining_tp
        new_fp_rate = remaining_fp / remaining_total * 100 if remaining_total > 0 else 0
        
        print(f"\n🎯 ACCURACY IMPROVEMENT:")
        print(f"  - Current FP rate: {current_fp_rate:.1f}%")
        print(f"  - After filtering: {new_fp_rate:.1f}%")
        print(f"  - Valid case retention: {tp['with_person']/tp['total']*100:.1f}%")
        
        # Save results
        self.save_results()
        
    def save_results(self):
        """Save detailed results"""
        summary = {
            'timestamp': datetime.now().isoformat(),
            'false_positives': self.results['false_positives'],
            'true_positives': self.results['true_positives'],
            'filtering_impact': {
                'fps_filtered': self.results['false_positives']['no_person'],
                'tps_lost': self.results['true_positives']['no_person'],
                'net_benefit': self.results['false_positives']['no_person'] - self.results['true_positives']['no_person'],
                'fp_reduction_rate': self.results['false_positives']['no_person'] / self.results['false_positives']['total'] * 100,
                'tp_retention_rate': self.results['true_positives']['with_person'] / self.results['true_positives']['total'] * 100
            }
        }
        
        with open('correct_no_person_analysis.json', 'w') as f:
            json.dump(summary, f, indent=2)
            
        print(f"\n📁 Results saved to: correct_no_person_analysis.json")

if __name__ == "__main__":
    analyzer = CorrectNoPersonAnalyzer()
    analyzer.analyze_files()
    analyzer.display_results()