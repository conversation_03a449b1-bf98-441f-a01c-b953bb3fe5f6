#!/usr/bin/env python3
"""
Intelligent Learning System for VALO AI-FARM
Analyzes VLM reasoning to improve prompts, not just confidence scores
"""

import json
import asyncio
import logging
from datetime import datetime
from typing import Dict, List, Tuple
import pandas as pd
from collections import defaultdict
import sys
import os
import re

sys.path.append('/home/<USER>/VALO_AI-FARM_2025/backend')
from app.services.valo_batch_processor import VALOBatchProcessor

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class IntelligentLearningProcessor(VALOBatchProcessor):
    def __init__(self):
        super().__init__()
        self.chunk_size = 10
        self.delay_between_chunks = 1
        
        # Learning insights from reasoning analysis
        self.reasoning_patterns = {
            'false_positive_indicators': defaultdict(int),
            'valid_violation_indicators': defaultdict(int),
            'common_misinterpretations': defaultdict(list),
            'successful_detections': defaultdict(list),
            'equipment_keywords': set(),
            'person_indicators': set(),
            'context_clues': defaultdict(list)
        }
        
        # Prompt evolution history
        self.prompt_evolution = []
        
    def analyze_reasoning(self, result: Dict) -> Dict:
        """Deep analysis of VLM reasoning to extract learning insights"""
        reasoning = result.get('reasoning', '').lower()
        is_correct = result.get('correct_prediction', False)
        is_fp = result.get('is_false_positive', True)
        person_detected = result.get('person_detected', 'unclear')
        
        insights = {
            'keywords_found': [],
            'detection_patterns': [],
            'improvement_suggestions': []
        }
        
        # Extract key phrases and patterns
        if is_correct:
            # Successful detection - learn what worked
            if is_fp and result.get('is_false_positive_predicted', False):
                # Correctly identified false positive
                if 'equipment' in reasoning:
                    insights['keywords_found'].append('equipment')
                    self.reasoning_patterns['equipment_keywords'].update(
                        re.findall(r'\b(crane|spreader|container|structure|vessel|machinery)\b', reasoning)
                    )
                if 'no person' in reasoning or 'no human' in reasoning:
                    insights['detection_patterns'].append('no_person_visible')
                    self.reasoning_patterns['false_positive_indicators']['no_person_visible'] += 1
                    
            elif not is_fp and not result.get('is_false_positive_predicted', False):
                # Correctly identified valid violation
                if 'person' in reasoning and 'visible' in reasoning:
                    insights['detection_patterns'].append('person_clearly_visible')
                    self.reasoning_patterns['valid_violation_indicators']['person_visible'] += 1
                    
        else:
            # Incorrect prediction - learn from mistakes
            if is_fp and not result.get('is_false_positive_predicted', False):
                # Failed to detect false positive (too conservative)
                insights['improvement_suggestions'].append('be_more_confident_with_equipment')
                
                # Extract why it was conservative
                if 'unclear' in reasoning or 'difficult' in reasoning:
                    self.reasoning_patterns['common_misinterpretations']['unclear_view'].append({
                        'case': result['case_number'],
                        'actual': 'equipment/structure',
                        'reasoning_snippet': reasoning[:200]
                    })
                elif 'ppe' in reasoning and 'full' in reasoning:
                    self.reasoning_patterns['common_misinterpretations']['ppe_compliant'].append({
                        'case': result['case_number'],
                        'actual': 'worker_in_full_ppe',
                        'reasoning_snippet': reasoning[:200]
                    })
                    
            elif not is_fp and result.get('is_false_positive_predicted', False):
                # Incorrectly dismissed valid violation (critical error)
                insights['improvement_suggestions'].append('never_dismiss_when_person_visible')
                self.reasoning_patterns['common_misinterpretations']['missed_violation'].append({
                    'case': result['case_number'],
                    'critical': True,
                    'reasoning_snippet': reasoning[:200]
                })
        
        # Extract context clues
        if result.get('terminal'):
            terminal = result['terminal']
            if terminal not in self.reasoning_patterns['context_clues']:
                self.reasoning_patterns['context_clues'][terminal] = []
            self.reasoning_patterns['context_clues'][terminal].append({
                'infringement_type': result.get('infringement_type'),
                'correct': is_correct,
                'person_detected': person_detected
            })
        
        return insights
    
    def generate_intelligent_prompt(self, case: Dict, round_num: int) -> str:
        """Generate progressively smarter prompts based on reasoning analysis"""
        
        # Base threshold adjusted by round and learning
        base_threshold = self.learning_params['confidence_thresholds'].get(
            case['infringement_type'], 90
        )
        
        # Build learned equipment keywords
        equipment_keywords = ', '.join(list(self.reasoning_patterns['equipment_keywords'])[:10])
        
        # Calculate terminal-specific insights
        terminal_patterns = self.reasoning_patterns['context_clues'].get(case['terminal'], [])
        terminal_accuracy = sum(1 for p in terminal_patterns if p['correct']) / len(terminal_patterns) * 100 if terminal_patterns else 0
        
        prompt = f"""🧠 INTELLIGENT VALO ANALYSIS - ROUND {round_num}

LEARNING INSIGHTS FROM {len(self.all_results) if hasattr(self, 'all_results') else 0} ANALYSES:
"""

        # Add specific learning from reasoning patterns
        if round_num > 1 and self.reasoning_patterns['false_positive_indicators']:
            top_fp_indicators = sorted(
                self.reasoning_patterns['false_positive_indicators'].items(),
                key=lambda x: x[1],
                reverse=True
            )[:5]
            
            prompt += f"""
📊 PROVEN FALSE POSITIVE INDICATORS:
"""
            for indicator, count in top_fp_indicators:
                prompt += f"   • {indicator.replace('_', ' ').title()}: {count} successful detections\n"
        
        # Add equipment detection patterns
        if equipment_keywords:
            prompt += f"""
🏗️ EQUIPMENT KEYWORDS LEARNED: {equipment_keywords}
   → When these are clearly visible with NO person = FALSE POSITIVE (high confidence)
"""

        # Add common misinterpretation warnings
        if self.reasoning_patterns['common_misinterpretations']:
            prompt += f"""
⚠️ COMMON MISTAKES TO AVOID:
"""
            for mistake_type, examples in self.reasoning_patterns['common_misinterpretations'].items():
                if examples and len(examples) > 0:
                    prompt += f"   • {mistake_type.replace('_', ' ').title()}: {len(examples)} cases\n"
                    if mistake_type == 'ppe_compliant' and len(examples) > 5:
                        prompt += f"      → Workers in FULL PPE (helmet + vest) are COMPLIANT = FALSE POSITIVE\n"
                    elif mistake_type == 'unclear_view' and len(examples) > 10:
                        prompt += f"      → If equipment shape is visible despite unclear view = FALSE POSITIVE\n"

        # Add specific case context
        prompt += f"""

CURRENT CASE ANALYSIS:
- Case: {case['case_number']}
- Terminal: {case['terminal']} (Historical accuracy: {terminal_accuracy:.1f}%)
- Camera: {case['camera_id']}
- Violation Type: {case['infringement_type']}
- Human Assessment: {case['alert_status']}
- Human Notes: {case.get('remarks', 'None')}
"""

        # Add infringement-specific learned patterns
        inf_type = case['infringement_type']
        if inf_type == 'PPE Non-compliance':
            ppe_mistakes = self.reasoning_patterns['common_misinterpretations'].get('ppe_compliant', [])
            if len(ppe_mistakes) > 20:
                prompt += f"""
✅ PPE COMPLIANCE LEARNING ({len(ppe_mistakes)} cases analyzed):
   - Workers wearing helmet + vest = COMPLIANT = FALSE POSITIVE
   - Don't be overly strict on PPE when basics are covered
   - Focus on MISSING critical PPE, not minor issues
"""
        elif inf_type == 'One man Lashing':
            prompt += f"""
🔗 LASHING OPERATION ANALYSIS:
   - First confirm: Is there a person performing lashing?
   - If just equipment/containers visible = FALSE POSITIVE
   - Ship crew in background often misidentified
"""
        elif inf_type == '2-Container Distance':
            prompt += f"""
📦 CONTAINER DISTANCE ANALYSIS:
   - Spreader/crane parts often mistaken for violations
   - If no person operating = likely FALSE POSITIVE
   - Focus on actual unsafe positioning, not equipment
"""

        # Add confidence calibration based on round
        if round_num > 2:
            missed_fps = len([r for r in self.reasoning_patterns['common_misinterpretations'].get('unclear_view', [])]) + \
                        len([r for r in self.reasoning_patterns['common_misinterpretations'].get('ppe_compliant', [])])
            
            if missed_fps > 50:
                prompt += f"""

💡 CONFIDENCE BOOST: Analysis shows {missed_fps} false positives missed due to over-caution.
   → Be MORE CONFIDENT when identifying equipment/structures
   → Trust pattern: No clear person = FALSE POSITIVE
   → Current threshold: {max(base_threshold - (round_num * 10), 60)}% (reduced from {base_threshold}%)
"""

        prompt += f"""

INTELLIGENT DECISION FRAMEWORK:
1. Person Detection: {self.get_person_detection_guidance(round_num)}
2. Context Analysis: {self.get_context_guidance(case, round_num)}
3. Confidence Level: {self.get_confidence_guidance(case, round_num)}

Output Format:
PERSON_DETECTED: [yes/no/possibly]
CONFIDENCE: [0-100]
IS_FALSE_POSITIVE: [true/false]
SAFETY_DECISION: [FLAG_FOR_REVIEW/DISMISS_WITH_CAUTION]
REASONING: [Detailed explanation using learned patterns]
"""
        
        return prompt
    
    def get_person_detection_guidance(self, round_num: int) -> str:
        """Get intelligent person detection guidance based on learning"""
        no_person_fps = self.reasoning_patterns['false_positive_indicators'].get('no_person_visible', 0)
        
        if no_person_fps > 100:
            return f"No person = FALSE POSITIVE ({no_person_fps} proven cases)"
        elif no_person_fps > 50:
            return f"No person likely = FALSE POSITIVE ({no_person_fps} cases)"
        else:
            return "Check carefully for person presence"
    
    def get_context_guidance(self, case: Dict, round_num: int) -> str:
        """Get context-specific guidance based on patterns"""
        terminal = case.get('terminal', '')
        inf_type = case.get('infringement_type', '')
        
        # Check for specific pattern matches
        if inf_type == 'PPE Non-compliance':
            ppe_patterns = self.reasoning_patterns['common_misinterpretations'].get('ppe_compliant', [])
            if len(ppe_patterns) > 10:
                return "If PPE basics visible (helmet+vest) = likely FALSE POSITIVE"
        
        return "Apply learned patterns for this violation type"
    
    def get_confidence_guidance(self, case: Dict, round_num: int) -> str:
        """Get confidence level guidance"""
        base = self.learning_params['confidence_thresholds'].get(case['infringement_type'], 90)
        adjusted = max(base - (round_num * 10), 60)
        
        if round_num > 2:
            return f"Use {adjusted}% threshold (reduced from {base}% for better detection)"
        else:
            return f"Use {base}% threshold (safety-first approach)"
    
    async def run_intelligent_learning(self, num_rounds: int = 5):
        """Run intelligent multi-round learning with reasoning analysis"""
        logger.info(f"🧠 STARTING INTELLIGENT AUTO-LEARNING WITH {num_rounds} ROUNDS")
        
        # Load cases
        self.all_cases = self.load_all_cases()
        logger.info(f"Loaded {len(self.all_cases)} cases for intelligent processing")
        
        # Check for existing Round 1 results
        round1_complete = False
        if os.path.exists("/home/<USER>/VALO_AI-FARM_2025/valo_advanced_round1_results.json"):
            logger.info("Found completed Round 1 results, starting from Round 2")
            round1_complete = True
            
            # Load Round 1 results for analysis
            with open("/home/<USER>/VALO_AI-FARM_2025/valo_batch_round1_intermediate.json", 'r') as f:
                round1_data = json.load(f)
                round1_results = round1_data.get('detailed_results', [])
                
            # Analyze Round 1 reasoning
            logger.info("Analyzing Round 1 reasoning patterns...")
            for result in round1_results:
                self.analyze_reasoning(result)
            
            self.all_results = round1_results
            start_round = 2
        else:
            start_round = 1
            self.all_results = []
        
        # Process rounds
        for round_num in range(start_round, num_rounds + 1):
            logger.info(f"\n{'='*60}")
            logger.info(f"INTELLIGENT ROUND {round_num} OF {num_rounds}")
            logger.info(f"{'='*60}")
            
            round_results = []
            total_chunks = (len(self.all_cases) + self.chunk_size - 1) // self.chunk_size
            
            for chunk_idx in range(total_chunks):
                start_idx = chunk_idx * self.chunk_size
                end_idx = min(start_idx + self.chunk_size, len(self.all_cases))
                chunk = self.all_cases[start_idx:end_idx]
                
                logger.info(f"Round {round_num} - Processing chunk {chunk_idx + 1}/{total_chunks}")
                
                # Process with intelligent prompts
                for case in chunk:
                    # Generate intelligent prompt based on learned patterns
                    prompt = self.generate_intelligent_prompt(case, round_num)
                    
                    # Get VLM analysis
                    vlm_result = await self.analyze_with_vlm(case['cropped_image'], prompt, case)
                    
                    # Combine results
                    full_result = {**case, **vlm_result, 'round': round_num}
                    
                    # Calculate accuracy
                    if case['is_false_positive']:
                        full_result['correct_prediction'] = vlm_result['is_false_positive_predicted']
                    else:
                        full_result['correct_prediction'] = not vlm_result['is_false_positive_predicted']
                        full_result['valid_case_protected'] = not vlm_result['is_false_positive_predicted']
                    
                    # Analyze reasoning for continuous learning
                    insights = self.analyze_reasoning(full_result)
                    full_result['learning_insights'] = insights
                    
                    round_results.append(full_result)
                    self.all_results.append(full_result)
                
                # Save progress
                if chunk_idx % 10 == 0:
                    await self.save_intermediate_results(round_num, round_results)
                
                await asyncio.sleep(self.delay_between_chunks)
            
            # Calculate round statistics
            await self.calculate_and_save_round_stats(round_num, round_results)
            
            # Early stopping if excellent performance
            valid_protection = self.calculate_valid_protection_rate(round_results)
            fp_detection = self.calculate_fp_detection_rate(round_results)
            
            logger.info(f"\nROUND {round_num} COMPLETE:")
            logger.info(f"- Valid Protection: {valid_protection:.1f}%")
            logger.info(f"- FP Detection: {fp_detection:.1f}%")
            logger.info(f"- Reasoning Patterns Learned: {len(self.reasoning_patterns['false_positive_indicators'])}")
            
            if valid_protection >= 100 and fp_detection >= 70:
                logger.info("🎯 Achieved optimal performance!")
                break
        
        # Save final intelligent results
        await self.save_final_intelligent_results()
    
    def calculate_valid_protection_rate(self, results: List[Dict]) -> float:
        """Calculate valid case protection rate"""
        valid_cases = [r for r in results if not r['is_false_positive']]
        if not valid_cases:
            return 100.0
        protected = sum(1 for r in valid_cases if not r.get('is_false_positive_predicted', False))
        return (protected / len(valid_cases)) * 100
    
    def calculate_fp_detection_rate(self, results: List[Dict]) -> float:
        """Calculate false positive detection rate"""
        fp_cases = [r for r in results if r['is_false_positive']]
        if not fp_cases:
            return 0.0
        detected = sum(1 for r in fp_cases if r.get('is_false_positive_predicted', False))
        return (detected / len(fp_cases)) * 100
    
    async def save_intermediate_results(self, round_num: int, results: List[Dict]):
        """Save intermediate results"""
        filename = f"/home/<USER>/VALO_AI-FARM_2025/valo_intelligent_round{round_num}_progress.json"
        
        summary = {
            'round': round_num,
            'cases_processed': len(results),
            'timestamp': datetime.now().isoformat(),
            'valid_protection_rate': self.calculate_valid_protection_rate(results),
            'fp_detection_rate': self.calculate_fp_detection_rate(results),
            'reasoning_patterns_learned': {
                'false_positive_indicators': len(self.reasoning_patterns['false_positive_indicators']),
                'equipment_keywords': len(self.reasoning_patterns['equipment_keywords']),
                'common_mistakes': sum(len(v) for v in self.reasoning_patterns['common_misinterpretations'].values())
            }
        }
        
        with open(filename, 'w') as f:
            json.dump(summary, f, indent=2)
    
    async def calculate_and_save_round_stats(self, round_num: int, results: List[Dict]):
        """Calculate and save detailed round statistics"""
        valid_protection = self.calculate_valid_protection_rate(results)
        fp_detection = self.calculate_fp_detection_rate(results)
        
        stats = {
            'round': round_num,
            'total_cases': len(results),
            'valid_protection_rate': valid_protection,
            'fp_detection_rate': fp_detection,
            'reasoning_analysis': {
                'total_patterns_learned': len(self.reasoning_patterns['false_positive_indicators']),
                'equipment_keywords_identified': list(self.reasoning_patterns['equipment_keywords'])[:20],
                'common_mistakes_found': {
                    k: len(v) for k, v in self.reasoning_patterns['common_misinterpretations'].items()
                }
            },
            'prompt_evolution': f"Round {round_num} prompt incorporates {len(self.reasoning_patterns['equipment_keywords'])} equipment patterns"
        }
        
        filename = f"/home/<USER>/VALO_AI-FARM_2025/valo_intelligent_round{round_num}_stats.json"
        with open(filename, 'w') as f:
            json.dump(stats, f, indent=2)
    
    async def save_final_intelligent_results(self):
        """Save comprehensive final results with all learning insights"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"/home/<USER>/VALO_AI-FARM_2025/valo_intelligent_final_{timestamp}.json"
        
        # Calculate final metrics
        valid_protection = self.calculate_valid_protection_rate(self.all_results)
        fp_detection = self.calculate_fp_detection_rate(self.all_results)
        
        final_results = {
            'processing_summary': {
                'total_cases_analyzed': len(self.all_results),
                'valid_protection_rate': valid_protection,
                'fp_detection_rate': fp_detection,
                'improvement_from_baseline': f"{fp_detection / 1.24:.1f}x better than Round 1"
            },
            'intelligent_learning_insights': {
                'reasoning_patterns_discovered': dict(self.reasoning_patterns['false_positive_indicators']),
                'equipment_vocabulary': list(self.reasoning_patterns['equipment_keywords']),
                'common_misinterpretations': {
                    k: f"{len(v)} cases" for k, v in self.reasoning_patterns['common_misinterpretations'].items()
                },
                'terminal_specific_patterns': {
                    terminal: {
                        'total_cases': len(patterns),
                        'accuracy': sum(1 for p in patterns if p['correct']) / len(patterns) * 100 if patterns else 0
                    }
                    for terminal, patterns in self.reasoning_patterns['context_clues'].items()
                }
            },
            'business_impact': {
                'annual_false_positives': 14484,
                'annual_fp_detected': int(14484 * (fp_detection / 100)),
                'annual_time_saved': int(14484 * (fp_detection / 100) * 5),
                'annual_cost_savings': int(14484 * (fp_detection / 100) * 5)
            },
            'key_learnings': [
                f"Equipment keywords for instant FP detection: {', '.join(list(self.reasoning_patterns['equipment_keywords'])[:10])}",
                f"PPE compliance over-flagging: {len(self.reasoning_patterns['common_misinterpretations'].get('ppe_compliant', []))} cases",
                f"No person visible = FP: {self.reasoning_patterns['false_positive_indicators'].get('no_person_visible', 0)} validated cases",
                "Confidence thresholds can be reduced after learning patterns"
            ],
            'timestamp': timestamp
        }
        
        with open(filename, 'w') as f:
            json.dump(final_results, f, indent=2)
        
        # Also save to Redis for dashboard
        self.redis_client.setex(
            'valo_intelligent_results',
            3600,
            json.dumps(final_results)
        )
        
        logger.info(f"\n{'='*60}")
        logger.info(f"INTELLIGENT LEARNING COMPLETE")
        logger.info(f"Final Valid Protection: {valid_protection:.1f}%")
        logger.info(f"Final FP Detection: {fp_detection:.1f}%")
        logger.info(f"Reasoning Patterns Learned: {len(self.reasoning_patterns['false_positive_indicators'])}")
        logger.info(f"Results saved to: {filename}")
        logger.info(f"{'='*60}")


async def main():
    """Run intelligent learning system"""
    processor = IntelligentLearningProcessor()
    
    try:
        await processor.run_intelligent_learning(num_rounds=5)
    except Exception as e:
        logger.error(f"Intelligent learning failed: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(main())