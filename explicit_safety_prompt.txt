SAFETY VIOLATION DETECTION SYSTEM

ANALYZE BOTH PROVIDED IMAGES.

STEP 1: What is in the CROPPED image?
A) STRUCTURE ONLY (needs >90% confidence)
B) PERSON (any human features)  
C) UNCLEAR

STEP 2: Apply these EXACT rules:

IF STRUCTURE ONLY (>90% confident):
→ Output: FALSE POSITIVE: YES

IF PERSON DETECTED:
→ Check for ANY of these violations:
  • Missing helmet
  • Missing vest  
  • Vest not fastened
  • Mobile phone use
  • Missing required equipment
  • Wrong location
  • Unsafe behavior
  
  IF ANY VIOLATION FOUND:
  → Output: FALSE POSITIVE: NO
  
  IF NO VIOLATIONS:
  → Output: FALSE POSITIVE: YES

IF UNCLEAR:
→ Output: FALSE POSITIVE: NO (safety first)

IMPORTANT: The question is "Is this a FALSE POSITIVE?"
- Real violations must output: FALSE POSITIVE: NO
- Only non-violations output: FALSE POSITIVE: YES

OUTPUT FORMAT (use exactly):
FALSE POSITIVE: [YES or NO]
Detection: [STRUCTURE/PERSON/UNCLEAR]
Violations: [List any found, or "None"]

EXAMPLES:

Example 1: Crane structure only
Detection: STRUCTURE
Violations: None
FALSE POSITIVE: YES

Example 2: Person with helmet and vest using mobile phone
Detection: PERSON  
Violations: Mobile phone use
FALSE POSITIVE: NO

Example 3: Person with all PPE, no violations
Detection: PERSON
Violations: None
FALSE POSITIVE: YES

Example 4: Person missing helmet
Detection: PERSON
Violations: Missing helmet
FALSE POSITIVE: NO

Example 5: Unclear image
Detection: UNCLEAR
Violations: Cannot assess
FALSE POSITIVE: NO