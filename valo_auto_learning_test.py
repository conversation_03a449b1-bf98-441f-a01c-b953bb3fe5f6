#!/usr/bin/env python3
"""
VALO AI-FARM Auto-Learning Test Script
Processes real PSA VALO violation data to demonstrate auto-learning capabilities
"""

import asyncio
import pandas as pd
import json
import sys
import os
from pathlib import Path
from typing import List, Dict, Any
from datetime import datetime
import httpx
import time

# Add backend to Python path
sys.path.append('/home/<USER>/VALO_AI-FARM_2025/backend')

from app.models.schemas import VLMAnalysisResult
from app.services.vlm_service import VLMService
from app.core.config import settings

class VALOAutoLearningTest:
    def __init__(self):
        self.csv_path = "/home/<USER>/VALO_AI-FARM_2025/ai_farm_images_fixed_250703/psa_valo_violation_report_without_images_csv_2025-07-03_14-14-01.CSV"
        self.images_base = "/home/<USER>/VALO_AI-FARM_2025/ai_farm_images_fixed_250703/ai_farm_images_fixed"
        self.vlm_service = VLMService()
        self.results = []
        self.patterns = {}
        
    def load_valo_data(self, sample_size=20) -> List[Dict]:
        """Load sample of VALO cases for auto-learning"""
        print(f"📊 Loading VALO data from {self.csv_path}")
        
        df = pd.read_csv(self.csv_path)
        print(f"✅ Total records: {len(df)}")
        
        # Balance sample between valid and invalid cases
        invalid_cases = df[df['Alert Status'] == 'Invalid'].head(sample_size//2)
        valid_cases = df[df['Alert Status'] == 'Valid'].head(sample_size//2)
        
        sample_df = pd.concat([invalid_cases, valid_cases])
        
        valo_cases = []
        for _, row in sample_df.iterrows():
            case = {
                'case_number': row['Case Int. ID'],
                'camera_id': row['Camera'],
                'terminal': row['Terminal'],
                'alert_status': row['Alert Status'],
                'infringement_type': row['Type of Infringement'],
                'follow_up': row['Follow Up'],
                'remarks': row['Remarks'],
                'acknowledged_by': row['Acknowledged By'],
                'reviewed_by': row['Reviewed By']
            }
            valo_cases.append(case)
        
        print(f"✅ Loaded {len(valo_cases)} balanced sample cases")
        print(f"   - Invalid (False Positive): {len(invalid_cases)}")
        print(f"   - Valid (True Violation): {len(valid_cases)}")
        
        return valo_cases
    
    def find_case_images(self, case_number: str, alert_status: str) -> Dict[str, str]:
        """Find corresponding images for a case"""
        status_dir = "invalid" if alert_status == "Invalid" else "valid"
        status_suffix = "invalid" if alert_status == "Invalid" else "valid"
        
        source_image = f"{self.images_base}/{status_dir}/{case_number}_source_{status_suffix}.JPEG"
        cropped_image = f"{self.images_base}/{status_dir}/{case_number}_cropped_{status_suffix}.JPEG"
        
        images = {}
        if os.path.exists(source_image):
            images['source'] = source_image
        if os.path.exists(cropped_image):
            images['cropped'] = cropped_image
            
        return images
    
    def generate_valo_prompt(self, case: Dict) -> str:
        """Generate VALO-specific VLM prompt based on infringement type"""
        base_prompt = f"""
VALO SAFETY VIOLATION ANALYSIS - PSA Port Terminal

CASE CONTEXT:
- Case Number: {case['case_number']}
- Camera: {case['camera_id']}
- Terminal: {case['terminal']}
- Infringement Type: {case['infringement_type']}
- Historical Human Assessment: {case['alert_status']}
- Human Remarks: {case['remarks']}

ANALYSIS TASK:
Analyze this port safety violation image to determine if this is a genuine safety violation or a false positive.

SPECIFIC FOCUS FOR {case['infringement_type']}:
"""
        
        if "PPE Non-compliance" in case['infringement_type']:
            base_prompt += """
- Verify actual personnel presence (not equipment/structures)
- Check for hard hat, high-visibility vest, safety boots
- Consider lighting conditions and camera angles
- Look for crane structures, vessel parts misidentified as people
"""
        elif "Container Distance" in case['infringement_type']:
            base_prompt += """
- Assess actual proximity between personnel and containers
- Account for camera perspective distortion
- Check if person is truly in danger zone vs. visual illusion
- Consider crane equipment creating false depth perception
"""
        elif "One man Lashing" in case['infringement_type']:
            base_prompt += """
- Count visible personnel in lashing operation area
- Distinguish between active lashing vs. inspection activities
- Check for multiple workers where only one is clearly visible
- Verify if this is actually lashing equipment operation
"""
        elif "Ex.Row Violation" in case['infringement_type']:
            base_prompt += """
- Identify clearly marked exclusion zones
- Verify person is actually within restricted area
- Consider authorized personnel vs. unauthorized access
- Check for equipment shadows creating false person detection
"""
        
        base_prompt += f"""

KNOWN FALSE POSITIVE PATTERNS (from human reviewers):
Based on historical VALO data, common false positives include:
- Crane structures captured as personnel
- Vessel structures misidentified 
- Workers in full PPE flagged incorrectly
- Equipment shadows creating false shapes

REQUIRED OUTPUT:
1. CONFIDENCE (0-100): How confident are you this is a genuine violation?
2. DETECTION_TYPE: personnel|equipment|structure|unclear
3. IS_FALSE_POSITIVE: true|false
4. REASONING: Detailed explanation of your analysis
5. RECOMMENDATION: DISMISS_ALERT|REQUIRES_REVIEW|CONFIRMED_VIOLATION

Focus on distinguishing genuine safety violations from equipment/structure misidentification.
"""
        return base_prompt
    
    async def analyze_case_with_vlm(self, case: Dict, images: Dict) -> Dict:
        """Analyze a single case using VLM"""
        print(f"🔍 Analyzing case {case['case_number']} - {case['infringement_type']}")
        
        # Use cropped image if available, otherwise source
        image_path = images.get('cropped', images.get('source'))
        if not image_path:
            print(f"❌ No images found for case {case['case_number']}")
            return None
        
        # Generate VALO-specific prompt
        prompt = self.generate_valo_prompt(case)
        
        try:
            # Analyze with VLM
            start_time = time.time()
            vlm_result = await self.vlm_service.analyze_image(image_path, case['case_number'], prompt)
            processing_time = time.time() - start_time
            
            # Create analysis result
            result = {
                'case_number': case['case_number'],
                'camera_id': case['camera_id'],
                'terminal': case['terminal'],
                'infringement_type': case['infringement_type'],
                'ground_truth': case['alert_status'],
                'human_remarks': case['remarks'],
                'vlm_confidence': vlm_result.confidence_score,
                'vlm_detection_type': vlm_result.detection_type,
                'vlm_is_false_positive': vlm_result.is_false_positive,
                'vlm_reasoning': vlm_result.reasoning,
                'vlm_recommendation': getattr(vlm_result, 'recommendation', 'UNKNOWN'),
                'processing_time_ms': int(processing_time * 1000),
                'image_used': 'cropped' if 'cropped' in images else 'source'
            }
            
            print(f"✅ Analysis complete - Confidence: {vlm_result.confidence_score}%, "
                  f"False Positive: {vlm_result.is_false_positive}")
            
            return result
            
        except Exception as e:
            print(f"❌ VLM analysis failed for case {case['case_number']}: {e}")
            return None
    
    def extract_patterns(self, results: List[Dict]) -> Dict:
        """Extract auto-learning patterns from results"""
        patterns = {
            'false_positive_causes': {},
            'camera_performance': {},
            'terminal_performance': {},
            'infringement_accuracy': {},
            'confidence_calibration': {},
            'total_cases': len(results),
            'successful_analyses': len([r for r in results if r]),
        }
        
        valid_results = [r for r in results if r]
        
        # Analyze false positive causes from human remarks
        for result in valid_results:
            if result['ground_truth'] == 'Invalid':  # Known false positive
                remarks = result['human_remarks'].upper()
                if 'CRANE STRUCTURE' in remarks:
                    patterns['false_positive_causes']['crane_misidentification'] = \
                        patterns['false_positive_causes'].get('crane_misidentification', 0) + 1
                elif 'VESSEL STRUCTURE' in remarks:
                    patterns['false_positive_causes']['vessel_misidentification'] = \
                        patterns['false_positive_causes'].get('vessel_misidentification', 0) + 1
                elif 'SPREADER' in remarks:
                    patterns['false_positive_causes']['spreader_misidentification'] = \
                        patterns['false_positive_causes'].get('spreader_misidentification', 0) + 1
                elif 'FULL PPE' in remarks:
                    patterns['false_positive_causes']['ppe_misclassification'] = \
                        patterns['false_positive_causes'].get('ppe_misclassification', 0) + 1
        
        # Camera performance analysis
        camera_stats = {}
        for result in valid_results:
            camera = result['camera_id']
            if camera not in camera_stats:
                camera_stats[camera] = {'total': 0, 'correct': 0}
            
            camera_stats[camera]['total'] += 1
            
            # Check if VLM agrees with ground truth
            ground_truth_fp = result['ground_truth'] == 'Invalid'
            vlm_fp = result['vlm_is_false_positive']
            
            if ground_truth_fp == vlm_fp:
                camera_stats[camera]['correct'] += 1
        
        # Calculate accuracy per camera
        for camera, stats in camera_stats.items():
            accuracy = (stats['correct'] / stats['total']) * 100 if stats['total'] > 0 else 0
            patterns['camera_performance'][camera] = {
                'accuracy': round(accuracy, 1),
                'total_cases': stats['total'],
                'correct_predictions': stats['correct']
            }
        
        # Terminal performance analysis
        terminal_stats = {}
        for result in valid_results:
            terminal = result['terminal']
            if terminal not in terminal_stats:
                terminal_stats[terminal] = {'total': 0, 'false_positives': 0}
            
            terminal_stats[terminal]['total'] += 1
            if result['ground_truth'] == 'Invalid':
                terminal_stats[terminal]['false_positives'] += 1
        
        for terminal, stats in terminal_stats.items():
            fp_rate = (stats['false_positives'] / stats['total']) * 100 if stats['total'] > 0 else 0
            patterns['terminal_performance'][terminal] = {
                'false_positive_rate': round(fp_rate, 1),
                'total_cases': stats['total'],
                'false_positives': stats['false_positives']
            }
        
        # Infringement type accuracy
        infringement_stats = {}
        for result in valid_results:
            inf_type = result['infringement_type']
            if inf_type not in infringement_stats:
                infringement_stats[inf_type] = {'total': 0, 'correct': 0}
            
            infringement_stats[inf_type]['total'] += 1
            
            ground_truth_fp = result['ground_truth'] == 'Invalid'
            vlm_fp = result['vlm_is_false_positive']
            
            if ground_truth_fp == vlm_fp:
                infringement_stats[inf_type]['correct'] += 1
        
        for inf_type, stats in infringement_stats.items():
            accuracy = (stats['correct'] / stats['total']) * 100 if stats['total'] > 0 else 0
            patterns['infringement_accuracy'][inf_type] = {
                'accuracy': round(accuracy, 1),
                'total_cases': stats['total'],
                'correct_predictions': stats['correct']
            }
        
        # Confidence calibration
        confidence_ranges = {'0-20': 0, '21-40': 0, '41-60': 0, '61-80': 0, '81-100': 0}
        confidence_correct = {'0-20': 0, '21-40': 0, '41-60': 0, '61-80': 0, '81-100': 0}
        
        for result in valid_results:
            confidence = result['vlm_confidence']
            if confidence <= 20:
                range_key = '0-20'
            elif confidence <= 40:
                range_key = '21-40'
            elif confidence <= 60:
                range_key = '41-60'
            elif confidence <= 80:
                range_key = '61-80'
            else:
                range_key = '81-100'
            
            confidence_ranges[range_key] += 1
            
            ground_truth_fp = result['ground_truth'] == 'Invalid'
            vlm_fp = result['vlm_is_false_positive']
            
            if ground_truth_fp == vlm_fp:
                confidence_correct[range_key] += 1
        
        for range_key in confidence_ranges:
            if confidence_ranges[range_key] > 0:
                accuracy = (confidence_correct[range_key] / confidence_ranges[range_key]) * 100
                patterns['confidence_calibration'][range_key] = {
                    'accuracy': round(accuracy, 1),
                    'total_cases': confidence_ranges[range_key],
                    'correct_predictions': confidence_correct[range_key]
                }
        
        return patterns
    
    async def run_auto_learning_test(self, sample_size=20):
        """Run complete auto-learning test with VALO data"""
        print("🚀 Starting VALO AI-FARM Auto-Learning Test")
        print("=" * 60)
        
        # Load VALO cases
        valo_cases = self.load_valo_data(sample_size)
        
        # Process each case
        print(f"\n🔬 Processing {len(valo_cases)} cases with VLM...")
        results = []
        
        for i, case in enumerate(valo_cases, 1):
            print(f"\n[{i}/{len(valo_cases)}] Processing case {case['case_number']}")
            
            # Find images
            images = self.find_case_images(case['case_number'], case['alert_status'])
            if not images:
                print(f"⚠️  No images found for case {case['case_number']}")
                continue
            
            # Analyze with VLM
            result = await self.analyze_case_with_vlm(case, images)
            if result:
                results.append(result)
                self.results.append(result)
        
        # Extract patterns
        print(f"\n📊 Analyzing patterns from {len(results)} successful analyses...")
        patterns = self.extract_patterns(results)
        self.patterns = patterns
        
        # Save results
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_file = f"/home/<USER>/VALO_AI-FARM_2025/valo_auto_learning_results_{timestamp}.json"
        
        output_data = {
            'test_info': {
                'timestamp': timestamp,
                'sample_size': sample_size,
                'total_processed': len(results),
                'csv_source': self.csv_path,
                'images_source': self.images_base
            },
            'results': results,
            'patterns': patterns
        }
        
        with open(results_file, 'w') as f:
            json.dump(output_data, f, indent=2)
        
        print(f"💾 Results saved to: {results_file}")
        
        # Print summary
        self.print_summary()
        
        return results, patterns
    
    def print_summary(self):
        """Print auto-learning test summary"""
        print("\n" + "=" * 60)
        print("🎯 VALO AUTO-LEARNING TEST SUMMARY")
        print("=" * 60)
        
        if not self.patterns:
            print("❌ No patterns data available")
            return
        
        print(f"📊 PROCESSING STATISTICS:")
        print(f"   Total Cases: {self.patterns['total_cases']}")
        print(f"   Successful Analyses: {self.patterns['successful_analyses']}")
        print(f"   Success Rate: {(self.patterns['successful_analyses'] / self.patterns['total_cases'] * 100):.1f}%")
        
        print(f"\n🔍 FALSE POSITIVE PATTERN DETECTION:")
        fp_causes = self.patterns['false_positive_causes']
        if fp_causes:
            for cause, count in fp_causes.items():
                print(f"   {cause.replace('_', ' ').title()}: {count} cases")
        else:
            print("   No false positive patterns detected")
        
        print(f"\n📷 CAMERA PERFORMANCE ANALYSIS:")
        camera_perf = self.patterns['camera_performance']
        if camera_perf:
            for camera, stats in camera_perf.items():
                print(f"   {camera}: {stats['accuracy']}% accuracy ({stats['correct_predictions']}/{stats['total_cases']})")
        
        print(f"\n🏢 TERMINAL PERFORMANCE ANALYSIS:")
        terminal_perf = self.patterns['terminal_performance']
        if terminal_perf:
            for terminal, stats in terminal_perf.items():
                print(f"   {terminal}: {stats['false_positive_rate']}% FP rate ({stats['false_positives']}/{stats['total_cases']})")
        
        print(f"\n⚡ INFRINGEMENT TYPE ACCURACY:")
        inf_acc = self.patterns['infringement_accuracy']
        if inf_acc:
            for inf_type, stats in inf_acc.items():
                print(f"   {inf_type}: {stats['accuracy']}% accuracy ({stats['correct_predictions']}/{stats['total_cases']})")
        
        print(f"\n🎯 CONFIDENCE CALIBRATION:")
        conf_cal = self.patterns['confidence_calibration']
        if conf_cal:
            for range_key, stats in conf_cal.items():
                print(f"   {range_key}% confidence: {stats['accuracy']}% accuracy ({stats['correct_predictions']}/{stats['total_cases']})")
        
        print("\n✅ Auto-learning test completed successfully!")

async def main():
    """Main function to run auto-learning test"""
    test = VALOAutoLearningTest()
    
    try:
        # Run with sample size of 20 cases for testing
        results, patterns = await test.run_auto_learning_test(sample_size=20)
        print(f"\n🎉 Auto-learning test completed successfully!")
        print(f"📁 Results available in current directory")
        
    except KeyboardInterrupt:
        print("\n❌ Test interrupted by user")
    except Exception as e:
        print(f"\n💥 Test failed with error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())