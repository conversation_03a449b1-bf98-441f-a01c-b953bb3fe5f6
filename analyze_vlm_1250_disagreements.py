#!/usr/bin/env python3
"""
Analyze VLM predictions vs ground truth for the 1250 cases
Only show cases where VLM and ground truth disagree
"""

import pandas as pd
import json
import re
from pathlib import Path
from datetime import datetime

class VLMDisagreementAnalyzer:
    def __init__(self):
        self.csv_file = 'ai_farm_images_fixed_250703/psa_valo_violation_report_without_images_csv_2025-07-03_14-14-01.CSV'
        self.disagreements = []
        self.agreements = []
        self.analysis_results = {
            'total_cases': 0,
            'agreements': 0,
            'disagreements': 0,
            'vlm_accuracy': 0,
            'critical_errors': 0,  # VLM says FP but it's a valid case
            'missed_fps': 0,       # VLM says TP but it's FP
            'disagreement_details': {
                'vlm_fp_human_tp': [],  # Critical: VLM filters valid cases
                'vlm_tp_human_fp': []   # Missed: VLM doesn't filter FPs
            }
        }
        
    def load_ground_truth(self):
        """Load ground truth from CSV"""
        df = pd.read_csv(self.csv_file)
        
        # Get unique cases from the CSV
        unique_cases = {}
        
        for _, row in df.iterrows():
            case_number = row['Case Int. ID']
            status = str(row['Alert Status']).strip() if pd.notna(row['Alert Status']) else ''
            
            # 'Invalid' status = FALSE_POSITIVE, everything else = TRUE_POSITIVE
            if status.lower() == 'invalid':
                unique_cases[case_number] = 'FALSE_POSITIVE'
            else:
                unique_cases[case_number] = 'TRUE_POSITIVE'
                    
        print(f"Loaded {len(unique_cases)} unique cases from CSV")
        
        # Count distribution
        fp_count = sum(1 for v in unique_cases.values() if v == 'FALSE_POSITIVE')
        tp_count = sum(1 for v in unique_cases.values() if v == 'TRUE_POSITIVE')
        print(f"Ground Truth: {fp_count} FALSE_POSITIVES, {tp_count} TRUE_POSITIVES")
        
        return unique_cases
    
    def extract_vlm_prediction(self, confidence_response):
        """Extract VLM's prediction from confidence response"""
        if not confidence_response:
            return 'UNCERTAIN', {}
            
        content = confidence_response
        content_lower = confidence_response.lower()
        
        # Extract key metrics
        metrics = {
            'fp_likelihood': None,
            'person_present': None,
            'ppe_compliance': None,
            'violation_confidence': None,
            'violation_type': None
        }
        
        # Extract FP likelihood - most reliable indicator
        fp_patterns = [
            r'\*\*false positive likelihood\*\*[:\s]+(\d+)%',
            r'false positive likelihood[:\s]+(\d+)%',
            r'likelihood of false positive[:\s]+(\d+)%',
            r'false positive[:\s]+(\d+)%',
            r'fp likelihood[:\s]+(\d+)%'
        ]
        
        for pattern in fp_patterns:
            match = re.search(pattern, content_lower)
            if match:
                metrics['fp_likelihood'] = int(match.group(1))
                break
                
        # Extract person presence (look for bold markers too)
        if '**person present**: yes' in content_lower or '**person present**: true' in content_lower:
            metrics['person_present'] = True
        elif '**person present**: no' in content_lower or '**person present**: false' in content_lower:
            metrics['person_present'] = False
        elif 'person present: yes' in content_lower or 'person present: true' in content_lower:
            metrics['person_present'] = True
        elif 'person present: no' in content_lower or 'person present: false' in content_lower:
            metrics['person_present'] = False
            
        # Extract PPE compliance (check various formats)
        if 'ppe_compliance: complete' in content_lower or 'ppe compliance: complete' in content_lower:
            metrics['ppe_compliance'] = 'COMPLETE'
        elif 'ppe_compliance: incomplete' in content_lower or 'ppe compliance: incomplete' in content_lower:
            metrics['ppe_compliance'] = 'INCOMPLETE'  
        elif 'ppe_compliance: none' in content_lower or 'ppe compliance: none' in content_lower:
            metrics['ppe_compliance'] = 'NONE'
        elif 'ppe_compliance: na' in content_lower:
            metrics['ppe_compliance'] = 'NA'
            
        # Extract violation confidence
        viol_match = re.search(r'violation confidence[:\s]+(\d+)%', content_lower)
        if viol_match:
            metrics['violation_confidence'] = int(viol_match.group(1))
            
        # Extract violation type
        viol_type_match = re.search(r'violation type[:\s]+([^\n]+)', content_lower)
        if viol_type_match:
            metrics['violation_type'] = viol_type_match.group(1).strip()
            
        # Determine VLM prediction based on extracted metrics
        # Primary indicator: FP likelihood
        if metrics['fp_likelihood'] is not None:
            if metrics['fp_likelihood'] > 50:
                return 'FALSE_POSITIVE', metrics
            else:
                return 'TRUE_POSITIVE', metrics
                
        # Secondary: No person = likely FP
        if metrics['person_present'] == False:
            return 'FALSE_POSITIVE', metrics
            
        # Tertiary: PPE compliance
        if metrics['person_present'] == True:
            if metrics['ppe_compliance'] == 'COMPLETE':
                return 'FALSE_POSITIVE', metrics
            elif metrics['ppe_compliance'] in ['INCOMPLETE', 'NONE']:
                return 'TRUE_POSITIVE', metrics
                
        return 'UNCERTAIN', metrics
    
    def parse_markdown_cases(self, file_path):
        """Parse markdown file to extract VLM responses by case"""
        cases = {}
        
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # Split by case sections
        case_sections = re.split(r'\n## Case: ', content)
        
        for section in case_sections[1:]:  # Skip header
            lines = section.split('\n')
            case_number = lines[0].strip()
            
            # Extract confidence analysis section
            conf_start = section.find('### Confidence Analysis')
            if conf_start == -1:
                continue
                
            # Find end of confidence section (next section or end)
            conf_end = section.find('\n---', conf_start)
            if conf_end == -1:
                conf_end = len(section)
                
            confidence_response = section[conf_start:conf_end]
            
            # Also extract description for context
            desc_start = section.find('### Comprehensive Description')
            desc_end = section.find('### Confidence Analysis')
            
            description = ''
            if desc_start != -1 and desc_end != -1:
                # Extract first 500 chars for preview
                full_desc = section[desc_start:desc_end]
                desc_content = full_desc.split('```')[1] if '```' in full_desc else full_desc
                description = desc_content[:500].strip() + '...' if len(desc_content) > 500 else desc_content.strip()
                
            cases[case_number] = {
                'confidence_response': confidence_response,
                'description_preview': description
            }
            
        return cases
    
    def analyze_all_cases(self):
        """Compare VLM predictions with ground truth"""
        # Load ground truth from CSV
        ground_truth = self.load_ground_truth()
        
        # Load VLM responses
        all_vlm_cases = {}
        
        # Load FALSE POSITIVE cases from VLM
        fp_file = Path('valo_comprehensive_data/false_positives/false_positive_analysis_20250725_232934.md')
        if fp_file.exists():
            fp_cases = self.parse_markdown_cases(fp_file)
            all_vlm_cases.update(fp_cases)
            print(f"Loaded {len(fp_cases)} FP cases from VLM data")
            
        # Load TRUE POSITIVE cases from VLM  
        tp_file = Path('valo_comprehensive_data/true_positives/true_positive_analysis_20250725_232934.md')
        if tp_file.exists():
            tp_cases = self.parse_markdown_cases(tp_file)
            all_vlm_cases.update(tp_cases)
            print(f"Loaded {len(tp_cases)} TP cases from VLM data")
            
        print(f"\nTotal VLM responses loaded: {len(all_vlm_cases)}")
        
        # Debug: show sample case numbers from both
        csv_cases = list(ground_truth.keys())[:5]
        vlm_cases = list(all_vlm_cases.keys())[:5]
        print(f"Sample CSV cases: {csv_cases}")
        print(f"Sample VLM cases: {vlm_cases}")
        
        # Analyze each case
        missing_cases = []
        for case_number, csv_label in ground_truth.items():
            if case_number not in all_vlm_cases:
                missing_cases.append(case_number)
                continue  # Skip if no VLM data
                
            vlm_data = all_vlm_cases[case_number]
            vlm_prediction, metrics = self.extract_vlm_prediction(vlm_data['confidence_response'])
            
            # Debug first few cases
            if self.analysis_results['total_cases'] < 3:
                print(f"\nDebug case {case_number}:")
                print(f"  CSV label: {csv_label}")
                print(f"  VLM prediction: {vlm_prediction}")
                print(f"  Metrics: {metrics}")
                print(f"  Confidence response preview: {vlm_data['confidence_response'][:200]}...")
            
            # Skip uncertain cases
            if vlm_prediction == 'UNCERTAIN':
                continue
                
            self.analysis_results['total_cases'] += 1
            
            # Check agreement
            agrees = (vlm_prediction == csv_label)
            
            case_result = {
                'case_number': case_number,
                'csv_ground_truth': csv_label,
                'vlm_prediction': vlm_prediction,
                'agrees': agrees,
                'metrics': metrics,
                'description_preview': vlm_data['description_preview'],
                'confidence_snippet': vlm_data['confidence_response'][:500] + '...'
            }
            
            if agrees:
                self.agreements.append(case_result)
                self.analysis_results['agreements'] += 1
            else:
                self.disagreements.append(case_result)
                self.analysis_results['disagreements'] += 1
                
                # Track disagreement type
                if vlm_prediction == 'FALSE_POSITIVE' and csv_label == 'TRUE_POSITIVE':
                    self.analysis_results['disagreement_details']['vlm_fp_human_tp'].append(case_number)
                    self.analysis_results['critical_errors'] += 1
                elif vlm_prediction == 'TRUE_POSITIVE' and csv_label == 'FALSE_POSITIVE':
                    self.analysis_results['disagreement_details']['vlm_tp_human_fp'].append(case_number)
                    self.analysis_results['missed_fps'] += 1
                    
        # Debug output
        if missing_cases:
            print(f"\nWarning: {len(missing_cases)} cases from CSV not found in VLM data")
            print(f"Sample missing cases: {missing_cases[:5]}")
            
        # Also check reverse - VLM cases in CSV
        vlm_in_csv = 0
        for vlm_case in all_vlm_cases.keys():
            if vlm_case in ground_truth:
                vlm_in_csv += 1
        print(f"\nVLM cases found in CSV: {vlm_in_csv} out of {len(all_vlm_cases)}")
            
        # Calculate accuracy
        if self.analysis_results['total_cases'] > 0:
            self.analysis_results['vlm_accuracy'] = (
                self.analysis_results['agreements'] / self.analysis_results['total_cases'] * 100
            )
    
    def generate_disagreement_report(self):
        """Generate report showing only disagreement cases"""
        report = f"""# VLM vs Ground Truth - Disagreement Analysis Report

## Executive Summary

**Total Cases Analyzed**: {self.analysis_results['total_cases']} / 1250
**VLM Accuracy**: {self.analysis_results['vlm_accuracy']:.1f}%

### Results Breakdown:
- ✅ **Passed (Agreement)**: {self.analysis_results['agreements']} cases
- ❌ **Failed (Disagreement)**: {self.analysis_results['disagreements']} cases

### Critical Findings:
- ⚠️ **Critical Errors**: {self.analysis_results['critical_errors']} valid cases filtered (unacceptable)
- 📊 **Missed FPs**: {self.analysis_results['missed_fps']} false positives not caught

---

# Detailed Disagreement Cases

"""
        
        # Sort disagreements by type
        critical_cases = [c for c in self.disagreements if c['vlm_prediction'] == 'FALSE_POSITIVE' and c['csv_ground_truth'] == 'TRUE_POSITIVE']
        missed_fp_cases = [c for c in self.disagreements if c['vlm_prediction'] == 'TRUE_POSITIVE' and c['csv_ground_truth'] == 'FALSE_POSITIVE']
        
        # Critical cases first - these are the worst errors
        if critical_cases:
            report += "\n## 🚨 CRITICAL: Valid Cases Incorrectly Filtered Out\n"
            report += "These are real safety violations that VLM would remove - UNACCEPTABLE!\n\n"
            
            for i, case in enumerate(critical_cases, 1):
                report += f"""
### {i}. Case: {case['case_number']} ⚠️ CRITICAL ERROR
- **Ground Truth**: TRUE_POSITIVE (Valid safety violation)
- **VLM Says**: FALSE_POSITIVE (Would filter out!)
- **FP Likelihood**: {case['metrics']['fp_likelihood']}% 
- **Person Present**: {case['metrics']['person_present']}
- **PPE Compliance**: {case['metrics']['ppe_compliance']}
- **Violation Type**: {case['metrics']['violation_type']}

**Description Preview**:
```
{case['description_preview']}
```

**Why VLM Failed**: Likely due to {self._analyze_failure_reason(case)}

---
"""
        
        # Missed FP cases - less critical but still important
        if missed_fp_cases:
            report += "\n## 📊 Missed False Positives\n"
            report += "These false alarms weren't filtered - reduces efficiency\n\n"
            
            for i, case in enumerate(missed_fp_cases, 1):
                report += f"""
### {i}. Case: {case['case_number']}
- **Ground Truth**: FALSE_POSITIVE (Invalid/false alarm)
- **VLM Says**: TRUE_POSITIVE (Didn't filter)
- **FP Likelihood**: {case['metrics']['fp_likelihood']}%
- **Person Present**: {case['metrics']['person_present']}
- **PPE Compliance**: {case['metrics']['ppe_compliance']}

**Description Preview**:
```
{case['description_preview']}
```

---
"""
        
        return report
    
    def _analyze_failure_reason(self, case):
        """Analyze why VLM failed on a case"""
        metrics = case['metrics']
        
        if metrics['fp_likelihood'] and metrics['fp_likelihood'] > 50:
            return f"high FP likelihood ({metrics['fp_likelihood']}%) despite being valid"
        elif metrics['person_present'] == False:
            return "no person detected in a valid violation"
        elif metrics['ppe_compliance'] == 'COMPLETE':
            return "complete PPE detected in a violation case"
        else:
            return "unclear detection criteria"
    
    def create_filtered_webpage(self):
        """Create webpage showing only disagreement cases"""
        html = f"""<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VLM Disagreement Cases - Review System</title>
    <style>
        body {{
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }}
        .container {{
            max-width: 1400px;
            margin: 0 auto;
        }}
        .header {{
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }}
        .summary {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }}
        .metric {{
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            text-align: center;
        }}
        .metric.critical {{
            background: #fee;
            border: 1px solid #fcc;
        }}
        .metric.warning {{
            background: #fef;
            border: 1px solid #fcf;
        }}
        .case {{
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }}
        .case.critical {{
            border-left: 5px solid #dc3545;
        }}
        .case.missed {{
            border-left: 5px solid #ffc107;
        }}
        .images {{
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }}
        .image-container {{
            text-align: center;
        }}
        .image-container img {{
            max-width: 100%;
            height: 300px;
            object-fit: contain;
            border: 1px solid #ddd;
            border-radius: 4px;
        }}
        .metrics-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
            margin: 15px 0;
        }}
        .metric-item {{
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            text-align: center;
        }}
        .navigation {{
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 20px 0;
            padding: 15px;
            background: white;
            border-radius: 8px;
        }}
        button {{
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }}
        .btn-primary {{
            background: #007bff;
            color: white;
        }}
        .btn-secondary {{
            background: #6c757d;
            color: white;
        }}
        .description {{
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
        }}
        .filter-buttons {{
            margin: 20px 0;
            text-align: center;
        }}
        .filter-buttons button {{
            margin: 0 5px;
        }}
        .filter-buttons button.active {{
            background: #28a745;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>VLM Disagreement Cases Review</h1>
            <p>Review cases where VLM predictions disagree with ground truth labels</p>
        </div>

        <div class="summary">
            <div class="metric">
                <h3>Total Analyzed</h3>
                <div style="font-size: 2em;">{self.analysis_results['total_cases']}</div>
            </div>
            <div class="metric">
                <h3>VLM Accuracy</h3>
                <div style="font-size: 2em;">{self.analysis_results['vlm_accuracy']:.1f}%</div>
            </div>
            <div class="metric critical">
                <h3>Critical Errors</h3>
                <div style="font-size: 2em;">{self.analysis_results['critical_errors']}</div>
                <small>Valid cases filtered</small>
            </div>
            <div class="metric warning">
                <h3>Missed FPs</h3>
                <div style="font-size: 2em;">{self.analysis_results['missed_fps']}</div>
                <small>FPs not caught</small>
            </div>
        </div>

        <div class="filter-buttons">
            <button class="active" onclick="filterCases('all')">All Disagreements ({len(self.disagreements)})</button>
            <button onclick="filterCases('critical')">Critical Errors ({self.analysis_results['critical_errors']})</button>
            <button onclick="filterCases('missed')">Missed FPs ({self.analysis_results['missed_fps']})</button>
        </div>

        <div class="navigation">
            <button class="btn-secondary" onclick="previousCase()">← Previous</button>
            <span id="case-counter">Case 1 of {len(self.disagreements)}</span>
            <button class="btn-secondary" onclick="nextCase()">Next →</button>
        </div>

        <div id="cases-container">
"""
        
        # Add all disagreement cases
        for i, case in enumerate(self.disagreements):
            is_critical = case['vlm_prediction'] == 'FALSE_POSITIVE' and case['csv_ground_truth'] == 'TRUE_POSITIVE'
            case_class = 'critical' if is_critical else 'missed'
            case_type = 'critical' if is_critical else 'missed'
            
            # Build image paths
            source_img = f"/home/<USER>/VALO_AI-FARM_2025/ai_farm_images_fixed_250703/ai_farm_images_fixed/{'invalid' if case['csv_ground_truth'] == 'FALSE_POSITIVE' else 'valid'}/{case['case_number']}_source_{'invalid' if case['csv_ground_truth'] == 'FALSE_POSITIVE' else 'valid'}.JPEG"
            cropped_img = f"/home/<USER>/VALO_AI-FARM_2025/ai_farm_images_fixed_250703/ai_farm_images_fixed/{'invalid' if case['csv_ground_truth'] == 'FALSE_POSITIVE' else 'valid'}/{case['case_number']}_cropped_{'invalid' if case['csv_ground_truth'] == 'FALSE_POSITIVE' else 'valid'}.JPEG"
            
            html += f"""
            <div class="case {case_class}" data-type="{case_type}" data-index="{i}" style="{'display:none' if i > 0 else ''}">
                <h2>Case: {case['case_number']} {'⚠️ CRITICAL' if is_critical else ''}</h2>
                
                <div class="metrics-grid">
                    <div class="metric-item">
                        <strong>Ground Truth</strong><br>
                        {case['csv_ground_truth']}
                    </div>
                    <div class="metric-item">
                        <strong>VLM Prediction</strong><br>
                        {case['vlm_prediction']}
                    </div>
                    <div class="metric-item">
                        <strong>FP Likelihood</strong><br>
                        {case['metrics']['fp_likelihood']}%
                    </div>
                    <div class="metric-item">
                        <strong>Person Present</strong><br>
                        {case['metrics']['person_present']}
                    </div>
                    <div class="metric-item">
                        <strong>PPE Status</strong><br>
                        {case['metrics']['ppe_compliance']}
                    </div>
                </div>

                <div class="images">
                    <div class="image-container">
                        <h4>Source Image</h4>
                        <img src="file://{source_img}" alt="Source">
                    </div>
                    <div class="image-container">
                        <h4>Cropped Image</h4>
                        <img src="file://{cropped_img}" alt="Cropped">
                    </div>
                </div>

                <h3>VLM Description</h3>
                <div class="description">{case['description_preview']}</div>

                <h3>Analysis</h3>
                <p><strong>Why this matters:</strong> 
                {('This is a real safety violation that VLM would incorrectly filter out!' if is_critical else 
                  'This is a false positive that VLM failed to identify and filter.')}
                </p>
            </div>
"""
        
        html += """
        </div>
    </div>

    <script>
        let currentIndex = 0;
        let filteredCases = Array.from(document.querySelectorAll('.case'));
        let currentFilter = 'all';

        function showCase(index) {
            filteredCases.forEach((c, i) => {
                c.style.display = i === index ? 'block' : 'none';
            });
            document.getElementById('case-counter').textContent = 
                `Case ${index + 1} of ${filteredCases.length}`;
        }

        function nextCase() {
            if (currentIndex < filteredCases.length - 1) {
                currentIndex++;
                showCase(currentIndex);
            }
        }

        function previousCase() {
            if (currentIndex > 0) {
                currentIndex--;
                showCase(currentIndex);
            }
        }

        function filterCases(type) {
            currentFilter = type;
            const allCases = Array.from(document.querySelectorAll('.case'));
            
            if (type === 'all') {
                filteredCases = allCases;
            } else {
                filteredCases = allCases.filter(c => c.dataset.type === type);
            }
            
            currentIndex = 0;
            showCase(0);
            
            // Update button states
            document.querySelectorAll('.filter-buttons button').forEach(b => {
                b.classList.remove('active');
            });
            event.target.classList.add('active');
        }

        // Keyboard navigation
        document.addEventListener('keydown', (e) => {
            if (e.key === 'ArrowLeft') previousCase();
            if (e.key === 'ArrowRight') nextCase();
        });
    </script>
</body>
</html>
"""
        
        # Save the webpage
        with open('vlm_disagreement_cases.html', 'w', encoding='utf-8') as f:
            f.write(html)
            
        print(f"\n📄 Webpage created: vlm_disagreement_cases.html")
        print(f"   Shows {len(self.disagreements)} disagreement cases")
        print(f"   - {self.analysis_results['critical_errors']} critical errors")
        print(f"   - {self.analysis_results['missed_fps']} missed FPs")
    
    def save_results(self):
        """Save analysis results"""
        # Save detailed report
        with open('vlm_1250_disagreements_report.md', 'w', encoding='utf-8') as f:
            f.write(self.generate_disagreement_report())
            
        # Save JSON data
        results = {
            'analysis_timestamp': datetime.now().isoformat(),
            'summary': self.analysis_results,
            'disagreements': [
                {
                    'case_number': d['case_number'],
                    'ground_truth': d['csv_ground_truth'],
                    'vlm_prediction': d['vlm_prediction'],
                    'is_critical': d['vlm_prediction'] == 'FALSE_POSITIVE' and d['csv_ground_truth'] == 'TRUE_POSITIVE',
                    'metrics': d['metrics']
                }
                for d in self.disagreements
            ]
        }
        
        with open('vlm_1250_disagreements.json', 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2)
            
        print(f"\n✅ Analysis Complete!")
        print(f"\n📊 Final Results:")
        print(f"   - Total cases analyzed: {self.analysis_results['total_cases']}")
        print(f"   - VLM Accuracy: {self.analysis_results['vlm_accuracy']:.1f}%")
        print(f"   - Critical Errors: {self.analysis_results['critical_errors']} (must be 0!)")
        print(f"   - Missed FPs: {self.analysis_results['missed_fps']}")
        print(f"\n📁 Files created:")
        print(f"   - vlm_1250_disagreements_report.md")
        print(f"   - vlm_1250_disagreements.json")
        print(f"   - vlm_disagreement_cases.html")

if __name__ == "__main__":
    analyzer = VLMDisagreementAnalyzer()
    analyzer.analyze_all_cases()
    analyzer.save_results()
    analyzer.create_filtered_webpage()