#!/usr/bin/env python3
"""
Quick test of auto-learning system
"""

import json
import os

print("Testing auto-learning setup...")

# Test 1: Load prompt
try:
    with open('FINAL_PRODUCTION_PROMPT.txt', 'r') as f:
        prompt = f.read()
    print(f"✓ Loaded prompt: {len(prompt)} characters")
except Exception as e:
    print(f"✗ Failed to load prompt: {e}")

# Test 2: Load data
try:
    with open('valo_batch_round3_complete.json', 'r') as f:
        data = json.load(f)
    cases = data.get('results', [])
    print(f"✓ Loaded data: {len(cases)} cases")
    
    # Show first case
    if cases:
        case = cases[0]
        print(f"\nFirst case:")
        print(f"  Case number: {case.get('case_number')}")
        print(f"  Source image: {case.get('source_image')}")
        print(f"  Cropped image: {case.get('cropped_image')}")
        print(f"  Is FP: {case.get('is_false_positive')}")
except Exception as e:
    print(f"✗ Failed to load data: {e}")

# Test 3: Check VLM endpoint
try:
    import requests
    url = "http://100.106.127.35:9500/v1/chat/completions"
    print(f"\n✓ VLM endpoint configured: {url}")
except Exception as e:
    print(f"✗ Failed to import requests: {e}")

print("\nSetup test complete.")