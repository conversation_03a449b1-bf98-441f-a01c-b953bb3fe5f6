#!/usr/bin/env python3
"""
Continue testing all 1250 cases in background
Monitor and resume automatically
"""

import subprocess
import time
import json
import os
from datetime import datetime

def monitor_and_continue():
    """Monitor progress and continue testing"""
    
    print("Starting continuous 1250 case testing...")
    print("This will run until all cases are complete")
    
    while True:
        try:
            # Check current progress
            if os.path.exists('all_1250_progress.json'):
                with open('all_1250_progress.json', 'r') as f:
                    progress = json.load(f)
                    processed = progress.get('processed', 0)
                    total = progress.get('total', 1250)
                    success_rate = progress.get('success_rate', 0)
                    
                    print(f"\nCurrent progress: {processed}/{total} ({processed/total*100:.1f}%)")
                    print(f"Success rate: {success_rate:.1f}%")
                    
                    if processed >= total:
                        print("✓ ALL 1250 CASES COMPLETED!")
                        break
            
            # Continue testing
            print(f"Continuing test from current position...")
            result = subprocess.run(
                ['python3', 'test_all_1250_robust_final.py'],
                timeout=600,  # 10 minute chunks
                capture_output=False
            )
            
            print(f"Test session completed with return code: {result.returncode}")
            
        except subprocess.TimeoutExpired:
            print("Test session timeout - restarting...")
            continue
        except KeyboardInterrupt:
            print("\nStopped by user")
            break
        except Exception as e:
            print(f"Error: {e}")
            time.sleep(30)  # Wait before retry
            continue
    
    # Generate final report
    if os.path.exists('all_1250_progress.json'):
        with open('all_1250_progress.json', 'r') as f:
            final_data = json.load(f)
            
        print(f"\n" + "="*60)
        print("FINAL RESULTS FOR ALL 1250 CASES")
        print("="*60)
        
        processed = final_data.get('processed', 0)
        results = final_data.get('results', [])
        successful = [r for r in results if r['status'] == 'success']
        
        if successful:
            correct = sum(1 for r in successful if r.get('correct', False))
            fp_cases = [r for r in successful if r.get('actual_fp', True)]
            fp_detected = sum(1 for r in fp_cases if r.get('predicted_fp', False))
            
            accuracy = (correct / len(successful)) * 100
            fp_rate = (fp_detected / len(fp_cases)) * 100 if fp_cases else 0
            
            print(f"Cases processed: {processed}/1250")
            print(f"Successful tests: {len(successful)}")
            print(f"Overall accuracy: {accuracy:.1f}%")
            print(f"FP detection rate: {fp_rate:.1f}%")
            
            print(f"\nProduction estimates:")
            print(f"  - Optimistic: {fp_rate * 0.85:.1f}%")
            print(f"  - Realistic: {fp_rate * 0.80:.1f}%")
            print(f"  - Conservative: {fp_rate * 0.75:.1f}%")
            
            meets_target = fp_rate * 0.80 >= 70
            print(f"\n70% target: {'✓ ACHIEVED' if meets_target else '○ Not quite reached'}")

if __name__ == "__main__":
    monitor_and_continue()