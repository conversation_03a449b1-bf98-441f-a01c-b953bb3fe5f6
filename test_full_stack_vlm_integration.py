#!/usr/bin/env python3
"""
Full Stack VLM Integration Test
Tests the complete integration from frontend through backend to external VLM API
"""

import requests
import json
import time
from pathlib import Path
import base64
from PIL import Image
import io

def create_test_image():
    """Create a simple test image for analysis"""
    # Create a simple test image with some content
    img = Image.new('RGB', (200, 200), color='red')
    
    # Save to bytes
    img_bytes = io.BytesIO()
    img.save(img_bytes, format='PNG')
    img_bytes.seek(0)
    
    return img_bytes.getvalue()

def test_frontend_backend_connectivity():
    """Test that frontend can reach backend"""
    print("🔍 Testing Frontend-Backend Connectivity")
    print("-" * 50)
    
    try:
        # Test backend health endpoint
        response = requests.get("http://localhost:8001/health", timeout=10)
        if response.status_code == 200:
            health_data = response.json()
            print(f"✅ Backend Health: {health_data.get('status', 'unknown')}")
            print(f"   - VLM Status: {health_data.get('vlm_status', 'unknown')}")
            print(f"   - Database Status: {health_data.get('database_status', 'unknown')}")
            return True
        else:
            print(f"❌ Backend health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Backend connectivity failed: {e}")
        return False

def test_vlm_endpoints():
    """Test VLM-specific endpoints"""
    print("\n🔍 Testing VLM Endpoints")
    print("-" * 50)
    
    # Test VLM health
    try:
        response = requests.get("http://localhost:8001/api/v1/vlm/health", timeout=10)
        if response.status_code == 200:
            vlm_health = response.json()
            print(f"✅ VLM Health: {vlm_health.get('status', 'unknown')}")
            print(f"   - Response Time: {vlm_health.get('response_time_ms', 'unknown')}ms")
        else:
            print(f"❌ VLM health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ VLM health check failed: {e}")
        return False
    
    # Test VLM config
    try:
        response = requests.get("http://localhost:8001/api/v1/vlm/config", timeout=10)
        if response.status_code == 200:
            vlm_config = response.json()
            print(f"✅ VLM Config:")
            print(f"   - Model: {vlm_config.get('model_name', 'unknown')}")
            print(f"   - API URL: {vlm_config.get('api_base_url', 'unknown')}")
            print(f"   - Has API Key: {vlm_config.get('has_api_key', False)}")
        else:
            print(f"❌ VLM config check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ VLM config check failed: {e}")
        return False
    
    return True

def test_vlm_image_analysis():
    """Test VLM image analysis endpoint"""
    print("\n🔍 Testing VLM Image Analysis")
    print("-" * 50)
    
    try:
        # Create test image
        test_image_data = create_test_image()
        
        # Prepare multipart form data
        files = {
            'image': ('test_image.png', test_image_data, 'image/png')
        }
        data = {
            'case_number': 'FULL_STACK_TEST_001',
            'custom_prompt': 'Analyze this image for safety violations'
        }
        
        print("📤 Uploading test image for VLM analysis...")
        start_time = time.time()
        
        response = requests.post(
            "http://localhost:8001/api/v1/vlm/analyze",
            files=files,
            data=data,
            timeout=60
        )
        
        end_time = time.time()
        processing_time = (end_time - start_time) * 1000
        
        print(f"📥 Response Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ VLM Analysis successful!")
            print(f"📊 Analysis Result:")
            print(f"   - Detection Type: {result.get('detection_type', 'unknown')}")
            print(f"   - False Positive Likelihood: {result.get('false_positive_likelihood', 0)*100:.0f}%")
            print(f"   - True Violation Likelihood: {result.get('true_violation_likelihood', 0)*100:.0f}%")
            print(f"   - Recommendation: {result.get('recommendation', 'unknown')}")
            print(f"   - Confidence Score: {result.get('confidence_score', 0)}")
            print(f"   - Processing Time: {processing_time:.0f}ms")
            
            if result.get('reasoning'):
                print(f"🤖 Reasoning: {result['reasoning'][:200]}...")
            
            return True
        else:
            print(f"❌ VLM analysis failed: {response.status_code}")
            if response.text:
                print(f"   Error: {response.text[:200]}")
            return False
            
    except Exception as e:
        print(f"❌ VLM analysis failed: {e}")
        return False

def test_frontend_accessibility():
    """Test that frontend is accessible"""
    print("\n🔍 Testing Frontend Accessibility")
    print("-" * 50)
    
    try:
        response = requests.get("http://localhost:3000", timeout=10)
        if response.status_code == 200 and "AI-FARM" in response.text:
            print("✅ Frontend is accessible")
            print("   - Title: AI-FARM - False Positive Alert Reduction")
            return True
        else:
            print(f"❌ Frontend accessibility failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Frontend accessibility failed: {e}")
        return False

def main():
    """Run full stack integration tests"""
    print("🚀 VALO AI-FARM Full Stack VLM Integration Test")
    print("=" * 60)
    
    tests = [
        ("Frontend Accessibility", test_frontend_accessibility),
        ("Frontend-Backend Connectivity", test_frontend_backend_connectivity),
        ("VLM Endpoints", test_vlm_endpoints),
        ("VLM Image Analysis", test_vlm_image_analysis),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 Full Stack Integration Test Results:")
    
    all_passed = True
    for test_name, passed in results.items():
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{status} {test_name}")
        if not passed:
            all_passed = False
    
    if all_passed:
        print("\n🎉 SUCCESS! Full stack VLM integration is working perfectly!")
        print("✅ Frontend accessible on http://localhost:3000")
        print("✅ Backend API functional on http://localhost:8001")
        print("✅ VLM integration working with external server **************:9500")
        print("✅ End-to-end image analysis pipeline operational")
        print("\n🚀 PRODUCTION READY: The VALO AI-FARM system is ready for deployment!")
    else:
        print("\n❌ Some tests failed. Please check the issues above.")
    
    return all_passed

if __name__ == "__main__":
    main()
