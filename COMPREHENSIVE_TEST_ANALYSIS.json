{"analysis_date": "2025-07-24T10:08:45.724776", "dataset_info": {"total_cases": 1250, "fp_cases": 1207, "valid_cases": 43, "fp_rate": 96.56}, "testing_summary": {"approaches_tested": 30, "successful_approaches": 3, "sample_sizes": "200-300 per approach", "total_tests": "~6000-9000 cases"}, "top_performers": [{"name": "alert_fatigue_prevention", "test_fp": 100.0, "test_valid": 100.0, "production_estimate_15pct": 85.0, "production_estimate_20pct": 80.0}, {"name": "assumption_based", "test_fp": 86.7, "test_valid": 100.0, "production_estimate_15pct": 73.69500000000001, "production_estimate_20pct": 69.36}, {"name": "worksite_reality", "test_fp": 75.0, "test_valid": 100.0, "production_estimate_15pct": 63.75, "production_estimate_20pct": 60.0}], "recommendation": {"primary": "assumption_based", "alternative": "alert_fatigue_prevention", "ensemble": "optional for +3-5% improvement"}}