<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI-FARM - Professional False Positive Reduction System</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            /* Professional Surveillance Color Scheme */
            --bg-primary: #0a0a0a;
            --bg-secondary: #1a1a1a;
            --bg-tertiary: #2d2d2d;
            --bg-card: #252525;
            --accent-blue: #00d4ff;
            --accent-green: #00ff88;
            --accent-red: #ff4757;
            --accent-orange: #ffa502;
            --text-primary: #ffffff;
            --text-secondary: #cccccc;
            --text-muted: #888888;
            --border: #404040;
            --shadow: 0 4px 20px rgba(0, 212, 255, 0.1);
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: var(--bg-primary);
            color: var(--text-primary);
            line-height: 1.6;
        }

        /* Global Components */
        .container {
            max-width: 1920px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            font-weight: 600;
            cursor: pointer;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--accent-blue), #0056b3);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow);
        }

        .btn-success {
            background: var(--accent-green);
            color: #000;
        }

        .btn-danger {
            background: var(--accent-red);
            color: white;
        }

        .card {
            background: var(--bg-card);
            border: 1px solid var(--border);
            border-radius: 8px;
            padding: 24px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        }

        .metric-card {
            background: var(--bg-tertiary);
            border: 1px solid var(--border);
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .metric-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: var(--accent-blue);
        }

        .metric-value {
            font-size: 36px;
            font-weight: 700;
            margin-bottom: 8px;
        }

        .metric-label {
            font-size: 14px;
            color: var(--text-secondary);
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .metric-change {
            font-size: 12px;
            margin-top: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
        }

        .positive { color: var(--accent-green); }
        .negative { color: var(--accent-red); }
        .warning { color: var(--accent-orange); }

        /* Header Navigation */
        .header {
            background: var(--bg-secondary);
            border-bottom: 1px solid var(--border);
            padding: 0;
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .nav {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            height: 70px;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 20px;
            font-weight: 700;
            color: var(--accent-blue);
        }

        .logo-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, var(--accent-blue), #0056b3);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: white;
        }

        .nav-links {
            display: flex;
            gap: 0;
            list-style: none;
        }

        .nav-link {
            padding: 25px 20px;
            color: var(--text-secondary);
            text-decoration: none;
            transition: all 0.3s ease;
            border-bottom: 3px solid transparent;
        }

        .nav-link:hover, .nav-link.active {
            color: var(--text-primary);
            background: var(--bg-tertiary);
            border-bottom-color: var(--accent-blue);
        }

        .status-indicators {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        .status-dot.online { background: var(--accent-green); }
        .status-dot.warning { background: var(--accent-orange); }
        .status-dot.offline { background: var(--accent-red); }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        /* Page Specific Styles */
        .page {
            display: none;
            min-height: calc(100vh - 70px);
        }

        .page.active {
            display: block;
        }

        /* Landing Page */
        .hero {
            background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
            padding: 80px 0;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 50% 50%, rgba(0, 212, 255, 0.1) 0%, transparent 70%);
        }

        .hero-content {
            position: relative;
            z-index: 2;
        }

        .hero h1 {
            font-size: 48px;
            margin-bottom: 20px;
            background: linear-gradient(135deg, var(--accent-blue), var(--accent-green));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .crisis-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 60px 0;
        }

        .crisis-stat {
            background: var(--bg-card);
            padding: 30px;
            border-radius: 12px;
            border: 1px solid var(--border);
            position: relative;
        }

        .crisis-stat.critical::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--accent-red);
        }

        .crisis-number {
            font-size: 42px;
            font-weight: 700;
            color: var(--accent-red);
            margin-bottom: 10px;
        }

        /* Upload Center */
        .upload-zone {
            border: 2px dashed var(--border);
            border-radius: 12px;
            padding: 60px;
            text-align: center;
            background: var(--bg-secondary);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .upload-zone:hover {
            border-color: var(--accent-blue);
            background: var(--bg-tertiary);
        }

        .upload-icon {
            font-size: 48px;
            color: var(--accent-blue);
            margin-bottom: 20px;
        }

        .file-list {
            background: var(--bg-card);
            border-radius: 8px;
            overflow: hidden;
        }

        .file-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 16px 20px;
            border-bottom: 1px solid var(--border);
        }

        .file-item:last-child {
            border-bottom: none;
        }

        .file-status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
        }

        .status-processing { background: var(--accent-orange); color: #000; }
        .status-complete { background: var(--accent-green); color: #000; }
        .status-error { background: var(--accent-red); color: white; }

        /* Processing Monitor */
        .processing-pipeline {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 20px;
            margin-bottom: 40px;
        }

        .pipeline-stage {
            background: var(--bg-card);
            padding: 24px;
            border-radius: 8px;
            text-align: center;
            position: relative;
            border: 2px solid var(--border);
        }

        .pipeline-stage.active {
            border-color: var(--accent-blue);
            background: var(--bg-tertiary);
        }

        .pipeline-stage.complete {
            border-color: var(--accent-green);
        }

        .stage-icon {
            font-size: 32px;
            margin-bottom: 16px;
            display: block;
        }

        .progress-bar {
            background: var(--bg-secondary);
            height: 8px;
            border-radius: 4px;
            overflow: hidden;
            margin: 16px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--accent-blue), var(--accent-green));
            transition: width 0.3s ease;
        }

        /* Surveillance Dashboard */
        .surveillance-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        .alert-feed {
            background: var(--bg-card);
            border-radius: 8px;
            overflow: hidden;
        }

        .feed-header {
            background: var(--bg-secondary);
            padding: 16px 20px;
            display: flex;
            justify-content: between;
            align-items: center;
            border-bottom: 1px solid var(--border);
        }

        .live-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
            color: var(--accent-green);
            font-weight: 600;
        }

        .alert-list {
            max-height: 600px;
            overflow-y: auto;
        }

        .alert-item {
            display: flex;
            padding: 16px 20px;
            border-bottom: 1px solid var(--border);
            transition: background 0.2s ease;
        }

        .alert-item:hover {
            background: var(--bg-tertiary);
        }

        .alert-thumbnail {
            width: 80px;
            height: 60px;
            background: var(--bg-secondary);
            border-radius: 6px;
            margin-right: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            color: var(--text-muted);
        }

        .alert-info {
            flex: 1;
        }

        .alert-id {
            font-weight: 600;
            margin-bottom: 4px;
        }

        .alert-meta {
            font-size: 12px;
            color: var(--text-secondary);
            margin-bottom: 8px;
        }

        .alert-badges {
            display: flex;
            gap: 8px;
            margin-left: auto;
            flex-direction: column;
            align-items: flex-end;
        }

        .badge {
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .badge-filtered { background: var(--accent-green); color: #000; }
        .badge-review { background: var(--accent-orange); color: #000; }
        .badge-urgent { background: var(--accent-red); color: white; }

        .confidence-score {
            font-size: 11px;
            color: var(--text-muted);
            margin-top: 4px;
        }

        /* Terminal Performance */
        .terminal-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin-bottom: 30px;
        }

        .terminal-card {
            background: var(--bg-card);
            border-radius: 8px;
            padding: 24px;
            border: 1px solid var(--border);
            position: relative;
        }

        .terminal-card.worst::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: var(--accent-red);
        }

        .terminal-card.best::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: var(--accent-green);
        }

        /* Analytics Charts */
        .chart-container {
            background: var(--bg-card);
            border-radius: 8px;
            padding: 24px;
            height: 400px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }

        .donut-chart {
            width: 200px;
            height: 200px;
            border-radius: 50%;
            background: conic-gradient(
                var(--accent-green) 0deg 252deg,
                var(--accent-orange) 252deg 360deg
            );
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }

        .donut-inner {
            width: 120px;
            height: 120px;
            background: var(--bg-card);
            border-radius: 50%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        .chart-legend {
            position: absolute;
            right: 20px;
            top: 20px;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 8px;
            font-size: 12px;
        }

        .legend-color {
            width: 12px;
            height: 12px;
            border-radius: 2px;
        }

        /* ROI Calculator */
        .calculator-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
        }

        .input-group {
            margin-bottom: 20px;
        }

        .input-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: var(--text-secondary);
        }

        .input-field {
            width: 100%;
            padding: 12px 16px;
            background: var(--bg-secondary);
            border: 1px solid var(--border);
            border-radius: 6px;
            color: var(--text-primary);
            font-size: 16px;
        }

        .input-field:focus {
            outline: none;
            border-color: var(--accent-blue);
            box-shadow: 0 0 0 3px rgba(0, 212, 255, 0.1);
        }

        .roi-results {
            background: var(--bg-secondary);
            border-radius: 8px;
            padding: 24px;
        }

        .roi-highlight {
            font-size: 32px;
            font-weight: 700;
            color: var(--accent-green);
            text-align: center;
            padding: 20px;
            background: var(--bg-card);
            border-radius: 8px;
            margin-bottom: 20px;
        }

        /* Alert Review Interface */
        .review-layout {
            display: grid;
            grid-template-columns: 1fr 400px;
            gap: 20px;
            height: calc(100vh - 140px);
        }

        .image-viewer {
            background: var(--bg-card);
            border-radius: 8px;
            padding: 20px;
            display: flex;
            flex-direction: column;
        }

        .review-image {
            flex: 1;
            background: var(--bg-secondary);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 20px;
            min-height: 400px;
        }

        .review-controls {
            display: flex;
            gap: 12px;
            justify-content: center;
        }

        .review-panel {
            background: var(--bg-card);
            border-radius: 8px;
            padding: 20px;
            overflow-y: auto;
        }

        .vlm-analysis {
            background: var(--bg-secondary);
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 20px;
        }

        .confidence-meter {
            background: var(--bg-primary);
            height: 20px;
            border-radius: 10px;
            overflow: hidden;
            margin: 12px 0;
            position: relative;
        }

        .confidence-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--accent-red), var(--accent-orange), var(--accent-green));
            transition: width 0.3s ease;
        }

        .confidence-label {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 12px;
            font-weight: 600;
            color: var(--text-primary);
        }

        /* Responsive Design */
        @media (max-width: 1200px) {
            .surveillance-grid,
            .calculator-grid,
            .review-layout {
                grid-template-columns: 1fr;
            }
            
            .processing-pipeline {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .terminal-grid {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 768px) {
            .nav-links {
                display: none;
            }
            
            .hero h1 {
                font-size: 32px;
            }
            
            .crisis-stats {
                grid-template-columns: 1fr;
            }
            
            .processing-pipeline {
                grid-template-columns: 1fr;
            }
        }

        /* Utility Classes */
        .text-center { text-align: center; }
        .text-right { text-align: right; }
        .mb-20 { margin-bottom: 20px; }
        .mb-30 { margin-bottom: 30px; }
        .mt-20 { margin-top: 20px; }
        .grid { display: grid; }
        .flex { display: flex; }
        .items-center { align-items: center; }
        .justify-between { justify-content: space-between; }
        .gap-20 { gap: 20px; }
    </style>
</head>
<body>
    <!-- Header Navigation -->
    <header class="header">
        <nav class="nav">
            <div class="logo">
                <div class="logo-icon">AF</div>
                <div>
                    <div>AI-FARM</div>
                    <div style="font-size: 12px; color: var(--text-secondary);">False Positive Reduction</div>
                </div>
            </div>
            
            <ul class="nav-links">
                <li><a href="#" class="nav-link active" data-page="landing">Home</a></li>
                <li><a href="#" class="nav-link" data-page="upload">Upload</a></li>
                <li><a href="#" class="nav-link" data-page="monitor">Monitor</a></li>
                <li><a href="#" class="nav-link" data-page="dashboard">Dashboard</a></li>
                <li><a href="#" class="nav-link" data-page="analytics">Analytics</a></li>
                <li><a href="#" class="nav-link" data-page="roi">ROI</a></li>
                <li><a href="#" class="nav-link" data-page="insights">Insights</a></li>
                <li><a href="#" class="nav-link" data-page="review">Review</a></li>
                <li><a href="#" class="nav-link" data-page="learning">Learning</a></li>
            </ul>
            
            <div class="status-indicators">
                <div style="display: flex; align-items: center; gap: 8px;">
                    <div class="status-dot online"></div>
                    <span style="font-size: 12px;">VLM Online</span>
                </div>
                <div style="display: flex; align-items: center; gap: 8px;">
                    <div class="status-dot online"></div>
                    <span style="font-size: 12px;">VALO Connected</span>
                </div>
            </div>
        </nav>
    </header>

    <!-- Page 1: Landing Page -->
    <div id="landing" class="page active">
        <section class="hero">
            <div class="container">
                <div class="hero-content">
                    <h1>End the False Positive Crisis</h1>
                    <p style="font-size: 20px; margin-bottom: 40px; color: var(--text-secondary);">
                        AI-powered analysis reduces safety alert false positives by 70%, saving $351K annually
                    </p>
                    <a href="#" class="btn btn-primary" onclick="showPage('upload')">Start Live Demo →</a>
                </div>
            </div>
        </section>

        <section style="padding: 60px 0; background: var(--bg-secondary);">
            <div class="container">
                <h2 style="text-align: center; margin-bottom: 40px; font-size: 36px;">The Current Crisis</h2>
                <div class="crisis-stats">
                    <div class="crisis-stat critical">
                        <div class="crisis-number">17,268</div>
                        <div>Monthly VALO Alerts</div>
                        <div style="color: var(--text-muted); font-size: 14px; margin-top: 8px;">+8% monthly growth</div>
                    </div>
                    <div class="crisis-stat critical">
                        <div class="crisis-number">97%</div>
                        <div>False Positive Rate</div>
                        <div style="color: var(--text-muted); font-size: 14px; margin-top: 8px;">Only 490 real violations</div>
                    </div>
                    <div class="crisis-stat critical">
                        <div class="crisis-number">$517K</div>
                        <div>Annual Waste</div>
                        <div style="color: var(--text-muted); font-size: 14px; margin-top: 8px;">862 hours/month reviewing garbage</div>
                    </div>
                    <div class="crisis-stat critical">
                        <div class="crisis-number">3 min</div>
                        <div>Per Alert Review</div>
                        <div style="color: var(--text-muted); font-size: 14px; margin-top: 8px;">Human reviewer burnout</div>
                    </div>
                </div>

                <div style="text-align: center; margin-top: 60px;">
                    <h3 style="margin-bottom: 20px; color: var(--accent-blue);">AI-FARM Solution Impact</h3>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px;">
                        <div class="metric-card">
                            <div class="metric-value positive">70%</div>
                            <div class="metric-label">False Positive Reduction</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value positive">$351K</div>
                            <div class="metric-label">Annual Savings</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value positive">3.7</div>
                            <div class="metric-label">Months Payback</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value positive">156%</div>
                            <div class="metric-label">First Year ROI</div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <!-- Page 2: Upload Center -->
    <div id="upload" class="page">
        <div class="container" style="padding: 40px 20px;">
            <h1 style="margin-bottom: 30px;">Upload Your VALO Data</h1>
            
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin-bottom: 40px;">
                <div class="card">
                    <h3 style="margin-bottom: 20px; color: var(--accent-blue);">📊 VALO Case Data (Required)</h3>
                    <div class="upload-zone" onclick="document.getElementById('csvFile').click()">
                        <div class="upload-icon">📄</div>
                        <h4>Upload CSV File</h4>
                        <p style="color: var(--text-secondary); margin-top: 8px;">
                            Select your VALO case data CSV file<br>
                            Maximum size: 10 MB
                        </p>
                        <input type="file" id="csvFile" accept=".csv" style="display: none;">
                    </div>
                </div>

                <div class="card">
                    <h3 style="margin-bottom: 20px; color: var(--accent-blue);">🖼️ Violation Images (Optional)</h3>
                    <div class="upload-zone" onclick="document.getElementById('zipFile').click()">
                        <div class="upload-icon">📦</div>
                        <h4>Upload ZIP File</h4>
                        <p style="color: var(--text-secondary); margin-top: 8px;">
                            Select ZIP containing violation images<br>
                            Maximum size: 500 MB
                        </p>
                        <input type="file" id="zipFile" accept=".zip" style="display: none;">
                    </div>
                </div>
            </div>

            <div class="card mb-30">
                <h3 style="margin-bottom: 20px;">Processing Configuration</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;">
                    <div class="input-group">
                        <label class="input-label">Processing Priority</label>
                        <select class="input-field">
                            <option>Standard (5-10 min)</option>
                            <option>High Priority (2-5 min)</option>
                            <option>Batch (15-30 min)</option>
                        </select>
                    </div>
                    <div class="input-group">
                        <label class="input-label">False Positive Threshold</label>
                        <select class="input-field">
                            <option>70% (Recommended)</option>
                            <option>80% (Conservative)</option>
                            <option>60% (Aggressive)</option>
                        </select>
                    </div>
                    <div class="input-group">
                        <label class="input-label">Learning Mode</label>
                        <select class="input-field">
                            <option>Enabled (Recommended)</option>
                            <option>Disabled</option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="card">
                <h3 style="margin-bottom: 20px;">Upload History</h3>
                <div class="file-list">
                    <div class="file-item">
                        <div>
                            <div style="font-weight: 600;">VALO_Cases_Nov2024.csv</div>
                            <div style="font-size: 12px; color: var(--text-secondary);">2,847 records • Uploaded 5 minutes ago</div>
                        </div>
                        <div class="file-status status-processing">Processing</div>
                    </div>
                    <div class="file-item">
                        <div>
                            <div style="font-weight: 600;">Violation_Images_Oct2024.zip</div>
                            <div style="font-size: 12px; color: var(--text-secondary);">1,205 images • Uploaded 2 hours ago</div>
                        </div>
                        <div class="file-status status-complete">Complete</div>
                    </div>
                    <div class="file-item">
                        <div>
                            <div style="font-weight: 600;">VALO_Cases_Oct2024.csv</div>
                            <div style="font-size: 12px; color: var(--text-secondary);">3,156 records • Uploaded yesterday</div>
                        </div>
                        <div class="file-status status-complete">Complete</div>
                    </div>
                </div>
            </div>

            <div style="text-align: center; margin-top: 40px;">
                <button class="btn btn-primary" onclick="showPage('monitor')">Start Processing →</button>
            </div>
        </div>
    </div>

    <!-- Page 3: Real-Time Processing Monitor -->
    <div id="monitor" class="page">
        <div class="container" style="padding: 40px 20px;">
            <h1 style="margin-bottom: 30px;">Live Processing Monitor</h1>
            
            <div class="processing-pipeline">
                <div class="pipeline-stage complete">
                    <div class="stage-icon">📝</div>
                    <h4>Data Validation</h4>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 100%;"></div>
                    </div>
                    <div style="font-size: 12px; color: var(--accent-green);">✓ Complete</div>
                </div>
                <div class="pipeline-stage complete">
                    <div class="stage-icon">🖼️</div>
                    <h4>Image Analysis</h4>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 100%;"></div>
                    </div>
                    <div style="font-size: 12px; color: var(--accent-green);">✓ Complete</div>
                </div>
                <div class="pipeline-stage active">
                    <div class="stage-icon">🤖</div>
                    <h4>VLM Processing</h4>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 73%;"></div>
                    </div>
                    <div style="font-size: 12px; color: var(--accent-blue);">73% Complete</div>
                </div>
                <div class="pipeline-stage">
                    <div class="stage-icon">📊</div>
                    <h4>Results</h4>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 0%;"></div>
                    </div>
                    <div style="font-size: 12px; color: var(--text-muted);">Pending</div>
                </div>
            </div>

            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 40px;">
                <div class="metric-card">
                    <div class="metric-value">2,078</div>
                    <div class="metric-label">Alerts Processed</div>
                    <div class="metric-change positive">↗ 847ms avg speed</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">1,456</div>
                    <div class="metric-label">False Positives Filtered</div>
                    <div class="metric-change positive">↗ 70.1% filter rate</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">622</div>
                    <div class="metric-label">Require Review</div>
                    <div class="metric-change positive">↗ 29.9% of total</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">769</div>
                    <div class="metric-label">Remaining Queue</div>
                    <div class="metric-change">⏱ ~8 minutes remaining</div>
                </div>
            </div>

            <div class="card">
                <h3 style="margin-bottom: 20px;">Live Processing Log</h3>
                <div style="background: var(--bg-primary); padding: 20px; border-radius: 6px; font-family: monospace; font-size: 14px; height: 300px; overflow-y: auto;">
                    <div style="color: var(--accent-green);">[14:23:45] ✓ Processing alert VALO_2078 - Structure misidentified (94% confidence)</div>
                    <div style="color: var(--accent-green);">[14:23:44] ✓ Processing alert VALO_2077 - Proper PPE detected (91% confidence)</div>
                    <div style="color: var(--accent-orange);">[14:23:43] ⚠ Processing alert VALO_2076 - Uncertain case (45% confidence) → Human review</div>
                    <div style="color: var(--accent-green);">[14:23:42] ✓ Processing alert VALO_2075 - Crane structure detected (96% confidence)</div>
                    <div style="color: var(--accent-green);">[14:23:41] ✓ Processing alert VALO_2074 - Equipment confusion (88% confidence)</div>
                    <div style="color: var(--accent-blue);">[14:23:40] ℹ VLM processing batch 42/58...</div>
                    <div style="color: var(--accent-green);">[14:23:39] ✓ Processing alert VALO_2073 - Vessel structure (93% confidence)</div>
                    <div style="color: var(--accent-orange);">[14:23:38] ⚠ Processing alert VALO_2072 - Genuine violation suspected (28% FP) → Human review</div>
                </div>
            </div>

            <div style="text-align: center; margin-top: 40px;">
                <button class="btn btn-primary" onclick="showPage('dashboard')">View Live Dashboard →</button>
            </div>
        </div>
    </div>

    <!-- Page 4: Surveillance Dashboard -->
    <div id="dashboard" class="page">
        <div class="container" style="padding: 20px;">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px;">
                <h1>Surveillance Control Dashboard</h1>
                <div class="live-indicator">
                    <div class="status-dot online"></div>
                    <span>LIVE MONITORING</span>
                </div>
            </div>

            <div class="surveillance-grid">
                <div class="alert-feed">
                    <div class="feed-header">
                        <h3>Live VALO Alert Feed</h3>
                        <div style="display: flex; gap: 20px; font-size: 12px;">
                            <span>Queue: 47 alerts</span>
                            <span>Processing: 847ms avg</span>
                        </div>
                    </div>
                    <div class="alert-list">
                        <div class="alert-item">
                            <div class="alert-thumbnail">IMG_2078</div>
                            <div class="alert-info">
                                <div class="alert-id">VALO Alert #2078</div>
                                <div class="alert-meta">Terminal P1 • QC-05 • Nov 24, 2024 14:23:45</div>
                                <div style="font-size: 12px; color: var(--text-secondary);">PPE Violation Detection</div>
                            </div>
                            <div class="alert-badges">
                                <div class="badge badge-filtered">AI Filtered</div>
                                <div class="confidence-score">94% Confidence</div>
                            </div>
                        </div>
                        
                        <div class="alert-item">
                            <div class="alert-thumbnail">IMG_2077</div>
                            <div class="alert-info">
                                <div class="alert-id">VALO Alert #2077</div>
                                <div class="alert-meta">Terminal P2 • Vessel-07 • Nov 24, 2024 14:23:44</div>
                                <div style="font-size: 12px; color: var(--text-secondary);">Structure Misidentification</div>
                            </div>
                            <div class="alert-badges">
                                <div class="badge badge-filtered">AI Filtered</div>
                                <div class="confidence-score">91% Confidence</div>
                            </div>
                        </div>

                        <div class="alert-item">
                            <div class="alert-thumbnail">IMG_2076</div>
                            <div class="alert-info">
                                <div class="alert-id">VALO Alert #2076</div>
                                <div class="alert-meta">Terminal P3 • Container-12 • Nov 24, 2024 14:23:43</div>
                                <div style="font-size: 12px; color: var(--text-secondary);">Uncertain Case</div>
                            </div>
                            <div class="alert-badges">
                                <div class="badge badge-review">Human Review</div>
                                <div class="confidence-score">45% FP Likelihood</div>
                            </div>
                        </div>

                        <div class="alert-item">
                            <div class="alert-thumbnail">IMG_2075</div>
                            <div class="alert-info">
                                <div class="alert-id">VALO Alert #2075</div>
                                <div class="alert-meta">Terminal P1 • QC-03 • Nov 24, 2024 14:23:42</div>
                                <div style="font-size: 12px; color: var(--text-secondary);">Crane Structure Detected</div>
                            </div>
                            <div class="alert-badges">
                                <div class="badge badge-filtered">AI Filtered</div>
                                <div class="confidence-score">96% Confidence</div>
                            </div>
                        </div>

                        <div class="alert-item">
                            <div class="alert-thumbnail">IMG_2074</div>
                            <div class="alert-info">
                                <div class="alert-id">VALO Alert #2074</div>
                                <div class="alert-meta">Terminal P2 • Equipment-15 • Nov 24, 2024 14:23:41</div>
                                <div style="font-size: 12px; color: var(--text-secondary);">Equipment Confusion</div>
                            </div>
                            <div class="alert-badges">
                                <div class="badge badge-filtered">AI Filtered</div>
                                <div class="confidence-score">88% Confidence</div>
                            </div>
                        </div>

                        <div class="alert-item">
                            <div class="alert-thumbnail">IMG_2073</div>
                            <div class="alert-info">
                                <div class="alert-id">VALO Alert #2073</div>
                                <div class="alert-meta">Terminal P3 • Vessel-03 • Nov 24, 2024 14:23:40</div>
                                <div style="font-size: 12px; color: var(--text-secondary);">Vessel Structure</div>
                            </div>
                            <div class="alert-badges">
                                <div class="badge badge-filtered">AI Filtered</div>
                                <div class="confidence-score">93% Confidence</div>
                            </div>
                        </div>

                        <div class="alert-item">
                            <div class="alert-thumbnail">IMG_2072</div>
                            <div class="alert-info">
                                <div class="alert-id">VALO Alert #2072</div>
                                <div class="alert-meta">Terminal P1 • Safety-Zone-A • Nov 24, 2024 14:23:39</div>
                                <div style="font-size: 12px; color: var(--accent-red);">Potential Genuine Violation</div>
                            </div>
                            <div class="alert-badges">
                                <div class="badge badge-urgent">Urgent Review</div>
                                <div class="confidence-score">28% FP Likelihood</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div>
                    <div style="display: grid; gap: 20px;">
                        <div class="metric-card">
                            <div class="metric-value">2,078</div>
                            <div class="metric-label">Processed Today</div>
                            <div class="metric-change positive">↗ +347 vs yesterday</div>
                        </div>
                        
                        <div class="metric-card">
                            <div class="metric-value positive">70.1%</div>
                            <div class="metric-label">Filter Rate</div>
                            <div class="metric-change positive">↗ Target: 70%</div>
                        </div>
                        
                        <div class="metric-card">
                            <div class="metric-value">847ms</div>
                            <div class="metric-label">Avg Processing</div>
                            <div class="metric-change positive">↗ Real-time performance</div>
                        </div>
                    </div>

                    <div class="card mt-20">
                        <h4 style="margin-bottom: 16px;">VLM Processing Pipeline</h4>
                        <div style="display: flex; flex-direction: column; gap: 12px;">
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <span style="font-size: 12px;">Image Analysis</span>
                                <span style="color: var(--accent-green); font-size: 12px;">✓ Active</span>
                            </div>
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <span style="font-size: 12px;">VLM Classification</span>
                                <span style="color: var(--accent-green); font-size: 12px;">✓ Active</span>
                            </div>
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <span style="font-size: 12px;">Confidence Scoring</span>
                                <span style="color: var(--accent-green); font-size: 12px;">✓ Active</span>
                            </div>
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <span style="font-size: 12px;">Filter Decision</span>
                                <span style="color: var(--accent-green); font-size: 12px;">✓ Active</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="terminal-grid">
                <div class="terminal-card worst">
                    <h4 style="color: var(--accent-red); margin-bottom: 12px;">Terminal P1 - Quay Cranes</h4>
                    <div style="font-size: 32px; font-weight: 700; color: var(--accent-red); margin-bottom: 8px;">98.2%</div>
                    <div style="font-size: 14px; color: var(--text-secondary);">False Positive Rate</div>
                    <div style="font-size: 12px; margin-top: 12px; color: var(--accent-red);">⚠ Needs attention - Camera QC-05 issues</div>
                </div>
                
                <div class="terminal-card">
                    <h4 style="color: var(--accent-orange); margin-bottom: 12px;">Terminal P2 - Vessel Operations</h4>
                    <div style="font-size: 32px; font-weight: 700; color: var(--accent-orange); margin-bottom: 8px;">96.7%</div>
                    <div style="font-size: 14px; color: var(--text-secondary);">False Positive Rate</div>
                    <div style="font-size: 12px; margin-top: 12px; color: var(--text-secondary);">Standard performance</div>
                </div>
                
                <div class="terminal-card best">
                    <h4 style="color: var(--accent-green); margin-bottom: 12px;">Terminal P3 - Container Yard</h4>
                    <div style="font-size: 32px; font-weight: 700; color: var(--accent-green); margin-bottom: 8px;">96.6%</div>
                    <div style="font-size: 14px; color: var(--text-secondary);">False Positive Rate</div>
                    <div style="font-size: 12px; margin-top: 12px; color: var(--accent-green);">✓ Best performance</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Page 5: Analytics Dashboard -->
    <div id="analytics" class="page">
        <div class="container" style="padding: 40px 20px;">
            <h1 style="margin-bottom: 30px;">Performance Analytics</h1>
            
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 40px;">
                <div class="metric-card">
                    <div class="metric-value">17,268</div>
                    <div class="metric-label">Monthly Alerts</div>
                    <div class="metric-change positive">↗ +8% vs last month</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value positive">12,088</div>
                    <div class="metric-label">False Positives Filtered</div>
                    <div class="metric-change positive">↗ 70% filter rate</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value positive">5,180</div>
                    <div class="metric-label">Alerts for Review</div>
                    <div class="metric-change positive">↗ 70% workload reduction</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value positive">603</div>
                    <div class="metric-label">Hours Saved</div>
                    <div class="metric-change positive">↗ $30,150 monthly savings</div>
                </div>
            </div>

            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin-bottom: 40px;">
                <div class="chart-container">
                    <div class="donut-chart">
                        <div class="donut-inner">
                            <div style="font-size: 24px; font-weight: 700; color: var(--accent-green);">70.0%</div>
                            <div style="font-size: 12px; color: var(--text-secondary);">Filtered</div>
                        </div>
                    </div>
                    <div class="chart-legend">
                        <div class="legend-item">
                            <div class="legend-color" style="background: var(--accent-green);"></div>
                            <span>False Positives Filtered: 12,088</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color" style="background: var(--accent-orange);"></div>
                            <span>Require Human Review: 5,180</span>
                        </div>
                    </div>
                    <h4 style="position: absolute; top: 20px; left: 20px;">Processing Results</h4>
                </div>

                <div class="card">
                    <h4 style="margin-bottom: 20px;">False Positive Categories</h4>
                    <div style="display: flex; flex-direction: column; gap: 16px;">
                        <div>
                            <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                                <span style="font-size: 14px;">Personnel with Proper PPE</span>
                                <span style="font-size: 14px; font-weight: 600;">42.0%</span>
                            </div>
                            <div style="background: var(--bg-secondary); height: 8px; border-radius: 4px; overflow: hidden;">
                                <div style="background: var(--accent-blue); height: 100%; width: 42%;"></div>
                            </div>
                        </div>
                        
                        <div>
                            <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                                <span style="font-size: 14px;">Structure Misidentification</span>
                                <span style="font-size: 14px; font-weight: 600;">29.6%</span>
                            </div>
                            <div style="background: var(--bg-secondary); height: 8px; border-radius: 4px; overflow: hidden;">
                                <div style="background: var(--accent-green); height: 100%; width: 29.6%;"></div>
                            </div>
                        </div>
                        
                        <div>
                            <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                                <span style="font-size: 14px;">Equipment Confusion</span>
                                <span style="font-size: 14px; font-weight: 600;">25.1%</span>
                            </div>
                            <div style="background: var(--bg-secondary); height: 8px; border-radius: 4px; overflow: hidden;">
                                <div style="background: var(--accent-orange); height: 100%; width: 25.1%;"></div>
                            </div>
                        </div>
                        
                        <div>
                            <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                                <span style="font-size: 14px;">No Actual Violation</span>
                                <span style="font-size: 14px; font-weight: 600;">3.3%</span>
                            </div>
                            <div style="background: var(--bg-secondary); height: 8px; border-radius: 4px; overflow: hidden;">
                                <div style="background: var(--accent-red); height: 100%; width: 3.3%;"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card">
                <h4 style="margin-bottom: 20px;">Confidence Score Distribution</h4>
                <div style="display: flex; align-items: end; justify-content: space-between; height: 200px; padding: 20px 0;">
                    <div style="display: flex; flex-direction: column; align-items: center;">
                        <div style="width: 40px; height: 60px; background: var(--accent-red); margin-bottom: 8px;"></div>
                        <span style="font-size: 12px;">50-60%</span>
                        <span style="font-size: 10px; color: var(--text-muted);">245</span>
                    </div>
                    <div style="display: flex; flex-direction: column; align-items: center;">
                        <div style="width: 40px; height: 100px; background: var(--accent-orange); margin-bottom: 8px;"></div>
                        <span style="font-size: 12px;">60-70%</span>
                        <span style="font-size: 10px; color: var(--text-muted);">487</span>
                    </div>
                    <div style="display: flex; flex-direction: column; align-items: center;">
                        <div style="width: 40px; height: 120px; background: var(--accent-blue); margin-bottom: 8px;"></div>
                        <span style="font-size: 12px;">70-80%</span>
                        <span style="font-size: 10px; color: var(--text-muted);">832</span>
                    </div>
                    <div style="display: flex; flex-direction: column; align-items: center;">
                        <div style="width: 40px; height: 180px; background: var(--accent-green); margin-bottom: 8px;"></div>
                        <span style="font-size: 12px;">80-90%</span>
                        <span style="font-size: 10px; color: var(--text-muted);">1,247</span>
                    </div>
                    <div style="display: flex; flex-direction: column; align-items: center;">
                        <div style="width: 40px; height: 200px; background: var(--accent-green); margin-bottom: 8px;"></div>
                        <span style="font-size: 12px;">90-100%</span>
                        <span style="font-size: 10px; color: var(--text-muted);">1,654</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Page 6: ROI Calculator -->
    <div id="roi" class="page">
        <div class="container" style="padding: 40px 20px;">
            <h1 style="margin-bottom: 30px;">ROI Calculator</h1>
            
            <div class="calculator-grid">
                <div class="card">
                    <h3 style="margin-bottom: 24px; color: var(--accent-blue);">Input Parameters</h3>
                    
                    <div class="input-group">
                        <label class="input-label">Monthly Alert Volume</label>
                        <input type="number" class="input-field" value="17268" placeholder="Number of alerts per month">
                    </div>
                    
                    <div class="input-group">
                        <label class="input-label">Current False Positive Rate (%)</label>
                        <input type="number" class="input-field" value="97" placeholder="Current FP rate">
                    </div>
                    
                    <div class="input-group">
                        <label class="input-label">Review Time per Alert (minutes)</label>
                        <input type="number" class="input-field" value="3" placeholder="Minutes per alert">
                    </div>
                    
                    <div class="input-group">
                        <label class="input-label">Reviewer Hourly Rate ($)</label>
                        <input type="number" class="input-field" value="50" placeholder="Cost per hour">
                    </div>
                    
                    <div class="input-group">
                        <label class="input-label">Target Filter Rate (%)</label>
                        <input type="number" class="input-field" value="70" placeholder="AI filter effectiveness">
                    </div>
                    
                    <div class="input-group">
                        <label class="input-label">Implementation Cost ($)</label>
                        <input type="number" class="input-field" value="225000" placeholder="One-time setup cost">
                    </div>
                    
                    <button class="btn btn-primary" style="width: 100%; margin-top: 20px;">Calculate ROI</button>
                </div>
                
                <div class="roi-results">
                    <h3 style="margin-bottom: 24px; color: var(--accent-green);">Financial Impact</h3>
                    
                    <div class="roi-highlight">
                        $361,800
                        <div style="font-size: 16px; color: var(--text-secondary); margin-top: 8px;">Annual Savings</div>
                    </div>
                    
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 30px;">
                        <div class="metric-card">
                            <div class="metric-value positive">156%</div>
                            <div class="metric-label">First Year ROI</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value positive">3.7</div>
                            <div class="metric-label">Months Payback</div>
                        </div>
                    </div>
                    
                    <div style="margin-bottom: 30px;">
                        <h4 style="margin-bottom: 16px;">Monthly Breakdown</h4>
                        <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                            <span>Current Cost:</span>
                            <span style="color: var(--accent-red);">$43,100</span>
                        </div>
                        <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                            <span>Post-AI Cost:</span>
                            <span style="color: var(--accent-green);">$12,950</span>
                        </div>
                        <div style="display: flex; justify-content: space-between; margin-bottom: 8px; padding-top: 8px; border-top: 1px solid var(--border);">
                            <span><strong>Monthly Savings:</strong></span>
                            <span style="color: var(--accent-green); font-weight: 700;">$30,150</span>
                        </div>
                    </div>
                    
                    <div>
                        <h4 style="margin-bottom: 16px;">5-Year Projection</h4>
                        <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                            <span>Total Savings:</span>
                            <span style="color: var(--accent-green);">$1,809,000</span>
                        </div>
                        <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                            <span>Implementation Cost:</span>
                            <span style="color: var(--accent-red);">$225,000</span>
                        </div>
                        <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                            <span>Operational Costs:</span>
                            <span style="color: var(--accent-red);">$320,000</span>
                        </div>
                        <div style="display: flex; justify-content: space-between; padding-top: 8px; border-top: 1px solid var(--border);">
                            <span><strong>Net 5-Year Value:</strong></span>
                            <span style="color: var(--accent-green); font-weight: 700; font-size: 18px;">$1,264,000</span>
                        </div>
                    </div>
                    
                    <button class="btn btn-success" style="width: 100%; margin-top: 30px;">Export Financial Report</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Page 7: Insights & Patterns -->
    <div id="insights" class="page">
        <div class="container" style="padding: 40px 20px;">
            <h1 style="margin-bottom: 30px;">AI Insights & Patterns</h1>
            
            <div class="card mb-30">
                <h3 style="margin-bottom: 20px; color: var(--accent-blue);">🧠 Auto-Detected Patterns</h3>
                <div style="display: grid; gap: 20px;">
                    <div style="background: var(--bg-secondary); padding: 20px; border-radius: 8px; border-left: 4px solid var(--accent-green);">
                        <h4 style="color: var(--accent-green); margin-bottom: 8px;">High Confidence Pattern</h4>
                        <p style="margin-bottom: 12px;"><strong>Quay Crane Structure Misidentification</strong></p>
                        <p style="font-size: 14px; color: var(--text-secondary); margin-bottom: 12px;">
                            Terminal P1 cameras consistently mistake crane arm structures for personnel violations. 
                            94% average confidence in filtering these false positives.
                        </p>
                        <div style="font-size: 12px; color: var(--text-muted);">
                            Affected cameras: QC-03, QC-05, QC-07 • Occurrence: 347 times this month
                        </div>
                    </div>
                    
                    <div style="background: var(--bg-secondary); padding: 20px; border-radius: 8px; border-left: 4px solid var(--accent-blue);">
                        <h4 style="color: var(--accent-blue); margin-bottom: 8px;">Medium Confidence Pattern</h4>
                        <p style="margin-bottom: 12px;"><strong>Proper PPE False Alerts</strong></p>
                        <p style="font-size: 14px; color: var(--text-secondary); margin-bottom: 12px;">
                            Workers wearing correct PPE (hard hat, hi-vis vest) still trigger violations due to 
                            lighting conditions or camera angles. 87% filter confidence.
                        </p>
                        <div style="font-size: 12px; color: var(--text-muted);">
                            Peak times: Early morning (6-8 AM), Late afternoon (4-6 PM) • Occurrence: 521 times
                        </div>
                    </div>
                    
                    <div style="background: var(--bg-secondary); padding: 20px; border-radius: 8px; border-left: 4px solid var(--accent-orange);">
                        <h4 style="color: var(--accent-orange); margin-bottom: 8px;">Learning Opportunity</h4>
                        <p style="margin-bottom: 12px;"><strong>Vessel Equipment Confusion</strong></p>
                        <p style="font-size: 14px; color: var(--text-secondary); margin-bottom: 12px;">
                            Ship masts and rigging equipment occasionally flagged as violations. 
                            AI confidence improving: 76% → 91% over past month.
                        </p>
                        <div style="font-size: 12px; color: var(--text-muted);">
                            Learning mode active • Confidence trend: +15% improvement • Occurrence: 198 times
                        </div>
                    </div>
                </div>
            </div>

            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin-bottom: 30px;">
                <div class="card">
                    <h4 style="margin-bottom: 20px;">Terminal-Specific Insights</h4>
                    <div style="display: flex; flex-direction: column; gap: 16px;">
                        <div>
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                                <span style="font-weight: 600; color: var(--accent-red);">Terminal P1</span>
                                <span style="color: var(--accent-red);">Needs Attention</span>
                            </div>
                            <div style="font-size: 14px; color: var(--text-secondary); margin-bottom: 12px;">
                                Camera QC-05 has consistent false positives. Recommend angle adjustment or recalibration.
                            </div>
                            <div style="font-size: 12px; color: var(--text-muted);">
                                Issues: Crane structure confusion, lighting glare
                            </div>
                        </div>
                        
                        <div>
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                                <span style="font-weight: 600; color: var(--accent-orange);">Terminal P2</span>
                                <span style="color: var(--accent-orange);">Standard</span>
                            </div>
                            <div style="font-size: 14px; color: var(--text-secondary); margin-bottom: 12px;">
                                Good overall performance. Some vessel equipment confusion during loading operations.
                            </div>
                            <div style="font-size: 12px; color: var(--text-muted);">
                                Opportunity: Vessel-specific AI training
                            </div>
                        </div>
                        
                        <div>
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                                <span style="font-weight: 600; color: var(--accent-green);">Terminal P3</span>
                                <span style="color: var(--accent-green);">Optimized</span>
                            </div>
                            <div style="font-size: 14px; color: var(--text-secondary); margin-bottom: 12px;">
                                Best performance across all metrics. Use as reference for other terminals.
                            </div>
                            <div style="font-size: 12px; color: var(--text-muted);">
                                Strength: Clear camera positioning, minimal interference
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="card">
                    <h4 style="margin-bottom: 20px;">Optimization Recommendations</h4>
                    <div style="display: flex; flex-direction: column; gap: 16px;">
                        <div style="padding: 16px; background: var(--bg-primary); border-radius: 6px; border-left: 3px solid var(--accent-blue);">
                            <div style="font-weight: 600; margin-bottom: 8px;">Camera Recalibration</div>
                            <div style="font-size: 14px; color: var(--text-secondary);">
                                Adjust Terminal P1 cameras QC-03 and QC-05 to reduce glare and improve crane structure recognition.
                            </div>
                            <div style="font-size: 12px; color: var(--accent-blue); margin-top: 8px;">
                                Estimated impact: +12% accuracy improvement
                            </div>
                        </div>
                        
                        <div style="padding: 16px; background: var(--bg-primary); border-radius: 6px; border-left: 3px solid var(--accent-green);">
                            <div style="font-weight: 600; margin-bottom: 8px;">Enhanced Training</div>
                            <div style="font-size: 14px; color: var(--text-secondary);">
                                Additional vessel equipment training data to improve recognition of ship structures and rigging.
                            </div>
                            <div style="font-size: 12px; color: var(--accent-green); margin-top: 8px;">
                                Estimated impact: +8% filter confidence
                            </div>
                        </div>
                        
                        <div style="padding: 16px; background: var(--bg-primary); border-radius: 6px; border-left: 3px solid var(--accent-orange);">
                            <div style="font-weight: 600; margin-bottom: 8px;">Threshold Optimization</div>
                            <div style="font-size: 14px; color: var(--text-secondary);">
                                Adjust confidence thresholds based on terminal-specific performance patterns.
                            </div>
                            <div style="font-size: 12px; color: var(--accent-orange); margin-top: 8px;">
                                Estimated impact: +5% overall efficiency
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card">
                <h4 style="margin-bottom: 20px;">Implementation Roadmap</h4>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                    <div style="padding: 20px; background: var(--bg-secondary); border-radius: 8px;">
                        <div style="color: var(--accent-blue); font-weight: 600; margin-bottom: 12px;">Phase 1: Immediate (1-2 weeks)</div>
                        <ul style="list-style: none; padding-left: 0;">
                            <li style="margin-bottom: 8px;">✓ Implement high-confidence patterns</li>
                            <li style="margin-bottom: 8px;">✓ Adjust Terminal P1 camera angles</li>
                            <li style="margin-bottom: 8px;">✓ Deploy crane structure recognition</li>
                        </ul>
                        <div style="font-size: 12px; color: var(--text-muted); margin-top: 12px;">
                            Expected: 15% immediate improvement
                        </div>
                    </div>
                    
                    <div style="padding: 20px; background: var(--bg-secondary); border-radius: 8px;">
                        <div style="color: var(--accent-green); font-weight: 600; margin-bottom: 12px;">Phase 2: Short-term (1-2 months)</div>
                        <ul style="list-style: none; padding-left: 0;">
                            <li style="margin-bottom: 8px;">• Enhanced vessel equipment training</li>
                            <li style="margin-bottom: 8px;">• Weather condition adaptations</li>
                            <li style="margin-bottom: 8px;">• Terminal-specific optimizations</li>
                        </ul>
                        <div style="font-size: 12px; color: var(--text-muted); margin-top: 12px;">
                            Expected: 25% additional improvement
                        </div>
                    </div>
                    
                    <div style="padding: 20px; background: var(--bg-secondary); border-radius: 8px;">
                        <div style="color: var(--accent-orange); font-weight: 600; margin-bottom: 12px;">Phase 3: Long-term (3-6 months)</div>
                        <ul style="list-style: none; padding-left: 0;">
                            <li style="margin-bottom: 8px;">• Advanced pattern recognition</li>
                            <li style="margin-bottom: 8px;">• Predictive analytics</li>
                            <li style="margin-bottom: 8px;">• Multi-terminal coordination</li>
                        </ul>
                        <div style="font-size: 12px; color: var(--text-muted); margin-top: 12px;">
                            Expected: 35% total improvement
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Page 8: Alert Review Center -->
    <div id="review" class="page">
        <div class="container" style="padding: 20px;">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                <h1>Alert Review Center</h1>
                <div style="display: flex; gap: 12px;">
                    <button class="btn btn-primary">Bulk Approve</button>
                    <button class="btn btn-danger">Bulk Reject</button>
                </div>
            </div>
            
            <div class="review-layout">
                <div class="image-viewer">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                        <h3>VALO Alert #2076</h3>
                        <div style="font-size: 14px; color: var(--text-secondary);">
                            Terminal P3 • Container-12 • Nov 24, 2024 14:23:43
                        </div>
                    </div>
                    
                    <div class="review-image">
                        <div style="font-size: 18px; color: var(--text-muted);">
                            📷 Violation Image with Bounding Box
                            <div style="font-size: 14px; margin-top: 8px;">
                                Original VALO detection shown in red box
                            </div>
                        </div>
                    </div>
                    
                    <div class="review-controls">
                        <button class="btn btn-success">✓ Valid Violation</button>
                        <button class="btn btn-danger">✗ False Positive</button>
                        <button class="btn" style="background: var(--bg-tertiary); color: var(--text-primary);">↷ Skip for Now</button>
                    </div>
                </div>
                
                <div class="review-panel">
                    <div class="vlm-analysis">
                        <h4 style="margin-bottom: 16px; color: var(--accent-blue);">VLM Analysis Results</h4>
                        
                        <div style="margin-bottom: 16px;">
                            <div style="font-weight: 600; margin-bottom: 8px;">Detection Type</div>
                            <div style="background: var(--bg-primary); padding: 8px 12px; border-radius: 4px; color: var(--accent-orange);">
                                UNCLEAR - Requires Human Review
                            </div>
                        </div>
                        
                        <div style="margin-bottom: 16px;">
                            <div style="font-weight: 600; margin-bottom: 8px;">False Positive Likelihood</div>
                            <div class="confidence-meter">
                                <div class="confidence-fill" style="width: 45%;"></div>
                                <div class="confidence-label">45%</div>
                            </div>
                        </div>
                        
                        <div style="margin-bottom: 16px;">
                            <div style="font-weight: 600; margin-bottom: 8px;">VLM Reasoning</div>
                            <div style="background: var(--bg-primary); padding: 12px; border-radius: 4px; font-size: 14px; line-height: 1.5;">
                                "Image contains a person in industrial setting. Hard hat appears to be present but visibility is partially obscured. 
                                Hi-vis vest is visible but lighting conditions make assessment uncertain. Container handling equipment 
                                visible in background. Recommend human verification due to ambiguous PPE visibility."
                            </div>
                        </div>
                        
                        <div style="margin-bottom: 16px;">
                            <div style="font-weight: 600; margin-bottom: 8px;">Processing Details</div>
                            <div style="font-size: 12px; color: var(--text-secondary);">
                                <div>Model: InternVL3 38B AWQ</div>
                                <div>Processing Time: 892ms</div>
                                <div>Confidence Score: 45% FP likelihood</div>
                                <div>Image Quality: Good</div>
                            </div>
                        </div>
                    </div>
                    
                    <div style="background: var(--bg-secondary); padding: 16px; border-radius: 8px; margin-bottom: 20px;">
                        <h4 style="margin-bottom: 12px;">Original VALO Detection</h4>
                        <div style="font-size: 14px; color: var(--text-secondary);">
                            <div><strong>Alert Type:</strong> PPE Violation</div>
                            <div><strong>Camera:</strong> Container-12</div>
                            <div><strong>Location:</strong> Terminal P3, Section C</div>
                            <div><strong>Time:</strong> 14:23:43 GMT+8</div>
                        </div>
                    </div>
                    
                    <div style="margin-bottom: 20px;">
                        <h4 style="margin-bottom: 12px;">Review Notes</h4>
                        <textarea 
                            style="width: 100%; height: 80px; background: var(--bg-primary); border: 1px solid var(--border); border-radius: 4px; padding: 12px; color: var(--text-primary); resize: vertical;"
                            placeholder="Add notes about this review decision..."></textarea>
                    </div>
                    
                    <div style="background: var(--bg-primary); padding: 16px; border-radius: 8px;">
                        <h4 style="margin-bottom: 12px;">Queue Status</h4>
                        <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                            <span>Current Position:</span>
                            <span style="font-weight: 600;">47 of 156</span>
                        </div>
                        <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                            <span>Remaining Reviews:</span>
                            <span style="font-weight: 600;">109 alerts</span>
                        </div>
                        <div style="display: flex; justify-content: space-between;">
                            <span>Estimated Time:</span>
                            <span style="font-weight: 600;">~5.5 hours</span>
                        </div>
                        
                        <div style="margin-top: 16px;">
                            <div style="background: var(--bg-secondary); height: 8px; border-radius: 4px; overflow: hidden;">
                                <div style="background: var(--accent-blue); height: 100%; width: 30%;"></div>
                            </div>
                            <div style="font-size: 12px; color: var(--text-muted); margin-top: 4px;">30% Complete</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Page 9: Auto-Learning System -->
    <div id="learning" class="page">
        <div class="container" style="padding: 40px 20px;">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px;">
                <h1>🧠 Auto-Learning Intelligence System</h1>
                <div style="display: flex; gap: 16px; align-items: center;">
                    <div style="display: flex; align-items: center; gap: 8px;">
                        <div class="status-dot online"></div>
                        <span style="font-size: 14px; color: var(--accent-green);">Learning Active</span>
                    </div>
                    <div style="background: var(--bg-card); padding: 8px 16px; border-radius: 6px; font-size: 14px;">
                        Day 23 of Learning Cycle
                    </div>
                </div>
            </div>

            <!-- Learning Progress Overview -->
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 40px;">
                <div class="metric-card">
                    <div class="metric-value positive">84.7%</div>
                    <div class="metric-label">Current Accuracy</div>
                    <div class="metric-change positive">↗ +14.5% vs baseline (70.2%)</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value positive">23</div>
                    <div class="metric-label">Patterns Detected</div>
                    <div class="metric-change positive">↗ +8 this week</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value positive">15</div>
                    <div class="metric-label">Custom Rules Created</div>
                    <div class="metric-change positive">↗ Auto-generated optimizations</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value positive">$8.5K</div>
                    <div class="metric-label">Additional Monthly Savings</div>
                    <div class="metric-change positive">↗ Beyond baseline ROI</div>
                </div>
            </div>

            <!-- Learning Phases Progress -->
            <div class="card mb-30">
                <h3 style="margin-bottom: 24px; color: var(--accent-blue);">Learning Phases Progress</h3>
                <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 20px;">
                    <div class="pipeline-stage complete">
                        <div class="stage-icon">📊</div>
                        <h4>Data Collection</h4>
                        <div style="font-size: 12px; color: var(--accent-green); margin-top: 8px;">✓ Week 1 Complete</div>
                        <div style="font-size: 11px; color: var(--text-muted); margin-top: 4px;">2,847 predictions analyzed</div>
                    </div>
                    <div class="pipeline-stage complete">
                        <div class="stage-icon">🔍</div>
                        <h4>Pattern Analysis</h4>
                        <div style="font-size: 12px; color: var(--accent-green); margin-top: 8px;">✓ Week 2 Complete</div>
                        <div style="font-size: 11px; color: var(--text-muted); margin-top: 4px;">23 patterns identified</div>
                    </div>
                    <div class="pipeline-stage complete">
                        <div class="stage-icon">⚙️</div>
                        <h4>Optimization</h4>
                        <div style="font-size: 12px; color: var(--accent-green); margin-top: 8px;">✓ Week 3 Complete</div>
                        <div style="font-size: 11px; color: var(--text-muted); margin-top: 4px;">Thresholds optimized</div>
                    </div>
                    <div class="pipeline-stage active">
                        <div class="stage-icon">🚀</div>
                        <h4>Continuous Learning</h4>
                        <div style="font-size: 12px; color: var(--accent-blue); margin-top: 8px;">⚡ Active</div>
                        <div style="font-size: 11px; color: var(--text-muted); margin-top: 4px;">Real-time adaptation</div>
                    </div>
                </div>
            </div>

            <!-- Performance Improvement Chart -->
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin-bottom: 40px;">
                <div class="card">
                    <h4 style="margin-bottom: 20px;">Accuracy Improvement Over Time</h4>
                    <div style="height: 250px; position: relative; background: var(--bg-secondary); border-radius: 6px; padding: 20px;">
                        <!-- Simulated line chart -->
                        <div style="position: absolute; bottom: 20px; left: 20px; right: 20px; height: 200px;">
                            <svg width="100%" height="100%" style="position: absolute;">
                                <!-- Grid lines -->
                                <defs>
                                    <pattern id="grid" width="40" height="40" patternUnits="userSpaceOnUse">
                                        <path d="M 40 0 L 0 0 0 40" fill="none" stroke="var(--border)" stroke-width="1" opacity="0.3"/>
                                    </pattern>
                                </defs>
                                <rect width="100%" height="100%" fill="url(#grid)" />
                                
                                <!-- Improvement line -->
                                <polyline points="0,160 60,155 120,145 180,125 240,95 300,75 360,60" 
                                         fill="none" stroke="var(--accent-green)" stroke-width="3"/>
                                
                                <!-- Data points -->
                                <circle cx="0" cy="160" r="4" fill="var(--accent-blue)"/>
                                <circle cx="60" cy="155" r="4" fill="var(--accent-blue)"/>
                                <circle cx="120" cy="145" r="4" fill="var(--accent-blue)"/>
                                <circle cx="180" cy="125" r="4" fill="var(--accent-green)"/>
                                <circle cx="240" cy="95" r="4" fill="var(--accent-green)"/>
                                <circle cx="300" cy="75" r="4" fill="var(--accent-green)"/>
                                <circle cx="360" cy="60" r="4" fill="var(--accent-green)"/>
                            </svg>
                            
                            <!-- Y-axis labels -->
                            <div style="position: absolute; left: -15px; top: 0; font-size: 10px; color: var(--text-muted);">90%</div>
                            <div style="position: absolute; left: -15px; top: 50px; font-size: 10px; color: var(--text-muted);">80%</div>
                            <div style="position: absolute; left: -15px; top: 100px; font-size: 10px; color: var(--text-muted);">75%</div>
                            <div style="position: absolute; left: -15px; top: 150px; font-size: 10px; color: var(--text-muted);">70%</div>
                            
                            <!-- X-axis labels -->
                            <div style="position: absolute; bottom: -15px; left: 0; font-size: 10px; color: var(--text-muted);">Week 1</div>
                            <div style="position: absolute; bottom: -15px; left: 120px; font-size: 10px; color: var(--text-muted);">Week 2</div>
                            <div style="position: absolute; bottom: -15px; left: 240px; font-size: 10px; color: var(--text-muted);">Week 3</div>
                            <div style="position: absolute; bottom: -15px; right: 0; font-size: 10px; color: var(--text-muted);">Now</div>
                        </div>
                    </div>
                    <div style="margin-top: 12px; font-size: 12px; color: var(--text-secondary);">
                        Baseline: 70.2% → Current: 84.7% (+14.5% improvement)
                    </div>
                </div>

                <div class="card">
                    <h4 style="margin-bottom: 20px;">Pattern Detection Breakdown</h4>
                    <div style="display: flex; flex-direction: column; gap: 16px;">
                        <div>
                            <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                                <span style="font-size: 14px;">Equipment Patterns</span>
                                <span style="font-size: 14px; font-weight: 600; color: var(--accent-green);">12 detected</span>
                            </div>
                            <div style="background: var(--bg-secondary); height: 6px; border-radius: 3px; overflow: hidden;">
                                <div style="background: var(--accent-green); height: 100%; width: 52%;"></div>
                            </div>
                        </div>
                        
                        <div>
                            <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                                <span style="font-size: 14px;">Time-Based Patterns</span>
                                <span style="font-size: 14px; font-weight: 600; color: var(--accent-blue);">6 detected</span>
                            </div>
                            <div style="background: var(--bg-secondary); height: 6px; border-radius: 3px; overflow: hidden;">
                                <div style="background: var(--accent-blue); height: 100%; width: 26%;"></div>
                            </div>
                        </div>
                        
                        <div>
                            <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                                <span style="font-size: 14px;">Location Hotspots</span>
                                <span style="font-size: 14px; font-weight: 600; color: var(--accent-orange);">3 detected</span>
                            </div>
                            <div style="background: var(--bg-secondary); height: 6px; border-radius: 3px; overflow: hidden;">
                                <div style="background: var(--accent-orange); height: 100%; width: 13%;"></div>
                            </div>
                        </div>
                        
                        <div>
                            <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                                <span style="font-size: 14px;">Environmental Patterns</span>
                                <span style="font-size: 14px; font-weight: 600; color: var(--accent-blue);">2 detected</span>
                            </div>
                            <div style="background: var(--bg-secondary); height: 6px; border-radius: 3px; overflow: hidden;">
                                <div style="background: var(--accent-blue); height: 100%; width: 9%;"></div>
                            </div>
                        </div>
                    </div>
                    
                    <div style="margin-top: 20px; padding: 12px; background: var(--bg-primary); border-radius: 6px;">
                        <div style="font-size: 12px; color: var(--accent-green); margin-bottom: 4px;">🎯 High-Impact Pattern</div>
                        <div style="font-size: 14px; font-weight: 600; margin-bottom: 8px;">Quay Crane Arm Detection</div>
                        <div style="font-size: 12px; color: var(--text-secondary);">
                            Terminal P1 crane arms cause 35% of false positives. Auto-optimization reduced these by 89%.
                        </div>
                    </div>
                </div>
            </div>

            <!-- Detected Patterns & Insights -->
            <div class="card mb-30">
                <h3 style="margin-bottom: 24px; color: var(--accent-blue);">🔍 Auto-Detected Patterns & Insights</h3>
                <div style="display: grid; gap: 20px;">
                    <div style="background: var(--bg-secondary); padding: 20px; border-radius: 8px; border-left: 4px solid var(--accent-green);">
                        <div style="display: flex; justify-content: space-between; align-items: start; margin-bottom: 12px;">
                            <div>
                                <h4 style="color: var(--accent-green); margin-bottom: 8px;">Critical Equipment Pattern</h4>
                                <div style="font-weight: 600; margin-bottom: 8px;">Quay Crane QC-05 False Positive Hotspot</div>
                            </div>
                            <div style="background: var(--accent-green); color: #000; padding: 4px 12px; border-radius: 12px; font-size: 11px; font-weight: 600;">
                                HIGH IMPACT
                            </div>
                        </div>
                        <div style="font-size: 14px; color: var(--text-secondary); margin-bottom: 12px;">
                            Terminal P1 Crane QC-05 generates 3x more false positives than average (450 alerts/month). 
                            Yellow crane arms consistently mistaken for workers in hi-vis vests.
                        </div>
                        <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 16px; margin-bottom: 12px;">
                            <div>
                                <div style="font-size: 12px; color: var(--text-muted);">Frequency</div>
                                <div style="font-weight: 600; color: var(--accent-red);">35% of P1 alerts</div>
                            </div>
                            <div>
                                <div style="font-size: 12px; color: var(--text-muted);">Peak Times</div>
                                <div style="font-weight: 600;">Dawn/Dusk hours</div>
                            </div>
                            <div>
                                <div style="font-size: 12px; color: var(--text-muted);">AI Confidence</div>
                                <div style="font-weight: 600; color: var(--accent-green);">94% filter rate</div>
                            </div>
                        </div>
                        <div style="background: var(--bg-primary); padding: 12px; border-radius: 6px;">
                            <div style="font-size: 12px; color: var(--accent-green); margin-bottom: 4px;">🎯 Auto-Generated Recommendation</div>
                            <div style="font-size: 13px; color: var(--text-primary);">
                                Adjust camera angle by 15° or add exclusion zone for crane arm area. 
                                <strong>Potential Impact:</strong> Eliminate 450 false alerts/month (+$2,250 savings)
                            </div>
                        </div>
                    </div>

                    <div style="background: var(--bg-secondary); padding: 20px; border-radius: 8px; border-left: 4px solid var(--accent-blue);">
                        <div style="display: flex; justify-content: space-between; align-items: start; margin-bottom: 12px;">
                            <div>
                                <h4 style="color: var(--accent-blue); margin-bottom: 8px;">Temporal Pattern</h4>
                                <div style="font-weight: 600; margin-bottom: 8px;">Sunrise False Positive Surge</div>
                            </div>
                            <div style="background: var(--accent-blue); color: #000; padding: 4px 12px; border-radius: 12px; font-size: 11px; font-weight: 600;">
                                MEDIUM IMPACT
                            </div>
                        </div>
                        <div style="font-size: 14px; color: var(--text-secondary); margin-bottom: 12px;">
                            False positives spike 250% during sunrise hours (6-7 AM) due to long shadows and backlighting. 
                            Affects all terminals but particularly P2 vessel operations.
                        </div>
                        <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 16px; margin-bottom: 12px;">
                            <div>
                                <div style="font-size: 12px; color: var(--text-muted);">Peak Window</div>
                                <div style="font-weight: 600; color: var(--accent-orange);">6:00-7:00 AM</div>
                            </div>
                            <div>
                                <div style="font-size: 12px; color: var(--text-muted);">Affected Areas</div>
                                <div style="font-weight: 600;">All terminals</div>
                            </div>
                            <div>
                                <div style="font-size: 12px; color: var(--text-muted);">Monthly Impact</div>
                                <div style="font-weight: 600; color: var(--accent-orange);">200 false alerts</div>
                            </div>
                        </div>
                        <div style="background: var(--bg-primary); padding: 12px; border-radius: 6px;">
                            <div style="font-size: 12px; color: var(--accent-blue); margin-bottom: 4px;">⚙️ Auto-Applied Solution</div>
                            <div style="font-size: 13px; color: var(--text-primary);">
                                Time-based threshold adjustment: 80% confidence → 90% during sunrise hours. 
                                <strong>Result:</strong> 85% reduction in sunrise false positives
                            </div>
                        </div>
                    </div>

                    <div style="background: var(--bg-secondary); padding: 20px; border-radius: 8px; border-left: 4px solid var(--accent-orange);">
                        <div style="display: flex; justify-content: space-between; align-items: start; margin-bottom: 12px;">
                            <div>
                                <h4 style="color: var(--accent-orange); margin-bottom: 8px;">Emerging Pattern</h4>
                                <div style="font-weight: 600; margin-bottom: 8px;">Container Spreader Confusion</div>
                            </div>
                            <div style="background: var(--accent-orange); color: #000; padding: 4px 12px; border-radius: 12px; font-size: 11px; font-weight: 600;">
                                LEARNING
                            </div>
                        </div>
                        <div style="font-size: 14px; color: var(--text-secondary); margin-bottom: 12px;">
                            New pattern detected: Container spreaders at Terminal P3 occasionally trigger PPE violations. 
                            AI learning to distinguish spreader hydraulics from worker equipment.
                        </div>
                        <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 16px; margin-bottom: 12px;">
                            <div>
                                <div style="font-size: 12px; color: var(--text-muted);">Detection Rate</div>
                                <div style="font-weight: 600; color: var(--accent-orange);">15% of P3 alerts</div>
                            </div>
                            <div>
                                <div style="font-size: 12px; color: var(--text-muted);">Learning Progress</div>
                                <div style="font-weight: 600;">76% → 91% confidence</div>
                            </div>
                            <div>
                                <div style="font-size: 12px; color: var(--text-muted);">Trend</div>
                                <div style="font-weight: 600; color: var(--accent-green);">+15% improvement</div>
                            </div>
                        </div>
                        <div style="background: var(--bg-primary); padding: 12px; border-radius: 6px;">
                            <div style="font-size: 12px; color: var(--accent-orange); margin-bottom: 4px;">🧠 Active Learning</div>
                            <div style="font-size: 13px; color: var(--text-primary);">
                                System collecting more training data on spreader equipment. 
                                <strong>ETA:</strong> Full optimization expected in 2 weeks
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Threshold Optimization Results -->
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin-bottom: 40px;">
                <div class="card">
                    <h4 style="margin-bottom: 20px;">Optimized Thresholds by Location</h4>
                    <div style="display: flex; flex-direction: column; gap: 16px;">
                        <div>
                            <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                                <span style="font-weight: 600;">Terminal P1 - Crane Area</span>
                                <div style="display: flex; gap: 12px; align-items: center;">
                                    <span style="font-size: 12px; color: var(--text-muted);">70% → </span>
                                    <span style="font-size: 14px; font-weight: 600; color: var(--accent-green);">85%</span>
                                </div>
                            </div>
                            <div style="font-size: 12px; color: var(--text-secondary); margin-bottom: 8px;">
                                Higher threshold needed due to crane structure confusion
                            </div>
                            <div style="background: var(--bg-secondary); height: 6px; border-radius: 3px; overflow: hidden;">
                                <div style="background: var(--accent-green); height: 100%; width: 85%;"></div>
                            </div>
                        </div>
                        
                        <div>
                            <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                                <span style="font-weight: 600;">Terminal P2 - Vessel Side</span>
                                <div style="display: flex; gap: 12px; align-items: center;">
                                    <span style="font-size: 12px; color: var(--text-muted);">70% → </span>
                                    <span style="font-size: 14px; font-weight: 600; color: var(--accent-blue);">82%</span>
                                </div>
                            </div>
                            <div style="font-size: 12px; color: var(--text-secondary); margin-bottom: 8px;">
                                Moderate adjustment for vessel equipment patterns
                            </div>
                            <div style="background: var(--bg-secondary); height: 6px; border-radius: 3px; overflow: hidden;">
                                <div style="background: var(--accent-blue); height: 100%; width: 82%;"></div>
                            </div>
                        </div>
                        
                        <div>
                            <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                                <span style="font-weight: 600;">Terminal P3 - Container Yard</span>
                                <div style="display: flex; gap: 12px; align-items: center;">
                                    <span style="font-size: 12px; color: var(--text-muted);">70% → </span>
                                    <span style="font-size: 14px; font-weight: 600; color: var(--accent-green);">78%</span>
                                </div>
                            </div>
                            <div style="font-size: 12px; color: var(--text-secondary); margin-bottom: 8px;">
                                Minimal adjustment - already best performing terminal
                            </div>
                            <div style="background: var(--bg-secondary); height: 6px; border-radius: 3px; overflow: hidden;">
                                <div style="background: var(--accent-green); height: 100%; width: 78%;"></div>
                            </div>
                        </div>
                    </div>
                    
                    <div style="margin-top: 20px; padding: 12px; background: var(--bg-primary); border-radius: 6px;">
                        <div style="font-size: 12px; color: var(--accent-blue); margin-bottom: 4px;">📊 Optimization Impact</div>
                        <div style="font-size: 13px;">
                            Dynamic thresholds improve accuracy by <strong style="color: var(--accent-green);">+18%</strong> 
                            compared to static 70% threshold across all locations.
                        </div>
                    </div>
                </div>

                <div class="card">
                    <h4 style="margin-bottom: 20px;">Custom Prompt Evolution</h4>
                    <div style="display: flex; flex-direction: column; gap: 16px;">
                        <div style="background: var(--bg-secondary); padding: 16px; border-radius: 6px;">
                            <div style="font-size: 12px; color: var(--text-muted); margin-bottom: 8px;">BASELINE PROMPT (Week 1)</div>
                            <div style="font-size: 13px; color: var(--text-secondary); font-family: monospace; line-height: 1.4;">
                                "Analyze this safety image for PPE violations. Look for workers without hard hats or hi-vis vests."
                            </div>
                        </div>
                        
                        <div style="text-align: center; color: var(--accent-blue); font-size: 12px;">
                            ⬇️ AUTO-LEARNING ENHANCEMENT ⬇️
                        </div>
                        
                        <div style="background: var(--bg-primary); padding: 16px; border-radius: 6px; border: 2px solid var(--accent-green);">
                            <div style="font-size: 12px; color: var(--accent-green); margin-bottom: 8px;">OPTIMIZED PROMPT (Current)</div>
                            <div style="font-size: 13px; color: var(--text-primary); font-family: monospace; line-height: 1.4;">
                                "Analyze this Terminal P1 quay crane safety image. CRITICAL: Yellow crane arms often mistaken for workers (35% of false positives). Distinguish between:
                                <br>• Crane structures: metallic yellow arms, hydraulic lines
                                <br>• Actual workers: human proportions, movement
                                <br>• Time context: Dawn/dusk lighting creates shadows
                                <br>Apply 85% confidence threshold for crane areas."
                            </div>
                        </div>
                    </div>
                    
                    <div style="margin-top: 16px;">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                            <span style="font-size: 12px; color: var(--text-muted);">Prompt Specificity</span>
                            <span style="font-size: 12px; font-weight: 600; color: var(--accent-green);">+340% more specific</span>
                        </div>
                        <div style="background: var(--bg-secondary); height: 6px; border-radius: 3px; overflow: hidden;">
                            <div style="background: var(--accent-green); height: 100%; width: 87%;"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Business Value & Future Projections -->
            <div class="card">
                <h3 style="margin-bottom: 24px; color: var(--accent-green);">💰 Business Value Generated by Auto-Learning</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px;">
                    <div style="background: var(--bg-secondary); padding: 20px; border-radius: 8px; text-align: center;">
                        <div style="font-size: 32px; font-weight: 700; color: var(--accent-green); margin-bottom: 8px;">+$8.5K</div>
                        <div style="font-size: 14px; color: var(--text-secondary); margin-bottom: 4px;">Additional Monthly Savings</div>
                        <div style="font-size: 12px; color: var(--text-muted);">Beyond baseline $30K savings</div>
                    </div>
                    <div style="background: var(--bg-secondary); padding: 20px; border-radius: 8px; text-align: center;">
                        <div style="font-size: 32px; font-weight: 700; color: var(--accent-blue); margin-bottom: 8px;">450</div>
                        <div style="font-size: 14px; color: var(--text-secondary); margin-bottom: 4px;">Extra Hours Saved</div>
                        <div style="font-size: 12px; color: var(--text-muted);">From pattern optimizations</div>
                    </div>
                    <div style="background: var(--bg-secondary); padding: 20px; border-radius: 8px; text-align: center;">
                        <div style="font-size: 32px; font-weight: 700; color: var(--accent-orange); margin-bottom: 8px;">50%</div>
                        <div style="font-size: 14px; color: var(--text-secondary); margin-bottom: 4px;">Faster New Site Setup</div>
                        <div style="font-size: 12px; color: var(--text-muted);">Learned patterns transfer</div>
                    </div>
                    <div style="background: var(--bg-secondary); padding: 20px; border-radius: 8px; text-align: center;">
                        <div style="font-size: 32px; font-weight: 700; color: var(--accent-green); margin-bottom: 8px;">92%</div>
                        <div style="font-size: 14px; color: var(--text-secondary); margin-bottom: 4px;">Pattern Recognition Rate</div>
                        <div style="font-size: 12px; color: var(--text-muted);">23 of 25 patterns found</div>
                    </div>
                </div>
                
                <div style="background: var(--bg-secondary); padding: 20px; border-radius: 8px;">
                    <h4 style="margin-bottom: 16px; color: var(--accent-blue);">📈 6-Month Projection</h4>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                        <div>
                            <div style="font-size: 14px; font-weight: 600; margin-bottom: 12px;">Expected Improvements</div>
                            <ul style="list-style: none; padding: 0; color: var(--text-secondary);">
                                <li style="margin-bottom: 8px; display: flex; align-items: center; gap: 8px;">
                                    <span style="color: var(--accent-green);">✓</span>
                                    Accuracy: 84.7% → 90%+ (seasonal learning)
                                </li>
                                <li style="margin-bottom: 8px; display: flex; align-items: center; gap: 8px;">
                                    <span style="color: var(--accent-green);">✓</span>
                                    Processing speed: 847ms → 650ms (optimization)
                                </li>
                                <li style="margin-bottom: 8px; display: flex; align-items: center; gap: 8px;">
                                    <span style="color: var(--accent-green);">✓</span>
                                    New terminal deployment: 3 weeks → 1.5 weeks
                                </li>
                                <li style="margin-bottom: 8px; display: flex; align-items: center; gap: 8px;">
                                    <span style="color: var(--accent-green);">✓</span>
                                    Additional savings: +$15K/month
                                </li>
                            </ul>
                        </div>
                        <div>
                            <div style="font-size: 14px; font-weight: 600; margin-bottom: 12px;">Competitive Advantages</div>
                            <ul style="list-style: none; padding: 0; color: var(--text-secondary);">
                                <li style="margin-bottom: 8px; display: flex; align-items: center; gap: 8px;">
                                    <span style="color: var(--accent-blue);">🛡️</span>
                                    Site-specific expertise (competitive moat)
                                </li>
                                <li style="margin-bottom: 8px; display: flex; align-items: center; gap: 8px;">
                                    <span style="color: var(--accent-blue);">🔮</span>
                                    Predictive maintenance insights
                                </li>
                                <li style="margin-bottom: 8px; display: flex; align-items: center; gap: 8px;">
                                    <span style="color: var(--accent-blue);">📚</span>
                                    Knowledge base for training programs
                                </li>
                                <li style="margin-bottom: 8px; display: flex; align-items: center; gap: 8px;">
                                    <span style="color: var(--accent-blue);">🚀</span>
                                    Platform for new terminal types
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Simple page navigation
        function showPage(pageId) {
            // Hide all pages
            document.querySelectorAll('.page').forEach(page => {
                page.classList.remove('active');
            });
            
            // Remove active class from all nav links
            document.querySelectorAll('.nav-link').forEach(link => {
                link.classList.remove('active');
            });
            
            // Show selected page
            document.getElementById(pageId).classList.add('active');
            
            // Add active class to corresponding nav link
            document.querySelector(`[data-page="${pageId}"]`).classList.add('active');
        }

        // Add click handlers to navigation links
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const pageId = link.getAttribute('data-page');
                showPage(pageId);
            });
        });
    </script>
</body>
</html>