#!/usr/bin/env python3
"""
Analyze the REAL results from Round 3 production test
First 30 cases show the true performance
"""

import json

def analyze_real_results():
    print("="*80)
    print("ROUND 3 PRODUCTION - REAL RESULTS ANALYSIS (30 CASES)")
    print("="*80)
    
    # Load progress
    with open('round3_production_real_progress.json', 'r') as f:
        progress = json.load(f)
    
    results = progress['results']
    
    # Calculate metrics
    total = len(results)
    correct = sum(r['correct'] for r in results)
    
    actual_fps = [r for r in results if r['actual_fp']]
    actual_valid = [r for r in results if not r['actual_fp']]
    
    fp_detected = sum(r['predicted_fp'] for r in actual_fps)
    valid_protected = sum(not r['predicted_fp'] for r in actual_valid) if actual_valid else 0
    
    print(f"\n📊 REAL METRICS (30 CASES):")
    print(f"├─ Overall Accuracy: {correct}/{total} = {correct/total*100:.1f}%")
    print(f"├─ FP Detection: {fp_detected}/{len(actual_fps)} = {fp_detected/len(actual_fps)*100:.1f}%")
    print(f"└─ Valid Protection: {valid_protected}/{len(actual_valid)} = {valid_protected/len(actual_valid)*100:.1f}%" if actual_valid else "└─ Valid Protection: No valid cases yet")
    
    print(f"\n🔍 WHAT'S HAPPENING:")
    print(f"├─ All 30 cases so far are FALSE POSITIVES")
    print(f"├─ Only 7 out of 30 correctly identified (23.3%)")
    print(f"└─ System is marking most FPs as valid violations!")
    
    # Analyze by entity type
    structure_cases = [r for r in results if r['entity'] == 'STRUCTURE']
    person_cases = [r for r in results if r['entity'] == 'PERSON']
    
    print(f"\n📈 ENTITY DETECTION:")
    print(f"├─ Identified as STRUCTURE: {len(structure_cases)}")
    print(f"│  └─ Correct FP detection: {sum(r['predicted_fp'] for r in structure_cases)}/{len(structure_cases)}")
    print(f"└─ Identified as PERSON: {len(person_cases)}")
    print(f"   └─ Correct FP detection: {sum(r['predicted_fp'] for r in person_cases)}/{len(person_cases)}")
    
    # Analyze violations detected
    violations_detected = [r for r in results if r['violations'] != 'None']
    print(f"\n⚠️  VIOLATIONS DETECTED IN FALSE POSITIVES:")
    print(f"Total cases with violations: {len(violations_detected)}")
    
    violation_types = {}
    for r in violations_detected:
        v = r['violations']
        if 'helmet' in v.lower():
            violation_types['Missing helmet'] = violation_types.get('Missing helmet', 0) + 1
        if 'vest' in v.lower():
            violation_types['Vest issues'] = violation_types.get('Vest issues', 0) + 1
        if 'mobile' in v.lower() or 'phone' in v.lower():
            violation_types['Mobile phone'] = violation_types.get('Mobile phone', 0) + 1
        if 'equipment' in v.lower():
            violation_types['Missing equipment'] = violation_types.get('Missing equipment', 0) + 1
    
    for vtype, count in violation_types.items():
        print(f"├─ {vtype}: {count} cases")
    
    print(f"\n💡 CRITICAL INSIGHTS:")
    print("1. The system is SEEING violations that don't exist")
    print("2. Even structures are sometimes not marked as FP")
    print("3. The prompt is too safety-focused, creating phantom violations")
    print("4. Real performance is FAR below projections")
    
    print(f"\n📊 COMPARISON:")
    print("┌─────────────────┬────────────┬────────────┐")
    print("│ Metric          │ Projected  │   REAL     │")
    print("├─────────────────┼────────────┼────────────┤")
    print("│ Accuracy        │   91.7%    │   23.3%    │")
    print("│ FP Detection    │   81.3%    │   23.3%    │")
    print("│ Valid Protection│   99.1%    │    TBD     │")
    print("└─────────────────┴────────────┴────────────┘")
    
    print(f"\n🚨 CONCLUSION:")
    print("The Round 3 'production' configuration based on small sample")
    print("auto-learning DOES NOT work on the full dataset!")
    print("The system is hallucinating violations in false positive cases.")
    print("="*80)

if __name__ == "__main__":
    analyze_real_results()