#!/bin/bash

echo "🔧 VALO System Complete Fix & Start"
echo "===================================="

# Stop all Docker containers
echo "1. Stopping all containers..."
sudo docker stop $(sudo docker ps -aq) 2>/dev/null || echo "No containers to stop"

# Remove all containers
echo "2. Removing all containers..."
sudo docker rm $(sudo docker ps -aq) 2>/dev/null || echo "No containers to remove"

# Remove all images
echo "3. Removing all images..."  
sudo docker rmi $(sudo docker images -q) 2>/dev/null || echo "No images to remove"

# Prune everything
echo "4. Pruning Docker system..."
sudo docker system prune -af

# Remove any volumes
echo "5. Removing volumes..."
sudo docker volume prune -f

# Clear networks
echo "6. Clearing networks..."
sudo docker network prune -f

echo
echo "7. Verifying correct configuration..."
echo "   - Using docker-compose.yml with port 5001"
echo "   - PostgreSQL will be on port 5433"

# Check current directory
echo "   - Current directory: $(pwd)"

# Show the port configuration
echo "   - Port configuration:"
grep -A 2 -B 2 "5001:5000" docker-compose.yml

echo
echo "8. Starting VALO system with clean state..."
echo "   Dashboard will be at: http://localhost:5001"
echo

sudo docker-compose up --build