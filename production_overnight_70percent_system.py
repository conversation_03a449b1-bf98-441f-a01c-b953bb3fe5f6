#!/usr/bin/env python3
"""
Production-Realistic Overnight System - Achieve 70% FP Detection
NO REMARKS ALLOWED - Only image and infringement type
"""
import asyncio
import json
import aiohttp
import logging
from datetime import datetime
import base64
import os
import time
from typing import Dict, List, Any, Optional

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('overnight_production_test.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ProductionApproachTester:
    def __init__(self):
        self.vlm_endpoint = "http://100.106.127.35:9500/v1/chat/completions"
        self.model = "VLM-38B-AWQ"
        self.target_fp_rate = 70.0
        self.best_result = {'approach': None, 'fp_rate': 0, 'valid_rate': 100}
        self.all_results = {}
        
    def load_test_data(self):
        """Load data WITHOUT using remarks"""
        with open('valo_batch_round3_complete.json', 'r') as f:
            data = json.load(f)
            cases = []
            for case in data['results']:
                # Production-realistic: Only use what system would have
                production_case = {
                    'case_number': case['case_number'],
                    'cropped_image': case['cropped_image'],
                    'infringement_type': case['infringement_type'],
                    'is_false_positive': case['is_false_positive'],
                    # Hidden for analysis only - NOT used in prompts
                    '_hidden_remarks': case.get('remarks', ''),
                    '_has_ppe_pattern': any(kw in case.get('remarks', '').upper() 
                                          for kw in ['FULL PPE', 'PROPER PPE', 'WEARING PPE'])
                }
                cases.append(production_case)
            return cases
    
    def get_approach_prompt(self, approach_name: str, case: Dict) -> str:
        """Generate prompts for different approaches - NO REMARKS USED"""
        
        prompts = {
            'aggressive_ppe': f"""ALERT TYPE: {case['infringement_type']}

CRITICAL INSTRUCTION: Workers wearing visible safety gear (helmet + vest) are COMPLIANT.
If you can see safety equipment, this is likely a FALSE POSITIVE.

Analyze the image:
1. Are there people visible?
2. If yes, can you see ANY safety gear on them?
3. If safety gear is visible, mark as FALSE POSITIVE

Be aggressive in identifying false positives to reduce alert fatigue.
Is this a FALSE POSITIVE? Answer YES or NO.""",

            'visual_patterns': f"""SAFETY SYSTEM ALERT: {case['infringement_type']}

Common FALSE POSITIVE patterns:
- Workers clearly wearing helmets and vests
- Equipment/machinery without people
- Structures (cranes, vessels) mistaken for violations
- Poor lighting/shadows creating false alerts

Look for these patterns in the image.
Is this a FALSE POSITIVE? YES/NO with brief reason.""",

            'presumption_of_compliance': f"""ALERT: {case['infringement_type']}

IMPORTANT: Assume workers are trying to comply with safety rules.
Only flag as valid violation if you see CLEAR evidence of non-compliance.

Decision criteria:
- Visible safety gear = FALSE POSITIVE
- No people visible = FALSE POSITIVE  
- Unclear/ambiguous = FALSE POSITIVE (give benefit of doubt)
- Only obvious violations = NOT false positive

Is this a FALSE POSITIVE? YES/NO""",

            'two_stage_analysis': f"""STAGE 1 - DETECTION
Alert type: {case['infringement_type']}

First, identify what's in the image:
A) People with visible safety gear
B) People without visible safety gear
C) No people (equipment/structure only)
D) Unclear/cannot determine

STAGE 2 - DECISION
- If A or C or D → FALSE POSITIVE
- If B → NOT false positive

Based on two-stage analysis, is this a FALSE POSITIVE? YES/NO""",

            'context_inference': f"""AUTOMATED ALERT: {case['infringement_type']}

Infer context from the image:
- Industrial setting usually requires PPE
- If workers are in active work area and appear equipped, they're likely compliant
- Equipment-only scenes cannot have PPE violations

Use visual context to determine if this alert makes sense.
Is this a FALSE POSITIVE? YES/NO""",

            'statistical_bias': f"""SYSTEM ALERT: {case['infringement_type']}

FACT: 97% of safety alerts are false positives.
This means statistically, this alert is probably wrong.

Only mark as valid violation if you see:
- Clear person WITHOUT helmet
- Clear person WITHOUT safety vest
- Obvious safety breach

Otherwise, assume FALSE POSITIVE.
Is this a FALSE POSITIVE? YES/NO""",

            'enhanced_ppe_focus': f"""PPE COMPLIANCE CHECK
System flagged: {case['infringement_type']}

Your ONLY job: Check if people have safety gear.
- See helmet? → FALSE POSITIVE
- See vest? → FALSE POSITIVE
- See both? → DEFINITELY FALSE POSITIVE
- No people? → FALSE POSITIVE
- Can't tell? → FALSE POSITIVE

Is this a FALSE POSITIVE? YES/NO""",

            'equipment_first': f"""ALERT ANALYSIS: {case['infringement_type']}

First check: Is this just equipment/structure?
- Cranes, vessels, machinery → FALSE POSITIVE
- Empty areas → FALSE POSITIVE

If people present: Do they have safety gear?
- Yes → FALSE POSITIVE
- No → Valid violation

Is this a FALSE POSITIVE? YES/NO""",

            'confidence_gradient': f"""VIOLATION ALERT: {case['infringement_type']}

Rate your confidence:
- 90-100% sure it's false positive → YES
- 70-89% sure it's false positive → YES  
- 50-69% unsure → YES (err on side of false positive)
- 30-49% unsure → YES (reduce alerts)
- 0-29% sure it's real violation → NO

Is this a FALSE POSITIVE? YES/NO""",

            'worker_benefit': f"""SAFETY ALERT: {case['infringement_type']}

PRINCIPLE: Give workers the benefit of the doubt.
- Assume they're following rules unless proven otherwise
- If you see any safety gear, assume full compliance
- Unclear images = assume compliance

Only flag clear, obvious violations.
Is this a FALSE POSITIVE? YES/NO"""
        }
        
        return prompts.get(approach_name, prompts['aggressive_ppe'])
    
    async def test_single_case(self, session: aiohttp.ClientSession, case: Dict, 
                              approach_name: str, prompt: str) -> Optional[Dict]:
        """Test a single case with given approach"""
        try:
            # Read and encode image
            if not os.path.exists(case['cropped_image']):
                return None
                
            with open(case['cropped_image'], 'rb') as img_file:
                image_data = base64.b64encode(img_file.read()).decode('utf-8')
            
            payload = {
                "model": self.model,
                "messages": [{
                    "role": "user",
                    "content": [
                        {"type": "text", "text": prompt},
                        {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{image_data}"}}
                    ]
                }],
                "temperature": 0.1,
                "max_tokens": 150
            }
            
            async with session.post(self.vlm_endpoint, json=payload) as response:
                if response.status == 200:
                    result = await response.json()
                    content = result['choices'][0]['message']['content']
                    
                    # Parse decision
                    decision = "YES" in content.upper()[:50]
                    
                    return {
                        'case_number': case['case_number'],
                        'is_false_positive': case['is_false_positive'],
                        'predicted_fp': decision,
                        'approach': approach_name,
                        '_has_ppe': case['_has_ppe_pattern']
                    }
                return None
                
        except Exception as e:
            logger.error(f"Error in {approach_name}: {e}")
            return None
    
    async def test_approach(self, approach_name: str, sample_size: int = 200) -> Dict:
        """Test a specific approach on sample data"""
        logger.info(f"\n{'='*60}")
        logger.info(f"Testing Approach: {approach_name.upper()}")
        logger.info(f"{'='*60}")
        
        # Get sample
        test_cases = self.test_data[:sample_size]
        results = []
        
        async with aiohttp.ClientSession() as session:
            # Process in batches
            batch_size = 5
            for i in range(0, len(test_cases), batch_size):
                batch = test_cases[i:i+batch_size]
                tasks = []
                
                for case in batch:
                    prompt = self.get_approach_prompt(approach_name, case)
                    task = self.test_single_case(session, case, approach_name, prompt)
                    tasks.append(task)
                
                batch_results = await asyncio.gather(*tasks)
                results.extend([r for r in batch_results if r])
                
                # Progress update
                if len(results) % 20 == 0:
                    tp = sum(1 for r in results if r['is_false_positive'] and r['predicted_fp'])
                    fp_total = sum(1 for r in results if r['is_false_positive'])
                    current_rate = (tp / fp_total * 100) if fp_total > 0 else 0
                    logger.info(f"Progress: {len(results)}/{sample_size} | FP Rate: {current_rate:.1f}%")
                
                await asyncio.sleep(0.1)
        
        # Calculate final metrics
        tp = sum(1 for r in results if r['is_false_positive'] and r['predicted_fp'])
        tn = sum(1 for r in results if not r['is_false_positive'] and not r['predicted_fp'])
        fp = sum(1 for r in results if not r['is_false_positive'] and r['predicted_fp'])
        fn = sum(1 for r in results if r['is_false_positive'] and not r['predicted_fp'])
        
        fp_total = sum(1 for r in results if r['is_false_positive'])
        valid_total = sum(1 for r in results if not r['is_false_positive'])
        
        fp_rate = (tp / fp_total * 100) if fp_total > 0 else 0
        valid_rate = (tn / valid_total * 100) if valid_total > 0 else 100
        
        # PPE-specific analysis
        ppe_cases = [r for r in results if r['_has_ppe']]
        ppe_detected = sum(1 for r in ppe_cases if r['predicted_fp'])
        ppe_rate = (ppe_detected / len(ppe_cases) * 100) if ppe_cases else 0
        
        result = {
            'approach': approach_name,
            'fp_detection_rate': fp_rate,
            'valid_protection_rate': valid_rate,
            'ppe_detection_rate': ppe_rate,
            'total_cases': len(results),
            'confusion_matrix': {'tp': tp, 'tn': tn, 'fp': fp, 'fn': fn},
            'timestamp': datetime.now().isoformat()
        }
        
        logger.info(f"\nResults for {approach_name}:")
        logger.info(f"  FP Detection: {fp_rate:.1f}%")
        logger.info(f"  Valid Protection: {valid_rate:.1f}%")
        logger.info(f"  PPE Detection: {ppe_rate:.1f}%")
        
        # Check if this is our best result
        if fp_rate > self.best_result['fp_rate'] and valid_rate >= 90:
            self.best_result = {
                'approach': approach_name,
                'fp_rate': fp_rate,
                'valid_rate': valid_rate
            }
            logger.info(f"  🎯 NEW BEST RESULT!")
            
            # Save if we hit target
            if fp_rate >= self.target_fp_rate:
                logger.info(f"  🎉 TARGET ACHIEVED: {fp_rate:.1f}% >= {self.target_fp_rate}%")
                self.save_results(approach_name, result)
        
        return result
    
    def save_results(self, approach_name: str, result: Dict):
        """Save successful result"""
        filename = f"production_success_{approach_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(filename, 'w') as f:
            json.dump(result, f, indent=2)
        logger.info(f"Saved successful result to {filename}")
    
    async def run_overnight_testing(self):
        """Main overnight testing loop"""
        logger.info("="*80)
        logger.info("PRODUCTION-REALISTIC OVERNIGHT TESTING SYSTEM")
        logger.info("Target: 70% FP Detection without remarks")
        logger.info("="*80)
        
        # Load data
        self.test_data = self.load_test_data()
        logger.info(f"Loaded {len(self.test_data)} cases for testing")
        
        # Define approaches to test
        approaches = [
            'aggressive_ppe',
            'visual_patterns',
            'presumption_of_compliance',
            'two_stage_analysis',
            'context_inference',
            'statistical_bias',
            'enhanced_ppe_focus',
            'equipment_first',
            'confidence_gradient',
            'worker_benefit'
        ]
        
        # Test each approach
        round_num = 1
        for approach in approaches:
            logger.info(f"\n{'='*80}")
            logger.info(f"ROUND {round_num}: {approach}")
            logger.info(f"{'='*80}")
            
            result = await self.test_approach(approach)
            self.all_results[approach] = result
            
            # Save intermediate results
            with open('overnight_progress.json', 'w') as f:
                json.dump({
                    'current_round': round_num,
                    'best_result': self.best_result,
                    'all_results': self.all_results,
                    'timestamp': datetime.now().isoformat()
                }, f, indent=2)
            
            round_num += 1
            
            # If we hit target, continue testing other approaches
            if result['fp_detection_rate'] >= self.target_fp_rate:
                logger.info(f"\n🎯 Target achieved with {approach}!")
            
            # Brief pause between approaches
            await asyncio.sleep(5)
        
        # After testing all base approaches, try combinations if needed
        if self.best_result['fp_rate'] < self.target_fp_rate:
            logger.info("\n" + "="*80)
            logger.info("PHASE 2: Testing Combined Approaches")
            logger.info("="*80)
            
            # Test combinations of best performers
            await self.test_combinations()
        
        # Final report
        self.generate_final_report()
    
    async def test_combinations(self):
        """Test combinations of successful approaches"""
        # Get top 3 approaches
        sorted_approaches = sorted(self.all_results.items(), 
                                 key=lambda x: x[1]['fp_detection_rate'], 
                                 reverse=True)[:3]
        
        logger.info(f"\nCombining top approaches:")
        for approach, result in sorted_approaches:
            logger.info(f"  - {approach}: {result['fp_detection_rate']:.1f}%")
        
        # Create hybrid prompt
        hybrid_name = "hybrid_best_3"
        
        # Test the hybrid
        await self.test_approach(hybrid_name, sample_size=300)
    
    def generate_final_report(self):
        """Generate comprehensive final report"""
        logger.info("\n" + "="*80)
        logger.info("FINAL OVERNIGHT TESTING REPORT")
        logger.info("="*80)
        
        # Sort results by performance
        sorted_results = sorted(self.all_results.items(),
                               key=lambda x: x[1]['fp_detection_rate'],
                               reverse=True)
        
        logger.info("\nAll Approaches Ranked:")
        for i, (approach, result) in enumerate(sorted_results):
            logger.info(f"{i+1}. {approach}: {result['fp_detection_rate']:.1f}% FP, "
                       f"{result['valid_protection_rate']:.1f}% Valid")
        
        logger.info(f"\nBEST RESULT: {self.best_result['approach']} - "
                   f"{self.best_result['fp_rate']:.1f}% FP Detection")
        
        if self.best_result['fp_rate'] >= self.target_fp_rate:
            logger.info(f"\n✅ SUCCESS: Target of {self.target_fp_rate}% achieved!")
        else:
            logger.info(f"\n❌ Target not achieved. Best: {self.best_result['fp_rate']:.1f}%")
        
        # Save final report
        with open('production_overnight_final_report.json', 'w') as f:
            json.dump({
                'summary': {
                    'target': self.target_fp_rate,
                    'achieved': self.best_result['fp_rate'] >= self.target_fp_rate,
                    'best_result': self.best_result,
                    'total_approaches_tested': len(self.all_results)
                },
                'all_results': dict(sorted_results),
                'timestamp': datetime.now().isoformat()
            }, f, indent=2)

async def main():
    """Main entry point"""
    tester = ProductionApproachTester()
    
    try:
        await tester.run_overnight_testing()
    except KeyboardInterrupt:
        logger.info("\nTesting interrupted by user")
        tester.generate_final_report()
    except Exception as e:
        logger.error(f"Error in overnight testing: {e}")
        tester.generate_final_report()

if __name__ == "__main__":
    asyncio.run(main())