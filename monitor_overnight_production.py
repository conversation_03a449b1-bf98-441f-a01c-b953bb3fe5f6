#!/usr/bin/env python3
"""
Live Monitoring Dashboard for Overnight Production Testing
"""
import json
import time
import os
from datetime import datetime
import subprocess

def display_dashboard():
    """Display live monitoring dashboard"""
    while True:
        os.system('clear')
        print("="*80)
        print("🌙 OVERNIGHT PRODUCTION TESTING - LIVE MONITOR")
        print(f"Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("="*80)
        
        # Check if progress file exists
        if os.path.exists('overnight_progress.json'):
            with open('overnight_progress.json', 'r') as f:
                progress = json.load(f)
            
            print(f"\nCurrent Round: {progress.get('current_round', 'N/A')}")
            print(f"Approaches Tested: {len(progress.get('all_results', {}))}")
            
            best = progress.get('best_result', {})
            print(f"\n🏆 BEST RESULT SO FAR:")
            print(f"Approach: {best.get('approach', 'None yet')}")
            print(f"FP Detection: {best.get('fp_rate', 0):.1f}%")
            print(f"Valid Protection: {best.get('valid_rate', 100):.1f}%")
            
            target = 70.0
            if best.get('fp_rate', 0) >= target:
                print(f"\n✅ TARGET ACHIEVED! {best.get('fp_rate', 0):.1f}% >= {target}%")
            else:
                gap = target - best.get('fp_rate', 0)
                print(f"\n📊 Gap to target: {gap:.1f}%")
            
            # Show all results
            print("\n" + "-"*40)
            print("ALL APPROACHES TESTED:")
            print("-"*40)
            
            results = progress.get('all_results', {})
            sorted_results = sorted(results.items(), 
                                  key=lambda x: x[1].get('fp_detection_rate', 0), 
                                  reverse=True)
            
            for i, (approach, result) in enumerate(sorted_results[:10]):
                fp_rate = result.get('fp_detection_rate', 0)
                valid_rate = result.get('valid_protection_rate', 100)
                status = "✅" if fp_rate >= target else "❌"
                print(f"{i+1:2d}. {approach:25s} {fp_rate:5.1f}% FP, {valid_rate:5.1f}% Valid {status}")
        
        else:
            print("\nWaiting for testing to begin...")
        
        # Check log file
        if os.path.exists('overnight_production_test.log'):
            print("\n" + "-"*40)
            print("RECENT LOG ENTRIES:")
            print("-"*40)
            result = subprocess.run(['tail', '-n', '10', 'overnight_production_test.log'], 
                                  capture_output=True, text=True)
            print(result.stdout)
        
        print("\n[Press Ctrl+C to exit monitor]")
        time.sleep(10)  # Update every 10 seconds

if __name__ == "__main__":
    try:
        display_dashboard()
    except KeyboardInterrupt:
        print("\nMonitoring stopped.")
    except Exception as e:
        print(f"Error: {e}")