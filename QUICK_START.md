# AI-FARM Quick Start Guide

**One-command launch for the complete AI-FARM application** 🚀

## Prerequisites

### macOS/Linux
- Python 3.8+
- Node.js 16+
- npm

### Windows
- Python 3.8+
- Node.js 16+
- npm

## Quick Start Commands

### 🚀 Start Everything
```bash
# macOS/Linux
./ai-farm.sh start

# Windows
ai-farm.bat start
```

### 🐳 Start with Docker
```bash
# macOS/Linux
./ai-farm.sh start-docker

# Windows  
ai-farm.bat start-docker
```

### 🛑 Stop Everything
```bash
# macOS/Linux
./ai-farm.sh stop

# Windows
ai-farm.bat stop
```

## First Time Setup

### 1. Configure Environment
```bash
# macOS/Linux
./ai-farm.sh setup

# Windows
ai-farm.bat setup
```

### 2. Edit Configuration
Edit the `.env` file with your OpenAI API settings:
```bash
VLM_API_BASE_URL=https://api.openai.com
VLM_API_KEY=your-openai-api-key-here
VLM_MODEL_NAME=gpt-4.1-2025-04-14
```

### 3. Start Application
```bash
# macOS/Linux
./ai-farm.sh start

# Windows
ai-farm.bat start
```

## Access Points

Once started, the application will be available at:

- **🌐 Frontend Dashboard**: http://localhost:3000
- **🔧 Backend API**: http://localhost:8000  
- **📚 API Documentation**: http://localhost:8000/docs

## All Available Commands

| Command | Description |
|---------|-------------|
| `start` | Start complete application (Python + Node.js) |
| `start-docker` | Start with Docker Compose |
| `stop` | Stop all services |
| `restart` | Restart all services |
| `status` | Show service status |
| `setup` | Setup environment and dependencies |
| `help` | Show help information |

## Example Usage

```bash
# First time setup
./ai-farm.sh setup
# Edit .env file with your settings
./ai-farm.sh start

# Check status
./ai-farm.sh status

# View logs (Linux/macOS only)
./ai-farm.sh logs backend
./ai-farm.sh logs frontend

# Stop when done
./ai-farm.sh stop
```

## Troubleshooting

### Port Already in Use
```bash
# Check what's using the ports
lsof -i :3000  # Frontend
lsof -i :8000  # Backend

# Or stop everything and restart
./ai-farm.sh stop
./ai-farm.sh start
```

### Environment Issues
```bash
# Reset environment
rm .env
./ai-farm.sh setup
# Edit .env with correct settings
./ai-farm.sh start
```

### Docker Issues
```bash
# Reset Docker environment
docker-compose down -v
./ai-farm.sh start-docker
```

## What Gets Started

### Native Mode (`start`)
- **Backend**: FastAPI server with VLM integration
- **Frontend**: React development server
- **Database**: SQLite (embedded)
- **Logs**: Stored in `logs/` directory

### Docker Mode (`start-docker`)
- **Backend**: FastAPI in container
- **Frontend**: React app with Nginx
- **Database**: PostgreSQL container
- **Redis**: For caching and sessions

## Demo Ready Features

✅ **Complete AI-FARM demo interface**  
✅ **VLM integration for image analysis**  
✅ **Batch processing with real-time progress**  
✅ **ROI calculator with customer metrics**  
✅ **Auto-learning pattern detection**  
✅ **Professional customer presentation UI**  

## Development Notes

- **Hot Reload**: Both frontend and backend support live code changes
- **API Testing**: Use http://localhost:8000/docs for interactive API testing
- **Logs**: Check `logs/` directory for detailed service logs
- **Configuration**: All settings in `.env` file are hot-reloadable

## Production Deployment

For Ubuntu production deployment:
```bash
# Use Docker for production
./ai-farm.sh start-docker

# Or see docs/deployment/ for detailed production setup
```

---

**Ready to demo AI-FARM's 70% false positive reduction!** 🎯