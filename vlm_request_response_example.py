#!/usr/bin/env python3
"""
Show complete VLM request and response example
"""

import json

# The prompt we're using (explicit safety prompt)
PROMPT = """SAFETY VIOLATION DETECTION SYSTEM

ANALYZE BOTH PROVIDED IMAGES.

STEP 1: What is in the CROPPED image?
A) STRUCTURE ONLY (needs >90% confidence)
B) PERSON (any human features)  
C) UNCLEAR

STEP 2: Apply these EXACT rules:

IF STRUCTURE ONLY (>90% confident):
→ Output: FALSE POSITIVE: YES

IF PERSON DETECTED:
→ Check for ANY of these violations:
  • Missing helmet
  • Missing vest  
  • Vest not fastened
  • Mobile phone use
  • Missing required equipment
  • Wrong location
  • Unsafe behavior
  
  IF ANY VIOLATION FOUND:
  → Output: FALSE POSITIVE: NO
  
  IF NO VIOLATIONS:
  → Output: FALSE POSITIVE: YES

IF UNCLEAR:
→ Output: FALSE POSITIVE: NO (safety first)

IMPORTANT: The question is "Is this a FALSE POSITIVE?"
- Real violations must output: FALSE POSITIVE: NO
- Only non-violations output: FALSE POSITIVE: YES

OUTPUT FORMAT (use exactly):
FALSE POSITIVE: [YES or NO]
Detection: [STRUCTURE/PERSON/UNCLEAR]
Violations: [List any found, or "None"]

[Examples follow in actual prompt...]"""

# Sample VLM API Request
sample_request = {
    "model": "VLM-38B-AWQ",
    "messages": [{
        "role": "user",
        "content": [
            {
                "type": "text", 
                "text": PROMPT
            },
            {
                "type": "text", 
                "text": "\n\nSOURCE IMAGE:"
            },
            {
                "type": "image_url", 
                "image_url": {
                    "url": "data:image/jpeg;base64,/9j/4AAQSkZJRg..." # Base64 encoded source image
                }
            },
            {
                "type": "text", 
                "text": "\n\nCROPPED IMAGE:"
            },
            {
                "type": "image_url", 
                "image_url": {
                    "url": "data:image/jpeg;base64,/9j/4AAQSkZJRg..." # Base64 encoded cropped image
                }
            }
        ]
    }],
    "temperature": 0.1,
    "max_tokens": 200
}

# Example VLM Responses for different scenarios

# 1. Crane Structure (False Positive)
crane_structure_response = """FALSE POSITIVE: YES
Detection: STRUCTURE
Violations: None"""

# 2. Person with Full PPE (False Positive) 
full_ppe_response = """FALSE POSITIVE: YES
Detection: PERSON
Violations: None"""

# 3. Person Using Mobile Phone (Valid Violation)
mobile_phone_response = """FALSE POSITIVE: NO
Detection: PERSON
Violations: Mobile phone use"""

# 4. Person Missing Helmet (Valid Violation)
missing_helmet_response = """FALSE POSITIVE: NO
Detection: PERSON
Violations: Missing helmet"""

# 5. Unclear Image (Default to Safety)
unclear_response = """FALSE POSITIVE: NO
Detection: UNCLEAR
Violations: Cannot assess"""

def show_examples():
    """Display complete request/response examples"""
    
    print("VLM API REQUEST AND RESPONSE EXAMPLES")
    print("="*60)
    
    print("\n1. API ENDPOINT:")
    print("URL: http://**************:9500/v1/chat/completions")
    print("Method: POST")
    print("Content-Type: application/json")
    
    print("\n2. REQUEST STRUCTURE:")
    print(json.dumps(sample_request, indent=2)[:500] + "...")
    
    print("\n3. EXAMPLE RESPONSES:")
    
    examples = [
        ("Crane Structure", crane_structure_response, 
         "System correctly identifies industrial structure → False Positive"),
        
        ("Person with Full PPE", full_ppe_response,
         "Worker properly equipped with no violations → False Positive"),
        
        ("Mobile Phone Use", mobile_phone_response,
         "Behavioral violation detected → Valid Violation"),
        
        ("Missing Safety Equipment", missing_helmet_response,
         "PPE violation detected → Valid Violation"),
        
        ("Unclear/Poor Quality", unclear_response,
         "Cannot determine clearly → Valid Violation (safety default)")
    ]
    
    for i, (scenario, response, explanation) in enumerate(examples, 1):
        print(f"\n{'-'*50}")
        print(f"SCENARIO {i}: {scenario}")
        print(f"{'-'*50}")
        print("VLM Response:")
        print(response)
        print(f"\nExplanation: {explanation}")
    
    print("\n\n4. KEY DECISION LOGIC:")
    print("- Structure → Always FALSE POSITIVE: YES")
    print("- Person + Violation → Always FALSE POSITIVE: NO") 
    print("- Person + No Violation → FALSE POSITIVE: YES")
    print("- Unclear → Always FALSE POSITIVE: NO (safety first)")
    
    print("\n5. PYTHON CODE TO CALL VLM:")
    print("""
import requests
import base64

def call_vlm(source_image_path, cropped_image_path, prompt):
    # Encode images
    with open(source_image_path, 'rb') as f:
        source_b64 = base64.b64encode(f.read()).decode('utf-8')
    with open(cropped_image_path, 'rb') as f:
        cropped_b64 = base64.b64encode(f.read()).decode('utf-8')
    
    # Create request
    payload = {
        "model": "VLM-38B-AWQ",
        "messages": [{
            "role": "user",
            "content": [
                {"type": "text", "text": prompt},
                {"type": "text", "text": "\\n\\nSOURCE IMAGE:"},
                {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{source_b64}"}},
                {"type": "text", "text": "\\n\\nCROPPED IMAGE:"},
                {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{cropped_b64}"}}
            ]
        }],
        "temperature": 0.1,
        "max_tokens": 200
    }
    
    # Send request
    response = requests.post(
        "http://**************:9500/v1/chat/completions",
        json=payload,
        timeout=20
    )
    
    # Parse response
    if response.status_code == 200:
        result = response.json()
        vlm_response = result['choices'][0]['message']['content']
        
        # Extract decision
        is_false_positive = "YES" in vlm_response.split("FALSE POSITIVE:")[1][:5]
        
        return vlm_response, is_false_positive
    else:
        raise Exception(f"VLM API error: {response.status_code}")
""")

if __name__ == "__main__":
    show_examples()