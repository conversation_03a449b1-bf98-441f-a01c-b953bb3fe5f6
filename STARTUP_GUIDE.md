# AI-FARM Startup Guide

## Quick Start - One Command to Rule Them All

The AI-FARM application comes with comprehensive startup scripts that handle both backend and frontend services automatically.

### Option 1: Cross-Platform Python Launcher (Recommended)

```bash
# Works on Windows, macOS, and Linux
python3 launch-ai-farm.py
```

### Option 2: Platform-Specific Scripts

#### For macOS/Linux Users

```bash
# Simple launcher
./start.sh

# Or use the full script directly
chmod +x ai-farm.sh
./ai-farm.sh start
```

#### For Windows Users

```cmd
# Simple launcher
start.bat

# Or use the full script directly
ai-farm.bat start
```

### Option 3: Direct Script Execution

```bash
# macOS/Linux
./ai-farm.sh start

# Windows
ai-farm.bat start
```

## What the Startup Scripts Do

1. **Environment Check**: Verifies all prerequisites (Python 3, Node.js, npm)
2. **Backend Management**: 
   - Checks if backend is already running on localhost:8000
   - If not running, starts the FastAPI backend server
   - Creates Python virtual environment if needed
   - Installs dependencies automatically
3. **Frontend Management**:
   - Starts React development server on localhost:3000
   - Installs npm dependencies if needed
4. **Service Readiness**: Waits for both services to be fully ready
5. **Access Information**: Displays URLs for both services
6. **Graceful Shutdown**: Handles Ctrl+C to stop both services cleanly

## Available Commands

| Command | Description |
|---------|-------------|
| `start` | Start both backend and frontend services |
| `start-docker` | Start using Docker Compose |
| `stop` | Stop all AI-FARM services |
| `restart` | Restart all services |
| `status` | Check if services are running |
| `logs [service]` | View logs (backend/frontend/docker) |
| `setup` | Setup environment and dependencies |
| `help` | Show help message |

## Service URLs

Once started, you can access:

- **Frontend Dashboard**: http://localhost:3000
- **Backend API**: http://localhost:8000
- **API Documentation**: http://localhost:8000/docs
- **Health Check**: http://localhost:8000/health

## Examples

```bash
# Start everything
./ai-farm.sh start

# Check status
./ai-farm.sh status

# View backend logs
./ai-farm.sh logs backend

# Stop all services
./ai-farm.sh stop

# Restart after changes
./ai-farm.sh restart
```

## Troubleshooting

### Port Already in Use
- If port 8000 is already in use by another service, the script will detect this and warn you
- If the AI-FARM backend is already running, the script will detect this and skip starting it again
- Use `./ai-farm.sh status` to check what's running

### Dependencies Missing
- The script will automatically install Python and npm dependencies
- If you get permission errors, make sure you have write access to the project directory

### Environment Configuration
- The script will create a `.env` file from `.env.example` if it doesn't exist
- Make sure to configure your VLM API settings in the `.env` file

## Advanced Usage

### Running in Background
```bash
# Start services and run in background
nohup ./ai-farm.sh start > startup.log 2>&1 &

# Check what's running
./ai-farm.sh status
```

### Docker Mode
```bash
# Use Docker instead of native Python/Node
./ai-farm.sh start-docker
```

### Log Monitoring
```bash
# Monitor backend logs in real-time
./ai-farm.sh logs backend

# Monitor frontend logs in real-time
./ai-farm.sh logs frontend
```

## Signal Handling (macOS/Linux)

The bash script includes graceful shutdown handling:
- Press `Ctrl+C` during startup to stop all services cleanly
- Services are stopped using proper signals (SIGTERM then SIGKILL if needed)
- Process IDs are tracked for reliable service management

This ensures no orphaned processes are left running when you stop the application.