#!/usr/bin/env python3
"""Wait for Round 4 completion and display results"""

import json
import os
import time
from datetime import datetime

print("Monitoring Round 4 Equipment Pattern Recognition...")
print("Started at 13:28, expected completion: 13:38-13:43")
print("-" * 60)

check_count = 0
while True:
    check_count += 1
    
    # Check for completion files
    completion_files = [
        'valo_round4_safe_complete.json',
        'valo_batch_round4_complete.json',
        'valo_round4_equipment_complete.json',
        'VALO_70_PERCENT_ACHIEVEMENT_FINAL.json'
    ]
    
    for file in completion_files:
        if os.path.exists(file):
            print(f"\n✓ Found {file}!")
            
            with open(file, 'r') as f:
                data = json.load(f)
                
            if 'stats' in data:
                stats = data['stats']
                print(f"\nRound 4 Results:")
                print(f"  Valid Protection: {stats.get('valid_protection_rate', 0):.1f}%")
                print(f"  FP Detection: {stats.get('fp_detection_rate', 0):.1f}%")
                print(f"  Total Cases: {stats.get('total_cases', 0)}")
                
                if stats.get('fp_detection_rate', 0) >= 70:
                    print("\n🎯 TARGET ACHIEVED! 70% FP reduction with 100% safety!")
                else:
                    gap = 70 - stats.get('fp_detection_rate', 0)
                    print(f"\n  Gap to 70% target: {gap:.1f}%")
                    print("  Round 5 will be needed...")
            
            elif 'success' in data:
                print("\n🎯 FINAL TARGET ACHIEVED!")
                print(f"  Success: {data['success']}")
                print(f"  Rounds needed: {data.get('rounds_completed', 'N/A')}")
            
            exit(0)
    
    # Check if process is still running
    import subprocess
    try:
        ps = subprocess.check_output(['ps', '-p', '913041'], text=True)
        if 'execute_round4' not in ps:
            print("\nProcess completed but no output file found yet...")
    except:
        print("\nRound 4 process has finished. Checking for results...")
        time.sleep(5)
        continue
    
    # Progress indicator
    if check_count % 6 == 0:  # Every 30 seconds
        print(f"[{datetime.now().strftime('%H:%M:%S')}] Still processing... (check #{check_count})")
    
    time.sleep(5)