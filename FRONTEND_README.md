# AI-FARM Frontend

A complete React TypeScript frontend application for the AI-FARM (False Positive Alert Reduction System) platform.

## 🚀 Quick Start

### Prerequisites
- Node.js 16+ and npm
- AI-FARM backend running on localhost:8000

### Installation & Running

1. **Auto Start (Recommended)**:
   ```bash
   ./start-frontend.sh
   ```

2. **Manual Start**:
   ```bash
   cd frontend
   npm install
   npm start
   ```

The frontend will be available at: **http://localhost:3000**

## 🏗️ Architecture

### Technology Stack
- **React 18** - Modern React with hooks
- **TypeScript** - Type safety and enhanced developer experience
- **Tailwind CSS** - Utility-first CSS framework
- **React Router** - Client-side routing
- **React Query** - Server state management
- **Recharts** - Data visualization
- **Lucide React** - Modern icon library
- **React Hot Toast** - Toast notifications
- **Axios** - HTTP client

### Project Structure
```
frontend/
├── src/
│   ├── components/          # Reusable UI components
│   │   ├── ui/             # Basic UI building blocks
│   │   ├── charts/         # Data visualization components
│   │   ├── dashboard/      # Dashboard-specific components
│   │   └── layout/         # Layout components
│   ├── pages/              # Application pages
│   ├── services/           # API service layer
│   ├── types/              # TypeScript type definitions
│   ├── utils/              # Utility functions
│   └── hooks/              # Custom React hooks
├── public/                 # Static assets
└── package.json           # Dependencies and scripts
```

## 📱 Application Features

### 1. Landing Page (`/`)
- Problem statement and value proposition
- Key metrics and benefits
- Call-to-action to start demo

### 2. Upload Page (`/upload`)
- CSV file upload for case data
- Optional images ZIP upload
- Processing configuration options
- Custom threshold settings

### 3. Processing Page (`/processing/:batchId`)
- Real-time processing progress
- Live status updates
- Processing timeline visualization
- Error handling and retry options

### 4. Results Page (`/results/:batchId`)
- Detailed processing results
- Image gallery with confidence scores
- Filtering and sorting capabilities
- Case-by-case analysis

### 5. ROI Calculator (`/roi/:batchId`)
- Financial impact calculations
- Cost savings projections
- ROI analysis and payback period
- Customizable business parameters

### 6. Insights Page (`/insights/:batchId`)
- AI learning insights
- Pattern detection results
- Performance optimization recommendations
- Customer environment analysis

### 7. Dashboard Page (`/dashboard`)
- Sample data demonstrations
- Historical performance metrics
- System health monitoring

## 🎨 UI Components

### Core Components
- **Button** - Versatile button with variants and states
- **Card** - Content containers with headers and footers
- **Alert** - Status messages and notifications
- **MetricCard** - Key performance indicator display
- **FileUpload** - Drag-and-drop file upload
- **LoadingSpinner** - Loading states and overlays
- **ProgressBar** - Progress indication

### Dashboard Components
- **ProcessingProgress** - Real-time batch processing status
- **ImageGallery** - Violation image display and analysis
- **MetricGrid** - Organized metric display

### Chart Components
- **ProcessingResultsChart** - Pie chart for alert distribution
- **ConfidenceDistributionChart** - Confidence score histogram
- **ProcessingTimeChart** - Timeline visualization
- **ComparisonChart** - Before/after comparisons
- **ROIProjectionChart** - Financial projections

## 🔧 API Integration

### Service Layer
- **ApiClient** - Centralized HTTP client with error handling
- **BatchService** - Batch processing operations
- **HealthService** - System health monitoring
- **MetricsService** - Performance metrics

### Key Features
- Automatic request/response interceptors
- Error handling with retry logic
- TypeScript type safety
- Real-time progress polling
- File upload support

## 🎯 Key Features

### Real-time Updates
- WebSocket connections for live progress
- Automatic polling for batch status
- Live metric updates

### Professional UI
- Customer demo-ready interface
- Responsive design for all devices
- Accessibility compliance
- Dark/light mode support

### Data Visualization
- Interactive charts and graphs
- Filtering and sorting capabilities
- Export functionality
- Print-friendly layouts

### Error Handling
- Comprehensive error boundaries
- User-friendly error messages
- Automatic retry mechanisms
- Graceful degradation

## 🚦 Development

### Available Scripts
```bash
npm start          # Start development server
npm build          # Build for production
npm test           # Run test suite
npm run eject      # Eject from Create React App
```

### Environment Variables
```bash
REACT_APP_API_URL=http://localhost:8000  # Backend API URL
```

### Development Guidelines
- Use TypeScript for all new code
- Follow React hooks patterns
- Implement proper error boundaries
- Write unit tests for components
- Use semantic HTML elements
- Follow accessibility guidelines

## 📊 Performance

### Optimization Features
- Code splitting with React.lazy
- Image lazy loading
- Memoized components
- Virtual scrolling for large lists
- Bundle size optimization

### Monitoring
- Performance metrics tracking
- Error reporting
- User analytics
- Load time monitoring

## 🔒 Security

### Security Features
- XSS protection
- CSRF protection
- Secure file uploads
- Input validation
- API authentication

## 🌐 Browser Support

- Chrome 88+
- Firefox 85+
- Safari 14+
- Edge 88+

## 📝 Contributing

1. Follow TypeScript strict mode
2. Use Tailwind CSS for styling
3. Write comprehensive tests
4. Document component props
5. Follow React best practices

## 🚀 Deployment

### Production Build
```bash
npm run build
```

### Docker Support
See `frontend/Dockerfile` for containerized deployment.

### Environment Setup
- Configure environment variables
- Set up CDN for static assets
- Enable gzip compression
- Set up monitoring

---

**Note**: This frontend is designed to work seamlessly with the AI-FARM backend API. Ensure the backend is running on localhost:8000 for full functionality.