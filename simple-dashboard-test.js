#!/usr/bin/env node

const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

async function testDashboard() {
  let browser = null;
  
  try {
    console.log('🚀 Starting simple dashboard test...');
    
    // Ensure screenshots directory exists
    const screenshotDir = './data/screenshots';
    if (!fs.existsSync(screenshotDir)) {
      fs.mkdirSync(screenshotDir, { recursive: true });
    }

    // Launch browser
    browser = await puppeteer.launch({
      headless: true,
      defaultViewport: { width: 1920, height: 1080 },
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });

    const page = await browser.newPage();
    
    // Capture console logs
    const consoleLogs = [];
    page.on('console', (msg) => {
      consoleLogs.push(`[${msg.type()}] ${msg.text()}`);
      console.log(`📝 Console [${msg.type()}]:`, msg.text());
    });

    // Capture errors
    const errors = [];
    page.on('pageerror', (error) => {
      errors.push(error.message);
      console.log('❌ Page Error:', error.message);
    });

    // Test 1: Try to load the main page
    console.log('🔍 Testing main page load...');
    try {
      await page.goto('http://localhost:3000/', {
        waitUntil: 'domcontentloaded',
        timeout: 15000
      });
      console.log('✅ Main page loaded successfully');

      await page.screenshot({
        path: path.join(screenshotDir, 'main-page.png'),
        fullPage: true
      });
    } catch (error) {
      console.log('❌ Main page failed to load:', error.message);
    }

    // Test 2: Try to load the data analysis page
    console.log('🔍 Testing data analysis page...');
    try {
      await page.goto('http://localhost:3000/data-analysis', {
        waitUntil: 'domcontentloaded',
        timeout: 15000 
      });
      console.log('✅ Data analysis page loaded successfully');
      
      // Wait a bit for any dynamic content
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      await page.screenshot({ 
        path: path.join(screenshotDir, 'data-analysis-page.png'),
        fullPage: true 
      });
      
      // Check for specific elements
      const pageInfo = await page.evaluate(() => {
        return {
          title: document.title,
          h1Text: document.querySelector('h1')?.textContent || 'No H1 found',
          cardCount: document.querySelectorAll('.surveillance-card').length,
          buttonCount: document.querySelectorAll('button').length,
          hasAutoRefresh: !!document.querySelector('input[type="checkbox"]'),
          bodyClasses: document.body.className,
          hasReactRoot: !!document.getElementById('root')
        };
      });
      
      console.log('📊 Page Info:', pageInfo);
      
    } catch (error) {
      console.log('❌ Data analysis page failed to load:', error.message);
      
      // Take screenshot of error state
      await page.screenshot({ 
        path: path.join(screenshotDir, 'data-analysis-error.png'),
        fullPage: true 
      });
    }

    // Test 3: Check backend connectivity
    console.log('🔍 Testing backend connectivity...');
    try {
      const response = await page.evaluate(async () => {
        try {
          const res = await fetch('http://localhost:8000/health');
          return {
            status: res.status,
            ok: res.ok,
            text: await res.text()
          };
        } catch (error) {
          return { error: error.message };
        }
      });
      
      if (response.ok) {
        console.log('✅ Backend is accessible:', response);
      } else {
        console.log('❌ Backend connectivity issue:', response);
      }
    } catch (error) {
      console.log('❌ Backend test failed:', error.message);
    }

    // Generate summary report
    const report = {
      timestamp: new Date().toISOString(),
      consoleLogs,
      errors,
      summary: {
        consoleLogCount: consoleLogs.length,
        errorCount: errors.length,
        screenshotsTaken: fs.readdirSync(screenshotDir).filter(f => f.endsWith('.png')).length
      }
    };

    fs.writeFileSync(path.join(screenshotDir, 'test-report.json'), JSON.stringify(report, null, 2));
    
    console.log('\n📋 TEST SUMMARY:');
    console.log('================');
    console.log(`Console logs: ${consoleLogs.length}`);
    console.log(`Errors: ${errors.length}`);
    console.log(`Screenshots: ${report.summary.screenshotsTaken}`);
    
    if (errors.length > 0) {
      console.log('\n🚨 ERRORS:');
      errors.forEach(error => console.log(`- ${error}`));
    }
    
    console.log(`\n📁 Results saved to: ${screenshotDir}`);

  } catch (error) {
    console.error('💥 Test failed:', error);
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

testDashboard().then(() => {
  console.log('🏁 Test completed');
}).catch(error => {
  console.error('💥 Test crashed:', error);
});
