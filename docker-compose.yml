# Docker Compose file for VALO Integrated System

services:
  # PostgreSQL Database for VALO Integrated System
  postgres:
    image: postgres:15-alpine
    container_name: valo-postgres
    environment:
      POSTGRES_DB: valo_system
      POSTGRES_USER: valo_user
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-valo_password}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./valo_integrated_system/database/postgresql_schema.sql:/docker-entrypoint-initdb.d/01-schema.sql
    ports:
      - "5433:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U valo_user -d valo_system"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - valo-network
    restart: unless-stopped

  # VALO Integrated System
  valo-system:
    build:
      context: ./valo_integrated_system
      dockerfile: Dockerfile
    container_name: valo-system
    depends_on:
      postgres:
        condition: service_healthy
    environment:
      # Database configuration
      DB_HOST: postgres
      DB_PORT: 5432
      DB_NAME: valo_system
      DB_USER: valo_user
      DB_PASSWORD: ${POSTGRES_PASSWORD:-valo_password}
      
      # VLM configuration
      VLM_URL: ${VLM_URL:-http://**************:9500/v1/chat/completions}
      VLM_MODEL: ${VLM_MODEL:-VLM-38B-AWQ}
      VLM_TEMPERATURE: ${VLM_TEMPERATURE:-0.1}
      VLM_MAX_TOKENS: ${VLM_MAX_TOKENS:-600}
      
      # Web configuration
      WEB_HOST: 0.0.0.0
      WEB_PORT: 5000
      
      # Processing configuration
      PROCESSING_WORKERS: ${PROCESSING_WORKERS:-3}
      PROCESSING_BATCH_SIZE: ${PROCESSING_BATCH_SIZE:-10}
    ports:
      - "5001:5000"
    volumes:
      - ./valo_integrated_system/config.yaml:/app/config.yaml:ro
      - ./valo_integrated_system/logs:/app/logs
      - ./valo_integrated_system/data:/app/data
      - ./ai_farm_images_fixed_250703:/app/ai_farm_images_fixed_250703:ro
    networks:
      - valo-network
    command: python orchestrator.py dashboard
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "python", "-c", "import requests; requests.get('http://localhost:5000/api/agents/status', timeout=5)"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

volumes:
  postgres_data:

networks:
  valo-network:
    driver: bridge