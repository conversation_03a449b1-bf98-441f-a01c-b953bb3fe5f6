-- AI-FARM Database Initialization Script
-- This script creates the initial database structure and users

-- Create extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Create application schema
CREATE SCHEMA IF NOT EXISTS ai_farm;

-- Set default search path
SET search_path = ai_farm, public;

-- Create demo data table for development/testing
CREATE TABLE IF NOT EXISTS demo_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    session_name VARCHAR(255) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    status VARCHAR(50) DEFAULT 'active',
    metadata JSONB DEFAULT '{}',
    CONSTRAINT unique_session_name UNIQUE (session_name)
);

-- Create demo processing results table
CREATE TABLE IF NOT EXISTS demo_processing_results (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    session_id UUID REFERENCES demo_sessions(id) ON DELETE CASCADE,
    image_path VARCHAR(500) NOT NULL,
    original_category VARCHAR(100),
    predicted_category VARCHAR(100),
    confidence_score DECIMAL(5,2),
    processing_time_ms INTEGER,
    vlm_response JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    status VARCHAR(50) DEFAULT 'completed'
);

-- Create batch processing jobs table
CREATE TABLE IF NOT EXISTS batch_jobs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    job_name VARCHAR(255) NOT NULL,
    status VARCHAR(50) DEFAULT 'pending',
    total_images INTEGER DEFAULT 0,
    processed_images INTEGER DEFAULT 0,
    successful_images INTEGER DEFAULT 0,
    failed_images INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    error_message TEXT,
    metadata JSONB DEFAULT '{}'
);

-- Create learning patterns table for auto-learning
CREATE TABLE IF NOT EXISTS learning_patterns (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    pattern_type VARCHAR(100) NOT NULL,
    pattern_data JSONB NOT NULL,
    confidence_threshold DECIMAL(5,2),
    success_rate DECIMAL(5,2),
    usage_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT true
);

-- Create confidence calibration table
CREATE TABLE IF NOT EXISTS confidence_calibration (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    category VARCHAR(100) NOT NULL,
    predicted_confidence DECIMAL(5,2) NOT NULL,
    actual_accuracy DECIMAL(5,2) NOT NULL,
    sample_count INTEGER DEFAULT 1,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT unique_category_confidence UNIQUE (category, predicted_confidence)
);

-- Create performance metrics table
CREATE TABLE IF NOT EXISTS performance_metrics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    metric_name VARCHAR(100) NOT NULL,
    metric_value DECIMAL(10,4) NOT NULL,
    metric_type VARCHAR(50) NOT NULL, -- 'counter', 'gauge', 'histogram'
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    metadata JSONB DEFAULT '{}'
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_demo_sessions_created_at ON demo_sessions(created_at);
CREATE INDEX IF NOT EXISTS idx_demo_sessions_status ON demo_sessions(status);
CREATE INDEX IF NOT EXISTS idx_demo_processing_results_session_id ON demo_processing_results(session_id);
CREATE INDEX IF NOT EXISTS idx_demo_processing_results_created_at ON demo_processing_results(created_at);
CREATE INDEX IF NOT EXISTS idx_demo_processing_results_confidence ON demo_processing_results(confidence_score);
CREATE INDEX IF NOT EXISTS idx_batch_jobs_status ON batch_jobs(status);
CREATE INDEX IF NOT EXISTS idx_batch_jobs_created_at ON batch_jobs(created_at);
CREATE INDEX IF NOT EXISTS idx_learning_patterns_type ON learning_patterns(pattern_type);
CREATE INDEX IF NOT EXISTS idx_learning_patterns_active ON learning_patterns(is_active);
CREATE INDEX IF NOT EXISTS idx_confidence_calibration_category ON confidence_calibration(category);
CREATE INDEX IF NOT EXISTS idx_performance_metrics_name_timestamp ON performance_metrics(metric_name, timestamp);

-- Create trigger function to update timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for automatic timestamp updates
CREATE TRIGGER update_demo_sessions_updated_at 
    BEFORE UPDATE ON demo_sessions 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_learning_patterns_updated_at 
    BEFORE UPDATE ON learning_patterns 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_confidence_calibration_updated_at 
    BEFORE UPDATE ON confidence_calibration 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert default confidence thresholds
INSERT INTO confidence_calibration (category, predicted_confidence, actual_accuracy, sample_count) 
VALUES 
    ('structure_misid', 70.0, 85.0, 100),
    ('proper_ppe', 65.0, 80.0, 100),
    ('no_violation', 75.0, 90.0, 100),
    ('default', 70.0, 82.0, 100)
ON CONFLICT (category, predicted_confidence) DO NOTHING;

-- Grant permissions (if needed for specific user)
-- GRANT ALL PRIVILEGES ON SCHEMA ai_farm TO ai_farm_user;
-- GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA ai_farm TO ai_farm_user;
-- GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA ai_farm TO ai_farm_user;

-- Add some initial demo data for development
INSERT INTO demo_sessions (session_name, status, metadata) VALUES
    ('Development Test Session', 'active', '{"description": "Initial development session", "version": "1.0.0"}'),
    ('Sample Batch Processing', 'completed', '{"description": "Sample batch processing session", "batch_size": 10}')
ON CONFLICT (session_name) DO NOTHING;