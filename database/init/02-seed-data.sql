-- AI-FARM Database Seed Data
-- This script inserts sample data for development and testing

SET search_path = ai_farm, public;

-- Insert sample learning patterns
INSERT INTO learning_patterns (pattern_type, pattern_data, confidence_threshold, success_rate, usage_count, is_active) VALUES
    ('structure_misidentification', 
     '{"keywords": ["structure", "building", "facility"], "confidence_boost": 5, "common_misclassifications": ["ppe_violation", "safety_hazard"]}', 
     70.0, 85.5, 45, true),
    
    ('proper_ppe_detection', 
     '{"required_equipment": ["helmet", "vest", "boots"], "detection_patterns": ["high_visibility", "safety_gear"], "confidence_boost": 8}', 
     65.0, 88.2, 67, true),
    
    ('no_violation_patterns', 
     '{"safe_indicators": ["proper_equipment", "safe_distance", "compliance"], "confidence_boost": 10, "validation_checks": ["equipment_check", "positioning"]}', 
     75.0, 92.1, 123, true),
    
    ('environmental_context', 
     '{"context_indicators": ["construction_site", "industrial_area", "warehouse"], "adjustment_factor": 0.15}', 
     60.0, 78.9, 89, true);

-- Insert sample batch jobs for demonstration
INSERT INTO batch_jobs (job_name, status, total_images, processed_images, successful_images, failed_images, started_at, completed_at, metadata) VALUES
    ('Demo Batch - Construction Site Analysis', 'completed', 50, 50, 47, 3, 
     CURRENT_TIMESTAMP - INTERVAL '2 hours', CURRENT_TIMESTAMP - INTERVAL '1 hour 30 minutes',
     '{"dataset": "construction_demo", "accuracy": 94.0, "avg_processing_time": 2.3}'),
    
    ('Sample PPE Compliance Check', 'completed', 25, 25, 24, 1,
     CURRENT_TIMESTAMP - INTERVAL '4 hours', CURRENT_TIMESTAMP - INTERVAL '3 hours 45 minutes',
     '{"dataset": "ppe_demo", "accuracy": 96.0, "avg_processing_time": 1.8}'),
    
    ('Industrial Safety Audit', 'in_progress', 100, 67, 62, 5,
     CURRENT_TIMESTAMP - INTERVAL '30 minutes', NULL,
     '{"dataset": "industrial_demo", "expected_completion": "2024-01-15T16:00:00Z"}');

-- Insert sample performance metrics
INSERT INTO performance_metrics (metric_name, metric_value, metric_type, timestamp, metadata) VALUES
    ('api_requests_total', 1547.0, 'counter', CURRENT_TIMESTAMP - INTERVAL '1 hour', '{"endpoint": "/api/v1/batch"}'),
    ('api_response_time_avg', 0.245, 'gauge', CURRENT_TIMESTAMP - INTERVAL '1 hour', '{"unit": "seconds"}'),
    ('vlm_api_success_rate', 0.982, 'gauge', CURRENT_TIMESTAMP - INTERVAL '1 hour', '{"unit": "percentage"}'),
    ('batch_processing_throughput', 23.5, 'gauge', CURRENT_TIMESTAMP - INTERVAL '1 hour', '{"unit": "images_per_minute"}'),
    ('database_connections_active', 8.0, 'gauge', CURRENT_TIMESTAMP - INTERVAL '1 hour', '{"max_connections": 20}'),
    ('memory_usage_percent', 67.2, 'gauge', CURRENT_TIMESTAMP - INTERVAL '1 hour', '{"component": "backend"}'),
    ('disk_usage_percent', 23.8, 'gauge', CURRENT_TIMESTAMP - INTERVAL '1 hour', '{"mount": "/app/data"}');

-- Insert additional demo processing results
DO $$
DECLARE
    session_uuid UUID;
    i INTEGER;
    categories TEXT[] := ARRAY['structure_misid', 'proper_ppe', 'no_violation', 'safety_hazard', 'equipment_issue'];
    statuses TEXT[] := ARRAY['completed', 'completed', 'completed', 'failed', 'completed'];
BEGIN
    -- Get the first demo session ID
    SELECT id INTO session_uuid FROM demo_sessions WHERE session_name = 'Development Test Session' LIMIT 1;
    
    -- Insert sample processing results
    FOR i IN 1..20 LOOP
        INSERT INTO demo_processing_results (
            session_id, 
            image_path, 
            original_category, 
            predicted_category, 
            confidence_score, 
            processing_time_ms, 
            vlm_response, 
            status
        ) VALUES (
            session_uuid,
            '/demo/images/sample_' || i || '.jpg',
            categories[1 + (i % array_length(categories, 1))],
            categories[1 + ((i + 1) % array_length(categories, 1))],
            45.0 + (random() * 50.0)::DECIMAL(5,2),
            (1500 + (random() * 3000))::INTEGER,
            jsonb_build_object(
                'analysis', 'Sample VLM analysis for image ' || i,
                'confidence', 45.0 + (random() * 50.0),
                'detected_objects', jsonb_build_array('person', 'equipment', 'structure'),
                'safety_assessment', 'Automated safety assessment result',
                'processing_time', (1500 + (random() * 3000))::INTEGER
            ),
            statuses[1 + (i % array_length(statuses, 1))]
        );
    END LOOP;
END $$;

-- Update confidence calibration with more realistic data
UPDATE confidence_calibration SET 
    actual_accuracy = CASE category
        WHEN 'structure_misid' THEN 87.3
        WHEN 'proper_ppe' THEN 91.2
        WHEN 'no_violation' THEN 94.8
        WHEN 'default' THEN 89.1
        ELSE actual_accuracy
    END,
    sample_count = CASE category
        WHEN 'structure_misid' THEN 245
        WHEN 'proper_ppe' THEN 189
        WHEN 'no_violation' THEN 312
        WHEN 'default' THEN 156
        ELSE sample_count
    END,
    updated_at = CURRENT_TIMESTAMP;

-- Insert additional confidence calibration points
INSERT INTO confidence_calibration (category, predicted_confidence, actual_accuracy, sample_count) VALUES
    ('structure_misid', 60.0, 78.2, 67),
    ('structure_misid', 80.0, 91.5, 134),
    ('structure_misid', 90.0, 96.8, 89),
    ('proper_ppe', 55.0, 72.4, 78),
    ('proper_ppe', 75.0, 87.9, 156),
    ('proper_ppe', 85.0, 94.2, 123),
    ('no_violation', 65.0, 82.1, 98),
    ('no_violation', 85.0, 97.3, 167),
    ('no_violation', 95.0, 99.1, 78)
ON CONFLICT (category, predicted_confidence) DO UPDATE SET
    actual_accuracy = EXCLUDED.actual_accuracy,
    sample_count = EXCLUDED.sample_count,
    updated_at = CURRENT_TIMESTAMP;