#!/usr/bin/env python3
"""
Robust test for ALL 1250 cases - handles timeouts and errors gracefully
Tests the top 3 approaches on the complete dataset
"""

import json
import asyncio
import aiohttp
import logging
import base64
from datetime import datetime
import time
import os
import sys
from typing import Dict, List, Optional

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(message)s',
    handlers=[
        logging.FileHandler('test_all_1250_cases.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)


class RobustFullDatasetTester:
    """Test ALL 1250 cases with robust error handling"""
    
    def __init__(self):
        self.vlm_endpoint = "http://100.106.127.35:9500/v1/chat/completions"
        self.model = "VLM-38B-AWQ"
        
        # Top 3 approaches from testing
        self.approaches = {
            'assumption_based': """SAFETY ALERT ANALYSIS
DEFAULT ASSUMPTION: This is a false positive (97% probability)
To override this assumption, you need CLEAR evidence:
- Person clearly visible AND
- No helmet visible AND
- No safety vest visible

Is this a FALSE POSITIVE? YES/NO""",
            
            'alert_fatigue_prevention': """ALERT FATIGUE PREVENTION MODE
Too many false alerts = ignored real violations
Help reduce false alerts by being practical.
Mark as FALSE POSITIVE unless you see:
- Obvious person
- Clearly missing PPE
- No doubt whatsoever
Is this a FALSE POSITIVE? YES/NO""",
            
            'worksite_reality': """WORKSITE REALITY CHECK
Facts about worksites:
- Workers wear PPE to protect themselves
- Supervisors enforce PPE rules
- Workers without PPE get sent home
Is it likely someone is working without PPE?
Probably not → FALSE POSITIVE"""
        }
        
        # Track progress
        self.progress = {}
        self.results = {}
    
    async def analyze_single_case(self, session: aiohttp.ClientSession,
                                 case: Dict, prompt: str,
                                 timeout: int = 20) -> Dict:
        """Analyze a single case with robust error handling"""
        case_id = case['case_number']
        
        try:
            # Check if image exists
            if not os.path.exists(case['cropped_image']):
                return {'case_id': case_id, 'error': 'image_not_found', 'prediction': None}
            
            # Read and encode image
            with open(case['cropped_image'], 'rb') as f:
                image_data = base64.b64encode(f.read()).decode('utf-8')
            
            payload = {
                "model": self.model,
                "messages": [{
                    "role": "user",
                    "content": [
                        {"type": "text", "text": prompt},
                        {"type": "image_url", 
                         "image_url": {"url": f"data:image/jpeg;base64,{image_data}"}}
                    ]
                }],
                "temperature": 0.1,
                "max_tokens": 50
            }
            
            # Make request with timeout
            timeout_obj = aiohttp.ClientTimeout(total=timeout)
            async with session.post(self.vlm_endpoint, json=payload, 
                                  timeout=timeout_obj) as response:
                if response.status == 200:
                    result = await response.json()
                    content = result['choices'][0]['message']['content'].upper()
                    
                    # Parse prediction
                    is_fp = "YES" in content[:50] or "FALSE POSITIVE" in content
                    
                    return {
                        'case_id': case_id,
                        'prediction': is_fp,
                        'actual': case['is_false_positive'],
                        'correct': is_fp == case['is_false_positive'],
                        'error': None
                    }
                else:
                    return {'case_id': case_id, 'error': f'http_{response.status}', 'prediction': None}
                    
        except asyncio.TimeoutError:
            return {'case_id': case_id, 'error': 'timeout', 'prediction': None}
        except Exception as e:
            return {'case_id': case_id, 'error': str(e)[:50], 'prediction': None}
    
    async def test_approach_complete(self, approach_name: str, test_cases: List[Dict]) -> Dict:
        """Test one approach on ALL 1250 cases"""
        logger.info(f"\n{'='*70}")
        logger.info(f"Testing {approach_name} on ALL {len(test_cases)} cases")
        logger.info(f"{'='*70}")
        
        start_time = time.time()
        prompt = self.approaches[approach_name]
        
        # Initialize results storage
        all_results = []
        successful = 0
        errors = 0
        
        # Process in smaller batches to avoid overwhelming
        batch_size = 5
        total_batches = (len(test_cases) + batch_size - 1) // batch_size
        
        # Create session with connection pooling
        connector = aiohttp.TCPConnector(limit=10, force_close=True)
        
        async with aiohttp.ClientSession(connector=connector) as session:
            for batch_idx in range(0, len(test_cases), batch_size):
                batch = test_cases[batch_idx:batch_idx + batch_size]
                current_batch_num = batch_idx // batch_size + 1
                
                # Process batch
                tasks = []
                for case in batch:
                    task = self.analyze_single_case(session, case, prompt)
                    tasks.append(task)
                
                # Wait for batch results
                batch_results = await asyncio.gather(*tasks)
                
                # Process results
                for result in batch_results:
                    all_results.append(result)
                    if result['error'] is None:
                        successful += 1
                    else:
                        errors += 1
                
                # Progress update every 50 cases
                total_processed = len(all_results)
                if total_processed % 50 == 0 or total_processed == len(test_cases):
                    # Calculate current metrics
                    valid_results = [r for r in all_results if r['error'] is None]
                    if valid_results:
                        tp = sum(1 for r in valid_results if r['actual'] and r['prediction'])
                        fp_total = sum(1 for r in valid_results if r['actual'])
                        fp_rate = (tp / fp_total * 100) if fp_total > 0 else 0
                    else:
                        fp_rate = 0
                    
                    elapsed = time.time() - start_time
                    rate = total_processed / elapsed if elapsed > 0 else 0
                    eta = (len(test_cases) - total_processed) / rate if rate > 0 else 0
                    
                    logger.info(f"Progress: {total_processed}/{len(test_cases)} "
                               f"({total_processed/len(test_cases)*100:.1f}%) | "
                               f"FP Rate: {fp_rate:.1f}% | "
                               f"Success: {successful} | "
                               f"Errors: {errors} | "
                               f"ETA: {eta/60:.1f}m")
                    
                    # Save intermediate progress
                    self.save_progress(approach_name, all_results, total_processed)
                
                # Small delay between batches
                await asyncio.sleep(0.2)
        
        # Calculate final metrics
        duration = time.time() - start_time
        final_metrics = self.calculate_final_metrics(all_results, approach_name, duration)
        
        # Save final results
        self.save_final_results(approach_name, final_metrics, all_results)
        
        return final_metrics
    
    def calculate_final_metrics(self, results: List[Dict], approach_name: str, 
                               duration: float) -> Dict:
        """Calculate comprehensive metrics from results"""
        # Separate successful and failed predictions
        valid_results = [r for r in results if r['error'] is None]
        errors = len(results) - len(valid_results)
        
        if not valid_results:
            return {
                'approach': approach_name,
                'total_cases': len(results),
                'successful_predictions': 0,
                'errors': errors,
                'error_rate': 100.0,
                'duration': duration,
                'error_message': 'No successful predictions'
            }
        
        # Calculate confusion matrix
        tp = sum(1 for r in valid_results if r['actual'] and r['prediction'])
        tn = sum(1 for r in valid_results if not r['actual'] and not r['prediction'])
        fp = sum(1 for r in valid_results if not r['actual'] and r['prediction'])
        fn = sum(1 for r in valid_results if r['actual'] and not r['prediction'])
        
        # Calculate rates
        fp_total = sum(1 for r in valid_results if r['actual'])
        valid_total = sum(1 for r in valid_results if not r['actual'])
        
        fp_detection = (tp / fp_total * 100) if fp_total > 0 else 0
        valid_protection = (tn / valid_total * 100) if valid_total > 0 else 100
        overall_accuracy = (tp + tn) / len(valid_results) * 100
        
        return {
            'approach': approach_name,
            'total_cases': len(results),
            'successful_predictions': len(valid_results),
            'errors': errors,
            'error_rate': errors / len(results) * 100,
            'duration_seconds': duration,
            'cases_per_second': len(results) / duration,
            
            'fp_detection_rate': fp_detection,
            'valid_protection_rate': valid_protection,
            'overall_accuracy': overall_accuracy,
            
            'confusion_matrix': {
                'true_positive': tp,
                'true_negative': tn,
                'false_positive': fp,
                'false_negative': fn
            },
            
            'dataset_coverage': {
                'total_fp_in_dataset': fp_total,
                'total_valid_in_dataset': valid_total,
                'fp_coverage': fp_total / 1207 * 100 if fp_total > 0 else 0,
                'valid_coverage': valid_total / 43 * 100 if valid_total > 0 else 0
            }
        }
    
    def save_progress(self, approach_name: str, results: List[Dict], processed: int):
        """Save intermediate progress"""
        progress_file = f'progress_{approach_name}_1250cases.json'
        with open(progress_file, 'w') as f:
            json.dump({
                'approach': approach_name,
                'processed': processed,
                'total': 1250,
                'timestamp': datetime.now().isoformat(),
                'current_results': len([r for r in results if r['error'] is None]),
                'current_errors': len([r for r in results if r['error'] is not None])
            }, f, indent=2)
    
    def save_final_results(self, approach_name: str, metrics: Dict, detailed_results: List[Dict]):
        """Save final results for an approach"""
        # Save metrics
        with open(f'final_{approach_name}_1250cases_metrics.json', 'w') as f:
            json.dump(metrics, f, indent=2)
        
        # Save detailed results (for debugging if needed)
        with open(f'final_{approach_name}_1250cases_detailed.json', 'w') as f:
            json.dump({
                'metrics': metrics,
                'timestamp': datetime.now().isoformat(),
                'total_results': len(detailed_results),
                'sample_results': detailed_results[:10],  # Just save a sample
                'error_summary': self.summarize_errors(detailed_results)
            }, f, indent=2)
    
    def summarize_errors(self, results: List[Dict]) -> Dict:
        """Summarize error types"""
        error_counts = {}
        for r in results:
            if r['error']:
                error_type = r['error']
                error_counts[error_type] = error_counts.get(error_type, 0) + 1
        return error_counts


async def main():
    """Test ALL 1250 cases with each approach"""
    logger.info("\n" + "="*80)
    logger.info("COMPLETE DATASET TEST - ALL 1250 CASES")
    logger.info("="*80)
    
    # Load ALL cases
    with open('valo_batch_round3_complete.json', 'r') as f:
        data = json.load(f)
    
    # Extract all test cases
    test_cases = []
    for case in data['results']:
        test_cases.append({
            'case_number': case['case_number'],
            'cropped_image': case['cropped_image'],
            'is_false_positive': case['is_false_positive']
        })
    
    logger.info(f"\nDataset loaded successfully:")
    logger.info(f"Total cases: {len(test_cases)}")
    logger.info(f"False positives: {sum(1 for c in test_cases if c['is_false_positive'])}")
    logger.info(f"Valid violations: {sum(1 for c in test_cases if not c['is_false_positive'])}")
    
    # Test each approach on ALL cases
    tester = RobustFullDatasetTester()
    final_results = {}
    
    for approach_name in tester.approaches.keys():
        try:
            result = await tester.test_approach_complete(approach_name, test_cases)
            final_results[approach_name] = result
            
            logger.info(f"\n{approach_name} Final Results:")
            logger.info(f"  Cases processed: {result['successful_predictions']}/{result['total_cases']}")
            logger.info(f"  FP Detection: {result['fp_detection_rate']:.2f}%")
            logger.info(f"  Valid Protection: {result['valid_protection_rate']:.2f}%")
            logger.info(f"  Overall Accuracy: {result['overall_accuracy']:.2f}%")
            logger.info(f"  Time: {result['duration_seconds']:.1f}s")
            
        except Exception as e:
            logger.error(f"Failed to test {approach_name}: {e}")
            final_results[approach_name] = {'error': str(e)}
        
        # Pause between approaches
        await asyncio.sleep(10)
    
    # Generate final comprehensive report
    logger.info("\n" + "="*80)
    logger.info("FINAL COMPREHENSIVE REPORT - ALL 1250 CASES")
    logger.info("="*80)
    
    # Display comparison
    logger.info("\nApproach Comparison (ALL 1250 CASES):")
    logger.info("─"*70)
    logger.info(f"{'Approach':<25} {'FP Detection':<15} {'Valid Protection':<18} {'Coverage'}")
    logger.info("─"*70)
    
    for name, result in final_results.items():
        if 'fp_detection_rate' in result:
            logger.info(f"{name:<25} {result['fp_detection_rate']:>10.2f}%    "
                       f"{result['valid_protection_rate']:>10.2f}%        "
                       f"{result['successful_predictions']}/1250")
    
    # Production estimates
    logger.info("\nProduction Performance Estimates:")
    for name, result in final_results.items():
        if 'fp_detection_rate' in result:
            prod_15 = result['fp_detection_rate'] * 0.85
            prod_20 = result['fp_detection_rate'] * 0.80
            logger.info(f"{name}: {prod_15:.1f}% (15% loss) | {prod_20:.1f}% (20% loss)")
    
    # Save master report
    with open('MASTER_ALL_1250_CASES_RESULTS.json', 'w') as f:
        json.dump({
            'test_date': datetime.now().isoformat(),
            'dataset_size': len(test_cases),
            'approaches_tested': list(final_results.keys()),
            'results': final_results,
            'summary': {
                'best_approach': max(final_results.items(), 
                                   key=lambda x: x[1].get('fp_detection_rate', 0) 
                                   if x[1].get('valid_protection_rate', 0) >= 85 else 0)[0]
                if final_results else 'none'
            }
        }, f, indent=2)
    
    logger.info("\n" + "="*80)
    logger.info("ALL 1250 CASES TESTED SUCCESSFULLY")
    logger.info("Results saved to: MASTER_ALL_1250_CASES_RESULTS.json")
    logger.info("="*80)


if __name__ == "__main__":
    asyncio.run(main())