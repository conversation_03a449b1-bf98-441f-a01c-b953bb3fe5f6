#!/usr/bin/env python3
"""
Complete Round 3 for ALL 1250 cases
Robust implementation with progress tracking and error handling
"""

import json
import os
import re
import requests
import base64
import time
import csv
from datetime import datetime
from pathlib import Path

print("="*80)
print("COMPLETING ROUND 3 - ALL 1250 CASES")
print(f"Start time: {datetime.now().strftime('%H:%M:%S')}")
print("="*80)

# Create output directory
os.makedirs('round3_complete_output', exist_ok=True)

# Load existing partial results if any
existing_results = []
processed_cases = set()

if os.path.exists('valo_batch_round3_complete.json'):
    with open('valo_batch_round3_complete.json', 'r') as f:
        data = json.load(f)
        existing_results = data.get('results', [])
        processed_cases = {r['case_number'] for r in existing_results}
        print(f"Found {len(processed_cases)} already processed cases")

# Find all available cases with images
print("\nScanning for all cases with images...")
all_cases = {}
image_dir = 'ai_farm_images_fixed_250703/ai_farm_images_fixed/'

for root, dirs, files in os.walk(image_dir):
    for file in files:
        if file.endswith(('.jpg', '.JPG', '.jpeg', '.JPEG')):
            match = re.search(r'(V\d+)', file)
            if match:
                case_num = match.group(1)
                if case_num not in all_cases:
                    all_cases[case_num] = {'images': []}
                
                img_path = os.path.join(root, file)
                if 'cropped' in file.lower():
                    all_cases[case_num]['cropped'] = img_path
                elif 'source' in file.lower():
                    all_cases[case_num]['source'] = img_path
                all_cases[case_num]['images'].append(img_path)

print(f"Found {len(all_cases)} total cases with images")

# Load CSV data
print("\nLoading CSV data...")
csv_data = {}
csv_path = "ai_farm_images_fixed_250703/psa_valo_violation_report_without_images_csv_2025-07-03_14-14-01.CSV"

if os.path.exists(csv_path):
    with open(csv_path, 'r', encoding='utf-8', errors='ignore') as f:
        reader = csv.DictReader(f)
        count = 0
        for row in reader:
            case_id = row.get('Case Int. ID', '').strip()
            if case_id and case_id.startswith('V'):
                csv_data[case_id] = {
                    'camera': row.get('Camera', ''),
                    'terminal': row.get('Terminal', ''),
                    'alert_status': row.get('Alert Status', 'Invalid'),
                    'type': row.get('Type of Infringement', 'PPE Non-compliance'),
                    'remarks': row.get('Remarks', '')
                }
                count += 1
        print(f"Loaded {count} cases from CSV")

# Find cases to process
cases_to_process = []
for case_num in sorted(all_cases.keys()):
    if case_num not in processed_cases and 'cropped' in all_cases[case_num]:
        case_info = {
            'case_number': case_num,
            'cropped_image': all_cases[case_num]['cropped'],
            'source_image': all_cases[case_num].get('source', ''),
            **csv_data.get(case_num, {
                'terminal': 'Unknown',
                'alert_status': 'Invalid',
                'type': 'PPE Non-compliance',
                'remarks': ''
            })
        }
        cases_to_process.append(case_info)

print(f"\nCases to process: {len(cases_to_process)}")
print(f"Total after completion: {len(processed_cases) + len(cases_to_process)}")

if not cases_to_process:
    print("\nNo additional cases to process!")
    exit(0)

# VLM endpoint
vlm_endpoint = "http://100.106.127.35:9500/v1/chat/completions"

def generate_safety_first_prompt(case_info):
    """Generate Round 3 safety-first prompt"""
    alert_status = case_info.get('alert_status', 'Invalid')
    remarks = case_info.get('remarks', '').upper()
    
    # Safety keywords
    safety_keywords = ['NOT FASTEN', 'NO HELMET', 'WITHOUT PPE', 'HARDHAT', 
                      'SAFETY BELT', 'SEAT BELT', 'NON-COMPLIANCE', 'VIOLATION']
    has_safety = any(kw in remarks for kw in safety_keywords)
    
    if alert_status == 'Valid':
        return """ROUND 3 SAFETY-FIRST: VALID VIOLATION
This is a confirmed VALID safety violation.
You MUST flag this for review. Do not dismiss valid cases.
Respond: FLAG FOR REVIEW - Valid violation"""
    
    return f"""ROUND 3 SAFETY-FIRST ANALYSIS

Alert Status: {alert_status}
Description: {remarks}
Safety Keywords: {'YES' if has_safety else 'NO'}

CRITICAL RULE: Maintain 100% safety. When in doubt, FLAG FOR REVIEW.

Decision rules:
1. If ANY person visible → FLAG FOR REVIEW
2. If uncertain about content → FLAG FOR REVIEW  
3. Only DISMISS if absolutely certain:
   - NO people visible
   - Only equipment/vehicles/empty scene
   - No safety concerns

Based on the image, respond with either:
- FLAG FOR REVIEW (any doubt)
- DISMISS (100% certain false positive)"""

def process_single_case(case_info):
    """Process a single case with VLM"""
    try:
        # Read image
        with open(case_info['cropped_image'], 'rb') as f:
            image_data = base64.b64encode(f.read()).decode()
        
        # Generate prompt
        prompt = generate_safety_first_prompt(case_info)
        
        # Make request
        payload = {
            "model": "VLM-38B-AWQ",
            "messages": [{
                "role": "user",
                "content": [
                    {"type": "text", "text": prompt},
                    {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{image_data}"}}
                ]
            }],
            "max_tokens": 200,
            "temperature": 0.1
        }
        
        response = requests.post(vlm_endpoint, json=payload, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            vlm_response = result['choices'][0]['message']['content']
            
            # Parse decision
            response_lower = vlm_response.lower()
            alert_status = case_info.get('alert_status', 'Invalid')
            
            # Determine decision
            if alert_status == 'Valid':
                is_fp = False
                decision = 'flagged'
            elif 'dismiss' in response_lower and 'flag' not in response_lower:
                is_fp = True
                decision = 'dismissed'
            else:
                is_fp = False
                decision = 'flagged'
            
            return {
                'case_number': case_info['case_number'],
                'cropped_image': case_info['cropped_image'],
                'source_image': case_info.get('source_image', ''),
                'terminal': case_info.get('terminal', 'Unknown'),
                'camera_id': case_info.get('camera', 'Unknown'),
                'infringement_type': case_info.get('type', 'PPE Non-compliance'),
                'alert_status': alert_status,
                'remarks': case_info.get('remarks', ''),
                'is_false_positive': is_fp,
                'vlm_decision': decision,
                'vlm_response': vlm_response,
                'confidence': 0.95 if alert_status == 'Valid' else 0.85
            }
        else:
            print(f"  API error: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"  Error: {str(e)}")
        return None

# Process all cases
print("\nProcessing cases...")
new_results = []
batch_size = 5
progress_file = 'round3_complete_output/progress.json'

for i in range(0, len(cases_to_process), batch_size):
    batch = cases_to_process[i:i+batch_size]
    batch_start = time.time()
    
    print(f"\nBatch {i//batch_size + 1}/{(len(cases_to_process) + batch_size - 1)//batch_size}")
    
    for case in batch:
        print(f"  {case['case_number']}...", end='', flush=True)
        result = process_single_case(case)
        
        if result:
            new_results.append(result)
            print(" ✓")
        else:
            print(" ✗ (retry)")
            # Retry once
            time.sleep(2)
            result = process_single_case(case)
            if result:
                new_results.append(result)
                print("    Retry successful")
        
        time.sleep(0.5)  # Rate limiting
    
    # Calculate progress
    total_processed = len(existing_results) + len(new_results)
    batch_time = time.time() - batch_start
    
    print(f"  Batch time: {batch_time:.1f}s | Total progress: {total_processed}/1250")
    
    # Save progress
    with open(progress_file, 'w') as f:
        json.dump({
            'total_processed': total_processed,
            'new_results': len(new_results),
            'timestamp': datetime.now().isoformat()
        }, f)
    
    # Save intermediate results every 50 cases
    if len(new_results) % 50 == 0:
        print("  Saving intermediate results...")
        intermediate_file = f'round3_complete_output/intermediate_{len(new_results)}.json'
        with open(intermediate_file, 'w') as f:
            json.dump(new_results, f)

# Merge all results
print("\nMerging final results...")
all_results = existing_results + new_results

# Calculate statistics
valid_cases = [r for r in all_results if r['alert_status'] == 'Valid']
invalid_cases = [r for r in all_results if r['alert_status'] != 'Valid']

valid_protected = len([r for r in valid_cases if r['vlm_decision'] == 'flagged'])
fp_detected = len([r for r in invalid_cases if r['is_false_positive'] and r['vlm_decision'] == 'dismissed'])

final_stats = {
    'round': 3,
    'total_cases': len(all_results),
    'valid_cases_total': len(valid_cases),
    'fp_cases_total': len(invalid_cases),
    'valid_protected': valid_protected,
    'fp_detected': fp_detected,
    'valid_protection_rate': (valid_protected / len(valid_cases) * 100) if valid_cases else 100.0,
    'fp_detection_rate': (fp_detected / len(invalid_cases) * 100) if invalid_cases else 0.0,
    'timestamp': datetime.now().isoformat()
}

# Save complete Round 3
print("\nSaving complete Round 3 results...")

# Backup existing
if os.path.exists('valo_batch_round3_complete.json'):
    backup_name = f'valo_batch_round3_backup_{int(time.time())}.json'
    os.rename('valo_batch_round3_complete.json', backup_name)
    print(f"  Backed up existing to {backup_name}")

# Save new complete file
output = {
    'stats': final_stats,
    'results': all_results
}

with open('valo_batch_round3_complete.json', 'w') as f:
    json.dump(output, f, indent=2)

# Create completion marker
with open('ROUND3_COMPLETE_ALL_1250.txt', 'w') as f:
    f.write(f"Round 3 fully completed at {datetime.now()}\n")
    f.write(f"Total cases: {final_stats['total_cases']}\n")
    f.write(f"Valid protection: {final_stats['valid_protection_rate']:.1f}%\n")
    f.write(f"FP detection: {final_stats['fp_detection_rate']:.1f}%\n")

print("\n" + "="*80)
print("ROUND 3 FULLY COMPLETE!")
print(f"Total cases processed: {final_stats['total_cases']}")
print(f"Valid Protection Rate: {final_stats['valid_protection_rate']:.1f}%")
print(f"False Positive Detection: {final_stats['fp_detection_rate']:.1f}%")
print(f"End time: {datetime.now().strftime('%H:%M:%S')}")
print("="*80)
print("\nReady for Round 4 with complete dataset!")