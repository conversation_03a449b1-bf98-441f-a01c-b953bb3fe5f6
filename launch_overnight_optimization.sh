#!/bin/bash
#
# VALO AI-FARM OVERNIGHT OPTIMIZATION LAUNCHER
# Runs 25 rounds of optimization to achieve 90%+ FP reduction
#

echo "=============================================="
echo "VALO AI-FARM OVERNIGHT OPTIMIZATION"
echo "Starting at: $(date)"
echo "=============================================="

# Make all scripts executable
chmod +x *.py
chmod +x *.sh

# Clear previous status files
echo "Clearing previous run data..."
rm -f overnight_status.txt
rm -f overnight_dashboard.json
rm -f health_status.json
rm -f health_alerts.log

# Initialize status
echo "$(date -Iseconds) - Overnight optimization initialized" > overnight_status.txt

# Check Round 5 completion
if [ -f "valo_round5_context_complete.json" ]; then
    echo "✅ Round 5 complete, ready to proceed"
else
    echo "❌ Round 5 not complete! Please wait for Round 5 to finish."
    exit 1
fi

# Launch health monitor in background
echo "Starting health monitor..."
nohup python3 health_monitor.py > health_monitor.log 2>&1 &
HEALTH_PID=$!
echo "Health monitor PID: $HEALTH_PID"

# Brief pause
sleep 2

# Launch Round 6 first (if not already complete)
if [ ! -f "valo_round6_ppe_complete.json" ]; then
    echo "Starting Round 6: Full PPE Intelligence..."
    nohup python3 round6_full_ppe_intelligence.py > round6.log 2>&1 &
    ROUND6_PID=$!
    echo "Round 6 PID: $ROUND6_PID"
    
    # Wait for Round 6 to complete or timeout
    ROUND6_TIMEOUT=3600  # 1 hour
    ROUND6_ELAPSED=0
    
    while [ ! -f "valo_round6_ppe_complete.json" ] && [ $ROUND6_ELAPSED -lt $ROUND6_TIMEOUT ]; do
        sleep 30
        ROUND6_ELAPSED=$((ROUND6_ELAPSED + 30))
        echo "Waiting for Round 6... ($ROUND6_ELAPSED seconds elapsed)"
    done
fi

# Launch the main orchestrator for rounds 7-25
echo "Starting main orchestrator for rounds 7-25..."
nohup python3 overnight_orchestrator_25rounds.py > overnight_orchestrator.log 2>&1 &
ORCHESTRATOR_PID=$!
echo "Orchestrator PID: $ORCHESTRATOR_PID"

# Brief pause
sleep 5

# Launch the monitoring dashboard
echo ""
echo "=============================================="
echo "OVERNIGHT OPTIMIZATION STARTED"
echo "=============================================="
echo ""
echo "Monitor progress with:"
echo "  python3 overnight_live_dashboard.py"
echo ""
echo "Check logs:"
echo "  tail -f overnight_orchestrator.log"
echo "  tail -f overnight_status.txt"
echo ""
echo "Process PIDs:"
echo "  Orchestrator: $ORCHESTRATOR_PID"
echo "  Health Monitor: $HEALTH_PID"
echo ""
echo "The system will run autonomously overnight."
echo "Expected completion: ~8 hours"
echo "=============================================="

# Create a process info file
cat > overnight_processes.txt << EOF
Overnight Optimization Processes
Started: $(date)
Orchestrator PID: $ORCHESTRATOR_PID
Health Monitor PID: $HEALTH_PID
EOF

# Optional: Launch dashboard automatically
read -p "Launch monitoring dashboard now? (y/n) " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    python3 overnight_live_dashboard.py
fi