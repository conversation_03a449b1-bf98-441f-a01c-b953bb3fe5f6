#!/usr/bin/env python3
"""
Continue Round 3 processing with balanced approach
Resumes from case 710 where we left off
"""

import json
import asyncio
import logging
from datetime import datetime
import sys

sys.path.append('/home/<USER>/VALO_AI-FARM_2025')
from valo_intelligent_learning import IntelligentLearningProcessor

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def continue_round3():
    """Continue Round 3 processing from where we left off"""
    
    processor = IntelligentLearningProcessor()
    processor.all_results = []  # Initialize as empty list
    
    # Load all cases
    all_cases = processor.load_all_cases()
    logger.info(f"Total cases available: {len(all_cases)}")
    
    # Load previous round 3 progress
    try:
        with open('valo_intelligent_round3_progress.json', 'r') as f:
            progress = json.load(f)
        start_index = progress['cases_processed']
        logger.info(f"Resuming from case {start_index}")
    except:
        start_index = 0
        logger.info("Starting fresh Round 3")
    
    # Load previous results to maintain statistics
    try:
        with open('valo_batch_round3_intermediate.json', 'r') as f:
            previous_data = json.load(f)
            # Handle both list and dict formats
            if isinstance(previous_data, list):
                processor.all_results = previous_data
            elif isinstance(previous_data, dict) and 'results' in previous_data:
                processor.all_results = previous_data['results']
            else:
                processor.all_results = []
        logger.info(f"Loaded {len(processor.all_results)} previous results")
    except:
        processor.all_results = []
    
    # Get remaining cases
    remaining_cases = all_cases[start_index:]
    logger.info(f"Processing {len(remaining_cases)} remaining cases")
    
    # Round 3 prompt - Balanced approach with safety first
    round3_prompt = """CRITICAL SAFETY SYSTEM - FALSE POSITIVE ANALYSIS
    
This is a safety-critical system. Our #1 priority is to NEVER filter out a valid safety violation.
Only dismiss cases when you are VERY confident (>90%) it's a false positive.

UPDATED ANALYSIS RULES:
1. Person Detection is KEY - ALL safety violations require a person
2. If you see ANY person in the cropped area, it's likely VALID
3. Equipment-only images are FALSE POSITIVES
4. When uncertain, always FLAG_FOR_REVIEW

Analyze this safety alert image:
- PERSON_DETECTED: [yes/no/possibly] - Any human visible in cropped area?
- EQUIPMENT_ONLY: [yes/no] - Only machinery/equipment visible?
- SAFETY_DECISION: [FLAG_FOR_REVIEW/DISMISS_WITH_CAUTION]
- CONFIDENCE: [0-100] - How confident in false positive assessment?
- REASONING: Brief explanation

CRITICAL: Only use DISMISS_WITH_CAUTION when:
- NO person visible (confidence >90%)
- ONLY equipment/machinery in frame
- Clear misidentification"""

    # Process in chunks
    chunk_size = 10
    delay_between_chunks = 1.0
    
    for chunk_idx in range(0, len(remaining_cases), chunk_size):
        chunk = remaining_cases[chunk_idx:chunk_idx + chunk_size]
        chunk_start_time = datetime.now()
        
        # Process chunk
        tasks = []
        for case in chunk:
            task = processor.analyze_with_vlm(
                case['cropped_image'],
                round3_prompt,
                case
            )
            tasks.append(task)
        
        # Get results
        chunk_results = await asyncio.gather(*tasks)
        
        # Add to results with round info and analysis
        for case, vlm_result in zip(chunk, chunk_results):
            full_result = {**case, **vlm_result, 'round': 3}
            
            # Calculate accuracy
            if case['is_false_positive']:
                full_result['correct_prediction'] = vlm_result['is_false_positive_predicted']
            else:
                full_result['correct_prediction'] = not vlm_result['is_false_positive_predicted']
                full_result['valid_case_protected'] = not vlm_result['is_false_positive_predicted']
            
            processor.all_results.append(full_result)
        
        # Calculate current stats
        total_processed = start_index + chunk_idx + len(chunk)
        valid_cases = [r for r in processor.all_results if not r['is_false_positive']]
        fp_cases = [r for r in processor.all_results if r['is_false_positive']]
        
        valid_protected = sum(1 for r in valid_cases if r.get('valid_case_protected', True))
        fp_detected = sum(1 for r in fp_cases if r.get('is_false_positive_predicted', False))
        
        valid_protection_rate = (valid_protected / len(valid_cases) * 100) if valid_cases else 100
        fp_detection_rate = (fp_detected / len(fp_cases) * 100) if fp_cases else 0
        
        # Save progress
        progress = {
            'round': 3,
            'cases_processed': total_processed,
            'timestamp': datetime.now().isoformat(),
            'valid_protection_rate': valid_protection_rate,
            'fp_detection_rate': fp_detection_rate,
            'remaining_cases': len(all_cases) - total_processed
        }
        
        with open('valo_intelligent_round3_progress.json', 'w') as f:
            json.dump(progress, f, indent=2)
        
        # Save intermediate results
        with open('valo_batch_round3_intermediate.json', 'w') as f:
            json.dump(processor.all_results, f, indent=2)
        
        # Log progress
        chunk_time = (datetime.now() - chunk_start_time).total_seconds()
        logger.info(f"Chunk {chunk_idx//chunk_size + 1}: Processed {len(chunk)} cases in {chunk_time:.1f}s")
        logger.info(f"Total: {total_processed}/{len(all_cases)} | Valid Protection: {valid_protection_rate:.1f}% | FP Detection: {fp_detection_rate:.1f}%")
        
        # Delay between chunks
        if chunk_idx + chunk_size < len(remaining_cases):
            await asyncio.sleep(delay_between_chunks)
    
    # Final summary
    logger.info("\n" + "="*60)
    logger.info("ROUND 3 COMPLETE!")
    logger.info(f"Total cases processed: {len(processor.all_results)}")
    logger.info(f"Valid protection rate: {valid_protection_rate:.1f}%")
    logger.info(f"False positive detection rate: {fp_detection_rate:.1f}%")
    logger.info("="*60)
    
    # Save final results
    final_stats = {
        'round': 3,
        'total_cases': len(processor.all_results),
        'valid_cases_total': len(valid_cases),
        'fp_cases_total': len(fp_cases),
        'valid_protected': valid_protected,
        'fp_detected': fp_detected,
        'valid_protection_rate': valid_protection_rate,
        'fp_detection_rate': fp_detection_rate,
        'timestamp': datetime.now().isoformat()
    }
    
    with open('valo_batch_round3_complete.json', 'w') as f:
        json.dump({
            'stats': final_stats,
            'results': processor.all_results
        }, f, indent=2)
    
    return final_stats


if __name__ == "__main__":
    asyncio.run(continue_round3())