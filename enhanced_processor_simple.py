#!/usr/bin/env python3
"""
Enhanced VALO Processor - Simple Terminal Version
Processes all 1250+ cases with improved false positive detection
"""

import json
import base64
import requests
import pandas as pd
import os
import time
from datetime import datetime
import re
import sys

class SimpleEnhancedProcessor:
    def __init__(self):
        self.vlm_url = "http://100.106.127.35:9500/v1/chat/completions"
        self.vlm_model = "VLM-38B-AWQ"
        
        # Load CSV data
        self.csv_path = 'ai_farm_images_fixed_250703/psa_valo_violation_report_without_images_csv_2025-07-03_14-14-01.CSV'
        self.df = pd.read_csv(self.csv_path)
        
        # Create case lookup
        self.case_lookup = {}
        for idx, row in self.df.iterrows():
            case_num = row['Case Int. ID']
            self.case_lookup[case_num] = {
                'infringement_type': row['Type of Infringement'],
                'alert_status': row['Alert Status'],
                'remarks': row['Remarks']
            }
        
        # Results storage
        self.results = []
        self.stats = {
            'total': 0,
            'processed': 0,
            'fp_detected': 0,
            'tp_protected': 0,
            'errors': 0,
            'fp_rate': 0,
            'protection_rate': 0
        }
    
    def encode_image(self, image_path):
        """Encode image to base64"""
        try:
            if os.path.exists(image_path):
                with open(image_path, 'rb') as f:
                    return base64.b64encode(f.read()).decode('utf-8')
        except Exception as e:
            print(f"Error encoding {image_path}: {str(e)}")
        return None
    
    def create_enhanced_prompt(self, infringement_type):
        """Create the enhanced prompt"""
        return f"""Analyze this safety alert image in extreme detail. The reported violation type is: {infringement_type}

1. MAIN SUBJECT IDENTIFICATION
   - Primary subject type: Person/Vessel Structure/Crane Structure/Spreader Structure/PM Structure/Others/Equipment
   - If Person: Number of individuals, gender if visible, apparent role
   - If Vessel Structure: Ship deck, cargo holds, vessel railings, bulkheads, hatch covers
   - If Crane Structure: Ship-to-shore cranes, gantry cranes, mobile cranes, crane booms, jib arms, crane cabins
   - If Spreader Structure: Container spreaders, twist locks, spreader bars, lifting attachments, hydraulic components
   - If PM Structure: Prime mover equipment, terminal tractors, yard trucks, chassis handling equipment
   - If Others/Equipment: Winches, cable drums, wire ropes, slings, sacks/bags, bulk handling equipment, machinery, vehicles, tools

2. PERSON DETAILS (if any visible)
   - Exact position and posture
   - Clothing description (colors, type, condition)
   - Safety equipment worn:
     * Head protection (helmet/hard hat) - color, style, properly worn?
     * High-visibility clothing - color, reflective strips visible?

3. ACTIVITY ANALYSIS
   - What specific action is being performed?
   - Tools or equipment being used
   - Body mechanics and positioning

4. ENVIRONMENT AND CONTEXT
   - Exact location (vessel deck/quay/yard/height/confined space)
   - Surrounding equipment and structures
   - Weather/lighting conditions
   - Potential hazards in vicinity

5. SAFETY ASSESSMENT
   - Primary safety concern visible
   - Violation type if apparent
   - Severity estimation

Provide the most detailed, factual description possible.

---

After completing the above detailed description, now analyze the same image for accuracy and confidence assessment:

6. DESCRIPTION ACCURACY (0-100%):
   How accurately does the above description match what you see in the image?

7. SUBJECT CONFIRMATION:
   - Is there definitely a PERSON in this image? YES/NO
   - If NO, what is the main subject?
   - Confidence in subject identification: (0-100%)

8. PPE COMPLIANCE CHECK (if person present):
   - Helmet/Hard hat present? YES/NO/PARTIAL/NA
   - Helmet confidence: (0-100%)
   - High-visibility vest present? YES/NO/PARTIAL/NA
   - Vest confidence: (0-100%)
   - Overall PPE compliance: COMPLETE/INCOMPLETE/NONE/NA
   - Overall PPE confidence: (0-100%)

9. SAFETY VIOLATION ASSESSMENT:
   - Is there a genuine safety violation visible? YES/NO/UNCERTAIN
   - If YES, describe the specific violation
   - Violation severity: MINOR/MODERATE/SEVERE/NA
   - Violation confidence: (0-100%)

10. FALSE POSITIVE DETECTION:
    Apply these core rules for "{infringement_type}" violations:
    - If NO person detected → likely False Positive (60-80%)
    - If ANY structure/others/equipment detected → likely False Positive (60-80%)
    - If NO person detected + ANY structure/others/equipment detected → high False Positive (80-95%)
    - If person detected with helmet/hardhat + vest BUT flagged for "PPE Non-compliance" → False Positive (85-95%)
    - List any additional factors suggesting false positive
    - Final false positive likelihood: (0-100%)

Format your final assessment EXACTLY as:

DESCRIPTION_ACCURACY: [X]%
PERSON_PRESENT: [YES/NO]
MAIN_SUBJECT: [Person/Vessel Structure/Crane Structure/Spreader Structure/PM Structure/Others/Equipment]
SUBJECT_CONFIDENCE: [X]%
HELMET_STATUS: [YES/NO/PARTIAL/NA]
HELMET_CONFIDENCE: [X]%
VEST_STATUS: [YES/NO/PARTIAL/NA]
VEST_CONFIDENCE: [X]%
PPE_COMPLIANCE: [COMPLETE/INCOMPLETE/NONE/NA]
PPE_CONFIDENCE: [X]%
SAFETY_VIOLATION: [YES/NO/UNCERTAIN]
VIOLATION_DESCRIPTION: [Description or NONE]
VIOLATION_SEVERITY: [MINOR/MODERATE/SEVERE/NA]
VIOLATION_CONFIDENCE: [X]%
FALSE_POSITIVE_LIKELIHOOD: [X]%
FP_INDICATORS: [List or NONE]"""
    
    def parse_vlm_response(self, response):
        """Parse the structured VLM response"""
        parsed = {
            'person_present': False,
            'main_subject': 'Unknown',
            'subject_confidence': 0,
            'helmet_status': 'NA',
            'vest_status': 'NA',
            'ppe_compliance': 'NA',
            'safety_violation': 'UNCERTAIN',
            'fp_likelihood': 50,
            'fp_indicators': []
        }
        
        patterns = {
            'person_present': r'PERSON_PRESENT:\s*(YES|NO)',
            'main_subject': r'MAIN_SUBJECT:\s*([^\n]+)',
            'subject_confidence': r'SUBJECT_CONFIDENCE:\s*(\d+)%',
            'helmet_status': r'HELMET_STATUS:\s*(\w+)',
            'vest_status': r'VEST_STATUS:\s*(\w+)',
            'ppe_compliance': r'PPE_COMPLIANCE:\s*(\w+)',
            'safety_violation': r'SAFETY_VIOLATION:\s*(YES|NO|UNCERTAIN)',
            'fp_likelihood': r'FALSE_POSITIVE_LIKELIHOOD:\s*(\d+)%',
            'fp_indicators': r'FP_INDICATORS:\s*([^\n]+)'
        }
        
        for field, pattern in patterns.items():
            match = re.search(pattern, response, re.IGNORECASE)
            if match:
                value = match.group(1).strip()
                if field in ['subject_confidence', 'fp_likelihood']:
                    parsed[field] = int(value)
                elif field == 'person_present':
                    parsed[field] = value.upper() == 'YES'
                elif field == 'fp_indicators':
                    parsed[field] = [i.strip() for i in value.split(',') if i.strip() and i.strip() != 'NONE']
                else:
                    parsed[field] = value
        
        return parsed
    
    def display_progress(self):
        """Display progress bar in terminal"""
        if self.stats['total'] > 0:
            progress = self.stats['processed'] / self.stats['total']
            bar_length = 40
            filled = int(bar_length * progress)
            bar = '█' * filled + '░' * (bar_length - filled)
            
            sys.stdout.write(f"\r[{bar}] {self.stats['processed']}/{self.stats['total']} "
                           f"({progress*100:.1f}%) | "
                           f"FP: {self.stats['fp_rate']:.1f}% | "
                           f"Protected: {self.stats['protection_rate']:.1f}%")
            sys.stdout.flush()
    
    def process_case(self, case_data):
        """Process a single case"""
        case_number = case_data['case_number']
        
        # Get info from CSV
        csv_info = self.case_lookup.get(case_number, {})
        infringement_type = csv_info.get('infringement_type', 'Unknown')
        ground_truth = csv_info.get('alert_status', 'Unknown')
        
        # Encode images
        cropped_b64 = self.encode_image(case_data['cropped_image'])
        source_b64 = self.encode_image(case_data['source_image'])
        
        if not cropped_b64:
            self.stats['errors'] += 1
            return None
        
        # Create prompt
        prompt = self.create_enhanced_prompt(infringement_type)
        
        # Build message
        content = [
            {"type": "text", "text": prompt},
            {"type": "text", "text": "\nANALYZING CROPPED ALERT IMAGE:"},
            {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{cropped_b64}"}}
        ]
        
        if source_b64:
            content.extend([
                {"type": "text", "text": "\nFULL CONTEXT IMAGE FOR REFERENCE:"},
                {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{source_b64}"}}
            ])
        
        payload = {
            "model": self.vlm_model,
            "messages": [{"role": "user", "content": content}],
            "temperature": 0.1,
            "max_tokens": 600
        }
        
        try:
            response = requests.post(self.vlm_url, json=payload, timeout=60)
            
            if response.status_code == 200:
                vlm_response = response.json()['choices'][0]['message']['content']
                parsed = self.parse_vlm_response(vlm_response)
                
                result = {
                    'case_number': case_number,
                    'infringement_type': infringement_type,
                    'ground_truth': ground_truth,
                    'remarks': csv_info.get('remarks', ''),
                    **parsed
                }
                
                # Update stats
                self.stats['processed'] += 1
                
                if ground_truth == 'Invalid' and parsed['fp_likelihood'] >= 70:
                    self.stats['fp_detected'] += 1
                elif ground_truth == 'Valid' and parsed['fp_likelihood'] < 50:
                    self.stats['tp_protected'] += 1
                
                # Update rates
                fp_total = sum(1 for r in self.results if r['ground_truth'] == 'Invalid')
                if fp_total > 0:
                    self.stats['fp_rate'] = (self.stats['fp_detected'] / fp_total) * 100
                
                tp_total = sum(1 for r in self.results if r['ground_truth'] == 'Valid')
                if tp_total > 0:
                    self.stats['protection_rate'] = (self.stats['tp_protected'] / tp_total) * 100
                
                self.results.append(result)
                return result
                
        except Exception as e:
            print(f"\nError processing {case_number}: {str(e)}")
            self.stats['errors'] += 1
            return None
    
    def process_all(self, limit=None):
        """Process all cases with progress display"""
        # Load cases
        with open('valo_batch_round3_complete.json', 'r') as f:
            data = json.load(f)
            cases = data['results']
        
        if limit:
            cases = cases[:limit]
        
        self.stats['total'] = len(cases)
        
        print("="*80)
        print(f"🚀 ENHANCED VALO PROCESSOR - Processing {len(cases)} cases")
        print("="*80)
        print("Using enhanced prompt with structure detection and infringement types")
        print("-"*80)
        
        start_time = time.time()
        
        for i, case in enumerate(cases):
            result = self.process_case(case)
            
            # Display progress
            self.display_progress()
            
            # Show details every 10 cases
            if (i + 1) % 10 == 0:
                elapsed = time.time() - start_time
                rate = self.stats['processed'] / elapsed if elapsed > 0 else 0
                eta = (self.stats['total'] - self.stats['processed']) / rate / 60 if rate > 0 else 0
                
                print(f"\n\n📊 Progress Update at case {i+1}:")
                print(f"   Processing rate: {rate:.1f} cases/sec")
                print(f"   ETA: {eta:.1f} minutes")
                
                # Show recent interesting cases
                recent = self.results[-5:]
                for r in recent:
                    if 'Structure' in r['main_subject']:
                        print(f"   🏗️  {r['case_number']}: {r['main_subject']} (FP: {r['fp_likelihood']}%)")
                    elif r['ppe_compliance'] == 'COMPLETE' and r['ground_truth'] == 'Invalid':
                        print(f"   👷 {r['case_number']}: Person with COMPLETE PPE (FP: {r['fp_likelihood']}%)")
            
            # Rate limiting
            time.sleep(0.5)
        
        # Final summary
        print("\n\n" + "="*80)
        print("✅ PROCESSING COMPLETE!")
        print("="*80)
        
        # Calculate final statistics
        total_time = time.time() - start_time
        fp_cases = [r for r in self.results if r['ground_truth'] == 'Invalid']
        tp_cases = [r for r in self.results if r['ground_truth'] == 'Valid']
        
        print(f"\n📊 FINAL RESULTS:")
        print(f"   Total Processed: {self.stats['processed']}")
        print(f"   Total Time: {total_time/60:.1f} minutes")
        print(f"   Processing Speed: {self.stats['processed']/total_time:.1f} cases/sec")
        
        print(f"\n🎯 FALSE POSITIVE DETECTION:")
        print(f"   FP Cases: {len(fp_cases)}")
        print(f"   FP Detected: {self.stats['fp_detected']} ({self.stats['fp_rate']:.1f}%)")
        
        print(f"\n🛡️  VALID CASE PROTECTION:")
        print(f"   Valid Cases: {len(tp_cases)}")
        print(f"   Protected: {self.stats['tp_protected']} ({self.stats['protection_rate']:.1f}%)")
        
        # Structure analysis
        structures = [r for r in self.results if 'Structure' in r['main_subject']]
        print(f"\n🏗️  STRUCTURE DETECTIONS:")
        print(f"   Total: {len(structures)}")
        print(f"   In FPs: {sum(1 for r in structures if r['ground_truth'] == 'Invalid')}")
        
        # PPE analysis
        ppe_complete_fps = [r for r in fp_cases if r.get('ppe_compliance') == 'COMPLETE']
        print(f"\n👷 PPE ANALYSIS:")
        print(f"   FPs with Complete PPE: {len(ppe_complete_fps)}")
        
        # Save results
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_file = f"enhanced_results_{timestamp}.json"
        
        with open(output_file, 'w') as f:
            json.dump({
                'results': self.results,
                'statistics': self.stats,
                'processing_time': total_time,
                'timestamp': timestamp
            }, f, indent=2)
        
        print(f"\n💾 Results saved to: {output_file}")
        
        # Recommendations
        print("\n🎯 RECOMMENDATIONS:")
        if self.stats['fp_rate'] >= 70:
            print("   ✅ Achieved 70%+ FP detection target!")
        else:
            print(f"   ⚠️  FP detection at {self.stats['fp_rate']:.1f}% - needs tuning")
        
        if self.stats['protection_rate'] >= 98:
            print("   ✅ Achieved 98%+ valid case protection!")
        else:
            print(f"   ⚠️  Protection at {self.stats['protection_rate']:.1f}% - needs adjustment")

if __name__ == "__main__":
    # Check if running test or full
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == 'test':
        print("Running test with 20 cases...")
        processor = SimpleEnhancedProcessor()
        processor.process_all(limit=20)
    else:
        print("Run with 'test' argument to process only 20 cases")
        print("Example: python3 enhanced_processor_simple.py test")
        print("\nOr run full processing (will take several hours):")
        print("python3 enhanced_processor_simple.py full")
        
        if len(sys.argv) > 1 and sys.argv[1] == 'full':
            processor = SimpleEnhancedProcessor()
            processor.process_all()