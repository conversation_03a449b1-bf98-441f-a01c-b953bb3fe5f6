#!/usr/bin/env python3
"""
VALO 100% Valid Protection Explorer - Mini Version
Tests just 10 cases per strategy to get quick results
"""

import json
import base64
import requests
import os
from datetime import datetime
import time
import random

class MiniValidProtectionExplorer:
    def __init__(self):
        self.vlm_url = "http://100.106.127.35:9500/v1/chat/completions"
        self.vlm_model = "VLM-38B-AWQ"
        self.session = requests.Session()
        
        # Test configuration
        self.cases_per_strategy = 10  # Small sample for quick results
        
        # Exploration strategies (simplified)
        self.strategies = {
            'baseline': {
                'name': 'Baseline (Round 3 Final)',
                'thresholds': {
                    'structure': 91,
                    'person': 50,
                    'ppe_compliant': 75,
                    'behavioral': 55
                }
            },
            'ultra_conservative': {
                'name': 'Ultra-Conservative',
                'thresholds': {
                    'structure': 99,  # Nearly impossible
                    'person': 30,     # Very easy
                    'ppe_compliant': 90,
                    'behavioral': 40
                }
            },
            'maximum_safety': {
                'name': 'Maximum Safety Mode',
                'thresholds': {
                    'structure': 98,
                    'person': 35,
                    'ppe_compliant': 95,
                    'behavioral': 35
                }
            }
        }
        
        self.results = {}
        
    def encode_image(self, image_path):
        """Encode image to base64"""
        try:
            if os.path.exists(image_path):
                with open(image_path, 'rb') as f:
                    return base64.b64encode(f.read()).decode('utf-8')
        except:
            pass
        return None
    
    def create_prompt_with_thresholds(self, thresholds):
        """Create prompt with specific thresholds"""
        
        # Load base prompt
        with open('FINAL_PRODUCTION_PROMPT.txt', 'r') as f:
            base_prompt = f.read()
        
        # Update structure threshold
        prompt = base_prompt.replace(
            "A) INDUSTRIAL STRUCTURE (need >90% confidence)",
            f"A) INDUSTRIAL STRUCTURE (need >{thresholds['structure']}% confidence)"
        )
        
        # Add threshold information
        threshold_info = f"""

CURRENT CONFIDENCE THRESHOLDS:
- Structure identification: {thresholds['structure']}%
- Person detection: {thresholds['person']}%
- PPE compliance: {thresholds['ppe_compliant']}%
- Behavioral violations: {thresholds['behavioral']}%

Apply these thresholds strictly when making decisions."""
        
        # Insert before decision rules
        prompt = prompt.replace(
            "STEP 4: MAKE SAFETY DECISION",
            threshold_info + "\n\nSTEP 4: MAKE SAFETY DECISION"
        )
        
        # For maximum safety strategies
        if thresholds['structure'] >= 98:
            prompt += "\n\nMAXIMUM SAFETY MODE: Any uncertainty = VALID VIOLATION"
        
        return prompt
    
    def test_single_case(self, case, prompt, strategy_name):
        """Test a single case and show real-time result"""
        
        print(f"    Testing {case['case_number']}...", end='', flush=True)
        
        # Encode images
        source_b64 = self.encode_image(case['source_image'])
        cropped_b64 = self.encode_image(case['cropped_image'])
        
        if not source_b64 or not cropped_b64:
            print(" [SKIP]")
            return None
        
        payload = {
            "model": self.vlm_model,
            "messages": [{
                "role": "user",
                "content": [
                    {"type": "text", "text": prompt},
                    {"type": "text", "text": "\n\nSOURCE IMAGE:"},
                    {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{source_b64}"}},
                    {"type": "text", "text": "\n\nCROPPED IMAGE:"},
                    {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{cropped_b64}"}}
                ]
            }],
            "temperature": 0.1,
            "max_tokens": 250
        }
        
        try:
            response = self.session.post(self.vlm_url, json=payload, timeout=30)
            if response.status_code == 200:
                vlm_response = response.json()['choices'][0]['message']['content']
                
                # Parse response
                predicted_fp = 'YES' in vlm_response.split('FALSE POSITIVE:')[1][:5]
                
                result = {
                    'case_number': case['case_number'],
                    'actual_fp': case['is_false_positive'],
                    'predicted_fp': predicted_fp,
                    'correct': predicted_fp == case['is_false_positive']
                }
                
                # Show immediate result
                if not case['is_false_positive'] and not predicted_fp:
                    print(f" [✓ Valid Protected]")
                elif not case['is_false_positive'] and predicted_fp:
                    print(f" [✗ VALID MISSED!]")
                elif case['is_false_positive'] and predicted_fp:
                    print(f" [✓ FP Detected]")
                else:
                    print(f" [- FP Not Caught]")
                
                return result
                
        except Exception as e:
            print(f" [ERROR: {str(e)[:20]}]")
        
        return None
    
    def test_strategy(self, strategy_name, config):
        """Test a strategy with real-time progress"""
        
        print(f"\n{'='*60}")
        print(f"Testing: {config['name']}")
        print(f"{'='*60}")
        print(f"Thresholds: {json.dumps(config['thresholds'], indent=2)}")
        
        # Load test data
        with open('valo_batch_round3_complete.json', 'r') as f:
            all_cases = json.load(f)['results']
        
        # Get mix of valid and FP cases
        valid_cases = [c for c in all_cases if not c['is_false_positive']]
        fp_cases = [c for c in all_cases if c['is_false_positive']]
        
        # Sample evenly
        test_cases = []
        if valid_cases:
            test_cases.extend(random.sample(valid_cases, min(5, len(valid_cases))))
        if fp_cases:
            test_cases.extend(random.sample(fp_cases, min(5, len(fp_cases))))
        
        random.shuffle(test_cases)
        
        print(f"\nTesting {len(test_cases)} cases ({len([c for c in test_cases if not c['is_false_positive']])} valid, {len([c for c in test_cases if c['is_false_positive']])} FP):")
        
        # Create prompt
        prompt = self.create_prompt_with_thresholds(config['thresholds'])
        
        # Test each case
        results = []
        for case in test_cases:
            result = self.test_single_case(case, prompt, strategy_name)
            if result:
                results.append(result)
            time.sleep(1)  # Rate limit
        
        # Calculate metrics
        if results:
            valid_results = [r for r in results if not r['actual_fp']]
            fp_results = [r for r in results if r['actual_fp']]
            
            valid_protected = sum(not r['predicted_fp'] for r in valid_results) if valid_results else 0
            fp_detected = sum(r['predicted_fp'] for r in fp_results) if fp_results else 0
            
            metrics = {
                'valid_protection': (valid_protected / len(valid_results) * 100) if valid_results else 100,
                'valid_protected': valid_protected,
                'valid_total': len(valid_results),
                'fp_detection': (fp_detected / len(fp_results) * 100) if fp_results else 0,
                'fp_detected': fp_detected,
                'fp_total': len(fp_results),
                'accuracy': sum(r['correct'] for r in results) / len(results) * 100
            }
            
            print(f"\nResults:")
            print(f"├─ Valid Protection: {metrics['valid_protection']:.0f}% ({metrics['valid_protected']}/{metrics['valid_total']})")
            print(f"├─ FP Detection: {metrics['fp_detection']:.0f}% ({metrics['fp_detected']}/{metrics['fp_total']})")
            print(f"└─ Overall Accuracy: {metrics['accuracy']:.0f}%")
            
            self.results[strategy_name] = {
                'config': config,
                'metrics': metrics,
                'raw_results': results
            }
    
    def run_exploration(self):
        """Run the mini exploration"""
        
        print("="*60)
        print("100% VALID PROTECTION EXPLORATION - MINI VERSION")
        print("="*60)
        print(f"Testing {self.cases_per_strategy} cases per strategy")
        print("Goal: Find strategies that achieve 100% valid protection")
        
        # Test each strategy
        for strategy_name, config in self.strategies.items():
            self.test_strategy(strategy_name, config)
        
        # Summary
        print("\n" + "="*60)
        print("EXPLORATION SUMMARY")
        print("="*60)
        
        # Find strategies with 100% valid protection
        perfect_strategies = []
        for name, result in self.results.items():
            metrics = result['metrics']
            if metrics['valid_protection'] == 100:
                perfect_strategies.append((name, metrics))
        
        if perfect_strategies:
            print("\n🎯 STRATEGIES ACHIEVING 100% VALID PROTECTION:")
            for name, metrics in perfect_strategies:
                print(f"\n{self.strategies[name]['name']}:")
                print(f"├─ Valid Protection: 100% ✓")
                print(f"├─ FP Detection: {metrics['fp_detection']:.0f}%")
                print(f"└─ Trade-off: Lost {100 - metrics['fp_detection']:.0f}% FP detection capability")
        else:
            print("\n⚠️ No strategies achieved 100% valid protection in this sample")
            print("\nBest Valid Protection:")
            best = max(self.results.items(), key=lambda x: x[1]['metrics']['valid_protection'])
            print(f"{self.strategies[best[0]]['name']}: {best[1]['metrics']['valid_protection']:.0f}%")
        
        # Save results
        report = {
            'timestamp': datetime.now().isoformat(),
            'sample_size': self.cases_per_strategy,
            'strategies': self.strategies,
            'results': {k: v['metrics'] for k, v in self.results.items()},
            'perfect_strategies': [s[0] for s in perfect_strategies] if perfect_strategies else []
        }
        
        with open('100_percent_mini_exploration.json', 'w') as f:
            json.dump(report, f, indent=2)
        
        print(f"\n📄 Results saved to: 100_percent_mini_exploration.json")

def main():
    explorer = MiniValidProtectionExplorer()
    explorer.run_exploration()

if __name__ == "__main__":
    main()