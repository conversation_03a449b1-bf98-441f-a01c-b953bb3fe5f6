#!/usr/bin/env python3
"""
VALO Auto-Learning Test System
Runs all 1250+ cases with intelligent self-tuning through multiple rounds
"""

import json
import base64
import requests
import os
from datetime import datetime
import time
from collections import defaultdict

class VALOAutoLearningTest:
    def __init__(self):
        self.vlm_url = "http://**************:9500/v1/chat/completions"
        self.vlm_model = "VLM-38B-AWQ"
        self.session = requests.Session()
        
        # Learning configuration
        self.max_rounds = 5  # Start with 5 rounds
        
        # Performance targets
        self.targets = {
            'valid_protection': 98.0,  # Must protect valid violations
            'fp_detection': 75.0,      # Target FP detection
        }
        
        # Dynamic confidence thresholds
        self.thresholds = {
            'structure': 90,
            'person': 50,
            'ppe_compliant': 70,
            'behavioral': 60
        }
        
        # Learning history
        self.history = []
        
    def encode_image(self, image_path):
        """Encode image to base64"""
        try:
            if os.path.exists(image_path):
                with open(image_path, 'rb') as f:
                    return base64.b64encode(f.read()).decode('utf-8')
        except:
            pass
        return None
    
    def create_adaptive_prompt(self, round_num):
        """Create prompt with adaptive thresholds"""
        
        prompt = f"""SAFETY VIOLATION DETECTION - ROUND {round_num} ADAPTIVE LEARNING

ANALYZE BOTH PROVIDED IMAGES (SOURCE and CROPPED).

STEP 1: ENTITY IDENTIFICATION WITH CONFIDENCE SCORING

A) INDUSTRIAL STRUCTURE (confidence threshold: {self.thresholds['structure']}%)
   Rate your confidence (0-100%) that this is a structure:
   
   CRANE: Metal framework, geometric beams, painted metal
   VESSEL: Ship railings, deck components, maritime equipment  
   PM: Mechanical truck, large wheels, industrial chassis
   SPREADER: Container frame, corner guides, lifting mechanisms
   
   If confidence >= {self.thresholds['structure']}% → It's a STRUCTURE

B) PERSON DETECTION (confidence threshold: {self.thresholds['person']}%)
   Rate your confidence (0-100%) that this is a person:
   - Human shape, clothing, PPE visible
   - Arms, legs, human proportions
   
   If confidence >= {self.thresholds['person']}% → It's a PERSON

C) UNCLEAR
   If neither threshold met → UNCLEAR

STEP 2: PPE COMPLIANCE CHECK (threshold: {self.thresholds['ppe_compliant']}%)
If PERSON detected, rate PPE compliance confidence:
- Helmet present and worn: Check
- Vest present and fastened: Check
- Both required for compliance

If confidence >= {self.thresholds['ppe_compliant']}% → PPE COMPLIANT

STEP 3: BEHAVIORAL VIOLATIONS (threshold: {self.thresholds['behavioral']}%)
Rate confidence for each potential violation:
- Mobile phone use
- Missing required equipment  
- Unsafe location/behavior

If any violation confidence >= {self.thresholds['behavioral']}% → VIOLATION DETECTED

STEP 4: DECISION WITH CONFIDENCE

1. STRUCTURE with high confidence → FALSE POSITIVE: YES
2. PERSON + PPE COMPLIANT + NO violations → FALSE POSITIVE: YES
3. PERSON + Any violation → FALSE POSITIVE: NO
4. UNCLEAR → FALSE POSITIVE: NO (safety default)

OUTPUT FORMAT:
FALSE POSITIVE: [YES/NO]
Entity: [STRUCTURE/PERSON/UNCLEAR]
Entity Confidence: [X%]
PPE Status: [COMPLIANT/NON-COMPLIANT/N/A]
PPE Confidence: [X%]
Violations: [List any]
Violation Confidence: [X%]
Overall Confidence: [High/Medium/Low]"""

        return prompt
    
    def test_case(self, case, prompt):
        """Test a single case"""
        
        # Encode images
        source_b64 = self.encode_image(case['source_image'])
        cropped_b64 = self.encode_image(case['cropped_image'])
        
        if not source_b64 or not cropped_b64:
            return None
        
        payload = {
            "model": self.vlm_model,
            "messages": [{
                "role": "user",
                "content": [
                    {"type": "text", "text": prompt},
                    {"type": "text", "text": "\n\nSOURCE IMAGE:"},
                    {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{source_b64}"}},
                    {"type": "text", "text": "\n\nCROPPED IMAGE:"},
                    {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{cropped_b64}"}}
                ]
            }],
            "temperature": 0.1,
            "max_tokens": 250
        }
        
        try:
            response = self.session.post(self.vlm_url, json=payload, timeout=20)
            if response.status_code == 200:
                vlm_response = response.json()['choices'][0]['message']['content']
                
                # Parse response
                predicted_fp = 'YES' in vlm_response.split('FALSE POSITIVE:')[1][:5]
                
                # Extract confidence scores
                entity_conf = 0
                ppe_conf = 0
                violation_conf = 0
                
                lines = vlm_response.split('\n')
                for line in lines:
                    if 'Entity Confidence:' in line:
                        try:
                            entity_conf = int(line.split(':')[1].strip().rstrip('%'))
                        except:
                            pass
                    elif 'PPE Confidence:' in line:
                        try:
                            ppe_conf = int(line.split(':')[1].strip().rstrip('%'))
                        except:
                            pass
                    elif 'Violation Confidence:' in line:
                        try:
                            violation_conf = int(line.split(':')[1].strip().rstrip('%'))
                        except:
                            pass
                
                return {
                    'case_number': case['case_number'],
                    'actual_fp': case['is_false_positive'],
                    'predicted_fp': predicted_fp,
                    'correct': predicted_fp == case['is_false_positive'],
                    'entity_confidence': entity_conf,
                    'ppe_confidence': ppe_conf,
                    'violation_confidence': violation_conf,
                    'response': vlm_response
                }
        except:
            pass
        
        return None
    
    def analyze_errors_and_adjust(self, results):
        """Analyze errors and adjust thresholds"""
        
        # Categorize errors
        false_negatives = []  # Valid violations marked as FP (BAD)
        false_positives = []  # FPs not detected
        
        for r in results:
            if not r['correct']:
                if not r['actual_fp'] and r['predicted_fp']:
                    false_negatives.append(r)  # Critical error
                elif r['actual_fp'] and not r['predicted_fp']:
                    false_positives.append(r)
        
        adjustments = {}
        
        # Priority: Protect valid violations
        if len(false_negatives) > 5:
            print(f"\n⚠️  {len(false_negatives)} valid violations missed!")
            
            # Analyze confidence patterns
            avg_entity_conf = sum(r['entity_confidence'] for r in false_negatives) / len(false_negatives)
            
            if avg_entity_conf > 80:  # High confidence in structure when it was a person
                adjustments['structure'] = min(95, self.thresholds['structure'] + 5)
                print(f"  → Increasing structure threshold to {adjustments['structure']}%")
            
            adjustments['behavioral'] = max(50, self.thresholds['behavioral'] - 5)
            print(f"  → Decreasing behavioral threshold to {adjustments['behavioral']}%")
        
        # Secondary: Improve FP detection
        if len(false_positives) > 20 and len(false_negatives) < 3:
            print(f"\n📊 {len(false_positives)} false positives not caught")
            adjustments['structure'] = max(85, self.thresholds['structure'] - 3)
            adjustments['ppe_compliant'] = min(80, self.thresholds['ppe_compliant'] + 5)
        
        return adjustments
    
    def run_learning_round(self, round_num, all_cases):
        """Run one complete learning round"""
        
        print(f"\n{'='*70}")
        print(f"LEARNING ROUND {round_num}")
        print(f"{'='*70}")
        
        # Create adaptive prompt
        prompt = self.create_adaptive_prompt(round_num)
        
        print(f"Current thresholds:")
        for key, value in self.thresholds.items():
            print(f"  {key}: {value}%")
        
        results = []
        errors = []
        
        print(f"\nTesting {len(all_cases)} cases...")
        start_time = time.time()
        
        for i, case in enumerate(all_cases):
            if i % 100 == 0 and i > 0:
                elapsed = time.time() - start_time
                rate = i / elapsed
                eta = (len(all_cases) - i) / rate / 60
                print(f"Progress: {i}/{len(all_cases)} ({i/len(all_cases)*100:.1f}%) - ETA: {eta:.1f} min")
            
            result = self.test_case(case, prompt)
            if result:
                results.append(result)
                if not result['correct']:
                    errors.append(result)
            
            time.sleep(0.2)  # Rate limit
        
        # Calculate metrics
        metrics = self.calculate_metrics(results)
        
        print(f"\nRound {round_num} Results:")
        print(f"├─ Overall Accuracy: {metrics['accuracy']:.1f}%")
        print(f"├─ FP Detection: {metrics['fp_detection']:.1f}% ({metrics['fp_detected']}/{metrics['fp_total']})")
        print(f"└─ Valid Protection: {metrics['valid_protection']:.1f}% ({metrics['valid_protected']}/{metrics['valid_total']})")
        
        # Analyze and adjust
        adjustments = self.analyze_errors_and_adjust(results)
        
        # Apply adjustments
        for key, new_value in adjustments.items():
            self.thresholds[key] = new_value
        
        # Save round data
        round_data = {
            'round': round_num,
            'metrics': metrics,
            'thresholds': self.thresholds.copy(),
            'errors': len(errors),
            'adjustments': adjustments
        }
        
        self.history.append(round_data)
        
        # Save intermediate results
        with open(f'valo_auto_round_{round_num}_results.json', 'w') as f:
            json.dump(round_data, f, indent=2)
        
        return metrics
    
    def calculate_metrics(self, results):
        """Calculate performance metrics"""
        
        total = len(results)
        correct = sum(r['correct'] for r in results)
        accuracy = correct / total * 100
        
        # FP metrics
        actual_fps = [r for r in results if r['actual_fp']]
        fp_total = len(actual_fps)
        fp_detected = sum(r['predicted_fp'] for r in actual_fps)
        fp_rate = fp_detected / fp_total * 100 if fp_total > 0 else 0
        
        # Valid metrics
        actual_valid = [r for r in results if not r['actual_fp']]
        valid_total = len(actual_valid)
        valid_protected = sum(not r['predicted_fp'] for r in actual_valid)
        protection_rate = valid_protected / valid_total * 100 if valid_total > 0 else 100
        
        return {
            'accuracy': accuracy,
            'fp_detection': fp_rate,
            'fp_total': fp_total,
            'fp_detected': fp_detected,
            'valid_protection': protection_rate,
            'valid_total': valid_total,
            'valid_protected': valid_protected
        }
    
    def run_auto_learning(self):
        """Run the complete auto-learning process"""
        
        print("VALO AUTO-LEARNING TEST SYSTEM")
        print("="*70)
        print("This will test ALL 1250+ cases through multiple learning rounds")
        print(f"\nTargets:")
        print(f"├─ Valid Protection: {self.targets['valid_protection']}%")
        print(f"└─ FP Detection: {self.targets['fp_detection']}%")
        
        # Load all data
        print("\nLoading test data...")
        with open('valo_batch_round3_complete.json', 'r') as f:
            all_cases = json.load(f)['results']
        
        print(f"Loaded {len(all_cases)} cases")
        
        # Run learning rounds
        for round_num in range(1, self.max_rounds + 1):
            metrics = self.run_learning_round(round_num, all_cases)
            
            # Check if targets met
            if metrics['valid_protection'] >= self.targets['valid_protection'] and \
               metrics['fp_detection'] >= self.targets['fp_detection']:
                print(f"\n✅ TARGETS ACHIEVED IN ROUND {round_num}!")
                break
            
            # Check for convergence
            if round_num > 2:
                recent = [h['metrics']['accuracy'] for h in self.history[-3:]]
                if max(recent) - min(recent) < 1.0:
                    print("\n📊 Performance converged. Stopping.")
                    break
        
        # Generate final report
        self.generate_final_report()
    
    def generate_final_report(self):
        """Generate final report"""
        
        print("\n" + "="*70)
        print("AUTO-LEARNING COMPLETE")
        print("="*70)
        
        # Find best round
        best_round = max(self.history, 
                        key=lambda x: x['metrics']['valid_protection'] * 0.7 + 
                                     x['metrics']['fp_detection'] * 0.3)
        
        print(f"\nBest Performance (Round {best_round['round']}):")
        print(f"├─ Overall Accuracy: {best_round['metrics']['accuracy']:.1f}%")
        print(f"├─ FP Detection: {best_round['metrics']['fp_detection']:.1f}%")
        print(f"└─ Valid Protection: {best_round['metrics']['valid_protection']:.1f}%")
        
        print(f"\nOptimal Thresholds:")
        for key, value in best_round['thresholds'].items():
            print(f"├─ {key}: {value}%")
        
        # Save final report
        report = {
            'timestamp': datetime.now().isoformat(),
            'total_rounds': len(self.history),
            'best_round': best_round,
            'learning_history': self.history,
            'targets_achieved': {
                'valid_protection': best_round['metrics']['valid_protection'] >= self.targets['valid_protection'],
                'fp_detection': best_round['metrics']['fp_detection'] >= self.targets['fp_detection']
            }
        }
        
        with open('valo_auto_learning_results.json', 'w') as f:
            json.dump(report, f, indent=2)
        
        print(f"\nFinal report saved to: valo_auto_learning_results.json")

def main():
    tester = VALOAutoLearningTest()
    tester.run_auto_learning()

if __name__ == "__main__":
    main()