#!/usr/bin/env python3
"""
Test the backend API endpoints to verify VLM integration
"""

import asyncio
import base64
import httpx
import json
import tempfile
import os


def create_test_image_file():
    """Create a test image file"""
    # 1x1 blue pixel PNG as bytes
    png_data = base64.b64decode("iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==")
    
    # Create temporary file
    with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as f:
        f.write(png_data)
        return f.name


async def test_backend_health():
    """Test backend health endpoint"""
    print("🔍 Testing Backend Health Endpoint")
    print("-" * 40)
    
    try:
        async with httpx.AsyncClient(timeout=10) as client:
            response = await client.get("http://localhost:8001/health")
            
            if response.status_code == 200:
                health_data = response.json()
                print(f"✅ Backend Health: {health_data['status']}")
                print(f"   - VLM API Status: {health_data['vlm_api_status']}")
                print(f"   - Database Status: {health_data['database_status']}")
                print(f"   - Uptime: {health_data['uptime_seconds']}s")
                return True, health_data
            else:
                print(f"❌ Health check failed: HTTP {response.status_code}")
                return False, None
                
    except Exception as e:
        print(f"❌ Health check error: {e}")
        return False, None


async def test_vlm_analysis_endpoint():
    """Test VLM analysis endpoint"""
    print(f"\n🔍 Testing VLM Analysis Endpoint")
    print("-" * 40)
    
    # Create test image
    test_image_path = create_test_image_file()
    
    try:
        # Prepare multipart form data
        with open(test_image_path, 'rb') as f:
            files = {
                'image': ('test.png', f, 'image/png')
            }
            data = {
                'case_number': 'API_TEST_001',
                'custom_prompt': 'Analyze this test image for safety violations'
            }
            
            async with httpx.AsyncClient(timeout=30) as client:
                print(f"📤 Uploading test image for analysis...")
                response = await client.post(
                    "http://localhost:8001/api/v1/vlm/analyze",
                    files=files,
                    data=data
                )
                
                print(f"📥 Response Status: {response.status_code}")
                
                if response.status_code == 200:
                    result = response.json()
                    print(f"✅ VLM Analysis successful!")
                    print(f"📊 Analysis Result:")
                    print(f"   - Detection Type: {result.get('detection_type')}")
                    print(f"   - False Positive Likelihood: {result.get('false_positive_likelihood')}%")
                    print(f"   - True Violation Likelihood: {result.get('true_violation_likelihood')}%")
                    print(f"   - Recommendation: {result.get('recommendation')}")
                    print(f"   - Confidence Score: {result.get('confidence_score')}")
                    print(f"   - Processing Time: {result.get('processing_time_ms')}ms")
                    print(f"🤖 Reasoning: {result.get('reasoning', 'N/A')}")
                    return True, result
                else:
                    print(f"❌ VLM Analysis failed: HTTP {response.status_code}")
                    print(f"📥 Error Response: {response.text}")
                    return False, None
                    
    except Exception as e:
        print(f"❌ VLM Analysis error: {e}")
        return False, None
    finally:
        # Clean up test image
        try:
            os.unlink(test_image_path)
        except:
            pass


async def test_vlm_health_endpoint():
    """Test VLM health endpoint"""
    print(f"\n🔍 Testing VLM Health Endpoint")
    print("-" * 40)
    
    try:
        async with httpx.AsyncClient(timeout=10) as client:
            response = await client.get("http://localhost:8001/api/v1/vlm/health")
            
            print(f"📥 Response Status: {response.status_code}")
            
            if response.status_code == 200:
                health_data = response.json()
                print(f"✅ VLM Health check successful!")
                print(f"📊 VLM Status: {health_data.get('status')}")
                print(f"   - Response Time: {health_data.get('response_time_ms')}ms")
                print(f"   - API Version: {health_data.get('api_version', 'unknown')}")
                if health_data.get('error'):
                    print(f"   - Error: {health_data.get('error')}")
                return True, health_data
            else:
                print(f"❌ VLM Health check failed: HTTP {response.status_code}")
                print(f"📥 Error Response: {response.text}")
                return False, None
                
    except Exception as e:
        print(f"❌ VLM Health check error: {e}")
        return False, None


async def main():
    """Main test function"""
    print("🚀 VALO AI-FARM Backend API Integration Test")
    print("=" * 60)
    
    # Test backend health
    backend_health_ok, backend_health_data = await test_backend_health()
    
    # Test VLM health endpoint
    vlm_health_ok, vlm_health_data = await test_vlm_health_endpoint()
    
    # Test VLM analysis endpoint
    vlm_analysis_ok, vlm_analysis_data = await test_vlm_analysis_endpoint()
    
    print("\n" + "=" * 60)
    print("📊 Backend API Test Results:")
    print(f"{'✅' if backend_health_ok else '❌'} Backend Health: {'PASS' if backend_health_ok else 'FAIL'}")
    print(f"{'✅' if vlm_health_ok else '❌'} VLM Health Endpoint: {'PASS' if vlm_health_ok else 'FAIL'}")
    print(f"{'✅' if vlm_analysis_ok else '❌'} VLM Analysis Endpoint: {'PASS' if vlm_analysis_ok else 'FAIL'}")
    
    if backend_health_ok and vlm_analysis_ok:
        print(f"\n🎉 SUCCESS! Backend API is fully functional.")
        print(f"✅ VLM integration working through backend API")
        print(f"✅ Ready to test frontend integration")
        return 0
    else:
        print(f"\n❌ Some backend API tests failed.")
        if vlm_health_data and vlm_health_data.get('error'):
            print(f"🔧 VLM Error: {vlm_health_data['error']}")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    exit(exit_code)
