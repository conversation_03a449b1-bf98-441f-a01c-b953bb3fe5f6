#!/usr/bin/env python3
"""Monitor Round 3 full completion progress"""

import json
import os
import time
from datetime import datetime

print("Monitoring Round 3 Full Completion (1250 cases)")
print("="*60)

start_time = time.time()

while True:
    # Check completion marker
    if os.path.exists('ROUND3_COMPLETE_ALL_1250.txt'):
        print("\n✅ ROUND 3 FULLY COMPLETE!")
        
        # Load final results
        with open('valo_batch_round3_complete.json', 'r') as f:
            data = json.load(f)
            stats = data['stats']
        
        print(f"\nFinal Statistics:")
        print(f"  Total cases: {stats['total_cases']}")
        print(f"  Valid Protection: {stats['valid_protection_rate']:.1f}%")
        print(f"  FP Detection: {stats['fp_detection_rate']:.1f}%")
        print(f"\nTime taken: {(time.time() - start_time)/60:.1f} minutes")
        break
    
    # Check progress file
    progress_file = 'round3_complete_output/progress.json'
    if os.path.exists(progress_file):
        try:
            with open(progress_file, 'r') as f:
                progress = json.load(f)
            
            total = progress['total_processed']
            percent = (total / 1250) * 100
            
            # Estimate time remaining
            if total > 540:
                cases_done = total - 540
                elapsed = time.time() - start_time
                rate = cases_done / elapsed
                remaining = (1250 - total) / rate if rate > 0 else 0
                
                print(f"\r[{datetime.now().strftime('%H:%M:%S')}] Progress: {total}/1250 ({percent:.1f}%) | ETA: {remaining/60:.1f} min", end='', flush=True)
            else:
                print(f"\r[{datetime.now().strftime('%H:%M:%S')}] Progress: {total}/1250 ({percent:.1f}%)", end='', flush=True)
        except:
            pass
    
    # Check log file
    if os.path.exists('round3_full_completion.log'):
        try:
            with open('round3_full_completion.log', 'r') as f:
                lines = f.readlines()
                if len(lines) > 100:
                    # Find latest batch info
                    for line in reversed(lines[-20:]):
                        if 'Total progress:' in line:
                            print(f"\r[{datetime.now().strftime('%H:%M:%S')}] {line.strip()}", end='', flush=True)
                            break
        except:
            pass
    
    time.sleep(3)

print("\n\nRound 3 monitoring complete. Ready for Round 4!")