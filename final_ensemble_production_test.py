#!/usr/bin/env python3
"""
Final Production Ensemble Test
Tests the recommended ensemble approach vs single approaches
Uses NO REMARKS - production realistic
"""

import json
import asyncio
import aiohttp
import logging
import base64
from datetime import datetime
from typing import Dict, List, Optional, Tuple

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class ProductionEnsembleAnalyzer:
    """Final production ensemble implementation"""
    
    def __init__(self):
        self.vlm_endpoint = "http://100.106.127.35:9500/v1/chat/completions"
        self.model = "VLM-38B-AWQ"
        
        # The three successful approaches
        self.approaches = {
            'assumption_based': """SAFETY ALERT ANALYSIS
DEFAULT ASSUMPTION: This is a false positive (97% probability)
To override this assumption, you need CLEAR evidence:
- Person clearly visible AND
- No helmet visible AND
- No safety vest visible

Is this a FALSE POSITIVE? YES/NO""",
            
            'alert_fatigue_prevention': """ALERT FATIGUE PREVENTION MODE
Too many false alerts = ignored real violations
Help reduce false alerts by being practical.
Mark as FALSE POSITIVE unless you see:
- Obvious person
- Clearly missing PPE
- No doubt whatsoever
Is this a FALSE POSITIVE? YES/NO""",
            
            'worksite_reality': """WORKSITE REALITY CHECK
Facts about worksites:
- Workers wear PPE to protect themselves
- Supervisors enforce PPE rules
- Workers without PPE get sent home
Is it likely someone is working without PPE?
Probably not → FALSE POSITIVE"""
        }
    
    async def analyze_single_approach(self, session: aiohttp.ClientSession, 
                                    image_path: str, prompt: str) -> Tuple[bool, float]:
        """Analyze with a single approach"""
        try:
            with open(image_path, 'rb') as f:
                image_data = base64.b64encode(f.read()).decode('utf-8')
            
            payload = {
                "model": self.model,
                "messages": [{
                    "role": "user",
                    "content": [
                        {"type": "text", "text": prompt},
                        {"type": "image_url", 
                         "image_url": {"url": f"data:image/jpeg;base64,{image_data}"}}
                    ]
                }],
                "temperature": 0.1,
                "max_tokens": 50
            }
            
            async with session.post(self.vlm_endpoint, json=payload, timeout=30) as response:
                if response.status == 200:
                    result = await response.json()
                    content = result['choices'][0]['message']['content'].upper()
                    
                    # Extract confidence based on response patterns
                    if "NO DOUBT" in content or "CLEARLY" in content:
                        confidence = 0.95
                    elif "PROBABLY" in content:
                        confidence = 0.8
                    else:
                        confidence = 0.7
                    
                    is_fp = "YES" in content[:50] or "FALSE POSITIVE" in content
                    return is_fp, confidence
                    
        except Exception as e:
            logger.error(f"Error in analysis: {e}")
            return False, 0.0
        
        return False, 0.0
    
    async def ensemble_analyze(self, session: aiohttp.ClientSession, 
                             case: Dict) -> Dict:
        """Ensemble analysis with confidence scoring"""
        
        results = {}
        
        # Stage 1: assumption_based (conservative filter)
        is_fp_1, conf_1 = await self.analyze_single_approach(
            session, case['cropped_image'], self.approaches['assumption_based']
        )
        results['stage1'] = {'is_fp': is_fp_1, 'confidence': conf_1}
        
        # Fast path: High confidence FP
        if is_fp_1 and conf_1 > 0.9:
            return {
                'is_false_positive': True,
                'confidence': conf_1,
                'method': 'fast_path_assumption',
                'stages_used': 1
            }
        
        # Stage 2: alert_fatigue_prevention (aggressive check)
        is_fp_2, conf_2 = await self.analyze_single_approach(
            session, case['cropped_image'], self.approaches['alert_fatigue_prevention']
        )
        results['stage2'] = {'is_fp': is_fp_2, 'confidence': conf_2}
        
        # If both agree
        if is_fp_1 == is_fp_2:
            avg_conf = (conf_1 + conf_2) / 2
            return {
                'is_false_positive': is_fp_1,
                'confidence': avg_conf,
                'method': 'consensus_agreement',
                'stages_used': 2
            }
        
        # Stage 3: worksite_reality (tiebreaker)
        is_fp_3, conf_3 = await self.analyze_single_approach(
            session, case['cropped_image'], self.approaches['worksite_reality']
        )
        results['stage3'] = {'is_fp': is_fp_3, 'confidence': conf_3}
        
        # Weighted voting
        fp_votes = sum([is_fp_1, is_fp_2, is_fp_3])
        total_conf = conf_1 + conf_2 + conf_3
        
        # Weighted confidence based on agreement
        if fp_votes == 3:
            final_conf = total_conf / 3
        elif fp_votes == 2:
            final_conf = (total_conf / 3) * 0.85  # Reduce confidence for disagreement
        else:
            final_conf = (total_conf / 3) * 0.7
        
        return {
            'is_false_positive': fp_votes >= 2,  # Majority vote
            'confidence': final_conf,
            'method': f'weighted_vote_{fp_votes}_of_3',
            'stages_used': 3,
            'details': results
        }
    
    async def test_approach(self, approach_name: str, test_cases: List[Dict]) -> Dict:
        """Test a single approach or ensemble"""
        results = []
        errors = 0
        
        async with aiohttp.ClientSession() as session:
            for i, case in enumerate(test_cases):
                try:
                    if approach_name == 'ensemble':
                        result = await self.ensemble_analyze(session, case)
                        predicted_fp = result['is_false_positive']
                        confidence = result['confidence']
                        method = result['method']
                    else:
                        prompt = self.approaches[approach_name]
                        predicted_fp, confidence = await self.analyze_single_approach(
                            session, case['cropped_image'], prompt
                        )
                        method = approach_name
                    
                    results.append({
                        'case_number': case['case_number'],
                        'actual_fp': case['is_false_positive'],
                        'predicted_fp': predicted_fp,
                        'confidence': confidence,
                        'method': method
                    })
                    
                except Exception as e:
                    logger.error(f"Error processing case {case['case_number']}: {e}")
                    errors += 1
                
                # Progress update
                if (i + 1) % 50 == 0:
                    tp = sum(1 for r in results if r['actual_fp'] and r['predicted_fp'])
                    fp_total = sum(1 for r in results if r['actual_fp'])
                    rate = (tp / fp_total * 100) if fp_total > 0 else 0
                    logger.info(f"{approach_name}: {i+1}/{len(test_cases)} | "
                               f"FP Detection: {rate:.1f}%")
                
                await asyncio.sleep(0.1)  # Rate limiting
        
        return self.calculate_metrics(results, approach_name, errors)
    
    def calculate_metrics(self, results: List[Dict], approach_name: str, errors: int) -> Dict:
        """Calculate comprehensive metrics"""
        tp = sum(1 for r in results if r['actual_fp'] and r['predicted_fp'])
        tn = sum(1 for r in results if not r['actual_fp'] and not r['predicted_fp'])
        fp = sum(1 for r in results if not r['actual_fp'] and r['predicted_fp'])
        fn = sum(1 for r in results if r['actual_fp'] and not r['predicted_fp'])
        
        fp_total = sum(1 for r in results if r['actual_fp'])
        valid_total = sum(1 for r in results if not r['actual_fp'])
        
        fp_detection = (tp / fp_total * 100) if fp_total > 0 else 0
        valid_protection = (tn / valid_total * 100) if valid_total > 0 else 100
        
        # Calculate average confidence
        avg_confidence = sum(r['confidence'] for r in results) / len(results)
        
        # Confidence distribution
        high_conf = sum(1 for r in results if r['confidence'] > 0.9)
        med_conf = sum(1 for r in results if 0.7 <= r['confidence'] <= 0.9)
        low_conf = sum(1 for r in results if r['confidence'] < 0.7)
        
        return {
            'approach': approach_name,
            'fp_detection_rate': fp_detection,
            'valid_protection_rate': valid_protection,
            'average_confidence': avg_confidence,
            'total_cases': len(results),
            'errors': errors,
            'confusion_matrix': {
                'tp': tp, 'tn': tn, 'fp': fp, 'fn': fn
            },
            'confidence_distribution': {
                'high': high_conf / len(results) * 100,
                'medium': med_conf / len(results) * 100,
                'low': low_conf / len(results) * 100
            },
            'method_distribution': {}  # For ensemble
        }


async def main():
    """Run the final comparison test"""
    logger.info("="*70)
    logger.info("FINAL PRODUCTION TEST: ENSEMBLE vs SINGLE APPROACHES")
    logger.info("="*70)
    
    # Load test data WITHOUT remarks
    with open('valo_batch_round3_complete.json', 'r') as f:
        data = json.load(f)
        test_cases = []
        
        # Use 500 cases for thorough testing
        for case in data['results'][:500]:
            test_cases.append({
                'case_number': case['case_number'],
                'cropped_image': case['cropped_image'],
                'infringement_type': case['infringement_type'],
                'is_false_positive': case['is_false_positive']
            })
    
    logger.info(f"Testing on {len(test_cases)} cases")
    logger.info(f"FP cases: {sum(1 for c in test_cases if c['is_false_positive'])}")
    logger.info(f"Valid cases: {sum(1 for c in test_cases if not c['is_false_positive'])}")
    
    analyzer = ProductionEnsembleAnalyzer()
    results = {}
    
    # Test each approach
    approaches_to_test = ['assumption_based', 'alert_fatigue_prevention', 
                         'worksite_reality', 'ensemble']
    
    for approach in approaches_to_test:
        logger.info(f"\n{'='*50}")
        logger.info(f"Testing: {approach}")
        logger.info(f"{'='*50}")
        
        start_time = datetime.now()
        result = await analyzer.test_approach(approach, test_cases)
        end_time = datetime.now()
        
        result['processing_time'] = (end_time - start_time).total_seconds()
        results[approach] = result
        
        logger.info(f"\nResults for {approach}:")
        logger.info(f"  FP Detection: {result['fp_detection_rate']:.1f}%")
        logger.info(f"  Valid Protection: {result['valid_protection_rate']:.1f}%")
        logger.info(f"  Avg Confidence: {result['average_confidence']:.2f}")
        logger.info(f"  Processing Time: {result['processing_time']:.1f}s")
        
        # Save intermediate results
        with open(f'final_test_{approach}_results.json', 'w') as f:
            json.dump(result, f, indent=2)
        
        await asyncio.sleep(5)  # Pause between approaches
    
    # Final comparison report
    logger.info("\n" + "="*70)
    logger.info("FINAL COMPARISON REPORT")
    logger.info("="*70)
    
    # Create comparison table
    logger.info("\nPerformance Comparison:")
    logger.info("─"*60)
    logger.info(f"{'Approach':<20} {'FP Rate':<10} {'Valid':<10} {'Conf':<10} {'Time':<10}")
    logger.info("─"*60)
    
    for name, result in results.items():
        logger.info(f"{name:<20} {result['fp_detection_rate']:>6.1f}% "
                   f"{result['valid_protection_rate']:>8.1f}% "
                   f"{result['average_confidence']:>8.2f} "
                   f"{result['processing_time']:>7.1f}s")
    
    # Key insights
    ensemble_result = results['ensemble']
    best_single = max([results['assumption_based'], results['alert_fatigue_prevention'], 
                      results['worksite_reality']], 
                     key=lambda x: x['fp_detection_rate'] if x['valid_protection_rate'] >= 85 else 0)
    
    logger.info("\n" + "="*70)
    logger.info("KEY INSIGHTS")
    logger.info("="*70)
    
    improvement = ensemble_result['fp_detection_rate'] - best_single['fp_detection_rate']
    logger.info(f"\n1. Ensemble Performance:")
    logger.info(f"   - FP Detection: {ensemble_result['fp_detection_rate']:.1f}%")
    logger.info(f"   - Valid Protection: {ensemble_result['valid_protection_rate']:.1f}%")
    logger.info(f"   - Improvement over best single: {improvement:+.1f}%")
    
    logger.info(f"\n2. Confidence Analysis:")
    logger.info(f"   - High confidence decisions: {ensemble_result['confidence_distribution']['high']:.1f}%")
    logger.info(f"   - Medium confidence: {ensemble_result['confidence_distribution']['medium']:.1f}%")
    logger.info(f"   - Low confidence: {ensemble_result['confidence_distribution']['low']:.1f}%")
    
    logger.info(f"\n3. Processing Efficiency:")
    logger.info(f"   - Ensemble uses fast path: ~{ensemble_result['confidence_distribution']['high']:.0f}% of cases")
    logger.info(f"   - Average time per case: {ensemble_result['processing_time']/len(test_cases):.2f}s")
    
    # Save final report
    final_report = {
        'test_summary': {
            'total_cases': len(test_cases),
            'test_date': datetime.now().isoformat(),
            'vlm_model': analyzer.model
        },
        'results': results,
        'winner': 'ensemble' if ensemble_result['fp_detection_rate'] > best_single['fp_detection_rate'] else best_single['approach'],
        'recommendation': {
            'approach': 'ensemble',
            'expected_production_performance': {
                'optimistic': ensemble_result['fp_detection_rate'],
                'realistic': ensemble_result['fp_detection_rate'] * 0.85,  # 15% degradation
                'conservative': ensemble_result['fp_detection_rate'] * 0.75  # 25% degradation
            }
        }
    }
    
    with open('FINAL_ENSEMBLE_TEST_RESULTS.json', 'w') as f:
        json.dump(final_report, f, indent=2)
    
    logger.info("\n" + "="*70)
    logger.info("TEST COMPLETE - Results saved to FINAL_ENSEMBLE_TEST_RESULTS.json")
    logger.info("="*70)


if __name__ == "__main__":
    asyncio.run(main())