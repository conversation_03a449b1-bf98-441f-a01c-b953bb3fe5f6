#!/usr/bin/env python3
"""
Show the REAL status of Round 3 production test
This is the truth about our performance claims
"""

import json
import os
from datetime import datetime

def show_real_status():
    print("\n" + "="*80)
    print("ROUND 3 PRODUCTION TEST - REAL STATUS UPDATE")
    print("="*80)
    print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Load current progress
    if os.path.exists('round3_production_real_progress.json'):
        with open('round3_production_real_progress.json', 'r') as f:
            progress = json.load(f)
        
        results = progress['results']
        total_tested = len(results)
        
        # Calculate real metrics
        correct = sum(r['correct'] for r in results)
        actual_fps = [r for r in results if r['actual_fp']]
        actual_valid = [r for r in results if not r['actual_fp']]
        
        fp_detected = sum(r['predicted_fp'] for r in actual_fps) if actual_fps else 0
        valid_protected = sum(not r['predicted_fp'] for r in actual_valid) if actual_valid else 0
        
        print(f"\n📊 REAL PERFORMANCE (NOT PROJECTIONS):")
        print(f"├─ Cases Tested: {total_tested}/1250")
        print(f"├─ Overall Accuracy: {correct}/{total_tested} = {correct/total_tested*100:.1f}%")
        print(f"├─ FP Detection: {fp_detected}/{len(actual_fps)} = {fp_detected/len(actual_fps)*100:.1f}%" if actual_fps else "├─ FP Detection: No data")
        print(f"└─ Valid Protection: {valid_protected}/{len(actual_valid)} = {valid_protected/len(actual_valid)*100:.1f}%" if actual_valid else "└─ Valid Protection: No valid cases yet")
        
        print(f"\n🔍 CASE BREAKDOWN:")
        print(f"├─ Total False Positives: {len(actual_fps)}")
        print(f"├─ Total Valid Violations: {len(actual_valid)}")
        print(f"├─ Correctly Identified: {correct}")
        print(f"└─ Incorrectly Identified: {total_tested - correct}")
        
        # Show hallucination patterns
        hallucinations = [r for r in results if not r['correct'] and r['violations'] != 'None']
        if hallucinations:
            print(f"\n⚠️  SYSTEM IS HALLUCINATING VIOLATIONS:")
            print(f"Total phantom violations detected: {len(hallucinations)}")
            
            # Count types
            phantom_types = {}
            for h in hallucinations:
                v = h['violations'].lower()
                if 'helmet' in v:
                    phantom_types['Missing helmet'] = phantom_types.get('Missing helmet', 0) + 1
                if 'vest' in v:
                    phantom_types['Vest issues'] = phantom_types.get('Vest issues', 0) + 1
                if 'phone' in v:
                    phantom_types['Mobile phone'] = phantom_types.get('Mobile phone', 0) + 1
            
            for ptype, count in phantom_types.items():
                print(f"├─ {ptype}: {count} phantom detections")
        
        print(f"\n📈 PROJECTION vs REALITY:")
        print("┌──────────────────┬────────────┬────────────┬────────────┐")
        print("│ Metric           │ Projected  │   Real     │    Gap     │")
        print("├──────────────────┼────────────┼────────────┼────────────┤")
        
        real_accuracy = correct/total_tested*100 if total_tested > 0 else 0
        real_fp = fp_detected/len(actual_fps)*100 if actual_fps else 0
        real_valid = valid_protected/len(actual_valid)*100 if actual_valid else 100
        
        print(f"│ Accuracy         │    91.7%   │   {real_accuracy:5.1f}%    │   {real_accuracy-91.7:+6.1f}%   │")
        print(f"│ FP Detection     │    81.3%   │   {real_fp:5.1f}%    │   {real_fp-81.3:+6.1f}%   │")
        print(f"│ Valid Protection │    99.1%   │   {real_valid:5.1f}%    │   {real_valid-99.1:+6.1f}%   │")
        print("└──────────────────┴────────────┴────────────┴────────────┘")
        
        print(f"\n💡 KEY INSIGHTS:")
        print("1. Small sample projections DO NOT scale to full dataset")
        print("2. System is seeing people and violations that don't exist")
        print("3. The 'optimal' thresholds are causing hallucinations")
        print("4. Real performance is VASTLY different from claims")
        
        print(f"\n🚨 BUSINESS IMPACT:")
        if real_fp < 50:
            print("├─ NO meaningful false positive reduction")
            print("├─ System adds NO value in current state")
            print("└─ Would actually INCREASE workload with phantom violations")
        
        # Estimate completion
        if progress.get('last_index', 0) > 0:
            elapsed = (datetime.now() - datetime.fromisoformat(progress['start_time'])).total_seconds()
            rate = progress['last_index'] / elapsed if elapsed > 0 else 0
            eta = (1250 - progress['last_index']) / rate / 60 if rate > 0 else 0
            print(f"\n⏱️  TEST PROGRESS:")
            print(f"├─ Current Index: {progress['last_index']}/1250")
            print(f"├─ Processing Rate: {rate:.2f} cases/sec")
            print(f"└─ ETA: {eta:.0f} minutes")
    else:
        print("\nNo test data found yet.")
    
    print("\n" + "="*80)
    print("CONCLUSION: The Round 3 'production' configuration is NOT production ready.")
    print("Real testing reveals the critical importance of full dataset validation.")
    print("="*80 + "\n")

if __name__ == "__main__":
    show_real_status()