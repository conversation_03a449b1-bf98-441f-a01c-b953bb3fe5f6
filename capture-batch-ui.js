const puppeteer = require('puppeteer');
const fs = require('fs').promises;
const path = require('path');

async function captureBatchUI() {
    console.log('🚀 Capturing AI-FARM Batch Processing UI');
    
    const screenshotsDir = path.join(__dirname, 'data', 'screenshots', 'batch-ui');
    await fs.mkdir(screenshotsDir, { recursive: true });
    
    const browser = await puppeteer.launch({
        headless: true,
        args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    try {
        const page = await browser.newPage();
        await page.setViewport({ width: 1920, height: 1080 });
        
        // Capture batch processing page
        console.log('📍 Navigating to batch processing page...');
        await page.goto('http://localhost:3000/batch-processing', { waitUntil: 'networkidle2' });
        await page.screenshot({ path: path.join(screenshotsDir, '01-batch-processing-page.png'), fullPage: true });
        
        // Check for job status on the page
        const jobId = 'valo_batch_20250708_162428';
        console.log(`📍 Checking status for job ${jobId}...`);
        
        // Get current status via API
        const status = await page.evaluate(async (jobId) => {
            try {
                const res = await fetch(`http://localhost:8001/api/v1/batch/api/batch/status/${jobId}`);
                return await res.json();
            } catch (error) {
                return { error: error.message };
            }
        }, jobId);
        
        console.log('Current status:', status);
        
        // Capture other relevant pages
        console.log('📍 Capturing results page...');
        await page.goto('http://localhost:3000/results', { waitUntil: 'networkidle2' });
        await page.screenshot({ path: path.join(screenshotsDir, '02-results-page.png'), fullPage: true });
        
        console.log('📍 Capturing upload page...');
        await page.goto('http://localhost:3000/upload', { waitUntil: 'networkidle2' });
        await page.screenshot({ path: path.join(screenshotsDir, '03-upload-page.png'), fullPage: true });
        
        console.log('📍 Capturing insights page...');
        await page.goto('http://localhost:3000/insights', { waitUntil: 'networkidle2' });
        await page.screenshot({ path: path.join(screenshotsDir, '04-insights-page.png'), fullPage: true });
        
        // Create summary
        const summary = {
            timestamp: new Date().toISOString(),
            job_id: jobId,
            status: status,
            screenshots: [
                '01-batch-processing-page.png',
                '02-results-page.png',
                '03-upload-page.png',
                '04-insights-page.png'
            ]
        };
        
        await fs.writeFile(
            path.join(screenshotsDir, 'summary.json'),
            JSON.stringify(summary, null, 2)
        );
        
        console.log('✅ UI capture completed!');
        console.log(`📸 Screenshots saved to: ${screenshotsDir}`);
        
    } catch (error) {
        console.error('❌ Error:', error);
    } finally {
        await browser.close();
    }
}

captureBatchUI().catch(console.error);