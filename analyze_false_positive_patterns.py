#!/usr/bin/env python3
"""
Analyze false positive patterns from Rounds 3-4 to plan Round 6+ strategy
"""

import json
from collections import Counter, defaultdict

print("="*80)
print("ANALYZING FALSE POSITIVE PATTERNS FOR ROUND 6+ STRATEGY")
print("="*80)

# Load data from completed rounds
with open('valo_batch_round3_complete.json', 'r') as f:
    round3_data = json.load(f)

with open('valo_round4_full_complete.json', 'r') as f:
    round4_data = json.load(f)

# Analyze remarks patterns
all_cases = round3_data['results']
fp_cases = [c for c in all_cases if c.get('alert_status') != 'Valid']

print(f"\nTotal FP cases to analyze: {len(fp_cases)}")

# Count remark patterns
remark_counter = Counter()
camera_counter = Counter()
terminal_counter = Counter()
infringement_counter = Counter()

# Detailed pattern analysis
vessel_structure_cases = []
crane_structure_cases = []
equipment_only_cases = []
person_related_cases = []
location_based_cases = []
quality_issues = []

for case in fp_cases:
    remarks = case.get('remarks', '').upper()
    camera = case.get('camera_id', '')
    terminal = case.get('terminal', '')
    infringement = case.get('infringement_type', '')
    
    # Count basic attributes
    remark_counter[remarks] += 1
    camera_counter[camera] += 1
    terminal_counter[terminal] += 1
    infringement_counter[infringement] += 1
    
    # Categorize by pattern type
    if 'VESSEL STRUCTURE' in remarks:
        vessel_structure_cases.append(case)
    elif 'CRANE STRUCTURE' in remarks:
        crane_structure_cases.append(case)
    elif any(x in remarks for x in ['CHERRY PICKER', 'FORKLIFT', 'VEHICLE', 'TRUCK', 'EQUIPMENT']):
        equipment_only_cases.append(case)
    elif any(x in remarks for x in ['PERSON', 'PEOPLE', 'WORKER', 'STAFF']):
        person_related_cases.append(case)
    elif any(x in remarks for x in ['BACKGROUND', 'FAR', 'DISTANCE']):
        location_based_cases.append(case)
    elif any(x in remarks for x in ['BLUR', 'DARK', 'POOR', 'QUALITY']):
        quality_issues.append(case)

print("\n\nTOP 20 FALSE POSITIVE REMARKS:")
print("-"*80)
for remark, count in remark_counter.most_common(20):
    percentage = (count / len(fp_cases)) * 100
    print(f"{count:4d} ({percentage:5.1f}%): {remark}")

print("\n\nFALSE POSITIVE CATEGORIES:")
print("-"*80)
print(f"Vessel Structure: {len(vessel_structure_cases)} ({len(vessel_structure_cases)/len(fp_cases)*100:.1f}%)")
print(f"Crane Structure: {len(crane_structure_cases)} ({len(crane_structure_cases)/len(fp_cases)*100:.1f}%)")
print(f"Equipment Only: {len(equipment_only_cases)} ({len(equipment_only_cases)/len(fp_cases)*100:.1f}%)")
print(f"Person Related: {len(person_related_cases)} ({len(person_related_cases)/len(fp_cases)*100:.1f}%)")
print(f"Location Based: {len(location_based_cases)} ({len(location_based_cases)/len(fp_cases)*100:.1f}%)")
print(f"Quality Issues: {len(quality_issues)} ({len(quality_issues)/len(fp_cases)*100:.1f}%)")

print("\n\nTOP 10 CAMERAS BY FALSE POSITIVES:")
print("-"*80)
for camera, count in camera_counter.most_common(10):
    percentage = (count / len(fp_cases)) * 100
    print(f"{count:4d} ({percentage:5.1f}%): {camera}")

print("\n\nFALSE POSITIVES BY TERMINAL:")
print("-"*80)
for terminal, count in terminal_counter.most_common():
    percentage = (count / len(fp_cases)) * 100
    print(f"{count:4d} ({percentage:5.1f}%): Terminal {terminal}")

# Analyze Round 4 performance on different categories
print("\n\nROUND 4 PERFORMANCE BY CATEGORY:")
print("-"*80)

# Map Round 4 results by case number
round4_results = {r['case_number']: r for r in round4_data['results']}

def analyze_round4_performance(cases, category_name):
    total = len(cases)
    if total == 0:
        return
    
    dismissed = 0
    for case in cases:
        case_num = case['case_number']
        if case_num in round4_results:
            if round4_results[case_num].get('round4_decision') == 'dismissed':
                dismissed += 1
    
    dismiss_rate = (dismissed / total) * 100 if total > 0 else 0
    print(f"{category_name}: {dismissed}/{total} dismissed ({dismiss_rate:.1f}%)")

analyze_round4_performance(vessel_structure_cases, "Vessel Structure")
analyze_round4_performance(crane_structure_cases, "Crane Structure")
analyze_round4_performance(equipment_only_cases, "Equipment Only")
analyze_round4_performance(person_related_cases, "Person Related")
analyze_round4_performance(location_based_cases, "Location Based")
analyze_round4_performance(quality_issues, "Quality Issues")

# Find patterns that Round 4 missed
print("\n\nPATTERNS WITH LOW DISMISSAL RATES IN ROUND 4:")
print("-"*80)

pattern_performance = defaultdict(lambda: {'total': 0, 'dismissed': 0})

for case in fp_cases:
    case_num = case['case_number']
    remarks = case.get('remarks', '')
    
    if case_num in round4_results:
        was_dismissed = round4_results[case_num].get('round4_decision') == 'dismissed'
        pattern_performance[remarks]['total'] += 1
        if was_dismissed:
            pattern_performance[remarks]['dismissed'] += 1

# Find patterns with low dismissal rates
low_performing_patterns = []
for pattern, stats in pattern_performance.items():
    if stats['total'] >= 5:  # At least 5 occurrences
        dismiss_rate = (stats['dismissed'] / stats['total']) * 100
        if dismiss_rate < 30:  # Less than 30% dismissed
            low_performing_patterns.append((pattern, stats['total'], dismiss_rate))

low_performing_patterns.sort(key=lambda x: x[1], reverse=True)  # Sort by frequency

for pattern, total, dismiss_rate in low_performing_patterns[:15]:
    print(f"{total:3d} cases, {dismiss_rate:4.1f}% dismissed: {pattern}")

print("\n\nSTRATEGIC RECOMMENDATIONS FOR ROUND 6+:")
print("="*80)
print("1. CAMERA-SPECIFIC MODELS:")
print("   - Create specialized prompts for high-FP cameras")
print("   - Cameras like QC313F, QC316F need aggressive dismissal")
print("\n2. PATTERN-SPECIFIC RULES:")
print("   - 'VESSEL STRUCTURE CAPTURED AS LS': Auto-dismiss if no person visible")
print("   - 'CRANE STRUCTURE': Auto-dismiss if only mechanical parts")
print("   - Equipment-only scenes: More aggressive dismissal")
print("\n3. MULTI-FACTOR SCORING:")
print("   - Combine: Camera + Terminal + Remark + Visual Analysis")
print("   - Create confidence thresholds per pattern type")
print("\n4. PERSON DETECTION FOCUS:")
print("   - Primary question: 'Is there a human in this image?'")
print("   - Secondary: 'Is the human clearly visible and identifiable?'")
print("   - If no clear human → dismiss (except Valid cases)")
print("\n5. QUALITY-BASED FILTERING:")
print("   - Dark/blurry images with no clear person → dismiss")
print("   - Far distance shots with no identifiable person → dismiss")