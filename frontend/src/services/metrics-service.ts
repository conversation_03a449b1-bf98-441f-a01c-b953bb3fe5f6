import { apiClient } from './api-client';
import {
  DemoMetrics,
  ROIProjection,
  AutoLearningInsights,
  PerformanceComparison,
  DashboardData,
  HistoricalPerformance,
} from '../types';

export class MetricsService {
  private readonly BASE_PATH = '/api/v1/metrics';

  /**
   * Get demo-ready metrics for a specific batch
   */
  async getDemoMetrics(
    batchId: string,
    hourlyRate: number = 50.0
  ): Promise<DemoMetrics> {
    const params = new URLSearchParams();
    params.append('hourly_rate', String(hourlyRate));
    
    return apiClient.get<DemoMetrics>(`${this.BASE_PATH}/demo/${batchId}?${params.toString()}`);
  }

  /**
   * Calculate ROI projections based on demo results and customer parameters
   */
  async calculateROI(
    batchId: string,
    options: {
      monthlyAlerts?: number;
      hourlyRate?: number;
      implementationCost?: number;
    } = {}
  ): Promise<ROIProjection> {
    const params = new URLSearchParams();
    if (options.monthlyAlerts) params.append('monthly_alerts', String(options.monthlyAlerts));
    if (options.hourlyRate) params.append('hourly_rate', String(options.hourlyRate));
    if (options.implementationCost) params.append('implementation_cost', String(options.implementationCost));
    
    return apiClient.get<ROIProjection>(`${this.BASE_PATH}/roi/${batchId}?${params.toString()}`);
  }

  /**
   * Get auto-learning insights for a specific batch
   */
  async getAutoLearningInsights(batchId: string): Promise<AutoLearningInsights> {
    return apiClient.get<AutoLearningInsights>(`${this.BASE_PATH}/auto-learning/${batchId}`);
  }

  /**
   * Compare AI-FARM performance against baseline system
   */
  async getPerformanceComparison(
    batchId: string,
    baselineFilterRate: number = 3.0
  ): Promise<PerformanceComparison> {
    const params = new URLSearchParams();
    params.append('baseline_filter_rate', String(baselineFilterRate));
    
    return apiClient.get<PerformanceComparison>(`${this.BASE_PATH}/performance-comparison/${batchId}?${params.toString()}`);
  }

  /**
   * Get comprehensive dashboard data for customer demo presentation
   */
  async getDashboardData(
    batchId: string,
    customerName?: string
  ): Promise<DashboardData> {
    const params = new URLSearchParams();
    if (customerName) params.append('customer_name', customerName);
    
    return apiClient.get<DashboardData>(`${this.BASE_PATH}/dashboard/${batchId}?${params.toString()}`);
  }

  /**
   * Export demo results in various formats
   */
  async exportDemoResults(
    batchId: string,
    options: {
      format?: 'json' | 'csv';
      includeDetails?: boolean;
    } = {}
  ): Promise<any> {
    const params = new URLSearchParams();
    if (options.format) params.append('format', options.format);
    if (options.includeDetails !== undefined) params.append('include_details', String(options.includeDetails));
    
    return apiClient.get(`${this.BASE_PATH}/export/${batchId}?${params.toString()}`);
  }

  /**
   * Get historical performance metrics across all batches
   */
  async getHistoricalPerformance(days: number = 30): Promise<HistoricalPerformance> {
    const params = new URLSearchParams();
    params.append('days', String(days));
    
    return apiClient.get<HistoricalPerformance>(`${this.BASE_PATH}/historical?${params.toString()}`);
  }

  /**
   * Calculate cost savings based on filter rate and customer parameters
   */
  calculateCostSavings(options: {
    monthlyAlerts: number;
    filterRate: number; // percentage
    hourlyRate: number;
    reviewTimePerAlert: number; // minutes
  }): {
    monthlyFilteredAlerts: number;
    monthlySavedHours: number;
    monthlyCostSavings: number;
    annualCostSavings: number;
  } {
    const monthlyFilteredAlerts = Math.round(options.monthlyAlerts * (options.filterRate / 100));
    const monthlySavedMinutes = monthlyFilteredAlerts * options.reviewTimePerAlert;
    const monthlySavedHours = monthlySavedMinutes / 60;
    const monthlyCostSavings = monthlySavedHours * options.hourlyRate;
    const annualCostSavings = monthlyCostSavings * 12;

    return {
      monthlyFilteredAlerts,
      monthlySavedHours,
      monthlyCostSavings,
      annualCostSavings,
    };
  }

  /**
   * Calculate ROI metrics
   */
  calculateROIMetrics(options: {
    annualSavings: number;
    implementationCost: number;
    annualMaintenanceCost?: number;
  }): {
    netAnnualBenefit: number;
    roiPercentage: number;
    paybackMonths: number;
    breakEvenPoint: string;
  } {
    const annualMaintenanceCost = options.annualMaintenanceCost || (options.implementationCost * 0.1);
    const netAnnualBenefit = options.annualSavings - annualMaintenanceCost;
    const roiPercentage = (netAnnualBenefit / options.implementationCost) * 100;
    const monthlyNetBenefit = netAnnualBenefit / 12;
    const paybackMonths = monthlyNetBenefit > 0 ? options.implementationCost / monthlyNetBenefit : Infinity;
    
    let breakEvenPoint: string;
    if (paybackMonths === Infinity) {
      breakEvenPoint = 'Never';
    } else if (paybackMonths <= 12) {
      breakEvenPoint = 'Year 1';
    } else {
      breakEvenPoint = `${Math.ceil(paybackMonths / 12)} years`;
    }

    return {
      netAnnualBenefit,
      roiPercentage,
      paybackMonths: Math.min(paybackMonths, 999), // Cap for display
      breakEvenPoint,
    };
  }

  /**
   * Format metrics for display
   */
  formatMetrics(metrics: DemoMetrics): {
    formattedMetrics: Record<string, string>;
    summaryText: string;
  } {
    const formattedMetrics = {
      totalAlertsProcessed: metrics.total_alerts_processed.toLocaleString(),
      falsePositivesFiltered: metrics.false_positives_filtered.toLocaleString(),
      alertsRequiringReview: metrics.alerts_requiring_review.toLocaleString(),
      filterRatePercentage: `${metrics.filter_rate_percentage.toFixed(1)}%`,
      timeSavedHours: `${metrics.time_saved_hours.toFixed(1)} hours`,
      costSavingsAnnual: `$${metrics.cost_savings_annual.toLocaleString('en-US', { maximumFractionDigits: 0 })}`,
      processingTimeAvg: `${metrics.processing_time_avg_ms.toFixed(0)}ms`,
      confidenceScoreAvg: `${(metrics.confidence_score_avg * 100).toFixed(1)}%`,
    };

    const summaryText = `AI-FARM successfully processed ${formattedMetrics.totalAlertsProcessed} alerts, ` +
      `filtering out ${formattedMetrics.falsePositivesFiltered} false positives (${formattedMetrics.filterRatePercentage}) ` +
      `and potentially saving ${formattedMetrics.costSavingsAnnual} annually.`;

    return {
      formattedMetrics,
      summaryText,
    };
  }

  /**
   * Generate presentation-ready summary
   */
  generatePresentationSummary(dashboardData: DashboardData): {
    keyHighlights: string[];
    businessValue: string[];
    technicalPerformance: string[];
    nextSteps: string[];
  } {
    const keyHighlights = dashboardData.headline_metrics ? [
      `${dashboardData.headline_metrics.false_positive_reduction} reduction in false positive alerts`,
      `${dashboardData.headline_metrics.alerts_filtered} alerts automatically filtered`,
      `${dashboardData.headline_metrics.annual_savings} in projected annual savings`,
      `${dashboardData.headline_metrics.roi_percentage} return on investment`,
    ] : [
      'Advanced AI-powered false positive reduction',
      'Automated alert filtering capabilities',
      'Significant cost savings potential',
      'Strong return on investment expected',
    ];

    const businessValue = dashboardData.business_impact && dashboardData.headline_metrics ? [
      `${dashboardData.business_impact.workload_reduction_percentage.toFixed(1)}% reduction in manual review workload`,
      `${dashboardData.headline_metrics.payback_months} month payback period`,
      `$${dashboardData.business_impact.monthly_cost_savings.toLocaleString()} in monthly cost savings`,
      `Break-even point: ${dashboardData.business_impact.break_even_point}`,
    ] : [
      'Significant reduction in manual review workload',
      'Fast payback period expected',
      'Substantial monthly cost savings',
      'Quick break-even point anticipated',
    ];

    const technicalPerformance = dashboardData.processing_performance && dashboardData.comparison_vs_baseline ? [
      `${dashboardData.processing_performance.total_alerts_processed} alerts processed successfully`,
      `${dashboardData.processing_performance.processing_time_avg_ms.toFixed(0)}ms average processing time`,
      `${(dashboardData.processing_performance.confidence_score_avg * 100).toFixed(1)}% average confidence score`,
      `${dashboardData.comparison_vs_baseline.improvement_factor.toFixed(1)}x improvement over baseline`,
    ] : [
      'High-volume alert processing capability',
      'Fast processing times achieved',
      'High confidence score accuracy',
      'Significant improvement over baseline',
    ];

    const nextSteps = dashboardData.next_steps || [
      'Schedule implementation planning session',
      'Conduct pilot deployment',
      'Train operations team',
      'Begin full production rollout',
    ];

    return {
      keyHighlights,
      businessValue,
      technicalPerformance,
      nextSteps,
    };
  }
}

export const metricsService = new MetricsService();