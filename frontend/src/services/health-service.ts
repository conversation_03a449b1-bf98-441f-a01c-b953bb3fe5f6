import { apiClient } from './api-client';
import { HealthCheckResponse } from '../types';

export class HealthService {
  private readonly BASE_PATH = '/api/v1/health';

  /**
   * Check system health status
   */
  async checkHealth(): Promise<HealthCheckResponse> {
    return apiClient.get<HealthCheckResponse>('/health');
  }

  /**
   * Get detailed system health information
   */
  async getDetailedHealth(): Promise<HealthCheckResponse> {
    return apiClient.get<HealthCheckResponse>(`${this.BASE_PATH}/detailed`);
  }

  /**
   * Check if the system is ready to accept requests
   */
  async checkReadiness(): Promise<{ ready: boolean; services: Record<string, boolean> }> {
    try {
      const health = await this.checkHealth();
      const ready = health.status === 'healthy' && 
                   health.database_status === 'connected' &&
                   health.vlm_api_status !== 'error';
      
      return {
        ready,
        services: {
          database: health.database_status === 'connected',
          vlm_api: health.vlm_api_status === 'connected',
          application: health.status === 'healthy',
        },
      };
    } catch (error) {
      return {
        ready: false,
        services: {
          database: false,
          vlm_api: false,
          application: false,
        },
      };
    }
  }

  /**
   * Monitor system health with periodic checks
   */
  async monitorHealth(
    onHealthUpdate: (health: HealthCheckResponse) => void,
    onError: (error: Error) => void,
    interval: number = 30000 // 30 seconds
  ): Promise<() => void> {
    let isMonitoring = true;
    
    const checkHealth = async () => {
      if (!isMonitoring) return;
      
      try {
        const health = await this.checkHealth();
        onHealthUpdate(health);
      } catch (error) {
        onError(error as Error);
      }
      
      if (isMonitoring) {
        setTimeout(checkHealth, interval);
      }
    };
    
    // Start monitoring
    checkHealth();
    
    // Return stop function
    return () => {
      isMonitoring = false;
    };
  }

  /**
   * Get system status summary
   */
  async getSystemStatus(): Promise<{
    status: 'healthy' | 'degraded' | 'unhealthy';
    message: string;
    uptime: string;
    services: {
      name: string;
      status: 'healthy' | 'unhealthy';
      message: string;
    }[];
  }> {
    try {
      const health = await this.checkHealth();
      
      const services = [
        {
          name: 'Database',
          status: health.database_status === 'connected' ? 'healthy' as const : 'unhealthy' as const,
          message: health.database_status === 'connected' ? 'Connected' : 'Connection failed',
        },
        {
          name: 'VLM API',
          status: health.vlm_api_status === 'connected' ? 'healthy' as const : 'unhealthy' as const,
          message: health.vlm_api_status === 'connected' ? 'Connected' : 
                  health.vlm_api_status === 'error' ? 'Connection error' : 'Disconnected',
        },
      ];
      
      const unhealthyServices = services.filter(s => s.status === 'unhealthy');
      let overallStatus: 'healthy' | 'degraded' | 'unhealthy';
      let message: string;
      
      if (health.status === 'unhealthy') {
        overallStatus = 'unhealthy';
        message = 'System is experiencing issues';
      } else if (unhealthyServices.length > 0) {
        overallStatus = 'degraded';
        message = `${unhealthyServices.length} service(s) are experiencing issues`;
      } else {
        overallStatus = 'healthy';
        message = 'All systems operational';
      }
      
      const uptime = this.formatUptime(health.uptime_seconds);
      
      return {
        status: overallStatus,
        message,
        uptime,
        services,
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        message: 'Unable to connect to system',
        uptime: 'Unknown',
        services: [
          {
            name: 'System',
            status: 'unhealthy',
            message: 'Connection failed',
          },
        ],
      };
    }
  }

  /**
   * Format uptime seconds into human-readable string
   */
  private formatUptime(seconds: number): string {
    if (seconds < 60) {
      return `${seconds} seconds`;
    } else if (seconds < 3600) {
      const minutes = Math.floor(seconds / 60);
      return `${minutes} minute${minutes !== 1 ? 's' : ''}`;
    } else if (seconds < 86400) {
      const hours = Math.floor(seconds / 3600);
      const minutes = Math.floor((seconds % 3600) / 60);
      return `${hours} hour${hours !== 1 ? 's' : ''} ${minutes} minute${minutes !== 1 ? 's' : ''}`;
    } else {
      const days = Math.floor(seconds / 86400);
      const hours = Math.floor((seconds % 86400) / 3600);
      return `${days} day${days !== 1 ? 's' : ''} ${hours} hour${hours !== 1 ? 's' : ''}`;
    }
  }

  /**
   * Test system connectivity
   */
  async testConnectivity(): Promise<{
    api: boolean;
    database: boolean;
    vlm: boolean;
    responseTime: number;
  }> {
    const startTime = Date.now();
    
    try {
      const health = await this.checkHealth();
      const responseTime = Date.now() - startTime;
      
      return {
        api: true,
        database: health.database_status === 'connected',
        vlm: health.vlm_api_status === 'connected',
        responseTime,
      };
    } catch (error) {
      const responseTime = Date.now() - startTime;
      
      return {
        api: false,
        database: false,
        vlm: false,
        responseTime,
      };
    }
  }
}

export const healthService = new HealthService();