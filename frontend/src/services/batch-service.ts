import { apiClient } from './api-client';

export interface BatchProcessingRequest {
  num_rounds?: number;
  chunk_size?: number;
  test_mode?: boolean;
}

export interface BatchProcessingResponse {
  status: string;
  message: string;
  job_id: string;
}

export interface ProcessingStatus {
  status: string;
  round?: number;
  chunk?: number;
  total_chunks?: number;
  cases_processed?: number;
  total_cases?: number;
  progress_percentage?: number;
  current_stats?: {
    round: number;
    valid_protection_rate: number;
    fp_detection_rate: number;
  };
}

export interface BatchResults {
  processing_summary: {
    total_cases: number;
    total_rounds: number;
    total_valid_cases: number;
    valid_cases_protected: number;
    valid_protection_rate: number;
    total_false_positives: number;
    false_positives_detected: number;
    fp_detection_rate: number;
    processing_errors: number;
  };
  business_impact: {
    annual_false_positives: number;
    annual_fp_filtered: number;
    annual_time_saved_minutes: number;
    annual_cost_savings: number;
    valid_cases_at_risk: number;
  };
  round_improvements: Array<{
    round: number;
    total_processed: number;
    total_valid: number;
    valid_protected: number;
    valid_protection_rate: number;
    total_fp: number;
    fp_detected: number;
    fp_detection_rate: number;
  }>;
  learning_parameters: {
    confidence_thresholds: Record<string, number>;
    person_detection_weight: number;
    pattern_weights: Record<string, number>;
    camera_accuracy: Record<string, any>;
    round_improvements: Array<any>;
  };
}

export interface DetailedResult {
  case_number: string;
  terminal: string;
  camera_id: string;
  infringement_type: string;
  alert_status: string;
  is_false_positive: boolean;
  vlm_decision: string;
  person_detected: string;
  confidence: number;
  is_false_positive_predicted: boolean;
  correct_prediction: boolean;
  valid_case_protected?: boolean;
  reasoning: string;
  timestamp: string;
}

class BatchService {
  private baseUrl = '/api/batch';

  async startBatchProcessing(request: BatchProcessingRequest): Promise<BatchProcessingResponse> {
    try {
      const response = await apiClient.post<BatchProcessingResponse>(
        `${this.baseUrl}/start`,
        request
      );
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.detail || 'Failed to start batch processing');
    }
  }

  async getProcessingStatus(jobId: string): Promise<ProcessingStatus> {
    try {
      const response = await apiClient.get<ProcessingStatus>(
        `${this.baseUrl}/status/${jobId}`
      );
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.detail || 'Failed to get processing status');
    }
  }

  async getLatestResults(): Promise<BatchResults> {
    try {
      const response = await apiClient.get<BatchResults>(
        `${this.baseUrl}/results/latest`
      );
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.detail || 'Failed to get results');
    }
  }

  async getDetailedResults(params?: {
    limit?: number;
    offset?: number;
    filter_valid_only?: boolean;
    filter_errors_only?: boolean;
  }): Promise<{ total: number; results: DetailedResult[] }> {
    try {
      const response = await apiClient.get(
        `${this.baseUrl}/results/details`,
        { params }
      );
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.detail || 'Failed to get detailed results');
    }
  }

  async cancelJob(jobId: string): Promise<{ message: string }> {
    try {
      const response = await apiClient.delete(`${this.baseUrl}/jobs/${jobId}`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.detail || 'Failed to cancel job');
    }
  }

  async getProcessingSummary(): Promise<{
    completed_jobs: number;
    latest_results: {
      valid_protection_rate: number;
      fp_detection_rate: number;
      annual_cost_savings: number;
    };
    jobs: Array<{
      job_id: string;
      completed_at: string;
      num_rounds: number;
    }>;
  }> {
    try {
      const response = await apiClient.get(`${this.baseUrl}/stats/summary`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.detail || 'Failed to get processing summary');
    }
  }
}

export const batchService = new BatchService();