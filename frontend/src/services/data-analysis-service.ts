/**
 * Data Analysis Service
 * Service for interacting with VALO AI-FARM data analysis APIs
 */

import { apiClient } from './api-client';

export interface ViolationCase {
  s_no: number;
  alert_id: string;
  case_int_id: string;
  camera: string;
  terminal: string;
  alert_status: string;
  acknowledged_by: string;
  alert_start_time: string;
  alert_acknowledged_timestamp: string;
  reviewed_by: string;
  alert_reviewed_timestamp: string;
  edited_by: string;
  alert_edited_timestamp: string;
  type_of_infringement: string;
  follow_up: string;
  remarks: string;
  has_images: boolean;
  source_image_path?: string;
  cropped_image_path?: string;
  image_classification?: 'valid' | 'invalid';
}

export interface AnalysisMetrics {
  total_csv_cases: number;
  total_image_sets: number;
  valid_detections: number;
  invalid_detections: number;
  false_positive_rate: number;
  filtering_effectiveness: number;
  cases_with_images: number;
  cases_without_images: number;
  analysis_timestamp: string;
}

export interface CameraAnalysis {
  [camera: string]: {
    total_cases: number;
    with_images: number;
    valid_detections: number;
    invalid_detections: number;
    accuracy_rate: number;
  };
}

export interface TemporalAnalysis {
  hourly_stats: {
    [hour: string]: {
      total: number;
      valid: number;
      invalid: number;
    };
  };
  daily_stats: {
    [date: string]: {
      total: number;
      valid: number;
      invalid: number;
    };
  };
  anomalous_hours: {
    [hour: string]: {
      fp_rate: number;
      total_alerts: number;
    };
  };
  peak_hours: Array<[string, { total: number; valid: number; invalid: number }]>;
}

export interface InfringementAnalysis {
  [infringement: string]: {
    total_cases: number;
    with_images: number;
    valid_detections: number;
    invalid_detections: number;
    accuracy_rate?: number;
    camera_diversity?: number;
    cameras?: string[];
    follow_ups?: { [followUp: string]: number };
  };
}

export interface FalsePositivePatterns {
  patterns: {
    camera_reliability: {
      camera_stats: CameraAnalysis;
      problematic_cameras: CameraAnalysis;
      total_cameras: number;
      avg_accuracy: number;
    };
    temporal_anomalies: TemporalAnalysis;
    infringement_consistency: InfringementAnalysis;
    follow_up_patterns: {
      [followUp: string]: {
        total: number;
        valid: number;
        invalid: number;
        accuracy_rate: number;
        cameras: { [camera: string]: number };
        infringements: { [infringement: string]: number };
      };
    };
    alert_duration_analysis: {
      quick_dismissals: number;
      normal_processing: number;
      delayed_processing: number;
      avg_processing_time: number;
    };
    clustering_analysis: {
      clusters: {
        [clusterKey: string]: {
          total_cases: number;
          valid_cases: number;
          invalid_cases: number;
          false_positive_rate: number;
          pattern_strength: 'high' | 'medium' | 'low';
        };
      };
      total_clusters: number;
      high_fp_clusters: number;
    };
  };
  recommendations: Array<{
    type: string;
    priority: 'high' | 'medium' | 'low';
    title: string;
    description: string;
    action: string;
    affected_cameras?: string[];
    affected_hours?: string[];
    impact?: string;
  }>;
  confidence_scores: {
    camera_analysis: number;
    temporal_analysis: number;
    overall: number;
  };
}

export interface CasesResponse {
  success: boolean;
  data: {
    cases: ViolationCase[];
    pagination: {
      page: number;
      per_page: number;
      total_cases: number;
      total_pages: number;
    };
    filters_applied: Record<string, any>;
  };
}

export interface AnalysisResponse {
  success: boolean;
  data: {
    metrics: AnalysisMetrics;
    follow_up_analysis: Record<string, any>;
    camera_analysis: CameraAnalysis;
    temporal_analysis: TemporalAnalysis;
    infringement_analysis: InfringementAnalysis;
  };
}

class DataAnalysisService {
  private baseUrl = '/api/v1/data-analysis';

  /**
   * Get health status of data analysis service
   */
  async getHealth(): Promise<{ status: string; service: string; timestamp?: string }> {
    return await apiClient.get<{ status: string; service: string; timestamp?: string }>(`${this.baseUrl}/health`);
  }

  /**
   * Run comprehensive data analysis
   */
  async runAnalysis(): Promise<AnalysisResponse> {
    return await apiClient.post<AnalysisResponse>(`${this.baseUrl}/analyze`);
  }

  /**
   * Get analysis summary and metrics
   */
  async getSummary(): Promise<{ success: boolean; data: any }> {
    return await apiClient.get<{ success: boolean; data: any }>(`${this.baseUrl}/summary`);
  }

  /**
   * Get key analysis metrics
   */
  async getMetrics(): Promise<{ success: boolean; data: AnalysisMetrics }> {
    return await apiClient.get<{ success: boolean; data: AnalysisMetrics }>(`${this.baseUrl}/metrics`);
  }

  /**
   * Get filtered violation cases
   */
  async getCases(filters?: {
    has_images?: boolean;
    classification?: 'valid' | 'invalid';
    follow_up?: string;
    camera?: string;
    page?: number;
    per_page?: number;
  }): Promise<CasesResponse> {
    const params = new URLSearchParams();
    
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          params.append(key, value.toString());
        }
      });
    }

    return await apiClient.get<CasesResponse>(`${this.baseUrl}/cases?${params.toString()}`);
  }

  /**
   * Get false positive cases
   */
  async getFalsePositives(): Promise<{
    success: boolean;
    data: {
      false_positive_cases: ViolationCase[];
      total_false_positives: number;
      analysis: AnalysisMetrics;
      camera_breakdown: CameraAnalysis;
      infringement_breakdown: InfringementAnalysis;
    };
  }> {
    return await apiClient.get<{
      success: boolean;
      data: {
        false_positive_cases: ViolationCase[];
        total_false_positives: number;
        analysis: AnalysisMetrics;
        camera_breakdown: CameraAnalysis;
        infringement_breakdown: InfringementAnalysis;
      };
    }>(`${this.baseUrl}/false-positives`);
  }

  /**
   * Get valid detection cases
   */
  async getValidDetections(): Promise<{
    success: boolean;
    data: {
      valid_cases: ViolationCase[];
      total_valid_detections: number;
    };
  }> {
    return await apiClient.get<{
      success: boolean;
      data: {
        valid_cases: ViolationCase[];
        total_valid_detections: number;
      };
    }>(`${this.baseUrl}/valid-detections`);
  }

  /**
   * Get camera analysis breakdown
   */
  async getCameraAnalysis(): Promise<{ success: boolean; data: CameraAnalysis }> {
    return await apiClient.get<{ success: boolean; data: CameraAnalysis }>(`${this.baseUrl}/camera-analysis`);
  }

  /**
   * Get temporal analysis
   */
  async getTemporalAnalysis(): Promise<{ success: boolean; data: TemporalAnalysis }> {
    return await apiClient.get<{ success: boolean; data: TemporalAnalysis }>(`${this.baseUrl}/temporal-analysis`);
  }

  /**
   * Get infringement analysis breakdown
   */
  async getInfringementAnalysis(): Promise<{ success: boolean; data: InfringementAnalysis }> {
    return await apiClient.get<{ success: boolean; data: InfringementAnalysis }>(`${this.baseUrl}/infringement-analysis`);
  }

  /**
   * Get advanced false positive patterns
   */
  async getFalsePositivePatterns(): Promise<{ success: boolean; data: FalsePositivePatterns }> {
    return await apiClient.get<{ success: boolean; data: FalsePositivePatterns }>(`${this.baseUrl}/false-positive-patterns`);
  }

  /**
   * Get AI-driven recommendations
   */
  async getRecommendations(): Promise<{
    success: boolean;
    data: {
      recommendations: FalsePositivePatterns['recommendations'];
      confidence_scores: FalsePositivePatterns['confidence_scores'];
      total_recommendations: number;
      high_priority_count: number;
    };
  }> {
    return await apiClient.get<{
      success: boolean;
      data: {
        recommendations: FalsePositivePatterns['recommendations'];
        confidence_scores: FalsePositivePatterns['confidence_scores'];
        total_recommendations: number;
        high_priority_count: number;
      };
    }>(`${this.baseUrl}/recommendations`);
  }

  /**
   * Reload data from CSV and images
   */
  async reloadData(): Promise<{
    success: boolean;
    message: string;
    data: {
      total_cases_loaded: number;
      cases_with_images: number;
      analysis_metrics: AnalysisMetrics;
    };
  }> {
    return await apiClient.post<{
      success: boolean;
      message: string;
      data: {
        total_cases_loaded: number;
        cases_with_images: number;
        analysis_metrics: AnalysisMetrics;
      };
    }>(`${this.baseUrl}/reload-data`);
  }

  /**
   * Export analysis results
   */
  async exportAnalysis(format: 'json' | 'csv' = 'json'): Promise<{
    success: boolean;
    data: any;
    export_timestamp?: string;
  }> {
    return await apiClient.get<{
      success: boolean;
      data: any;
      export_timestamp?: string;
    }>(`${this.baseUrl}/export?format=${format}`);
  }
}

export const dataAnalysisService = new DataAnalysisService();
