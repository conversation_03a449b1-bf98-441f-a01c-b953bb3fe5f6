/**
 * Insights Service
 * Handles API calls for batch processing insights and analytics
 */

import { apiClient } from './api-client';

export interface InsightsAnalysisMetrics {
  total_csv_cases: number;
  total_image_sets: number;
  valid_detections: number;
  invalid_detections: number;
  false_positive_rate: number;
  filtering_effectiveness: number;
  cases_with_images: number;
  cases_without_images: number;
  analysis_timestamp: string;
}

export interface InsightsFalsePositivePattern {
  pattern_type: string;
  occurrence_count: number;
  confidence_level: number;
  examples: string[];
  filtering_success_rate: number;
}

export interface InsightsImageSetAnalysis {
  case_number: string;
  has_source_image: boolean;
  has_cropped_image: boolean;
  vlm_analysis_result?: {
    detection_type: string;
    false_positive_likelihood: number;
    reasoning: string;
    recommendation: string;
  };
  processing_status: 'pending' | 'completed' | 'error';
}

export interface InsightsProcessingSummary {
  total_cases: number;
  completed_cases: number;
  failed_cases: number;
  success_rate: number;
  average_processing_time_ms: number;
  started_at: string | null;
  completed_at: string | null;
}

export interface InsightsDataSourceInfo {
  batch_id: string;
  priority: string;
  use_auto_learning: boolean;
  custom_thresholds: any;
}

export interface BatchInsightsResponse {
  batch_id: string;
  analysis_metrics: InsightsAnalysisMetrics;
  false_positive_patterns: InsightsFalsePositivePattern[];
  image_set_analysis: InsightsImageSetAnalysis[];
  processing_summary: InsightsProcessingSummary;
  data_source_info: InsightsDataSourceInfo;
}

export interface RecentInsightsSummary {
  batch_id: string;
  completed_at: string | null;
  total_cases: number;
  false_positives_detected: number;
  false_positive_rate: number;
  processing_time_minutes: number;
}

export interface RecentInsightsResponse {
  recent_insights: RecentInsightsSummary[];
  summary: {
    total_batches: number;
    total_cases_processed: number;
    average_false_positive_rate: number;
  };
}

class InsightsService {
  private baseUrl = '/api/v1/insights';

  /**
   * Get comprehensive insights for a specific batch
   */
  async getBatchInsights(batchId: string): Promise<BatchInsightsResponse> {
    try {
      const response = await apiClient.get<BatchInsightsResponse>(`${this.baseUrl}/batch/${batchId}`);
      return response.data;
    } catch (error) {
      console.error('Failed to fetch batch insights:', error);
      throw new Error('Failed to fetch batch insights');
    }
  }

  /**
   * Get insights from recent batch processing jobs
   */
  async getRecentInsights(limit: number = 5): Promise<RecentInsightsResponse> {
    try {
      const response = await apiClient.get<RecentInsightsResponse>(`${this.baseUrl}/recent`, {
        params: { limit }
      });
      return response.data;
    } catch (error) {
      console.error('Failed to fetch recent insights:', error);
      throw new Error('Failed to fetch recent insights');
    }
  }

  /**
   * Check if a batch has insights data available
   */
  async hasBatchInsights(batchId: string): Promise<boolean> {
    try {
      await this.getBatchInsights(batchId);
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * Generate demo insights data for sales presentations
   * This creates realistic-looking data for demonstration purposes
   */
  generateDemoInsights(): BatchInsightsResponse {
    const totalCases = 10247;
    const imageSets = 5123;
    const validDetections = 3456;
    const invalidDetections = 1667;
    
    return {
      batch_id: 'demo-batch-' + Date.now(),
      analysis_metrics: {
        total_csv_cases: totalCases,
        total_image_sets: imageSets,
        valid_detections: validDetections,
        invalid_detections: invalidDetections,
        false_positive_rate: (invalidDetections / totalCases) * 100,
        filtering_effectiveness: 87.3,
        cases_with_images: imageSets,
        cases_without_images: totalCases - imageSets,
        analysis_timestamp: new Date().toISOString()
      },
      false_positive_patterns: [
        {
          pattern_type: "Structural Misidentification",
          occurrence_count: 423,
          confidence_level: 89.2,
          examples: [
            "Building structures misidentified as vehicles",
            "Architectural elements flagged as suspicious objects",
            "Construction equipment confused with threats"
          ],
          filtering_success_rate: 91.7
        },
        {
          pattern_type: "Equipment Shadow Artifacts",
          occurrence_count: 387,
          confidence_level: 85.6,
          examples: [
            "Camera equipment shadows creating false alerts",
            "Lighting equipment casting suspicious shadows",
            "Infrastructure shadows misinterpreted as objects"
          ],
          filtering_success_rate: 88.4
        },
        {
          pattern_type: "Environmental Conditions",
          occurrence_count: 298,
          confidence_level: 82.1,
          examples: [
            "Weather conditions affecting detection accuracy",
            "Lighting variations causing false positives",
            "Seasonal changes impacting recognition"
          ],
          filtering_success_rate: 85.9
        },
        {
          pattern_type: "Motion Blur Artifacts",
          occurrence_count: 234,
          confidence_level: 78.9,
          examples: [
            "Fast-moving objects creating blur artifacts",
            "Camera shake affecting image clarity",
            "Motion-induced distortions"
          ],
          filtering_success_rate: 83.2
        },
        {
          pattern_type: "Equipment Misidentification",
          occurrence_count: 189,
          confidence_level: 76.4,
          examples: [
            "Security equipment misclassified as threats",
            "Maintenance tools flagged incorrectly",
            "Authorized equipment triggering alerts"
          ],
          filtering_success_rate: 80.7
        }
      ],
      image_set_analysis: this.generateDemoImageSetAnalysis(),
      processing_summary: {
        total_cases: totalCases,
        completed_cases: imageSets,
        failed_cases: 45,
        success_rate: 99.1,
        average_processing_time_ms: 18750,
        started_at: new Date(Date.now() - 3600000).toISOString(), // 1 hour ago
        completed_at: new Date(Date.now() - 300000).toISOString()  // 5 minutes ago
      },
      data_source_info: {
        batch_id: 'demo-batch-' + Date.now(),
        priority: 'high',
        use_auto_learning: true,
        custom_thresholds: {
          confidence_threshold: 0.75,
          false_positive_threshold: 0.85
        }
      }
    };
  }

  private generateDemoImageSetAnalysis(): InsightsImageSetAnalysis[] {
    const caseNumbers = [
      'CASE-2024-001', 'CASE-2024-002', 'CASE-2024-003', 'CASE-2024-004', 'CASE-2024-005',
      'CASE-2024-006', 'CASE-2024-007', 'CASE-2024-008', 'CASE-2024-009', 'CASE-2024-010',
      'CASE-2024-011', 'CASE-2024-012', 'CASE-2024-013', 'CASE-2024-014', 'CASE-2024-015',
      'CASE-2024-016', 'CASE-2024-017', 'CASE-2024-018', 'CASE-2024-019', 'CASE-2024-020'
    ];

    const detectionTypes = ['STRUCTURE_MISIDENTIFIED', 'SHADOW_ARTIFACT', 'ENVIRONMENTAL_CONDITION', 'MOTION_BLUR', 'EQUIPMENT_MISIDENTIFIED'];
    const recommendations = ['DISMISS_ALERT', 'REQUIRES_REVIEW', 'ESCALATE_ALERT'];

    return caseNumbers.map((caseNumber, index) => ({
      case_number: caseNumber,
      has_source_image: true,
      has_cropped_image: true,
      processing_status: 'completed' as const,
      vlm_analysis_result: {
        detection_type: detectionTypes[index % detectionTypes.length],
        false_positive_likelihood: 0.65 + (Math.random() * 0.3), // 65-95%
        reasoning: this.generateDemoReasoning(detectionTypes[index % detectionTypes.length]),
        recommendation: recommendations[index % recommendations.length]
      }
    }));
  }

  private generateDemoReasoning(detectionType: string): string {
    const reasoningMap: Record<string, string[]> = {
      'STRUCTURE_MISIDENTIFIED': [
        'Analysis indicates building structure misidentified as vehicle due to angular geometry and metallic surfaces.',
        'Architectural elements with reflective properties triggered false positive detection algorithms.',
        'Construction framework patterns confused with vehicle silhouettes in low-light conditions.'
      ],
      'SHADOW_ARTIFACT': [
        'Equipment shadow artifacts creating false object detection due to high contrast lighting conditions.',
        'Infrastructure shadows cast by overhead lighting systems misinterpreted as suspicious objects.',
        'Camera positioning creating shadow patterns that trigger detection algorithms incorrectly.'
      ],
      'ENVIRONMENTAL_CONDITION': [
        'Weather-related visibility conditions affecting detection accuracy and creating false alerts.',
        'Seasonal lighting variations causing environmental factors to be misclassified as threats.',
        'Atmospheric conditions creating visual distortions that trigger false positive responses.'
      ],
      'MOTION_BLUR': [
        'Fast-moving legitimate objects creating motion blur artifacts that confuse detection systems.',
        'Camera shake during windy conditions producing blur patterns misidentified as suspicious activity.',
        'Motion-induced distortions from authorized personnel movement triggering false alerts.'
      ],
      'EQUIPMENT_MISIDENTIFIED': [
        'Authorized security equipment misclassified as unauthorized objects due to similar visual signatures.',
        'Maintenance tools and equipment flagged incorrectly by detection algorithms.',
        'Standard operational equipment triggering alerts due to positioning and lighting conditions.'
      ]
    };

    const reasons = reasoningMap[detectionType] || ['Standard analysis completed with confidence metrics applied.'];
    return reasons[Math.floor(Math.random() * reasons.length)];
  }
}

export const insightsService = new InsightsService();
