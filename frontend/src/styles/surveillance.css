/* AI-FARM Surveillance Dashboard Styling System */
/* Based on customer design template - Professional surveillance theme */

:root {
  /* Professional Surveillance Color Scheme */
  --bg-primary: #0a0a0a;
  --bg-secondary: #1a1a1a;
  --bg-tertiary: #2d2d2d;
  --bg-card: #252525;
  --accent-blue: #00d4ff;
  --accent-green: #00ff88;
  --accent-red: #ff4757;
  --accent-orange: #ffa502;
  --text-primary: #ffffff;
  --text-secondary: #cccccc;
  --text-muted: #888888;
  --border: #404040;
  --shadow: 0 4px 20px rgba(0, 212, 255, 0.1);
}

/* Global Components */
.surveillance-container {
  max-width: 1920px;
  margin: 0 auto;
  padding: 0 20px;
}

.surveillance-btn {
  padding: 12px 24px;
  border: none;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
  font-size: 14px;
}

.surveillance-btn-primary {
  background: linear-gradient(135deg, var(--accent-blue), #0056b3);
  color: white;
}

.surveillance-btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow);
}

.surveillance-btn-success {
  background: var(--accent-green);
  color: #000;
}

.surveillance-btn-danger {
  background: var(--accent-red);
  color: white;
}

.surveillance-card {
  background: var(--bg-card);
  border: 1px solid var(--border);
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.metric-card-surveillance {
  background: var(--bg-tertiary);
  border: 1px solid var(--border);
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.metric-card-surveillance::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--accent-blue);
}

.metric-value-surveillance {
  font-size: 36px;
  font-weight: 700;
  margin-bottom: 8px;
  color: var(--text-primary);
}

.metric-label-surveillance {
  font-size: 14px;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.metric-change {
  font-size: 12px;
  margin-top: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.positive { color: var(--accent-green); }
.negative { color: var(--accent-red); }
.warning { color: var(--accent-orange); }

/* Header Navigation */
.nav-surveillance {
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border);
  padding: 0;
  position: sticky;
  top: 0;
  z-index: 1000;
}

.nav-surveillance .nav {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  height: 70px;
}

.surveillance-logo {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 20px;
  font-weight: 700;
  color: var(--accent-blue);
}

.surveillance-logo-icon {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, var(--accent-blue), #0056b3);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  color: white;
}

.surveillance-nav-links {
  display: flex;
  gap: 0;
  list-style: none;
}

.surveillance-nav-link {
  padding: 25px 20px;
  color: var(--text-secondary);
  text-decoration: none;
  transition: all 0.3s ease;
  border-bottom: 3px solid transparent;
}

.surveillance-nav-link:hover, 
.surveillance-nav-link.active {
  color: var(--text-primary);
  background: var(--bg-tertiary);
  border-bottom-color: var(--accent-blue);
}

.status-indicators {
  display: flex;
  align-items: center;
  gap: 20px;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.status-dot.online { background: var(--accent-green); }
.status-dot.warning { background: var(--accent-orange); }
.status-dot.offline { background: var(--accent-red); }

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* Crisis Statistics */
.crisis-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin: 60px 0;
}

.crisis-stat {
  background: var(--bg-card);
  padding: 30px;
  border-radius: 12px;
  border: 1px solid var(--border);
  position: relative;
}

.crisis-stat.critical::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--accent-red);
}

.crisis-number {
  font-size: 42px;
  font-weight: 700;
  color: var(--accent-red);
  margin-bottom: 10px;
}

/* Processing Pipeline */
.processing-pipeline {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  margin-bottom: 40px;
}

.pipeline-stage {
  background: var(--bg-card);
  padding: 24px;
  border-radius: 8px;
  text-align: center;
  position: relative;
  border: 2px solid var(--border);
}

.pipeline-stage.active {
  border-color: var(--accent-blue);
  background: var(--bg-tertiary);
}

.pipeline-stage.complete {
  border-color: var(--accent-green);
}

.stage-icon {
  font-size: 32px;
  margin-bottom: 16px;
  display: block;
}

.progress-bar {
  background: var(--bg-secondary);
  height: 8px;
  border-radius: 4px;
  overflow: hidden;
  margin: 16px 0;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--accent-blue), var(--accent-green));
  transition: width 0.3s ease;
}

/* Alert Feed */
.alert-feed {
  background: var(--bg-card);
  border-radius: 8px;
  overflow: hidden;
}

.feed-header {
  background: var(--bg-secondary);
  padding: 16px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid var(--border);
}

.live-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--accent-green);
  font-weight: 600;
}

.alert-list {
  max-height: 600px;
  overflow-y: auto;
}

.alert-item {
  display: flex;
  padding: 16px 20px;
  border-bottom: 1px solid var(--border);
  transition: background 0.2s ease;
}

.alert-item:hover {
  background: var(--bg-tertiary);
}

.alert-thumbnail {
  width: 80px;
  height: 60px;
  background: var(--bg-secondary);
  border-radius: 6px;
  margin-right: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: var(--text-muted);
}

.alert-info {
  flex: 1;
}

.alert-id {
  font-weight: 600;
  margin-bottom: 4px;
  color: var(--text-primary);
}

.alert-meta {
  font-size: 12px;
  color: var(--text-secondary);
  margin-bottom: 8px;
}

.alert-badges {
  display: flex;
  gap: 8px;
  margin-left: auto;
  flex-direction: column;
  align-items: flex-end;
}

.surveillance-badge {
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
}

.badge-filtered { background: var(--accent-green); color: #000; }
.badge-review { background: var(--accent-orange); color: #000; }
.badge-urgent { background: var(--accent-red); color: white; }

.confidence-score {
  font-size: 11px;
  color: var(--text-muted);
  margin-top: 4px;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .processing-pipeline {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .surveillance-nav-links {
    display: none;
  }
  
  .crisis-stats {
    grid-template-columns: 1fr;
  }
  
  .processing-pipeline {
    grid-template-columns: 1fr;
  }
}

/* Utility Classes */
.text-center { text-align: center; }
.text-right { text-align: right; }
.mb-20 { margin-bottom: 20px; }
.mb-30 { margin-bottom: 30px; }
.mt-20 { margin-top: 20px; }
.surveillance-grid { display: grid; }
.surveillance-flex { display: flex; }
.items-center { align-items: center; }
.justify-between { justify-content: space-between; }
.gap-20 { gap: 20px; }
