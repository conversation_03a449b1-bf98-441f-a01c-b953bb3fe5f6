import { useEffect } from 'react';

export type Theme = 'surveillance';

export const useTheme = () => {
  const theme: Theme = 'surveillance';

  useEffect(() => {
    // Always apply surveillance theme
    document.documentElement.classList.add('surveillance-theme');
    document.body.classList.add('bg-dark-500', 'text-gray-100');
    document.body.classList.remove('bg-gray-50', 'text-gray-900');
  }, []);

  return { theme };
};