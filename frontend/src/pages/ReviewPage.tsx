import React, { useState, useEffect } from 'react';
import { 
  Eye, 
  Brain, 
  CheckCircle, 
  XCircle, 
  AlertTriangle,
  Clock,
  Camera,
  Zap,
  ThumbsUp,
  ThumbsDown,
  RotateCcw,
  ZoomIn,
  ZoomOut
} from 'lucide-react';
import '../styles/surveillance.css';

interface VALOAlert {
  id: string;
  timestamp: string;
  camera: string;
  terminal: string;
  imageUrl: string;
  status: 'pending' | 'reviewed' | 'approved' | 'rejected';
  vlmAnalysis?: {
    category: string;
    confidence: number;
    recommendation: 'filter' | 'review' | 'escalate';
    reasoning: string;
    details: string[];
  };
}

interface ReviewStats {
  pending: number;
  reviewed: number;
  approved: number;
  rejected: number;
  accuracy: number;
}

export const ReviewPage: React.FC = () => {
  const [selectedAlert, setSelectedAlert] = useState<VALOAlert | null>(null);
  const [alerts, setAlerts] = useState<VALOAlert[]>([]);
  const [filter, setFilter] = useState<'all' | 'pending' | 'high-confidence' | 'low-confidence'>('pending');
  const [imageZoom, setImageZoom] = useState(1);
  
  const [stats, setStats] = useState<ReviewStats>({
    pending: 247,
    reviewed: 1834,
    approved: 156,
    rejected: 1678,
    accuracy: 94.2
  });

  // Mock data - in real app, this would come from API
  useEffect(() => {
    const mockAlerts: VALOAlert[] = [
      {
        id: 'ALERT-P1-20240704-142312',
        timestamp: '2024-07-04 14:23:12',
        camera: 'CAM-P1-GATE-01',
        terminal: 'P1',
        imageUrl: '/api/placeholder/400/300',
        status: 'pending',
        vlmAnalysis: {
          category: 'Empty Corridor',
          confidence: 94.2,
          recommendation: 'filter',
          reasoning: 'No person visible in the image. The corridor appears empty with normal lighting conditions.',
          details: [
            'No human figures detected',
            'Normal lighting conditions',
            'No suspicious objects or activities',
            'Corridor appears clear and unobstructed'
          ]
        }
      },
      {
        id: 'ALERT-P2-20240704-142345',
        timestamp: '2024-07-04 14:23:45',
        camera: 'CAM-P2-ENTRY-02',
        terminal: 'P2',
        imageUrl: '/api/placeholder/400/300',
        status: 'pending',
        vlmAnalysis: {
          category: 'Authorized Personnel',
          confidence: 67.8,
          recommendation: 'review',
          reasoning: 'Person detected but wearing what appears to be authorized uniform. Low confidence due to image quality.',
          details: [
            'Single person detected',
            'Uniform consistent with authorized personnel',
            'Image quality affects confidence',
            'Recommend manual verification'
          ]
        }
      },
      {
        id: 'ALERT-P3-20240704-142401',
        timestamp: '2024-07-04 14:24:01',
        camera: 'CAM-P3-SECURE-03',
        terminal: 'P3',
        imageUrl: '/api/placeholder/400/300',
        status: 'pending',
        vlmAnalysis: {
          category: 'Equipment/Maintenance',
          confidence: 89.5,
          recommendation: 'filter',
          reasoning: 'Maintenance equipment visible in restricted area. Appears to be scheduled maintenance activity.',
          details: [
            'Maintenance cart detected',
            'No unauthorized personnel',
            'Equipment consistent with scheduled maintenance',
            'Area properly secured'
          ]
        }
      }
    ];
    
    setAlerts(mockAlerts);
    setSelectedAlert(mockAlerts[0]);
  }, []);

  const handleReview = (alertId: string, decision: 'approve' | 'reject') => {
    setAlerts(prev => prev.map(alert => 
      alert.id === alertId 
        ? { ...alert, status: decision === 'approve' ? 'approved' : 'rejected' }
        : alert
    ));
    
    // Update stats
    setStats(prev => ({
      ...prev,
      pending: prev.pending - 1,
      reviewed: prev.reviewed + 1,
      [decision === 'approve' ? 'approved' : 'rejected']: prev[decision === 'approve' ? 'approved' : 'rejected'] + 1
    }));
    
    // Move to next alert
    const currentIndex = alerts.findIndex(a => a.id === alertId);
    const nextAlert = alerts[currentIndex + 1] || alerts[0];
    setSelectedAlert(nextAlert);
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 90) return 'text-green-400';
    if (confidence >= 70) return 'text-orange-400';
    return 'text-red-400';
  };

  const getRecommendationColor = (recommendation: string) => {
    switch (recommendation) {
      case 'filter': return 'text-green-400';
      case 'review': return 'text-orange-400';
      case 'escalate': return 'text-red-400';
      default: return 'text-gray-400';
    }
  };

  const filteredAlerts = alerts.filter(alert => {
    switch (filter) {
      case 'pending': return alert.status === 'pending';
      case 'high-confidence': return alert.vlmAnalysis && alert.vlmAnalysis.confidence >= 85;
      case 'low-confidence': return alert.vlmAnalysis && alert.vlmAnalysis.confidence < 70;
      default: return true;
    }
  });

  return (
    <div className="min-h-screen" style={{ background: 'var(--bg-primary)' }}>
      <div className="surveillance-container py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">Alert Review Center</h1>
            <p className="text-gray-400">Manual review of VALO alerts with VLM analysis</p>
          </div>
          <div className="flex items-center gap-4">
            <button className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded font-medium">
              Add Stamps
            </button>
            <button className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded font-medium">
              Skip Review
            </button>
            <select
              value={filter}
              onChange={(e) => setFilter(e.target.value as any)}
              className="bg-gray-800 text-white border border-gray-600 rounded px-3 py-2"
            >
              <option value="all">All Alerts</option>
              <option value="pending">Pending Review</option>
              <option value="high-confidence">High Confidence</option>
              <option value="low-confidence">Low Confidence</option>
            </select>
          </div>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-5 gap-6 mb-8">
          <div className="metric-card-surveillance">
            <div className="metric-value-surveillance text-orange-400">{stats.pending}</div>
            <div className="metric-label-surveillance">Pending Review</div>
          </div>
          <div className="metric-card-surveillance">
            <div className="metric-value-surveillance text-blue-400">{stats.reviewed}</div>
            <div className="metric-label-surveillance">Reviewed</div>
          </div>
          <div className="metric-card-surveillance">
            <div className="metric-value-surveillance text-green-400">{stats.approved}</div>
            <div className="metric-label-surveillance">Approved</div>
          </div>
          <div className="metric-card-surveillance">
            <div className="metric-value-surveillance text-red-400">{stats.rejected}</div>
            <div className="metric-label-surveillance">Rejected</div>
          </div>
          <div className="metric-card-surveillance">
            <div className="metric-value-surveillance text-purple-400">{stats.accuracy}%</div>
            <div className="metric-label-surveillance">Accuracy</div>
          </div>
        </div>

        {/* Main Review Interface */}
        {selectedAlert && (
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            {/* Main Image Display - Takes up 3 columns */}
            <div className="lg:col-span-3">
              <div className="surveillance-card mb-6">
                <div className="feed-header">
                  <h3 className="text-lg font-semibold text-white">VALO Alert #{selectedAlert.id.split('-').pop()}</h3>
                  <div className="text-sm text-gray-400">
                    Terminal {selectedAlert.terminal} • Container: {selectedAlert.timestamp.split(' ')[0]} • {selectedAlert.timestamp.split(' ')[1]}
                  </div>
                </div>

                {/* Large Image Display */}
                <div className="p-6">
                  <div className="bg-gray-900 rounded-lg p-8 mb-6 min-h-96 flex items-center justify-center">
                    <div className="text-center">
                      <Camera className="w-16 h-16 text-gray-600 mx-auto mb-4" />
                      <p className="text-gray-500 text-lg">VLM Image with Detection Box</p>
                      <p className="text-gray-600 text-sm mt-2">Original VALO violation image is not available</p>
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <button
                        onClick={() => setImageZoom(Math.max(0.5, imageZoom - 0.25))}
                        className="flex items-center space-x-2 px-3 py-2 bg-gray-700 hover:bg-gray-600 rounded text-sm"
                      >
                        <ZoomOut className="w-4 h-4" />
                        <span>Zoom Out</span>
                      </button>
                      <button
                        onClick={() => setImageZoom(Math.min(3, imageZoom + 0.25))}
                        className="flex items-center space-x-2 px-3 py-2 bg-gray-700 hover:bg-gray-600 rounded text-sm"
                      >
                        <ZoomIn className="w-4 h-4" />
                        <span>Zoom In</span>
                      </button>
                      <span className="text-sm text-gray-400">Zoom: {Math.round(imageZoom * 100)}%</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <button className="flex items-center space-x-2 px-4 py-2 bg-green-600 hover:bg-green-700 rounded text-sm">
                        <CheckCircle className="w-4 h-4" />
                        <span>Mark Reviewed</span>
                      </button>
                      <button className="flex items-center space-x-2 px-4 py-2 bg-red-600 hover:bg-red-700 rounded text-sm">
                        <XCircle className="w-4 h-4" />
                        <span>False Positive</span>
                      </button>
                      <button className="flex items-center space-x-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded text-sm">
                        <RotateCcw className="w-4 h-4" />
                        <span>Skip for Now</span>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Right Sidebar - VLM Analysis Results */}
            <div className="lg:col-span-1">
              <div className="surveillance-card mb-6">
                <div className="feed-header">
                  <h3 className="text-lg font-semibold text-white">VLM Analysis Results</h3>
                </div>
                <div className="p-4 space-y-4">
                  <div>
                    <h4 className="text-sm font-semibold text-blue-400 mb-2">Detection Type</h4>
                    <p className="text-white bg-gray-800 px-3 py-2 rounded">
                      {selectedAlert.vlmAnalysis?.category || 'UNCLASSIFIED - Requires Human Review'}
                    </p>
                  </div>

                  <div>
                    <h4 className="text-sm font-semibold text-blue-400 mb-2">False Positive Likelihood</h4>
                    <div className="bg-gray-800 px-3 py-2 rounded">
                      <div className="flex items-center justify-between mb-1">
                        <span className="text-white text-sm">High</span>
                        <span className="text-white text-sm">{selectedAlert.vlmAnalysis?.confidence || 94}%</span>
                      </div>
                      <div className="w-full bg-gray-700 rounded-full h-2">
                        <div
                          className="bg-red-500 h-2 rounded-full"
                          style={{ width: `${selectedAlert.vlmAnalysis?.confidence || 94}%` }}
                        />
                      </div>
                    </div>
                  </div>

                  <div>
                    <h4 className="text-sm font-semibold text-blue-400 mb-2">VLM Reasoning</h4>
                    <div className="bg-gray-800 px-3 py-2 rounded text-sm text-gray-300">
                      {selectedAlert.vlmAnalysis?.reasoning || 'I need to examine this violation to determine if it represents a genuine safety concern. Let me analyze the image carefully to identify any potential false positive indicators.'}
                    </div>
                  </div>
                </div>
              </div>

              {/* Processing Details */}
              <div className="surveillance-card mb-6">
                <div className="feed-header">
                  <h3 className="text-lg font-semibold text-white">Processing Details</h3>
                </div>
                <div className="p-4 space-y-3">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-400">Processing Time:</span>
                    <span className="text-white">2.3s</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-400">Model Version:</span>
                    <span className="text-white">VLM-38B-AWQ</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-400">Confidence Threshold:</span>
                    <span className="text-white">85%</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-400">Image Resolution:</span>
                    <span className="text-white">1920x1080</span>
                  </div>
                </div>
              </div>

              {/* Original VALO Detection */}
              <div className="surveillance-card mb-6">
                <div className="feed-header">
                  <h3 className="text-lg font-semibold text-white">Original VALO Detection</h3>
                </div>
                <div className="p-4 space-y-3">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-400">Alert Type:</span>
                    <span className="text-white">PPE Violation</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-400">Location:</span>
                    <span className="text-white">Terminal P1 - Section C</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-400">Time:</span>
                    <span className="text-white">{selectedAlert.timestamp}</span>
                  </div>
                </div>
              </div>

              {/* Review Notes */}
              <div className="surveillance-card">
                <div className="feed-header">
                  <h3 className="text-lg font-semibold text-white">Review Notes</h3>
                </div>
                <div className="p-4">
                  <textarea
                    className="w-full bg-gray-800 border border-gray-600 rounded px-3 py-2 text-white text-sm"
                    rows={4}
                    placeholder="Add review notes, special observations, or recommendations..."
                  />
                  <div className="flex justify-between items-center mt-3 text-xs text-gray-500">
                    <span>Last updated: {new Date().toLocaleString()}</span>
                    <button className="text-blue-400 hover:text-blue-300">Save Notes</button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Queue Status */}
        <div className="mt-8 bg-gray-800 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <h3 className="text-lg font-semibold text-white">Queue Status</h3>
              <span className="text-sm text-gray-400">Current Position:</span>
              <span className="text-white font-bold">47 of 156</span>
            </div>
            <div className="flex items-center space-x-4">
              <button className="px-4 py-2 bg-gray-700 hover:bg-gray-600 rounded text-sm">
                Previous Alert
              </button>
              <button className="px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded text-sm">
                Next Alert
              </button>
            </div>
          </div>
        </div>

        {/* Alert Selection Sidebar - Only show when no alert is selected */}
        {!selectedAlert && (
          <div className="surveillance-card">
            <div className="feed-header">
              <h3 className="text-lg font-semibold text-white">Alert Queue</h3>
              <span className="text-sm text-gray-400">{filteredAlerts.length} alerts</span>
            </div>

            <div className="alert-list max-h-96 overflow-y-auto">
              {filteredAlerts.map((alert) => (
                <div
                  key={alert.id}
                  className="alert-item cursor-pointer hover:bg-gray-700"
                  onClick={() => setSelectedAlert(alert)}
                >
                  <div className="alert-thumbnail">
                    <Camera className="w-6 h-6 text-gray-500" />
                  </div>
                  <div className="alert-info">
                    <div className="alert-id">{alert.id}</div>
                    <div className="alert-meta">
                      {alert.camera} • {alert.timestamp}
                    </div>
                    {alert.vlmAnalysis && (
                      <div className="text-xs text-gray-400 mt-1">
                        {alert.vlmAnalysis.category} • {alert.vlmAnalysis.confidence}% confidence
                      </div>
                    )}
                  </div>
                  <div className="alert-badges">
                    <div className={`surveillance-badge ${
                      alert.status === 'pending' ? 'badge-review' :
                      alert.status === 'approved' ? 'badge-filtered' : 'badge-urgent'
                    }`}>
                      {alert.status}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
