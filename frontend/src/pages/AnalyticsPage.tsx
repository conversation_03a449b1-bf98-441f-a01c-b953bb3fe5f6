import React, { useState, useEffect } from 'react';
import { 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>, 
  TrendingUp, 
  TrendingDown,
  Activity,
  Target,
  Clock,
  AlertTriangle,
  CheckCircle,
  Filter
} from 'lucide-react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  XAxis,
  <PERSON><PERSON><PERSON>s,
  <PERSON>tesianGrid,
  <PERSON>lt<PERSON>,
  Responsive<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  Pie,
  Cell,
  LineChart,
  Line,
  Area,
  AreaChart
} from 'recharts';
import '../styles/surveillance.css';

interface PerformanceData {
  month: string;
  totalAlerts: number;
  falsePositives: number;
  truePositives: number;
  filtered: number;
}

interface CategoryData {
  category: string;
  count: number;
  percentage: number;
  color: string;
}

interface TerminalData {
  terminal: string;
  alerts: number;
  accuracy: number;
  falsePositiveRate: number;
  avgProcessingTime: number;
}

const COLORS = ['#00d4ff', '#00ff88', '#ffa502', '#ff4757', '#9c88ff'];

export const AnalyticsPage: React.FC = () => {
  const [timeRange, setTimeRange] = useState<'7d' | '30d' | '90d' | '1y'>('30d');
  
  const performanceData: PerformanceData[] = [
    { month: 'Jan', totalAlerts: 15420, falsePositives: 14957, truePositives: 463, filtered: 10470 },
    { month: 'Feb', totalAlerts: 16890, falsePositives: 16383, truePositives: 507, filtered: 11423 },
    { month: 'Mar', totalAlerts: 17268, falsePositives: 16750, truePositives: 518, filtered: 11725 },
    { month: 'Apr', totalAlerts: 16543, falsePositives: 16047, truePositives: 496, filtered: 11238 },
    { month: 'May', totalAlerts: 18234, falsePositives: 17687, truePositives: 547, filtered: 12364 },
    { month: 'Jun', totalAlerts: 17892, falsePositives: 17355, truePositives: 537, filtered: 12149 }
  ];

  const categoryData: CategoryData[] = [
    { category: 'Empty Corridor', count: 4567, percentage: 38.2, color: '#00d4ff' },
    { category: 'Authorized Personnel', count: 2890, percentage: 24.1, color: '#00ff88' },
    { category: 'Equipment/Maintenance', count: 1834, percentage: 15.3, color: '#ffa502' },
    { category: 'Lighting/Shadow', count: 1456, percentage: 12.2, color: '#ff4757' },
    { category: 'Other', count: 1228, percentage: 10.2, color: '#9c88ff' }
  ];

  const terminalData: TerminalData[] = [
    { terminal: 'P1', alerts: 6234, accuracy: 94.2, falsePositiveRate: 96.8, avgProcessingTime: 2.3 },
    { terminal: 'P2', alerts: 5891, accuracy: 92.7, falsePositiveRate: 97.2, avgProcessingTime: 2.1 },
    { terminal: 'P3', alerts: 5767, accuracy: 95.1, falsePositiveRate: 96.4, avgProcessingTime: 2.4 }
  ];

  const processingTrendData = [
    { time: '00:00', alerts: 45, filtered: 32 },
    { time: '04:00', alerts: 23, filtered: 16 },
    { time: '08:00', alerts: 78, filtered: 54 },
    { time: '12:00', alerts: 92, filtered: 67 },
    { time: '16:00', alerts: 87, filtered: 61 },
    { time: '20:00', alerts: 65, filtered: 45 }
  ];

  const currentMetrics = {
    totalProcessed: 17892,
    falsePositivesFiltered: 12149,
    accuracyRate: 94.7,
    processingTime: 2.3,
    costSavings: 351000,
    efficiencyGain: 68.9
  };

  return (
    <div className="min-h-screen" style={{ background: 'var(--bg-primary)' }}>
      <div className="surveillance-container py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">Performance Analytics</h1>
            <p className="text-gray-400">AI-FARM system performance and insights</p>
          </div>
          <div className="flex items-center gap-4">
            <select 
              value={timeRange}
              onChange={(e) => setTimeRange(e.target.value as any)}
              className="bg-gray-800 text-white border border-gray-600 rounded px-3 py-2"
            >
              <option value="7d">Last 7 Days</option>
              <option value="30d">Last 30 Days</option>
              <option value="90d">Last 90 Days</option>
              <option value="1y">Last Year</option>
            </select>
          </div>
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-6 mb-8">
          <div className="metric-card-surveillance">
            <div className="metric-value-surveillance">{currentMetrics.totalProcessed.toLocaleString()}</div>
            <div className="metric-label-surveillance">Total Processed</div>
            <div className="metric-change positive">
              <TrendingUp className="w-3 h-3" />
              +12.4%
            </div>
          </div>
          <div className="metric-card-surveillance">
            <div className="metric-value-surveillance text-green-400">{currentMetrics.falsePositivesFiltered.toLocaleString()}</div>
            <div className="metric-label-surveillance">False Positives Filtered</div>
            <div className="metric-change positive">
              <TrendingUp className="w-3 h-3" />
              +15.2%
            </div>
          </div>
          <div className="metric-card-surveillance">
            <div className="metric-value-surveillance text-blue-400">{currentMetrics.accuracyRate}%</div>
            <div className="metric-label-surveillance">Accuracy Rate</div>
            <div className="metric-change positive">
              <TrendingUp className="w-3 h-3" />
              +2.1%
            </div>
          </div>
          <div className="metric-card-surveillance">
            <div className="metric-value-surveillance text-purple-400">{currentMetrics.processingTime}s</div>
            <div className="metric-label-surveillance">Avg Processing Time</div>
            <div className="metric-change negative">
              <TrendingDown className="w-3 h-3" />
              -8.3%
            </div>
          </div>
          <div className="metric-card-surveillance">
            <div className="metric-value-surveillance text-green-400">${(currentMetrics.costSavings / 1000).toFixed(0)}K</div>
            <div className="metric-label-surveillance">Cost Savings</div>
            <div className="metric-change positive">
              <TrendingUp className="w-3 h-3" />
              +18.7%
            </div>
          </div>
          <div className="metric-card-surveillance">
            <div className="metric-value-surveillance text-orange-400">{currentMetrics.efficiencyGain}%</div>
            <div className="metric-label-surveillance">Efficiency Gain</div>
            <div className="metric-change positive">
              <TrendingUp className="w-3 h-3" />
              +5.4%
            </div>
          </div>
        </div>

        {/* Charts Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          {/* Monthly Performance */}
          <div className="surveillance-card">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-semibold text-white flex items-center gap-2">
                <BarChart3 className="w-5 h-5 text-blue-400" />
                Monthly Performance
              </h3>
            </div>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={performanceData}>
                <CartesianGrid strokeDasharray="3 3" stroke="#404040" />
                <XAxis dataKey="month" stroke="#888888" />
                <YAxis stroke="#888888" />
                <Tooltip 
                  contentStyle={{ 
                    backgroundColor: 'var(--bg-card)', 
                    border: '1px solid var(--border)',
                    borderRadius: '6px'
                  }}
                />
                <Bar dataKey="totalAlerts" fill="#00d4ff" name="Total Alerts" />
                <Bar dataKey="filtered" fill="#00ff88" name="Filtered" />
              </BarChart>
            </ResponsiveContainer>
          </div>

          {/* False Positive Categories */}
          <div className="surveillance-card">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-semibold text-white flex items-center gap-2">
                <PieChart className="w-5 h-5 text-blue-400" />
                False Positive Categories
              </h3>
            </div>
            <ResponsiveContainer width="100%" height={300}>
              <RechartsPieChart>
                <Pie
                  data={categoryData}
                  cx="50%"
                  cy="50%"
                  outerRadius={80}
                  dataKey="count"
                  label={({ category, percentage }: any) => `${category}: ${percentage}%`}
                >
                  {categoryData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </RechartsPieChart>
            </ResponsiveContainer>
          </div>
        </div>

        {/* Terminal Performance Comparison */}
        <div className="surveillance-card mb-8">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-xl font-semibold text-white flex items-center gap-2">
              <Target className="w-5 h-5 text-blue-400" />
              Terminal Performance Comparison
            </h3>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {terminalData.map((terminal) => (
              <div key={terminal.terminal} className="bg-gray-800 rounded-lg p-6">
                <div className="flex items-center justify-between mb-4">
                  <h4 className="text-lg font-semibold text-white">Terminal {terminal.terminal}</h4>
                  <div className="text-2xl font-bold text-blue-400">{terminal.alerts.toLocaleString()}</div>
                </div>
                
                <div className="space-y-4">
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span className="text-gray-400">Accuracy Rate</span>
                      <span className="text-green-400">{terminal.accuracy}%</span>
                    </div>
                    <div className="progress-bar">
                      <div 
                        className="progress-fill" 
                        style={{ width: `${terminal.accuracy}%` }}
                      ></div>
                    </div>
                  </div>
                  
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span className="text-gray-400">False Positive Rate</span>
                      <span className="text-red-400">{terminal.falsePositiveRate}%</span>
                    </div>
                    <div className="progress-bar">
                      <div 
                        className="h-full bg-red-500 transition-all duration-300" 
                        style={{ width: `${terminal.falsePositiveRate}%` }}
                      ></div>
                    </div>
                  </div>
                  
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-400">Avg Processing Time</span>
                    <span className="text-purple-400">{terminal.avgProcessingTime}s</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Processing Trend */}
        <div className="surveillance-card">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-xl font-semibold text-white flex items-center gap-2">
              <Activity className="w-5 h-5 text-blue-400" />
              24-Hour Processing Trend
            </h3>
          </div>
          <ResponsiveContainer width="100%" height={300}>
            <AreaChart data={processingTrendData}>
              <CartesianGrid strokeDasharray="3 3" stroke="#404040" />
              <XAxis dataKey="time" stroke="#888888" />
              <YAxis stroke="#888888" />
              <Tooltip 
                contentStyle={{ 
                  backgroundColor: 'var(--bg-card)', 
                  border: '1px solid var(--border)',
                  borderRadius: '6px'
                }}
              />
              <Area 
                type="monotone" 
                dataKey="alerts" 
                stackId="1" 
                stroke="#00d4ff" 
                fill="#00d4ff" 
                fillOpacity={0.3}
                name="Total Alerts"
              />
              <Area 
                type="monotone" 
                dataKey="filtered" 
                stackId="2" 
                stroke="#00ff88" 
                fill="#00ff88" 
                fillOpacity={0.3}
                name="Filtered"
              />
            </AreaChart>
          </ResponsiveContainer>
        </div>
      </div>
    </div>
  );
};
