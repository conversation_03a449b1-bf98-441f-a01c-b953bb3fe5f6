import React, { useEffect, useState, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Activity,
  AlertTriangle,
  BarChart3,
  Brain,
  Camera,
  CheckCircle,
  Clock,
  Database,
  Eye,
  Filter,
  Gauge,
  RefreshCw,
  Target,
  TrendingDown,
  TrendingUp,
  Users,
  Zap
} from 'lucide-react';
import {
  Button,
  Card,
  CardHeader,
  CardBody,
  Alert,
  LoadingSpinner
} from '../components/ui';
import { dataAnalysisService, AnalysisMetrics, CameraAnalysis, FalsePositivePatterns, ViolationCase } from '../services/data-analysis-service';
import toast from 'react-hot-toast';

export const DataAnalysisDashboard: React.FC = () => {
  const navigate = useNavigate();
  
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [metrics, setMetrics] = useState<AnalysisMetrics | null>(null);
  const [cameraAnalysis, setCameraAnalysis] = useState<CameraAnalysis | null>(null);
  const [patterns, setPatterns] = useState<FalsePositivePatterns | null>(null);
  const [cases, setCases] = useState<ViolationCase[]>([]);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);
  
  // Auto-refresh interval
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const [autoRefresh, setAutoRefresh] = useState(false);

  // Real-time processing state
  const [isProcessing, setIsProcessing] = useState(false);
  const [processingProgress, setProcessingProgress] = useState(0);
  const [processingStatus, setProcessingStatus] = useState<string>('');

  const fetchAnalysisData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch all analysis data in parallel
      const [metricsResponse, cameraResponse, patternsResponse, casesResponse] = await Promise.all([
        dataAnalysisService.getMetrics(),
        dataAnalysisService.getCameraAnalysis(),
        dataAnalysisService.getFalsePositivePatterns(),
        dataAnalysisService.getCases({ page: 1, per_page: 10 })
      ]);

      if (metricsResponse.success) {
        setMetrics(metricsResponse.data);
      }

      if (cameraResponse.success) {
        setCameraAnalysis(cameraResponse.data);
      }

      if (patternsResponse.success) {
        setPatterns(patternsResponse.data);
      }

      if (casesResponse.success) {
        setCases(casesResponse.data.cases);
      }

      setLastUpdated(new Date());
    } catch (err) {
      const errorMsg = err instanceof Error ? err.message : 'Failed to load analysis data';
      setError(errorMsg);
      toast.error(errorMsg);
    } finally {
      setLoading(false);
    }
  };

  const runFullAnalysis = async () => {
    try {
      setIsAnalyzing(true);
      setIsProcessing(true);
      setProcessingProgress(0);
      setProcessingStatus('Initializing analysis...');
      toast.loading('Running comprehensive analysis...', { id: 'analysis' });

      // Simulate real-time progress updates
      const progressInterval = setInterval(() => {
        setProcessingProgress(prev => {
          const newProgress = prev + Math.random() * 12;
          if (newProgress >= 95) {
            clearInterval(progressInterval);
            return 95;
          }
          return newProgress;
        });

        // Update status messages based on progress
        setProcessingStatus(prev => {
          const currentProgress = processingProgress;
          if (currentProgress < 20) return 'Loading violation data...';
          if (currentProgress < 40) return 'Processing image analysis...';
          if (currentProgress < 60) return 'Detecting false positives...';
          if (currentProgress < 80) return 'Analyzing patterns...';
          if (currentProgress < 95) return 'Generating insights...';
          return 'Finalizing results...';
        });
      }, 800);

      const response = await dataAnalysisService.runAnalysis();

      clearInterval(progressInterval);
      setProcessingProgress(100);
      setProcessingStatus('Analysis completed!');

      if (response.success) {
        toast.success('Analysis completed successfully!', { id: 'analysis' });
        await fetchAnalysisData(); // Refresh data after analysis
      } else {
        throw new Error('Analysis failed');
      }
    } catch (err) {
      const errorMsg = err instanceof Error ? err.message : 'Analysis failed';
      toast.error(errorMsg, { id: 'analysis' });
    } finally {
      setIsAnalyzing(false);
      setIsProcessing(false);
      setTimeout(() => {
        setProcessingProgress(0);
        setProcessingStatus('');
      }, 2000);
    }
  };

  const toggleAutoRefresh = () => {
    if (autoRefresh) {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
      setAutoRefresh(false);
      toast.success('Auto-refresh disabled');
    } else {
      intervalRef.current = setInterval(fetchAnalysisData, 30000); // Refresh every 30 seconds
      setAutoRefresh(true);
      toast.success('Auto-refresh enabled (30s interval)');
    }
  };

  useEffect(() => {
    fetchAnalysisData();
    
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  const getTopProblematicCameras = () => {
    if (!patterns?.patterns.camera_reliability.problematic_cameras) return [];
    
    return Object.entries(patterns.patterns.camera_reliability.problematic_cameras)
      .sort(([, a], [, b]) => a.accuracy_rate - b.accuracy_rate)
      .slice(0, 5);
  };

  const getHighPriorityRecommendations = () => {
    if (!patterns?.recommendations) return [];
    return patterns.recommendations.filter(rec => rec.priority === 'high');
  };

  if (loading && !metrics) {
    return (
      <div className="min-h-screen bg-dark-500 py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-gray-100 mb-4">Loading Analysis Dashboard...</h1>
            <LoadingSpinner size="lg" />
          </div>
        </div>
      </div>
    );
  }

  if (error && !metrics) {
    return (
      <div className="min-h-screen bg-dark-500 py-8">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <Card className="surveillance-card">
            <CardBody>
              <Alert variant="danger" title="Failed to Load Analysis Data">
                {error}
              </Alert>
              <div className="mt-4 text-center space-x-4">
                <Button onClick={fetchAnalysisData}>
                  Retry
                </Button>
                <Button variant="outline" onClick={() => navigate('/')}>
                  Go Home
                </Button>
              </div>
            </CardBody>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-dark-500 bg-grid-pattern py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-100 mb-2">
              VALO AI-FARM Data Analysis Dashboard
            </h1>
            <p className="text-lg text-gray-400">
              Real-time analysis of {metrics?.total_csv_cases || 0} violation cases and {metrics?.total_image_sets || 0} image sets
            </p>
            {lastUpdated && (
              <div className="flex items-center space-x-2 mt-1">
                <p className="text-sm text-gray-500">
                  Last updated: {lastUpdated.toLocaleTimeString()}
                </p>
                {autoRefresh && (
                  <div className="flex items-center space-x-1">
                    <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                    <span className="text-xs text-green-400">Live</span>
                  </div>
                )}
              </div>
            )}
          </div>
          
          <div className="flex space-x-4">
            <Button
              variant="outline"
              onClick={toggleAutoRefresh}
              leftIcon={RefreshCw}
              className={autoRefresh ? 'border-green-500 text-green-400' : ''}
            >
              {autoRefresh ? 'Auto-Refresh ON' : 'Auto-Refresh OFF'}
            </Button>
            <Button
              onClick={runFullAnalysis}
              disabled={isAnalyzing}
              leftIcon={isAnalyzing ? RefreshCw : Brain}
              className={isAnalyzing ? 'animate-pulse' : ''}
            >
              {isAnalyzing ? 'Analyzing...' : 'Run Analysis'}
            </Button>
            <Button
              variant="outline"
              onClick={fetchAnalysisData}
              leftIcon={RefreshCw}
              disabled={loading}
            >
              Refresh
            </Button>
          </div>
        </div>

        {/* Real-time Processing Progress */}
        {isProcessing && (
          <Card className="surveillance-card mb-8">
            <CardBody>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold text-gray-100">Analysis Progress</h3>
                  <span className="text-sm text-gray-400">{Math.round(processingProgress)}%</span>
                </div>

                <div className="w-full bg-gray-700 rounded-full h-3">
                  <div
                    className="bg-gradient-to-r from-blue-500 to-purple-600 h-3 rounded-full transition-all duration-500 ease-out"
                    style={{ width: `${processingProgress}%` }}
                  />
                </div>

                <div className="flex items-center space-x-2">
                  <LoadingSpinner size="sm" />
                  <span className="text-sm text-gray-300">{processingStatus}</span>
                </div>
              </div>
            </CardBody>
          </Card>
        )}

        {/* Key Metrics Overview */}
        {metrics && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div className="surveillance-card p-6">
              <div className="flex items-center justify-between mb-4">
                <Database className="w-8 h-8 text-blue-400" />
                <span className="text-xs px-2 py-1 bg-blue-900 text-blue-200 rounded">TOTAL</span>
              </div>
              <div className="text-3xl font-bold text-gray-100 mb-2">
                {metrics.total_csv_cases.toLocaleString()}
              </div>
              <div className="text-sm text-gray-400">Total Cases Analyzed</div>
              <div className="text-xs text-gray-500 mt-1">
                {metrics.cases_with_images} with images, {metrics.cases_without_images} without
              </div>
            </div>

            <div className="surveillance-card p-6">
              <div className="flex items-center justify-between mb-4">
                <CheckCircle className="w-8 h-8 text-green-400" />
                <span className="text-xs px-2 py-1 bg-green-900 text-green-200 rounded">VALID</span>
              </div>
              <div className="text-3xl font-bold text-gray-100 mb-2">
                {metrics.valid_detections.toLocaleString()}
              </div>
              <div className="text-sm text-gray-400">Valid Detections</div>
              <div className="text-xs text-green-400 mt-1">
                {((metrics.valid_detections / metrics.total_csv_cases) * 100).toFixed(1)}% of total
              </div>
            </div>

            <div className="surveillance-card p-6">
              <div className="flex items-center justify-between mb-4">
                <AlertTriangle className="w-8 h-8 text-red-400" />
                <span className="text-xs px-2 py-1 bg-red-900 text-red-200 rounded">FALSE+</span>
              </div>
              <div className="text-3xl font-bold text-gray-100 mb-2">
                {metrics.invalid_detections.toLocaleString()}
              </div>
              <div className="text-sm text-gray-400">False Positives</div>
              <div className="text-xs text-red-400 mt-1">
                {metrics.false_positive_rate.toFixed(1)}% false positive rate
              </div>
            </div>

            <div className="surveillance-card p-6">
              <div className="flex items-center justify-between mb-4">
                <Filter className="w-8 h-8 text-purple-400" />
                <span className="text-xs px-2 py-1 bg-purple-900 text-purple-200 rounded">FILTER</span>
              </div>
              <div className="text-3xl font-bold text-gray-100 mb-2">
                {metrics.filtering_effectiveness.toFixed(1)}%
              </div>
              <div className="text-sm text-gray-400">Filtering Effectiveness</div>
              <div className="text-xs text-purple-400 mt-1">
                AI-FARM accuracy improvement
              </div>
            </div>
          </div>
        )}

        {/* Camera Performance Analysis */}
        {cameraAnalysis && (
          <Card className="surveillance-card mb-8">
            <CardHeader>
              <div className="flex items-center space-x-2">
                <Camera className="w-5 h-5 text-primary-400" />
                <h2 className="text-xl font-semibold text-gray-100">Camera Performance Analysis</h2>
              </div>
              <p className="text-sm text-gray-400">
                Performance breakdown across {Object.keys(cameraAnalysis).length} cameras
              </p>
            </CardHeader>
            <CardBody>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Top Performing Cameras */}
                <div className="bg-dark-600 rounded-lg p-6 border border-gray-700">
                  <h3 className="font-semibold text-gray-100 mb-4 flex items-center">
                    <TrendingUp className="w-4 h-4 mr-2 text-green-400" />
                    Top Performing Cameras
                  </h3>
                  <div className="space-y-3">
                    {Object.entries(cameraAnalysis)
                      .sort(([, a], [, b]) => b.accuracy_rate - a.accuracy_rate)
                      .slice(0, 5)
                      .map(([camera, stats]) => (
                        <div key={camera} className="flex items-center justify-between">
                          <div className="flex-1 min-w-0">
                            <div className="text-sm font-medium text-gray-200 truncate">{camera}</div>
                            <div className="text-xs text-gray-400">
                              {stats.total_cases} cases, {stats.with_images} with images
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="text-sm font-medium text-green-400">
                              {stats.accuracy_rate.toFixed(1)}%
                            </div>
                            <div className="text-xs text-gray-500">accuracy</div>
                          </div>
                        </div>
                      ))}
                  </div>
                </div>

                {/* Problematic Cameras */}
                <div className="bg-dark-600 rounded-lg p-6 border border-gray-700">
                  <h3 className="font-semibold text-gray-100 mb-4 flex items-center">
                    <TrendingDown className="w-4 h-4 mr-2 text-red-400" />
                    Cameras Needing Attention
                  </h3>
                  <div className="space-y-3">
                    {getTopProblematicCameras().map(([camera, stats]: [string, any]) => (
                      <div key={camera} className="flex items-center justify-between">
                        <div className="flex-1 min-w-0">
                          <div className="text-sm font-medium text-gray-200 truncate">{camera}</div>
                          <div className="text-xs text-gray-400">
                            {stats.total_cases} cases, {stats.with_images} with images
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="text-sm font-medium text-red-400">
                            {stats.accuracy_rate.toFixed(1)}%
                          </div>
                          <div className="text-xs text-gray-500">accuracy</div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </CardBody>
          </Card>
        )}

        {/* Real-time Activity Feed */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          <Card className="surveillance-card">
            <CardHeader>
              <div className="text-gray-100 flex items-center space-x-2">
                <h3 className="text-lg font-semibold">Live Activity Feed</h3>
                {autoRefresh && (
                  <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                )}
              </div>
            </CardHeader>
            <CardBody>
              <div className="space-y-3 max-h-96 overflow-y-auto">
                {cases.slice(0, 5).map((violationCase: ViolationCase, index: number) => (
                  <div key={index} className="flex items-center space-x-3 p-3 bg-gray-800 rounded-lg">
                    <div className={`w-3 h-3 rounded-full ${
                      violationCase.image_classification === 'invalid' ? 'bg-red-400' : 'bg-green-400'
                    }`}></div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-100 truncate">
                        Case #{violationCase.case_int_id} - {violationCase.type_of_infringement}
                      </p>
                      <p className="text-xs text-gray-400">
                        Camera {violationCase.camera} • {
                          violationCase.alert_start_time
                            ? new Date(violationCase.alert_start_time).toLocaleTimeString()
                            : 'Unknown time'
                        }
                      </p>
                    </div>
                    <span className={`px-2 py-1 rounded text-xs font-medium ${
                      violationCase.image_classification === 'invalid'
                        ? 'bg-red-900 text-red-300'
                        : 'bg-green-900 text-green-300'
                    }`}>
                      {violationCase.image_classification === 'invalid' ? 'FP' : 'Valid'}
                    </span>
                  </div>
                ))}
                {cases.length === 0 && (
                  <div className="text-center py-8">
                    <p className="text-gray-400">No recent activity</p>
                  </div>
                )}
              </div>
            </CardBody>
          </Card>

          <Card className="surveillance-card">
            <CardHeader>
              <h3 className="text-lg font-semibold text-gray-100">Processing Status</h3>
            </CardHeader>
            <CardBody>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-gray-300">Analysis Engine</span>
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                    <span className="text-sm text-green-400">Online</span>
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-gray-300">Data Processing</span>
                  <div className="flex items-center space-x-2">
                    <div className={`w-2 h-2 rounded-full ${isProcessing ? 'bg-yellow-400 animate-pulse' : 'bg-green-400'}`}></div>
                    <span className={`text-sm ${isProcessing ? 'text-yellow-400' : 'text-green-400'}`}>
                      {isProcessing ? 'Processing' : 'Ready'}
                    </span>
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-gray-300">Auto-Refresh</span>
                  <div className="flex items-center space-x-2">
                    <div className={`w-2 h-2 rounded-full ${autoRefresh ? 'bg-green-400 animate-pulse' : 'bg-gray-500'}`}></div>
                    <span className={`text-sm ${autoRefresh ? 'text-green-400' : 'text-gray-500'}`}>
                      {autoRefresh ? 'Enabled' : 'Disabled'}
                    </span>
                  </div>
                </div>

                <div className="pt-4 border-t border-gray-700">
                  <div className="text-sm text-gray-400">
                    <p>Total Cases: {metrics?.total_csv_cases || 0}</p>
                    <p>False Positives: {metrics?.invalid_detections || 0}</p>
                    <p>False Positive Rate: {metrics?.false_positive_rate ? `${metrics.false_positive_rate.toFixed(1)}%` : 'N/A'}</p>
                  </div>
                </div>
              </div>
            </CardBody>
          </Card>
        </div>

        {/* AI Recommendations */}
        {patterns && (
          <Card className="surveillance-card mb-8">
            <CardHeader>
              <div className="flex items-center space-x-2">
                <Brain className="w-5 h-5 text-primary-400" />
                <h2 className="text-xl font-semibold text-gray-100">AI-Driven Recommendations</h2>
              </div>
              <p className="text-sm text-gray-400">
                {patterns.recommendations.length} recommendations generated with {(patterns.confidence_scores.overall * 100).toFixed(0)}% confidence
              </p>
            </CardHeader>
            <CardBody>
              <div className="space-y-4">
                {getHighPriorityRecommendations().map((rec, index) => (
                  <div key={index} className="bg-dark-600 rounded-lg p-6 border border-gray-700">
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex items-center space-x-3">
                        <div className="w-3 h-3 bg-red-400 rounded-full" />
                        <div>
                          <h3 className="font-semibold text-gray-100">{rec.title}</h3>
                          <span className="text-xs px-2 py-1 bg-red-900 text-red-200 rounded">
                            {rec.priority.toUpperCase()} PRIORITY
                          </span>
                        </div>
                      </div>
                      <span className="text-xs px-2 py-1 bg-gray-700 text-gray-300 rounded">
                        {rec.type}
                      </span>
                    </div>
                    <p className="text-gray-300 mb-3">{rec.description}</p>
                    <div className="bg-dark-700 rounded p-3">
                      <div className="text-xs text-gray-400 mb-1">Recommended Action:</div>
                      <div className="text-sm text-gray-200">{rec.action}</div>
                    </div>
                  </div>
                ))}
              </div>
            </CardBody>
          </Card>
        )}

        {/* System Status */}
        <Card className="surveillance-card">
          <CardHeader>
            <div className="flex items-center space-x-2">
              <Activity className="w-5 h-5 text-primary-400" />
              <h2 className="text-xl font-semibold text-gray-100">System Status</h2>
            </div>
          </CardHeader>
          <CardBody>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  <CheckCircle className="w-8 h-8 text-white" />
                </div>
                <h3 className="font-semibold text-gray-100 mb-2">Data Analysis Service</h3>
                <p className="text-sm text-green-400">Online & Processing</p>
              </div>
              
              <div className="text-center">
                <div className="w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Database className="w-8 h-8 text-white" />
                </div>
                <h3 className="font-semibold text-gray-100 mb-2">Data Sources</h3>
                <p className="text-sm text-blue-400">CSV & Images Loaded</p>
              </div>
              
              <div className="text-center">
                <div className="w-16 h-16 bg-purple-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Brain className="w-8 h-8 text-white" />
                </div>
                <h3 className="font-semibold text-gray-100 mb-2">AI Analysis Engine</h3>
                <p className="text-sm text-purple-400">Pattern Detection Active</p>
              </div>
            </div>
          </CardBody>
        </Card>
      </div>
    </div>
  );
};
