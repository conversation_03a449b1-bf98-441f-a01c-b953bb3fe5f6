import React from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  Shield, 
  AlertTriangle, 
  DollarSign, 
  TrendingDown, 
  TrendingUp, 
  Clock,
  Target,
  Zap,
  ArrowRight
} from 'lucide-react';
import '../styles/surveillance.css';

interface CrisisStatProps {
  number: string;
  label: string;
  description: string;
  icon: React.ReactNode;
  type?: 'critical' | 'warning' | 'info';
}

const CrisisStat: React.FC<CrisisStatProps> = ({ number, label, description, icon, type = 'critical' }) => (
  <div className={`crisis-stat ${type}`}>
    <div className="flex items-center justify-between mb-4">
      <div className="text-2xl">{icon}</div>
      <div className="crisis-number">{number}</div>
    </div>
    <h3 className="text-lg font-semibold text-white mb-2">{label}</h3>
    <p className="text-sm text-gray-400">{description}</p>
  </div>
);

interface SolutionMetricProps {
  number: string;
  label: string;
  description: string;
  icon: React.ReactNode;
  trend: 'up' | 'down';
}

const SolutionMetric: React.FC<SolutionMetricProps> = ({ number, label, description, icon, trend }) => (
  <div className="metric-card-surveillance">
    <div className="flex items-center justify-between mb-4">
      <div className="text-2xl text-blue-400">{icon}</div>
      <div className={`text-sm ${trend === 'up' ? 'text-green-400' : 'text-red-400'}`}>
        {trend === 'up' ? <TrendingUp className="w-4 h-4" /> : <TrendingDown className="w-4 h-4" />}
      </div>
    </div>
    <div className="metric-value-surveillance text-green-400">{number}</div>
    <div className="metric-label-surveillance">{label}</div>
    <p className="text-xs text-gray-500 mt-2">{description}</p>
  </div>
);

export const LandingPage: React.FC = () => {
  const navigate = useNavigate();

  const handleGetStarted = () => {
    navigate('/upload');
  };

  const handleViewDemo = () => {
    navigate('/dashboard');
  };

  return (
    <div className="min-h-screen bg-gray-900" style={{ background: 'var(--bg-primary)' }}>
      {/* Hero Section */}
      <div className="nav-surveillance">
        <div className="surveillance-container">
          <div className="nav">
            <div className="surveillance-logo">
              <div className="surveillance-logo-icon">
                <Shield className="w-6 h-6" />
              </div>
              <div>
                <div className="text-xl font-bold">AI-FARM</div>
                <div className="text-xs text-gray-400">False Positive Alert Reduction System</div>
              </div>
            </div>
            <div className="flex gap-4">
              <button 
                onClick={handleViewDemo}
                className="surveillance-btn surveillance-btn-primary"
              >
                View Demo
                <ArrowRight className="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="surveillance-container py-20">
        {/* Problem Statement */}
        <div className="text-center mb-16">
          <h1 className="text-5xl font-bold text-white mb-6">
            The VALO Alert Crisis
          </h1>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto mb-8">
            PSA VALO surveillance systems generate thousands of false positive alerts monthly, 
            overwhelming security teams and wasting critical resources. The current manual review 
            process is unsustainable and costly.
          </p>
          <div className="flex justify-center gap-4">
            <AlertTriangle className="w-8 h-8 text-red-500" />
            <span className="text-red-400 font-semibold">URGENT ACTION REQUIRED</span>
            <AlertTriangle className="w-8 h-8 text-red-500" />
          </div>
        </div>

        {/* Crisis Statistics */}
        <div className="crisis-stats">
          <CrisisStat
            number="17,268"
            label="Monthly Alerts"
            description="Total VALO alerts generated across all PSA terminals monthly"
            icon={<AlertTriangle className="w-8 h-8 text-red-500" />}
            type="critical"
          />
          <CrisisStat
            number="97%"
            label="False Positive Rate"
            description="Percentage of alerts that require no action, overwhelming security teams"
            icon={<Target className="w-8 h-8 text-red-500" />}
            type="critical"
          />
          <CrisisStat
            number="$517K"
            label="Annual Waste"
            description="Cost of manual review process for false positive alerts"
            icon={<DollarSign className="w-8 h-8 text-red-500" />}
            type="critical"
          />
          <CrisisStat
            number="24/7"
            label="Manual Review"
            description="Continuous human oversight required for alert validation"
            icon={<Clock className="w-8 h-8 text-orange-500" />}
            type="warning"
          />
        </div>

        {/* Solution Introduction */}
        <div className="text-center my-20">
          <div className="inline-flex items-center gap-4 mb-8">
            <div className="w-16 h-1 bg-gradient-to-r from-transparent to-blue-500"></div>
            <Zap className="w-12 h-12 text-blue-400" />
            <div className="w-16 h-1 bg-gradient-to-l from-transparent to-blue-500"></div>
          </div>
          <h2 className="text-4xl font-bold text-white mb-6">
            Introducing AI-FARM
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto mb-8">
            Our AI-powered False Positive Alert Reduction System uses advanced Vision Language Models 
            to automatically analyze and filter VALO alerts, dramatically reducing manual review workload 
            while maintaining security effectiveness.
          </p>
        </div>

        {/* Solution Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-16">
          <SolutionMetric
            number="70%"
            label="Reduction in False Positives"
            description="Automated filtering of non-actionable alerts"
            icon={<TrendingDown className="w-8 h-8" />}
            trend="down"
          />
          <SolutionMetric
            number="$351K"
            label="Annual Savings"
            description="Reduced manual review costs and improved efficiency"
            icon={<DollarSign className="w-8 h-8" />}
            trend="up"
          />
          <SolutionMetric
            number="3.7"
            label="Months Payback"
            description="Return on investment timeline"
            icon={<Clock className="w-8 h-8" />}
            trend="up"
          />
          <SolutionMetric
            number="99.2%"
            label="Accuracy Rate"
            description="VLM analysis precision for alert classification"
            icon={<Target className="w-8 h-8" />}
            trend="up"
          />
        </div>

        {/* Call to Action */}
        <div className="text-center">
          <div className="surveillance-card max-w-2xl mx-auto">
            <h3 className="text-2xl font-bold text-white mb-4">
              Ready to Transform Your Security Operations?
            </h3>
            <p className="text-gray-300 mb-8">
              Experience the power of AI-FARM with our interactive demo. Upload your VALO alert data 
              and see real-time false positive reduction in action.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button 
                onClick={handleGetStarted}
                className="surveillance-btn surveillance-btn-primary text-lg px-8 py-4"
              >
                Start Demo
                <ArrowRight className="w-5 h-5" />
              </button>
              <button 
                onClick={handleViewDemo}
                className="surveillance-btn bg-gray-700 text-white hover:bg-gray-600 text-lg px-8 py-4"
              >
                View Live Dashboard
                <Shield className="w-5 h-5" />
              </button>
            </div>
          </div>
        </div>

        {/* Features Preview */}
        <div className="mt-20 grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="surveillance-card text-center">
            <div className="text-4xl mb-4">🚀</div>
            <h4 className="text-lg font-semibold text-white mb-2">Real-time Processing</h4>
            <p className="text-gray-400 text-sm">
              Instant analysis of VALO alerts with live progress tracking and results
            </p>
          </div>
          <div className="surveillance-card text-center">
            <div className="text-4xl mb-4">🧠</div>
            <h4 className="text-lg font-semibold text-white mb-2">AI-Powered Analysis</h4>
            <p className="text-gray-400 text-sm">
              Advanced VLM technology for accurate alert classification and reasoning
            </p>
          </div>
          <div className="surveillance-card text-center">
            <div className="text-4xl mb-4">📊</div>
            <h4 className="text-lg font-semibold text-white mb-2">Comprehensive Insights</h4>
            <p className="text-gray-400 text-sm">
              Detailed analytics, ROI calculations, and performance metrics
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};
