import React, { useState, useEffect } from 'react';
import { 
  Database, 
  Eye, 
  Brain, 
  CheckCircle, 
  Activity, 
  Clock, 
  AlertTriangle,
  Zap,
  Terminal,
  Play,
  Pause
} from 'lucide-react';
import '../styles/surveillance.css';

interface ProcessingStage {
  id: string;
  name: string;
  icon: React.ReactNode;
  status: 'pending' | 'active' | 'complete' | 'error';
  progress: number;
  description: string;
}

interface ProcessingMetrics {
  totalAlerts: number;
  processed: number;
  falsePositivesFiltered: number;
  reviewRequired: number;
  processingSpeed: number;
  queueSize: number;
}

interface LogEntry {
  id: string;
  timestamp: string;
  level: 'info' | 'success' | 'warning' | 'error';
  message: string;
  details?: string;
}

export const MonitorPage: React.FC = () => {
  const [isProcessing, setIsProcessing] = useState(true);
  const [stages, setStages] = useState<ProcessingStage[]>([
    {
      id: 'validation',
      name: 'Data Validation',
      icon: <Database className="w-8 h-8" />,
      status: 'complete',
      progress: 100,
      description: 'Validating CSV data and image files'
    },
    {
      id: 'analysis',
      name: 'Image Analysis',
      icon: <Eye className="w-8 h-8" />,
      status: 'active',
      progress: 67,
      description: 'Processing VALO alert images'
    },
    {
      id: 'vlm',
      name: 'VLM Processing',
      icon: <Brain className="w-8 h-8" />,
      status: 'pending',
      progress: 0,
      description: 'AI analysis and classification'
    },
    {
      id: 'results',
      name: 'Results',
      icon: <CheckCircle className="w-8 h-8" />,
      status: 'pending',
      progress: 0,
      description: 'Generating final report'
    }
  ]);

  const [metrics, setMetrics] = useState<ProcessingMetrics>({
    totalAlerts: 1247,
    processed: 834,
    falsePositivesFiltered: 587,
    reviewRequired: 247,
    processingSpeed: 12.4,
    queueSize: 413
  });

  const [logs, setLogs] = useState<LogEntry[]>([
    {
      id: '1',
      timestamp: '14:23:45',
      level: 'info',
      message: 'Processing batch B-2024-0704-001',
      details: '1247 alerts loaded'
    },
    {
      id: '2',
      timestamp: '14:23:47',
      level: 'success',
      message: 'Data validation complete',
      details: 'All files validated successfully'
    },
    {
      id: '3',
      timestamp: '14:23:52',
      level: 'info',
      message: 'Image analysis started',
      details: 'Processing 834 alert images'
    },
    {
      id: '4',
      timestamp: '14:24:15',
      level: 'success',
      message: 'False positive detected: ALERT-P1-20240704-14:23:12',
      details: 'Confidence: 94.2% - Reason: Empty corridor, no person visible'
    },
    {
      id: '5',
      timestamp: '14:24:18',
      level: 'warning',
      message: 'Low confidence alert: ALERT-P2-20240704-14:23:45',
      details: 'Confidence: 67.8% - Flagged for manual review'
    }
  ]);

  // Simulate real-time updates
  useEffect(() => {
    if (!isProcessing) return;

    const interval = setInterval(() => {
      // Update processing progress
      setStages(prev => prev.map(stage => {
        if (stage.status === 'active' && stage.progress < 100) {
          const newProgress = Math.min(stage.progress + Math.random() * 5, 100);
          return { ...stage, progress: newProgress };
        }
        return stage;
      }));

      // Update metrics
      setMetrics(prev => ({
        ...prev,
        processed: Math.min(prev.processed + Math.floor(Math.random() * 3), prev.totalAlerts),
        falsePositivesFiltered: Math.min(prev.falsePositivesFiltered + Math.floor(Math.random() * 2), prev.processed),
        queueSize: Math.max(prev.queueSize - Math.floor(Math.random() * 2), 0)
      }));

      // Add new log entries occasionally
      if (Math.random() < 0.3) {
        const newLog: LogEntry = {
          id: Date.now().toString(),
          timestamp: new Date().toLocaleTimeString('en-US', { hour12: false }),
          level: Math.random() > 0.8 ? 'warning' : Math.random() > 0.6 ? 'success' : 'info',
          message: `Processing alert: ALERT-P${Math.floor(Math.random() * 3) + 1}-${Date.now()}`,
          details: `Confidence: ${(Math.random() * 40 + 60).toFixed(1)}%`
        };
        
        setLogs(prev => [newLog, ...prev.slice(0, 19)]); // Keep last 20 logs
      }
    }, 2000);

    return () => clearInterval(interval);
  }, [isProcessing]);

  const toggleProcessing = () => {
    setIsProcessing(!isProcessing);
  };

  const getLogLevelColor = (level: string) => {
    switch (level) {
      case 'success': return 'text-green-400';
      case 'warning': return 'text-orange-400';
      case 'error': return 'text-red-400';
      default: return 'text-blue-400';
    }
  };

  return (
    <div className="min-h-screen" style={{ background: 'var(--bg-primary)' }}>
      <div className="surveillance-container py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">Processing Monitor</h1>
            <p className="text-gray-400">Real-time VALO alert processing pipeline</p>
          </div>
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <div className={`status-dot ${isProcessing ? 'online' : 'offline'}`}></div>
              <span className="text-sm text-gray-300">
                {isProcessing ? 'Processing Active' : 'Processing Paused'}
              </span>
            </div>
            <button 
              onClick={toggleProcessing}
              className={`surveillance-btn ${isProcessing ? 'surveillance-btn-danger' : 'surveillance-btn-success'}`}
            >
              {isProcessing ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
              {isProcessing ? 'Pause' : 'Resume'}
            </button>
          </div>
        </div>

        {/* Processing Pipeline */}
        <div className="processing-pipeline mb-8">
          {stages.map((stage) => (
            <div 
              key={stage.id} 
              className={`pipeline-stage ${stage.status}`}
            >
              <div className="stage-icon text-blue-400">{stage.icon}</div>
              <h3 className="text-lg font-semibold text-white mb-2">{stage.name}</h3>
              <p className="text-sm text-gray-400 mb-4">{stage.description}</p>
              
              <div className="progress-bar">
                <div 
                  className="progress-fill" 
                  style={{ width: `${stage.progress}%` }}
                ></div>
              </div>
              <div className="text-sm text-gray-300 mt-2">{stage.progress}%</div>
            </div>
          ))}
        </div>

        {/* Metrics Grid */}
        <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-6 mb-8">
          <div className="metric-card-surveillance">
            <div className="metric-value-surveillance">{metrics.totalAlerts.toLocaleString()}</div>
            <div className="metric-label-surveillance">Total Alerts</div>
          </div>
          <div className="metric-card-surveillance">
            <div className="metric-value-surveillance text-blue-400">{metrics.processed.toLocaleString()}</div>
            <div className="metric-label-surveillance">Processed</div>
          </div>
          <div className="metric-card-surveillance">
            <div className="metric-value-surveillance text-green-400">{metrics.falsePositivesFiltered.toLocaleString()}</div>
            <div className="metric-label-surveillance">False Positives Filtered</div>
          </div>
          <div className="metric-card-surveillance">
            <div className="metric-value-surveillance text-orange-400">{metrics.reviewRequired.toLocaleString()}</div>
            <div className="metric-label-surveillance">Review Required</div>
          </div>
          <div className="metric-card-surveillance">
            <div className="metric-value-surveillance text-purple-400">{metrics.processingSpeed}</div>
            <div className="metric-label-surveillance">Alerts/sec</div>
          </div>
          <div className="metric-card-surveillance">
            <div className="metric-value-surveillance text-yellow-400">{metrics.queueSize.toLocaleString()}</div>
            <div className="metric-label-surveillance">Queue Size</div>
          </div>
        </div>

        {/* Live Processing Log */}
        <div className="surveillance-card">
          <div className="feed-header">
            <div className="flex items-center gap-3">
              <Terminal className="w-5 h-5 text-blue-400" />
              <h3 className="text-lg font-semibold text-white">Live Processing Log</h3>
            </div>
            <div className="live-indicator">
              <div className="status-dot online"></div>
              <span>LIVE</span>
            </div>
          </div>
          
          <div className="bg-black p-4 font-mono text-sm max-h-96 overflow-y-auto">
            {logs.map((log) => (
              <div key={log.id} className="mb-2 flex">
                <span className="text-gray-500 mr-3">[{log.timestamp}]</span>
                <span className={`mr-3 font-semibold ${getLogLevelColor(log.level)}`}>
                  {log.level.toUpperCase()}
                </span>
                <div className="flex-1">
                  <div className="text-gray-300">{log.message}</div>
                  {log.details && (
                    <div className="text-gray-500 text-xs mt-1 ml-4">└─ {log.details}</div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};
