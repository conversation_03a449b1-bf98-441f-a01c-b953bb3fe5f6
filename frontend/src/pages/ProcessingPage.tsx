import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { ArrowRight, RotateCcw, X } from 'lucide-react';
import { <PERSON><PERSON>, Card, CardHeader, CardBody, Alert } from '../components/ui';
import { ProcessingProgress } from '../components/dashboard';
import { ProcessingResultsChart, ProcessingTimeChart } from '../components/charts';
import { BatchProcessingResponse, ProcessingStatus } from '../types';
import { batchService } from '../services';
import toast from 'react-hot-toast';

export const ProcessingPage: React.FC = () => {
  const { batchId } = useParams<{ batchId: string }>();
  const navigate = useNavigate();
  const [batch, setBatch] = useState<BatchProcessingResponse | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [timelineData, setTimelineData] = useState<Array<{
    time: string;
    processed: number;
    cumulative: number;
  }>>([]);

  useEffect(() => {
    if (!batchId) {
      navigate('/');
      return;
    }

    // Initialize timeline data
    setTimelineData([
      { time: '0:00', processed: 0, cumulative: 0 },
    ]);
  }, [batchId, navigate]);

  const handleProcessingComplete = (completedBatch: BatchProcessingResponse) => {
    setBatch(completedBatch);
    toast.success('Processing completed successfully!');
  };

  const handleProcessingError = (errorMessage: string) => {
    setError(errorMessage);
    toast.error('Processing failed');
  };

  const handleCancelProcessing = async () => {
    if (!batchId || !batch) return;

    try {
      await batchService.cancelBatch(batchId);
      toast.success('Processing cancelled');
      navigate('/');
    } catch (error) {
      toast.error('Failed to cancel processing');
    }
  };

  const handleRetryProcessing = () => {
    setError(null);
    navigate('/upload');
  };

  const getProcessingStatusMessage = () => {
    if (!batch) return 'Initializing...';
    
    switch (batch.status) {
      case ProcessingStatus.PENDING:
        return 'Queued for processing. Your batch will start shortly.';
      case ProcessingStatus.PROCESSING:
        return 'AI analysis in progress. Processing violation images with computer vision.';
      case ProcessingStatus.COMPLETED:
        return 'Processing completed successfully! View your results and insights.';
      case ProcessingStatus.FAILED:
        return 'Processing encountered an error. Please review the details below.';
      default:
        return 'Status unknown';
    }
  };

  if (!batchId) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Card>
          <CardBody>
            <Alert variant="danger" title="Missing Batch ID">
              No batch ID provided. Please start a new processing job.
            </Alert>
            <div className="mt-4 text-center">
              <Button onClick={() => navigate('/')}>Go Home</Button>
            </div>
          </CardBody>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            Processing Your Data
          </h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            {getProcessingStatusMessage()}
          </p>
        </div>

        {/* Progress Steps */}
        <div className="mb-8">
          <div className="flex items-center justify-center space-x-8">
            <div className="flex items-center">
              <div className="bg-success-600 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-medium">
                ✓
              </div>
              <span className="ml-2 text-sm font-medium text-success-600">Upload Complete</span>
            </div>
            <div className="w-16 h-0.5 bg-success-300"></div>
            <div className="flex items-center">
              <div className={`rounded-full w-8 h-8 flex items-center justify-center text-sm font-medium ${
                batch?.status === ProcessingStatus.COMPLETED 
                  ? 'bg-success-600 text-white'
                  : batch?.status === ProcessingStatus.FAILED
                  ? 'bg-danger-600 text-white'
                  : 'bg-primary-600 text-white'
              }`}>
                {batch?.status === ProcessingStatus.COMPLETED ? '✓' : 
                 batch?.status === ProcessingStatus.FAILED ? '✗' : '2'}
              </div>
              <span className={`ml-2 text-sm font-medium ${
                batch?.status === ProcessingStatus.COMPLETED 
                  ? 'text-success-600'
                  : batch?.status === ProcessingStatus.FAILED
                  ? 'text-danger-600'
                  : 'text-primary-600'
              }`}>
                Processing
              </span>
            </div>
            <div className={`w-16 h-0.5 ${
              batch?.status === ProcessingStatus.COMPLETED ? 'bg-success-300' : 'bg-gray-300'
            }`}></div>
            <div className="flex items-center">
              <div className={`rounded-full w-8 h-8 flex items-center justify-center text-sm font-medium ${
                batch?.status === ProcessingStatus.COMPLETED
                  ? 'bg-primary-600 text-white'
                  : 'bg-gray-300 text-gray-500'
              }`}>
                3
              </div>
              <span className={`ml-2 text-sm font-medium ${
                batch?.status === ProcessingStatus.COMPLETED
                  ? 'text-primary-600'
                  : 'text-gray-500'
              }`}>
                View Results
              </span>
            </div>
          </div>
        </div>

        {/* Main Processing Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          {/* Processing Progress */}
          <div className="lg:col-span-2">
            <ProcessingProgress
              batchId={batchId}
              onComplete={handleProcessingComplete}
              onError={handleProcessingError}
            />
          </div>

          {/* Real-time Charts */}
          {batch && batch.status !== ProcessingStatus.PENDING && (
            <>
              <ProcessingResultsChart
                data={{
                  totalAlerts: batch.total_cases,
                  falsePositivesFiltered: batch.summary?.false_positives_filtered || 0,
                  alertsRequiringReview: batch.total_cases - (batch.summary?.false_positives_filtered || 0),
                }}
              />
              
              <ProcessingTimeChart
                data={timelineData}
              />
            </>
          )}
        </div>

        {/* Status-specific content */}
        {batch?.status === ProcessingStatus.COMPLETED && (
          <Card className="mb-8">
            <CardHeader>
              <h2 className="text-xl font-semibold text-success-600">
                🎉 Processing Completed Successfully!
              </h2>
            </CardHeader>
            <CardBody>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                <div className="text-center">
                  <p className="text-3xl font-bold text-gray-900">
                    {batch.total_cases.toLocaleString()}
                  </p>
                  <p className="text-sm text-gray-500">Total Cases Processed</p>
                </div>
                <div className="text-center">
                  <p className="text-3xl font-bold text-success-600">
                    {(batch.summary?.false_positives_filtered || 0).toLocaleString()}
                  </p>
                  <p className="text-sm text-gray-500">False Positives Filtered</p>
                </div>
                <div className="text-center">
                  <p className="text-3xl font-bold text-primary-600">
                    {((batch.summary?.false_positives_filtered || 0) / batch.total_cases * 100).toFixed(1)}%
                  </p>
                  <p className="text-sm text-gray-500">Filter Rate</p>
                </div>
              </div>
              
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button
                  variant="primary"
                  size="lg"
                  onClick={() => navigate(`/results/${batchId}`)}
                  rightIcon={ArrowRight}
                >
                  View Detailed Results
                </Button>
                <Button
                  variant="outline"
                  size="lg"
                  onClick={() => navigate(`/roi/${batchId}`)}
                >
                  Calculate ROI
                </Button>
              </div>
            </CardBody>
          </Card>
        )}

        {batch?.status === ProcessingStatus.FAILED && (
          <Card className="mb-8">
            <CardBody>
              <Alert variant="danger" title="Processing Failed">
                <div className="space-y-4">
                  <p>
                    {error || batch.summary?.error_message || 'An unknown error occurred during processing.'}
                  </p>
                  <div className="flex flex-col sm:flex-row gap-3">
                    <Button
                      variant="primary"
                      onClick={handleRetryProcessing}
                      leftIcon={RotateCcw}
                    >
                      Try Again
                    </Button>
                    <Button
                      variant="outline"
                      onClick={() => navigate('/')}
                    >
                      Go Home
                    </Button>
                  </div>
                </div>
              </Alert>
            </CardBody>
          </Card>
        )}

        {/* Action Buttons */}
        {batch && batch.status === ProcessingStatus.PROCESSING && (
          <div className="text-center">
            <Button
              variant="outline"
              onClick={handleCancelProcessing}
              leftIcon={X}
              className="text-danger-600 border-danger-300 hover:bg-danger-50"
            >
              Cancel Processing
            </Button>
          </div>
        )}

        {/* Information Panel */}
        <Card>
          <CardHeader>
            <h3 className="text-lg font-medium text-gray-900">What's Happening</h3>
          </CardHeader>
          <CardBody>
            <div className="space-y-4">
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0 w-6 h-6 bg-primary-100 rounded-full flex items-center justify-center mt-0.5">
                  <span className="text-xs font-medium text-primary-600">1</span>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-900">Data Validation</h4>
                  <p className="text-sm text-gray-600">
                    Validating CSV structure and checking for required fields
                  </p>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0 w-6 h-6 bg-primary-100 rounded-full flex items-center justify-center mt-0.5">
                  <span className="text-xs font-medium text-primary-600">2</span>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-900">Image Processing</h4>
                  <p className="text-sm text-gray-600">
                    Loading and analyzing violation images with computer vision
                  </p>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0 w-6 h-6 bg-primary-100 rounded-full flex items-center justify-center mt-0.5">
                  <span className="text-xs font-medium text-primary-600">3</span>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-900">AI Analysis</h4>
                  <p className="text-sm text-gray-600">
                    Running VLM analysis to detect violations and calculate confidence scores
                  </p>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0 w-6 h-6 bg-primary-100 rounded-full flex items-center justify-center mt-0.5">
                  <span className="text-xs font-medium text-primary-600">4</span>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-900">Results Generation</h4>
                  <p className="text-sm text-gray-600">
                    Compiling final recommendations and generating insights
                  </p>
                </div>
              </div>
            </div>
          </CardBody>
        </Card>
      </div>
    </div>
  );
};