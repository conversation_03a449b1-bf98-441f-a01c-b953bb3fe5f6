import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  DollarSign,
  TrendingUp,
  Clock,
  Calculator,
  Download,
  Settings,
  ArrowRight
} from 'lucide-react';
import { 
  <PERSON><PERSON>, 
  Card, 
  CardHeader, 
  CardBody, 
  MetricGrid,
  ComparisonMetricCard,
  Alert,
  LoadingSpinner
} from '../components/ui';
import { ROIProjectionChart } from '../components/charts';
import { ROIProjection, DemoConfig } from '../types';
import { metricsService } from '../services';
import toast from 'react-hot-toast';

export const ROIPage: React.FC = () => {
  const { batchId } = useParams<{ batchId: string }>();
  const navigate = useNavigate();
  
  const [roiData, setRoiData] = useState<ROIProjection | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [config, setConfig] = useState<DemoConfig>({
    customerName: 'Valued Customer',
    monthlyAlerts: 17268,
    hourlyRate: 50,
    implementationCost: 225000,
    baselineFilterRate: 3.0,
  });
  const [isConfigOpen, setIsConfigOpen] = useState(false);

  // Generate demo ROI data
  const generateDemoROIData = (): ROIProjection & { comparison: any } => {
    const monthlyAlerts = config.monthlyAlerts || 17268;
    const hourlyRate = config.hourlyRate || 50;
    const implementationCost = config.implementationCost || 225000;
    const baselineFilterRate = config.baselineFilterRate || 3.0;
    const aiFilterRate = 15.8; // AI-FARM's demonstrated filter rate

    // Calculate current state (baseline)
    const currentAlertsFiltered = Math.round(monthlyAlerts * (baselineFilterRate / 100));
    const currentAlertsToReview = monthlyAlerts - currentAlertsFiltered;
    const currentReviewHours = currentAlertsToReview * 0.5; // 30 minutes per alert
    const currentMonthlyCost = currentReviewHours * hourlyRate;

    // Calculate AI-FARM state
    const aiAlertsFiltered = Math.round(monthlyAlerts * (aiFilterRate / 100));
    const aiAlertsToReview = monthlyAlerts - aiAlertsFiltered;
    const aiReviewHours = aiAlertsToReview * 0.5;
    const aiMonthlyCost = aiReviewHours * hourlyRate;

    // Calculate savings and ROI
    const monthlyTimeSaved = currentReviewHours - aiReviewHours;
    const monthlyCostSavings = currentMonthlyCost - aiMonthlyCost;
    const annualCostSavings = monthlyCostSavings * 12;
    const workloadReduction = ((currentAlertsToReview - aiAlertsToReview) / currentAlertsToReview) * 100;
    const roiPercentage = ((annualCostSavings - implementationCost) / implementationCost) * 100;
    const paybackMonths = implementationCost / monthlyCostSavings;

    return {
      batch_id: 'demo-batch',
      customer_parameters: {
        monthly_alerts: monthlyAlerts,
        hourly_rate: hourlyRate,
        implementation_cost: implementationCost,
      },
      demo_performance: {
        sample_size: 2847,
        filter_rate_percentage: aiFilterRate,
        avg_confidence_score: 87.3,
      },
      projected_impact: {
        monthly_false_positives_filtered: aiAlertsFiltered - currentAlertsFiltered,
        monthly_time_saved_hours: monthlyTimeSaved,
        monthly_cost_savings: monthlyCostSavings,
        annual_cost_savings: annualCostSavings,
        workload_reduction_percentage: workloadReduction,
      },
      roi_analysis: {
        implementation_cost: implementationCost,
        annual_net_benefit: annualCostSavings - (implementationCost * 0.1), // 10% annual maintenance
        roi_percentage: roiPercentage,
        payback_period_months: paybackMonths,
        break_even_point: `Break even in ${paybackMonths.toFixed(1)} months`,
      },
      comparison: {
        before: {
          monthly_alerts_to_review: currentAlertsToReview,
          monthly_review_hours: currentReviewHours,
          monthly_cost: currentMonthlyCost,
        },
        after: {
          monthly_alerts_to_review: aiAlertsToReview,
          monthly_review_hours: aiReviewHours,
          monthly_cost: aiMonthlyCost,
        },
      },
    };
  };

  useEffect(() => {
    const fetchROIData = async () => {
      if (!batchId) {
        // Generate demo ROI data when no batchId is provided
        const demoData = generateDemoROIData();
        setRoiData(demoData);
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        const data = await metricsService.calculateROI(batchId, {
          monthlyAlerts: config.monthlyAlerts || 17268,
          hourlyRate: config.hourlyRate || 50,
          implementationCost: config.implementationCost || 225000,
        });
        setRoiData(data);
        setError(null);
      } catch (err) {
        const errorMsg = err instanceof Error ? err.message : 'Failed to calculate ROI';
        setError(errorMsg);
        toast.error(errorMsg);
      } finally {
        setLoading(false);
      }
    };

    fetchROIData();
  }, [batchId, navigate, config]);

  const handleConfigUpdate = async () => {
    try {
      setLoading(true);

      if (!batchId) {
        // Update demo data with new configuration
        const demoData = generateDemoROIData();
        setRoiData(demoData);
      } else {
        // Update with real API data
        const data = await metricsService.calculateROI(batchId, {
          monthlyAlerts: config.monthlyAlerts || 17268,
          hourlyRate: config.hourlyRate || 50,
          implementationCost: config.implementationCost || 225000,
        });
        setRoiData(data);
      }

      setIsConfigOpen(false);
      toast.success('ROI calculations updated');
    } catch (err) {
      toast.error('Failed to update calculations');
    } finally {
      setLoading(false);
    }
  };

  const generateProjectionData = () => {
    if (!roiData) return [];
    
    const annualSavings = roiData.projected_impact.annual_cost_savings;
    const implementationCost = roiData.roi_analysis.implementation_cost;
    const annualMaintenanceCost = implementationCost * 0.1; // 10% maintenance
    
    return Array.from({ length: 5 }, (_, index) => {
      const year = index + 1;
      const costs = year === 1 ? implementationCost + annualMaintenanceCost : annualMaintenanceCost;
      const savings = annualSavings;
      const netBenefit = savings - costs;
      const cumulativeBenefit = year === 1 
        ? netBenefit 
        : (annualSavings - annualMaintenanceCost) * (year - 1) + netBenefit;
      
      return {
        year,
        costs,
        savings,
        netBenefit,
        cumulativeBenefit,
      };
    });
  };

  const handleExportROI = async () => {
    if (!batchId || !roiData) return;
    
    try {
      const exportData = {
        customer: config.customerName,
        batch_id: batchId,
        generated_at: new Date().toISOString(),
        configuration: config,
        roi_analysis: roiData,
        projection_data: generateProjectionData(),
      };
      
      const blob = new Blob([JSON.stringify(exportData, null, 2)], {
        type: 'application/json',
      });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `ai-farm-roi-analysis-${batchId}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      
      toast.success('ROI analysis exported successfully');
    } catch (error) {
      toast.error('Failed to export ROI analysis');
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-4">Calculating ROI...</h1>
            <LoadingSpinner size="lg" />
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <Card>
            <CardBody>
              <Alert variant="danger" title="Failed to Calculate ROI">
                {error}
              </Alert>
              <div className="mt-4 text-center space-x-4">
                <Button onClick={() => window.location.reload()}>
                  Retry
                </Button>
                <Button variant="outline" onClick={() => navigate('/')}>
                  Go Home
                </Button>
              </div>
            </CardBody>
          </Card>
        </div>
      </div>
    );
  }

  if (!roiData) return null;

  const keyMetrics = [
    {
      title: 'Annual Cost Savings',
      value: `$${roiData.projected_impact.annual_cost_savings.toLocaleString()}`,
      subtitle: 'Projected yearly savings',
      icon: DollarSign,
      color: 'success' as const,
      trend: {
        value: roiData.roi_analysis.roi_percentage,
        direction: 'up' as const,
        label: 'ROI',
      },
    },
    {
      title: 'ROI Percentage',
      value: `${roiData.roi_analysis.roi_percentage.toFixed(0)}%`,
      subtitle: 'Return on investment',
      icon: TrendingUp,
      color: 'primary' as const,
    },
    {
      title: 'Payback Period',
      value: `${roiData.roi_analysis.payback_period_months.toFixed(1)} mo`,
      subtitle: roiData.roi_analysis.break_even_point,
      icon: Clock,
      color: 'warning' as const,
    },
    {
      title: 'Monthly Time Saved',
      value: `${roiData.projected_impact.monthly_time_saved_hours.toFixed(0)} hrs`,
      subtitle: `${roiData.projected_impact.workload_reduction_percentage.toFixed(1)}% workload reduction`,
      icon: Clock,
      color: 'neutral' as const,
    },
  ];

  return (
    <div className="min-h-screen bg-dark-500 bg-grid-pattern py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-100 mb-2">
              ROI Analysis
            </h1>
            <p className="text-lg text-gray-400">
              Financial impact and return on investment for {config.customerName}
            </p>
          </div>
          
          <div className="flex space-x-4">
            <Button
              variant="outline"
              onClick={() => setIsConfigOpen(true)}
              leftIcon={Settings}
            >
              Configure Parameters
            </Button>
            <Button
              variant="outline"
              onClick={handleExportROI}
              leftIcon={Download}
            >
              Export Analysis
            </Button>
          </div>
        </div>

        {/* Configuration Panel */}
        {isConfigOpen && (
          <Card className="mb-8">
            <CardHeader>
              <h2 className="text-xl font-semibold text-gray-900">ROI Configuration</h2>
              <p className="text-sm text-gray-600">
                Adjust parameters to customize the ROI calculation for your organization
              </p>
            </CardHeader>
            <CardBody>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Customer Name
                  </label>
                  <input
                    type="text"
                    value={config.customerName}
                    onChange={(e) => setConfig(prev => ({ ...prev, customerName: e.target.value }))}
                    className="form-input"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Monthly Alert Volume
                  </label>
                  <input
                    type="number"
                    value={config.monthlyAlerts}
                    onChange={(e) => setConfig(prev => ({ ...prev, monthlyAlerts: parseInt(e.target.value) }))}
                    className="form-input"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Hourly Rate ($)
                  </label>
                  <input
                    type="number"
                    value={config.hourlyRate}
                    onChange={(e) => setConfig(prev => ({ ...prev, hourlyRate: parseFloat(e.target.value) }))}
                    className="form-input"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Implementation Cost ($)
                  </label>
                  <input
                    type="number"
                    value={config.implementationCost}
                    onChange={(e) => setConfig(prev => ({ ...prev, implementationCost: parseFloat(e.target.value) }))}
                    className="form-input"
                  />
                </div>
              </div>
              
              <div className="flex justify-end space-x-4 mt-6">
                <Button
                  variant="outline"
                  onClick={() => setIsConfigOpen(false)}
                >
                  Cancel
                </Button>
                <Button
                  variant="primary"
                  onClick={handleConfigUpdate}
                  leftIcon={Calculator}
                >
                  Update Calculations
                </Button>
              </div>
            </CardBody>
          </Card>
        )}

        {/* Key Metrics */}
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">Financial Impact Summary</h2>
          <MetricGrid metrics={keyMetrics} columns={4} />
        </div>

        {/* ROI Highlight */}
        <Alert variant="success" title="Excellent ROI Potential" className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              Based on your parameters, AI-FARM offers a strong return on investment with 
              {` ${roiData.roi_analysis.roi_percentage.toFixed(0)}% ROI`} and payback in 
              {` ${roiData.roi_analysis.break_even_point.toLowerCase()}`}.
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => navigate(`/insights/${batchId}`)}
              rightIcon={ArrowRight}
            >
              View Implementation Plan
            </Button>
          </div>
        </Alert>

        {/* Comparison Cards */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
          <ComparisonMetricCard
            title="Monthly Alert Review"
            before={{
              label: 'Current State',
              value: roiData.comparison.before.monthly_alerts_to_review.toLocaleString(),
            }}
            after={{
              label: 'With AI-FARM',
              value: roiData.comparison.after.monthly_alerts_to_review.toLocaleString(),
            }}
            improvement={{
              value: roiData.projected_impact.workload_reduction_percentage,
              direction: 'down',
              label: 'reduction',
            }}
            icon={Calculator}
          />
          
          <ComparisonMetricCard
            title="Monthly Review Hours"
            before={{
              label: 'Current State',
              value: `${roiData.comparison.before.monthly_review_hours.toFixed(0)} hrs`,
            }}
            after={{
              label: 'With AI-FARM',
              value: `${roiData.comparison.after.monthly_review_hours.toFixed(0)} hrs`,
            }}
            improvement={{
              value: ((roiData.comparison.before.monthly_review_hours - roiData.comparison.after.monthly_review_hours) / roiData.comparison.before.monthly_review_hours) * 100,
              direction: 'down',
              label: 'time saved',
            }}
            icon={Clock}
          />
          
          <ComparisonMetricCard
            title="Monthly Operational Cost"
            before={{
              label: 'Current State',
              value: `$${roiData.comparison.before.monthly_cost.toLocaleString()}`,
            }}
            after={{
              label: 'With AI-FARM',
              value: `$${roiData.comparison.after.monthly_cost.toLocaleString()}`,
            }}
            improvement={{
              value: ((roiData.comparison.before.monthly_cost - roiData.comparison.after.monthly_cost) / roiData.comparison.before.monthly_cost) * 100,
              direction: 'down',
              label: 'cost reduction',
            }}
            icon={DollarSign}
          />
        </div>

        {/* ROI Projection Chart */}
        <ROIProjectionChart
          data={generateProjectionData()}
          className="mb-8"
        />

        {/* Detailed Breakdown */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          {/* Cost Breakdown */}
          <Card>
            <CardHeader>
              <h3 className="text-xl font-semibold text-gray-900">Cost Breakdown</h3>
            </CardHeader>
            <CardBody>
              <div className="space-y-4">
                <div className="flex justify-between items-center py-2 border-b border-gray-200">
                  <span className="text-gray-600">Implementation Cost</span>
                  <span className="font-semibold">${roiData.roi_analysis.implementation_cost.toLocaleString()}</span>
                </div>
                <div className="flex justify-between items-center py-2 border-b border-gray-200">
                  <span className="text-gray-600">Annual Maintenance (10%)</span>
                  <span className="font-semibold">${(roiData.roi_analysis.implementation_cost * 0.1).toLocaleString()}</span>
                </div>
                <div className="flex justify-between items-center py-2 border-b border-gray-200">
                  <span className="text-gray-600">Training & Setup</span>
                  <span className="font-semibold">Included</span>
                </div>
                <div className="flex justify-between items-center py-2 font-semibold text-lg">
                  <span>Total Year 1 Investment</span>
                  <span>${(roiData.roi_analysis.implementation_cost * 1.1).toLocaleString()}</span>
                </div>
              </div>
            </CardBody>
          </Card>

          {/* Savings Breakdown */}
          <Card>
            <CardHeader>
              <h3 className="text-xl font-semibold text-gray-900">Annual Savings</h3>
            </CardHeader>
            <CardBody>
              <div className="space-y-4">
                <div className="flex justify-between items-center py-2 border-b border-gray-200">
                  <span className="text-gray-600">Time Savings</span>
                  <span className="font-semibold">${(roiData.projected_impact.monthly_time_saved_hours * 12 * (config.hourlyRate || 50)).toLocaleString()}</span>
                </div>
                <div className="flex justify-between items-center py-2 border-b border-gray-200">
                  <span className="text-gray-600">Efficiency Gains</span>
                  <span className="font-semibold">${(roiData.projected_impact.annual_cost_savings * 0.1).toLocaleString()}</span>
                </div>
                <div className="flex justify-between items-center py-2 border-b border-gray-200">
                  <span className="text-gray-600">Reduced Alert Fatigue</span>
                  <span className="font-semibold">Qualitative</span>
                </div>
                <div className="flex justify-between items-center py-2 font-semibold text-lg text-success-600">
                  <span>Total Annual Savings</span>
                  <span>${roiData.projected_impact.annual_cost_savings.toLocaleString()}</span>
                </div>
              </div>
            </CardBody>
          </Card>
        </div>

        {/* Demo Performance Context */}
        <Card className="mb-8">
          <CardHeader>
            <h3 className="text-xl font-semibold text-gray-900">Demo Performance Context</h3>
            <p className="text-sm text-gray-600">
              These projections are based on the actual performance demonstrated with your data
            </p>
          </CardHeader>
          <CardBody>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <p className="text-3xl font-bold text-primary-600">
                  {roiData.demo_performance.sample_size.toLocaleString()}
                </p>
                <p className="text-sm text-gray-500">Cases Analyzed in Demo</p>
              </div>
              <div className="text-center">
                <p className="text-3xl font-bold text-success-600">
                  {roiData.demo_performance.filter_rate_percentage.toFixed(1)}%
                </p>
                <p className="text-sm text-gray-500">Achieved Filter Rate</p>
              </div>
              <div className="text-center">
                <p className="text-3xl font-bold text-warning-600">
                  {(roiData.demo_performance.avg_confidence_score * 100).toFixed(1)}%
                </p>
                <p className="text-sm text-gray-500">Average Confidence</p>
              </div>
            </div>
          </CardBody>
        </Card>

        {/* Call to Action */}
        <Card className="bg-gradient-to-r from-primary-600 to-primary-700 text-white">
          <CardBody>
            <div className="text-center">
              <h3 className="text-2xl font-bold mb-4">Ready to Implement AI-FARM?</h3>
              <p className="text-primary-100 mb-6 max-w-2xl mx-auto">
                With a {roiData.roi_analysis.roi_percentage.toFixed(0)}% ROI and payback in 
                {` ${roiData.roi_analysis.break_even_point.toLowerCase()}`}, AI-FARM represents 
                a compelling investment in operational efficiency.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button
                  variant="outline"
                  size="lg"
                  className="bg-white text-primary-700 hover:bg-primary-50"
                  onClick={() => navigate(`/insights/${batchId}`)}
                  rightIcon={ArrowRight}
                >
                  View Implementation Plan
                </Button>
                <Button
                  variant="secondary"
                  size="lg"
                  className="bg-primary-700 text-white hover:bg-primary-800"
                  onClick={handleExportROI}
                  leftIcon={Download}
                >
                  Download ROI Report
                </Button>
              </div>
            </div>
          </CardBody>
        </Card>
      </div>
    </div>
  );
};