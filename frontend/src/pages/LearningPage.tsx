import React, { useState, useEffect, useRef } from 'react';
import {
  Brain,
  TrendingUp,
  Target,
  Zap,
  Activity,
  BarChart3,
  Clock,
  CheckCircle,
  AlertTriangle,
  DollarSign,
  Settings,
  Eye,
  Lightbulb,
  Database,
  Cpu,
  RefreshCw
} from 'lucide-react';
import { Card, CardHeader, CardBody } from '../components/ui/Card';
import { Button } from '../components/ui/Button';
import '../styles/surveillance.css';

interface LearningMetrics {
  currentAccuracy: number;
  patternsDetected: number;
  optimizationsCompleted: number;
  confidenceScore: number;
  businessValue: number;
  processingSpeed: number;
  learningCycle: number;
}

interface LearningProgress {
  dataCollection: { status: 'complete' | 'active' | 'pending'; progress: number };
  patternAnalysis: { status: 'complete' | 'active' | 'pending'; progress: number };
  optimization: { status: 'complete' | 'active' | 'pending'; progress: number };
  continuousLearning: { status: 'complete' | 'active' | 'pending'; progress: number };
}

interface PatternDetection {
  id: string;
  type: 'critical' | 'temporal' | 'structural' | 'operational';
  title: string;
  description: string;
  confidence: number;
  impact: string;
  status: 'detected' | 'validated' | 'implemented';
  businessValue: number;
}

export const LearningPage: React.FC = () => {
  const [metrics, setMetrics] = useState<LearningMetrics>({
    currentAccuracy: 84.7,
    patternsDetected: 23,
    optimizationsCompleted: 15,
    confidenceScore: 8.5,
    businessValue: 58.5,
    processingSpeed: 450,
    learningCycle: 3
  });

  const [learningProgress, setLearningProgress] = useState<LearningProgress>({
    dataCollection: { status: 'complete', progress: 100 },
    patternAnalysis: { status: 'complete', progress: 100 },
    optimization: { status: 'active', progress: 67 },
    continuousLearning: { status: 'pending', progress: 0 }
  });

  const [patterns, setPatterns] = useState<PatternDetection[]>([
    {
      id: 'critical-001',
      type: 'critical',
      title: 'Critical Equipment Pattern',
      description: 'Quay Crane OC-20 False Positive Hotspot',
      confidence: 94.2,
      impact: 'Reduces false alerts by 23% at Terminal P1',
      status: 'implemented',
      businessValue: 12.3
    },
    {
      id: 'temporal-001', 
      type: 'temporal',
      title: 'Temporal Pattern',
      description: 'Sunrise False Positive Surge',
      confidence: 89.7,
      impact: 'Optimizes detection during 06:00-08:00 window',
      status: 'validated',
      businessValue: 8.7
    },
    {
      id: 'structural-001',
      type: 'structural', 
      title: 'Structural Pattern',
      description: 'Container Stacking Confusion',
      confidence: 91.5,
      impact: 'Improves container yard accuracy by 18%',
      status: 'detected',
      businessValue: 15.2
    }
  ]);

  const [isLearning, setIsLearning] = useState(false);
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  // Simulate real-time learning
  useEffect(() => {
    if (isLearning) {
      intervalRef.current = setInterval(() => {
        setMetrics(prev => ({
          ...prev,
          currentAccuracy: Math.min(99.9, prev.currentAccuracy + (Math.random() * 0.1)),
          patternsDetected: prev.patternsDetected + (Math.random() > 0.8 ? 1 : 0),
          processingSpeed: prev.processingSpeed + Math.floor((Math.random() - 0.5) * 20)
        }));
        setLastUpdate(new Date());
      }, 2000);
    } else {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [isLearning]);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'complete': return <CheckCircle className="w-4 h-4 text-green-400" />;
      case 'active': return <RefreshCw className="w-4 h-4 text-blue-400 animate-spin" />;
      case 'pending': return <Clock className="w-4 h-4 text-gray-400" />;
      default: return <Clock className="w-4 h-4 text-gray-400" />;
    }
  };

  const getPatternTypeColor = (type: string) => {
    switch (type) {
      case 'critical': return 'border-red-500 bg-red-900/20';
      case 'temporal': return 'border-blue-500 bg-blue-900/20';
      case 'structural': return 'border-yellow-500 bg-yellow-900/20';
      case 'operational': return 'border-green-500 bg-green-900/20';
      default: return 'border-gray-500 bg-gray-900/20';
    }
  };

  const getPatternIcon = (type: string) => {
    switch (type) {
      case 'critical': return <AlertTriangle className="w-5 h-5 text-red-400" />;
      case 'temporal': return <Clock className="w-5 h-5 text-blue-400" />;
      case 'structural': return <Database className="w-5 h-5 text-yellow-400" />;
      case 'operational': return <Settings className="w-5 h-5 text-green-400" />;
      default: return <Brain className="w-5 h-5 text-gray-400" />;
    }
  };

  return (
    <div className="min-h-screen" style={{ background: 'var(--bg-primary)' }}>
      <div className="surveillance-container py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <div className="flex items-center space-x-3 mb-2">
              <Brain className="w-8 h-8 text-primary-400" />
              <h1 className="text-3xl font-bold text-white">Auto-Learning Intelligence System</h1>
            </div>
            <p className="text-gray-400">Real-time AI learning and pattern detection for VALO false positive reduction</p>
          </div>
          <div className="flex items-center space-x-4">
            <div className="text-right">
              <p className="text-xs text-gray-500 uppercase tracking-wider">Learning Cycle</p>
              <p className="text-lg font-bold text-primary-400">Cycle #{metrics.learningCycle}</p>
            </div>
            <Button
              variant={isLearning ? "secondary" : "primary"}
              onClick={() => setIsLearning(!isLearning)}
              className="flex items-center space-x-2"
            >
              {isLearning ? <RefreshCw className="w-4 h-4 animate-spin" /> : <Zap className="w-4 h-4" />}
              <span>{isLearning ? 'Learning Active' : 'Start Learning'}</span>
            </Button>
          </div>
        </div>

        {/* Key Metrics */}
        <div className="surveillance-grid grid-cols-1 md:grid-cols-4 mb-8">
          <div className="metric-card-surveillance metric-primary">
            <div className="flex items-start justify-between">
              <div>
                <p className="metric-label-surveillance">Current Accuracy</p>
                <p className="metric-value-surveillance">{metrics.currentAccuracy.toFixed(1)}%</p>
                <p className="text-xs text-gray-500 mt-1">Continuously improving</p>
              </div>
              <Target className="w-8 h-8 text-primary-500 opacity-50" />
            </div>
          </div>

          <div className="metric-card-surveillance metric-success">
            <div className="flex items-start justify-between">
              <div>
                <p className="metric-label-surveillance">Patterns Detected</p>
                <p className="metric-value-surveillance">{metrics.patternsDetected}</p>
                <p className="text-xs text-gray-500 mt-1">In this session</p>
              </div>
              <Eye className="w-8 h-8 text-green-500 opacity-50" />
            </div>
          </div>

          <div className="metric-card-surveillance metric-warning">
            <div className="flex items-start justify-between">
              <div>
                <p className="metric-label-surveillance">Optimizations Completed</p>
                <p className="metric-value-surveillance">{metrics.optimizationsCompleted}</p>
                <p className="text-xs text-gray-500 mt-1">Threshold adjustments</p>
              </div>
              <Settings className="w-8 h-8 text-yellow-500 opacity-50" />
            </div>
          </div>

          <div className="metric-card-surveillance metric-info">
            <div className="flex items-start justify-between">
              <div>
                <p className="metric-label-surveillance">Confidence Score Savings</p>
                <p className="metric-value-surveillance">${metrics.confidenceScore}K</p>
                <p className="text-xs text-gray-500 mt-1">Operational efficiency</p>
              </div>
              <DollarSign className="w-8 h-8 text-blue-500 opacity-50" />
            </div>
          </div>
        </div>

        {/* Learning Process Progress */}
        <Card className="surveillance-card mb-8">
          <CardHeader>
            <div className="flex items-center space-x-2">
              <Activity className="w-5 h-5 text-primary-400" />
              <h2 className="text-xl font-semibold text-gray-100">Learning Process Progress</h2>
            </div>
            <p className="text-sm text-gray-400">
              Real-time monitoring of AI-FARM's learning pipeline stages
            </p>
          </CardHeader>
          <CardBody>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              {Object.entries(learningProgress).map(([key, stage]) => (
                <div key={key} className="bg-dark-600 rounded-lg p-6 border border-gray-700">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center space-x-2">
                      {getStatusIcon(stage.status)}
                      <h3 className="font-semibold text-gray-200 capitalize">
                        {key.replace(/([A-Z])/g, ' $1').trim()}
                      </h3>
                    </div>
                    <span className="text-xs text-gray-400 uppercase tracking-wider">
                      {stage.status}
                    </span>
                  </div>
                  <div className="mb-3">
                    <div className="flex justify-between text-sm mb-1">
                      <span className="text-gray-400">Progress</span>
                      <span className="text-gray-300">{stage.progress}%</span>
                    </div>
                    <div className="w-full bg-gray-700 rounded-full h-2">
                      <div
                        className={`h-2 rounded-full transition-all duration-500 ${
                          stage.status === 'complete' ? 'bg-green-500' :
                          stage.status === 'active' ? 'bg-blue-500' : 'bg-gray-600'
                        }`}
                        style={{ width: `${stage.progress}%` }}
                      />
                    </div>
                  </div>
                  <p className="text-xs text-gray-500">
                    {key === 'dataCollection' && 'Collecting VALO alert data and images'}
                    {key === 'patternAnalysis' && 'Analyzing false positive patterns'}
                    {key === 'optimization' && 'Optimizing detection thresholds'}
                    {key === 'continuousLearning' && 'Implementing learned improvements'}
                  </p>
                </div>
              ))}
            </div>
          </CardBody>
        </Card>

        {/* Accuracy Improvement Chart */}
        <div className="surveillance-grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          <Card className="surveillance-card">
            <CardHeader>
              <div className="flex items-center space-x-2">
                <TrendingUp className="w-5 h-5 text-primary-400" />
                <h2 className="text-xl font-semibold text-gray-100">Accuracy Improvement Over Time</h2>
              </div>
              <p className="text-sm text-gray-400">
                Real-time accuracy tracking showing continuous improvement
              </p>
            </CardHeader>
            <CardBody>
              <div className="h-64 flex items-end justify-between space-x-2 mb-4">
                {[78.2, 81.5, 83.1, 84.7, 85.9, 87.2].map((accuracy, index) => (
                  <div key={index} className="flex-1 flex flex-col items-center">
                    <div
                      className="w-full bg-gradient-to-t from-primary-600 to-primary-400 rounded-t"
                      style={{ height: `${(accuracy / 100) * 200}px` }}
                    />
                    <span className="text-xs text-gray-400 mt-2">
                      {index === 0 ? 'Start' : `+${index}h`}
                    </span>
                  </div>
                ))}
              </div>
              <div className="text-center">
                <p className="text-sm text-gray-400">
                  Accuracy: 78.2% → {metrics.currentAccuracy.toFixed(1)}%
                  <span className="text-green-400 ml-2">
                    (+{(metrics.currentAccuracy - 78.2).toFixed(1)}%)
                  </span>
                </p>
              </div>
            </CardBody>
          </Card>

          <Card className="surveillance-card">
            <CardHeader>
              <div className="flex items-center space-x-2">
                <BarChart3 className="w-5 h-5 text-primary-400" />
                <h2 className="text-xl font-semibold text-gray-100">Pattern Detection Breakdown</h2>
              </div>
              <p className="text-sm text-gray-400">
                Distribution of detected patterns by category
              </p>
            </CardHeader>
            <CardBody>
              <div className="space-y-4">
                {[
                  { label: 'Equipment Patterns', value: 12, color: 'bg-red-500' },
                  { label: 'Temporal Patterns', value: 8, color: 'bg-blue-500' },
                  { label: 'Structural Patterns', value: 6, color: 'bg-yellow-500' },
                  { label: 'Operational Patterns', value: 4, color: 'bg-green-500' }
                ].map((item, index) => (
                  <div key={index} className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className={`w-3 h-3 rounded-full ${item.color}`} />
                      <span className="text-sm text-gray-300">{item.label}</span>
                    </div>
                    <div className="flex items-center space-x-3">
                      <div className="w-24 bg-gray-700 rounded-full h-2">
                        <div
                          className={`h-2 rounded-full ${item.color}`}
                          style={{ width: `${(item.value / 12) * 100}%` }}
                        />
                      </div>
                      <span className="text-sm font-semibold text-gray-200 w-8 text-right">
                        {item.value}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </CardBody>
          </Card>
        </div>

        {/* Auto-Detected Patterns & Insights */}
        <Card className="surveillance-card mb-8">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Lightbulb className="w-5 h-5 text-primary-400" />
                <h2 className="text-xl font-semibold text-gray-100">Auto-Detected Patterns & Insights</h2>
              </div>
              <div className="text-right">
                <p className="text-xs text-gray-500 uppercase tracking-wider">Last Updated</p>
                <p className="text-sm text-gray-300">{lastUpdate.toLocaleTimeString()}</p>
              </div>
            </div>
            <p className="text-sm text-gray-400">
              AI-FARM's advanced pattern detection identifies critical insights for false positive reduction
            </p>
          </CardHeader>
          <CardBody>
            <div className="space-y-6">
              {patterns.map((pattern) => (
                <div
                  key={pattern.id}
                  className={`rounded-lg border-2 p-6 ${getPatternTypeColor(pattern.type)}`}
                >
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center space-x-3">
                      {getPatternIcon(pattern.type)}
                      <div>
                        <h3 className="font-semibold text-gray-100">{pattern.title}</h3>
                        <p className="text-sm text-gray-400">{pattern.description}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                        pattern.status === 'implemented' ? 'bg-green-900 text-green-300' :
                        pattern.status === 'validated' ? 'bg-blue-900 text-blue-300' :
                        'bg-yellow-900 text-yellow-300'
                      }`}>
                        {pattern.status.toUpperCase()}
                      </div>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                    <div className="bg-dark-700 rounded p-3">
                      <p className="text-xs text-gray-500 uppercase tracking-wider mb-1">Confidence</p>
                      <p className="text-lg font-bold text-gray-200">{pattern.confidence}%</p>
                    </div>
                    <div className="bg-dark-700 rounded p-3">
                      <p className="text-xs text-gray-500 uppercase tracking-wider mb-1">Business Value</p>
                      <p className="text-lg font-bold text-green-400">${pattern.businessValue}K</p>
                    </div>
                    <div className="bg-dark-700 rounded p-3">
                      <p className="text-xs text-gray-500 uppercase tracking-wider mb-1">Type</p>
                      <p className="text-lg font-bold text-gray-200 capitalize">{pattern.type}</p>
                    </div>
                  </div>

                  <div className="bg-dark-700 rounded p-4">
                    <p className="text-xs text-gray-500 uppercase tracking-wider mb-2">Impact Analysis</p>
                    <p className="text-sm text-gray-300">{pattern.impact}</p>
                  </div>
                </div>
              ))}
            </div>
          </CardBody>
        </Card>

        {/* Business Value Generated */}
        <Card className="surveillance-card mb-8">
          <CardHeader>
            <div className="flex items-center space-x-2">
              <DollarSign className="w-5 h-5 text-primary-400" />
              <h2 className="text-xl font-semibold text-gray-100">Business Value Generated by Auto-Learning</h2>
            </div>
            <p className="text-sm text-gray-400">
              Quantified impact of AI-FARM's learning capabilities on operational efficiency
            </p>
          </CardHeader>
          <CardBody>
            <div className="surveillance-grid grid-cols-1 md:grid-cols-4 gap-6">
              <div className="bg-dark-600 rounded-lg p-6 border border-green-500/30">
                <div className="flex items-center justify-between mb-3">
                  <DollarSign className="w-8 h-8 text-green-400" />
                  <span className="text-xs text-green-400 uppercase tracking-wider">Savings</span>
                </div>
                <p className="text-2xl font-bold text-green-400">${metrics.businessValue}K</p>
                <p className="text-xs text-gray-500 mt-1">Operational cost reduction</p>
              </div>

              <div className="bg-dark-600 rounded-lg p-6 border border-blue-500/30">
                <div className="flex items-center justify-between mb-3">
                  <Cpu className="w-8 h-8 text-blue-400" />
                  <span className="text-xs text-blue-400 uppercase tracking-wider">Speed</span>
                </div>
                <p className="text-2xl font-bold text-blue-400">{metrics.processingSpeed}</p>
                <p className="text-xs text-gray-500 mt-1">Alerts per hour</p>
              </div>

              <div className="bg-dark-600 rounded-lg p-6 border border-yellow-500/30">
                <div className="flex items-center justify-between mb-3">
                  <Target className="w-8 h-8 text-yellow-400" />
                  <span className="text-xs text-yellow-400 uppercase tracking-wider">Accuracy</span>
                </div>
                <p className="text-2xl font-bold text-yellow-400">92%</p>
                <p className="text-xs text-gray-500 mt-1">Pattern detection rate</p>
              </div>

              <div className="bg-dark-600 rounded-lg p-6 border border-purple-500/30">
                <div className="flex items-center justify-between mb-3">
                  <Brain className="w-8 h-8 text-purple-400" />
                  <span className="text-xs text-purple-400 uppercase tracking-wider">Learning</span>
                </div>
                <p className="text-2xl font-bold text-purple-400">50%</p>
                <p className="text-xs text-gray-500 mt-1">False positive reduction</p>
              </div>
            </div>
          </CardBody>
        </Card>

        {/* System Status */}
        <div className="text-center py-8">
          <div className="inline-flex items-center space-x-2 bg-dark-600 rounded-full px-6 py-3 border border-gray-700">
            <div className={`w-3 h-3 rounded-full ${isLearning ? 'bg-green-400 animate-pulse' : 'bg-gray-400'}`} />
            <span className="text-sm text-gray-300">
              {isLearning ? 'Auto-Learning System Active' : 'Auto-Learning System Standby'}
            </span>
            <span className="text-xs text-gray-500 ml-4">
              Last cycle: {lastUpdate.toLocaleString()}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};
