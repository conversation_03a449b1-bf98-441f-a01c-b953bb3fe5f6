import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  CheckCircle,
  AlertTriangle,
  TrendingUp,
  Download,
  Eye,
  Filter,
  Search,
  ArrowRight
} from 'lucide-react';
import {
  <PERSON><PERSON>,
  Card,
  CardHeader,
  CardBody,
  Alert,
  LoadingSpinner,
  MetricCard
} from '../components/ui';
import { ProcessingResultsChart, ConfidenceDistributionChart } from '../components/charts';
import { BatchProcessingResponse, ProcessingStatus } from '../types';
import { batchService } from '../services';
import toast from 'react-hot-toast';

export const ResultsPage: React.FC = () => {
  const { batchId } = useParams<{ batchId: string }>();
  const navigate = useNavigate();

  const [batch, setBatch] = useState<BatchProcessingResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filterStatus, setFilterStatus] = useState<'all' | 'false_positive' | 'legitimate'>('all');

  useEffect(() => {
    const fetchResults = async () => {
      if (!batchId) {
        // Show demo results if no batchId
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        const batchData = await batchService.getBatchStatus(batchId);
        setBatch(batchData);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load results');
        toast.error('Failed to load results');
      } finally {
        setLoading(false);
      }
    };

    fetchResults();
  }, [batchId]);

  if (loading) {
    return (
      <div className="min-h-screen bg-dark-500 flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-dark-500 p-8">
        <Alert variant="danger" title="Error Loading Results">
          {error}
        </Alert>
      </div>
    );
  }

  // Demo data when no batch is provided
  const demoResults = {
    totalAlerts: 1247,
    falsePositivesFiltered: 892,
    legitimateAlerts: 355,
    confidenceScore: 94.2,
    processingTime: '2.3 minutes',
    costSavings: '$12,450'
  };

  const results = batch ? {
    totalAlerts: batch.total_cases,
    falsePositivesFiltered: batch.summary?.false_positives_filtered || 0,
    legitimateAlerts: batch.total_cases - (batch.summary?.false_positives_filtered || 0),
    confidenceScore: batch.summary?.average_confidence || 0,
    processingTime: batch.completed_at && batch.started_at
      ? `${Math.round((new Date(batch.completed_at).getTime() - new Date(batch.started_at).getTime()) / 60000)} minutes`
      : 'Processing...',
    costSavings: `$${((batch.summary?.false_positives_filtered || 0) * 15).toLocaleString()}`
  } : demoResults;

  return (
    <div className="min-h-screen bg-dark-500 p-8">
      {/* Header */}
      <div className="max-w-7xl mx-auto mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-100 mb-2">
              Analysis Results
            </h1>
            <p className="text-gray-400">
              {batchId ? `Batch ID: ${batchId}` : 'Demo Results - Sample Analysis'}
            </p>
          </div>
          <div className="flex gap-4">
            <Button variant="outline" leftIcon={Download}>
              Export Results
            </Button>
            <Button
              variant="primary"
              leftIcon={ArrowRight}
              onClick={() => navigate(batchId ? `/roi/${batchId}` : '/roi')}
            >
              View ROI Analysis
            </Button>
          </div>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="max-w-7xl mx-auto mb-8">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <MetricCard
            title="Total Alerts"
            value={results.totalAlerts.toLocaleString()}
            icon={AlertTriangle}
            trend={{ value: 0, direction: 'neutral', label: 'processed' }}
            className="bg-dark-400 border-gray-700"
          />
          <MetricCard
            title="False Positives Filtered"
            value={results.falsePositivesFiltered.toLocaleString()}
            icon={Filter}
            trend={{ value: 71.5, direction: 'up', label: 'reduction rate' }}
            className="bg-dark-400 border-gray-700"
          />
          <MetricCard
            title="Legitimate Alerts"
            value={results.legitimateAlerts.toLocaleString()}
            icon={CheckCircle}
            trend={{ value: 28.5, direction: 'up', label: 'accuracy' }}
            className="bg-dark-400 border-gray-700"
          />
          <MetricCard
            title="Confidence Score"
            value={`${results.confidenceScore}%`}
            icon={TrendingUp}
            trend={{ value: 94.2, direction: 'up', label: 'confidence' }}
            className="bg-dark-400 border-gray-700"
          />
        </div>
      </div>

      {/* Charts Section */}
      <div className="max-w-7xl mx-auto mb-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <Card className="bg-dark-400 border-gray-700">
            <CardHeader>
              <h3 className="text-lg font-semibold text-gray-100">Processing Results</h3>
            </CardHeader>
            <CardBody>
              <ProcessingResultsChart
                data={{
                  totalAlerts: results.totalAlerts,
                  falsePositivesFiltered: results.falsePositivesFiltered,
                  alertsRequiringReview: results.legitimateAlerts
                }}
              />
            </CardBody>
          </Card>

          <Card className="bg-dark-400 border-gray-700">
            <CardHeader>
              <h3 className="text-lg font-semibold text-gray-100">Confidence Distribution</h3>
            </CardHeader>
            <CardBody>
              <ConfidenceDistributionChart
                data={[
                  { range: '90-100%', count: 245, percentage: 68.9 },
                  { range: '80-89%', count: 78, percentage: 21.9 },
                  { range: '70-79%', count: 23, percentage: 6.5 },
                  { range: '60-69%', count: 9, percentage: 2.5 },
                  { range: '<60%', count: 1, percentage: 0.3 }
                ]}
              />
            </CardBody>
          </Card>
        </div>
      </div>

      {/* Summary Card */}
      <div className="max-w-7xl mx-auto">
        <Card className="bg-dark-400 border-gray-700">
          <CardHeader>
            <h3 className="text-lg font-semibold text-gray-100">Analysis Summary</h3>
          </CardHeader>
          <CardBody>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="text-2xl font-bold text-primary-400 mb-2">
                  {((results.falsePositivesFiltered / results.totalAlerts) * 100).toFixed(1)}%
                </div>
                <div className="text-sm text-gray-400">False Positive Reduction</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-primary-400 mb-2">
                  {results.processingTime}
                </div>
                <div className="text-sm text-gray-400">Processing Time</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-primary-400 mb-2">
                  {results.costSavings}
                </div>
                <div className="text-sm text-gray-400">Estimated Monthly Savings</div>
              </div>
            </div>
          </CardBody>
        </Card>
      </div>
    </div>
  );
};