import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card } from '../components/ui/Card';
import { Alert } from '../components/ui/Alert';
import { Button } from '../components/ui/Button';
import { ProgressBar } from '../components/ui/ProgressBar';
import { MetricCard } from '../components/ui/MetricCard';
import { batchService } from '../services/batch-service';
import {
  PlayIcon,
  StopIcon,
  ArrowPathIcon,
  ChartBarIcon,
  CheckCircleIcon,
  XCircleIcon,
  DocumentChartBarIcon,
  CurrencyDollarIcon,
} from '@heroicons/react/24/outline';

interface ProcessingStatus {
  status: string;
  round?: number;
  chunk?: number;
  total_chunks?: number;
  cases_processed?: number;
  total_cases?: number;
  progress_percentage?: number;
  current_stats?: {
    valid_protection_rate: number;
    fp_detection_rate: number;
  };
}

interface BatchResults {
  processing_summary: {
    total_cases: number;
    total_rounds: number;
    total_valid_cases: number;
    valid_cases_protected: number;
    valid_protection_rate: number;
    total_false_positives: number;
    false_positives_detected: number;
    fp_detection_rate: number;
    processing_errors: number;
  };
  business_impact: {
    annual_false_positives: number;
    annual_fp_filtered: number;
    annual_time_saved_minutes: number;
    annual_cost_savings: number;
    valid_cases_at_risk: number;
  };
  round_improvements: Array<{
    round: number;
    valid_protection_rate: number;
    fp_detection_rate: number;
  }>;
}

export function BatchProcessingPage() {
  const navigate = useNavigate();
  const [isProcessing, setIsProcessing] = useState(false);
  const [jobId, setJobId] = useState<string | null>(null);
  const [status, setStatus] = useState<ProcessingStatus | null>(null);
  const [results, setResults] = useState<BatchResults | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [testMode, setTestMode] = useState(false);

  // Poll for status updates
  useEffect(() => {
    if (!jobId || !isProcessing) return;

    const interval = setInterval(async () => {
      try {
        const statusData = await batchService.getProcessingStatus(jobId);
        setStatus(statusData);

        if (statusData.status === 'completed') {
          setIsProcessing(false);
          // Load results
          const resultsData = await batchService.getLatestResults();
          setResults(resultsData);
        } else if (statusData.status === 'failed') {
          setIsProcessing(false);
          setError('Processing failed. Please check logs.');
        }
      } catch (err) {
        console.error('Failed to get status:', err);
      }
    }, 2000); // Poll every 2 seconds

    return () => clearInterval(interval);
  }, [jobId, isProcessing]);

  const startProcessing = async () => {
    try {
      setError(null);
      setIsProcessing(true);
      const response = await batchService.startBatchProcessing({
        num_rounds: 3,
        chunk_size: 5,
        test_mode: testMode,
      });
      setJobId(response.job_id);
    } catch (err: any) {
      setError(err.message || 'Failed to start processing');
      setIsProcessing(false);
    }
  };

  const loadLatestResults = async () => {
    try {
      const resultsData = await batchService.getLatestResults();
      setResults(resultsData);
    } catch (err: any) {
      setError('No results available');
    }
  };

  return (
    <div className="p-6 max-w-7xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            VALO Batch Processing
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-2">
            Process all 1,250+ cases with multi-round auto-learning
          </p>
        </div>
        <div className="flex space-x-4">
          <Button
            variant="outline"
            onClick={() => setTestMode(!testMode)}
            className="flex items-center space-x-2"
          >
            <ArrowPathIcon className="h-5 w-5" />
            <span>{testMode ? 'Test Mode' : 'Full Mode'}</span>
          </Button>
          <Button
            variant="primary"
            onClick={startProcessing}
            disabled={isProcessing}
            className="flex items-center space-x-2"
          >
            {isProcessing ? (
              <>
                <StopIcon className="h-5 w-5" />
                <span>Processing...</span>
              </>
            ) : (
              <>
                <PlayIcon className="h-5 w-5" />
                <span>Start Processing</span>
              </>
            )}
          </Button>
        </div>
      </div>

      {/* Alerts */}
      {error && (
        <Alert variant="error" onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {testMode && (
        <Alert variant="info">
          Test mode enabled - Processing only 50 cases for quick testing
        </Alert>
      )}

      {/* Processing Status */}
      {isProcessing && status && (
        <Card>
          <div className="p-6">
            <h2 className="text-xl font-semibold mb-4">Processing Status</h2>
            
            {/* Progress Bar */}
            <div className="mb-6">
              <div className="flex justify-between mb-2">
                <span className="text-sm text-gray-600 dark:text-gray-400">
                  Round {status.round || 0} - Chunk {status.chunk || 0}/{status.total_chunks || 0}
                </span>
                <span className="text-sm font-semibold">
                  {status.cases_processed || 0} / {status.total_cases || 0} cases
                </span>
              </div>
              <ProgressBar percentage={status.progress_percentage || 0} />
            </div>

            {/* Current Stats */}
            {status.current_stats && (
              <div className="grid grid-cols-2 gap-4 mt-4">
                <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
                  <div className="text-green-600 dark:text-green-400 text-sm font-medium">
                    Valid Protection Rate
                  </div>
                  <div className="text-2xl font-bold text-green-700 dark:text-green-300">
                    {status.current_stats.valid_protection_rate.toFixed(1)}%
                  </div>
                </div>
                <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
                  <div className="text-blue-600 dark:text-blue-400 text-sm font-medium">
                    FP Detection Rate
                  </div>
                  <div className="text-2xl font-bold text-blue-700 dark:text-blue-300">
                    {status.current_stats.fp_detection_rate.toFixed(1)}%
                  </div>
                </div>
              </div>
            )}
          </div>
        </Card>
      )}

      {/* Results */}
      {results && (
        <>
          {/* Summary Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <MetricCard
              title="Valid Protection Rate"
              value={`${results.processing_summary.valid_protection_rate.toFixed(1)}%`}
              description={`${results.processing_summary.valid_cases_protected}/${results.processing_summary.total_valid_cases} protected`}
              icon={<CheckCircleIcon className="h-6 w-6" />}
              trend="up"
            />
            <MetricCard
              title="FP Detection Rate"
              value={`${results.processing_summary.fp_detection_rate.toFixed(1)}%`}
              description={`${results.processing_summary.false_positives_detected}/${results.processing_summary.total_false_positives} detected`}
              icon={<XCircleIcon className="h-6 w-6" />}
              trend="up"
            />
            <MetricCard
              title="Annual Savings"
              value={`$${results.business_impact.annual_cost_savings.toLocaleString()}`}
              description={`${results.business_impact.annual_time_saved_minutes.toLocaleString()} minutes saved`}
              icon={<CurrencyDollarIcon className="h-6 w-6" />}
              trend="up"
            />
            <MetricCard
              title="Cases at Risk"
              value={results.business_impact.valid_cases_at_risk.toString()}
              description="Valid cases potentially missed"
              icon={<DocumentChartBarIcon className="h-6 w-6" />}
              trend={results.business_impact.valid_cases_at_risk === 0 ? 'up' : 'down'}
            />
          </div>

          {/* Round Improvements */}
          <Card>
            <div className="p-6">
              <h2 className="text-xl font-semibold mb-4">
                Multi-Round Learning Progress
              </h2>
              <div className="space-y-4">
                {results.round_improvements.map((round) => (
                  <div key={round.round} className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <div>
                      <span className="font-medium">Round {round.round}</span>
                    </div>
                    <div className="flex space-x-8">
                      <div className="text-right">
                        <div className="text-sm text-gray-500 dark:text-gray-400">Valid Protection</div>
                        <div className="font-semibold text-green-600 dark:text-green-400">
                          {round.valid_protection_rate.toFixed(1)}%
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-sm text-gray-500 dark:text-gray-400">FP Detection</div>
                        <div className="font-semibold text-blue-600 dark:text-blue-400">
                          {round.fp_detection_rate.toFixed(1)}%
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </Card>

          {/* Business Impact */}
          <Card>
            <div className="p-6">
              <h2 className="text-xl font-semibold mb-4">Business Impact Analysis</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="font-medium text-gray-700 dark:text-gray-300 mb-3">
                    Annual Projections
                  </h3>
                  <dl className="space-y-2">
                    <div className="flex justify-between">
                      <dt className="text-gray-600 dark:text-gray-400">Total False Positives:</dt>
                      <dd className="font-medium">{results.business_impact.annual_false_positives.toLocaleString()}</dd>
                    </div>
                    <div className="flex justify-between">
                      <dt className="text-gray-600 dark:text-gray-400">FPs Filtered by AI:</dt>
                      <dd className="font-medium text-green-600 dark:text-green-400">
                        {results.business_impact.annual_fp_filtered.toLocaleString()}
                      </dd>
                    </div>
                    <div className="flex justify-between">
                      <dt className="text-gray-600 dark:text-gray-400">Time Saved:</dt>
                      <dd className="font-medium">
                        {(results.business_impact.annual_time_saved_minutes / 60).toFixed(0)} hours
                      </dd>
                    </div>
                  </dl>
                </div>
                <div>
                  <h3 className="font-medium text-gray-700 dark:text-gray-300 mb-3">
                    Safety First Approach
                  </h3>
                  <p className="text-gray-600 dark:text-gray-400">
                    Our conservative approach prioritizes never missing valid safety violations.
                    With {results.processing_summary.valid_protection_rate.toFixed(1)}% valid case protection,
                    we ensure safety while still achieving significant cost savings.
                  </p>
                  {results.business_impact.valid_cases_at_risk === 0 && (
                    <div className="mt-2 text-green-600 dark:text-green-400 font-medium">
                      ✓ Zero valid cases at risk
                    </div>
                  )}
                </div>
              </div>
            </div>
          </Card>

          {/* Actions */}
          <div className="flex justify-center space-x-4">
            <Button
              variant="outline"
              onClick={() => navigate('/results')}
              className="flex items-center space-x-2"
            >
              <ChartBarIcon className="h-5 w-5" />
              <span>View Detailed Results</span>
            </Button>
            <Button
              variant="outline"
              onClick={loadLatestResults}
              className="flex items-center space-x-2"
            >
              <ArrowPathIcon className="h-5 w-5" />
              <span>Refresh Results</span>
            </Button>
          </div>
        </>
      )}
    </div>
  );
}