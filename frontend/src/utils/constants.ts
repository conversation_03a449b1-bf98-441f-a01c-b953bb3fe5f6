// Application constants
export const APP_CONFIG = {
  name: 'AI-FARM',
  fullName: 'AI-FARM - False Positive Alert Reduction System',
  version: '1.0.0',
  description: 'Revolutionize your safety monitoring with AI-powered analysis',
};

// API Configuration
export const API_CONFIG = {
  baseURL: process.env.REACT_APP_API_URL || 'http://localhost:8001',
  timeout: parseInt(process.env.REACT_APP_API_TIMEOUT || '300000'), // 5 minutes for large file uploads
  retryAttempts: 3,
  retryDelay: 1000,
};

// File Upload Limits
export const UPLOAD_LIMITS = {
  csvMaxSize: 50 * 1024 * 1024, // 50MB
  imagesMaxSize: 1024 * 1024 * 1024, // 1GB
  allowedCsvTypes: ['.csv'],
  allowedImageTypes: ['.zip'],
  allowedImageFormats: ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'],
};

// Demo Defaults
export const DEMO_DEFAULTS = {
  customerName: process.env.REACT_APP_DEFAULT_CUSTOMER_NAME || 'Valued Customer',
  monthlyAlerts: parseInt(process.env.REACT_APP_DEFAULT_MONTHLY_ALERTS || '17268'),
  hourlyRate: parseFloat(process.env.REACT_APP_DEFAULT_HOURLY_RATE || '50'),
  implementationCost: parseFloat(process.env.REACT_APP_DEFAULT_IMPLEMENTATION_COST || '225000'),
  baselineFilterRate: 3.0,
  reviewTimePerAlert: 3, // minutes
};

// Processing Configuration
export const PROCESSING_CONFIG = {
  pollInterval: 2000, // 2 seconds
  maxPollDuration: 30 * 60 * 1000, // 30 minutes
  progressUpdateInterval: 1000, // 1 second
};

// UI Configuration
export const UI_CONFIG = {
  resultsPageSize: 50,
  chartColors: {
    primary: '#3b82f6',
    success: '#22c55e',
    warning: '#f59e0b',
    danger: '#ef4444',
    gray: '#6b7280',
  },
  animationDuration: 300,
  toastDuration: 4000,
};

// Validation Rules
export const VALIDATION_RULES = {
  batchId: {
    minLength: 8,
    maxLength: 50,
    pattern: /^[a-fA-F0-9-]+$/,
  },
  caseNumber: {
    pattern: /^V125/,
    maxLength: 50,
  },
  confidence: {
    min: 0,
    max: 100,
  },
  hourlyRate: {
    min: 1,
    max: 1000,
  },
  implementationCost: {
    min: 1000,
    max: 10000000,
  },
};

// Error Messages
export const ERROR_MESSAGES = {
  network: 'Network error - please check your connection',
  timeout: 'Request timed out - please try again',
  serverError: 'Server error - please try again later',
  notFound: 'Resource not found',
  unauthorized: 'Unauthorized access',
  forbidden: 'Access forbidden',
  validation: 'Invalid input data',
  fileUpload: 'File upload failed',
  processing: 'Processing failed',
  unknown: 'An unexpected error occurred',
};

// Success Messages
export const SUCCESS_MESSAGES = {
  uploadComplete: 'Files uploaded successfully',
  processingComplete: 'Processing completed successfully',
  dataExported: 'Data exported successfully',
  configUpdated: 'Configuration updated successfully',
  batchCancelled: 'Processing cancelled successfully',
};

// Feature Flags
export const FEATURE_FLAGS = {
  enableDebugMode: process.env.REACT_APP_ENABLE_DEBUG === 'true',
  enableMockData: process.env.REACT_APP_ENABLE_MOCK_DATA === 'true',
  enableAutoLearning: true,
  enableCustomThresholds: true,
  enableRealTimeUpdates: true,
};

// Local Storage Keys
export const STORAGE_KEYS = {
  demoConfig: 'ai-farm-demo-config',
  userPreferences: 'ai-farm-user-preferences',
  recentBatches: 'ai-farm-recent-batches',
  uploadHistory: 'ai-farm-upload-history',
};

// Route Paths
export const ROUTES = {
  dashboard: '/',
  upload: '/upload',
  processing: '/processing/:batchId',
  results: '/results/:batchId',
  roi: '/roi/:batchId',
  insights: '/insights/:batchId',
};

// Status Mappings
export const STATUS_LABELS = {
  pending: 'Queued for processing',
  processing: 'Processing in progress',
  completed: 'Processing completed',
  failed: 'Processing failed',
  cancelled: 'Processing cancelled',
};

export const STATUS_COLORS = {
  pending: 'gray',
  processing: 'primary',
  completed: 'success',
  failed: 'danger',
  cancelled: 'warning',
};

// Metric Display Formats
export const METRIC_FORMATS = {
  percentage: { suffix: '%', decimals: 1 },
  currency: { prefix: '$', decimals: 0 },
  time: { suffix: 'ms', decimals: 0 },
  hours: { suffix: ' hrs', decimals: 1 },
  count: { decimals: 0 },
};