// Utility functions for formatting data

/**
 * Format a number as currency
 */
export const formatCurrency = (amount: number, options: Intl.NumberFormatOptions = {}): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
    ...options,
  }).format(amount);
};

/**
 * Format a number with commas
 */
export const formatNumber = (num: number, decimals: number = 0): string => {
  return new Intl.NumberFormat('en-US', {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals,
  }).format(num);
};

/**
 * Format a percentage
 */
export const formatPercentage = (value: number, decimals: number = 1): string => {
  return `${value.toFixed(decimals)}%`;
};

/**
 * Format file size in human-readable format
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;
};

/**
 * Format duration in human-readable format
 */
export const formatDuration = (seconds: number): string => {
  if (seconds < 60) {
    return `${seconds}s`;
  } else if (seconds < 3600) {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return remainingSeconds > 0 ? `${minutes}m ${remainingSeconds}s` : `${minutes}m`;
  } else if (seconds < 86400) {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return minutes > 0 ? `${hours}h ${minutes}m` : `${hours}h`;
  } else {
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    return hours > 0 ? `${days}d ${hours}h` : `${days}d`;
  }
};

/**
 * Format processing time in milliseconds
 */
export const formatProcessingTime = (ms: number): string => {
  if (ms < 1000) {
    return `${Math.round(ms)}ms`;
  } else if (ms < 60000) {
    return `${(ms / 1000).toFixed(1)}s`;
  } else {
    const minutes = Math.floor(ms / 60000);
    const seconds = Math.floor((ms % 60000) / 1000);
    return seconds > 0 ? `${minutes}m ${seconds}s` : `${minutes}m`;
  }
};

/**
 * Format date in relative format (e.g., "2 hours ago")
 */
export const formatRelativeTime = (date: Date | string): string => {
  const now = new Date();
  const target = typeof date === 'string' ? new Date(date) : date;
  const diffMs = now.getTime() - target.getTime();
  const diffSeconds = Math.floor(diffMs / 1000);
  const diffMinutes = Math.floor(diffSeconds / 60);
  const diffHours = Math.floor(diffMinutes / 60);
  const diffDays = Math.floor(diffHours / 24);

  if (diffSeconds < 60) {
    return 'Just now';
  } else if (diffMinutes < 60) {
    return `${diffMinutes} minute${diffMinutes !== 1 ? 's' : ''} ago`;
  } else if (diffHours < 24) {
    return `${diffHours} hour${diffHours !== 1 ? 's' : ''} ago`;
  } else if (diffDays < 7) {
    return `${diffDays} day${diffDays !== 1 ? 's' : ''} ago`;
  } else {
    return target.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  }
};

/**
 * Format date for display
 */
export const formatDate = (date: Date | string, options: Intl.DateTimeFormatOptions = {}): string => {
  const target = typeof date === 'string' ? new Date(date) : date;
  
  const defaultOptions: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  };

  return target.toLocaleDateString('en-US', { ...defaultOptions, ...options });
};

/**
 * Format confidence score as percentage
 */
export const formatConfidence = (score: number): string => {
  return `${(score * 100).toFixed(1)}%`;
};

/**
 * Format batch ID for display (truncate if too long)
 */
export const formatBatchId = (batchId: string, maxLength: number = 8): string => {
  if (batchId.length <= maxLength) {
    return batchId;
  }
  return `${batchId.substring(0, maxLength)}...`;
};

/**
 * Format case number for display
 */
export const formatCaseNumber = (caseNumber: string): string => {
  // Ensure case number formatting is consistent
  return caseNumber.toUpperCase();
};

/**
 * Format ROI as a readable string
 */
export const formatROI = (roiPercentage: number): string => {
  const sign = roiPercentage >= 0 ? '+' : '';
  return `${sign}${roiPercentage.toFixed(0)}%`;
};

/**
 * Format trend value with appropriate sign and color indicator
 */
export const formatTrend = (value: number, direction: 'up' | 'down' | 'neutral'): {
  text: string;
  color: 'success' | 'danger' | 'neutral';
} => {
  const sign = value > 0 ? '+' : '';
  const text = `${sign}${value.toFixed(1)}%`;
  
  let color: 'success' | 'danger' | 'neutral';
  if (direction === 'up') {
    color = 'success';
  } else if (direction === 'down') {
    color = 'danger';
  } else {
    color = 'neutral';
  }
  
  return { text, color };
};

/**
 * Truncate text with ellipsis
 */
export const truncateText = (text: string, maxLength: number = 50): string => {
  if (text.length <= maxLength) {
    return text;
  }
  return `${text.substring(0, maxLength - 3)}...`;
};

/**
 * Format validation status for display
 */
export const formatValidationStatus = (status: string): string => {
  return status
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
};

/**
 * Format processing status with appropriate styling
 */
export const formatProcessingStatus = (status: string): {
  label: string;
  color: 'primary' | 'success' | 'warning' | 'danger' | 'neutral';
} => {
  const statusMap: Record<string, { label: string; color: any }> = {
    pending: { label: 'Pending', color: 'neutral' },
    processing: { label: 'Processing', color: 'primary' },
    completed: { label: 'Completed', color: 'success' },
    failed: { label: 'Failed', color: 'danger' },
    cancelled: { label: 'Cancelled', color: 'warning' },
  };
  
  return statusMap[status] || { label: status, color: 'neutral' };
};