import React, { useCallback, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import clsx from 'clsx';
import { Upload, File, X, AlertCircle, CheckCircle } from 'lucide-react';
import { Button } from './Button';

export interface FileUploadProps {
  accept?: Record<string, string[]>;
  multiple?: boolean;
  maxSize?: number;
  maxFiles?: number;
  onFileSelect?: (files: File[]) => void;
  onFileRemove?: (file: File) => void;
  disabled?: boolean;
  error?: string;
  className?: string;
  title?: string;
  description?: string;
  showPreview?: boolean;
}

export const FileUpload: React.FC<FileUploadProps> = ({
  accept,
  multiple = false,
  maxSize,
  maxFiles,
  onFileSelect,
  onFileRemove,
  disabled = false,
  error,
  className,
  title = 'Upload files',
  description = 'Drag and drop files here, or click to browse',
  showPreview = true,
}) => {
  const [files, setFiles] = useState<File[]>([]);
  const [uploadError, setUploadError] = useState<string | null>(null);

  const onDrop = useCallback((acceptedFiles: File[], rejectedFiles: any[]) => {
    setUploadError(null);
    
    if (rejectedFiles.length > 0) {
      const firstRejection = rejectedFiles[0];
      if (firstRejection.errors[0]?.code === 'file-too-large') {
        setUploadError(`File is too large. Maximum size is ${formatFileSize(maxSize || 0)}`);
      } else if (firstRejection.errors[0]?.code === 'file-invalid-type') {
        setUploadError('File type is not supported');
      } else {
        setUploadError('File upload failed');
      }
      return;
    }

    const newFiles = multiple ? [...files, ...acceptedFiles] : acceptedFiles;
    
    if (maxFiles && newFiles.length > maxFiles) {
      setUploadError(`Maximum ${maxFiles} file${maxFiles > 1 ? 's' : ''} allowed`);
      return;
    }

    setFiles(newFiles);
    onFileSelect?.(newFiles);
  }, [files, multiple, maxSize, maxFiles, onFileSelect]);

  const { getRootProps, getInputProps, isDragActive, isDragReject } = useDropzone({
    onDrop,
    accept,
    multiple,
    maxSize,
    disabled,
    noClick: disabled,
    noKeyboard: disabled,
  });

  const removeFile = (fileToRemove: File) => {
    const updatedFiles = files.filter(file => file !== fileToRemove);
    setFiles(updatedFiles);
    onFileRemove?.(fileToRemove);
    onFileSelect?.(updatedFiles);
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const currentError = error || uploadError;

  return (
    <div className={clsx('space-y-4', className)}>
      <div
        {...getRootProps()}
        className={clsx(
          'border-2 border-dashed rounded-lg p-8 text-center transition-colors duration-200',
          {
            'border-primary-300 bg-primary-50': isDragActive && !isDragReject,
            'border-danger-300 bg-danger-50': isDragReject || currentError,
            'border-gray-300 bg-gray-50 hover:border-gray-400': !isDragActive && !currentError && !disabled,
            'border-gray-200 bg-gray-100 cursor-not-allowed': disabled,
            'cursor-pointer': !disabled,
          }
        )}
      >
        <input {...getInputProps()} />
        
        <div className="space-y-2">
          <Upload className={clsx(
            'mx-auto w-12 h-12',
            {
              'text-primary-500': isDragActive && !isDragReject,
              'text-danger-500': isDragReject || currentError,
              'text-gray-400': !isDragActive && !currentError && !disabled,
              'text-gray-300': disabled,
            }
          )} />
          
          <div>
            <p className={clsx(
              'text-lg font-medium',
              {
                'text-primary-700': isDragActive && !isDragReject,
                'text-danger-700': isDragReject || currentError,
                'text-gray-700': !isDragActive && !currentError && !disabled,
                'text-gray-500': disabled,
              }
            )}>
              {isDragActive ? 'Drop files here' : title}
            </p>
            
            <p className={clsx(
              'text-sm',
              {
                'text-primary-600': isDragActive && !isDragReject,
                'text-danger-600': isDragReject || currentError,
                'text-gray-500': !isDragActive && !currentError && !disabled,
                'text-gray-400': disabled,
              }
            )}>
              {description}
            </p>
            
            {maxSize && (
              <p className="text-xs text-gray-400 mt-1">
                Maximum file size: {formatFileSize(maxSize)}
              </p>
            )}
          </div>
        </div>
      </div>

      {currentError && (
        <div className="flex items-center space-x-2 text-danger-600 text-sm">
          <AlertCircle className="w-4 h-4" />
          <span>{currentError}</span>
        </div>
      )}

      {showPreview && files.length > 0 && (
        <div className="space-y-2">
          <h4 className="text-sm font-medium text-gray-700">Selected files:</h4>
          <div className="space-y-2">
            {files.map((file, index) => (
              <div
                key={index}
                className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
              >
                <div className="flex items-center space-x-3">
                  <File className="w-5 h-5 text-gray-400" />
                  <div>
                    <p className="text-sm font-medium text-gray-900">{file.name}</p>
                    <p className="text-xs text-gray-500">{formatFileSize(file.size)}</p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-2">
                  <CheckCircle className="w-4 h-4 text-success-500" />
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => removeFile(file)}
                    leftIcon={X}
                  >
                    Remove
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export interface BatchFileUploadProps {
  csvFile?: File | null;
  imagesFile?: File | null;
  onCsvFileSelect?: (file: File | null) => void;
  onImagesFileSelect?: (file: File | null) => void;
  className?: string;
}

export const BatchFileUpload: React.FC<BatchFileUploadProps> = ({
  csvFile,
  imagesFile,
  onCsvFileSelect,
  onImagesFileSelect,
  className,
}) => {
  return (
    <div className={clsx('space-y-6', className)}>
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">
          Case Data (Required)
        </h3>
        <FileUpload
          accept={{ 'text/csv': ['.csv'] }}
          maxSize={50 * 1024 * 1024} // 50MB
          title="Upload CSV file"
          description="Select the CSV file containing case data"
          onFileSelect={(files) => onCsvFileSelect?.(files[0] || null)}
          onFileRemove={() => onCsvFileSelect?.(null)}
        />
        {csvFile && (
          <div className="mt-2 text-sm text-success-600">
            ✓ CSV file selected: {csvFile.name}
          </div>
        )}
      </div>

      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">
          Images (Optional)
        </h3>
        <FileUpload
          accept={{ 'application/zip': ['.zip'] }}
          maxSize={1024 * 1024 * 1024} // 1GB
          title="Upload images ZIP file"
          description="Select a ZIP file containing violation images (optional, up to 1GB)"
          onFileSelect={(files) => onImagesFileSelect?.(files[0] || null)}
          onFileRemove={() => onImagesFileSelect?.(null)}
        />
        {imagesFile && (
          <div className="mt-2 text-sm text-success-600">
            ✓ Images ZIP file selected: {imagesFile.name}
          </div>
        )}
      </div>
    </div>
  );
};