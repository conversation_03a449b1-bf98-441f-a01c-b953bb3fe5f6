import React from 'react';
import clsx from 'clsx';
import { TrendingUp, TrendingDown, Minus, ArrowUpRight, ArrowDownRight } from 'lucide-react';

export interface MetricCardProps {
  title: string;
  value: string | number;
  subtitle?: string;
  trend?: {
    value: number;
    direction: 'up' | 'down' | 'neutral';
    label: string;
  };
  icon?: React.ComponentType<any>;
  color?: 'primary' | 'success' | 'warning' | 'danger' | 'neutral';
  size?: 'sm' | 'md' | 'lg';
  loading?: boolean;
  onClick?: () => void;
  className?: string;
}

export const MetricCard: React.FC<MetricCardProps> = ({
  title,
  value,
  subtitle,
  trend,
  icon: Icon,
  color = 'neutral',
  size = 'md',
  loading = false,
  onClick,
  className,
}) => {
  const colorClasses = {
    primary: {
      background: 'bg-primary-50',
      icon: 'text-primary-600',
      border: 'border-primary-200',
    },
    success: {
      background: 'bg-success-50',
      icon: 'text-success-600',
      border: 'border-success-200',
    },
    warning: {
      background: 'bg-warning-50',
      icon: 'text-warning-600',
      border: 'border-warning-200',
    },
    danger: {
      background: 'bg-danger-50',
      icon: 'text-danger-600',
      border: 'border-danger-200',
    },
    neutral: {
      background: 'bg-gray-50',
      icon: 'text-gray-600',
      border: 'border-gray-200',
    },
  };
  
  const sizeClasses = {
    sm: {
      padding: 'p-4',
      value: 'text-xl',
      title: 'text-xs',
      icon: 'w-5 h-5',
    },
    md: {
      padding: 'p-6',
      value: 'text-3xl',
      title: 'text-sm',
      icon: 'w-6 h-6',
    },
    lg: {
      padding: 'p-8',
      value: 'text-4xl',
      title: 'text-base',
      icon: 'w-8 h-8',
    },
  };
  
  const getTrendIcon = () => {
    if (!trend) return null;
    
    switch (trend.direction) {
      case 'up':
        return <TrendingUp className="w-4 h-4" />;
      case 'down':
        return <TrendingDown className="w-4 h-4" />;
      default:
        return <Minus className="w-4 h-4" />;
    }
  };
  
  const getTrendClasses = () => {
    if (!trend) return '';
    
    switch (trend.direction) {
      case 'up':
        return 'metric-trend-up';
      case 'down':
        return 'metric-trend-down';
      default:
        return 'metric-trend-neutral';
    }
  };
  
  return (
    <div
      className={clsx(
        'card',
        sizeClasses[size].padding,
        {
          'cursor-pointer hover:shadow-card-hover transition-shadow duration-200': onClick,
          'animate-pulse': loading,
        },
        className
      )}
      onClick={onClick}
    >
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <p className={clsx('metric-label', sizeClasses[size].title)}>
            {title}
          </p>
          
          {loading ? (
            <div className="bg-gray-200 rounded h-8 w-24 mt-2 animate-pulse" />
          ) : (
            <p className={clsx('metric-value', sizeClasses[size].value)}>
              {typeof value === 'number' ? value.toLocaleString() : value}
            </p>
          )}
          
          {subtitle && !loading && (
            <p className="text-sm text-gray-600 mt-1">{subtitle}</p>
          )}
          
          {trend && !loading && (
            <div className={clsx(getTrendClasses(), 'mt-2')}>
              {getTrendIcon()}
              <span className="ml-1">
                {trend.value > 0 ? '+' : ''}{trend.value}%
              </span>
              <span className="ml-1 text-gray-500">{trend.label}</span>
            </div>
          )}
        </div>
        
        {Icon && (
          <div className={clsx(
            'rounded-full p-3',
            colorClasses[color].background,
            colorClasses[color].border,
            'border'
          )}>
            <Icon className={clsx(
              sizeClasses[size].icon,
              colorClasses[color].icon
            )} />
          </div>
        )}
      </div>
    </div>
  );
};

export interface MetricGridProps {
  metrics: MetricCardProps[];
  columns?: 1 | 2 | 3 | 4;
  className?: string;
}

export const MetricGrid: React.FC<MetricGridProps> = ({
  metrics,
  columns = 4,
  className,
}) => {
  const gridClasses = {
    1: 'grid-cols-1',
    2: 'grid-cols-1 md:grid-cols-2',
    3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
    4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4',
  };
  
  return (
    <div className={clsx(
      'grid gap-6',
      gridClasses[columns],
      className
    )}>
      {metrics.map((metric, index) => (
        <MetricCard key={index} {...metric} />
      ))}
    </div>
  );
};

export interface ComparisonMetricCardProps {
  title: string;
  before: {
    label: string;
    value: string | number;
  };
  after: {
    label: string;
    value: string | number;
  };
  improvement?: {
    value: number;
    direction: 'up' | 'down';
    label: string;
  };
  icon?: React.ComponentType<any>;
  className?: string;
}

export const ComparisonMetricCard: React.FC<ComparisonMetricCardProps> = ({
  title,
  before,
  after,
  improvement,
  icon: Icon,
  className,
}) => {
  return (
    <div className={clsx('card p-6', className)}>
      <div className="flex items-start justify-between mb-4">
        <h3 className="text-sm font-medium text-gray-700">{title}</h3>
        {Icon && (
          <Icon className="w-5 h-5 text-gray-400" />
        )}
      </div>
      
      <div className="grid grid-cols-2 gap-4 mb-4">
        <div>
          <p className="text-xs text-gray-500 mb-1">{before.label}</p>
          <p className="text-xl font-semibold text-gray-900">
            {typeof before.value === 'number' ? before.value.toLocaleString() : before.value}
          </p>
        </div>
        
        <div>
          <p className="text-xs text-gray-500 mb-1">{after.label}</p>
          <p className="text-xl font-semibold text-primary-600">
            {typeof after.value === 'number' ? after.value.toLocaleString() : after.value}
          </p>
        </div>
      </div>
      
      {improvement && (
        <div className="flex items-center">
          {improvement.direction === 'up' ? (
            <ArrowUpRight className="w-4 h-4 text-success-600" />
          ) : (
            <ArrowDownRight className="w-4 h-4 text-danger-600" />
          )}
          <span className={clsx(
            'text-sm font-medium ml-1',
            improvement.direction === 'up' ? 'text-success-600' : 'text-danger-600'
          )}>
            {improvement.value > 0 ? '+' : ''}{improvement.value}%
          </span>
          <span className="text-sm text-gray-500 ml-1">{improvement.label}</span>
        </div>
      )}
    </div>
  );
};