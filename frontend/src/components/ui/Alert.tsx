import React from 'react';
import clsx from 'clsx';
import { AlertCircle, CheckCircle, Info, AlertTriangle, X } from 'lucide-react';

export interface AlertProps {
  variant?: 'success' | 'warning' | 'danger' | 'info';
  title?: string;
  children: React.ReactNode;
  dismissible?: boolean;
  onDismiss?: () => void;
  className?: string;
}

export const Alert: React.FC<AlertProps> = ({
  variant = 'info',
  title,
  children,
  dismissible = false,
  onDismiss,
  className,
}) => {
  const variantClasses = {
    success: 'alert-success',
    warning: 'alert-warning',
    danger: 'alert-danger',
    info: 'alert-info',
  };
  
  const iconClasses = {
    success: 'text-success-600',
    warning: 'text-warning-600',
    danger: 'text-danger-600',
    info: 'text-primary-600',
  };
  
  const textClasses = {
    success: 'text-success-800',
    warning: 'text-warning-800',
    danger: 'text-danger-800',
    info: 'text-primary-800',
  };
  
  const getIcon = () => {
    switch (variant) {
      case 'success':
        return <CheckCircle className="w-5 h-5" />;
      case 'warning':
        return <AlertTriangle className="w-5 h-5" />;
      case 'danger':
        return <AlertCircle className="w-5 h-5" />;
      default:
        return <Info className="w-5 h-5" />;
    }
  };
  
  return (
    <div className={clsx(variantClasses[variant], className)}>
      <div className="flex items-start">
        <div className={clsx('flex-shrink-0', iconClasses[variant])}>
          {getIcon()}
        </div>
        
        <div className="ml-3 flex-1">
          {title && (
            <h3 className={clsx('text-sm font-medium', textClasses[variant])}>
              {title}
            </h3>
          )}
          
          <div className={clsx(
            'text-sm',
            textClasses[variant],
            { 'mt-1': title }
          )}>
            {children}
          </div>
        </div>
        
        {dismissible && (
          <div className="ml-auto pl-3">
            <div className="-mx-1.5 -my-1.5">
              <button
                type="button"
                className={clsx(
                  'inline-flex rounded-md p-1.5 focus:outline-none focus:ring-2 focus:ring-offset-2',
                  {
                    'text-success-500 hover:bg-success-100 focus:ring-success-600': variant === 'success',
                    'text-warning-500 hover:bg-warning-100 focus:ring-warning-600': variant === 'warning',
                    'text-danger-500 hover:bg-danger-100 focus:ring-danger-600': variant === 'danger',
                    'text-primary-500 hover:bg-primary-100 focus:ring-primary-600': variant === 'info',
                  }
                )}
                onClick={onDismiss}
              >
                <span className="sr-only">Dismiss</span>
                <X className="w-5 h-5" />
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export interface AlertListProps {
  alerts: Array<{
    id: string;
    variant?: 'success' | 'warning' | 'danger' | 'info';
    title?: string;
    message: string;
    dismissible?: boolean;
  }>;
  onDismiss?: (id: string) => void;
  className?: string;
}

export const AlertList: React.FC<AlertListProps> = ({
  alerts,
  onDismiss,
  className,
}) => {
  if (alerts.length === 0) return null;
  
  return (
    <div className={clsx('space-y-4', className)}>
      {alerts.map((alert) => (
        <Alert
          key={alert.id}
          variant={alert.variant}
          title={alert.title}
          dismissible={alert.dismissible}
          onDismiss={() => onDismiss?.(alert.id)}
        >
          {alert.message}
        </Alert>
      ))}
    </div>
  );
};