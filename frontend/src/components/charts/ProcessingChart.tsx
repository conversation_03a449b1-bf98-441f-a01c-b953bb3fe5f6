import React from 'react';
import {
  <PERSON><PERSON>hart,
  Pie,
  Cell,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  LineChart,
  Line,
  Area,
  AreaChart,
} from 'recharts';
import clsx from 'clsx';

const COLORS = {
  primary: '#00ff88',
  success: '#22c55e',
  warning: '#ffaa00',
  danger: '#ff3366',
  gray: '#6b7280',
  background: '#1a1a1a',
  text: '#e5e7eb',
  textSecondary: '#9ca3af',
};

export interface ProcessingResultsChartProps {
  data: {
    totalAlerts: number;
    falsePositivesFiltered: number;
    alertsRequiringReview: number;
  };
  className?: string;
}

export const ProcessingResultsChart: React.FC<ProcessingResultsChartProps> = ({
  data,
  className,
}) => {
  const chartData = [
    {
      name: 'False Positives Filtered',
      value: data.falsePositivesFiltered,
      percentage: ((data.falsePositivesFiltered / data.totalAlerts) * 100).toFixed(1),
    },
    {
      name: 'Alerts Requiring Review',
      value: data.alertsRequiringReview,
      percentage: ((data.alertsRequiringReview / data.totalAlerts) * 100).toFixed(1),
    },
  ];

  const colors = [COLORS.success, COLORS.warning];

  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-dark-50 p-3 border border-gray-700 rounded-lg shadow-lg">
          <p className="font-medium text-gray-100">{data.name}</p>
          <p className="text-sm text-gray-300">
            {data.value.toLocaleString()} alerts ({data.percentage}%)
          </p>
        </div>
      );
    }
    return null;
  };

  return (
    <div className={clsx('surveillance-card', className)}>
      <div className="surveillance-card-header">
        <h3 className="text-lg font-medium text-gray-100">Processing Results</h3>
        <p className="text-sm text-gray-400">
          Distribution of {data.totalAlerts.toLocaleString()} processed alerts
        </p>
      </div>
      <div className="surveillance-card-body">
        <ResponsiveContainer width="100%" height={300}>
          <PieChart>
            <Pie
              data={chartData}
              cx="50%"
              cy="50%"
              labelLine={false}
              label={({ name, percentage }) => `${name}: ${percentage}%`}
              outerRadius={80}
              fill="#8884d8"
              dataKey="value"
            >
              {chartData.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={colors[index % colors.length]} />
              ))}
            </Pie>
            <Tooltip content={<CustomTooltip />} />
            <Legend />
          </PieChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
};

export interface ConfidenceDistributionChartProps {
  data: Array<{
    range: string;
    count: number;
    percentage: number;
  }>;
  className?: string;
}

export const ConfidenceDistributionChart: React.FC<ConfidenceDistributionChartProps> = ({
  data,
  className,
}) => {
  return (
    <div className={clsx('surveillance-card', className)}>
      <div className="surveillance-card-header">
        <h3 className="text-lg font-medium text-gray-100">Confidence Score Distribution</h3>
        <p className="text-sm text-gray-400">
          VLM confidence scores across all processed alerts
        </p>
      </div>
      <div className="surveillance-card-body">
        <ResponsiveContainer width="100%" height={300}>
          <BarChart data={data}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="range" />
            <YAxis />
            <Tooltip
              formatter={(value: number, name: string) => [
                `${value} alerts`,
                'Count'
              ]}
              labelFormatter={(label) => `Confidence: ${label}`}
            />
            <Bar dataKey="count" fill={COLORS.primary} />
          </BarChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
};

export interface ProcessingTimeChartProps {
  data: Array<{
    time: string;
    processed: number;
    cumulative: number;
  }>;
  className?: string;
}

export const ProcessingTimeChart: React.FC<ProcessingTimeChartProps> = ({
  data,
  className,
}) => {
  return (
    <div className={clsx('surveillance-card', className)}>
      <div className="surveillance-card-header">
        <h3 className="text-lg font-medium text-gray-100">Processing Timeline</h3>
        <p className="text-sm text-gray-400">
          Alerts processed over time
        </p>
      </div>
      <div className="surveillance-card-body">
        <ResponsiveContainer width="100%" height={300}>
          <AreaChart data={data}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="time" />
            <YAxis />
            <Tooltip />
            <Legend />
            <Area
              type="monotone"
              dataKey="processed"
              stackId="1"
              stroke={COLORS.primary}
              fill={COLORS.primary}
              fillOpacity={0.6}
              name="Processed per period"
            />
            <Line
              type="monotone"
              dataKey="cumulative"
              stroke={COLORS.success}
              strokeWidth={2}
              name="Cumulative total"
            />
          </AreaChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
};

export interface ComparisonChartProps {
  data: {
    baseline: {
      name: string;
      falsePositiveRate: number;
      alertsFiltered: number;
      alertsForReview: number;
    };
    aiFarm: {
      name: string;
      falsePositiveRate: number;
      alertsFiltered: number;
      alertsForReview: number;
    };
  };
  className?: string;
}

export const ComparisonChart: React.FC<ComparisonChartProps> = ({
  data,
  className,
}) => {
  const chartData = [
    {
      metric: 'False Positive Rate (%)',
      baseline: data.baseline.falsePositiveRate,
      aiFarm: data.aiFarm.falsePositiveRate,
    },
    {
      metric: 'Alerts Filtered',
      baseline: data.baseline.alertsFiltered,
      aiFarm: data.aiFarm.alertsFiltered,
    },
    {
      metric: 'Alerts for Review',
      baseline: data.baseline.alertsForReview,
      aiFarm: data.aiFarm.alertsForReview,
    },
  ];

  return (
    <div className={clsx('surveillance-card', className)}>
      <div className="surveillance-card-header">
        <h3 className="text-lg font-medium text-gray-100">System Comparison</h3>
        <p className="text-sm text-gray-400">
          AI-FARM vs. Baseline System Performance
        </p>
      </div>
      <div className="surveillance-card-body">
        <ResponsiveContainer width="100%" height={300}>
          <BarChart data={chartData} layout="horizontal">
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis type="number" />
            <YAxis dataKey="metric" type="category" width={120} />
            <Tooltip />
            <Legend />
            <Bar
              dataKey="baseline"
              fill={COLORS.gray}
              name={data.baseline.name}
            />
            <Bar
              dataKey="aiFarm"
              fill={COLORS.primary}
              name={data.aiFarm.name}
            />
          </BarChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
};

export interface ROIProjectionChartProps {
  data: Array<{
    year: number;
    costs: number;
    savings: number;
    netBenefit: number;
    cumulativeBenefit: number;
  }>;
  className?: string;
}

export const ROIProjectionChart: React.FC<ROIProjectionChartProps> = ({
  data,
  className,
}) => {
  return (
    <div className={clsx('surveillance-card', className)}>
      <div className="surveillance-card-header">
        <h3 className="text-lg font-medium text-gray-100">ROI Projection</h3>
        <p className="text-sm text-gray-400">
          5-year cost savings and return on investment
        </p>
      </div>
      <div className="surveillance-card-body">
        <ResponsiveContainer width="100%" height={300}>
          <LineChart data={data}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="year" />
            <YAxis />
            <Tooltip
              formatter={(value: number) => [
                `$${value.toLocaleString()}`,
                undefined
              ]}
            />
            <Legend />
            <Line
              type="monotone"
              dataKey="savings"
              stroke={COLORS.success}
              strokeWidth={2}
              name="Annual Savings"
            />
            <Line
              type="monotone"
              dataKey="costs"
              stroke={COLORS.danger}
              strokeWidth={2}
              name="Annual Costs"
            />
            <Line
              type="monotone"
              dataKey="cumulativeBenefit"
              stroke={COLORS.primary}
              strokeWidth={3}
              name="Cumulative Benefit"
            />
          </LineChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
};