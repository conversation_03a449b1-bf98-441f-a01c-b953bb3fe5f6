import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { Menu, Shield, Activity } from 'lucide-react';
import { SurveillanceNavigation, MobileSurveillanceNavigation } from './SurveillanceNavigation';
import { Button } from '../ui';

export interface HeaderProps {
  className?: string;
}

export const Header: React.FC<HeaderProps> = ({ className }) => {
  const [isMobileNavOpen, setIsMobileNavOpen] = useState(false);

  return (
    <>
      {/* Surveillance Header */}
      <header className={`nav-surveillance ${className}`}>
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Link to="/" className="flex items-center gap-4">
                <Shield className="w-10 h-10 text-primary-500" />
                <div>
                  <h1 className="text-2xl font-bold text-gray-100 tracking-wider">
                    AI-FARM SURVEILLANCE
                  </h1>
                  <p className="text-sm text-gray-400 uppercase tracking-widest">
                    PSA VALO False Positive Reduction System
                  </p>
                </div>
              </Link>
            </div>
            <div className="flex items-center gap-6">
              <div className="text-right">
                <p className="text-xs text-gray-500 uppercase tracking-wider">System Status</p>
                <div className="flex items-center gap-2 text-primary-500">
                  <Activity className="w-4 h-4 animate-pulse" />
                  <span className="font-bold tracking-wider">OPERATIONAL</span>
                </div>
              </div>
              <div className="text-right">
                <p className="text-xs text-gray-500 uppercase tracking-wider">VLM Model</p>
                <p className="text-sm font-mono text-secondary-400">InternVL3 38B AWQ</p>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Navigation Bar */}
      <nav className="bg-dark-400 border-b border-gray-700 shadow-lg">
        <div className="max-w-7xl mx-auto px-6">
          <div className="flex items-center justify-between h-16">
            {/* Desktop Navigation */}
            <div className="hidden lg:block">
              <SurveillanceNavigation />
            </div>

            {/* Actions */}
            <div className="hidden sm:flex items-center space-x-4">
              <Link to="/upload">
                <Button variant="primary" size="sm">
                  Start Demo
                </Button>
              </Link>
            </div>

            {/* Mobile menu button */}
            <button
              type="button"
              className="lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-200 hover:bg-dark-300 transition-colors"
              onClick={() => setIsMobileNavOpen(true)}
            >
              <span className="sr-only">Open navigation menu</span>
              <Menu className="w-6 h-6" />
            </button>
          </div>
        </div>
      </nav>

      {/* Mobile Navigation */}
      <MobileSurveillanceNavigation
        isOpen={isMobileNavOpen}
        onClose={() => setIsMobileNavOpen(false)}
      />
    </>
  );
};