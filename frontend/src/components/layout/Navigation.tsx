import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { X } from 'lucide-react';

const navItems = [
  { path: '/', label: 'Home', icon: '🏠' },
  { path: '/upload', label: 'Upload', icon: '📤' },
  { path: '/monitor', label: 'Monitor', icon: '📊' },
  { path: '/dashboard', label: 'Dashboard', icon: '🛡️' },
  { path: '/analytics', label: 'Analytics', icon: '📈' },
  { path: '/roi', label: 'ROI', icon: '💰' },
  { path: '/insights', label: 'Insights', icon: '💡' },
  { path: '/review', label: 'Review', icon: '👁️' },
  { path: '/learning', label: 'Learning', icon: '🧠' }
];

export const Navigation: React.FC = () => {
  const location = useLocation();
  const isActive = (path: string) => location.pathname === path;

  return (
    <nav className="bg-dark-400 shadow-lg border-b border-gray-700">
      <div className="container mx-auto px-4">
        <div className="flex space-x-8">
          {navItems.map((item) => (
            <Link
              key={item.path}
              to={item.path}
              className={`py-4 px-2 border-b-2 font-medium text-sm transition-colors duration-200 ${
                isActive(item.path)
                  ? 'border-primary-500 text-primary-400'
                  : 'border-transparent text-gray-400 hover:text-gray-200 hover:border-gray-500'
              }`}
            >
              <span className="mr-2">{item.icon}</span>
              {item.label}
            </Link>
          ))}
        </div>
      </div>
    </nav>
  );
};

interface MobileNavigationProps {
  isOpen: boolean;
  onClose: () => void;
}

export const MobileNavigation: React.FC<MobileNavigationProps> = ({ isOpen, onClose }) => {
  const location = useLocation();
  const isActive = (path: string) => location.pathname === path;

  if (!isOpen) return null;

  return (
    <div className="lg:hidden">
      {/* Backdrop */}
      <div 
        className="fixed inset-0 bg-black bg-opacity-50 z-40"
        onClick={onClose}
      />
      
      {/* Sidebar */}
      <div className="fixed top-0 right-0 w-64 h-full bg-dark-400 shadow-lg z-50 transform transition-transform duration-300">
        <div className="flex items-center justify-between p-4 border-b border-gray-700">
          <h2 className="text-lg font-semibold text-gray-100">Menu</h2>
          <button
            onClick={onClose}
            className="p-2 rounded-md text-gray-400 hover:text-gray-200 hover:bg-dark-300"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        <nav className="p-4">
          {navItems.map((item) => (
            <Link
              key={item.path}
              to={item.path}
              onClick={onClose}
              className={`flex items-center py-3 px-4 rounded-md mb-2 transition-colors duration-200 ${
                isActive(item.path)
                  ? 'bg-primary-900 text-primary-400 border-l-4 border-primary-500'
                  : 'text-gray-300 hover:bg-dark-300'
              }`}
            >
              <span className="mr-3 text-lg">{item.icon}</span>
              {item.label}
            </Link>
          ))}
        </nav>
      </div>
    </div>
  );
};