import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import {
  Shield,
  Upload,
  BarChart3,
  DollarSign,
  Lightbulb,
  Activity,
  X,
  Database,
  Cpu
} from 'lucide-react';

const navItems = [
  { path: '/', label: 'Dashboard', icon: Shield },
  { path: '/upload', label: 'Upload', icon: Upload },
  { path: '/batch-processing', label: 'Batch Process', icon: Cpu },
  { path: '/results', label: 'Results', icon: BarChart3 },
  { path: '/real-results', label: 'Real Results', icon: Activity },
  { path: '/data-analysis', label: 'Data Analysis', icon: Database },
  { path: '/roi', label: 'ROI', icon: DollarSign },
  { path: '/insights', label: 'Insights', icon: Lightbulb }
];

export const SurveillanceNavigation: React.FC = () => {
  const location = useLocation();
  const isActive = (path: string) => location.pathname === path || location.pathname.startsWith(path + '/');

  return (
    <nav className="flex items-center space-x-8">
      {navItems.map((item) => {
        const IconComponent = item.icon;
        return (
          <Link
            key={item.path}
            to={item.path}
            className={`flex items-center space-x-2 px-3 py-2 rounded-md font-medium text-sm transition-all duration-200 uppercase tracking-wider ${
              isActive(item.path)
                ? 'bg-primary-900 text-primary-400 border border-primary-500'
                : 'text-gray-400 hover:text-gray-200 hover:bg-dark-300'
            }`}
          >
            <IconComponent className="w-4 h-4" />
            <span>{item.label}</span>
          </Link>
        );
      })}
    </nav>
  );
};

interface MobileSurveillanceNavigationProps {
  isOpen: boolean;
  onClose: () => void;
}

export const MobileSurveillanceNavigation: React.FC<MobileSurveillanceNavigationProps> = ({ 
  isOpen, 
  onClose 
}) => {
  const location = useLocation();
  const isActive = (path: string) => location.pathname === path || location.pathname.startsWith(path + '/');

  if (!isOpen) return null;

  return (
    <div className="lg:hidden">
      {/* Backdrop */}
      <div 
        className="fixed inset-0 bg-black bg-opacity-75 z-40"
        onClick={onClose}
      />
      
      {/* Sidebar */}
      <div className="fixed top-0 right-0 w-80 h-full bg-dark-400 shadow-2xl z-50 transform transition-transform duration-300 border-l border-gray-700">
        <div className="flex items-center justify-between p-6 border-b border-gray-700">
          <div className="flex items-center space-x-3">
            <Shield className="w-6 h-6 text-primary-500" />
            <h2 className="text-lg font-bold text-gray-100 uppercase tracking-wider">
              AI-FARM Menu
            </h2>
          </div>
          <button
            onClick={onClose}
            className="p-2 rounded-md text-gray-400 hover:text-gray-200 hover:bg-dark-300 transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>
        
        <nav className="p-6">
          <div className="space-y-2">
            {navItems.map((item) => {
              const IconComponent = item.icon;
              return (
                <Link
                  key={item.path}
                  to={item.path}
                  onClick={onClose}
                  className={`flex items-center space-x-3 px-4 py-3 rounded-md transition-all duration-200 uppercase tracking-wider font-medium ${
                    isActive(item.path)
                      ? 'bg-primary-900 text-primary-400 border-l-4 border-primary-500'
                      : 'text-gray-300 hover:bg-dark-300 hover:text-gray-100'
                  }`}
                >
                  <IconComponent className="w-5 h-5" />
                  <span>{item.label}</span>
                  {isActive(item.path) && (
                    <div className="ml-auto">
                      <Activity className="w-4 h-4 text-primary-500 animate-pulse" />
                    </div>
                  )}
                </Link>
              );
            })}
          </div>
          
          {/* System Status in Mobile Menu */}
          <div className="mt-8 pt-6 border-t border-gray-700">
            <div className="text-center">
              <p className="text-xs text-gray-500 uppercase tracking-wider mb-2">System Status</p>
              <div className="flex items-center justify-center space-x-2 text-primary-500">
                <Activity className="w-4 h-4 animate-pulse" />
                <span className="font-bold tracking-wider text-sm">OPERATIONAL</span>
              </div>
            </div>
          </div>
        </nav>
      </div>
    </div>
  );
};
