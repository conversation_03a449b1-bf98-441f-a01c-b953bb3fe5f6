import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { Layout } from './components/layout/Layout';
import {
  LandingPage,
  UploadPage,
  ProcessingPage,
  MonitorPage,
  ResultsPage,
  AnalyticsPage,
  ReviewPage,
  ROIPage,
  InsightsPage,
  LearningPage,
  SurveillanceDashboard,
  DataAnalysisDashboard
} from './pages/index';
import { BatchProcessingPage } from './pages/BatchProcessingPage';
import { RealResultsPage } from './pages/RealResultsPage';
import './App.css';
import './styles/surveillance.css';

function App() {
  return (
    <Router>
      <Routes>
        {/* Dashboard is the home page */}
        <Route path="/" element={<SurveillanceDashboard />} />
        <Route path="/home" element={<SurveillanceDashboard />} />
        <Route path="/dashboard" element={<SurveillanceDashboard />} />

        {/* Landing page for marketing/demo purposes */}
        <Route path="/landing" element={<LandingPage />} />

        {/* New pages with Layout */}
        <Route path="/monitor" element={<Layout><MonitorPage /></Layout>} />
        <Route path="/analytics" element={<Layout><AnalyticsPage /></Layout>} />
        <Route path="/review" element={<Layout><ReviewPage /></Layout>} />

        {/* Existing pages use the Layout with surveillance navigation */}
        <Route path="/upload" element={<Layout><UploadPage /></Layout>} />
        <Route path="/processing" element={<Layout><ProcessingPage /></Layout>} />
        <Route path="/processing/:batchId" element={<Layout><ProcessingPage /></Layout>} />
        <Route path="/results" element={<Layout><ResultsPage /></Layout>} />
        <Route path="/results/:batchId" element={<Layout><ResultsPage /></Layout>} />
        <Route path="/roi" element={<Layout><ROIPage /></Layout>} />
        <Route path="/roi/:batchId" element={<Layout><ROIPage /></Layout>} />
        <Route path="/insights" element={<Layout><InsightsPage /></Layout>} />
        <Route path="/insights/:batchId" element={<Layout><InsightsPage /></Layout>} />
        <Route path="/learning" element={<Layout><LearningPage /></Layout>} />
        <Route path="/data-analysis" element={<Layout><DataAnalysisDashboard /></Layout>} />
        <Route path="/batch-processing" element={<Layout><BatchProcessingPage /></Layout>} />
        <Route path="/real-results" element={<Layout><RealResultsPage /></Layout>} />
      </Routes>
    </Router>
  );
}

export default App;