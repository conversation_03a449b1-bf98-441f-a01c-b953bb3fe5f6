// API Types matching backend schemas

export type ValidationStatus = 'valid' | 'invalid' | 'invalid_false_positive' | 'unknown';
export type ImageType = 'source' | 'cropped';
export enum ProcessingStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed'
}
export type VLMRecommendation = 'DISMISS_ALERT' | 'REQUIRES_REVIEW';
export type ProcessingPriority = 'low' | 'normal' | 'high';
export type HealthStatus = 'healthy' | 'unhealthy';
export type ConnectionStatus = 'connected' | 'disconnected' | 'error';

export interface VLMAnalysisResult {
  detection_type: string;
  false_positive_likelihood: number;
  true_violation_likelihood: number;
  reasoning: string;
  recommendation: VLMRecommendation;
  confidence_score: number;
  processing_time_ms?: number;
}

export interface CaseData {
  pk_event: number;
  case_number: string;
  image_url: string;
  validation_status: ValidationStatus;
}

export interface ImageGalleryItem {
  id: string;
  imageUrl: string;
  thumbnailUrl?: string;
  caseNumber?: string;
  confidence?: number;
  recommendation?: string;
  category: 'all' | 'filtered' | 'review_required';
  processingTime?: number;
  title?: string;
  description?: string;
}

export interface ProcessingRequest {
  case_numbers: string[];
  use_auto_learning?: boolean;
  custom_thresholds?: Record<string, number>;
  priority?: ProcessingPriority;
}

export interface ProcessingResult {
  case_number: string;
  pk_event: number;
  images_processed: Array<Record<string, any>>;
  vlm_results: VLMAnalysisResult[];
  final_recommendation: string;
  processing_status: ProcessingStatus;
  processing_time_total_ms: number;
  error_message?: string;
}

export interface BatchProcessingResponse {
  batch_id: string;
  total_cases: number;
  status: ProcessingStatus;
  results: ProcessingResult[];
  summary: Record<string, any>;
  started_at: string;
  completed_at?: string;
}

export interface DemoMetrics {
  total_alerts_processed: number;
  false_positives_filtered: number;
  alerts_requiring_review: number;
  filter_rate_percentage: number;
  time_saved_hours: number;
  cost_savings_annual: number;
  processing_time_avg_ms: number;
  confidence_score_avg: number;
}

export interface PatternCategory {
  category: string;
  pattern_count: number;
  confidence_level: number;
  false_positive_reduction: number;
  description: string;
  examples: string[];
}

export interface ImplementationRecommendation {
  priority: 'High' | 'Medium' | 'Low';
  category: string;
  recommendation: string;
  expected_impact: string;
  implementation_effort: 'Low' | 'Medium' | 'High';
  timeline: string;
}

export interface PerformanceMetrics {
  processing_speed_ms: number;
  memory_efficiency: number;
  model_accuracy: number;
  false_positive_rate: number;
  true_positive_rate: number;
  system_uptime: number;
}

export interface LearningSummary {
  total_patterns_detected: number;
  accuracy_improvement_percentage: number;
  confidence_score_average: number;
  learning_iterations: number;
  threshold_optimizations: number;
}

export interface AutoLearningInsights {
  batch_id: string;
  learning_summary: LearningSummary;
  pattern_categories: PatternCategory[];
  implementation_recommendations: ImplementationRecommendation[];
  performance_metrics: PerformanceMetrics;
  // Legacy fields for backward compatibility
  detected_patterns?: Record<string, any>;
  optimized_thresholds?: Record<string, number>;
  accuracy_improvement?: number;
  confidence_calibration?: Record<string, number>;
  recommendations?: string[];
}

export interface CustomerEnvironmentAnalysis {
  unique_structures: string[];
  common_false_positive_patterns: string[];
  lighting_conditions: string;
  camera_angles: string[];
  equipment_types: string[];
  optimization_opportunities: string[];
}

export interface HealthCheckResponse {
  status: HealthStatus;
  timestamp: string;
  version: string;
  vlm_api_status: ConnectionStatus;
  database_status: ConnectionStatus;
  uptime_seconds: number;
}

export interface ErrorResponse {
  error: string;
  message: string;
  details?: Record<string, any>;
  timestamp: string;
  request_id?: string;
}

// ROI Calculation Types
export interface ROIProjection {
  batch_id: string;
  customer_parameters: {
    monthly_alerts: number;
    hourly_rate: number;
    implementation_cost: number;
  };
  demo_performance: {
    sample_size: number;
    filter_rate_percentage: number;
    avg_confidence_score: number;
  };
  projected_impact: {
    monthly_false_positives_filtered: number;
    monthly_time_saved_hours: number;
    monthly_cost_savings: number;
    annual_cost_savings: number;
    workload_reduction_percentage: number;
  };
  roi_analysis: {
    implementation_cost: number;
    annual_net_benefit: number;
    roi_percentage: number;
    payback_period_months: number;
    break_even_point: string;
  };
  comparison: {
    before: {
      monthly_alerts_to_review: number;
      monthly_review_hours: number;
      monthly_cost: number;
    };
    after: {
      monthly_alerts_to_review: number;
      monthly_review_hours: number;
      monthly_cost: number;
    };
  };
}

// Terminal Performance Types
export interface TerminalPerformance {
  terminal_id: string;
  name: string;
  accuracy: number;
  false_positive_rate: number;
  patterns_learned: number;
  last_optimization: string;
}

export interface CustomerInfo {
  name: string;
  location: string;
  terminal_count: number;
  active_since: string;
  demo_date?: string;
  batch_id?: string;
}

export interface AutoLearningSummary {
  patterns_detected: number;
  accuracy_improvement: number;
  thresholds_optimized: number;
  recommendations_count: number;
  learning_velocity: number;
  confidence_trend: 'increasing' | 'decreasing' | 'stable';
}

// Dashboard Data Types
export interface DashboardData {
  customer_info: CustomerInfo;
  headline_metrics?: {
    false_positive_reduction: string;
    alerts_filtered: number;
    time_saved_monthly: string;
    annual_savings: string;
    roi_percentage: string;
    payback_months: string;
  };
  processing_performance?: {
    total_alerts_processed: number;
    processing_time_avg_ms: number;
    confidence_score_avg: number;
    batch_duration?: number;
  };
  business_impact?: {
    workload_reduction_percentage: number;
    monthly_cost_savings: number;
    annual_net_benefit: number;
    break_even_point: string;
  };
  comparison_vs_baseline?: {
    filter_rate_improvement_percentage: number;
    improvement_factor: number;
    additional_alerts_filtered: number;
    workload_reduction_percentage: number;
  };
  auto_learning_summary?: AutoLearningSummary;
  terminal_performance?: TerminalPerformance[];
  next_steps?: string[];
}

// Batch Results with Pagination
export interface BatchResultsResponse {
  batch_id: string;
  results: Array<{
    case_number: string;
    pk_event: number;
    image_url: string;
    validation_status: ValidationStatus;
    final_recommendation: string;
    processing_status: ProcessingStatus;
    processing_time_ms: number;
    vlm_results: VLMAnalysisResult[];
    error_message?: string;
    processed_at: string;
  }>;
  pagination: {
    total: number;
    limit: number;
    offset: number;
    has_more: boolean;
  };
}

// Performance Comparison
export interface PerformanceComparison {
  batch_id: string;
  comparison: {
    baseline_system: {
      filter_rate_percentage: number;
      alerts_filtered: number;
      alerts_for_review: number;
    };
    ai_farm_system: {
      filter_rate_percentage: number;
      alerts_filtered: number;
      alerts_for_review: number;
    };
  };
  improvement_metrics: {
    filter_rate_improvement_percentage: number;
    improvement_factor: number;
    additional_alerts_filtered: number;
    workload_reduction_percentage: number;
  };
  performance_summary: {
    total_alerts_analyzed: number;
    average_processing_time_ms: number;
    average_confidence_score: number;
    system_reliability: string;
  };
}

// Historical Performance
export interface HistoricalPerformance {
  date_range: {
    start_date: string;
    end_date: string;
    days: number;
  };
  aggregate_metrics: {
    total_batches: number;
    total_cases_processed: number;
    total_alerts_filtered: number;
    average_filter_rate_percentage: number;
    average_processing_time_ms: number;
  };
  performance_trends: {
    system_stability: string;
    processing_efficiency: string;
    filter_effectiveness: string;
  };
}