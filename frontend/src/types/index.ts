export * from './api';

// Additional UI-specific types
export interface NavigationItem {
  name: string;
  href: string;
  icon?: React.ComponentType<any>;
  current?: boolean;
}

export interface MetricCard {
  title: string;
  value: string | number;
  subtitle?: string;
  trend?: {
    value: number;
    direction: 'up' | 'down' | 'neutral';
    label: string;
  };
  icon?: React.ComponentType<any>;
  color?: 'primary' | 'success' | 'warning' | 'danger';
}

export interface ChartDataPoint {
  name: string;
  value: number;
  percentage?: number;
  color?: string;
}

export interface UploadProgress {
  filename: string;
  progress: number;
  status: 'uploading' | 'processing' | 'completed' | 'error';
  error?: string;
}

export interface ProcessingProgress {
  batchId: string;
  totalCases: number;
  processedCases: number;
  failedCases: number;
  progressPercentage: number;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  startedAt: string;
  estimatedTimeRemaining?: number;
  currentCase?: string;
}

export interface AlertItem {
  id: string;
  type: 'success' | 'warning' | 'error' | 'info';
  title: string;
  message: string;
  timestamp: string;
  dismissible?: boolean;
}

export interface FilterOption {
  label: string;
  value: string;
  count?: number;
}

export interface SortOption {
  label: string;
  value: string;
  direction: 'asc' | 'desc';
}

// Demo Configuration
export interface DemoConfig {
  customerName?: string;
  monthlyAlerts?: number;
  hourlyRate?: number;
  implementationCost?: number;
  baselineFilterRate?: number;
}

// Image Gallery Types
export interface ImageGalleryItem {
  id: string;
  src: string;
  alt: string;
  category: 'filtered' | 'review_required';
  confidence?: number;
  recommendation?: string;
  caseNumber?: string;
  processingTime?: number;
}

// Loading States
export interface LoadingState {
  isLoading: boolean;
  error?: string;
  retryCount?: number;
}

// Form Types
export interface FormFieldError {
  field: string;
  message: string;
}

export interface FormState<T = any> {
  data: T;
  errors: FormFieldError[];
  isSubmitting: boolean;
  isDirty: boolean;
  isValid: boolean;
}