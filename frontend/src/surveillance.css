/* AI-FARM Professional Surveillance Theme */

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  /* Dark theme base */
  html {
    @apply antialiased;
    background-color: #0a0a0a;
  }
  
  body {
    @apply bg-dark-500 text-gray-100;
    font-family: 'Inter', monospace;
  }
}

@layer components {
  /* Surveillance Card Components */
  .surveillance-card {
    @apply bg-dark-50 border border-gray-800 rounded-md shadow-lg;
    background: linear-gradient(145deg, #1a1a1a 0%, #141414 100%);
  }
  
  .surveillance-card-header {
    @apply px-6 py-4 border-b border-gray-800;
  }
  
  .surveillance-card-body {
    @apply px-6 py-4;
  }
  
  /* Professional Buttons */
  .btn-surveillance {
    @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded 
           transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-dark-500
           disabled:opacity-50 disabled:cursor-not-allowed uppercase tracking-wider;
  }
  
  .btn-surveillance-primary {
    @apply btn-surveillance bg-transparent border border-primary-500 text-primary-500 
           hover:bg-primary-500 hover:text-dark-900 hover:shadow-lg hover:shadow-primary-500/30
           focus:ring-primary-500;
  }
  
  .btn-surveillance-danger {
    @apply btn-surveillance bg-transparent border border-danger-500 text-danger-500 
           hover:bg-danger-500 hover:text-dark-900 hover:shadow-lg hover:shadow-danger-500/30
           focus:ring-danger-500;
  }
  
  .btn-surveillance-warning {
    @apply btn-surveillance bg-transparent border border-warning-500 text-warning-500 
           hover:bg-warning-500 hover:text-dark-900 hover:shadow-lg hover:shadow-warning-500/30
           focus:ring-warning-500;
  }
  
  /* Alert Status Indicators */
  .status-badge-surveillance {
    @apply inline-flex items-center px-3 py-1 rounded text-xs font-bold uppercase tracking-wider;
  }
  
  .status-false-positive {
    @apply status-badge-surveillance bg-danger-900/20 text-danger-400 border border-danger-800;
  }
  
  .status-valid-alert {
    @apply status-badge-surveillance bg-success-900/20 text-success-400 border border-success-800;
  }
  
  .status-pending-review {
    @apply status-badge-surveillance bg-warning-900/20 text-warning-400 border border-warning-800;
  }
  
  /* Terminal Badges */
  .terminal-badge {
    @apply inline-flex items-center px-2 py-0.5 rounded text-xs font-bold uppercase tracking-wider;
  }
  
  .terminal-p1 {
    @apply terminal-badge bg-danger-900/30 text-danger-400 border border-danger-800;
  }
  
  .terminal-p2 {
    @apply terminal-badge bg-secondary-900/30 text-secondary-400 border border-secondary-800;
  }
  
  .terminal-p3 {
    @apply terminal-badge bg-success-900/30 text-success-400 border border-success-800;
  }
  
  /* Metric Cards */
  .metric-card-surveillance {
    @apply surveillance-card p-6 relative overflow-hidden;
  }
  
  .metric-card-surveillance::before {
    content: '';
    @apply absolute top-0 left-0 w-full h-1 bg-gradient-to-r;
  }
  
  .metric-card-surveillance.metric-primary::before {
    @apply from-primary-500 to-primary-600;
  }
  
  .metric-card-surveillance.metric-danger::before {
    @apply from-danger-500 to-danger-600;
  }
  
  .metric-card-surveillance.metric-warning::before {
    @apply from-warning-500 to-warning-600;
  }
  
  .metric-card-surveillance.metric-success::before {
    @apply from-success-500 to-success-600;
  }
  
  .metric-value-surveillance {
    @apply text-4xl font-bold text-primary-400;
    text-shadow: 0 0 20px rgba(0, 255, 136, 0.5);
  }
  
  .metric-label-surveillance {
    @apply text-sm font-medium text-gray-400 uppercase tracking-wider;
  }
  
  /* Alert Feed Styles */
  .alert-feed-container {
    @apply surveillance-card h-full max-h-[600px] overflow-hidden;
  }
  
  .alert-feed-header {
    @apply surveillance-card-header flex items-center justify-between;
  }
  
  .alert-feed-body {
    @apply overflow-y-auto h-full;
    max-height: calc(100% - 73px);
  }
  
  .alert-item {
    @apply border-b border-gray-800 p-4 hover:bg-dark-100 transition-colors duration-200;
  }
  
  .alert-item:last-child {
    @apply border-b-0;
  }
  
  /* VLM Processing Animation */
  .processing-indicator {
    @apply flex items-center gap-2;
  }
  
  .processing-dot {
    @apply w-2 h-2 bg-primary-500 rounded-full;
    animation: pulse-glow 1.5s infinite;
  }
  
  .processing-dot:nth-child(2) {
    animation-delay: 0.3s;
  }
  
  .processing-dot:nth-child(3) {
    animation-delay: 0.6s;
  }
  
  @keyframes pulse-glow {
    0%, 60%, 100% {
      transform: scale(1);
      opacity: 1;
      box-shadow: 0 0 0 0 rgba(0, 255, 136, 0.7);
    }
    30% {
      transform: scale(1.5);
      opacity: 0.7;
      box-shadow: 0 0 10px 5px rgba(0, 255, 136, 0.3);
    }
  }
  
  /* Chart Styles */
  .chart-container {
    @apply surveillance-card p-6;
  }
  
  .chart-title {
    @apply text-lg font-semibold text-gray-100 mb-4;
  }
  
  /* Table Styles */
  .surveillance-table {
    @apply min-w-full divide-y divide-gray-800;
  }
  
  .surveillance-table-header {
    @apply bg-dark-100;
  }
  
  .surveillance-table-header-cell {
    @apply px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider;
  }
  
  .surveillance-table-row {
    @apply bg-dark-50 hover:bg-dark-100 transition-colors duration-200;
  }
  
  .surveillance-table-cell {
    @apply px-6 py-4 whitespace-nowrap text-sm text-gray-300;
  }
  
  /* Navigation Styles */
  .nav-surveillance {
    @apply bg-dark-100 border-b border-gray-800;
  }
  
  .nav-link-surveillance {
    @apply flex items-center px-4 py-3 text-sm font-medium transition-all duration-200
           border-b-2 border-transparent uppercase tracking-wider;
  }
  
  .nav-link-surveillance-active {
    @apply text-primary-400 border-primary-400;
  }
  
  .nav-link-surveillance-inactive {
    @apply text-gray-400 hover:text-gray-200 hover:border-gray-600;
  }
  
  /* Grid System */
  .surveillance-grid {
    @apply grid gap-6;
  }
  
  /* Scrollbar Styling */
  .surveillance-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: #333333 transparent;
  }
  
  .surveillance-scrollbar::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }
  
  .surveillance-scrollbar::-webkit-scrollbar-track {
    background: transparent;
  }
  
  .surveillance-scrollbar::-webkit-scrollbar-thumb {
    background-color: #333333;
    border-radius: 4px;
  }
  
  .surveillance-scrollbar::-webkit-scrollbar-thumb:hover {
    background-color: #444444;
  }
  
  /* Glow Effects */
  .glow-primary {
    box-shadow: 0 0 20px rgba(0, 255, 136, 0.3);
  }
  
  .glow-danger {
    box-shadow: 0 0 20px rgba(255, 51, 102, 0.3);
  }
  
  .glow-warning {
    box-shadow: 0 0 20px rgba(255, 170, 0, 0.3);
  }
  
  /* VALO Context Specific */
  .valo-alert-thumbnail {
    @apply w-20 h-16 bg-dark-100 border border-gray-800 rounded overflow-hidden;
  }
  
  .camera-indicator {
    @apply inline-flex items-center gap-1 text-xs text-gray-500 font-mono;
  }
  
  .confidence-meter {
    @apply relative h-2 bg-dark-100 rounded-full overflow-hidden;
  }
  
  .confidence-meter-fill {
    @apply absolute top-0 left-0 h-full bg-gradient-to-r from-danger-500 via-warning-500 to-success-500;
  }
}

@layer utilities {
  /* Text Shadow Utilities */
  .text-glow-primary {
    text-shadow: 0 0 10px rgba(0, 255, 136, 0.5);
  }
  
  .text-glow-danger {
    text-shadow: 0 0 10px rgba(255, 51, 102, 0.5);
  }
  
  .text-glow-warning {
    text-shadow: 0 0 10px rgba(255, 170, 0, 0.5);
  }
  
  /* Background Patterns */
  .bg-grid-pattern {
    background-image: 
      linear-gradient(rgba(255, 255, 255, 0.02) 1px, transparent 1px),
      linear-gradient(90deg, rgba(255, 255, 255, 0.02) 1px, transparent 1px);
    background-size: 50px 50px;
  }
  
  /* Animations */
  .animate-scan-line {
    animation: scan-line 8s linear infinite;
  }
  
  @keyframes scan-line {
    0% {
      transform: translateY(-100%);
    }
    100% {
      transform: translateY(100%);
    }
  }
}