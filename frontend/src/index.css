@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    @apply antialiased;
    background-color: #0a0a0a;
  }

  body {
    @apply bg-dark-500 text-gray-100;
    font-family: 'Inter', monospace;
  }
}

@layer components {
  /* Button Components */
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
  }
  
  .btn-primary {
    @apply btn bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500;
  }
  
  .btn-secondary {
    @apply btn bg-gray-200 text-gray-900 hover:bg-gray-300 focus:ring-gray-500;
  }
  
  .btn-success {
    @apply btn bg-success-600 text-white hover:bg-success-700 focus:ring-success-500;
  }
  
  .btn-warning {
    @apply btn bg-warning-600 text-white hover:bg-warning-700 focus:ring-warning-500;
  }
  
  .btn-danger {
    @apply btn bg-danger-600 text-white hover:bg-danger-700 focus:ring-danger-500;
  }
  
  .btn-outline {
    @apply btn border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-primary-500;
  }
  
  .btn-sm {
    @apply px-3 py-1.5 text-xs;
  }
  
  .btn-lg {
    @apply px-6 py-3 text-base;
  }
  
  /* Card Components */
  .card {
    @apply bg-white rounded-lg shadow-sm border border-gray-200;
  }
  
  .card-header {
    @apply px-6 py-4 border-b border-gray-200;
  }
  
  .card-body {
    @apply px-6 py-4;
  }
  
  .card-footer {
    @apply px-6 py-4 border-t border-gray-200 bg-gray-50;
  }
  
  /* Form Components */
  .form-input {
    @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500;
  }
  
  .form-label {
    @apply block text-sm font-medium text-gray-700 mb-1;
  }
  
  .form-error {
    @apply text-sm text-danger-600 mt-1;
  }
  
  .form-help {
    @apply text-sm text-gray-500 mt-1;
  }
  
  /* Status Indicators */
  .status-badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }
  
  .status-success {
    @apply status-badge bg-success-100 text-success-800;
  }
  
  .status-warning {
    @apply status-badge bg-warning-100 text-warning-800;
  }
  
  .status-danger {
    @apply status-badge bg-danger-100 text-danger-800;
  }
  
  .status-info {
    @apply status-badge bg-primary-100 text-primary-800;
  }
  
  .status-neutral {
    @apply status-badge bg-gray-100 text-gray-800;
  }
  
  /* Progress Bars */
  .progress-bar {
    @apply w-full bg-gray-200 rounded-full overflow-hidden;
  }
  
  .progress-fill {
    @apply h-full bg-primary-600 transition-all duration-300 ease-out;
  }
  
  .progress-fill-success {
    @apply bg-success-600;
  }
  
  .progress-fill-warning {
    @apply bg-warning-600;
  }
  
  .progress-fill-danger {
    @apply bg-danger-600;
  }
  
  /* Loading Spinner */
  .spinner {
    @apply animate-spin rounded-full border-2 border-gray-300 border-t-primary-600;
  }
  
  /* Metric Cards */
  .metric-card {
    @apply card p-6;
  }
  
  .metric-value {
    @apply text-3xl font-bold text-gray-900;
  }
  
  .metric-label {
    @apply text-sm font-medium text-gray-500 uppercase tracking-wide;
  }
  
  .metric-trend {
    @apply flex items-center text-sm font-medium;
  }
  
  .metric-trend-up {
    @apply metric-trend text-success-600;
  }
  
  .metric-trend-down {
    @apply metric-trend text-danger-600;
  }
  
  .metric-trend-neutral {
    @apply metric-trend text-gray-500;
  }
  
  /* Navigation */
  .nav-link {
    @apply flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors duration-200;
  }
  
  .nav-link-active {
    @apply nav-link bg-primary-100 text-primary-700;
  }
  
  .nav-link-inactive {
    @apply nav-link text-gray-600 hover:text-gray-900 hover:bg-gray-50;
  }
  
  /* Tables */
  .table {
    @apply min-w-full divide-y divide-gray-200;
  }
  
  .table-header {
    @apply bg-gray-50;
  }
  
  .table-header-cell {
    @apply px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;
  }
  
  .table-row {
    @apply bg-white;
  }
  
  .table-row-striped {
    @apply odd:bg-white even:bg-gray-50;
  }
  
  .table-cell {
    @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900;
  }
  
  /* Alerts */
  .alert {
    @apply rounded-md p-4;
  }
  
  .alert-success {
    @apply alert bg-success-50 border border-success-200;
  }
  
  .alert-warning {
    @apply alert bg-warning-50 border border-warning-200;
  }
  
  .alert-danger {
    @apply alert bg-danger-50 border border-danger-200;
  }
  
  .alert-info {
    @apply alert bg-primary-50 border border-primary-200;
  }
  
  /* Tooltips */
  .tooltip {
    @apply absolute z-10 px-3 py-2 text-sm font-medium text-white bg-gray-900 rounded-lg shadow-sm opacity-0 transition-opacity duration-300;
  }
  
  .tooltip-show {
    @apply opacity-100;
  }
}

@layer utilities {
  /* Custom animations */
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }
  
  .animate-slide-up {
    animation: slideUp 0.5s ease-out;
  }
  
  .animate-pulse-slow {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }
  
  /* Gradient backgrounds */
  .bg-gradient-primary {
    @apply bg-gradient-to-r from-primary-600 to-primary-700;
  }
  
  .bg-gradient-success {
    @apply bg-gradient-to-r from-success-600 to-success-700;
  }
  
  .bg-gradient-warning {
    @apply bg-gradient-to-r from-warning-600 to-warning-700;
  }
  
  .bg-gradient-danger {
    @apply bg-gradient-to-r from-danger-600 to-danger-700;
  }
  
  /* Text gradients */
  .text-gradient-primary {
    @apply bg-gradient-to-r from-primary-600 to-primary-700 bg-clip-text text-transparent;
  }
  
  /* Shadows */
  .shadow-card {
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  }
  
  .shadow-card-hover {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  }
  
  /* Borders */
  .border-dashed-primary {
    @apply border-2 border-dashed border-primary-300;
  }
  
  /* Scrollbars */
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
  }
  
  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  
  .scrollbar-thin::-webkit-scrollbar-track {
    background: transparent;
  }
  
  .scrollbar-thin::-webkit-scrollbar-thumb {
    background-color: rgba(156, 163, 175, 0.5);
    border-radius: 3px;
  }
  
  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background-color: rgba(156, 163, 175, 0.7);
  }
}