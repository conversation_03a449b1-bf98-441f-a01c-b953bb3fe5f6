/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./src/**/*.{js,jsx,ts,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        // Surveillance theme colors
        primary: {
          50: '#e6fff4',
          100: '#ccffe9',
          200: '#99ffd4',
          300: '#66ffbe',
          400: '#33ffa9',
          500: '#00ff88',
          600: '#00cc6b',
          700: '#00994f',
          800: '#006633',
          900: '#003318',
        },
        secondary: {
          50: '#e6f3ff',
          100: '#cce7ff',
          200: '#99cfff',
          300: '#66b7ff',
          400: '#339fff',
          500: '#0088ff',
          600: '#006bcc',
          700: '#004f99',
          800: '#003366',
          900: '#001833',
        },
        success: {
          50: '#e6fff4',
          100: '#ccffe9',
          200: '#99ffd4',
          300: '#66ffbe',
          400: '#33ffa9',
          500: '#00ff88',
          600: '#00cc6b',
          700: '#00994f',
          800: '#006633',
          900: '#003318',
        },
        warning: {
          50: '#fff4e6',
          100: '#ffe9cc',
          200: '#ffd499',
          300: '#ffbe66',
          400: '#ffa933',
          500: '#ffaa00',
          600: '#cc8800',
          700: '#996600',
          800: '#664400',
          900: '#332200',
        },
        danger: {
          50: '#ffe6ee',
          100: '#ffccdd',
          200: '#ff99bb',
          300: '#ff6699',
          400: '#ff3377',
          500: '#ff3366',
          600: '#cc2952',
          700: '#991f3d',
          800: '#661429',
          900: '#330a14',
        },
        // Dark theme backgrounds
        dark: {
          50: '#1a1a1a',
          100: '#171717',
          200: '#141414',
          300: '#111111',
          400: '#0e0e0e',
          500: '#0a0a0a',
          600: '#080808',
          700: '#050505',
          800: '#030303',
          900: '#000000',
        },
        gray: {
          50: '#f5f5f5',
          100: '#e0e0e0',
          200: '#cccccc',
          300: '#b3b3b3',
          400: '#999999',
          500: '#808080',
          600: '#666666',
          700: '#4d4d4d',
          800: '#333333',
          900: '#1a1a1a',
        },
      },
      fontFamily: {
        sans: ['Inter', 'ui-sans-serif', 'system-ui', '-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', 'Noto Sans', 'sans-serif'],
      },
      animation: {
        'fade-in': 'fadeIn 0.5s ease-in-out',
        'slide-up': 'slideUp 0.5s ease-out',
        'pulse-slow': 'pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideUp: {
          '0%': { transform: 'translateY(20px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
      },
    },
  },
  plugins: [
    require('@tailwindcss/forms'),
    require('@tailwindcss/typography'),
  ],
}