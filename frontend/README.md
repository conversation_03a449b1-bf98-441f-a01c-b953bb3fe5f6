# AI-FARM Frontend Dashboard

A complete React TypeScript frontend for the AI-FARM (False Positive Alert Reduction System) demo dashboard.

## Features

### 🚀 Complete Demo Experience
- **Landing Page**: Problem presentation and value proposition
- **Upload Interface**: CSV and image file upload with validation
- **Real-time Processing**: Live progress tracking with charts
- **Results Dashboard**: Detailed analysis with image galleries
- **ROI Calculator**: Financial impact analysis with customizable parameters
- **AI Insights**: Auto-learning recommendations and implementation guidance

### 🎨 Professional UI Components
- **Responsive Design**: Mobile-first approach with Tailwind CSS
- **Interactive Charts**: Real-time data visualization with Recharts
- **File Upload**: Drag-and-drop interface with progress tracking
- **Image Gallery**: Zoomable gallery with filtering and sorting
- **Metric Cards**: Professional KPI displays with trends
- **Progress Indicators**: Real-time processing status updates

### 🔧 Technical Architecture
- **React 18** with TypeScript for type safety
- **React Router** for seamless navigation
- **React Query** for efficient data fetching and caching
- **Tailwind CSS** for utility-first styling
- **Axios** for API communication with comprehensive error handling
- **React Hot Toast** for user notifications

## Quick Start

### Prerequisites
- Node.js 16+ and npm
- AI-FARM backend running on `http://localhost:8000`

### Installation
```bash
# Install dependencies
npm install

# Copy environment configuration
cp .env.example .env.local

# Start development server
npm start
```

The application will be available at `http://localhost:3000`

### Build for Production
```bash
# Create production build
npm run build

# Test production build locally
npx serve -s build
```

## Project Structure

```
src/
├── components/           # Reusable UI components
│   ├── ui/              # Base UI components (Button, Card, etc.)
│   ├── charts/          # Chart components using Recharts
│   ├── dashboard/       # Specialized dashboard components
│   └── layout/          # Layout components (Header, Footer, Navigation)
├── pages/               # Page components
│   ├── LandingPage.tsx  # Problem presentation and CTA
│   ├── UploadPage.tsx   # File upload and configuration
│   ├── ProcessingPage.tsx # Real-time processing status
│   ├── ResultsPage.tsx  # Detailed results and analysis
│   ├── ROIPage.tsx      # ROI calculator and projections
│   ├── InsightsPage.tsx # Auto-learning insights
│   └── DashboardPage.tsx # Sample data demonstration
├── services/            # API service layer
│   ├── api-client.ts    # Base HTTP client with error handling
│   ├── batch-service.ts # Batch processing API calls
│   ├── metrics-service.ts # Metrics and ROI API calls
│   └── health-service.ts # System health monitoring
├── types/               # TypeScript type definitions
│   ├── api.ts          # API response types matching backend schemas
│   └── index.ts        # UI-specific types
├── utils/               # Utility functions
│   ├── constants.ts    # Application constants and configuration
│   └── formatters.ts   # Data formatting utilities
└── App.tsx             # Main application component with routing
```

## Key Pages

### 1. Landing Page (`/`)
- **Problem Statement**: Highlights the 97% false positive crisis
- **Value Proposition**: Demonstrates potential time and cost savings
- **Call to Action**: Guides users to start the demo

### 2. Upload Page (`/upload`)
- **File Upload**: CSV data and optional image ZIP files
- **Configuration**: Processing options and custom thresholds
- **Validation**: Real-time file validation and error handling

### 3. Processing Page (`/processing/:batchId`)
- **Live Progress**: Real-time processing status and progress bars
- **Timeline**: Visual representation of processing stages
- **Cancellation**: Ability to cancel running jobs

### 4. Results Page (`/results/:batchId`)
- **Performance Metrics**: Key statistics and achievements
- **Image Gallery**: Filtered vs. review-required images
- **Comparison Charts**: Performance vs. baseline systems
- **Export Options**: Download detailed results

### 5. ROI Calculator (`/roi/:batchId`)
- **Financial Impact**: Cost savings and ROI calculations
- **Customizable Parameters**: Adjust rates and implementation costs
- **5-Year Projections**: Long-term financial benefits
- **Business Case**: Professional presentation materials

### 6. Insights Page (`/insights/:batchId`)
- **Auto-Learning Results**: AI-detected patterns and optimizations
- **Implementation Plan**: Step-by-step deployment guidance
- **Best Practices**: Optimization recommendations
- **Success Metrics**: KPIs to track post-implementation

## API Integration

The frontend integrates with the FastAPI backend through:

### Service Layer
- **Centralized API Client**: Consistent error handling and request/response processing
- **Type-Safe Interfaces**: TypeScript types matching backend Pydantic schemas
- **Automatic Retries**: Intelligent retry logic for failed requests
- **Real-time Updates**: Polling mechanisms for live status updates

### Error Handling
- **Comprehensive Error Types**: Network, validation, and server error handling
- **User-Friendly Messages**: Clear error communication with recovery options
- **Fallback States**: Graceful degradation when services are unavailable

## Customization

### Environment Configuration
```bash
# API Configuration
REACT_APP_API_URL=http://localhost:8000
REACT_APP_API_TIMEOUT=30000

# Demo Defaults
REACT_APP_DEFAULT_CUSTOMER_NAME=Demo Customer
REACT_APP_DEFAULT_MONTHLY_ALERTS=17268
REACT_APP_DEFAULT_HOURLY_RATE=50
REACT_APP_DEFAULT_IMPLEMENTATION_COST=225000
```

### Styling Customization
The application uses Tailwind CSS with a custom design system:
- **Color Palette**: Primary, success, warning, danger color schemes
- **Typography**: Inter font family with consistent sizing
- **Components**: Reusable component classes in `index.css`

### Adding New Features
1. **Components**: Add to appropriate subdirectory in `components/`
2. **Pages**: Create in `pages/` and add to router in `App.tsx`
3. **API Services**: Extend service classes in `services/`
4. **Types**: Define in `types/` for type safety

## Performance Features

### Optimization
- **Code Splitting**: Automatic route-based splitting
- **Image Optimization**: Lazy loading and responsive images
- **Caching**: React Query for intelligent data caching
- **Bundle Optimization**: Tree shaking and minification

### Monitoring
- **Error Tracking**: Comprehensive error logging
- **Performance Metrics**: Request timing and response monitoring
- **Health Checks**: System status monitoring

## Testing

```bash
# Run tests
npm test

# Run tests with coverage
npm test -- --coverage

# Type checking
npm run type-check

# Linting
npm run lint
npm run lint:fix
```

## Deployment

### Production Build
```bash
npm run build
```

### Docker Deployment
```dockerfile
FROM node:18-alpine as build
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=build /app/build /usr/share/nginx/html
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

### Environment Variables for Production
- Set `REACT_APP_API_URL` to your backend URL
- Configure authentication if required
- Set up monitoring and analytics

## Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## Contributing

1. Follow TypeScript best practices
2. Use existing component patterns
3. Add tests for new features
4. Update documentation
5. Follow semantic commit messages

## License

Private - Sensen AI-FARM Project