{"name": "ai-farm-frontend", "version": "1.0.0", "private": true, "dependencies": {"@tanstack/react-query": "^5.81.5", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@types/node": "^18.19.0", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "axios": "^1.3.4", "clsx": "^2.1.1", "date-fns": "^2.30.0", "lucide-react": "^0.294.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-hot-toast": "^2.5.2", "react-query": "^3.39.3", "react-router-dom": "^6.8.1", "react-scripts": "5.0.1", "recharts": "^2.8.0", "tailwind-merge": "^2.2.0", "typescript": "^4.9.5", "web-vitals": "^2.1.4"}, "devDependencies": {"@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "tailwindcss": "^3.4.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:8000"}