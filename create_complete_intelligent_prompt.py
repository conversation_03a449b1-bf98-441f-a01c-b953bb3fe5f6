#!/usr/bin/env python3
"""
Create the COMPLETE intelligent prompt with ALL our learnings:
- Detailed structure descriptions from VLM analysis
- PPE compliance patterns from VLM analysis  
- Clear decision rules
- Safety-first approach
"""

def create_complete_intelligent_prompt():
    """Create the full prompt with all learned patterns"""
    
    prompt = """SAFETY VIOLATION DETECTION WITH LEARNED PATTERNS

ANALYZE BOTH PROVIDED IMAGES (SOURCE and CROPPED).

STEP 1: ENTITY IDENTIFICATION
What is in the CROPPED image?

A) INDUSTRIAL STRUCTURE (requires >90% confidence)
   
   CRANE STRUCTURE characteristics:
   • Large geometric metal frames with angular supports
   • Painted metal beams in yellow/orange/red colors
   • Mechanical components with wheels at base
   • Grid-like patterns of steel beams
   • No organic shapes, purely mechanical
   
   VESSEL STRUCTURE characteristics:
   • Ship railings and deck components
   • Painted metal surfaces (often white/gray)
   • Maritime equipment and fixtures
   • Curved hull structures
   • Port/starboard equipment
   
   PM (PRIME MOVER) STRUCTURE characteristics:
   • Boxy mechanical truck unit
   • Large wheels and industrial chassis
   • Cab structure with windows
   • Container handling equipment
   • Yellow/orange industrial colors
   
   SPREADER STRUCTURE characteristics:
   • Rectangular metal frame for containers
   • Corner guides and twist locks
   • Lifting mechanisms and cables
   • Grid pattern on top surface
   • No human features
   
   OTHER STRUCTURES:
   • Camera poles, cell guides, light fixtures
   • Water reflections, shadows
   • Pure mechanical/industrial features

B) PERSON (any human characteristics)
   • Human body shape/proportions
   • Clothing or PPE visible
   • Arms, legs, head distinguishable
   • Movement or human posture
   • Organic forms vs mechanical

C) UNCLEAR/MIXED
   • Cannot determine with confidence
   • Both person and structure visible
   • Poor image quality

STEP 2: PPE COMPLIANCE ASSESSMENT (if PERSON detected)

PROPER PPE COMPLIANCE looks like:

FULL PPE characteristics:
• Helmet: Hard hat clearly visible (white/orange/yellow/red)
• Vest: High-vis reflective vest (orange/yellow), properly worn and fastened
• Coveralls: Work uniform/coveralls visible
• Equipment: Safety harness if required
• Overall: Professional appearance, all PPE fitted properly

COMPLIANT COMBINATIONS:
✓ White/orange/yellow helmet + Orange/yellow high-vis vest
✓ Hard hat + Reflective vest + Work coveralls
✓ Helmet + Life jacket + Safety harness (vessel work)
✓ Any proper color helmet + Fastened high-vis vest

PPE VIOLATIONS:
✗ No helmet visible
✗ No vest or vest not worn
✗ Vest not properly fastened
✗ Missing required safety equipment
✗ Improper PPE wear

STEP 3: BEHAVIORAL VIOLATION CHECK (even with full PPE)

Check for these violations REGARDLESS of PPE status:
□ Mobile phone/device use
□ Missing required equipment (GO/STOP bat, etc.)
□ In unauthorized/dangerous location
□ Unsafe operations or shortcuts
□ One person doing two-person job
□ Not maintaining safe distances
□ Taking spreader rides
□ Any other unsafe behavior

STEP 4: DECISION RULES

1. STRUCTURE (>90% confidence) + No person visible
   → FALSE POSITIVE: YES

2. PERSON + FULL PPE + NO behavioral violations
   → FALSE POSITIVE: YES

3. PERSON + PPE VIOLATION
   → FALSE POSITIVE: NO

4. PERSON + BEHAVIORAL VIOLATION (regardless of PPE)
   → FALSE POSITIVE: NO

5. UNCLEAR/UNCERTAIN
   → FALSE POSITIVE: NO (safety first)

CRITICAL: Real safety violations must NEVER be missed.

OUTPUT FORMAT:
FALSE POSITIVE: [YES/NO]
Entity: [STRUCTURE/PERSON/UNCLEAR]
If PERSON - PPE Status: [COMPLIANT/NON-COMPLIANT]
Violations: [None OR list specific violations]
Confidence: [High/Medium/Low]"""

    return prompt

def create_test_configuration():
    """Create configuration for testing"""
    
    config = {
        "prompt_name": "complete_intelligent_v1",
        "includes": {
            "structure_descriptions": True,
            "ppe_patterns": True,
            "behavioral_checks": True,
            "safety_defaults": True
        },
        "thresholds": {
            "structure_confidence": 90,
            "person_confidence": "any",
            "default_action": "safety_first"
        },
        "learned_patterns": {
            "structures": [
                "crane_geometric_frames",
                "vessel_deck_components", 
                "pm_boxy_mechanical",
                "spreader_rectangular_frame"
            ],
            "ppe_compliance": [
                "helmet_colors",
                "vest_high_vis",
                "proper_fit_and_wear"
            ],
            "behavioral_violations": [
                "mobile_phone_use",
                "missing_equipment",
                "unsafe_location",
                "improper_operations"
            ]
        },
        "expected_performance": {
            "valid_protection": "100%",
            "fp_detection": "75-85%",
            "structure_detection": "90%+",
            "ppe_recognition": "80%+"
        }
    }
    
    return config

def main():
    # Create complete prompt
    complete_prompt = create_complete_intelligent_prompt()
    
    # Save prompt
    with open('complete_intelligent_prompt.txt', 'w') as f:
        f.write(complete_prompt)
    
    # Create configuration
    config = create_test_configuration()
    
    import json
    with open('complete_prompt_config.json', 'w') as f:
        json.dump(config, f, indent=2)
    
    print("COMPLETE INTELLIGENT PROMPT CREATED")
    print("="*60)
    print("\nThis prompt includes ALL our learnings:")
    print("✓ Detailed structure descriptions from VLM analysis")
    print("✓ PPE compliance patterns from VLM analysis")
    print("✓ Behavioral violation checks")
    print("✓ Clear decision rules")
    print("✓ Safety-first defaults")
    
    print("\nKey improvements over simple prompt:")
    print("1. VLM knows what crane/vessel/PM/spreader structures look like")
    print("2. VLM knows what proper PPE compliance looks like")
    print("3. VLM can distinguish structures from people")
    print("4. VLM can identify PPE compliance vs violations")
    print("5. VLM checks for behavioral violations even with PPE")
    
    print("\nFiles created:")
    print("- complete_intelligent_prompt.txt")
    print("- complete_prompt_config.json")
    
    print("\nReady to test on all 1250 cases!")

if __name__ == "__main__":
    main()