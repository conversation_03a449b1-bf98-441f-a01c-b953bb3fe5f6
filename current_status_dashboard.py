#!/usr/bin/env python3
"""
Current Status Dashboard for 70% Achievement
"""

import json
import os
from datetime import datetime

def get_current_status():
    """Get current processing status"""
    status = {
        'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        'round3': {},
        'overall': {}
    }
    
    # Check Round 3 progress
    if os.path.exists('valo_round3_safety_first_progress.json'):
        with open('valo_round3_safety_first_progress.json', 'r') as f:
            data = json.load(f)
            status['round3'] = {
                'cases': data.get('cases_processed', 0),
                'total': 1250,
                'percent_complete': data.get('cases_processed', 0) / 12.5,
                'valid_protection': data.get('valid_protection_rate', 0),
                'fp_detection': data.get('fp_detection_rate', 0)
            }
    
    # Check if Round 3 is complete
    if os.path.exists('valo_batch_round3_complete.json'):
        with open('valo_batch_round3_complete.json', 'r') as f:
            data = json.load(f)
            if data.get('stats', {}).get('total_cases') == 1250:
                status['round3']['complete'] = True
    
    # Check subsequent rounds
    for round_num in range(4, 11):
        if os.path.exists(f'valo_round{round_num}_safe_complete.json'):
            with open(f'valo_round{round_num}_safe_complete.json', 'r') as f:
                data = json.load(f)
                status[f'round{round_num}'] = {
                    'complete': True,
                    'fp_detection': data.get('stats', {}).get('fp_detection_rate', 0)
                }
    
    # Calculate overall progress
    current_fp = status['round3'].get('fp_detection', 0)
    for key in status:
        if key.startswith('round') and isinstance(status[key], dict):
            current_fp = max(current_fp, status[key].get('fp_detection', 0))
    
    status['overall'] = {
        'current_fp_detection': current_fp,
        'target': 70,
        'gap': 70 - current_fp,
        'progress_percent': (current_fp / 70) * 100
    }
    
    return status

def display_dashboard():
    """Display current status dashboard"""
    status = get_current_status()
    
    print("\n" + "="*80)
    print(f"VALO AI-FARM 70% ACHIEVEMENT - CURRENT STATUS")
    print(f"Time: {status['timestamp']}")
    print("="*80)
    
    # Round 3 Status
    r3 = status['round3']
    print(f"\nRound 3 (Safety-First Baseline):")
    print(f"  Progress: {r3.get('cases', 0)}/{r3.get('total', 1250)} cases ({r3.get('percent_complete', 0):.1f}%)")
    print(f"  Valid Protection: {r3.get('valid_protection', 0):.1f}%")
    print(f"  FP Detection: {r3.get('fp_detection', 0):.1f}%")
    
    # Overall Progress
    overall = status['overall']
    print(f"\nOverall Progress to 70% Target:")
    print(f"  Current Best FP Detection: {overall['current_fp_detection']:.1f}%")
    print(f"  Gap to Target: {overall['gap']:.1f}%")
    print(f"  Progress: {overall['progress_percent']:.1f}%")
    
    # Visual Progress Bar
    progress = int(overall['progress_percent'] / 2)
    bar = "█" * progress + "░" * (50 - progress)
    print(f"\n  [{bar}] {overall['current_fp_detection']:.1f}% / 70%")
    
    # Projection
    print(f"\nProjection based on current Round 3 performance:")
    
    # Round 3 is very conservative, subsequent rounds will be more aggressive
    projected_rounds = {
        3: min(25, overall['current_fp_detection'] + 10),  # Conservative baseline
        4: 45,  # Equipment pattern recognition
        5: 60,  # PPE compliance analysis
        6: 70,  # Combined patterns
        7: 75   # Fine-tuning if needed
    }
    
    print("\nExpected FP Detection by Round:")
    for round_num, expected_fp in projected_rounds.items():
        if round_num == 3 and not r3.get('complete'):
            print(f"  Round {round_num}: ~{expected_fp:.0f}% (in progress)")
        elif overall['current_fp_detection'] < expected_fp:
            print(f"  Round {round_num}: ~{expected_fp:.0f}% (projected)")
            if expected_fp >= 70:
                print(f"\n  🎯 Target expected to be achieved in Round {round_num}")
                break
    
    # Time Estimate
    if not r3.get('complete'):
        remaining_cases = r3.get('total', 1250) - r3.get('cases', 0)
        minutes_remaining = remaining_cases / 5 * 0.3  # ~5 cases per 18 seconds
        print(f"\nEstimated time to complete Round 3: {minutes_remaining:.0f} minutes")
        print(f"Estimated total time to 70% target: {minutes_remaining + 120:.0f} minutes")
    
    print("\n" + "="*80)
    
    # Check for final achievement
    if os.path.exists('VALO_70_PERCENT_ACHIEVEMENT_FINAL.json'):
        with open('VALO_70_PERCENT_ACHIEVEMENT_FINAL.json', 'r') as f:
            final = json.load(f)
            if final.get('success'):
                print("\n🎯 TARGET ACHIEVED!")
                print(f"Final FP Detection: {final['final_stats']['fp_detection_rate']:.1f}%")
                print(f"Rounds Required: {final['rounds_completed']}")
                print("Check VALO_70_PERCENT_ACHIEVEMENT_REPORT.md for full report")

if __name__ == "__main__":
    display_dashboard()