#!/usr/bin/env python3
"""
Create the final balanced prompt that:
1. Protects 100% of valid violations
2. Correctly identifies PPE compliance as false positives
3. Uses learned patterns for both structures and PPE
"""

def create_final_balanced_prompt():
    """Create the ultimate balanced safety prompt"""
    
    prompt = """SAFETY VIOLATION DETECTION - FINAL BALANCED SYSTEM

ANALYZE BOTH IMAGES PROVIDED.

STEP 1: ENTITY DETECTION
What is in the CROPPED image?

A) DEFINITE STRUCTURE (>90% confidence)
   Visual characteristics:
   • Crane: Metal beams, geometric frames, angular supports
   • Vessel: Ship rails, deck structures, painted metal
   • PM Equipment: Boxy mechanical units with wheels
   • Spreader: Container handling frame, corner guides
   • Cell guides, camera structures, water reflections

B) PERSON
   Any human characteristics:
   • Human shape/form
   • Clothing/PPE visible
   • Organic movement

C) UNCLEAR/MIXED

STEP 2: DECISION RULES

FOR STRUCTURE (>90% confidence):
→ OUTPUT: FALSE POSITIVE: YES

FOR PERSON:
→ Check PPE Compliance:

COMPLIANT PPE includes:
• Helmet/hard hat (any color) + High-vis vest (properly worn)
• Coveralls + vest + helmet
• Life jacket + helmet (vessel work)
• Full safety equipment visible and properly worn

IF PPE COMPLIANT:
→ Check for behavioral violations:
  □ Mobile phone use → VALID VIOLATION
  □ Missing required equipment (GO/STOP bat) → VALID VIOLATION
  □ Unsafe location/operations → VALID VIOLATION
  □ Other unsafe behavior → VALID VIOLATION
  
  IF NO behavioral violations → FALSE POSITIVE: YES
  IF ANY behavioral violation → FALSE POSITIVE: NO

IF PPE NON-COMPLIANT:
→ OUTPUT: FALSE POSITIVE: NO

FOR UNCLEAR:
→ OUTPUT: FALSE POSITIVE: NO (safety default)

CRITICAL OUTPUT RULES:
- Person with full PPE and no violations = FALSE POSITIVE: YES
- Person with any violation = FALSE POSITIVE: NO
- Structure only = FALSE POSITIVE: YES
- Uncertain = FALSE POSITIVE: NO

OUTPUT FORMAT:
FALSE POSITIVE: [YES/NO]
Entity: [STRUCTURE/PERSON/UNCLEAR]
PPE Status: [COMPLIANT/NON-COMPLIANT/N/A]
Violations: [None OR list specific violations]"""

    return prompt

def create_test_matrix():
    """Create clear test matrix for validation"""
    
    matrix = """
TEST MATRIX FOR VALIDATION:

Scenario                          | Expected Output
----------------------------------|------------------
Crane/vessel structure only       | FALSE POSITIVE: YES
Person with full PPE, no issues   | FALSE POSITIVE: YES
Person with PPE + mobile phone    | FALSE POSITIVE: NO
Person missing helmet             | FALSE POSITIVE: NO
Person missing GO/STOP bat        | FALSE POSITIVE: NO
Unclear/mixed image              | FALSE POSITIVE: NO
Multiple workers in full PPE      | FALSE POSITIVE: YES
Worker at wharf with full PPE    | FALSE POSITIVE: YES
"""
    return matrix

def main():
    # Create final prompt
    final_prompt = create_final_balanced_prompt()
    test_matrix = create_test_matrix()
    
    # Save final prompt
    with open('final_balanced_safety_prompt.txt', 'w') as f:
        f.write(final_prompt)
    
    # Save test matrix
    with open('prompt_test_matrix.txt', 'w') as f:
        f.write(test_matrix)
    
    # Create configuration
    config = {
        "prompt_version": "final_balanced_v1",
        "structure_threshold": 90,
        "ppe_compliance_rules": {
            "required_items": ["helmet", "vest"],
            "acceptable_colors": {
                "helmet": ["white", "orange", "yellow", "red"],
                "vest": ["orange", "yellow", "green", "high-vis"]
            }
        },
        "behavioral_violations": [
            "mobile_phone_use",
            "missing_equipment",
            "unsafe_location",
            "improper_operations"
        ],
        "decision_priority": [
            "protect_valid_violations",
            "identify_ppe_compliance",
            "detect_structures",
            "reduce_false_positives"
        ],
        "expected_performance": {
            "valid_protection": "100%",
            "fp_detection": "70-80%",
            "overall_accuracy": "85%+"
        }
    }
    
    import json
    with open('final_prompt_config.json', 'w') as f:
        json.dump(config, f, indent=2)
    
    print("FINAL BALANCED PROMPT CREATED")
    print("="*60)
    print("\nKey features:")
    print("1. Clear decision tree with no ambiguity")
    print("2. PPE compliance → FP (unless behavioral violation)")
    print("3. Structure detection → FP")
    print("4. Safety default for unclear cases")
    print("\nExpected performance:")
    print("- 100% valid violation protection")
    print("- 70-80% false positive detection")
    print("- Clear, consistent decisions")
    
    print("\nFiles created:")
    print("- final_balanced_safety_prompt.txt")
    print("- prompt_test_matrix.txt")
    print("- final_prompt_config.json")
    
    print("\n✅ This prompt combines:")
    print("- Structure recognition (from VLM analysis)")
    print("- PPE compliance patterns (from VLM analysis)")
    print("- Behavioral violation detection")
    print("- Safety-first defaults")

if __name__ == "__main__":
    main()