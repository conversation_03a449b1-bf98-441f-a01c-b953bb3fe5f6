#!/usr/bin/env python3
"""
Run just the simple equipment test on all 1250 cases
More robust with better error handling
"""

import json
import base64
import requests
import os
import time
from datetime import datetime
import sys

class SimpleEquipmentTester:
    def __init__(self):
        self.vlm_url = "http://**************:9500/v1/chat/completions"
        self.vlm_model = "VLM-38B-AWQ"
        self.prompt = "Is this image primarily showing industrial equipment (crane, vessel, truck, or spreader) with no people visible? Answer only YES or NO."
        self.results = []
        self.progress_file = 'simple_equipment_test_progress.json'
        
    def encode_image(self, image_path):
        """Encode image to base64"""
        try:
            if os.path.exists(image_path):
                with open(image_path, 'rb') as f:
                    return base64.b64encode(f.read()).decode('utf-8')
        except Exception as e:
            print(f"Error encoding {image_path}: {e}")
        return None
    
    def test_single_case(self, case):
        """Test a single case with retries"""
        source_b64 = self.encode_image(case['source_image'])
        cropped_b64 = self.encode_image(case['cropped_image'])
        
        if not source_b64 or not cropped_b64:
            return None
        
        payload = {
            "model": self.vlm_model,
            "messages": [{
                "role": "user",
                "content": [
                    {"type": "text", "text": self.prompt},
                    {"type": "text", "text": "\n\nSOURCE IMAGE:"},
                    {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{source_b64}"}},
                    {"type": "text", "text": "\n\nCROPPED IMAGE:"},
                    {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{cropped_b64}"}}
                ]
            }],
            "temperature": 0.1,
            "max_tokens": 50
        }
        
        # Try up to 3 times
        for attempt in range(3):
            try:
                response = requests.post(self.vlm_url, json=payload, timeout=30)
                if response.status_code == 200:
                    vlm_response = response.json()['choices'][0]['message']['content']
                    
                    # Parse response
                    is_equipment = "YES" in vlm_response.upper()[:10]
                    predicted_fp = is_equipment
                    actual_fp = case['is_false_positive']
                    
                    return {
                        'case_number': case['case_number'],
                        'actual_fp': actual_fp,
                        'predicted_fp': predicted_fp,
                        'correct': predicted_fp == actual_fp,
                        'response': vlm_response.strip()
                    }
                elif response.status_code == 429:
                    print(f"Rate limit hit, waiting {2**attempt} seconds...")
                    time.sleep(2**attempt)
                else:
                    print(f"API error {response.status_code}")
                    return None
            except requests.exceptions.Timeout:
                print(f"Timeout on attempt {attempt+1}")
                if attempt < 2:
                    time.sleep(2)
            except Exception as e:
                print(f"Error on attempt {attempt+1}: {str(e)}")
                if attempt < 2:
                    time.sleep(2)
        
        return None
    
    def save_progress(self):
        """Save current progress"""
        with open(self.progress_file, 'w') as f:
            json.dump({
                'timestamp': datetime.now().isoformat(),
                'total_cases': len(self.results),
                'correct': sum(r['correct'] for r in self.results),
                'accuracy': sum(r['correct'] for r in self.results) / len(self.results) * 100 if self.results else 0,
                'last_case': self.results[-1]['case_number'] if self.results else None
            }, f, indent=2)
    
    def run_test(self):
        """Run the simple equipment test on all cases"""
        print("="*80)
        print("SIMPLE EQUIPMENT DETECTION TEST - ALL 1250 CASES")
        print("="*80)
        
        # Load test data
        with open('valo_batch_round3_complete.json', 'r') as f:
            all_cases = json.load(f)['results']
        
        print(f"Loaded {len(all_cases)} cases")
        print("Starting test with better error handling...")
        
        start_time = time.time()
        
        for i, case in enumerate(all_cases):
            # Progress update
            if i % 10 == 0:
                elapsed = time.time() - start_time
                if i > 0:
                    rate = i / elapsed
                    eta = (len(all_cases) - i) / rate / 60
                    acc = sum(r['correct'] for r in self.results) / len(self.results) * 100 if self.results else 0
                    print(f"\nProgress: {i}/{len(all_cases)} ({i/len(all_cases)*100:.1f}%) - "
                          f"Accuracy: {acc:.1f}% - ETA: {eta:.1f} min")
                else:
                    print(f"\nProgress: {i}/{len(all_cases)} (0.0%)")
                
                self.save_progress()
            
            # Test case
            result = self.test_single_case(case)
            
            if result:
                self.results.append(result)
                
                # Show first few results
                if i < 5:
                    status = "✓" if result['correct'] else "✗"
                    print(f"{status} {result['case_number']}: {result['response']} "
                          f"(Predicted: {result['predicted_fp']}, Actual: {result['actual_fp']})")
            else:
                print(f"✗ {case['case_number']}: Failed to process")
            
            # Rate limiting
            time.sleep(0.5)
        
        # Final results
        print("\n" + "="*80)
        print("FINAL RESULTS - SIMPLE EQUIPMENT DETECTION")
        print("="*80)
        
        if self.results:
            total = len(self.results)
            correct = sum(r['correct'] for r in self.results)
            
            # Separate by type
            actual_fps = [r for r in self.results if r['actual_fp']]
            actual_valid = [r for r in self.results if not r['actual_fp']]
            
            fp_detected = sum(r['predicted_fp'] for r in actual_fps)
            valid_protected = sum(not r['predicted_fp'] for r in actual_valid)
            
            print(f"Total processed: {total}/{len(all_cases)}")
            print(f"Overall Accuracy: {correct/total*100:.1f}%")
            print(f"FP Detection: {fp_detected}/{len(actual_fps)} ({fp_detected/len(actual_fps)*100:.1f}%)")
            print(f"Valid Protection: {valid_protected}/{len(actual_valid)} ({valid_protected/len(actual_valid)*100:.1f}%)")
            print(f"Time taken: {(time.time() - start_time)/60:.1f} minutes")
            
            # Save final results
            final_results = {
                'test_name': 'Simple Equipment Detection',
                'timestamp': datetime.now().isoformat(),
                'total_cases': len(all_cases),
                'processed_cases': total,
                'accuracy': correct/total*100,
                'fp_detection_rate': fp_detected/len(actual_fps)*100,
                'valid_protection_rate': valid_protected/len(actual_valid)*100,
                'time_minutes': (time.time() - start_time)/60,
                'prompt': self.prompt,
                'sample_results': self.results[:20]
            }
            
            with open('simple_equipment_final_results.json', 'w') as f:
                json.dump(final_results, f, indent=2)
            
            print(f"\nResults saved to: simple_equipment_final_results.json")

if __name__ == "__main__":
    tester = SimpleEquipmentTester()
    tester.run_test()