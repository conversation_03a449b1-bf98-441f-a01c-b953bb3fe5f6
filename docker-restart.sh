#!/bin/bash

echo "🚀 Restarting VALO System with Fixed Ports"
echo "=========================================="

# Stop any existing containers
echo "Stopping existing containers..."
sudo docker-compose down

# Clean up any dangling containers
echo "Cleaning up..."
sudo docker system prune -f

# Start with new configuration
echo "Starting VALO system..."
echo "  - PostgreSQL: localhost:5433"
echo "  - Dashboard: http://localhost:5001"
echo

sudo docker-compose up --build