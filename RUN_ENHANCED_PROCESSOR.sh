#!/bin/bash

echo "=================================="
echo "🚀 VALO ENHANCED PROCESSOR LAUNCHER"
echo "=================================="
echo ""
echo "Choose an option:"
echo "1) Test with 20 cases (No Flask needed)"
echo "2) Run full 1250+ cases (No Flask needed)"
echo "3) Install Flask and run with dashboard"
echo "4) Check service status"
echo ""
read -p "Enter choice (1-4): " choice

case $choice in
    1)
        echo "Running test with 20 cases..."
        python3 enhanced_processor_simple.py test
        ;;
    2)
        echo "⚠️  WARNING: This will process all 1250+ cases and take 8-15 hours!"
        read -p "Are you sure? (yes/no): " confirm
        if [ "$confirm" == "yes" ]; then
            echo "Starting full processing..."
            python3 enhanced_processor_simple.py full
        else
            echo "Cancelled."
        fi
        ;;
    3)
        echo "Installing Flask..."
        pip3 install flask flask-cors
        echo "Running with dashboard..."
        python3 enhanced_valo_processor.py
        ;;
    4)
        echo "Checking services..."
        python3 check_services.py
        ;;
    *)
        echo "Invalid choice. Please run again and select 1-4."
        ;;
esac