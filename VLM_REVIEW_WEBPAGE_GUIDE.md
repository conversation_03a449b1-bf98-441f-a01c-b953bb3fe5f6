# VLM Review Webpage Guide

## Overview

A comprehensive review webpage has been created for reviewing all 1250 VALO AI-FARM cases. The webpage allows you to review VLM outputs, add human ground truth comments, and identify any issues.

## 📄 File Location

```
valo_review_webpage/valo_vlm_review_system.html
```

Simply open this HTML file in any modern web browser.

## 🎯 What's Included for Each Case

### 1. **Images**
- **Source Image**: Full surveillance image
- **Cropped Image**: The specific alert region analyzed by VLM

### 2. **Case Information**
- Case Number (e.g., V1250627132)
- Ground Truth Label (FALSE_POSITIVE or TRUE_POSITIVE)
- Original Remark from CSV
- Processing Time
- Person Present (YES/NO)
- Main Subject (Person/Equipment/Structure)
- PPE Compliance Status
- False Positive Likelihood

### 3. **VLM Query (Full Prompt)**
Shows the exact prompt sent to VLM:
- Main subject identification request
- Person details analysis
- Activity analysis
- Environment context
- Safety assessment
- Token limit: 1200 tokens

### 4. **VLM Description Response**
- Complete VLM description (average ~2500 characters)
- Character count and estimated tokens
- Detailed analysis of the image

### 5. **VLM Confidence Analysis Response**
- Description accuracy percentage
- Person presence confirmation
- PPE status details
- Safety violation assessment
- False positive indicators
- All confidence scores

### 6. **Token Information**
- Request token limit (1200 for description, 600 for confidence)
- Actual response length in characters
- Estimated token usage

### 7. **Human Review Section**
Interactive checkboxes and fields:
- ✓ VLM Analysis Correct
- ✓ Has Issue
- ✓ Description Accurate
- ✓ Confidence Reliable
- Issue Type dropdown:
  - Wrong Subject Identification
  - Missed Person
  - Wrong PPE Assessment
  - Hallucination
  - Token Limit Cutoff
  - Other
- Free-text comment box for detailed observations

## 🚀 How to Use

### Navigation
- **Next/Previous buttons**: Navigate between cases
- **Jump to Case**: Enter case index (1-1250)
- **Search**: Find specific case by case number
- **Keyboard shortcuts**: 
  - `←` Previous case
  - `→` Next case
  - `Ctrl+S` Save review

### Review Process
1. **Examine Images**: Compare source and cropped images
2. **Read VLM Description**: Check if it accurately describes the image
3. **Verify Metrics**: Confirm person presence, PPE status, etc.
4. **Check Token Usage**: See if responses were cut off
5. **Add Review**:
   - Check appropriate boxes
   - Select issue type if any
   - Add detailed comments
6. **Save Review**: Click "Save Review" button

### Progress Tracking
- **Progress Bar**: Shows overall completion
- **Statistics Panel**:
  - Total Reviewed
  - Total Correct
  - Issues Found
  - Progress Percentage

### Export Reviews
- Click **"Export All Reviews"** button
- Downloads JSON file with all reviews
- Includes summary statistics

## 📊 Review Focus Areas

### 1. **Description Accuracy**
- Does VLM correctly identify what's in the image?
- Are person counts accurate?
- Is equipment type correctly identified?

### 2. **PPE Assessment**
- Is helmet/vest presence correctly identified?
- Are PPE compliance judgments accurate?

### 3. **False Positive Detection**
- Does VLM correctly identify structure-only cases?
- Are FP likelihood scores reasonable?

### 4. **Token Limits**
- Are descriptions cut off mid-sentence?
- Is critical information missing due to token limits?

### 5. **Hallucinations**
- Does VLM describe things not in the image?
- Are there invented details?

## 💾 Data Persistence

- Reviews are automatically saved to browser's localStorage
- Progress is maintained between sessions
- Export regularly to backup reviews

## 🎯 Expected Insights

Your reviews will help identify:
1. VLM accuracy patterns
2. Common failure modes
3. Token limit impacts
4. Optimal prompt improvements
5. Cases needing special handling

## 📝 Sample Review Comment

```
"VLM correctly identified the crane but missed the person 
standing near the base. The person is not wearing a helmet, 
which should have been flagged as a violation. The FP 
likelihood of 10% is too low for this case."
```

---

The webpage provides everything needed for comprehensive review of all 1250 cases. Your human ground truth annotations will be invaluable for improving the system!