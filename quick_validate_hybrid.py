#!/usr/bin/env python3
"""
Quick validation of hybrid prompt on critical cases
Focus on ensuring valid violation protection
"""

import json
import base64
import requests
import os

VLM_API_URL = "http://100.106.127.35:9500/v1/chat/completions"
VLM_MODEL = "VLM-38B-AWQ"

def encode_image(image_path):
    """Encode image to base64"""
    try:
        if os.path.exists(image_path):
            with open(image_path, 'rb') as f:
                return base64.b64encode(f.read()).decode('utf-8')
    except:
        pass
    return None

def test_critical_cases():
    """Test on a few critical valid violations"""
    
    print("QUICK VALIDATION: HYBRID PROMPT")
    print("="*60)
    
    # Load hybrid prompt
    with open('hybrid_safety_prompt.txt', 'r') as f:
        prompt = f.read()
    
    # Critical test cases (known valid violations that were missed)
    critical_cases = [
        "V1250627179",  # Missing GO/STOP bat
        "V1250627194",  # Vest not fastened
        "V1250627208",  # Mobile phone use
        "V1250628030",  # No life jacket
        "V1250628101"   # No helmet/vest
    ]
    
    # Load case data
    with open('valo_batch_round3_complete.json', 'r') as f:
        data = json.load(f)
    
    results = []
    session = requests.Session()
    
    for case_num in critical_cases:
        # Find case
        case = next((c for c in data['results'] if c['case_number'] == case_num), None)
        if not case:
            continue
        
        print(f"\nTesting: {case_num}")
        print(f"Type: {case['infringement_type']}")
        print(f"Remark: {case.get('remarks', 'N/A')[:60]}...")
        
        # Encode images
        source_b64 = encode_image(case['source_image'])
        cropped_b64 = encode_image(case['cropped_image'])
        
        if not source_b64 or not cropped_b64:
            continue
        
        # Call VLM
        payload = {
            "model": VLM_MODEL,
            "messages": [{
                "role": "user",
                "content": [
                    {"type": "text", "text": prompt},
                    {"type": "text", "text": "\n\nSOURCE IMAGE:"},
                    {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{source_b64}"}},
                    {"type": "text", "text": "\n\nCROPPED IMAGE:"},
                    {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{cropped_b64}"}}
                ]
            }],
            "temperature": 0.1,
            "max_tokens": 200
        }
        
        try:
            response = session.post(VLM_API_URL, json=payload, timeout=20)
            if response.status_code == 200:
                result = response.json()
                vlm_response = result['choices'][0]['message']['content'].strip()
                
                # Check if marked as FP
                is_fp = 'YES' in vlm_response.upper() if 'FALSE POSITIVE:' in vlm_response.upper() else False
                
                # These are valid violations, should NOT be FP
                protected = not is_fp
                
                print(f"Result: {'✅ PROTECTED' if protected else '❌ MISSED'}")
                if not protected:
                    print(f"Response: {vlm_response[:100]}...")
                
                results.append({
                    'case': case_num,
                    'protected': protected
                })
        except Exception as e:
            print(f"Error: {e}")
    
    # Summary
    print("\n" + "="*60)
    print("SUMMARY")
    print("="*60)
    
    if results:
        protected = sum(1 for r in results if r['protected'])
        rate = protected / len(results) * 100
        
        print(f"Valid violations tested: {len(results)}")
        print(f"Protected: {protected}/{len(results)} ({rate:.0f}%)")
        
        if rate < 100:
            print("\n⚠️ WARNING: Still missing valid violations!")
            print("Need further prompt adjustment")
        else:
            print("\n✅ SUCCESS: All critical violations protected!")
            print("Ready for full dataset test")
    else:
        print("No results obtained")

if __name__ == "__main__":
    test_critical_cases()