#!/usr/bin/env python3
"""
Smart retry strategy for completing 1250 cases
Uses the original VLM endpoint with intelligent retry and batching
"""

import json
import base64
import requests
import os
from datetime import datetime
import time
from pathlib import Path
import threading
import queue

# VLM Configuration
VLM_API_URL = "http://100.106.127.35:9500/v1/chat/completions"
VLM_MODEL = "VLM-38B-AWQ"

# The proven production prompt
ASSUMPTION_BASED_PROMPT = """SAFETY ALERT ANALYSIS
DEFAULT ASSUMPTION: This is a false positive (97% probability)
To override this assumption, you need CLEAR evidence:
- Person clearly visible AND  
- No helmet visible AND
- No safety vest visible

Is this a FALSE POSITIVE? YES/NO"""

class SmartRetryProcessor:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({'Content-Type': 'application/json'})
        self.results_queue = queue.Queue()
        self.failed_cases = []
        
    def encode_image(self, image_path):
        """Encode image to base64"""
        if not os.path.exists(image_path):
            return None
        try:
            with open(image_path, 'rb') as f:
                return base64.b64encode(f.read()).decode('utf-8')
        except Exception as e:
            print(f"\nError encoding {image_path}: {e}")
            return None
    
    def call_vlm_with_backoff(self, image_base64, prompt, max_retries=3):
        """Call VLM with exponential backoff"""
        payload = {
            "model": VLM_MODEL,
            "messages": [{
                "role": "user",
                "content": [
                    {"type": "text", "text": prompt},
                    {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{image_base64}"}}
                ]
            }],
            "temperature": 0.1,
            "max_tokens": 50
        }
        
        for attempt in range(max_retries):
            try:
                # Shorter timeout for faster failure detection
                response = self.session.post(VLM_API_URL, json=payload, timeout=15)
                if response.status_code == 200:
                    result = response.json()
                    return result['choices'][0]['message']['content'].strip()
                else:
                    if attempt < max_retries - 1:
                        time.sleep(2 ** attempt)
            except requests.Timeout:
                if attempt < max_retries - 1:
                    time.sleep(2 ** attempt)
            except Exception as e:
                if attempt < max_retries - 1:
                    time.sleep(2 ** attempt)
        
        return None
    
    def process_single_case(self, case):
        """Process a single case"""
        case_num = case['case_number']
        cropped_path = case['cropped_image']
        is_valid = not case['is_false_positive']  # Valid = NOT false positive
        
        # Encode image
        image_base64 = self.encode_image(cropped_path)
        if not image_base64:
            return {
                'case_number': case_num,
                'status': 'error',
                'error': 'Failed to encode image'
            }
        
        # Call VLM with smart retry
        response = self.call_vlm_with_backoff(image_base64, ASSUMPTION_BASED_PROMPT)
        
        if response:
            is_fp = 'YES' in response.upper()
            correct = (is_fp and not is_valid) or (not is_fp and is_valid)
            
            return {
                'case_number': case_num,
                'status': 'success',
                'response': response,
                'predicted_fp': is_fp,
                'actual_valid': is_valid,
                'correct': correct
            }
        else:
            return {
                'case_number': case_num,
                'status': 'failed',
                'error': 'VLM timeout/error'
            }
    
    def load_previous_results(self):
        """Load and analyze previous results"""
        print("\n1. Analyzing Previous Test Results")
        print("-"*50)
        
        tested_cases = {}
        
        # Load partial 1250 test results
        if os.path.exists('test_all_1250_partial_results.json'):
            with open('test_all_1250_partial_results.json', 'r') as f:
                partial = json.load(f)
                for result in partial['results']:
                    if result['status'] == 'success':
                        tested_cases[result['case_number']] = result
                print(f"Loaded {len(tested_cases)} successful cases from partial test")
        
        # Load any existing complete results
        if os.path.exists('complete_1250_analysis.json'):
            with open('complete_1250_analysis.json', 'r') as f:
                complete = json.load(f)
                if 'results' in complete:
                    for result in complete['results']:
                        if result['status'] == 'success' and result['case_number'] not in tested_cases:
                            tested_cases[result['case_number']] = result
        
        return tested_cases
    
    def smart_retry_strategy(self):
        """Implement smart retry for remaining cases"""
        print("\n" + "="*80)
        print("SMART RETRY STRATEGY FOR 1250 CASES")
        print("="*80)
        
        # Load previous results
        tested_cases = self.load_previous_results()
        print(f"\nTotal successfully tested: {len(tested_cases)}")
        
        # Load full dataset
        with open('valo_batch_round3_complete.json', 'r') as f:
            full_data = json.load(f)
        
        all_cases = full_data['results']
        print(f"Total cases in dataset: {len(all_cases)}")
        
        # Identify remaining cases
        remaining_cases = []
        for case in all_cases:
            if case['case_number'] not in tested_cases:
                remaining_cases.append(case)
        
        print(f"Remaining to test: {len(remaining_cases)}")
        
        if not remaining_cases:
            print("\nAll 1250 cases already tested successfully!")
            self.generate_final_report(tested_cases)
            return
        
        # Smart retry approach
        print(f"\n2. Testing {len(remaining_cases)} Remaining Cases")
        print("-"*50)
        print("Using smart retry with:")
        print("  - 15 second timeout (faster failure detection)")
        print("  - Exponential backoff")
        print("  - Progress saving every 25 cases")
        
        # Process remaining cases
        start_time = time.time()
        new_results = []
        
        for i, case in enumerate(remaining_cases):
            print(f"\rProgress: {i+1}/{len(remaining_cases)} | Case: {case['case_number']}", end='', flush=True)
            
            result = self.process_single_case(case)
            new_results.append(result)
            
            # Save progress every 25 cases
            if (i + 1) % 25 == 0:
                self.save_progress(tested_cases, new_results)
                
                # Check if we should stop (if failure rate too high)
                recent_failures = sum(1 for r in new_results[-25:] if r['status'] == 'failed')
                if recent_failures > 20:
                    print(f"\n\n⚠️  High failure rate detected ({recent_failures}/25). VLM may be overloaded.")
                    print("Stopping to prevent wasted attempts.")
                    break
        
        # Final analysis
        print(f"\n\n3. Smart Retry Results")
        print("-"*50)
        
        successful_new = sum(1 for r in new_results if r['status'] == 'success')
        failed_new = sum(1 for r in new_results if r['status'] == 'failed')
        
        print(f"New successful tests: {successful_new}")
        print(f"Failed attempts: {failed_new}")
        print(f"Total successful: {len(tested_cases) + successful_new}/1250")
        
        # Combine all results
        all_tested = tested_cases.copy()
        for result in new_results:
            if result['status'] == 'success':
                all_tested[result['case_number']] = result
        
        # Generate comprehensive report
        self.generate_final_report(all_tested)
        
        elapsed = time.time() - start_time
        print(f"\nProcessing time: {elapsed:.1f} seconds")
        print(f"Average per case: {elapsed/len(new_results):.2f} seconds")
    
    def save_progress(self, tested_cases, new_results):
        """Save intermediate progress"""
        progress = {
            'timestamp': datetime.now().isoformat(),
            'previously_tested': len(tested_cases),
            'new_attempts': len(new_results),
            'new_successes': sum(1 for r in new_results if r['status'] == 'success'),
            'new_results': new_results
        }
        with open('smart_retry_progress.json', 'w') as f:
            json.dump(progress, f, indent=2)
    
    def generate_final_report(self, all_tested):
        """Generate comprehensive final report"""
        print("\n\n4. FINAL COMPREHENSIVE REPORT")
        print("="*80)
        
        total_tested = len(all_tested)
        coverage = (total_tested / 1250) * 100
        
        print(f"\nDataset Coverage:")
        print(f"  - Total cases: 1,250")
        print(f"  - Successfully tested: {total_tested}")
        print(f"  - Coverage: {coverage:.1f}%")
        
        # Calculate performance
        correct = sum(1 for r in all_tested.values() if r.get('correct', False))
        fp_cases = [r for r in all_tested.values() if r.get('actual_valid', False) == False]  # actual_valid=False means it's a FP
        fp_detected = sum(1 for r in fp_cases if r.get('predicted_fp', False))
        valid_cases = [r for r in all_tested.values() if r.get('actual_valid', False) == True]  # actual_valid=True means it's valid
        valid_protected = sum(1 for r in valid_cases if not r.get('predicted_fp', True))
        
        accuracy = (correct / total_tested) * 100 if total_tested > 0 else 0
        fp_rate = (fp_detected / len(fp_cases)) * 100 if fp_cases else 0
        valid_rate = (valid_protected / len(valid_cases)) * 100 if valid_cases else 100
        
        print(f"\nPerformance Metrics:")
        print(f"  - Overall Accuracy: {accuracy:.1f}%")
        print(f"  - FP Detection Rate: {fp_rate:.1f}%")
        print(f"  - Valid Protection Rate: {valid_rate:.1f}%")
        
        # Statistical confidence
        import math
        if total_tested > 0:
            p = fp_rate / 100
            n = total_tested
            se = math.sqrt((p * (1-p)) / n)
            ci_95 = 1.96 * se * 100
            
            print(f"\nStatistical Confidence:")
            print(f"  - 95% CI: ±{ci_95:.1f}%")
            print(f"  - Expected range: {fp_rate-ci_95:.1f}% to {fp_rate+ci_95:.1f}%")
        
        # Production estimates
        print(f"\nProduction Performance Estimates:")
        print(f"  - Optimistic (15% degradation): {fp_rate * 0.85:.1f}%")
        print(f"  - Realistic (20% degradation): {fp_rate * 0.80:.1f}%")
        print(f"  - Conservative (25% degradation): {fp_rate * 0.75:.1f}%")
        
        # Save final report
        final_report = {
            'report_date': datetime.now().isoformat(),
            'dataset_coverage': {
                'total_cases': 1250,
                'tested_cases': total_tested,
                'coverage_percent': coverage
            },
            'performance': {
                'accuracy': accuracy,
                'fp_detection_rate': fp_rate,
                'valid_protection_rate': valid_rate,
                'confidence_interval': ci_95 if total_tested > 0 else None
            },
            'production_estimates': {
                'optimistic': fp_rate * 0.85,
                'realistic': fp_rate * 0.80,
                'conservative': fp_rate * 0.75
            },
            'recommendation': 'assumption_based approach ready for production' if fp_rate > 75 else 'Further optimization needed'
        }
        
        with open('FINAL_1250_SMART_RETRY_REPORT.json', 'w') as f:
            json.dump(final_report, f, indent=2)
        
        print(f"\n✓ Report saved to: FINAL_1250_SMART_RETRY_REPORT.json")
        print(f"\n{'='*80}")
        print(f"CONCLUSION: Tested {total_tested}/1250 cases ({coverage:.1f}%) with {fp_rate:.1f}% FP detection")
        print(f"{'='*80}")

if __name__ == "__main__":
    processor = SmartRetryProcessor()
    processor.smart_retry_strategy()