#!/usr/bin/env python3
"""
Round 8: Multi-Factor Decision Making
Combines multiple signals for better accuracy
"""
import asyncio
import json
import logging
import aiohttp
from datetime import datetime

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def analyze_case_round8(session, case, vlm_endpoint):
    """Multi-factor analysis"""
    try:
        # Extract factors
        terminal = case.get('terminal', '')
        camera = case.get('camera_id', '')
        remarks = case.get('remarks', '').upper()
        
        # Multi-factor prompt
        prompt = f"""ROUND 8: MULTI-FACTOR ANALYSIS

Consider ALL factors:
1. Terminal: {terminal}
2. Camera: {camera}
3. Remarks: {remarks}
4. Image content analysis

DECISION FACTORS:
- PPE compliance (Full PPE = NOT a violation)
- Equipment-only scenes = False positive
- Multiple corroborating signals increase confidence

Is this a FALSE POSITIVE that should be auto-dismissed?
Answer: YES/NO with reasoning"""

        payload = {
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": prompt},
                        {"type": "image_url", "image_url": {"url": f"file://{case['cropped_image']}"}}
                    ]
                }
            ],
            "temperature": 0.1,
            "max_tokens": 200
        }
        
        async with session.post(vlm_endpoint, json=payload) as response:
            if response.status == 200:
                result = await response.json()
                content = result['choices'][0]['message']['content']
                
                decision = "YES" in content.upper()[:50]
                
                return {
                    'case_number': case['case_number'],
                    'is_false_positive': case['is_false_positive'],
                    'vlm_decision': decision,
                    'confidence': 85 if decision else 95,
                    'reasoning': content
                }
    except Exception as e:
        logger.error(f"Error: {e}")
        return None

async def main():
    vlm_endpoint = "http://100.106.127.35:9500/v1/chat/completions"
    
    # Load cases
    with open('valo_batch_round3_complete.json', 'r') as f:
        all_cases = json.load(f)['results']
    
    logger.info("="*80)
    logger.info("ROUND 8: MULTI-FACTOR DECISION MAKING")
    logger.info("="*80)
    
    async with aiohttp.ClientSession() as session:
        results = []
        for i in range(0, len(all_cases), 20):
            batch = all_cases[i:i+20]
            tasks = [analyze_case_round8(session, case, vlm_endpoint) for case in batch]
            batch_results = await asyncio.gather(*tasks)
            results.extend([r for r in batch_results if r])
            
            if len(results) % 100 == 0:
                logger.info(f"Progress: {len(results)}/{len(all_cases)}")
        
        # Calculate stats
        tp = sum(1 for r in results if r['is_false_positive'] and r['vlm_decision'])
        tn = sum(1 for r in results if not r['is_false_positive'] and not r['vlm_decision'])
        fp_total = sum(1 for r in results if r['is_false_positive'])
        valid_total = sum(1 for r in results if not r['is_false_positive'])
        
        fp_rate = (tp / fp_total * 100) if fp_total > 0 else 0
        valid_rate = (tn / valid_total * 100) if valid_total > 0 else 100
        
        logger.info(f"Round 8 Complete: {fp_rate:.1f}% FP detection")
        
        # Save results
        output = {
            'stats': {
                'round': 8,
                'fp_detection_rate': fp_rate,
                'valid_protection_rate': valid_rate,
                'total_cases': len(results)
            },
            'results': results
        }
        
        with open('valo_round8_multifactor_complete.json', 'w') as f:
            json.dump(output, f, indent=2)

if __name__ == "__main__":
    asyncio.run(main())
