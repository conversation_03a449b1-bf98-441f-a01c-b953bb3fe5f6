#!/usr/bin/env python3
"""
Live monitor for comprehensive test progress
"""

import json
import os
import time
from datetime import datetime

def monitor():
    """Monitor test progress"""
    
    print("COMPREHENSIVE TEST MONITOR")
    print("="*60)
    print("Press Ctrl+C to stop monitoring (test will continue running)")
    print("="*60)
    
    last_update = {}
    
    while True:
        try:
            # Check if progress file exists
            if os.path.exists('multi_approach_progress.json'):
                with open('multi_approach_progress.json', 'r') as f:
                    current = json.load(f)
                
                results = current.get('results', {})
                
                # Clear screen and show update
                os.system('clear')
                print(f"COMPREHENSIVE TEST PROGRESS - {datetime.now().strftime('%H:%M:%S')}")
                print("="*60)
                
                # Show completed approaches
                if results:
                    print("\n✓ COMPLETED APPROACHES:")
                    for approach_key, result in results.items():
                        print(f"\n{result['name']}:")
                        metrics = result['metrics']
                        print(f"  Accuracy: {metrics['accuracy']:.1f}%")
                        print(f"  FP Detection: {metrics['fp_detection']:.1f}% ({metrics['fp_detected']}/{metrics['fp_total']})")
                        print(f"  Valid Protection: {metrics['valid_protection']:.1f}% ({metrics['valid_protected']}/{metrics['valid_total']})")
                        print(f"  Max Tokens: {result['config']['max_tokens']}")
                
                # Show what's running
                approaches_to_test = ['simple_equipment', 'simple_two_step', 'complex_original_fixed', 'medium_structured']
                remaining = [a for a in approaches_to_test if a not in results]
                
                if remaining:
                    print(f"\n⏳ CURRENTLY TESTING: {remaining[0]}")
                    print(f"   Remaining: {len(remaining)-1} approaches")
                else:
                    print("\n✅ ALL APPROACHES COMPLETE!")
                    
                    # Check for final report
                    if os.path.exists('multi_approach_comparison_report.json'):
                        print("\n📄 Final report available: multi_approach_comparison_report.json")
                        
                        with open('multi_approach_comparison_report.json', 'r') as f:
                            report = json.load(f)
                        
                        print(f"\n🏆 BEST APPROACH: {report['best_approach']}")
                        print("\nKEY CONCLUSIONS:")
                        for conclusion in report['conclusions']:
                            print(f"  • {conclusion}")
                        
                        break
                
                # Estimate time remaining
                if len(results) > 0:
                    avg_time_per_approach = 45  # minutes (estimated)
                    eta_minutes = len(remaining) * avg_time_per_approach
                    print(f"\n⏰ Estimated time remaining: {eta_minutes} minutes")
            
            else:
                print("Waiting for test to start...")
            
            time.sleep(10)  # Update every 10 seconds
            
        except KeyboardInterrupt:
            print("\n\nMonitoring stopped. Test continues in background.")
            print("Run this script again to resume monitoring.")
            break
        except Exception as e:
            print(f"Error: {e}")
            time.sleep(5)

if __name__ == "__main__":
    monitor()