#!/usr/bin/env python3
"""
Test Round 3 Production Configuration on ALL 1250 Cases
This will show the REAL results, not projections
"""

import json
import base64
import requests
import os
from datetime import datetime
import time
import sys

class Round3ProductionTest:
    def __init__(self):
        self.vlm_url = "http://100.106.127.35:9500/v1/chat/completions"
        self.vlm_model = "VLM-38B-AWQ"
        self.session = requests.Session()
        
        # Round 3 Production Configuration (from auto-learning)
        self.optimal_thresholds = {
            'structure': 91,
            'person': 50,
            'ppe_compliant': 75,
            'behavioral': 55
        }
        
        # Progress tracking
        self.progress_file = 'round3_production_real_progress.json'
        self.results_file = 'round3_production_real_results.json'
        self.progress = self.load_progress()
        
    def load_progress(self):
        """Load previous progress if exists"""
        if os.path.exists(self.progress_file):
            print(f"Loading previous progress from {self.progress_file}")
            with open(self.progress_file, 'r') as f:
                return json.load(f)
        return {
            'completed_cases': [],
            'results': [],
            'last_index': 0,
            'start_time': datetime.now().isoformat()
        }
    
    def save_progress(self):
        """Save current progress"""
        with open(self.progress_file, 'w') as f:
            json.dump(self.progress, f, indent=2)
    
    def load_production_prompt(self):
        """Load the final production prompt with optimal thresholds"""
        with open('FINAL_PRODUCTION_PROMPT.txt', 'r') as f:
            base_prompt = f.read()
        
        # Add threshold information
        threshold_section = f"""

ROUND 3 PRODUCTION CONFIGURATION:
Current Confidence Thresholds (Optimized through auto-learning):
- Structure identification: >{self.optimal_thresholds['structure']}% confidence required
- Person detection: >{self.optimal_thresholds['person']}% confidence threshold
- PPE compliance assessment: >{self.optimal_thresholds['ppe_compliant']}% confidence
- Behavioral violation detection: >{self.optimal_thresholds['behavioral']}% confidence

Apply these specific thresholds when making decisions."""
        
        # Insert threshold info
        prompt = base_prompt.replace(
            "STEP 4: MAKE SAFETY DECISION",
            threshold_section + "\n\nSTEP 4: MAKE SAFETY DECISION"
        )
        
        # Update the structure threshold in the prompt
        prompt = prompt.replace(
            "A) INDUSTRIAL STRUCTURE (need >90% confidence)",
            f"A) INDUSTRIAL STRUCTURE (need >{self.optimal_thresholds['structure']}% confidence)"
        )
        
        return prompt
    
    def encode_image(self, image_path):
        """Encode image to base64"""
        try:
            if os.path.exists(image_path):
                with open(image_path, 'rb') as f:
                    return base64.b64encode(f.read()).decode('utf-8')
        except:
            pass
        return None
    
    def test_single_case(self, case, prompt):
        """Test a single case with robust retry logic"""
        
        # Skip if already tested
        if case['case_number'] in self.progress['completed_cases']:
            # Find and return existing result
            for r in self.progress['results']:
                if r['case_number'] == case['case_number']:
                    return r
            return None
        
        # Encode images
        source_b64 = self.encode_image(case['source_image'])
        cropped_b64 = self.encode_image(case['cropped_image'])
        
        if not source_b64 or not cropped_b64:
            return None
        
        payload = {
            "model": self.vlm_model,
            "messages": [{
                "role": "user",
                "content": [
                    {"type": "text", "text": prompt},
                    {"type": "text", "text": "\n\nSOURCE IMAGE:"},
                    {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{source_b64}"}},
                    {"type": "text", "text": "\n\nCROPPED IMAGE:"},
                    {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{cropped_b64}"}}
                ]
            }],
            "temperature": 0.1,
            "max_tokens": 300
        }
        
        # Retry logic with exponential backoff
        max_retries = 3
        for attempt in range(max_retries):
            try:
                timeout = 20 + (attempt * 10)
                response = self.session.post(self.vlm_url, json=payload, timeout=timeout)
                
                if response.status_code == 200:
                    vlm_response = response.json()['choices'][0]['message']['content']
                    
                    # Parse response
                    predicted_fp = 'YES' in vlm_response.split('FALSE POSITIVE:')[1][:5]
                    
                    # Extract entity and violations
                    entity = "UNKNOWN"
                    violations = "None"
                    
                    lines = vlm_response.split('\n')
                    for line in lines:
                        if 'Entity:' in line:
                            entity = line.split('Entity:')[1].strip()
                        elif 'Violations:' in line:
                            violations = line.split('Violations:')[1].strip()
                    
                    result = {
                        'case_number': case['case_number'],
                        'actual_fp': case['is_false_positive'],
                        'predicted_fp': predicted_fp,
                        'correct': predicted_fp == case['is_false_positive'],
                        'entity': entity,
                        'violations': violations,
                        'infringement_type': case.get('infringement_type', 'Unknown'),
                        'response_snippet': vlm_response[:150]
                    }
                    
                    # Mark as completed
                    self.progress['completed_cases'].append(case['case_number'])
                    self.progress['results'].append(result)
                    
                    return result
                    
            except requests.exceptions.Timeout:
                if attempt < max_retries - 1:
                    print(f"  Timeout on attempt {attempt + 1}, retrying...")
                    time.sleep(2 * (attempt + 1))
            except Exception as e:
                if attempt < max_retries - 1:
                    print(f"  Error on attempt {attempt + 1}: {str(e)[:50]}")
                    time.sleep(2 * (attempt + 1))
        
        return None
    
    def calculate_metrics(self, results):
        """Calculate comprehensive metrics"""
        if not results:
            return {}
        
        # Overall metrics
        total = len(results)
        correct = sum(r['correct'] for r in results)
        
        # Separate by actual type
        actual_fps = [r for r in results if r['actual_fp']]
        actual_valid = [r for r in results if not r['actual_fp']]
        
        # FP metrics
        fp_detected = sum(r['predicted_fp'] for r in actual_fps) if actual_fps else 0
        fp_total = len(actual_fps)
        
        # Valid metrics
        valid_protected = sum(not r['predicted_fp'] for r in actual_valid) if actual_valid else 0
        valid_missed = len(actual_valid) - valid_protected if actual_valid else 0
        valid_total = len(actual_valid)
        
        # By violation type
        type_stats = {}
        for r in results:
            vtype = r.get('infringement_type', 'Unknown')
            if vtype not in type_stats:
                type_stats[vtype] = {'total': 0, 'correct': 0, 'fp_detected': 0, 'valid_protected': 0}
            
            type_stats[vtype]['total'] += 1
            if r['correct']:
                type_stats[vtype]['correct'] += 1
            if r['actual_fp'] and r['predicted_fp']:
                type_stats[vtype]['fp_detected'] += 1
            if not r['actual_fp'] and not r['predicted_fp']:
                type_stats[vtype]['valid_protected'] += 1
        
        return {
            'total_cases': total,
            'overall_accuracy': (correct / total * 100) if total > 0 else 0,
            'fp_detection_rate': (fp_detected / fp_total * 100) if fp_total > 0 else 0,
            'fp_detected': fp_detected,
            'fp_total': fp_total,
            'valid_protection_rate': (valid_protected / valid_total * 100) if valid_total > 0 else 0,
            'valid_protected': valid_protected,
            'valid_total': valid_total,
            'valid_missed': valid_missed,
            'by_type': type_stats
        }
    
    def run_full_test(self):
        """Run the complete test on all 1250 cases"""
        
        print("="*80)
        print("ROUND 3 PRODUCTION TEST - REAL RESULTS ON ALL 1250 CASES")
        print("="*80)
        print(f"\nUsing optimal thresholds from auto-learning:")
        for key, value in self.optimal_thresholds.items():
            print(f"  {key}: {value}%")
        
        # Load test data
        print("\nLoading test data...")
        with open('valo_batch_round3_complete.json', 'r') as f:
            all_cases = json.load(f)['results']
        
        total_cases = len(all_cases)
        print(f"Total cases to test: {total_cases}")
        
        # Load production prompt
        prompt = self.load_production_prompt()
        print("\nUsing FINAL_PRODUCTION_PROMPT.txt with optimal thresholds")
        
        # Resume from last position
        start_index = self.progress['last_index']
        if start_index > 0:
            print(f"\nResuming from case {start_index + 1}")
        
        print("\nStarting test... (Press Ctrl+C to pause)")
        print("-" * 80)
        
        start_time = time.time()
        
        try:
            for i in range(start_index, total_cases):
                case = all_cases[i]
                
                # Update progress
                self.progress['last_index'] = i
                
                # Progress display
                if i % 10 == 0:
                    elapsed = time.time() - start_time
                    rate = (i - start_index) / elapsed if elapsed > 0 else 0
                    eta = (total_cases - i) / rate / 60 if rate > 0 else 0
                    
                    print(f"\nProgress: {i}/{total_cases} ({i/total_cases*100:.1f}%)")
                    print(f"Rate: {rate:.1f} cases/sec, ETA: {eta:.1f} minutes")
                    
                    # Save progress every 10 cases
                    self.save_progress()
                    
                    # Show interim metrics
                    if len(self.progress['results']) > 50:
                        metrics = self.calculate_metrics(self.progress['results'])
                        print(f"Current metrics: Accuracy={metrics['overall_accuracy']:.1f}%, "
                              f"FP={metrics['fp_detection_rate']:.1f}%, "
                              f"Valid={metrics['valid_protection_rate']:.1f}%")
                
                # Test case
                print(f"  Testing {case['case_number']}...", end='', flush=True)
                result = self.test_single_case(case, prompt)
                
                if result:
                    if result['correct']:
                        if result['actual_fp'] and result['predicted_fp']:
                            print(" [✓ FP]")
                        elif not result['actual_fp'] and not result['predicted_fp']:
                            print(" [✓ Valid]")
                        else:
                            print(" [✓]")
                    else:
                        if not result['actual_fp'] and result['predicted_fp']:
                            print(" [✗ VALID MISSED!]")
                        else:
                            print(" [✗ FP not caught]")
                else:
                    print(" [SKIP]")
                
                # Rate limit
                time.sleep(0.5)
                
        except KeyboardInterrupt:
            print("\n\nTest paused. Run again to resume.")
            self.save_progress()
            return
        
        # Final save
        self.save_progress()
        
        # Generate final report
        self.generate_final_report()
    
    def generate_final_report(self):
        """Generate comprehensive final report"""
        
        print("\n" + "="*80)
        print("FINAL REPORT - ROUND 3 PRODUCTION REAL RESULTS")
        print("="*80)
        
        metrics = self.calculate_metrics(self.progress['results'])
        
        print(f"\nTested: {metrics['total_cases']} cases")
        print(f"\n📊 OVERALL METRICS:")
        print(f"├─ Overall Accuracy: {metrics['overall_accuracy']:.2f}%")
        print(f"├─ FP Detection Rate: {metrics['fp_detection_rate']:.2f}% ({metrics['fp_detected']}/{metrics['fp_total']})")
        print(f"├─ Valid Protection Rate: {metrics['valid_protection_rate']:.2f}% ({metrics['valid_protected']}/{metrics['valid_total']})")
        print(f"└─ Valid Violations Missed: {metrics['valid_missed']}")
        
        print(f"\n📈 PERFORMANCE BY VIOLATION TYPE:")
        for vtype, stats in metrics['by_type'].items():
            accuracy = (stats['correct'] / stats['total'] * 100) if stats['total'] > 0 else 0
            print(f"\n{vtype} ({stats['total']} cases):")
            print(f"  Accuracy: {accuracy:.1f}%")
        
        print(f"\n🎯 TARGET ACHIEVEMENT:")
        print(f"├─ FP Reduction Target: 70% → Achieved: {metrics['fp_detection_rate']:.1f}% {'✅' if metrics['fp_detection_rate'] >= 70 else '❌'}")
        print(f"└─ Valid Protection Target: 98% → Achieved: {metrics['valid_protection_rate']:.1f}% {'✅' if metrics['valid_protection_rate'] >= 98 else '❌'}")
        
        # Save final report
        final_report = {
            'test_date': datetime.now().isoformat(),
            'configuration': 'Round 3 Production with Optimal Thresholds',
            'thresholds': self.optimal_thresholds,
            'total_cases_tested': metrics['total_cases'],
            'metrics': metrics,
            'targets_met': {
                'fp_reduction_70': metrics['fp_detection_rate'] >= 70,
                'valid_protection_98': metrics['valid_protection_rate'] >= 98
            },
            'conclusion': 'REAL results from testing ALL 1250 cases with Round 3 production configuration'
        }
        
        with open(self.results_file, 'w') as f:
            json.dump(final_report, f, indent=2)
        
        print(f"\n📄 Full report saved to: {self.results_file}")
        print("="*80)

def main():
    tester = Round3ProductionTest()
    tester.run_full_test()

if __name__ == "__main__":
    main()