#!/usr/bin/env python3
"""
Analyze VLM predictions vs CSV ground truth labels
Only show cases where they disagree
"""

import pandas as pd
import json
import re
from pathlib import Path
from datetime import datetime

class VLMGroundTruthAnalyzer:
    def __init__(self):
        self.csv_file = 'ai_farm_images_fixed_250703/psa_valo_violation_report_without_images_csv_2025-07-03_14-14-01.CSV'
        self.disagreements = []
        self.agreements = []
        self.analysis_results = {
            'total_cases': 0,
            'agreements': 0,
            'disagreements': 0,
            'vlm_accuracy': 0,
            'disagreement_types': {
                'vlm_fp_human_tp': [],  # VLM says FP, Human says TP (Valid)
                'vlm_tp_human_fp': []   # VLM says TP, Human says FP (Invalid)
            }
        }
        
    def load_ground_truth(self):
        """Load ground truth from CSV"""
        df = pd.read_csv(self.csv_file)
        
        # Create ground truth mapping
        # 'Invalid' in Alert Status column = FALSE_POSITIVE
        # everything else = TRUE_POSITIVE (Valid case)
        ground_truth = {}
        
        for _, row in df.iterrows():
            case_number = row['Case Int. ID']
            status = str(row['Alert Status']).strip() if pd.notna(row['Alert Status']) else ''
            
            if status.lower() == 'invalid':
                ground_truth[case_number] = 'FALSE_POSITIVE'
            else:
                ground_truth[case_number] = 'TRUE_POSITIVE'
                
        print(f"Loaded {len(ground_truth)} cases from CSV")
        
        # Count distribution
        fp_count = sum(1 for v in ground_truth.values() if v == 'FALSE_POSITIVE')
        tp_count = sum(1 for v in ground_truth.values() if v == 'TRUE_POSITIVE')
        print(f"Ground Truth: {fp_count} FALSE_POSITIVES, {tp_count} TRUE_POSITIVES")
        
        return ground_truth
    
    def extract_vlm_prediction(self, confidence_response):
        """Extract VLM's prediction from confidence response"""
        if not confidence_response:
            return 'UNCERTAIN', {}
            
        content_lower = confidence_response.lower()
        
        # Extract key metrics
        metrics = {
            'fp_likelihood': None,
            'person_present': None,
            'ppe_compliance': None,
            'violation_confidence': None
        }
        
        # Extract FP likelihood - this is the primary indicator
        fp_patterns = [
            r'false positive likelihood[:\s]+(\d+)%',
            r'likelihood of false positive[:\s]+(\d+)%',
            r'false positive[:\s]+(\d+)%',
            r'fp likelihood[:\s]+(\d+)%'
        ]
        
        for pattern in fp_patterns:
            match = re.search(pattern, content_lower)
            if match:
                metrics['fp_likelihood'] = int(match.group(1))
                break
                
        # Extract person presence
        if 'person present: yes' in content_lower or 'person present: true' in content_lower:
            metrics['person_present'] = True
        elif 'person present: no' in content_lower or 'person present: false' in content_lower:
            metrics['person_present'] = False
            
        # Extract PPE compliance
        if 'ppe compliance: complete' in content_lower:
            metrics['ppe_compliance'] = 'COMPLETE'
        elif 'ppe compliance: incomplete' in content_lower:
            metrics['ppe_compliance'] = 'INCOMPLETE'  
        elif 'ppe compliance: none' in content_lower or 'ppe compliance: no ppe' in content_lower:
            metrics['ppe_compliance'] = 'NONE'
            
        # Extract violation confidence
        viol_match = re.search(r'violation confidence[:\s]+(\d+)%', content_lower)
        if viol_match:
            metrics['violation_confidence'] = int(viol_match.group(1))
            
        # Determine VLM prediction based on extracted metrics
        # Primary decision: FP likelihood
        if metrics['fp_likelihood'] is not None:
            if metrics['fp_likelihood'] > 50:
                return 'FALSE_POSITIVE', metrics
            else:
                return 'TRUE_POSITIVE', metrics
                
        # Secondary: If no person, likely FP
        if metrics['person_present'] == False:
            return 'FALSE_POSITIVE', metrics
            
        # Tertiary: PPE compliance (complete PPE = likely FP)
        if metrics['person_present'] == True:
            if metrics['ppe_compliance'] == 'COMPLETE':
                return 'FALSE_POSITIVE', metrics
            elif metrics['ppe_compliance'] in ['INCOMPLETE', 'NONE']:
                return 'TRUE_POSITIVE', metrics
                
        return 'UNCERTAIN', metrics
    
    def parse_markdown_cases(self, file_path):
        """Parse markdown file to extract VLM responses by case"""
        cases = {}
        
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # Split by case sections
        case_sections = re.split(r'\n## Case Number: ', content)
        
        for section in case_sections[1:]:  # Skip header
            lines = section.split('\n')
            case_number = lines[0].strip()
            
            # Extract confidence analysis section
            conf_start = section.find('### Confidence Analysis:')
            if conf_start == -1:
                continue
                
            conf_end = section.find('---', conf_start)
            if conf_end == -1:
                conf_end = len(section)
                
            confidence_response = section[conf_start:conf_end].replace('### Confidence Analysis:', '').strip()
            
            # Also extract description for context
            desc_start = section.find('### Description:')
            desc_end = section.find('### Confidence Analysis:')
            
            description = ''
            if desc_start != -1 and desc_end != -1:
                description = section[desc_start:desc_end].replace('### Description:', '').strip()
                
            cases[case_number] = {
                'confidence_response': confidence_response,
                'description': description[:500] + '...' if len(description) > 500 else description
            }
            
        return cases
    
    def analyze_all_cases(self):
        """Compare VLM predictions with CSV ground truth"""
        # Load ground truth from CSV
        ground_truth = self.load_ground_truth()
        
        # Load VLM responses
        all_vlm_cases = {}
        
        # Load FALSE POSITIVE cases from VLM
        fp_file = Path('valo_comprehensive_data/false_positives/false_positive_analysis_20250725_232934.md')
        if fp_file.exists():
            fp_cases = self.parse_markdown_cases(fp_file)
            all_vlm_cases.update(fp_cases)
            
        # Load TRUE POSITIVE cases from VLM  
        tp_file = Path('valo_comprehensive_data/true_positives/true_positive_analysis_20250725_232934.md')
        if tp_file.exists():
            tp_cases = self.parse_markdown_cases(tp_file)
            all_vlm_cases.update(tp_cases)
            
        print(f"\nLoaded {len(all_vlm_cases)} VLM responses")
        
        # Analyze each case
        for case_number, csv_label in ground_truth.items():
            if case_number not in all_vlm_cases:
                print(f"Warning: No VLM response found for {case_number}")
                continue
                
            vlm_data = all_vlm_cases[case_number]
            vlm_prediction, metrics = self.extract_vlm_prediction(vlm_data['confidence_response'])
            
            # Skip uncertain cases
            if vlm_prediction == 'UNCERTAIN':
                continue
                
            self.analysis_results['total_cases'] += 1
            
            # Check agreement
            agrees = (vlm_prediction == csv_label)
            
            case_result = {
                'case_number': case_number,
                'csv_ground_truth': csv_label,
                'vlm_prediction': vlm_prediction,
                'agrees': agrees,
                'metrics': metrics,
                'description_preview': vlm_data['description'],
                'confidence_preview': vlm_data['confidence_response'][:300] + '...'
            }
            
            if agrees:
                self.agreements.append(case_result)
                self.analysis_results['agreements'] += 1
            else:
                self.disagreements.append(case_result)
                self.analysis_results['disagreements'] += 1
                
                # Track disagreement type
                if vlm_prediction == 'FALSE_POSITIVE' and csv_label == 'TRUE_POSITIVE':
                    self.analysis_results['disagreement_types']['vlm_fp_human_tp'].append(case_number)
                elif vlm_prediction == 'TRUE_POSITIVE' and csv_label == 'FALSE_POSITIVE':
                    self.analysis_results['disagreement_types']['vlm_tp_human_fp'].append(case_number)
                    
        # Calculate accuracy
        if self.analysis_results['total_cases'] > 0:
            self.analysis_results['vlm_accuracy'] = (
                self.analysis_results['agreements'] / self.analysis_results['total_cases'] * 100
            )
    
    def generate_disagreement_report(self):
        """Generate report showing only disagreement cases"""
        report = f"""# VLM vs CSV Ground Truth - Disagreement Analysis

## Summary

- **Total Cases Analyzed**: {self.analysis_results['total_cases']}
- **Passed (Agreement)**: {self.analysis_results['agreements']} cases ({self.analysis_results['agreements']/self.analysis_results['total_cases']*100:.1f}%)
- **Failed (Disagreement)**: {self.analysis_results['disagreements']} cases ({self.analysis_results['disagreements']/self.analysis_results['total_cases']*100:.1f}%)
- **VLM Accuracy**: {self.analysis_results['vlm_accuracy']:.1f}%

## Disagreement Breakdown

### Type 1: VLM says FALSE_POSITIVE, CSV says TRUE_POSITIVE (Valid)
**Count**: {len(self.analysis_results['disagreement_types']['vlm_fp_human_tp'])} cases
**Impact**: VLM incorrectly filtering out valid safety violations

### Type 2: VLM says TRUE_POSITIVE, CSV says FALSE_POSITIVE (Invalid)  
**Count**: {len(self.analysis_results['disagreement_types']['vlm_tp_human_fp'])} cases
**Impact**: VLM failing to filter false positives

---

# Detailed Disagreement Cases

"""
        
        # Sort disagreements by type
        type1_cases = [c for c in self.disagreements if c['vlm_prediction'] == 'FALSE_POSITIVE' and c['csv_ground_truth'] == 'TRUE_POSITIVE']
        type2_cases = [c for c in self.disagreements if c['vlm_prediction'] == 'TRUE_POSITIVE' and c['csv_ground_truth'] == 'FALSE_POSITIVE']
        
        # Type 1 disagreements
        if type1_cases:
            report += "\n## Type 1: VLM Incorrectly Filtered Valid Cases (FALSE NEGATIVES)\n"
            report += "These are CRITICAL - real safety violations that VLM would filter out!\n\n"
            
            for i, case in enumerate(type1_cases, 1):
                report += f"""
### {i}. Case: {case['case_number']} ⚠️ CRITICAL
- **CSV Ground Truth**: TRUE_POSITIVE (Valid safety violation)
- **VLM Prediction**: FALSE_POSITIVE (Would filter out!)
- **FP Likelihood**: {case['metrics']['fp_likelihood']}%
- **Person Present**: {case['metrics']['person_present']}
- **PPE Compliance**: {case['metrics']['ppe_compliance']}

**VLM Confidence Response Preview**:
```
{case['confidence_preview']}
```

---
"""
        
        # Type 2 disagreements
        if type2_cases:
            report += "\n## Type 2: VLM Failed to Filter False Positives\n"
            report += "These reduce efficiency - false alarms that VLM didn't catch\n\n"
            
            for i, case in enumerate(type2_cases, 1):
                report += f"""
### {i}. Case: {case['case_number']}
- **CSV Ground Truth**: FALSE_POSITIVE (Invalid/false alarm)
- **VLM Prediction**: TRUE_POSITIVE (Failed to filter)
- **FP Likelihood**: {case['metrics']['fp_likelihood']}%
- **Person Present**: {case['metrics']['person_present']}
- **PPE Compliance**: {case['metrics']['ppe_compliance']}

**VLM Confidence Response Preview**:
```
{case['confidence_preview']}
```

---
"""
        
        return report
    
    def save_results(self):
        """Save analysis results"""
        # Create disagreements-only webpage data
        webpage_data = {
            'summary': {
                'total_analyzed': self.analysis_results['total_cases'],
                'passed': self.analysis_results['agreements'],
                'failed': self.analysis_results['disagreements'],
                'accuracy': self.analysis_results['vlm_accuracy']
            },
            'disagreements': []
        }
        
        # Add disagreement details for webpage
        for case in self.disagreements:
            webpage_data['disagreements'].append({
                'case_number': case['case_number'],
                'csv_label': case['csv_ground_truth'],
                'vlm_prediction': case['vlm_prediction'],
                'fp_likelihood': case['metrics']['fp_likelihood'],
                'person_present': case['metrics']['person_present'],
                'ppe_compliance': case['metrics']['ppe_compliance'],
                'is_critical': case['vlm_prediction'] == 'FALSE_POSITIVE' and case['csv_ground_truth'] == 'TRUE_POSITIVE'
            })
        
        # Save report
        with open('vlm_csv_disagreements_only.md', 'w', encoding='utf-8') as f:
            f.write(self.generate_disagreement_report())
            
        # Save JSON for webpage
        with open('vlm_csv_disagreements.json', 'w', encoding='utf-8') as f:
            json.dump(webpage_data, f, indent=2)
            
        print(f"\n✅ Analysis Complete!")
        print(f"\n📊 Results:")
        print(f"   - Total cases: {self.analysis_results['total_cases']}")
        print(f"   - PASSED (Agreement): {self.analysis_results['agreements']} ({self.analysis_results['vlm_accuracy']:.1f}%)")
        print(f"   - FAILED (Disagreement): {self.analysis_results['disagreements']}")
        print(f"\n⚠️  Critical Issues:")
        print(f"   - Valid cases filtered: {len(self.analysis_results['disagreement_types']['vlm_fp_human_tp'])}")
        print(f"   - False positives missed: {len(self.analysis_results['disagreement_types']['vlm_tp_human_fp'])}")
        print(f"\n📁 Files created:")
        print(f"   - vlm_csv_disagreements_only.md (detailed report)")
        print(f"   - vlm_csv_disagreements.json (webpage data)")

if __name__ == "__main__":
    analyzer = VLMGroundTruthAnalyzer()
    analyzer.analyze_all_cases()
    analyzer.save_results()