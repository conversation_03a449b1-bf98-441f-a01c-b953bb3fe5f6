#!/usr/bin/env python3
"""
Test explicit safety prompt on critical cases
Ensure 100% valid violation protection
"""

import json
import base64
import requests
import os

VLM_API_URL = "http://100.106.127.35:9500/v1/chat/completions"
VLM_MODEL = "VLM-38B-AWQ"

def test_explicit_prompt():
    """Test explicit prompt on known cases"""
    
    print("TESTING EXPLICIT SAFETY PROMPT")
    print("="*60)
    
    # Load explicit prompt
    with open('explicit_safety_prompt.txt', 'r') as f:
        prompt = f.read()
    
    # Test cases - mix of valid and FP
    test_cases = {
        # Valid violations (should output FALSE POSITIVE: NO)
        "V1250627179": "Missing GO/STOP bat",
        "V1250627208": "Mobile phone use", 
        "V1250627194": "Vest not fastened",
        # False positives (should output FALSE POSITIVE: YES)
        "V1250627134": "Crane structure",
        "V1250627151": "Full PPE at wharf"
    }
    
    # Load data
    with open('valo_batch_round3_complete.json', 'r') as f:
        data = json.load(f)
    
    session = requests.Session()
    results = []
    
    for case_num, description in test_cases.items():
        case = next((c for c in data['results'] if c['case_number'] == case_num), None)
        if not case:
            continue
        
        print(f"\nCase: {case_num} - {description}")
        print(f"Actual: {'False Positive' if case['is_false_positive'] else 'Valid Violation'}")
        
        # Encode images
        try:
            with open(case['source_image'], 'rb') as f:
                source_b64 = base64.b64encode(f.read()).decode('utf-8')
            with open(case['cropped_image'], 'rb') as f:
                cropped_b64 = base64.b64encode(f.read()).decode('utf-8')
        except:
            print("Error encoding images")
            continue
        
        # Call VLM
        payload = {
            "model": VLM_MODEL,
            "messages": [{
                "role": "user",
                "content": [
                    {"type": "text", "text": prompt},
                    {"type": "text", "text": "\n\nSOURCE IMAGE:"},
                    {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{source_b64}"}},
                    {"type": "text", "text": "\n\nCROPPED IMAGE:"},
                    {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{cropped_b64}"}}
                ]
            }],
            "temperature": 0.1,
            "max_tokens": 150
        }
        
        try:
            response = session.post(VLM_API_URL, json=payload, timeout=20)
            if response.status_code == 200:
                result = response.json()
                vlm_response = result['choices'][0]['message']['content'].strip()
                print(f"Response:\n{vlm_response}")
                
                # Parse response
                predicted_fp = 'YES' in vlm_response.split('FALSE POSITIVE:')[1][:5] if 'FALSE POSITIVE:' in vlm_response else None
                
                if predicted_fp is not None:
                    correct = predicted_fp == case['is_false_positive']
                    print(f"Result: {'✅ CORRECT' if correct else '❌ WRONG'}")
                    
                    results.append({
                        'case': case_num,
                        'correct': correct,
                        'is_fp': case['is_false_positive'],
                        'predicted_fp': predicted_fp
                    })
                else:
                    print("Could not parse response")
                    
        except Exception as e:
            print(f"Error: {e}")
    
    # Summary
    print("\n" + "="*60)
    print("SUMMARY")
    
    if results:
        # Valid protection
        valid_results = [r for r in results if not r['is_fp']]
        if valid_results:
            protected = sum(1 for r in valid_results if not r['predicted_fp'])
            print(f"\nValid violations: {protected}/{len(valid_results)} protected")
        
        # FP detection
        fp_results = [r for r in results if r['is_fp']]
        if fp_results:
            detected = sum(1 for r in fp_results if r['predicted_fp'])
            print(f"False positives: {detected}/{len(fp_results)} detected")
        
        # Overall
        correct = sum(1 for r in results if r['correct'])
        print(f"\nOverall: {correct}/{len(results)} correct ({correct/len(results)*100:.0f}%)")
        
        if valid_results and protected == len(valid_results):
            print("\n✅ SUCCESS: All valid violations protected!")
            print("Ready for full dataset testing")
        else:
            print("\n⚠️ Still missing some valid violations")

if __name__ == "__main__":
    test_explicit_prompt()