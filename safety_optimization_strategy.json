{"phase1": {"name": "Safety Validation", "goal": "Ensure 100% valid violation protection", "approach": "Test on all valid violations with safety-first prompt", "success_criteria": "≥98% valid protection rate"}, "phase2": {"name": "Balance Optimization", "goal": "Maximize FP detection while maintaining safety", "approach": "Test hybrid prompt with different thresholds", "success_criteria": "≥70% FP detection with ≥95% valid protection"}, "phase3": {"name": "Production Validation", "goal": "Verify performance on full dataset", "approach": "Run optimized prompt on all 1250 cases", "success_criteria": "Meet both FP and safety targets"}}