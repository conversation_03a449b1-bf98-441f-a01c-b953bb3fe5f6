import React, { useState, useEffect } from 'react';
import { 
  Shield, 
  AlertTriangle, 
  Activity, 
  Camera,
  TrendingUp,
  Clock,
  DollarSign,
  Terminal,
  Zap
} from 'lucide-react';
import { 
  ProcessingResultsChart, 
  ConfidenceDistribution<PERSON><PERSON>,
  Comparison<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
} from '../components/charts';

interface VALOAlert {
  alert_id: string;
  timestamp: string;
  camera_id: string;
  terminal: 'P1' | 'P2' | 'P3';
  detection_type: string;
  vlm_analysis?: {
    category: string;
    confidence: number;
    recommendation: string;
    reasoning: string;
  };
  status: 'pending' | 'processed';
}

export const SurveillanceDashboard: React.FC = () => {
  const [alerts, setAlerts] = useState<VALOAlert[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [terminalStats, setTerminalStats] = useState({
    P1: { alerts: 0, filtered: 0, rate: 0 },
    P2: { alerts: 0, filtered: 0, rate: 0 },
    P3: { alerts: 0, filtered: 0, rate: 0 }
  });

  // Simulate real-time alerts
  useEffect(() => {
    const interval = setInterval(() => {
      const newAlert: VALOAlert = {
        alert_id: `VALO_${Date.now()}`,
        timestamp: new Date().toISOString(),
        camera_id: `Terminal_P${Math.ceil(Math.random() * 3)}_${Math.random() < 0.5 ? 'QC' : 'VS'}${Math.floor(Math.random() * 10)}`,
        terminal: `P${Math.ceil(Math.random() * 3)}` as 'P1' | 'P2' | 'P3',
        detection_type: Math.random() < 0.7 ? 'PPE_VIOLATION' : 'UNAUTHORIZED_ACCESS',
        status: 'pending'
      };

      setAlerts(prev => [newAlert, ...prev].slice(0, 20));
      
      // Auto-process after 2 seconds
      setTimeout(() => processAlert(newAlert.alert_id), 2000);
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  const processAlert = (alertId: string) => {
    setAlerts(prev => prev.map(alert => {
      if (alert.alert_id === alertId) {
        const confidence = 60 + Math.random() * 35;
        const isFalsePositive = confidence > 80;
        
        return {
          ...alert,
          status: 'processed' as const,
          vlm_analysis: {
            category: getRandomCategory(),
            confidence,
            recommendation: isFalsePositive ? 'DISMISS_ALERT' : 'HUMAN_REVIEW',
            reasoning: getRandomReasoning()
          }
        };
      }
      return alert;
    }));
  };

  const getRandomCategory = () => {
    const categories = [
      'STRUCTURE_MISIDENTIFIED',
      'PERSONNEL_WITH_PROPER_PPE',
      'EQUIPMENT_CONFUSION',
      'NO_ACTUAL_VIOLATION'
    ];
    return categories[Math.floor(Math.random() * categories.length)];
  };

  const getRandomReasoning = () => {
    const reasonings = [
      'Quay crane arm detected as personnel',
      'Worker wearing complete PPE (hard hat, hi-vis vest)',
      'Container handling equipment misidentified',
      'Shadow/lighting condition causing false detection'
    ];
    return reasonings[Math.floor(Math.random() * reasonings.length)];
  };

  // Calculate metrics
  const processedAlerts = alerts.filter(a => a.status === 'processed');
  const falsePositives = processedAlerts.filter(a => a.vlm_analysis?.recommendation === 'DISMISS_ALERT');
  const filterRate = processedAlerts.length > 0 ? (falsePositives.length / processedAlerts.length * 100) : 0;

  return (
    <div className="min-h-screen bg-dark-500 bg-grid-pattern">
      {/* Header */}
      <header className="nav-surveillance">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Shield className="w-10 h-10 text-primary-500" />
              <div>
                <h1 className="text-2xl font-bold text-gray-100 tracking-wider">
                  AI-FARM SURVEILLANCE
                </h1>
                <p className="text-sm text-gray-400 uppercase tracking-widest">
                  PSA VALO False Positive Reduction System
                </p>
              </div>
            </div>
            <div className="flex items-center gap-6">
              <div className="text-right">
                <p className="text-xs text-gray-500 uppercase tracking-wider">System Status</p>
                <div className="flex items-center gap-2 text-primary-500">
                  <Activity className="w-4 h-4 animate-pulse" />
                  <span className="font-bold tracking-wider">OPERATIONAL</span>
                </div>
              </div>
              <div className="text-right">
                <p className="text-xs text-gray-500 uppercase tracking-wider">VLM Model</p>
                <p className="text-sm font-mono text-secondary-400">InternVL3 38B AWQ</p>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-6 py-8">
        {/* Key Metrics */}
        <div className="surveillance-grid grid-cols-1 md:grid-cols-4 mb-8">
          <div className="metric-card-surveillance metric-primary">
            <div className="flex items-start justify-between">
              <div>
                <p className="metric-label-surveillance">Alerts Processed</p>
                <p className="metric-value-surveillance">{processedAlerts.length}</p>
                <p className="text-xs text-gray-500 mt-1">Real-time analysis</p>
              </div>
              <Zap className="w-8 h-8 text-primary-500 opacity-50" />
            </div>
          </div>

          <div className="metric-card-surveillance metric-danger">
            <div className="flex items-start justify-between">
              <div>
                <p className="metric-label-surveillance">False Positives</p>
                <p className="metric-value-surveillance text-danger-400">{falsePositives.length}</p>
                <p className="text-xs text-gray-500 mt-1">{filterRate.toFixed(1)}% filter rate</p>
              </div>
              <AlertTriangle className="w-8 h-8 text-danger-500 opacity-50" />
            </div>
          </div>

          <div className="metric-card-surveillance metric-warning">
            <div className="flex items-start justify-between">
              <div>
                <p className="metric-label-surveillance">Time Saved</p>
                <p className="metric-value-surveillance text-warning-400">
                  {(falsePositives.length * 3).toFixed(0)}m
                </p>
                <p className="text-xs text-gray-500 mt-1">This session</p>
              </div>
              <Clock className="w-8 h-8 text-warning-500 opacity-50" />
            </div>
          </div>

          <div className="metric-card-surveillance metric-success">
            <div className="flex items-start justify-between">
              <div>
                <p className="metric-label-surveillance">Cost Savings</p>
                <p className="metric-value-surveillance text-success-400">
                  ${(falsePositives.length * 2.5).toFixed(0)}
                </p>
                <p className="text-xs text-gray-500 mt-1">Live calculation</p>
              </div>
              <DollarSign className="w-8 h-8 text-success-500 opacity-50" />
            </div>
          </div>
        </div>

        {/* Main Grid */}
        <div className="surveillance-grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Alert Feed */}
          <div className="lg:col-span-2">
            <div className="alert-feed-container">
              <div className="alert-feed-header">
                <h2 className="text-lg font-semibold text-gray-100 uppercase tracking-wider">
                  VALO Alert Feed
                </h2>
                {isProcessing && (
                  <div className="processing-indicator">
                    <span className="text-xs text-gray-500 uppercase">Processing</span>
                    <div className="processing-dot"></div>
                    <div className="processing-dot"></div>
                    <div className="processing-dot"></div>
                  </div>
                )}
              </div>
              <div className="alert-feed-body surveillance-scrollbar">
                {alerts.map(alert => (
                  <div key={alert.alert_id} className="alert-item">
                    <div className="flex items-start gap-4">
                      <div className="valo-alert-thumbnail flex items-center justify-center">
                        <Camera className="w-8 h-8 text-gray-600" />
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center gap-3">
                            <h3 className="font-mono text-sm text-gray-200">{alert.alert_id}</h3>
                            <span className={`terminal-${alert.terminal.toLowerCase()}`}>
                              TERMINAL {alert.terminal}
                            </span>
                            {alert.status === 'processed' && alert.vlm_analysis && (
                              <span className={alert.vlm_analysis.recommendation === 'DISMISS_ALERT' 
                                ? 'status-false-positive' 
                                : 'status-pending-review'}>
                                {alert.vlm_analysis.recommendation === 'DISMISS_ALERT' 
                                  ? 'FALSE POSITIVE' 
                                  : 'NEEDS REVIEW'}
                              </span>
                            )}
                          </div>
                          <span className="text-xs text-gray-500">
                            {new Date(alert.timestamp).toLocaleTimeString()}
                          </span>
                        </div>
                        
                        <div className="camera-indicator mb-2">
                          <Camera className="w-3 h-3" />
                          {alert.camera_id}
                        </div>

                        {alert.vlm_analysis && (
                          <div className="bg-dark-100 rounded p-3 mt-2">
                            <p className="text-sm text-gray-300 mb-2">{alert.vlm_analysis.reasoning}</p>
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-4">
                                <span className="text-xs text-gray-500">
                                  Category: <span className="text-gray-400">{alert.vlm_analysis.category}</span>
                                </span>
                                <span className="text-xs text-gray-500">
                                  Confidence: <span className="text-primary-400">{alert.vlm_analysis.confidence.toFixed(1)}%</span>
                                </span>
                              </div>
                            </div>
                            <div className="confidence-meter mt-2">
                              <div 
                                className="confidence-meter-fill" 
                                style={{ width: `${alert.vlm_analysis.confidence}%` }}
                              />
                            </div>
                          </div>
                        )}

                        {alert.status === 'pending' && (
                          <div className="flex items-center gap-2 text-sm text-gray-500 mt-2">
                            <Activity className="w-4 h-4 animate-pulse" />
                            <span>Analyzing with VLM...</span>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Terminal Performance */}
          <div>
            <div className="surveillance-card p-6 mb-6">
              <h2 className="text-lg font-semibold text-gray-100 uppercase tracking-wider mb-6">
                Terminal Analysis
              </h2>
              
              {['P1', 'P2', 'P3'].map(terminal => {
                const terminalAlerts = alerts.filter(a => a.terminal === terminal && a.status === 'processed');
                const terminalFP = terminalAlerts.filter(a => a.vlm_analysis?.recommendation === 'DISMISS_ALERT');
                const rate = terminalAlerts.length > 0 ? (terminalFP.length / terminalAlerts.length * 100) : 0;
                
                return (
                  <div key={terminal} className="mb-6 last:mb-0">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center gap-2">
                        <Terminal className="w-4 h-4 text-gray-400" />
                        <span className={`terminal-${terminal.toLowerCase()}`}>
                          TERMINAL {terminal}
                        </span>
                      </div>
                      <span className="text-xs text-gray-500">
                        {terminal === 'P1' ? 'Quay Cranes' : terminal === 'P2' ? 'Vessel Ops' : 'Container Yard'}
                      </span>
                    </div>
                    <div className="bg-dark-100 rounded p-3">
                      <div className="flex justify-between text-sm mb-2">
                        <span className="text-gray-400">Alerts</span>
                        <span className="text-gray-200">{terminalAlerts.length}</span>
                      </div>
                      <div className="flex justify-between text-sm mb-2">
                        <span className="text-gray-400">False Positives</span>
                        <span className="text-danger-400">{terminalFP.length}</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-400">Filter Rate</span>
                        <span className="text-primary-400">{rate.toFixed(1)}%</span>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>

            {/* PSA Context */}
            <div className="surveillance-card p-6">
              <h3 className="text-lg font-semibold text-gray-100 uppercase tracking-wider mb-4">
                PSA VALO Context
              </h3>
              <div className="space-y-3 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-400">Monthly Alerts</span>
                  <span className="text-gray-200 font-mono">17,268</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Current FP Rate</span>
                  <span className="text-danger-400 font-mono">97%</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Target Reduction</span>
                  <span className="text-success-400 font-mono">70%</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Annual Savings</span>
                  <span className="text-primary-400 font-mono">$351K</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Charts */}
        <div className="surveillance-grid grid-cols-1 lg:grid-cols-2 gap-8 mt-8">
          <div className="chart-container">
            <h3 className="chart-title">PROCESSING PERFORMANCE</h3>
            <ProcessingResultsChart data={{
              totalAlerts: alerts.filter(a => a.status === 'processed').length,
              falsePositivesFiltered: falsePositives.length,
              alertsRequiringReview: processedAlerts.length - falsePositives.length
            }} />
          </div>
          
          <div className="chart-container">
            <h3 className="chart-title">CONFIDENCE DISTRIBUTION</h3>
            <ConfidenceDistributionChart data={[
              { range: '60-70%', count: processedAlerts.filter(a => a.vlm_analysis && a.vlm_analysis.confidence >= 60 && a.vlm_analysis.confidence < 70).length, percentage: 15 },
              { range: '70-80%', count: processedAlerts.filter(a => a.vlm_analysis && a.vlm_analysis.confidence >= 70 && a.vlm_analysis.confidence < 80).length, percentage: 25 },
              { range: '80-90%', count: processedAlerts.filter(a => a.vlm_analysis && a.vlm_analysis.confidence >= 80 && a.vlm_analysis.confidence < 90).length, percentage: 40 },
              { range: '90-100%', count: processedAlerts.filter(a => a.vlm_analysis && a.vlm_analysis.confidence >= 90).length, percentage: 20 },
            ]} />
          </div>
        </div>
      </main>
    </div>
  );
};