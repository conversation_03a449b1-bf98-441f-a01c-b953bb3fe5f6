import React, { useEffect, useState } from 'react';
import { Clock, CheckCircle, AlertCircle, XCircle, Play, Pause } from 'lucide-react';
import { SurveillanceButton } from '../ui';
import { ProcessingStatus, BatchProcessingResponse } from '../../types';
import { batchService } from '../../services';
import clsx from 'clsx';

export interface ProcessingProgressProps {
  batchId: string;
  onComplete?: (batch: BatchProcessingResponse) => void;
  onError?: (error: string) => void;
  autoRefresh?: boolean;
  refreshInterval?: number;
  className?: string;
}

export const ProcessingProgress: React.FC<ProcessingProgressProps> = ({
  batchId,
  onComplete,
  onError,
  autoRefresh = true,
  refreshInterval = 2000,
  className,
}) => {
  const [batch, setBatch] = useState<BatchProcessingResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [elapsed, setElapsed] = useState(0);
  const [isPolling, setIsPolling] = useState(autoRefresh);

  useEffect(() => {
    let intervalId: NodeJS.Timeout;
    let elapsedIntervalId: NodeJS.Timeout;

    const fetchBatchStatus = async () => {
      try {
        const batchStatus = await batchService.getBatchStatus(batchId);
        setBatch(batchStatus);
        setLoading(false);
        setError(null);

        if (batchStatus.status === ProcessingStatus.COMPLETED) {
          setIsPolling(false);
          onComplete?.(batchStatus);
        } else if (batchStatus.status === ProcessingStatus.FAILED) {
          setIsPolling(false);
          const errorMsg = batchStatus.summary?.error_message || 'Processing failed';
          setError(errorMsg);
          onError?.(errorMsg);
        }
      } catch (err) {
        const errorMsg = err instanceof Error ? err.message : 'Failed to fetch status';
        setError(errorMsg);
        setLoading(false);
        onError?.(errorMsg);
      }
    };

    // Initial fetch
    fetchBatchStatus();

    // Setup polling
    if (isPolling) {
      intervalId = setInterval(fetchBatchStatus, refreshInterval);
    }

    // Setup elapsed time counter
    elapsedIntervalId = setInterval(() => {
      setElapsed(prev => prev + 1);
    }, 1000);

    return () => {
      if (intervalId) clearInterval(intervalId);
      if (elapsedIntervalId) clearInterval(elapsedIntervalId);
    };
  }, [batchId, isPolling, refreshInterval, onComplete, onError]);

  const getStatusIcon = (status: ProcessingStatus) => {
    switch (status) {
      case ProcessingStatus.PENDING:
        return <Clock className="w-5 h-5 text-gray-400" />;
      case ProcessingStatus.PROCESSING:
        return <Play className="w-5 h-5 text-primary-400 animate-pulse" />;
      case ProcessingStatus.COMPLETED:
        return <CheckCircle className="w-5 h-5 text-success-400" />;
      case ProcessingStatus.FAILED:
        return <XCircle className="w-5 h-5 text-danger-400" />;
      default:
        return <AlertCircle className="w-5 h-5 text-gray-400" />;
    }
  };

  const getStatusText = (status: ProcessingStatus) => {
    switch (status) {
      case ProcessingStatus.PENDING:
        return 'Queued for processing';
      case ProcessingStatus.PROCESSING:
        return 'Processing in progress';
      case ProcessingStatus.COMPLETED:
        return 'Processing completed';
      case ProcessingStatus.FAILED:
        return 'Processing failed';
      default:
        return 'Unknown status';
    }
  };

  const getProgressPercentage = () => {
    if (!batch) return 0;
    const processed = batch.summary?.processed_cases || 0;
    const total = batch.total_cases || 1;
    return Math.round((processed / total) * 100);
  };

  const formatElapsedTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;

    if (hours > 0) {
      return `${hours}h ${minutes}m ${secs}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${secs}s`;
    } else {
      return `${secs}s`;
    }
  };

  const getEstimatedTimeRemaining = () => {
    if (!batch || batch.status !== ProcessingStatus.PROCESSING) return null;
    
    const processed = batch.summary?.processed_cases || 0;
    const total = batch.total_cases || 1;
    const remaining = total - processed;
    
    if (processed === 0 || elapsed === 0) return null;
    
    const avgTimePerCase = elapsed / processed;
    const estimatedRemaining = Math.round(remaining * avgTimePerCase);
    
    return formatElapsedTime(estimatedRemaining);
  };

  if (loading && !batch) {
    return (
      <div className={clsx('surveillance-card', className)}>
        <div className="surveillance-card-body">
          <div className="animate-pulse">
            <div className="flex items-center space-x-3 mb-4">
              <div className="w-5 h-5 bg-gray-700 rounded"></div>
              <div className="h-4 bg-gray-700 rounded w-1/3"></div>
            </div>
            <div className="h-3 bg-gray-700 rounded mb-2"></div>
            <div className="h-4 bg-gray-700 rounded w-1/4"></div>
          </div>
        </div>
      </div>
    );
  }

  if (error && !batch) {
    return (
      <div className={clsx('surveillance-card', className)}>
        <div className="surveillance-card-body">
          <div className="status-false-positive p-4 rounded-md border border-danger-800">
            <div className="flex items-start">
              <div className="flex-shrink-0 text-danger-400">
                <AlertCircle className="w-5 h-5" />
              </div>
              <div className="ml-3 flex-1">
                <h3 className="text-sm font-medium text-danger-400">Processing Error</h3>
                <div className="text-sm text-danger-400 mt-1">{error}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={clsx('surveillance-card', className)}>
      <div className="surveillance-card-header">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            {batch && getStatusIcon(batch.status)}
            <div>
              <h3 className="text-lg font-medium text-gray-100">
                Batch Processing
              </h3>
              <p className="text-sm text-gray-400">
                {batch && getStatusText(batch.status)}
              </p>
            </div>
          </div>
          
          <div className="text-right">
            <p className="text-sm font-medium text-gray-100 font-mono">
              {formatElapsedTime(elapsed)}
            </p>
            <p className="text-xs text-gray-400">Elapsed</p>
          </div>
        </div>
      </div>

      <div className="surveillance-card-body">
        {batch && (
          <div className="space-y-6">
            {/* Progress Bar */}
            <div className="space-y-2">
              <div className="flex justify-between items-center text-sm">
                <span className="font-medium text-gray-300">
                  Processing: {batch.summary?.processed_cases || 0} of {batch.total_cases} cases
                </span>
                <span className="text-gray-400 font-mono">{getProgressPercentage()}%</span>
              </div>
              <div className="h-4 bg-dark-100 rounded-full overflow-hidden">
                <div
                  className={clsx(
                    'h-full transition-all duration-300 ease-out',
                    {
                      'bg-success-500': batch.status === ProcessingStatus.COMPLETED,
                      'bg-danger-500': batch.status === ProcessingStatus.FAILED,
                      'bg-primary-500': batch.status !== ProcessingStatus.COMPLETED && batch.status !== ProcessingStatus.FAILED,
                      'animate-pulse-slow': batch.status === ProcessingStatus.PROCESSING,
                    }
                  )}
                  style={{ width: `${getProgressPercentage()}%` }}
                  role="progressbar"
                  aria-valuenow={getProgressPercentage()}
                  aria-valuemin={0}
                  aria-valuemax={100}
                />
              </div>
            </div>

            {/* Stats Grid */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <p className="text-2xl font-bold text-gray-100 font-mono">
                  {batch.total_cases.toLocaleString()}
                </p>
                <p className="text-sm text-gray-400">Total Cases</p>
              </div>
              
              <div className="text-center">
                <p className="text-2xl font-bold text-primary-400 font-mono">
                  {(batch.summary?.processed_cases || 0).toLocaleString()}
                </p>
                <p className="text-sm text-gray-400">Processed</p>
              </div>
              
              <div className="text-center">
                <p className="text-2xl font-bold text-danger-400 font-mono">
                  {(batch.summary?.failed_cases || 0).toLocaleString()}
                </p>
                <p className="text-sm text-gray-400">Failed</p>
              </div>
              
              <div className="text-center">
                <p className="text-2xl font-bold text-gray-300 font-mono">
                  {((batch.total_cases || 0) - (batch.summary?.processed_cases || 0)).toLocaleString()}
                </p>
                <p className="text-sm text-gray-400">Remaining</p>
              </div>
            </div>

            {/* Time Information */}
            {batch.status === ProcessingStatus.PROCESSING && (
              <div className="flex justify-between items-center text-sm text-gray-400">
                <span className="font-mono">Time elapsed: {formatElapsedTime(elapsed)}</span>
                {getEstimatedTimeRemaining() && (
                  <span className="font-mono">Estimated remaining: {getEstimatedTimeRemaining()}</span>
                )}
              </div>
            )}

            {/* Batch Information */}
            <div className="bg-dark-100 rounded-lg p-4 border border-gray-800">
              <h4 className="text-sm font-medium text-gray-300 mb-2">Batch Details</h4>
              <div className="grid grid-cols-2 gap-2 text-sm">
                <div>
                  <span className="text-gray-400">Batch ID:</span>
                  <span className="ml-2 font-mono text-xs text-gray-200">{batch.batch_id}</span>
                </div>
                <div>
                  <span className="text-gray-400">Started:</span>
                  <span className="ml-2 text-gray-200 font-mono">{new Date(batch.started_at).toLocaleTimeString()}</span>
                </div>
                {batch.completed_at && (
                  <div className="col-span-2">
                    <span className="text-gray-400">Completed:</span>
                    <span className="ml-2 text-gray-200 font-mono">{new Date(batch.completed_at).toLocaleTimeString()}</span>
                  </div>
                )}
              </div>
            </div>

            {/* Control Buttons */}
            <div className="flex justify-between items-center">
              <SurveillanceButton
                variant="secondary"
                size="sm"
                leftIcon={isPolling ? Pause : Play}
                onClick={() => setIsPolling(!isPolling)}
                disabled={batch.status === ProcessingStatus.COMPLETED || batch.status === ProcessingStatus.FAILED}
              >
                {isPolling ? 'Pause Updates' : 'Resume Updates'}
              </SurveillanceButton>

              {batch.status === ProcessingStatus.COMPLETED && (
                <span className="text-sm text-success-400 font-medium">
                  ✓ Processing completed successfully
                </span>
              )}
            </div>

            {/* Error Information */}
            {batch.status === ProcessingStatus.FAILED && batch.summary?.error_message && (
              <div className="status-false-positive p-4 rounded-md border border-danger-800">
                <div className="flex items-start">
                  <div className="flex-shrink-0 text-danger-400">
                    <AlertCircle className="w-5 h-5" />
                  </div>
                  <div className="ml-3 flex-1">
                    <h3 className="text-sm font-medium text-danger-400">Processing Failed</h3>
                    <div className="text-sm text-danger-400 mt-1">{batch.summary.error_message}</div>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};