import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Upload, Settings, Play, AlertCircle, CheckCircle } from 'lucide-react';
import { 
  <PERSON>ton, 
  Card, 
  CardHeader, 
  CardBody, 
  BatchFileUpload, 
  Alert,
  LoadingSpinner 
} from '../components/ui';
import { batchService } from '../services';
import { BatchProcessingResponse } from '../types';
import toast from 'react-hot-toast';

export const UploadPage: React.FC = () => {
  const navigate = useNavigate();
  const [csvFile, setCsvFile] = useState<File | null>(null);
  const [imagesFile, setImagesFile] = useState<File | null>(null);
  const [useAutoLearning, setUseAutoLearning] = useState(true);
  const [priority, setPriority] = useState<'low' | 'normal' | 'high'>('normal');
  const [customThresholds, setCustomThresholds] = useState({
    highConfidence: 80,
    mediumConfidence: 60,
    lowConfidence: 40,
  });
  const [useCustomThresholds, setUseCustomThresholds] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);

  const handleUpload = async () => {
    if (!csvFile) {
      toast.error('Please select a CSV file to upload');
      return;
    }

    setIsUploading(true);
    setUploadProgress(0);

    try {
      // Simulate upload progress
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 90;
          }
          return prev + 10;
        });
      }, 200);

      const response: BatchProcessingResponse = await batchService.uploadAndProcess(
        csvFile,
        imagesFile || undefined,
        undefined,
        {
          useAutoLearning,
          customThresholds: useCustomThresholds ? customThresholds : undefined,
          priority,
        }
      );

      clearInterval(progressInterval);
      setUploadProgress(100);

      toast.success('Upload successful! Processing started.');
      
      // Navigate to processing page
      setTimeout(() => {
        navigate(`/processing/${response.batch_id}`);
      }, 1000);

    } catch (error) {
      console.error('Upload failed:', error);
      toast.error('Upload failed. Please try again.');
      setIsUploading(false);
      setUploadProgress(0);
    }
  };

  const validateFiles = () => {
    const errors: string[] = [];
    
    if (!csvFile) {
      errors.push('CSV file is required');
    } else {
      const csvValidation = batchService.validateCsvFile(csvFile);
      if (!csvValidation.isValid) {
        errors.push(...csvValidation.errors);
      }
    }

    if (imagesFile) {
      const imagesValidation = batchService.validateImagesZip(imagesFile);
      if (!imagesValidation.isValid) {
        errors.push(...imagesValidation.errors);
      }
    }

    return errors;
  };

  const validationErrors = validateFiles();
  const canUpload = validationErrors.length === 0 && !isUploading;

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            Upload Your Data
          </h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Upload your CSV case data and optional violation images to start the AI-FARM analysis process.
          </p>
        </div>

        {/* Progress Steps */}
        <div className="mb-8">
          <div className="flex items-center justify-center space-x-8">
            <div className="flex items-center">
              <div className="bg-primary-600 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-medium">
                1
              </div>
              <span className="ml-2 text-sm font-medium text-primary-600">Upload Files</span>
            </div>
            <div className="w-16 h-0.5 bg-gray-300"></div>
            <div className="flex items-center">
              <div className="bg-gray-300 text-gray-500 rounded-full w-8 h-8 flex items-center justify-center text-sm font-medium">
                2
              </div>
              <span className="ml-2 text-sm font-medium text-gray-500">Configure</span>
            </div>
            <div className="w-16 h-0.5 bg-gray-300"></div>
            <div className="flex items-center">
              <div className="bg-gray-300 text-gray-500 rounded-full w-8 h-8 flex items-center justify-center text-sm font-medium">
                3
              </div>
              <span className="ml-2 text-sm font-medium text-gray-500">Process</span>
            </div>
          </div>
        </div>

        {/* File Upload Section */}
        <Card className="mb-8">
          <CardHeader>
            <div className="flex items-center space-x-2">
              <Upload className="w-5 h-5 text-primary-600" />
              <h2 className="text-xl font-semibold text-gray-900">File Upload</h2>
            </div>
            <p className="text-sm text-gray-600">
              Select your case data CSV file and optionally include violation images.
            </p>
          </CardHeader>
          <CardBody>
            <BatchFileUpload
              csvFile={csvFile}
              imagesFile={imagesFile}
              onCsvFileSelect={setCsvFile}
              onImagesFileSelect={setImagesFile}
            />
            
            {validationErrors.length > 0 && (
              <div className="mt-4">
                <Alert variant="danger" title="File Validation Errors">
                  <ul className="list-disc list-inside space-y-1">
                    {validationErrors.map((error, index) => (
                      <li key={index}>{error}</li>
                    ))}
                  </ul>
                </Alert>
              </div>
            )}
          </CardBody>
        </Card>

        {/* Configuration Section */}
        <Card className="mb-8">
          <CardHeader>
            <div className="flex items-center space-x-2">
              <Settings className="w-5 h-5 text-primary-600" />
              <h2 className="text-xl font-semibold text-gray-900">Processing Configuration</h2>
            </div>
            <p className="text-sm text-gray-600">
              Configure processing options and thresholds for your analysis.
            </p>
          </CardHeader>
          <CardBody>
            <div className="space-y-6">
              {/* Auto Learning */}
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-sm font-medium text-gray-900">Auto-Learning</h3>
                  <p className="text-sm text-gray-500">
                    Enable automatic pattern detection and threshold optimization
                  </p>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={useAutoLearning}
                    onChange={(e) => setUseAutoLearning(e.target.checked)}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
                </label>
              </div>

              {/* Processing Priority */}
              <div>
                <h3 className="text-sm font-medium text-gray-900 mb-2">Processing Priority</h3>
                <div className="flex space-x-4">
                  {['low', 'normal', 'high'].map((p) => (
                    <label key={p} className="flex items-center">
                      <input
                        type="radio"
                        name="priority"
                        value={p}
                        checked={priority === p}
                        onChange={(e) => setPriority(e.target.value as any)}
                        className="form-radio h-4 w-4 text-primary-600"
                      />
                      <span className="ml-2 text-sm text-gray-700 capitalize">{p}</span>
                    </label>
                  ))}
                </div>
              </div>

              {/* Custom Thresholds */}
              <div>
                <div className="flex items-center justify-between mb-4">
                  <div>
                    <h3 className="text-sm font-medium text-gray-900">Custom Confidence Thresholds</h3>
                    <p className="text-sm text-gray-500">
                      Override default confidence thresholds for filtering
                    </p>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={useCustomThresholds}
                      onChange={(e) => setUseCustomThresholds(e.target.checked)}
                      className="sr-only peer"
                    />
                    <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
                  </label>
                </div>

                {useCustomThresholds && (
                  <div className="grid grid-cols-3 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        High Confidence
                      </label>
                      <div className="relative">
                        <input
                          type="number"
                          min="0"
                          max="100"
                          value={customThresholds.highConfidence}
                          onChange={(e) => setCustomThresholds(prev => ({
                            ...prev,
                            highConfidence: parseInt(e.target.value)
                          }))}
                          className="form-input pr-8"
                        />
                        <span className="absolute right-3 top-2 text-sm text-gray-500">%</span>
                      </div>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Medium Confidence
                      </label>
                      <div className="relative">
                        <input
                          type="number"
                          min="0"
                          max="100"
                          value={customThresholds.mediumConfidence}
                          onChange={(e) => setCustomThresholds(prev => ({
                            ...prev,
                            mediumConfidence: parseInt(e.target.value)
                          }))}
                          className="form-input pr-8"
                        />
                        <span className="absolute right-3 top-2 text-sm text-gray-500">%</span>
                      </div>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Low Confidence
                      </label>
                      <div className="relative">
                        <input
                          type="number"
                          min="0"
                          max="100"
                          value={customThresholds.lowConfidence}
                          onChange={(e) => setCustomThresholds(prev => ({
                            ...prev,
                            lowConfidence: parseInt(e.target.value)
                          }))}
                          className="form-input pr-8"
                        />
                        <span className="absolute right-3 top-2 text-sm text-gray-500">%</span>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </CardBody>
        </Card>

        {/* Action Section */}
        <Card>
          <CardBody>
            {isUploading ? (
              <div className="text-center py-8">
                <LoadingSpinner size="lg" className="mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Uploading and Starting Processing...
                </h3>
                <p className="text-gray-600 mb-4">
                  Please wait while we upload your files and initialize the processing pipeline.
                </p>
                <div className="max-w-xs mx-auto">
                  <div className="bg-gray-200 rounded-full h-2 overflow-hidden">
                    <div 
                      className="bg-primary-600 h-full transition-all duration-300"
                      style={{ width: `${uploadProgress}%` }}
                    />
                  </div>
                  <p className="text-sm text-gray-500 mt-2">{uploadProgress}% complete</p>
                </div>
              </div>
            ) : (
              <div className="text-center py-4">
                <div className="flex items-center justify-center space-x-4 mb-6">
                  {csvFile && (
                    <div className="flex items-center text-success-600">
                      <CheckCircle className="w-5 h-5 mr-2" />
                      <span className="text-sm">CSV file ready</span>
                    </div>
                  )}
                  {imagesFile && (
                    <div className="flex items-center text-success-600">
                      <CheckCircle className="w-5 h-5 mr-2" />
                      <span className="text-sm">Images ready</span>
                    </div>
                  )}
                  {!csvFile && (
                    <div className="flex items-center text-gray-400">
                      <AlertCircle className="w-5 h-5 mr-2" />
                      <span className="text-sm">CSV file required</span>
                    </div>
                  )}
                </div>

                <Button
                  size="lg"
                  variant="primary"
                  onClick={handleUpload}
                  disabled={!canUpload}
                  leftIcon={Play}
                  className="w-full sm:w-auto"
                >
                  Start Processing
                </Button>

                <p className="text-sm text-gray-500 mt-4">
                  Processing typically takes 2-5 minutes depending on the number of cases.
                </p>
              </div>
            )}
          </CardBody>
        </Card>
      </div>
    </div>
  );
};