# TSX Components Backup

This directory contains all 25 .tsx files from the AI-FARM project frontend.

## Backup Created: July 3, 2025

### Source Location
Original files from: `/home/<USER>/VALO_AI-FARM_2025/frontend/src`

### File Categories

#### **Pages (6 files)** - Main Dashboard Screens
- `SurveillanceDashboard.tsx` - Main surveillance dashboard with metrics
- `UploadPage.tsx` - File upload interface for CSV/images
- `ProcessingPage.tsx` - Real-time processing status display
- `ResultsPage.tsx` - Processing results and analysis
- `ROIPage.tsx` - ROI calculations and projections
- `InsightsPage.tsx` - Data insights and analytics

#### **Components (18 files)** - Reusable UI Components

##### Dashboard Components
- `ImageGallery.tsx` - Image grid/list display with VLM results
- `ProcessingProgress.tsx` - Progress tracking with real-time updates

##### Chart Components
- `ProcessingChart.tsx` - Data visualization charts (5 different chart types)

##### Layout Components
- `Layout.tsx` - Main layout wrapper
- `Header.tsx` - Top navigation header
- `Footer.tsx` - Bottom footer
- `Navigation.tsx` - Main navigation menu

##### UI Elements (10 files)
- `SurveillanceButton.tsx` - Surveillance-themed buttons ✅ 
- `SurveillanceMetricCard.tsx` - Surveillance-themed metric cards ✅
- `Button.tsx` - Generic buttons
- `Card.tsx` - Generic cards
- `Alert.tsx` - Alert/notification components
- `MetricCard.tsx` - Generic metric display cards
- `ProgressBar.tsx` - Progress indicators
- `LoadingSpinner.tsx` - Loading animations
- `FileUpload.tsx` - File upload components

#### **App Structure (1 file)**
- `App.tsx` - Main application router and routing logic
- `index.tsx` - React application entry point

## Theme Conversion Status

✅ **Surveillance Theme Applied To:**
- All dashboard components (ImageGallery, ProcessingProgress)
- All chart components (ProcessingChart)
- All pages now use SurveillanceButton and surveillance styling

## File Sizes
Total backup size: ~236KB
Average file size: ~9.4KB per component

## Usage Notes
- All files use TypeScript (.tsx extension)
- React functional components with hooks
- Tailwind CSS for styling with custom surveillance theme
- Professional surveillance color scheme (black/dark with green accents)

## Restoration
To restore any component, copy from this backup directory back to:
`/home/<USER>/VALO_AI-FARM_2025/frontend/src/[appropriate_subdirectory]/`