import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { 
  Brain, 
  Target, 
  TrendingUp, 
  Lightbulb,
  Settings,
  Download,
  CheckCircle,
  AlertTriangle,
  Info,
  ArrowRight,
  Zap,
  DollarSign,
  Clock
} from 'lucide-react';
import { 
  Button, 
  Card, 
  CardHeader, 
  CardBody, 
  Alert,
  LoadingSpinner,
  MetricCard
} from '../components/ui';
import { AutoLearningInsights, DashboardData } from '../types';
import { metricsService } from '../services';
import toast from 'react-hot-toast';

export const InsightsPage: React.FC = () => {
  const { batchId } = useParams<{ batchId: string }>();
  const navigate = useNavigate();
  
  const [insights, setInsights] = useState<AutoLearningInsights | null>(null);
  const [dashboard, setDashboard] = useState<DashboardData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      if (!batchId) {
        navigate('/');
        return;
      }

      try {
        setLoading(true);
        const [insightsData, dashboardData] = await Promise.all([
          metricsService.getAutoLearningInsights(batchId),
          metricsService.getDashboardData(batchId),
        ]);
        
        setInsights(insightsData);
        setDashboard(dashboardData);
        setError(null);
      } catch (err) {
        // Auto-learning insights might not be available
        try {
          const dashboardData = await metricsService.getDashboardData(batchId);
          setDashboard(dashboardData);
          setInsights(null);
        } catch (dashboardErr) {
          const errorMsg = err instanceof Error ? err.message : 'Failed to load insights';
          setError(errorMsg);
        }
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [batchId, navigate]);

  const handleExportInsights = async () => {
    if (!batchId || !dashboard) return;
    
    try {
      const exportData = {
        batch_id: batchId,
        customer: dashboard.customer_info.name,
        generated_at: new Date().toISOString(),
        auto_learning_insights: insights,
        dashboard_data: dashboard,
        recommendations: getImplementationPlan(),
      };
      
      const blob = new Blob([JSON.stringify(exportData, null, 2)], {
        type: 'application/json',
      });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `ai-farm-insights-${batchId}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      
      toast.success('Insights exported successfully');
    } catch (error) {
      toast.error('Failed to export insights');
    }
  };

  const getImplementationPlan = () => {
    const baseSteps = [
      {
        phase: 'Phase 1: Planning & Preparation',
        duration: '2-3 weeks',
        tasks: [
          'Conduct technical requirements assessment',
          'Set up development and staging environments',
          'Configure data integration pipelines',
          'Establish testing protocols and success criteria',
        ],
      },
      {
        phase: 'Phase 2: System Integration',
        duration: '3-4 weeks',
        tasks: [
          'Integrate with existing safety monitoring systems',
          'Configure AI-FARM processing pipeline',
          'Set up automated data flows',
          'Implement custom threshold configurations',
        ],
      },
      {
        phase: 'Phase 3: Testing & Validation',
        duration: '2-3 weeks',
        tasks: [
          'Run parallel processing with existing system',
          'Validate accuracy against historical data',
          'Fine-tune confidence thresholds',
          'User acceptance testing with safety teams',
        ],
      },
      {
        phase: 'Phase 4: Deployment & Training',
        duration: '1-2 weeks',
        tasks: [
          'Deploy to production environment',
          'Train operations and safety teams',
          'Establish monitoring and alerting',
          'Go-live with gradual rollout',
        ],
      },
    ];

    return baseSteps;
  };

  const getOptimizationRecommendations = () => {
    const recommendations = insights?.recommendations || [];
    
    const defaultRecommendations = [
      'Consider implementing custom thresholds based on your specific environment',
      'Establish regular model retraining schedule to maintain accuracy',
      'Monitor false positive rates and adjust confidence thresholds as needed',
      'Implement feedback loops for continuous learning',
      'Consider expanding to additional safety monitoring use cases',
    ];

    return recommendations.length > 0 ? recommendations : defaultRecommendations;
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-4">Generating Insights...</h1>
            <LoadingSpinner size="lg" />
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <Card>
            <CardBody>
              <Alert variant="danger" title="Failed to Load Insights">
                {error}
              </Alert>
              <div className="mt-4 text-center space-x-4">
                <Button onClick={() => window.location.reload()}>
                  Retry
                </Button>
                <Button variant="outline" onClick={() => navigate('/')}>
                  Go Home
                </Button>
              </div>
            </CardBody>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              AI-FARM Insights & Implementation
            </h1>
            <p className="text-lg text-gray-600">
              Auto-learning insights and implementation guidance for {dashboard?.customer_info.name}
            </p>
          </div>
          
          <div className="flex space-x-4">
            <Button
              variant="outline"
              onClick={handleExportInsights}
              leftIcon={Download}
            >
              Export Report
            </Button>
          </div>
        </div>

        {/* Auto-Learning Summary */}
        {dashboard?.auto_learning_summary && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <MetricCard
              title="Patterns Detected"
              value={dashboard.auto_learning_summary.patterns_detected}
              icon={Brain}
              color="primary"
            />
            <MetricCard
              title="Accuracy Improvement"
              value={`+${dashboard.auto_learning_summary.accuracy_improvement.toFixed(1)}%`}
              icon={TrendingUp}
              color="success"
            />
            <MetricCard
              title="Thresholds Optimized"
              value={dashboard.auto_learning_summary.thresholds_optimized}
              icon={Target}
              color="warning"
            />
            <MetricCard
              title="Recommendations"
              value={dashboard.auto_learning_summary.recommendations_count}
              icon={Lightbulb}
              color="neutral"
            />
          </div>
        )}

        {/* Auto-Learning Status */}
        <Alert 
          variant={insights ? "success" : "info"} 
          title={insights ? "Auto-Learning Complete" : "Auto-Learning Analysis"}
          className="mb-8"
        >
          {insights ? (
            <div>
              AI-FARM has successfully analyzed your data patterns and generated optimized configurations. 
              The system detected {Object.keys(insights.detected_patterns).length} unique patterns and 
              improved accuracy by {insights.accuracy_improvement.toFixed(1)}%.
            </div>
          ) : (
            <div>
              Auto-learning analysis is being processed. While detailed insights are being generated, 
              you can review the implementation plan and recommendations below.
            </div>
          )}
        </Alert>

        {/* Detected Patterns */}
        {insights && (
          <Card className="mb-8">
            <CardHeader>
              <div className="flex items-center space-x-2">
                <Brain className="w-5 h-5 text-primary-600" />
                <h2 className="text-xl font-semibold text-gray-900">Detected Patterns</h2>
              </div>
              <p className="text-sm text-gray-600">
                AI-FARM identified these patterns in your safety monitoring data
              </p>
            </CardHeader>
            <CardBody>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {Object.entries(insights.detected_patterns).map(([pattern, data]) => (
                  <div key={pattern} className="bg-gray-50 rounded-lg p-4">
                    <h3 className="font-medium text-gray-900 mb-2 capitalize">
                      {pattern.replace(/_/g, ' ')}
                    </h3>
                    <p className="text-sm text-gray-600">
                      {typeof data === 'object' ? JSON.stringify(data) : String(data)}
                    </p>
                  </div>
                ))}
              </div>
            </CardBody>
          </Card>
        )}

        {/* Optimized Thresholds */}
        {insights && Object.keys(insights.optimized_thresholds).length > 0 && (
          <Card className="mb-8">
            <CardHeader>
              <div className="flex items-center space-x-2">
                <Target className="w-5 h-5 text-warning-600" />
                <h2 className="text-xl font-semibold text-gray-900">Optimized Thresholds</h2>
              </div>
              <p className="text-sm text-gray-600">
                Recommended confidence thresholds based on your data characteristics
              </p>
            </CardHeader>
            <CardBody>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {Object.entries(insights.optimized_thresholds).map(([threshold, value]) => (
                  <div key={threshold} className="text-center">
                    <div className="bg-warning-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-3">
                      <span className="text-2xl font-bold text-warning-600">{value}%</span>
                    </div>
                    <h3 className="font-medium text-gray-900 capitalize">
                      {threshold.replace(/_/g, ' ')}
                    </h3>
                  </div>
                ))}
              </div>
            </CardBody>
          </Card>
        )}

        {/* Implementation Plan */}
        <Card className="mb-8">
          <CardHeader>
            <div className="flex items-center space-x-2">
              <Settings className="w-5 h-5 text-primary-600" />
              <h2 className="text-xl font-semibold text-gray-900">Implementation Plan</h2>
            </div>
            <p className="text-sm text-gray-600">
              Recommended phases for deploying AI-FARM in your environment
            </p>
          </CardHeader>
          <CardBody>
            <div className="space-y-6">
              {getImplementationPlan().map((phase, index) => (
                <div key={index} className="border-l-4 border-primary-200 pl-6">
                  <div className="flex items-start justify-between mb-3">
                    <div>
                      <h3 className="text-lg font-medium text-gray-900">{phase.phase}</h3>
                      <p className="text-sm text-gray-500">Duration: {phase.duration}</p>
                    </div>
                    <div className="bg-primary-100 rounded-full w-8 h-8 flex items-center justify-center">
                      <span className="text-sm font-bold text-primary-600">{index + 1}</span>
                    </div>
                  </div>
                  <ul className="space-y-2">
                    {phase.tasks.map((task, taskIndex) => (
                      <li key={taskIndex} className="flex items-start space-x-2">
                        <CheckCircle className="w-4 h-4 text-success-500 mt-0.5 flex-shrink-0" />
                        <span className="text-sm text-gray-700">{task}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </CardBody>
        </Card>

        {/* Optimization Recommendations */}
        <Card className="mb-8">
          <CardHeader>
            <div className="flex items-center space-x-2">
              <Lightbulb className="w-5 h-5 text-warning-600" />
              <h2 className="text-xl font-semibold text-gray-900">Optimization Recommendations</h2>
            </div>
            <p className="text-sm text-gray-600">
              Best practices and suggestions for maximizing AI-FARM effectiveness
            </p>
          </CardHeader>
          <CardBody>
            <div className="space-y-4">
              {getOptimizationRecommendations().map((recommendation, index) => (
                <div key={index} className="flex items-start space-x-3">
                  <div className="flex-shrink-0 w-6 h-6 bg-warning-100 rounded-full flex items-center justify-center mt-0.5">
                    <span className="text-xs font-bold text-warning-600">{index + 1}</span>
                  </div>
                  <p className="text-gray-700">{recommendation}</p>
                </div>
              ))}
            </div>
          </CardBody>
        </Card>

        {/* Success Metrics */}
        {dashboard && (
          <Card className="mb-8">
            <CardHeader>
              <div className="flex items-center space-x-2">
                <TrendingUp className="w-5 h-5 text-success-600" />
                <h2 className="text-xl font-semibold text-gray-900">Expected Success Metrics</h2>
              </div>
              <p className="text-sm text-gray-600">
                Key performance indicators to track post-implementation
              </p>
            </CardHeader>
            <CardBody>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div className="text-center">
                  <div className="bg-success-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-3">
                    <Zap className="w-8 h-8 text-success-600" />
                  </div>
                  <h3 className="font-medium text-gray-900">Filter Rate</h3>
                  <p className="text-2xl font-bold text-success-600">
                    {dashboard.headline_metrics.false_positive_reduction}
                  </p>
                  <p className="text-sm text-gray-500">Target efficiency</p>
                </div>
                
                <div className="text-center">
                  <div className="bg-primary-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-3">
                    <TrendingUp className="w-8 h-8 text-primary-600" />
                  </div>
                  <h3 className="font-medium text-gray-900">ROI</h3>
                  <p className="text-2xl font-bold text-primary-600">
                    {dashboard.headline_metrics.roi_percentage}
                  </p>
                  <p className="text-sm text-gray-500">Expected return</p>
                </div>
                
                <div className="text-center">
                  <div className="bg-warning-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-3">
                    <Clock className="w-8 h-8 text-warning-600" />
                  </div>
                  <h3 className="font-medium text-gray-900">Time Savings</h3>
                  <p className="text-2xl font-bold text-warning-600">
                    {dashboard.headline_metrics.time_saved_monthly}
                  </p>
                  <p className="text-sm text-gray-500">Monthly reduction</p>
                </div>
                
                <div className="text-center">
                  <div className="bg-danger-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-3">
                    <DollarSign className="w-8 h-8 text-danger-600" />
                  </div>
                  <h3 className="font-medium text-gray-900">Cost Savings</h3>
                  <p className="text-2xl font-bold text-danger-600">
                    {dashboard.headline_metrics.annual_savings}
                  </p>
                  <p className="text-sm text-gray-500">Annual target</p>
                </div>
              </div>
            </CardBody>
          </Card>
        )}

        {/* Next Steps */}
        <Card className="bg-gradient-to-r from-primary-600 to-primary-700 text-white">
          <CardBody>
            <div className="text-center">
              <h3 className="text-2xl font-bold mb-4">Ready to Transform Your Operations?</h3>
              <p className="text-primary-100 mb-6 max-w-2xl mx-auto">
                You've seen the potential. Now let's make it reality. Our implementation team 
                is ready to help you deploy AI-FARM and achieve these results.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button
                  variant="outline"
                  size="lg"
                  className="bg-white text-primary-700 hover:bg-primary-50"
                  onClick={() => navigate(`/dashboard`)}
                  leftIcon={TrendingUp}
                >
                  View Full Dashboard
                </Button>
                <Button
                  variant="secondary"
                  size="lg"
                  className="bg-primary-700 text-white hover:bg-primary-800"
                  onClick={handleExportInsights}
                  leftIcon={Download}
                >
                  Download Implementation Guide
                </Button>
              </div>
            </div>
          </CardBody>
        </Card>
      </div>
    </div>
  );
};