import React from 'react';
import { LucideIcon } from 'lucide-react';

interface SurveillanceMetricCardProps {
  title: string;
  value: string | number;
  subtitle?: string;
  icon?: LucideIcon;
  color?: 'primary' | 'danger' | 'warning' | 'success';
  trend?: {
    value: number;
    direction: 'up' | 'down';
    label: string;
  };
}

export const SurveillanceMetricCard: React.FC<SurveillanceMetricCardProps> = ({
  title,
  value,
  subtitle,
  icon: Icon,
  color = 'primary',
  trend
}) => {
  const colorClasses = {
    primary: 'metric-primary',
    danger: 'metric-danger',
    warning: 'metric-warning',
    success: 'metric-success'
  };

  const trendColorClasses = {
    up: 'text-success-400',
    down: 'text-danger-400'
  };

  return (
    <div className={`metric-card-surveillance ${colorClasses[color]}`}>
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <p className="metric-label-surveillance">{title}</p>
          <p className={`metric-value-surveillance ${
            color === 'danger' ? 'text-danger-400' : 
            color === 'warning' ? 'text-warning-400' : 
            color === 'success' ? 'text-success-400' : ''
          }`}>
            {value}
          </p>
          {subtitle && (
            <p className="text-xs text-gray-500 mt-1">{subtitle}</p>
          )}
          {trend && (
            <div className={`flex items-center gap-1 mt-2 text-xs ${trendColorClasses[trend.direction]}`}>
              <span className="font-bold">
                {trend.direction === 'up' ? '↑' : '↓'} {trend.value}%
              </span>
              <span className="text-gray-500">{trend.label}</span>
            </div>
          )}
        </div>
        {Icon && (
          <Icon className={`w-8 h-8 opacity-50 ${
            color === 'danger' ? 'text-danger-500' : 
            color === 'warning' ? 'text-warning-500' : 
            color === 'success' ? 'text-success-500' : 
            'text-primary-500'
          }`} />
        )}
      </div>
    </div>
  );
};