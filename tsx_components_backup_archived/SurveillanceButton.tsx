import React from 'react';
import { LucideIcon } from 'lucide-react';

interface SurveillanceButtonProps {
  children: React.ReactNode;
  variant?: 'primary' | 'danger' | 'warning' | 'secondary';
  size?: 'sm' | 'md' | 'lg';
  leftIcon?: LucideIcon;
  rightIcon?: LucideIcon;
  onClick?: () => void;
  disabled?: boolean;
  className?: string;
}

export const SurveillanceButton: React.FC<SurveillanceButtonProps> = ({
  children,
  variant = 'primary',
  size = 'md',
  leftIcon: LeftIcon,
  rightIcon: RightIcon,
  onClick,
  disabled = false,
  className = ''
}) => {
  const sizeClasses = {
    sm: 'px-3 py-1.5 text-xs',
    md: 'px-4 py-2 text-sm',
    lg: 'px-6 py-3 text-base'
  };

  const variantClasses = {
    primary: 'btn-surveillance-primary',
    danger: 'btn-surveillance-danger',
    warning: 'btn-surveillance-warning',
    secondary: 'btn-surveillance bg-transparent border border-gray-600 text-gray-300 hover:bg-gray-800 hover:text-white'
  };

  return (
    <button
      onClick={onClick}
      disabled={disabled}
      className={`${variantClasses[variant]} ${sizeClasses[size]} ${className}`}
    >
      {LeftIcon && <LeftIcon className="w-4 h-4 mr-2" />}
      {children}
      {RightIcon && <RightIcon className="w-4 h-4 ml-2" />}
    </button>
  );
};