import React from 'react';
import { useNavigate } from 'react-router-dom';
import { AlertTriangle, TrendingUp, Clock, DollarSign, Users, Shield, ArrowRight } from 'lucide-react';
import { Button, Card, CardBody, MetricCard } from '../components/ui';

export const LandingPage: React.FC = () => {
  const navigate = useNavigate();

  const problemMetrics = [
    {
      title: 'False Positive Rate',
      value: '97%',
      subtitle: 'Of safety alerts are false positives',
      icon: AlertTriangle,
      color: 'danger' as const,
      trend: {
        value: 15,
        direction: 'up' as const,
        label: 'vs last year',
      },
    },
    {
      title: 'Review Time per Alert',
      value: '3 min',
      subtitle: 'Average time spent on each alert',
      icon: Clock,
      color: 'warning' as const,
    },
    {
      title: 'Monthly Alert Volume',
      value: '17,268',
      subtitle: 'Safety alerts requiring review',
      icon: Users,
      color: 'neutral' as const,
      trend: {
        value: 8,
        direction: 'up' as const,
        label: 'monthly growth',
      },
    },
    {
      title: 'Annual Review Cost',
      value: '$867K',
      subtitle: 'Cost of manual alert review',
      icon: DollarSign,
      color: 'danger' as const,
    },
  ];

  const benefits = [
    {
      icon: Shield,
      title: 'Reduce False Positives',
      description: 'AI-powered analysis identifies and filters out false positive alerts with 95%+ accuracy.',
      impact: 'Up to 70% reduction in false positives',
    },
    {
      icon: Clock,
      title: 'Save Review Time',
      description: 'Automated pre-screening reduces manual review workload and accelerates response times.',
      impact: 'Save 40+ hours per week',
    },
    {
      icon: DollarSign,
      title: 'Cut Operational Costs',
      description: 'Significant reduction in labor costs and improved resource allocation efficiency.',
      impact: '$500K+ annual savings potential',
    },
    {
      icon: TrendingUp,
      title: 'Improve Accuracy',
      description: 'Machine learning continuously improves detection accuracy and adapts to your environment.',
      impact: '95%+ confidence in recommendations',
    },
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <div className="bg-gradient-to-r from-primary-600 to-primary-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold text-white mb-6">
              AI-FARM
            </h1>
            <p className="text-xl md:text-2xl text-primary-100 mb-4">
              False Positive Alert Reduction System
            </p>
            <p className="text-lg text-primary-200 max-w-3xl mx-auto mb-8">
              Revolutionize your safety monitoring with AI-powered analysis that dramatically reduces 
              false positive alerts while maintaining the highest safety standards.
            </p>
            <Button
              size="lg"
              variant="outline"
              className="bg-white text-primary-700 hover:bg-primary-50"
              onClick={() => navigate('/upload')}
              rightIcon={ArrowRight}
            >
              Start Demo
            </Button>
          </div>
        </div>
      </div>

      {/* Problem Statement */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            The False Positive Crisis
          </h2>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            Safety monitoring systems generate thousands of alerts daily, but the vast majority are 
            false positives that waste valuable time and resources while potentially causing alert fatigue.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
          {problemMetrics.map((metric, index) => (
            <MetricCard key={index} {...metric} />
          ))}
        </div>

        {/* Impact Visualization */}
        <Card className="mb-16">
          <CardBody>
            <div className="text-center mb-8">
              <h3 className="text-2xl font-bold text-gray-900 mb-4">
                The Impact on Your Operations
              </h3>
            </div>
            
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              <div className="bg-danger-50 border border-danger-200 rounded-lg p-6">
                <h4 className="text-lg font-semibold text-danger-800 mb-4">Current State</h4>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-danger-700">Monthly alerts to review:</span>
                    <span className="font-bold text-danger-900">17,268</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-danger-700">False positives (97%):</span>
                    <span className="font-bold text-danger-900">16,750</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-danger-700">Time wasted monthly:</span>
                    <span className="font-bold text-danger-900">837 hours</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-danger-700">Annual cost:</span>
                    <span className="font-bold text-danger-900">$867,000</span>
                  </div>
                </div>
              </div>

              <div className="bg-success-50 border border-success-200 rounded-lg p-6">
                <h4 className="text-lg font-semibold text-success-800 mb-4">With AI-FARM</h4>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-success-700">Alerts requiring review:</span>
                    <span className="font-bold text-success-900">5,180</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-success-700">False positives filtered:</span>
                    <span className="font-bold text-success-900">12,088 (70%)</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-success-700">Time saved monthly:</span>
                    <span className="font-bold text-success-900">603 hours</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-success-700">Annual savings:</span>
                    <span className="font-bold text-success-900">$625,000</span>
                  </div>
                </div>
              </div>
            </div>
          </CardBody>
        </Card>
      </div>

      {/* Benefits Section */}
      <div className="bg-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Transform Your Safety Operations
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              AI-FARM leverages advanced computer vision and machine learning to intelligently 
              analyze safety alerts and dramatically reduce false positives.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {benefits.map((benefit, index) => (
              <Card key={index} hover className="h-full">
                <CardBody>
                  <div className="flex items-start space-x-4">
                    <div className="flex-shrink-0">
                      <div className="flex items-center justify-center w-12 h-12 rounded-lg bg-primary-100">
                        <benefit.icon className="w-6 h-6 text-primary-600" />
                      </div>
                    </div>
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold text-gray-900 mb-2">
                        {benefit.title}
                      </h3>
                      <p className="text-gray-600 mb-3">
                        {benefit.description}
                      </p>
                      <div className="bg-primary-50 border border-primary-200 rounded-lg p-3">
                        <p className="text-sm font-medium text-primary-800">
                          {benefit.impact}
                        </p>
                      </div>
                    </div>
                  </div>
                </CardBody>
              </Card>
            ))}
          </div>
        </div>
      </div>

      {/* How It Works */}
      <div className="bg-gray-50 py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              How AI-FARM Works
            </h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="bg-primary-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-primary-600">1</span>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Upload Your Data</h3>
              <p className="text-gray-600">
                Upload your CSV case data and violation images to start the analysis process.
              </p>
            </div>

            <div className="text-center">
              <div className="bg-primary-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-primary-600">2</span>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">AI Analysis</h3>
              <p className="text-gray-600">
                Advanced computer vision analyzes each image and provides confidence-based recommendations.
              </p>
            </div>

            <div className="text-center">
              <div className="bg-primary-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-primary-600">3</span>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Review Results</h3>
              <p className="text-gray-600">
                Get actionable insights, ROI calculations, and filtered results to improve your operations.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* CTA Section */}
      <div className="bg-primary-600 py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold text-white mb-4">
            Ready to Transform Your Safety Operations?
          </h2>
          <p className="text-xl text-primary-100 mb-8 max-w-2xl mx-auto">
            Experience the power of AI-FARM with your own data. See how much time and money 
            you can save with intelligent false positive reduction.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button
              size="lg"
              variant="outline"
              className="bg-white text-primary-700 hover:bg-primary-50"
              onClick={() => navigate('/upload')}
              rightIcon={ArrowRight}
            >
              Start Your Demo
            </Button>
            <Button
              size="lg"
              variant="secondary"
              className="bg-primary-700 text-white hover:bg-primary-800"
              onClick={() => navigate('/dashboard')}
            >
              View Sample Results
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};