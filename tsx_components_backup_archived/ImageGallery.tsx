import React, { useState } from 'react';
import { SurveillanceButton } from '../ui';
import { Eye, Download, Filter, Grid, List, ExternalLink, X } from 'lucide-react';
import clsx from 'clsx';
import { ImageGalleryItem, VLMRecommendation } from '../../types';

// Utility functions for surveillance theme
const getConfidenceColor = (confidence?: number) => {
  if (!confidence) return 'text-gray-500';
  if (confidence >= 0.8) return 'text-success-400';
  if (confidence >= 0.6) return 'text-warning-400';
  return 'text-danger-400';
};

const getRecommendationBadge = (recommendation?: string) => {
  if (!recommendation) return null;
  
  const badgeClass = recommendation === 'DISMISS_ALERT' 
    ? 'status-false-positive' 
    : 'status-pending-review';
  
  const text = recommendation === 'DISMISS_ALERT' ? 'Dismiss Alert' : 'Requires Review';
  
  return (
    <span className={badgeClass}>
      {text}
    </span>
  );
};

export interface ImageGalleryProps {
  items: ImageGalleryItem[];
  title?: string;
  viewMode?: 'grid' | 'list';
  showFilters?: boolean;
  onItemClick?: (item: ImageGalleryItem) => void;
  onDownload?: (item: ImageGalleryItem) => void;
  className?: string;
}

export const ImageGallery: React.FC<ImageGalleryProps> = ({
  items,
  title = 'Image Gallery',
  viewMode: initialViewMode = 'grid',
  showFilters = true,
  onItemClick,
  onDownload,
  className,
}) => {
  const [viewMode, setViewMode] = useState(initialViewMode);
  const [filterCategory, setFilterCategory] = useState<'all' | 'filtered' | 'review_required'>('all');
  const [sortBy, setSortBy] = useState<'confidence' | 'case_number' | 'processing_time'>('confidence');

  const filteredItems = items.filter(item => {
    if (filterCategory === 'all') return true;
    return item.category === filterCategory;
  });

  const sortedItems = [...filteredItems].sort((a, b) => {
    switch (sortBy) {
      case 'confidence':
        return (b.confidence || 0) - (a.confidence || 0);
      case 'case_number':
        return (a.caseNumber || '').localeCompare(b.caseNumber || '');
      case 'processing_time':
        return (b.processingTime || 0) - (a.processingTime || 0);
      default:
        return 0;
    }
  });

  const GridView = () => (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
      {sortedItems.map((item) => (
        <div key={item.id} className="group relative">
          <div className="aspect-w-1 aspect-h-1 bg-dark-100 rounded-lg overflow-hidden border border-gray-800">
            <img
              src={item.src}
              alt={item.alt}
              className="w-full h-full object-cover group-hover:opacity-75 transition-opacity"
              loading="lazy"
            />
            
            {/* Overlay on hover */}
            <div className="absolute inset-0 bg-black bg-opacity-70 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center space-x-2">
              <SurveillanceButton
                variant="primary"
                size="sm"
                leftIcon={Eye}
                onClick={() => onItemClick?.(item)}
              >
                View
              </SurveillanceButton>
              {onDownload && (
                <SurveillanceButton
                  variant="secondary"
                  size="sm"
                  leftIcon={Download}
                  onClick={() => onDownload(item)}
                >
                  Download
                </SurveillanceButton>
              )}
            </div>
          </div>
          
          {/* Image metadata */}
          <div className="mt-3 space-y-2">
            <div className="flex items-center justify-between">
              <p className="text-sm font-medium text-gray-100 truncate font-mono">
                {item.caseNumber || 'Unknown Case'}
              </p>
              {getRecommendationBadge(item.recommendation)}
            </div>
            
            {item.confidence && (
              <div className="flex items-center justify-between text-xs">
                <span className="text-gray-500">Confidence:</span>
                <span className={clsx('font-medium font-mono', getConfidenceColor(item.confidence))}>
                  {(item.confidence * 100).toFixed(1)}%
                </span>
              </div>
            )}
            
            {item.processingTime && (
              <div className="flex items-center justify-between text-xs">
                <span className="text-gray-500">Processing:</span>
                <span className="text-gray-300 font-mono">{item.processingTime}ms</span>
              </div>
            )}
          </div>
        </div>
      ))}
    </div>
  );

  const ListView = () => (
    <div className="space-y-4">
      {sortedItems.map((item) => (
        <div key={item.id} className="flex items-center space-x-4 p-4 bg-dark-50 border border-gray-800 rounded-lg hover:bg-dark-100 transition-colors">
          <div className="flex-shrink-0">
            <img
              src={item.src}
              alt={item.alt}
              className="w-16 h-16 object-cover rounded-lg border border-gray-700"
              loading="lazy"
            />
          </div>
          
          <div className="flex-1 min-w-0">
            <div className="flex items-center justify-between">
              <p className="text-sm font-medium text-gray-100 truncate font-mono">
                {item.caseNumber || 'Unknown Case'}
              </p>
              {getRecommendationBadge(item.recommendation)}
            </div>
            
            <div className="mt-1 grid grid-cols-2 gap-4 text-xs text-gray-500">
              {item.confidence && (
                <div>
                  <span>Confidence: </span>
                  <span className={clsx('font-mono', getConfidenceColor(item.confidence))}>
                    {(item.confidence * 100).toFixed(1)}%
                  </span>
                </div>
              )}
              {item.processingTime && (
                <div className="font-mono">
                  Processing: {item.processingTime}ms
                </div>
              )}
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <SurveillanceButton
              variant="primary"
              size="sm"
              leftIcon={Eye}
              onClick={() => onItemClick?.(item)}
            >
              View
            </SurveillanceButton>
            {onDownload && (
              <SurveillanceButton
                variant="secondary"
                size="sm"
                leftIcon={Download}
                onClick={() => onDownload(item)}
              >
                Download
              </SurveillanceButton>
            )}
          </div>
        </div>
      ))}
    </div>
  );

  return (
    <div className={clsx('surveillance-card', className)}>
      <div className="surveillance-card-header">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-medium text-gray-100">{title}</h3>
            <p className="text-sm text-gray-400">
              {sortedItems.length} of {items.length} images
            </p>
          </div>
          
          <div className="flex items-center space-x-2">
            {/* View mode toggle */}
            <div className="flex items-center bg-dark-100 rounded-lg p-1">
              <button
                onClick={() => setViewMode('grid')}
                className={clsx(
                  'p-1.5 rounded transition-colors',
                  viewMode === 'grid' ? 'bg-primary-600 text-white shadow-sm' : 'text-gray-400 hover:bg-dark-200'
                )}
              >
                <Grid className="w-4 h-4" />
              </button>
              <button
                onClick={() => setViewMode('list')}
                className={clsx(
                  'p-1.5 rounded transition-colors',
                  viewMode === 'list' ? 'bg-primary-600 text-white shadow-sm' : 'text-gray-400 hover:bg-dark-200'
                )}
              >
                <List className="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>

        {/* Filters */}
        {showFilters && (
          <div className="flex flex-wrap items-center gap-4 mt-4">
            <div className="flex items-center space-x-2">
              <Filter className="w-4 h-4 text-gray-400" />
              <select
                value={filterCategory}
                onChange={(e) => setFilterCategory(e.target.value as any)}
                className="text-sm bg-dark-100 border border-gray-700 text-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              >
                <option value="all">All Images</option>
                <option value="filtered">Filtered (False Positives)</option>
                <option value="review_required">Review Required</option>
              </select>
            </div>
            
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as any)}
              className="text-sm bg-dark-100 border border-gray-700 text-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            >
              <option value="confidence">Sort by Confidence</option>
              <option value="case_number">Sort by Case Number</option>
              <option value="processing_time">Sort by Processing Time</option>
            </select>
          </div>
        )}
      </div>

      <div className="surveillance-card-body">
        {sortedItems.length === 0 ? (
          <div className="text-center py-12">
            <div className="text-gray-500 mb-4">
              <Grid className="w-12 h-12 mx-auto" />
            </div>
            <p className="text-gray-400">No images found matching your filters</p>
          </div>
        ) : (
          <>
            {viewMode === 'grid' ? <GridView /> : <ListView />}
          </>
        )}
      </div>
    </div>
  );
};

export interface ImageModalProps {
  item: ImageGalleryItem | null;
  isOpen: boolean;
  onClose: () => void;
  onPrevious?: () => void;
  onNext?: () => void;
}

export const ImageModal: React.FC<ImageModalProps> = ({
  item,
  isOpen,
  onClose,
  onPrevious,
  onNext,
}) => {
  if (!isOpen || !item) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-90">
      <div className="relative max-w-4xl max-h-screen p-4">
        <div className="surveillance-card overflow-hidden">
          <div className="flex items-center justify-between p-4 border-b border-gray-800">
            <h3 className="text-lg font-medium text-gray-100">
              {item.caseNumber || 'Image Details'}
            </h3>
            <SurveillanceButton variant="secondary" size="sm" onClick={onClose}>
              <X className="w-4 h-4" />
            </SurveillanceButton>
          </div>
          
          <div className="p-4">
            <img
              src={item.src}
              alt={item.alt}
              className="max-w-full max-h-96 mx-auto object-contain border border-gray-700 rounded"
            />
            
            <div className="mt-4 grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="font-medium text-gray-300">Case Number:</span>
                <span className="ml-2 text-gray-100 font-mono">{item.caseNumber || 'Unknown'}</span>
              </div>
              
              {item.confidence && (
                <div>
                  <span className="font-medium text-gray-300">Confidence:</span>
                  <span className={clsx('ml-2 font-mono', getConfidenceColor(item.confidence))}>
                    {(item.confidence * 100).toFixed(1)}%
                  </span>
                </div>
              )}
              
              <div>
                <span className="font-medium text-gray-300">Category:</span>
                <span className="ml-2 text-gray-100 capitalize">{item.category.replace('_', ' ')}</span>
              </div>
              
              {item.processingTime && (
                <div>
                  <span className="font-medium text-gray-300">Processing Time:</span>
                  <span className="ml-2 text-gray-100 font-mono">{item.processingTime}ms</span>
                </div>
              )}
            </div>
            
            {item.recommendation && (
              <div className="mt-4">
                <span className="font-medium text-gray-300">Recommendation:</span>
                <span className="ml-2">{getRecommendationBadge(item.recommendation)}</span>
              </div>
            )}
          </div>
          
          <div className="flex items-center justify-between p-4 border-t border-gray-800">
            <div className="flex space-x-2">
              {onPrevious && (
                <SurveillanceButton variant="secondary" size="sm" onClick={onPrevious}>
                  Previous
                </SurveillanceButton>
              )}
              {onNext && (
                <SurveillanceButton variant="secondary" size="sm" onClick={onNext}>
                  Next
                </SurveillanceButton>
              )}
            </div>
            
            <SurveillanceButton
              variant="primary"
              size="sm"
              leftIcon={ExternalLink}
              onClick={() => window.open(item.src, '_blank')}
            >
              Open in New Tab
            </SurveillanceButton>
          </div>
        </div>
      </div>
    </div>
  );
};