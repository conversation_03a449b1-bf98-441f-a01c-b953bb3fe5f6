import React from 'react';
import clsx from 'clsx';

export interface ProgressBarProps {
  value: number; // 0-100
  max?: number;
  variant?: 'primary' | 'success' | 'warning' | 'danger';
  size?: 'sm' | 'md' | 'lg';
  showPercentage?: boolean;
  label?: string;
  animated?: boolean;
  striped?: boolean;
  className?: string;
}

export const ProgressBar: React.FC<ProgressBarProps> = ({
  value,
  max = 100,
  variant = 'primary',
  size = 'md',
  showPercentage = false,
  label,
  animated = false,
  striped = false,
  className,
}) => {
  const percentage = Math.min(Math.max((value / max) * 100, 0), 100);
  
  const heightClasses = {
    sm: 'h-2',
    md: 'h-3',
    lg: 'h-4',
  };
  
  const variantClasses = {
    primary: 'progress-fill',
    success: 'progress-fill-success',
    warning: 'progress-fill-warning',
    danger: 'progress-fill-danger',
  };
  
  return (
    <div className={clsx('space-y-2', className)}>
      {(label || showPercentage) && (
        <div className="flex justify-between items-center text-sm">
          {label && <span className="font-medium text-gray-700">{label}</span>}
          {showPercentage && (
            <span className="text-gray-500">{Math.round(percentage)}%</span>
          )}
        </div>
      )}
      
      <div className={clsx('progress-bar', heightClasses[size])}>
        <div
          className={clsx(
            variantClasses[variant],
            {
              'animate-pulse-slow': animated,
              'bg-gradient-to-r': striped,
            }
          )}
          style={{ width: `${percentage}%` }}
          role="progressbar"
          aria-valuenow={value}
          aria-valuemin={0}
          aria-valuemax={max}
          aria-label={label}
        />
      </div>
    </div>
  );
};

export interface MultiProgressBarProps {
  segments: Array<{
    value: number;
    variant?: 'primary' | 'success' | 'warning' | 'danger';
    label?: string;
  }>;
  max?: number;
  size?: 'sm' | 'md' | 'lg';
  showLegend?: boolean;
  className?: string;
}

export const MultiProgressBar: React.FC<MultiProgressBarProps> = ({
  segments,
  max = 100,
  size = 'md',
  showLegend = false,
  className,
}) => {
  const total = segments.reduce((sum, segment) => sum + segment.value, 0);
  
  const heightClasses = {
    sm: 'h-2',
    md: 'h-3',
    lg: 'h-4',
  };
  
  const variantClasses = {
    primary: 'bg-primary-600',
    success: 'bg-success-600',
    warning: 'bg-warning-600',
    danger: 'bg-danger-600',
  };
  
  const variantLegendClasses = {
    primary: 'bg-primary-600',
    success: 'bg-success-600',
    warning: 'bg-warning-600',
    danger: 'bg-danger-600',
  };
  
  return (
    <div className={clsx('space-y-2', className)}>
      <div className={clsx('progress-bar', heightClasses[size])}>
        {segments.map((segment, index) => {
          const percentage = Math.min(Math.max((segment.value / max) * 100, 0), 100);
          return (
            <div
              key={index}
              className={clsx(
                'h-full transition-all duration-300 ease-out',
                variantClasses[segment.variant || 'primary'],
                {
                  'rounded-l-full': index === 0,
                  'rounded-r-full': index === segments.length - 1,
                }
              )}
              style={{ width: `${percentage}%` }}
            />
          );
        })}
      </div>
      
      {showLegend && (
        <div className="flex flex-wrap gap-4 text-sm">
          {segments.map((segment, index) => (
            <div key={index} className="flex items-center space-x-2">
              <div
                className={clsx(
                  'w-3 h-3 rounded-full',
                  variantLegendClasses[segment.variant || 'primary']
                )}
              />
              <span className="text-gray-700">
                {segment.label || `Segment ${index + 1}`}: {segment.value}
              </span>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};