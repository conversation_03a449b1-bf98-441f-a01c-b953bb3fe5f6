import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { Layout } from './components/layout/Layout';
import { 
  LandingPage, 
  UploadPage, 
  ProcessingPage, 
  ResultsPage, 
  ROIPage, 
  InsightsPage,
  SurveillanceDashboard 
} from './pages/index';
import './App.css';

function App() {
  return (
    <Router>
      <Layout>
        <Routes>
          <Route path="/" element={<SurveillanceDashboard />} />
          <Route path="/home" element={<LandingPage />} />
          <Route path="/upload" element={<UploadPage />} />
          <Route path="/processing" element={<ProcessingPage />} />
          <Route path="/dashboard" element={<SurveillanceDashboard />} />
          <Route path="/results" element={<ResultsPage />} />
          <Route path="/roi" element={<ROIPage />} />
          <Route path="/insights" element={<InsightsPage />} />
        </Routes>
      </Layout>
    </Router>
  );
}

export default App;