#!/bin/bash

echo "🚨 URGENT: Nextcloud AIO Recovery"
echo "================================="
echo

# First, check if volumes still exist
echo "1. Checking for existing Nextcloud volumes..."
sudo docker volume ls | grep -i nextcloud

echo
echo "2. Checking for any remaining Nextcloud containers..."
sudo docker ps -a | grep -i nextcloud

echo
echo "3. Starting Nextcloud AIO Master Container..."
echo "   This will recreate your Nextcloud setup"

# Standard Nextcloud AIO recovery command
sudo docker run \
--init \
--sig-proxy=false \
--name nextcloud-aio-mastercontainer \
--restart always \
--publish 8080:8080 \
--publish 8443:8443 \
--volume nextcloud_aio_mastercontainer:/mnt/docker-aio-config \
--volume /var/run/docker.sock:/var/run/docker.sock:ro \
nextcloud/all-in-one:latest

echo
echo "After this runs, visit: https://localhost:8443"
echo "Your data should be preserved in the volumes!"