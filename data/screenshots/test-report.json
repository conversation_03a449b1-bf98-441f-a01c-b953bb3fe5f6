{"timestamp": "2025-07-04T07:11:28.018Z", "consoleLogs": ["[info] %cDownload the React DevTools for a better development experience: https://reactjs.org/link/react-devtools font-weight:bold", "[warn] ⚠️ React Router Future Flag Warning: React Router will begin wrapping state updates in `React.startTransition` in v7. You can use the `v7_startTransition` future flag to opt-in early. For more information, see https://reactrouter.com/v6/upgrading/future#v7_starttransition.", "[warn] ⚠️ React Router Future Flag Warning: Relative route resolution within Splat routes is changing in v7. You can use the `v7_relativeSplatPath` future flag to opt-in early. For more information, see https://reactrouter.com/v6/upgrading/future#v7_relativesplatpath.", "[error] Failed to load resource: the server responded with a status of 404 (Not Found)", "[error] Failed to load resource: the server responded with a status of 404 (Not Found)", "[error] Error while trying to use the following icon from the Manifest: http://localhost:3000/logo192.png (Download error or resource isn't a valid image)", "[info] %cDownload the React DevTools for a better development experience: https://reactjs.org/link/react-devtools font-weight:bold", "[warn] ⚠️ React Router Future Flag Warning: React Router will begin wrapping state updates in `React.startTransition` in v7. You can use the `v7_startTransition` future flag to opt-in early. For more information, see https://reactrouter.com/v6/upgrading/future#v7_starttransition.", "[warn] ⚠️ React Router Future Flag Warning: Relative route resolution within Splat routes is changing in v7. You can use the `v7_relativeSplatPath` future flag to opt-in early. For more information, see https://reactrouter.com/v6/upgrading/future#v7_relativesplatpath.", "[error] Access to XMLHttpRequest at 'http://localhost:8000/api/v1/data-analysis/metrics' from origin 'http://localhost:3000' has been blocked by CORS policy: Response to preflight request doesn't pass access control check: No 'Access-Control-Allow-Origin' header is present on the requested resource.", "[error] Failed to load resource: net::ERR_FAILED", "[error] Access to XMLHttpRequest at 'http://localhost:8000/api/v1/data-analysis/camera-analysis' from origin 'http://localhost:3000' has been blocked by CORS policy: Response to preflight request doesn't pass access control check: No 'Access-Control-Allow-Origin' header is present on the requested resource.", "[error] Failed to load resource: net::ERR_FAILED", "[error] Access to XMLHttpRequest at 'http://localhost:8000/api/v1/data-analysis/metrics' from origin 'http://localhost:3000' has been blocked by CORS policy: Response to preflight request doesn't pass access control check: No 'Access-Control-Allow-Origin' header is present on the requested resource.", "[error] Failed to load resource: net::ERR_FAILED", "[error] Access to XMLHttpRequest at 'http://localhost:8000/api/v1/data-analysis/false-positive-patterns' from origin 'http://localhost:3000' has been blocked by CORS policy: Response to preflight request doesn't pass access control check: No 'Access-Control-Allow-Origin' header is present on the requested resource.", "[error] Failed to load resource: net::ERR_FAILED", "[error] Access to XMLHttpRequest at 'http://localhost:8000/api/v1/data-analysis/camera-analysis' from origin 'http://localhost:3000' has been blocked by CORS policy: Response to preflight request doesn't pass access control check: No 'Access-Control-Allow-Origin' header is present on the requested resource.", "[error] Failed to load resource: net::ERR_FAILED", "[error] Access to XMLHttpRequest at 'http://localhost:8000/api/v1/data-analysis/false-positive-patterns' from origin 'http://localhost:3000' has been blocked by CORS policy: Response to preflight request doesn't pass access control check: No 'Access-Control-Allow-Origin' header is present on the requested resource.", "[error] Failed to load resource: net::ERR_FAILED", "[error] Failed to load resource: the server responded with a status of 404 (Not Found)", "[error] Error while trying to use the following icon from the Manifest: http://localhost:3000/logo192.png (Download error or resource isn't a valid image)", "[error] Access to fetch at 'http://localhost:8000/health' from origin 'http://localhost:3000' has been blocked by CORS policy: No 'Access-Control-Allow-Origin' header is present on the requested resource.", "[error] Failed to load resource: net::ERR_FAILED"], "errors": [], "summary": {"consoleLogCount": 25, "errorCount": 0, "screenshotsTaken": 2}}