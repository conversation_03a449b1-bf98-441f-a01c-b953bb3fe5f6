#!/usr/bin/env python3
"""
Show a real VLM request/response from our testing
"""

import json
import base64
import requests

# Case V1250627134 - Crane structure example
def show_real_example():
    print("REAL VLM REQUEST/RESPONSE EXAMPLE")
    print("="*60)
    print("\nCase: V1250627134 - Crane Structure")
    print("Expected: FALSE POSITIVE (it's just equipment, not a person)")
    
    # The actual prompt we send
    prompt = """SAFETY VIOLATION DETECTION SYSTEM

ANALYZE BOTH PROVIDED IMAGES.

STEP 1: What is in the CROPPED image?
A) STRUCTURE ONLY (needs >90% confidence)
B) PERSON (any human features)  
C) UNCLEAR

STEP 2: Apply these EXACT rules:

IF STRUCTURE ONLY (>90% confident):
→ Output: FALSE POSITIVE: YES

IF PERSON DETECTED:
→ Check for ANY of these violations:
  • Missing helmet
  • Missing vest  
  • Vest not fastened
  • Mobile phone use
  • Missing required equipment
  • Wrong location
  • Unsafe behavior
  
  IF ANY VIOLATION FOUND:
  → Output: FALSE POSITIVE: NO
  
  IF NO VIOLATIONS:
  → Output: FALSE POSITIVE: YES

IF UNCLEAR:
→ Output: FALSE POSITIVE: NO (safety first)

IMPORTANT: The question is "Is this a FALSE POSITIVE?"
- Real violations must output: FALSE POSITIVE: NO
- Only non-violations output: FALSE POSITIVE: YES

OUTPUT FORMAT (use exactly):
FALSE POSITIVE: [YES or NO]
Detection: [STRUCTURE/PERSON/UNCLEAR]
Violations: [List any found, or "None"]"""

    # API Request structure
    print("\n1. API REQUEST:")
    print("-"*40)
    
    request_structure = {
        "url": "http://**************:9500/v1/chat/completions",
        "method": "POST",
        "headers": {
            "Content-Type": "application/json"
        },
        "body": {
            "model": "VLM-38B-AWQ",
            "messages": [{
                "role": "user",
                "content": [
                    {"type": "text", "text": "[The prompt above]"},
                    {"type": "text", "text": "\n\nSOURCE IMAGE:"},
                    {"type": "image_url", "image_url": {"url": "data:image/jpeg;base64,[base64_encoded_source_image]"}},
                    {"type": "text", "text": "\n\nCROPPED IMAGE:"},
                    {"type": "image_url", "image_url": {"url": "data:image/jpeg;base64,[base64_encoded_cropped_image]"}}
                ]
            }],
            "temperature": 0.1,
            "max_tokens": 200
        }
    }
    
    print(json.dumps(request_structure, indent=2))
    
    # Actual VLM response
    print("\n2. VLM RESPONSE:")
    print("-"*40)
    
    actual_response = """FALSE POSITIVE: YES
Detection: STRUCTURE
Violations: None"""
    
    print(actual_response)
    
    print("\n3. RESPONSE INTERPRETATION:")
    print("-"*40)
    print("✓ Correctly identified as STRUCTURE")
    print("✓ No violations possible (it's equipment)")
    print("✓ Marked as FALSE POSITIVE")
    print("✓ Result: This alert can be filtered out")
    
    # Another example - Mobile phone violation
    print("\n\n" + "="*60)
    print("Case: V1250627208 - Mobile Phone Use")
    print("Expected: VALID VIOLATION (real safety issue)")
    
    print("\nVLM RESPONSE:")
    print("-"*40)
    
    mobile_response = """FALSE POSITIVE: NO
Detection: PERSON
Violations: Mobile phone use"""
    
    print(mobile_response)
    
    print("\n3. RESPONSE INTERPRETATION:")
    print("-"*40)
    print("✓ Correctly identified as PERSON")
    print("✓ Detected behavioral violation (mobile phone)")
    print("✓ NOT marked as false positive")
    print("✓ Result: This is a real violation - alert safety team!")
    
    print("\n\n4. HOW THE SYSTEM WORKS:")
    print("-"*40)
    print("1. System analyzes both source (wide view) and cropped (zoomed) images")
    print("2. First determines what's in the image (structure/person/unclear)")
    print("3. If person, checks for violations")
    print("4. Makes safety-first decision")
    print("5. Returns structured response for easy parsing")
    
    print("\n5. PARSING THE RESPONSE IN CODE:")
    print("-"*40)
    print("""
# Parse VLM response
vlm_response = "FALSE POSITIVE: YES\\nDetection: STRUCTURE\\nViolations: None"

# Extract decision
is_false_positive = "YES" in vlm_response.split("FALSE POSITIVE:")[1][:5]
print(f"Is false positive: {is_false_positive}")  # True

# Extract detection type
for line in vlm_response.split('\\n'):
    if 'Detection:' in line:
        detection = line.split('Detection:')[1].strip()
        print(f"Detected: {detection}")  # STRUCTURE

# Decision logic
if is_false_positive:
    print("→ Filter out this alert")
else:
    print("→ Alert safety team!")
""")

if __name__ == "__main__":
    show_real_example()