#!/usr/bin/env python3
"""
Debug why test seems stuck
"""

import os
import json
import psutil
import time
from datetime import datetime

def debug_status():
    print(f"\nDEBUG STATUS CHECK - {datetime.now().strftime('%H:%M:%S')}")
    print("="*60)
    
    # Check process
    test_running = False
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            if 'test_multiple_approaches' in ' '.join(proc.info['cmdline'] or []):
                test_running = True
                print(f"✓ Process running: PID {proc.info['pid']}")
                print(f"  CPU: {proc.cpu_percent(interval=1)}%")
                print(f"  Memory: {proc.memory_info().rss / 1024 / 1024:.1f} MB")
                print(f"  Status: {proc.status()}")
                print(f"  Running for: {(time.time() - proc.create_time()) / 60:.1f} minutes")
        except:
            pass
    
    if not test_running:
        print("✗ Test process NOT found!")
    
    # Check files
    print("\nFile Status:")
    files_to_check = [
        'multi_approach_progress.json',
        'comprehensive_test_log.txt',
        'valo_batch_round3_complete.json'
    ]
    
    for file in files_to_check:
        if os.path.exists(file):
            stat = os.stat(file)
            mod_time = datetime.fromtimestamp(stat.st_mtime)
            size = stat.st_size
            print(f"  {file}: {size:,} bytes, modified {mod_time.strftime('%H:%M:%S')}")
        else:
            print(f"  {file}: NOT FOUND")
    
    # Check last VLM endpoint connectivity
    print("\nVLM Endpoint Check:")
    import requests
    try:
        response = requests.get("http://100.106.127.35:9500/", timeout=5)
        print(f"  Status: {response.status_code}")
    except Exception as e:
        print(f"  ERROR: {str(e)}")
    
    # Possible issues
    print("\nPossible Issues:")
    print("1. VLM endpoint might be down or slow")
    print("2. Process might be stuck on network timeout")
    print("3. File I/O issue")
    
    print("\nRecommendation:")
    print("If test has been running >2 hours with no progress,")
    print("consider killing and restarting with simpler test first.")

if __name__ == "__main__":
    debug_status()