#!/usr/bin/env python3
"""
Analyze what was detected in false positive cases based on human remarks from CSV
"""

import pandas as pd
import re
from collections import defaultdict, Counter
import json

# Read the CSV file
csv_path = 'ai_farm_images_fixed_250703/psa_valo_violation_report_without_images_csv_2025-07-03_14-14-01.CSV'
df = pd.read_csv(csv_path)

print(f"Total cases in CSV: {len(df)}")

# Filter for false positives (Invalid alerts)
false_positives = df[df['Alert Status'] == 'Invalid'].copy()
print(f"Total false positives: {len(false_positives)}")

# Analyze the remarks
remarks_analysis = defaultdict(int)
structure_types = defaultdict(int)
ppe_issues = defaultdict(int)
violation_types_fp = defaultdict(int)

# Pattern categories
patterns = {
    'STRUCTURES': {
        'crane_structure': r'CRANE STRUCTURE|crane structure',
        'vessel_structure': r'VESSEL STRUCTURE|vessel structure|ship',
        'spreader_structure': r'SPREADER|spreader',
        'pm_structure': r'PM STRUCTURE|pm structure',
        'wharf_structure': r'WHARF STRUCTURE|wharf structure',
        'lashing_equipment': r'LASHING TURNBUCKLE|lashing bar|Lashing Bar',
        'tree_structure': r'Tree STRUCTURE|tree structure',
        'barge_structure': r'Barge structure|barge structure',
        'container': r'Container Captured|container captured'
    },
    'PEOPLE_WITH_PPE': {
        'full_ppe': r'in full PPE|Full PPE|FULL PPE|proper PPE',
        'with_lifejacket': r'with life ?jacket|with lanyard',
        'with_tlad': r'with T-?LAD|TLAD'
    },
    'NO_VIOLATION': {
        'not_doing_lashing': r'not doing lashing|LS not doing lashing',
        'two_ls_working': r'2 LS|Both LS',
        'standing_by': r'standing by|standing at working bay',
        'no_exception': r'No exception|no exception',
        'doing_other_work': r'doing preparation|housekeeping|spreader ride|conning|deconning'
    },
    'TECHNICAL_ISSUES': {
        'no_footage': r'No camera footage|No video footage',
        'not_clear': r'not clear|image not clear',
        'wrongly_captured': r'wrongly captured|captured as LS'
    },
    'CREW_TYPES': {
        'ship_crew': r'SHIP CREW|Ship crew',
        'pmd': r'PMD|PM DRIVER|PM driver',
        'wos': r'WOS|Charlie',
        'wta': r'WTA',
        'technician': r'Technician'
    }
}

# Analyze each false positive
for idx, row in false_positives.iterrows():
    remark = str(row['Remarks']).lower() if pd.notna(row['Remarks']) else ''
    violation_type = row['Type of Infringement']
    
    # Count violation types in false positives
    violation_types_fp[violation_type] += 1
    
    # Categorize the remark
    categorized = False
    for category, subcategories in patterns.items():
        for subcat_name, pattern in subcategories.items():
            if re.search(pattern, remark, re.IGNORECASE):
                remarks_analysis[f"{category}:{subcat_name}"] += 1
                categorized = True
                
                # Special tracking for structure types
                if category == 'STRUCTURES':
                    structure_types[subcat_name] += 1
                elif category == 'PEOPLE_WITH_PPE':
                    ppe_issues[subcat_name] += 1
    
    if not categorized and remark:
        remarks_analysis['OTHER:uncategorized'] += 1

# Print analysis results
print("\n" + "="*80)
print("FALSE POSITIVE ANALYSIS REPORT")
print("="*80)

print("\n1. VIOLATION TYPES IN FALSE POSITIVES:")
print("-"*40)
for vtype, count in sorted(violation_types_fp.items(), key=lambda x: x[1], reverse=True):
    percentage = (count / len(false_positives)) * 100
    print(f"  {vtype}: {count} ({percentage:.1f}%)")

print("\n2. WHAT WAS DETECTED INSTEAD OF VIOLATIONS:")
print("-"*40)

# Group by main categories
main_categories = defaultdict(int)
for key, count in remarks_analysis.items():
    category = key.split(':')[0]
    main_categories[category] += count

for category, total in sorted(main_categories.items(), key=lambda x: x[1], reverse=True):
    percentage = (total / len(false_positives)) * 100
    print(f"\n{category}: {total} cases ({percentage:.1f}%)")
    
    # Show subcategories
    for key, count in sorted(remarks_analysis.items(), key=lambda x: x[1], reverse=True):
        if key.startswith(category + ':'):
            subcat = key.split(':')[1]
            sub_percentage = (count / len(false_positives)) * 100
            print(f"  - {subcat}: {count} ({sub_percentage:.1f}%)")

print("\n3. DETAILED STRUCTURE MISIDENTIFICATION:")
print("-"*40)
total_structures = sum(structure_types.values())
print(f"Total structure-related false positives: {total_structures}")
for struct_type, count in sorted(structure_types.items(), key=lambda x: x[1], reverse=True):
    percentage = (count / total_structures) * 100 if total_structures > 0 else 0
    print(f"  - {struct_type}: {count} ({percentage:.1f}% of structures)")

print("\n4. PEOPLE WITH PROPER PPE (FALSE POSITIVES):")
print("-"*40)
total_ppe = sum(ppe_issues.values())
print(f"Total false positives with people in proper PPE: {total_ppe}")
for ppe_type, count in sorted(ppe_issues.items(), key=lambda x: x[1], reverse=True):
    percentage = (count / total_ppe) * 100 if total_ppe > 0 else 0
    print(f"  - {ppe_type}: {count} ({percentage:.1f}% of PPE cases)")

print("\n5. SUMMARY INSIGHTS:")
print("-"*40)

# Calculate key metrics
structure_fp = sum(1 for key, _ in remarks_analysis.items() if key.startswith('STRUCTURES:'))
people_ppe_fp = sum(1 for key, _ in remarks_analysis.items() if key.startswith('PEOPLE_WITH_PPE:'))
no_violation_fp = sum(1 for key, _ in remarks_analysis.items() if key.startswith('NO_VIOLATION:'))
technical_fp = sum(1 for key, _ in remarks_analysis.items() if key.startswith('TECHNICAL_ISSUES:'))

print(f"• Structures misidentified as people: {structure_fp} ({structure_fp/len(false_positives)*100:.1f}%)")
print(f"• People with proper PPE flagged: {people_ppe_fp} ({people_ppe_fp/len(false_positives)*100:.1f}%)")
print(f"• No actual violation occurring: {no_violation_fp} ({no_violation_fp/len(false_positives)*100:.1f}%)")
print(f"• Technical/camera issues: {technical_fp} ({technical_fp/len(false_positives)*100:.1f}%)")

# Sample remarks for each major category
print("\n6. EXAMPLE REMARKS BY CATEGORY:")
print("-"*40)

examples = {
    'CRANE STRUCTURE': [],
    'VESSEL STRUCTURE': [],
    'FULL PPE': [],
    'NOT DOING LASHING': []
}

for idx, row in false_positives.iterrows():
    remark = str(row['Remarks']) if pd.notna(row['Remarks']) else ''
    
    if 'CRANE STRUCTURE' in remark and len(examples['CRANE STRUCTURE']) < 3:
        examples['CRANE STRUCTURE'].append(f"Case {row['Case Int. ID']}: {remark}")
    elif 'VESSEL STRUCTURE' in remark and len(examples['VESSEL STRUCTURE']) < 3:
        examples['VESSEL STRUCTURE'].append(f"Case {row['Case Int. ID']}: {remark}")
    elif 'full PPE' in remark and len(examples['FULL PPE']) < 3:
        examples['FULL PPE'].append(f"Case {row['Case Int. ID']}: {remark}")
    elif 'not doing lashing' in remark and len(examples['NOT DOING LASHING']) < 3:
        examples['NOT DOING LASHING'].append(f"Case {row['Case Int. ID']}: {remark}")

for category, example_list in examples.items():
    if example_list:
        print(f"\n{category} Examples:")
        for example in example_list:
            print(f"  • {example}")

# Save detailed analysis
analysis_data = {
    'total_cases': len(df),
    'total_false_positives': len(false_positives),
    'violation_types_in_fp': dict(violation_types_fp),
    'detection_categories': dict(main_categories),
    'structure_types': dict(structure_types),
    'ppe_issues': dict(ppe_issues),
    'detailed_patterns': dict(remarks_analysis)
}

with open('false_positive_pattern_analysis.json', 'w') as f:
    json.dump(analysis_data, f, indent=2)

print("\n" + "="*80)
print("Analysis saved to: false_positive_pattern_analysis.json")
print("="*80)