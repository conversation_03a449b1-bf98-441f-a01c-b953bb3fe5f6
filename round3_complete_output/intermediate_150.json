[{"case_number": "V1250623121", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623121_cropped_invalid.JPEG", "source_image": "", "terminal": "P2", "camera_id": "QC605 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS in full PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623122", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623122_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623122_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC519 WOS Cam (C) (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Technician Captured as Ls", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623123", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623123_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623123_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC320 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "WOS IN FULL PPE AT WHARF", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623124", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623124_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623124_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC109F (VALO)", "infringement_type": "STA Double-up", "alert_status": "Invalid", "remarks": "STA guiding ops  no exception", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623125", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623125_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623125_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC108F (VALO)", "infringement_type": "One man Lashing", "alert_status": "Invalid", "remarks": "LS in full PPE doing spreader ride", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623126", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623126_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623126_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC108F (VALO)", "infringement_type": "One man Lashing", "alert_status": "Invalid", "remarks": "STA guiding ops  no exception", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623127", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623127_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623127_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC506 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "PM structure captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623128", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623128_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623128_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC106F (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS in full PPE doing spreader ride", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623129", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623129_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623129_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC108F (VALO)", "infringement_type": "One man Lashing", "alert_status": "Invalid", "remarks": "STA guiding ops  no exception", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623130", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623130_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623130_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC108F (VALO)", "infringement_type": "One man Lashing", "alert_status": "Invalid", "remarks": "No exception in observation", "is_false_positive": true, "vlm_decision": "dismissed", "vlm_response": "DISMISS", "confidence": 0.85}, {"case_number": "V1250623131", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623131_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623131_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC108F (VALO)", "infringement_type": "One man Lashing", "alert_status": "Invalid", "remarks": "No exception in observation", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623132", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623132_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623132_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC108F (VALO)", "infringement_type": "One man Lashing", "alert_status": "Invalid", "remarks": "STA guiding ops  no exception", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623133", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623133_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623133_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC108F (VALO)", "infringement_type": "One man Lashing", "alert_status": "Invalid", "remarks": "STA guiding ops  no exception", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623134", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623134_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623134_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC108F (VALO)", "infringement_type": "One man Lashing", "alert_status": "Invalid", "remarks": "STA guiding ops  no exception", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623135", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623135_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623135_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC614F (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "VESSEL STRUCTURE CAPTURED  AS LS", "is_false_positive": true, "vlm_decision": "dismissed", "vlm_response": "DISMISS", "confidence": 0.85}, {"case_number": "V1250623136", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623136_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623136_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC108F (VALO)", "infringement_type": "One man Lashing", "alert_status": "Invalid", "remarks": "No exception in observation", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623137", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/valid/V1250623137_cropped_valid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/valid/V1250623137_source_valid.JPEG", "terminal": "P3", "camera_id": "QC523 WOS Cam (C) (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "<PERSON><PERSON>", "remarks": "WOS without STOP & GO bat", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW - Valid violation", "confidence": 0.95}, {"case_number": "V1250623138", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623138_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623138_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC109F (VALO)", "infringement_type": "One man Lashing", "alert_status": "Invalid", "remarks": "No exception in observation", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623139", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623139_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623139_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC320 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS in full PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623140", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623140_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623140_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC109F (VALO)", "infringement_type": "One man Lashing", "alert_status": "Invalid", "remarks": "No exception in observation", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623141", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623141_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623141_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC523 WOS Cam (C) (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS Full PPE at wharf.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623142", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623142_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623142_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC614F (VALO)", "infringement_type": "2-Container Distance", "alert_status": "Invalid", "remarks": "VESSEL STRUCTURE CAPTURED  AS LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623143", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623143_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623143_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC614F (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "VESSEL STRUCTURE CAPTURED  AS LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623144", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/valid/V1250623144_cropped_valid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/valid/V1250623144_source_valid.JPEG", "terminal": "P2", "camera_id": "QC320 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "<PERSON><PERSON>", "remarks": "Van drivers walk down without helmet and safety vest", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW - Valid violation", "confidence": 0.95}, {"case_number": "V1250623145", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623145_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623145_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC504F (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Crane structure captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623146", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623146_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623146_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC601 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS in full PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623147", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623147_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623147_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC605 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS in full PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623148", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623148_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623148_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC313F (VALO)", "infringement_type": "Ex.Row Violation", "alert_status": "Invalid", "remarks": "No video footage", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623149", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623149_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623149_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC313F (VALO)", "infringement_type": "One man Lashing", "alert_status": "Invalid", "remarks": "No video footage", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623150", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623150_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623150_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC313F (VALO)", "infringement_type": "Ex.Row Violation", "alert_status": "Invalid", "remarks": "LS full ppe doing 2 man lashing with lanyard hook", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623151", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623151_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623151_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC313F (VALO)", "infringement_type": "One man Lashing", "alert_status": "Invalid", "remarks": "LS doing 2 man lashing with lanyard hook", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623152", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623152_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623152_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC313F (VALO)", "infringement_type": "Ex.Row Violation", "alert_status": "Invalid", "remarks": "LS doing 2 man lashing with lanyard hook", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623153", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623153_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623153_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC320 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS in full PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623154", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623154_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623154_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC307F (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "VESSEL STRUCTURE CAPTURED  AS LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623155", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623155_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623155_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC605 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS in full PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623156", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623156_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623156_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC307F (VALO)", "infringement_type": "STA Double-up", "alert_status": "Invalid", "remarks": "Vessel structure captured as LS.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623157", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623157_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623157_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC307F (VALO)", "infringement_type": "2-Container Distance", "alert_status": "Invalid", "remarks": "Vessel structure & crane structure  captured AS LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623158", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/valid/V1250623158_cropped_valid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/valid/V1250623158_source_valid.JPEG", "terminal": "P2", "camera_id": "QC320 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "<PERSON><PERSON>", "remarks": "A consignee did not fasten up his safety vest at wharf area at QC320 zero lane", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW - Valid violation", "confidence": 0.95}, {"case_number": "V1250623159", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623159_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623159_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC108F (VALO)", "infringement_type": "One man Lashing", "alert_status": "Invalid", "remarks": "Vessel structure wrongly captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623160", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623160_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623160_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC108F (VALO)", "infringement_type": "Ex.Row Violation", "alert_status": "Invalid", "remarks": "Vessel structure wrongly captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623161", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623161_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623161_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC528 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "PM Driver Full PPE At Wharf.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623162", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623162_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623162_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC605 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "WOS / PMD IN FULL PPE AT WHARF", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623163", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623163_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623163_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC320 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "FM lorry capture as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623164", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623164_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623164_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC605 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS in full PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623165", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623165_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623165_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC601 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS in full PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623166", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623166_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623166_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC507F (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Vessel structure captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623167", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623167_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623167_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC507F (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Vessel structure captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623168", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623168_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623168_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC512 WOS Cam (C) (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "PM structure captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623169", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623169_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623169_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC510 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "PM structure captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623170", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623170_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623170_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC601 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS in full PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623171", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623171_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623171_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC320 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "SHIP SUPPLIER CAPTURED AS LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623172", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623172_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623172_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC611 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS in full PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623173", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623173_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623173_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC308F (VALO)", "infringement_type": "One man Lashing", "alert_status": "Invalid", "remarks": "VESSEL STRUCTURE CAPTURED  AS LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623174", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/valid/V1250623174_cropped_valid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/valid/V1250623174_source_valid.JPEG", "terminal": "P2", "camera_id": "QC601 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "<PERSON><PERSON>", "remarks": "2 consignee standing within the yellow chevron gantry path at QC601", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW - Valid violation", "confidence": 0.95}, {"case_number": "V1250623175", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623175_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623175_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC320 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "SHIP SUPPLIER CAPTURED AS LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623176", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623176_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623176_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC519 WOS Cam (C) (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "PM structure captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623177", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623177_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623177_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC320 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "SHIP SUPPLIER CAPTURED AS LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623178", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623178_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623178_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC605 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Container Structure Captured As Ls", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623179", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623179_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623179_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC601 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "PM STRUCTURE CAPTURED  AS LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623180", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623180_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623180_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC601 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS in full PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623181", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623181_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623181_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC510 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "WOS in proper PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623182", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623182_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623182_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC528 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "PM structure captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623183", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623183_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623183_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC605 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Container Structure Captured As Ls", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623184", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623184_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623184_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC320 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS in full PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623185", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623185_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623185_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC320 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LSUP IN FULL PPE AT WHARF", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623186", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623186_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623186_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC109F (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS in proper PPE when taking spreader ride", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623187", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623187_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623187_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC531F", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Vessel structure captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623188", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623188_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623188_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC531F", "infringement_type": "One man Lashing", "alert_status": "Invalid", "remarks": "Vessel structure captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623189", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623189_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623189_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC605 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Container Structure Captured As Ls", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623190", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623190_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623190_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC510 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS Full PPE at wharf.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624001", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624001_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624001_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC531F", "infringement_type": "One man Lashing", "alert_status": "Invalid", "remarks": "Vessel structure captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624002", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624002_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624002_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC510 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS Full PPE at wharf.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624003", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624003_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624003_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC320 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS in full PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624004", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624004_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624004_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC531F", "infringement_type": "One man Lashing", "alert_status": "Invalid", "remarks": "Vessel structure captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624005", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624005_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624005_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC108F (VALO)", "infringement_type": "2-Container Distance", "alert_status": "Invalid", "remarks": "LS taking spreader ride to container top", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624006", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624006_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624006_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC510 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS Full PPE at wharf.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624007", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624007_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624007_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC531F", "infringement_type": "One man Lashing", "alert_status": "Invalid", "remarks": "Vessel structure captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624008", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624008_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624008_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC512 WOS Cam (C) (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "WOS in proper PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624009", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624009_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624009_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC605 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "QCO in full PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624010", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624010_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624010_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC531F", "infringement_type": "One man Lashing", "alert_status": "Invalid", "remarks": "Vessel structure captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624011", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624011_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624011_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC510 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS Full PPE at wharf.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624012", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624012_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624012_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC601 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "WOS IN FULL PPE AT WHARF", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624013", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624013_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624013_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC510 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS Full PPE at wharf.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624014", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624014_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624014_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC601 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS in full PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624015", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624015_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624015_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC531F", "infringement_type": "One man Lashing", "alert_status": "Invalid", "remarks": "Vessel structure captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624016", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624016_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624016_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC531F", "infringement_type": "One man Lashing", "alert_status": "Invalid", "remarks": "Vessel structure captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624017", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624017_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624017_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC531F", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Vessel structure captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624018", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624018_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624018_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC531F", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Vessel structure captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624019", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624019_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624019_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC510 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS Full PPE at wharf.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624020", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624020_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624020_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC320 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS in full PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624021", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624021_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624021_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC531F", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Vessel structure captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624022", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624022_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624022_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC531F", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Vessel structure captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624023", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624023_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624023_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC506 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Crane structure captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624024", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624024_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624024_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC531F", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Vessel structure captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624025", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624025_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624025_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC601 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Forklift structure captured as LS.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624026", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624026_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624026_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC601 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS in full PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624027", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624027_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624027_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC510 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Forklift captured as LS.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624028", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624028_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624028_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC530F (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Container captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624029", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624029_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624029_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC531F", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Vessel structure captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624030", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624030_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624030_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC601 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS in full PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624031", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624031_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624031_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC531F", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Vessel structure captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624032", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624032_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624032_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC531F", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Vessel structure captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624033", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624033_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624033_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC109F (VALO)", "infringement_type": "One man Lashing", "alert_status": "Invalid", "remarks": "STA wrongly captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624034", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624034_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624034_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC601 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS in full PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624035", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624035_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624035_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC531F", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Vessel structure captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624036", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624036_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624036_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC320 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "PM STRUCTURE CAPTURED  AS LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624037", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624037_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624037_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC531F", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Vessel structure captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624038", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624038_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624038_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC531F", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Vessel structure captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624039", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624039_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624039_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC531F", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Vessel structure captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624040", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624040_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624040_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC109F (VALO)", "infringement_type": "One man Lashing", "alert_status": "Invalid", "remarks": "Vessel structure wrongly captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624041", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624041_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624041_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC510 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "WOS in proper PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624042", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624042_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624042_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC109F (VALO)", "infringement_type": "Ex.Row Violation", "alert_status": "Invalid", "remarks": "STA wrongly captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624043", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624043_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624043_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC109F (VALO)", "infringement_type": "One man Lashing", "alert_status": "Invalid", "remarks": "No LS doing lashing", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624044", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624044_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624044_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC601 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS in full PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624045", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624045_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624045_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC109F (VALO)", "infringement_type": "STA Double-up", "alert_status": "Invalid", "remarks": "Vessel structure wrongly captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624046", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/valid/V1250624046_cropped_valid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/valid/V1250624046_source_valid.JPEG", "terminal": "P3", "camera_id": "QC506 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "<PERSON><PERSON>", "remarks": "A group of unidentified personnel without putting on any PPE walking at the wharf operational area towards the gangway of the vessel.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW - Valid violation", "confidence": 0.95}, {"case_number": "V1250624047", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624047_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624047_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC109F (VALO)", "infringement_type": "One man Lashing", "alert_status": "Invalid", "remarks": "STA wrongly captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624048", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624048_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624048_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC109F (VALO)", "infringement_type": "One man Lashing", "alert_status": "Invalid", "remarks": "Vessel structure wrongly captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624049", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624049_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624049_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC109F (VALO)", "infringement_type": "Ex.Row Violation", "alert_status": "Invalid", "remarks": "No camera footage", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624050", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624050_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624050_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC109F (VALO)", "infringement_type": "One man Lashing", "alert_status": "Invalid", "remarks": "No camera footage", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624051", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624051_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624051_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC601 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "WOS IN FULL PPE AT WHARF", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624052", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624052_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624052_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC530F (VALO)", "infringement_type": "One man Lashing", "alert_status": "Invalid", "remarks": "Vessel structure captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624053", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624053_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624053_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC530F (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Vessel structure captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624054", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624054_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624054_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC318F (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "VESSEL STRUCTURE CAPTURED  AS LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624055", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624055_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624055_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC506 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Spreader structure captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624056", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624056_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624056_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC601 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "WOS IN FULL PPE AT WHARF", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624057", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624057_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624057_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC104F (VALO)", "infringement_type": "2-Container Distance", "alert_status": "Invalid", "remarks": "No spreader operation or movement", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624058", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624058_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624058_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC601 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "WOS IN FULL PPE AT WHARF", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624059", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624059_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624059_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC601 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "WOS IN FULL PPE AT WHARF", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624060", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624060_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624060_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC320 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS in full PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624061", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624061_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624061_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC601 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "WOS IN FULL PPE AT WHARF", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624062", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624062_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624062_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC523 WOS Cam (C) (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS Full PPE at wharf.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624063", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624063_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624063_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC519 WOS Cam (C) (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Crane structure captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624064", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624064_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624064_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC523 WOS Cam (C) (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "WOS in proper PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624065", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624065_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624065_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC605 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "WOS IN FULL PPE AT WHARF", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624066", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624066_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624066_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC601 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "QCO in proper PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624067", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624067_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624067_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC320 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Ship supplier captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624068", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624068_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624068_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC601 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "PM Driver Full PPE At Wharf.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624069", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/valid/V1250624069_cropped_valid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/valid/V1250624069_source_valid.JPEG", "terminal": "P3", "camera_id": "QC523 WOS Cam (C) (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "<PERSON><PERSON>", "remarks": "PM driver was not wearing safety helmet while at the wharf area during change shift", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW - Valid violation", "confidence": 0.95}, {"case_number": "V1250624070", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624070_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624070_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC104F (VALO)", "infringement_type": "One man Lashing", "alert_status": "Invalid", "remarks": "STA wrongly captured as LS doing one man lashing", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624071", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/valid/V1250624071_cropped_valid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/valid/V1250624071_source_valid.JPEG", "terminal": "P3", "camera_id": "QC528 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "<PERSON><PERSON>", "remarks": "The workers didn't follow 3 point contact when descending from the lorry.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW - Valid violation", "confidence": 0.95}, {"case_number": "V1250624072", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624072_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624072_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC510 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Charlie Full PPE At Wharf Guiding QCO Hatch Cover Ops", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624073", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624073_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624073_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC320 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS in full PPE at wharf and waiting the ship crew to fix the safety net before LS onboard vessel.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624074", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624074_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624074_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC104F (VALO)", "infringement_type": "Ex.Row Violation", "alert_status": "Invalid", "remarks": "No LS at the extreme row doing lashing", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624075", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624075_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624075_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC519 WOS Cam (C) (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "WOS in proper PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624076", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624076_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624076_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC510 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "<PERSON> Full PPE at wharf.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624077", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624077_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624077_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC320 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "PM STRUCTURE CAPTURED AS LS AND LS WITH FULL PPE MOVING AT WORKING BAY.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624078", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624078_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624078_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC507F (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "wharf side captured the image", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624079", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624079_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624079_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC526F (VALO)", "infringement_type": "2-Container Distance", "alert_status": "Invalid", "remarks": "⦁\tLS in full PPE  life jacket and TLAD doing container top unlocking", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624080", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624080_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624080_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC602F (VALO)", "infringement_type": "One man Lashing", "alert_status": "Invalid", "remarks": "VESSEL STRUCTURE CAPTURED  AS LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}]