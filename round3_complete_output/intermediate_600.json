[{"case_number": "V1250623121", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623121_cropped_invalid.JPEG", "source_image": "", "terminal": "P2", "camera_id": "QC605 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS in full PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623122", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623122_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623122_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC519 WOS Cam (C) (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Technician Captured as Ls", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623123", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623123_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623123_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC320 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "WOS IN FULL PPE AT WHARF", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623124", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623124_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623124_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC109F (VALO)", "infringement_type": "STA Double-up", "alert_status": "Invalid", "remarks": "STA guiding ops  no exception", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623125", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623125_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623125_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC108F (VALO)", "infringement_type": "One man Lashing", "alert_status": "Invalid", "remarks": "LS in full PPE doing spreader ride", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623126", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623126_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623126_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC108F (VALO)", "infringement_type": "One man Lashing", "alert_status": "Invalid", "remarks": "STA guiding ops  no exception", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623127", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623127_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623127_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC506 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "PM structure captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623128", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623128_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623128_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC106F (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS in full PPE doing spreader ride", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623129", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623129_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623129_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC108F (VALO)", "infringement_type": "One man Lashing", "alert_status": "Invalid", "remarks": "STA guiding ops  no exception", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623130", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623130_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623130_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC108F (VALO)", "infringement_type": "One man Lashing", "alert_status": "Invalid", "remarks": "No exception in observation", "is_false_positive": true, "vlm_decision": "dismissed", "vlm_response": "DISMISS", "confidence": 0.85}, {"case_number": "V1250623131", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623131_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623131_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC108F (VALO)", "infringement_type": "One man Lashing", "alert_status": "Invalid", "remarks": "No exception in observation", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623132", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623132_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623132_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC108F (VALO)", "infringement_type": "One man Lashing", "alert_status": "Invalid", "remarks": "STA guiding ops  no exception", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623133", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623133_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623133_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC108F (VALO)", "infringement_type": "One man Lashing", "alert_status": "Invalid", "remarks": "STA guiding ops  no exception", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623134", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623134_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623134_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC108F (VALO)", "infringement_type": "One man Lashing", "alert_status": "Invalid", "remarks": "STA guiding ops  no exception", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623135", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623135_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623135_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC614F (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "VESSEL STRUCTURE CAPTURED  AS LS", "is_false_positive": true, "vlm_decision": "dismissed", "vlm_response": "DISMISS", "confidence": 0.85}, {"case_number": "V1250623136", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623136_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623136_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC108F (VALO)", "infringement_type": "One man Lashing", "alert_status": "Invalid", "remarks": "No exception in observation", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623137", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/valid/V1250623137_cropped_valid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/valid/V1250623137_source_valid.JPEG", "terminal": "P3", "camera_id": "QC523 WOS Cam (C) (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "<PERSON><PERSON>", "remarks": "WOS without STOP & GO bat", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW - Valid violation", "confidence": 0.95}, {"case_number": "V1250623138", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623138_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623138_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC109F (VALO)", "infringement_type": "One man Lashing", "alert_status": "Invalid", "remarks": "No exception in observation", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623139", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623139_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623139_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC320 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS in full PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623140", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623140_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623140_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC109F (VALO)", "infringement_type": "One man Lashing", "alert_status": "Invalid", "remarks": "No exception in observation", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623141", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623141_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623141_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC523 WOS Cam (C) (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS Full PPE at wharf.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623142", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623142_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623142_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC614F (VALO)", "infringement_type": "2-Container Distance", "alert_status": "Invalid", "remarks": "VESSEL STRUCTURE CAPTURED  AS LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623143", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623143_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623143_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC614F (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "VESSEL STRUCTURE CAPTURED  AS LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623144", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/valid/V1250623144_cropped_valid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/valid/V1250623144_source_valid.JPEG", "terminal": "P2", "camera_id": "QC320 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "<PERSON><PERSON>", "remarks": "Van drivers walk down without helmet and safety vest", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW - Valid violation", "confidence": 0.95}, {"case_number": "V1250623145", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623145_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623145_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC504F (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Crane structure captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623146", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623146_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623146_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC601 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS in full PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623147", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623147_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623147_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC605 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS in full PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623148", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623148_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623148_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC313F (VALO)", "infringement_type": "Ex.Row Violation", "alert_status": "Invalid", "remarks": "No video footage", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623149", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623149_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623149_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC313F (VALO)", "infringement_type": "One man Lashing", "alert_status": "Invalid", "remarks": "No video footage", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623150", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623150_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623150_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC313F (VALO)", "infringement_type": "Ex.Row Violation", "alert_status": "Invalid", "remarks": "LS full ppe doing 2 man lashing with lanyard hook", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623151", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623151_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623151_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC313F (VALO)", "infringement_type": "One man Lashing", "alert_status": "Invalid", "remarks": "LS doing 2 man lashing with lanyard hook", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623152", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623152_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623152_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC313F (VALO)", "infringement_type": "Ex.Row Violation", "alert_status": "Invalid", "remarks": "LS doing 2 man lashing with lanyard hook", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623153", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623153_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623153_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC320 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS in full PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623154", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623154_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623154_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC307F (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "VESSEL STRUCTURE CAPTURED  AS LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623155", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623155_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623155_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC605 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS in full PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623156", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623156_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623156_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC307F (VALO)", "infringement_type": "STA Double-up", "alert_status": "Invalid", "remarks": "Vessel structure captured as LS.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623157", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623157_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623157_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC307F (VALO)", "infringement_type": "2-Container Distance", "alert_status": "Invalid", "remarks": "Vessel structure & crane structure  captured AS LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623158", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/valid/V1250623158_cropped_valid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/valid/V1250623158_source_valid.JPEG", "terminal": "P2", "camera_id": "QC320 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "<PERSON><PERSON>", "remarks": "A consignee did not fasten up his safety vest at wharf area at QC320 zero lane", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW - Valid violation", "confidence": 0.95}, {"case_number": "V1250623159", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623159_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623159_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC108F (VALO)", "infringement_type": "One man Lashing", "alert_status": "Invalid", "remarks": "Vessel structure wrongly captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623160", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623160_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623160_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC108F (VALO)", "infringement_type": "Ex.Row Violation", "alert_status": "Invalid", "remarks": "Vessel structure wrongly captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623161", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623161_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623161_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC528 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "PM Driver Full PPE At Wharf.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623162", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623162_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623162_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC605 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "WOS / PMD IN FULL PPE AT WHARF", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623163", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623163_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623163_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC320 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "FM lorry capture as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623164", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623164_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623164_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC605 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS in full PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623165", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623165_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623165_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC601 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS in full PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623166", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623166_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623166_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC507F (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Vessel structure captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623167", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623167_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623167_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC507F (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Vessel structure captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623168", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623168_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623168_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC512 WOS Cam (C) (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "PM structure captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623169", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623169_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623169_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC510 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "PM structure captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623170", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623170_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623170_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC601 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS in full PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623171", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623171_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623171_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC320 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "SHIP SUPPLIER CAPTURED AS LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623172", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623172_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623172_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC611 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS in full PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623173", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623173_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623173_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC308F (VALO)", "infringement_type": "One man Lashing", "alert_status": "Invalid", "remarks": "VESSEL STRUCTURE CAPTURED  AS LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623174", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/valid/V1250623174_cropped_valid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/valid/V1250623174_source_valid.JPEG", "terminal": "P2", "camera_id": "QC601 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "<PERSON><PERSON>", "remarks": "2 consignee standing within the yellow chevron gantry path at QC601", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW - Valid violation", "confidence": 0.95}, {"case_number": "V1250623175", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623175_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623175_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC320 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "SHIP SUPPLIER CAPTURED AS LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623176", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623176_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623176_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC519 WOS Cam (C) (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "PM structure captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623177", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623177_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623177_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC320 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "SHIP SUPPLIER CAPTURED AS LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623178", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623178_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623178_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC605 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Container Structure Captured As Ls", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623179", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623179_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623179_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC601 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "PM STRUCTURE CAPTURED  AS LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623180", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623180_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623180_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC601 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS in full PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623181", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623181_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623181_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC510 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "WOS in proper PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623182", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623182_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623182_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC528 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "PM structure captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623183", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623183_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623183_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC605 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Container Structure Captured As Ls", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623184", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623184_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623184_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC320 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS in full PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623185", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623185_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623185_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC320 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LSUP IN FULL PPE AT WHARF", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623186", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623186_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623186_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC109F (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS in proper PPE when taking spreader ride", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623187", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623187_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623187_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC531F", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Vessel structure captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623188", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623188_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623188_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC531F", "infringement_type": "One man Lashing", "alert_status": "Invalid", "remarks": "Vessel structure captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623189", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623189_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623189_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC605 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Container Structure Captured As Ls", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250623190", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623190_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250623190_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC510 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS Full PPE at wharf.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624001", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624001_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624001_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC531F", "infringement_type": "One man Lashing", "alert_status": "Invalid", "remarks": "Vessel structure captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624002", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624002_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624002_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC510 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS Full PPE at wharf.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624003", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624003_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624003_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC320 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS in full PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624004", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624004_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624004_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC531F", "infringement_type": "One man Lashing", "alert_status": "Invalid", "remarks": "Vessel structure captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624005", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624005_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624005_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC108F (VALO)", "infringement_type": "2-Container Distance", "alert_status": "Invalid", "remarks": "LS taking spreader ride to container top", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624006", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624006_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624006_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC510 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS Full PPE at wharf.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624007", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624007_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624007_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC531F", "infringement_type": "One man Lashing", "alert_status": "Invalid", "remarks": "Vessel structure captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624008", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624008_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624008_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC512 WOS Cam (C) (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "WOS in proper PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624009", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624009_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624009_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC605 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "QCO in full PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624010", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624010_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624010_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC531F", "infringement_type": "One man Lashing", "alert_status": "Invalid", "remarks": "Vessel structure captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624011", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624011_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624011_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC510 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS Full PPE at wharf.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624012", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624012_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624012_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC601 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "WOS IN FULL PPE AT WHARF", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624013", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624013_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624013_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC510 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS Full PPE at wharf.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624014", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624014_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624014_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC601 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS in full PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624015", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624015_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624015_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC531F", "infringement_type": "One man Lashing", "alert_status": "Invalid", "remarks": "Vessel structure captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624016", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624016_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624016_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC531F", "infringement_type": "One man Lashing", "alert_status": "Invalid", "remarks": "Vessel structure captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624017", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624017_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624017_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC531F", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Vessel structure captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624018", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624018_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624018_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC531F", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Vessel structure captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624019", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624019_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624019_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC510 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS Full PPE at wharf.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624020", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624020_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624020_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC320 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS in full PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624021", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624021_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624021_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC531F", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Vessel structure captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624022", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624022_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624022_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC531F", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Vessel structure captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624023", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624023_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624023_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC506 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Crane structure captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624024", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624024_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624024_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC531F", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Vessel structure captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624025", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624025_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624025_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC601 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Forklift structure captured as LS.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624026", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624026_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624026_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC601 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS in full PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624027", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624027_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624027_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC510 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Forklift captured as LS.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624028", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624028_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624028_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC530F (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Container captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624029", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624029_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624029_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC531F", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Vessel structure captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624030", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624030_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624030_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC601 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS in full PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624031", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624031_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624031_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC531F", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Vessel structure captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624032", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624032_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624032_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC531F", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Vessel structure captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624033", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624033_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624033_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC109F (VALO)", "infringement_type": "One man Lashing", "alert_status": "Invalid", "remarks": "STA wrongly captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624034", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624034_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624034_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC601 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS in full PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624035", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624035_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624035_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC531F", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Vessel structure captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624036", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624036_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624036_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC320 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "PM STRUCTURE CAPTURED  AS LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624037", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624037_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624037_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC531F", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Vessel structure captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624038", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624038_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624038_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC531F", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Vessel structure captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624039", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624039_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624039_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC531F", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Vessel structure captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624040", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624040_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624040_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC109F (VALO)", "infringement_type": "One man Lashing", "alert_status": "Invalid", "remarks": "Vessel structure wrongly captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624041", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624041_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624041_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC510 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "WOS in proper PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624042", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624042_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624042_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC109F (VALO)", "infringement_type": "Ex.Row Violation", "alert_status": "Invalid", "remarks": "STA wrongly captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624043", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624043_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624043_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC109F (VALO)", "infringement_type": "One man Lashing", "alert_status": "Invalid", "remarks": "No LS doing lashing", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624044", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624044_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624044_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC601 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS in full PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624045", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624045_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624045_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC109F (VALO)", "infringement_type": "STA Double-up", "alert_status": "Invalid", "remarks": "Vessel structure wrongly captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624046", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/valid/V1250624046_cropped_valid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/valid/V1250624046_source_valid.JPEG", "terminal": "P3", "camera_id": "QC506 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "<PERSON><PERSON>", "remarks": "A group of unidentified personnel without putting on any PPE walking at the wharf operational area towards the gangway of the vessel.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW - Valid violation", "confidence": 0.95}, {"case_number": "V1250624047", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624047_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624047_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC109F (VALO)", "infringement_type": "One man Lashing", "alert_status": "Invalid", "remarks": "STA wrongly captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624048", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624048_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624048_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC109F (VALO)", "infringement_type": "One man Lashing", "alert_status": "Invalid", "remarks": "Vessel structure wrongly captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624049", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624049_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624049_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC109F (VALO)", "infringement_type": "Ex.Row Violation", "alert_status": "Invalid", "remarks": "No camera footage", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624050", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624050_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624050_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC109F (VALO)", "infringement_type": "One man Lashing", "alert_status": "Invalid", "remarks": "No camera footage", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624051", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624051_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624051_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC601 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "WOS IN FULL PPE AT WHARF", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624052", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624052_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624052_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC530F (VALO)", "infringement_type": "One man Lashing", "alert_status": "Invalid", "remarks": "Vessel structure captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624053", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624053_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624053_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC530F (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Vessel structure captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624054", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624054_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624054_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC318F (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "VESSEL STRUCTURE CAPTURED  AS LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624055", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624055_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624055_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC506 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Spreader structure captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624056", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624056_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624056_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC601 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "WOS IN FULL PPE AT WHARF", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624057", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624057_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624057_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC104F (VALO)", "infringement_type": "2-Container Distance", "alert_status": "Invalid", "remarks": "No spreader operation or movement", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624058", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624058_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624058_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC601 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "WOS IN FULL PPE AT WHARF", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624059", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624059_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624059_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC601 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "WOS IN FULL PPE AT WHARF", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624060", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624060_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624060_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC320 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS in full PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624061", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624061_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624061_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC601 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "WOS IN FULL PPE AT WHARF", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624062", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624062_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624062_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC523 WOS Cam (C) (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS Full PPE at wharf.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624063", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624063_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624063_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC519 WOS Cam (C) (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Crane structure captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624064", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624064_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624064_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC523 WOS Cam (C) (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "WOS in proper PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624065", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624065_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624065_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC605 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "WOS IN FULL PPE AT WHARF", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624066", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624066_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624066_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC601 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "QCO in proper PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624067", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624067_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624067_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC320 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Ship supplier captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624068", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624068_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624068_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC601 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "PM Driver Full PPE At Wharf.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624069", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/valid/V1250624069_cropped_valid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/valid/V1250624069_source_valid.JPEG", "terminal": "P3", "camera_id": "QC523 WOS Cam (C) (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "<PERSON><PERSON>", "remarks": "PM driver was not wearing safety helmet while at the wharf area during change shift", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW - Valid violation", "confidence": 0.95}, {"case_number": "V1250624070", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624070_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624070_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC104F (VALO)", "infringement_type": "One man Lashing", "alert_status": "Invalid", "remarks": "STA wrongly captured as LS doing one man lashing", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624071", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/valid/V1250624071_cropped_valid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/valid/V1250624071_source_valid.JPEG", "terminal": "P3", "camera_id": "QC528 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "<PERSON><PERSON>", "remarks": "The workers didn't follow 3 point contact when descending from the lorry.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW - Valid violation", "confidence": 0.95}, {"case_number": "V1250624072", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624072_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624072_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC510 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Charlie Full PPE At Wharf Guiding QCO Hatch Cover Ops", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624073", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624073_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624073_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC320 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS in full PPE at wharf and waiting the ship crew to fix the safety net before LS onboard vessel.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624074", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624074_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624074_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC104F (VALO)", "infringement_type": "Ex.Row Violation", "alert_status": "Invalid", "remarks": "No LS at the extreme row doing lashing", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624075", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624075_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624075_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC519 WOS Cam (C) (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "WOS in proper PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624076", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624076_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624076_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC510 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "<PERSON> Full PPE at wharf.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624077", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624077_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624077_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC320 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "PM STRUCTURE CAPTURED AS LS AND LS WITH FULL PPE MOVING AT WORKING BAY.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624078", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624078_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624078_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC507F (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "wharf side captured the image", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624079", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624079_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624079_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC526F (VALO)", "infringement_type": "2-Container Distance", "alert_status": "Invalid", "remarks": "⦁\tLS in full PPE  life jacket and TLAD doing container top unlocking", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624080", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624080_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624080_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC602F (VALO)", "infringement_type": "One man Lashing", "alert_status": "Invalid", "remarks": "VESSEL STRUCTURE CAPTURED  AS LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624081", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624081_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624081_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC510 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "PM structure captured as LS", "is_false_positive": true, "vlm_decision": "dismissed", "vlm_response": "DISMISS", "confidence": 0.85}, {"case_number": "V1250624082", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624082_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624082_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC602F (VALO)", "infringement_type": "STA Double-up", "alert_status": "Invalid", "remarks": "STA STANDING AND MOVING AT WORKING BAY ONLY", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624083", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/valid/V1250624083_cropped_valid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/valid/V1250624083_source_valid.JPEG", "terminal": "P2", "camera_id": "QC320 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "<PERSON><PERSON>", "remarks": "LS USING MOBILE DEVICE AT WHARF", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW - Valid violation", "confidence": 0.95}, {"case_number": "V1250624084", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624084_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624084_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC601 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "CAMERA STRUCTURE CAPTURED AS LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624085", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624085_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624085_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC320 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS / Driver in full PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624086", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624086_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624086_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC523 WOS Cam (C) (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Technician captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624087", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624087_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624087_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC510 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS Full PPE At Wharf. Doing conning Ops", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624088", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624088_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624088_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC109F (VALO)", "infringement_type": "2-Container Distance", "alert_status": "Invalid", "remarks": "LS alighted from spreader ride on container top", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624089", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624089_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624089_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC320 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "WOS in proper PPE at wharf. Doing QC Gantry ops", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624090", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624090_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624090_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC109F (VALO)", "infringement_type": "2-Container Distance", "alert_status": "Invalid", "remarks": "LS alighted from spreader ride on container top", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624091", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624091_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624091_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC605 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Crane structure captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624092", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624092_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624092_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC601 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Safety officer captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624093", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624093_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624093_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC614F (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Spreader Structure Captured As Ls", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624094", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624094_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624094_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC614F (VALO)", "infringement_type": "2-Container Distance", "alert_status": "Invalid", "remarks": "Container Structure and Spreader Structure Captured As Ls", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624095", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624095_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624095_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC605 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS in full PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624096", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624096_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624096_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC601 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "SHIP OFFICER CAPTURED AND TAKING FOTO OF THE VESSEL.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624097", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624097_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624097_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC108F (VALO)", "infringement_type": "One man Lashing", "alert_status": "Invalid", "remarks": "LS doing loosening of lashing turnbuckle", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624098", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624098_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624098_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC512 WOS Cam (C) (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Crane structure captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624099", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624099_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624099_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC320 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Technician captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624100", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624100_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624100_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC601 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LSUP  / OFFICER IN FULL PPE AT WHARF", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624101", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624101_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624101_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC320 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Technician captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624102", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624102_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624102_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC601 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Safety officers and LSUP in full PPE at wharf.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624103", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624103_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624103_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC507F (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "No exception in observation", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624104", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624104_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624104_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC507F (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Sea side captured the image", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624105", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624105_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624105_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC602F (VALO)", "infringement_type": "One man Lashing", "alert_status": "Invalid", "remarks": "WATER REFLECT CAPTURED AS LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624106", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624106_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624106_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC520F (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Lashing material captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624107", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624107_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624107_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC507F (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Sea side captured the image", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624108", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624108_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624108_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC510 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "<PERSON> Full PPE at wharf.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624109", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624109_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624109_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC509F (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Vessel structure captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624110", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624110_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624110_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC507F (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Sea side captured the image", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624111", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624111_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624111_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC104F (VALO)", "infringement_type": "One man Lashing", "alert_status": "Invalid", "remarks": "Ship crew wrongly captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624112", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624112_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624112_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC320 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS in full PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624113", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624113_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624113_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC601 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS IS IN FULL PPE DOING CONNING OPS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624114", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624114_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624114_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC506 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS Full PPE at wharf.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624115", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624115_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624115_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC320 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS IS IN FULL PPE DOING CONNING OPS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624116", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624116_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624116_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC601 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS in full PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624117", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624117_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624117_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC104F (VALO)", "infringement_type": "One man Lashing", "alert_status": "Invalid", "remarks": "STA wrongly captured as LS doing one man lashing", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624118", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624118_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624118_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC528 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS Full PPE at wharf.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624119", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624119_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624119_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC602F (VALO)", "infringement_type": "One man Lashing", "alert_status": "Invalid", "remarks": "2 LS in full PPE with lanyard hooked on anchoring point doing lashing.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624120", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624120_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624120_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC302F (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "WHARF STRUCTURE CAPTURED AS LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624121", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624121_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624121_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC601 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "WOS IN FULL PPE AT WHARF", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624122", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624122_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624122_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC510 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "<PERSON> Full PPE at wharf.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624123", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624123_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624123_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC108F (VALO)", "infringement_type": "2-Container Distance", "alert_status": "Invalid", "remarks": "LS alighted from spreader ride on container top", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624124", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624124_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624124_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC601 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "WOS IN FULL PPE AT WHARF AND GUIDING OVER HIGH FLAME.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624125", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624125_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624125_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC525F (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Vessel structure captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624126", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624126_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624126_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC510 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS Full PPE At Wharf. Doing conning Ops", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624127", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624127_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624127_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC506 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "WOS in proper PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624128", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624128_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624128_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC528 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS Full PPE at wharf.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624129", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624129_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624129_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC601 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "WOS in proper PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624130", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624130_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624130_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC523 WOS Cam (C) (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Crane structure captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624131", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624131_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624131_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC602F (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS IN FULL PPE STANDING BY AT WORKING BAY", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624132", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624132_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624132_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC602F (VALO)", "infringement_type": "STA Double-up", "alert_status": "Invalid", "remarks": "VESSEL STRUCTURE CAPTURED  AS LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624133", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624133_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624133_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC602F (VALO)", "infringement_type": "One man Lashing", "alert_status": "Invalid", "remarks": "VESSEL STRUCTURE CAPTURED  AS LS", "is_false_positive": true, "vlm_decision": "dismissed", "vlm_response": "DISMISS", "confidence": 0.85}, {"case_number": "V1250624134", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624134_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624134_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC521F (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS/STA in full PPE at wharf and waiting ship crew to fix a safety net before onboard.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624135", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624135_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624135_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC320 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "WOS IN FULL PPE AT WHARF", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624136", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624136_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624136_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC523 WOS Cam (C) (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "<PERSON> preparedness the safety cone at the lane 4.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624137", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624137_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624137_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC104F (VALO)", "infringement_type": "One man Lashing", "alert_status": "Invalid", "remarks": "STA wrongly captured as LS doing one man lashing", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624138", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624138_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624138_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC109F (VALO)", "infringement_type": "STA Double-up", "alert_status": "Invalid", "remarks": "STA moving  did not double up", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624139", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624139_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624139_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC306F (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "VESSEL STRUCTURE CAPTURED  AS LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624140", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624140_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624140_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC601 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS in full PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624141", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624141_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624141_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC108F (VALO)", "infringement_type": "One man Lashing", "alert_status": "Invalid", "remarks": "STA wrongly captured as LS doing one man lashing", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624142", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624142_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624142_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC306F (VALO)", "infringement_type": "2-Container Distance", "alert_status": "Invalid", "remarks": "VESSEL STRUCTURE CAPTURED  AS LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624143", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624143_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624143_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC306F (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "VESSEL STRUCTURE CAPTURED  AS LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624144", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624144_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624144_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC109F (VALO)", "infringement_type": "One man Lashing", "alert_status": "Invalid", "remarks": "Exception  BJH foreman momentarily to check container position", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624145", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624145_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624145_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC601 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "CAMERA STRUCTURE CAPTURED AS LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624146", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624146_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624146_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC528 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "QCO in proper PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624147", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624147_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624147_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC605 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "PMD in Full PPE At Wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624148", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624148_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624148_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC108F (VALO)", "infringement_type": "Ex.Row Violation", "alert_status": "Invalid", "remarks": "No exception in observation", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624149", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624149_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624149_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC510 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "WOS in proper PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624150", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624150_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624150_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC605 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS in full PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624151", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624151_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624151_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC506 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "WOS in proper PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624152", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624152_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624152_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC510 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS Full PPE at wharf.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624153", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624153_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624153_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC526F (VALO)", "infringement_type": "2-Container Distance", "alert_status": "Invalid", "remarks": "Container structure captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624154", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624154_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624154_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC523 WOS Cam (C) (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS Full PPE at wharf.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624155", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624155_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624155_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC601 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "CRANE STRUCTURE CAPTURED  AS LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624156", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624156_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624156_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC507F (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Container structure captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624157", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624157_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624157_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC605 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "WOS IN FULL PPE AT WHARF", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624158", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624158_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624158_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC523 WOS Cam (C) (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS Full PPE at wharf.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624159", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624159_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624159_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC320 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "PM STRUCTURE CAPTURED  AS LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624160", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/valid/V1250624160_cropped_valid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/valid/V1250624160_source_valid.JPEG", "terminal": "P2", "camera_id": "QC610F (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "<PERSON><PERSON>", "remarks": "P25 EVER VERSE Ship crew did not wear their life jacket", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW - Valid violation", "confidence": 0.95}, {"case_number": "V1250624161", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624161_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624161_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC601 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "QCO IS IN FULL PPE CAPTURED AS LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624162", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624162_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624162_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC320 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LSUP IN FULL PPE AT WHARF", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624163", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624163_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624163_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC108F (VALO)", "infringement_type": "STA Double-up", "alert_status": "Invalid", "remarks": "No exception in observation", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624164", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624164_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624164_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC528 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "WTA IN PROPER PPE AT WHARF", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624165", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624165_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624165_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC509F (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "TUG BOAT STRUCTURE CAPTURED AS LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624166", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624166_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624166_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC512 WOS Cam (C) (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "No video footage clear", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624167", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624167_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624167_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC517F (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Tugboat structure captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624168", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624168_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624168_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC605 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Forklift structure captured as LS.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624169", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624169_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624169_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC601 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "CRANE STRUCTURE CAPTURED  AS LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624170", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624170_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624170_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC506 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "WOS in proper PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624171", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624171_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624171_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC611 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS in full PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624172", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624172_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624172_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC505F (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Spreader structure captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624173", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624173_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624173_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC605 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS in full PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624174", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624174_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624174_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC614F (VALO)", "infringement_type": "Ex.Row Violation", "alert_status": "Invalid", "remarks": "No video footage", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624175", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624175_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624175_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC614F (VALO)", "infringement_type": "One man Lashing", "alert_status": "Invalid", "remarks": "VESSEL STRUCTURE CAPTURED  AS LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624176", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624176_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624176_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC107F (VALO)", "infringement_type": "One man Lashing", "alert_status": "Invalid", "remarks": "LS in full PPE doing unlocking", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624177", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624177_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624177_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC601 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "CRANE STRUCTURE CAPTURED  AS LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250624178", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624178_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250624178_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC605 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS in full PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625001", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625001_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625001_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC510 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "WTA IN PROPER PPE AT WHARF", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625002", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625002_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625002_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC605 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS in full PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625003", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625003_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625003_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC109F (VALO)", "infringement_type": "STA Double-up", "alert_status": "Invalid", "remarks": "No exception in observation", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625004", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625004_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625004_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC109F (VALO)", "infringement_type": "One man Lashing", "alert_status": "Invalid", "remarks": "STA guiding ops  no exception", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625005", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625005_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625005_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC506 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS Full PPE at wharf.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625006", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625006_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625006_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC601 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "CRANE STRUCTURE CAPTURED  AS LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625007", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625007_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625007_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC528 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS Full PPE at wharf.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625008", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625008_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625008_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC605 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS in full PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625009", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625009_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625009_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC510 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "PM structure captured as LS", "is_false_positive": true, "vlm_decision": "dismissed", "vlm_response": "DISMISS", "confidence": 0.85}, {"case_number": "V1250625010", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625010_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625010_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC110F (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS in full PPE doing spreader ride", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625011", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625011_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625011_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC601 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "CRANE STRUCTURE CAPTURED  AS LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625012", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625012_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625012_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC528 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS Full PPE at wharf.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625013", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625013_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625013_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC510 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Forklift captured as LS.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625014", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625014_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625014_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC614F (VALO)", "infringement_type": "Ex.Row Violation", "alert_status": "Invalid", "remarks": "LS full ppe doing 2 man lashing with lanyard hook not in extreme row", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625015", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625015_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625015_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC614F (VALO)", "infringement_type": "One man Lashing", "alert_status": "Invalid", "remarks": "2LS IN FULL PPE WITH LANYARD HOOKED ON ANCHORING POINT DOING LASHING", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625016", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625016_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625016_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC611 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "WOS IN FULL PPE AT WHARF", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625017", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625017_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625017_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC601 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "CRANE STRUCTURE CAPTURED  AS LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625018", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625018_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625018_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC605 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS in full PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625019", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625019_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625019_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC510 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "PM structure captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625020", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625020_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625020_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC510 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "PM structure captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625021", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625021_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625021_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC510 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "PM structure captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625022", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625022_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625022_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC605 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS in full PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625023", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625023_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625023_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC506 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS Full PPE at wharf.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625024", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625024_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625024_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC109F (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "No exception in observation", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625025", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625025_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625025_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC109F (VALO)", "infringement_type": "2-Container Distance", "alert_status": "Invalid", "remarks": "LS in full PPE doing spreader ride", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625026", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625026_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625026_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC512 WOS Cam (C) (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS Full PPE at wharf.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625027", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/valid/V1250625027_cropped_valid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/valid/V1250625027_source_valid.JPEG", "terminal": "P2", "camera_id": "QC605 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "<PERSON><PERSON>", "remarks": "Crane 605 gantry WOS stand inside the gantry path", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW - Valid violation", "confidence": 0.95}, {"case_number": "V1250625028", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625028_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625028_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC506 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS Full PPE at wharf.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625029", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625029_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625029_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC605 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS in full PPE doing spreader ride", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625030", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625030_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625030_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC512 WOS Cam (C) (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS Full PPE at wharf.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625031", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625031_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625031_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC110F (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "STA guiding ops  no exception", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625032", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625032_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625032_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC109F (VALO)", "infringement_type": "One man Lashing", "alert_status": "Invalid", "remarks": "No exception in observation", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625033", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625033_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625033_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC605 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "WOS IN FULL PPE AT WHARF", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625034", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625034_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625034_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC601 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "CRANE STRUCTURE CAPTURED  AS LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625035", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625035_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625035_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC510 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "PM structure captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625036", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625036_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625036_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC611 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS in full PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625037", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625037_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625037_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC320 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS in full PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625038", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625038_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625038_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC109F (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "STA guiding ops  no exception", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625039", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625039_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625039_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC506 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Spreader structure captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625040", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625040_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625040_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC107F (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "No exception in observation", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625041", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625041_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625041_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC320 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS in full PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625042", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625042_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625042_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC506 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Spreader structure captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625043", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625043_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625043_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC107F (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "No exception in observation", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625044", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625044_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625044_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC320 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS in full PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625045", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625045_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625045_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC605 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "WOS IN FULL PPE AT WHARF", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625046", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/valid/V1250625046_cropped_valid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/valid/V1250625046_source_valid.JPEG", "terminal": "P2", "camera_id": "QC605 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "<PERSON><PERSON>", "remarks": "CONEMAN FAILED TO <PERSON><PERSON><PERSON><PERSON> FAR SIDE TO NEAR SIDE", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW - Valid violation", "confidence": 0.95}, {"case_number": "V1250625047", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625047_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625047_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC510 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS Full PPE at wharf.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625048", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625048_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625048_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC506 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Spreader structure captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625049", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625049_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625049_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC109F (VALO)", "infringement_type": "Ex.Row Violation", "alert_status": "Invalid", "remarks": "STA guiding ops  no exception", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625050", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625050_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625050_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC109F (VALO)", "infringement_type": "One man Lashing", "alert_status": "Invalid", "remarks": "No exception in observation", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625051", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625051_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625051_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC320 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS in full PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625052", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625052_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625052_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC605 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "PM Driver Full PPE At Wharf.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625053", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625053_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625053_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC519 WOS Cam (C) (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "PM Driver Full PPE At Wharf.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625054", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625054_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625054_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC605 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "PM Driver Full PPE At Wharf.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625055", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625055_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625055_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC320 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS Full PPE at wharf.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625056", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625056_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625056_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC506 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "WOS in proper PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625057", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625057_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625057_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC510 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "WOS in proper PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625058", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625058_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625058_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC601 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "PM Driver Full PPE At Wharf.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625059", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625059_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625059_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC104F (VALO)", "infringement_type": "One man Lashing", "alert_status": "Invalid", "remarks": "Ship crew wrongly captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625060", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625060_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625060_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC521F (VALO)", "infringement_type": "Spreader Ride", "alert_status": "Invalid", "remarks": "LS in full PPE doing spreader ride OPS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625061", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625061_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625061_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC320 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "WOS IN FULL PPE AT WHARF", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625062", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625062_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625062_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC605 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS in full PPE with life jacket and TLAD coming down from spreader.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625063", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/valid/V1250625063_cropped_valid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/valid/V1250625063_source_valid.JPEG", "terminal": "P2", "camera_id": "QC611 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "<PERSON><PERSON>", "remarks": "WOS for QC611 standing along yellow line within the QC gantry path and leaning his body against the crane leg", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW - Valid violation", "confidence": 0.95}, {"case_number": "V1250625064", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625064_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625064_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC320 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "WOS IN FULL PPE AT WHARF", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625065", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625065_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625065_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC510 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "cleaner contractors captured As LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625066", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625066_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625066_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC605 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "WHARF STRUCTURE CAPTURED AS LS", "is_false_positive": true, "vlm_decision": "dismissed", "vlm_response": "DISMISS", "confidence": 0.85}, {"case_number": "V1250625067", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625067_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625067_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC106F (VALO)", "infringement_type": "One man Lashing", "alert_status": "Invalid", "remarks": "2 LS in full PPE with lanyard hooked on anchoring point doing lashing", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625068", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625068_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625068_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC605 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "PM STRUCTURE CAPTURED  AS LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625069", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625069_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625069_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC109F (VALO)", "infringement_type": "Ex.Row Violation", "alert_status": "Invalid", "remarks": "No LS at the extreme row doing lashing", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625070", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625070_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625070_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC109F (VALO)", "infringement_type": "One man Lashing", "alert_status": "Invalid", "remarks": "2 LS in full PPE working at center row", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625071", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625071_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625071_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC605 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "WOS IN FULL PPE AT WHARF", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625072", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625072_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625072_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC109F (VALO)", "infringement_type": "Ex.Row Violation", "alert_status": "Invalid", "remarks": "2 LS in full PPE doing lashing not at the extreme row", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625073", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625073_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625073_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC109F (VALO)", "infringement_type": "One man Lashing", "alert_status": "Invalid", "remarks": "2 LS in full PPE doing lashing", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625074", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625074_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625074_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC510 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "<PERSON> Full PPE at wharf.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625075", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625075_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625075_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC506 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS Full PPE At Wharf. Doing conning Ops", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625076", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625076_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625076_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC302F (VALO)", "infringement_type": "2-Container Distance", "alert_status": "Invalid", "remarks": "Spreader Structure Captured As Ls", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625077", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625077_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625077_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC109F (VALO)", "infringement_type": "One man Lashing", "alert_status": "Invalid", "remarks": "LS doing tightening of lashing turnbuckle", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625078", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625078_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625078_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC601 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS in full PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625079", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625079_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625079_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC106F (VALO)", "infringement_type": "One man Lashing", "alert_status": "Invalid", "remarks": "Vessel structure wrongly captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625080", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625080_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625080_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC106F (VALO)", "infringement_type": "Ex.Row Violation", "alert_status": "Invalid", "remarks": "Vessel structure wrongly captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625081", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625081_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625081_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC510 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Charlie Full PPE At Wharf. Doing conning Ops", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625082", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625082_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625082_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC106F (VALO)", "infringement_type": "Ex.Row Violation", "alert_status": "Invalid", "remarks": "Vessel structure wrongly captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625083", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625083_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625083_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC106F (VALO)", "infringement_type": "One man Lashing", "alert_status": "Invalid", "remarks": "Vessel structure wrongly captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625084", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625084_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625084_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC611 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "WOS IN FULL PPE AT WHARF", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625085", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625085_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625085_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC604F (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "VESSEL STRUCTURE CAPTURED  AS LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625086", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625086_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625086_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC109F (VALO)", "infringement_type": "One man Lashing", "alert_status": "Invalid", "remarks": "2 LS in full PPE with lanyard hooked on anchoring point doing lashing", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625087", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625087_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625087_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC506 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS in full PPE at wharf and guiding QCO crane gantry", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625088", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625088_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625088_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC510 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "<PERSON> Full PPE at wharf.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625089", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625089_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625089_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC603F (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "SHIP CREW CAPTURED AS LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625090", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625090_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625090_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC604F (VALO)", "infringement_type": "One man Lashing", "alert_status": "Invalid", "remarks": "VESSEL STRUCTURE SPOTLIGHT CAPTURED  AS LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625091", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625091_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625091_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC308F (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "WHARF STRUCTURE CAPTURED AS LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625092", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625092_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625092_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC510 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "<PERSON> Full PPE at wharf.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625093", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625093_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625093_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC512 WOS Cam (C) (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Container Structure Captured As Ls", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625094", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625094_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625094_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC605 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS in full PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625095", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625095_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625095_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC308F (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "WHARF STRUCTURE CAPTURED AS LS AND STA GUIDING QOC CLOSING HATCH COVER.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625096", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625096_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625096_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC512 WOS Cam (C) (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Container captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625097", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625097_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625097_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC512 WOS Cam (C) (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Container captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625098", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625098_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625098_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC519 WOS Cam (C) (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "WOS in proper PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625099", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625099_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625099_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC506 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS in full PPE doing descending from the spreader ride", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625100", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625100_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625100_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC605 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "PM STRUCTURE CAPTURED  AS LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625101", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625101_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625101_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC512 WOS Cam (C) (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Container Structure Captured As Ls", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625102", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625102_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625102_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC512 WOS Cam (C) (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Container captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625103", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625103_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625103_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC611 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "WOS IN FULL PPE AT WHARF", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625104", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625104_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625104_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC320 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "CAMERA STRUCTURE CAPTURE AS LS", "is_false_positive": true, "vlm_decision": "dismissed", "vlm_response": "DISMISS", "confidence": 0.85}, {"case_number": "V1250625105", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625105_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625105_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC523 WOS Cam (C) (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "WOS in proper PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625106", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625106_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625106_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC510 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "<PERSON> Full PPE at wharf.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625107", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625107_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625107_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC523 WOS Cam (C) (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS Full PPE At Wharf. Doing Deconning Ops", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625108", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625108_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625108_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC615F (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "VESSEL STRUCTURE CAPTURED  AS LS", "is_false_positive": true, "vlm_decision": "dismissed", "vlm_response": "DISMISS", "confidence": 0.85}, {"case_number": "V1250625109", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625109_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625109_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC601 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS IS IN FULL PPE DOING CONNING OPS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625110", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625110_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625110_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC506 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Crane structure captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625111", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625111_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625111_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC601 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS IS IN FULL PPE DOING CONNING OPS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625112", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625112_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625112_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC506 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Crane structure captured as LS", "is_false_positive": true, "vlm_decision": "dismissed", "vlm_response": "DISMISS", "confidence": 0.85}, {"case_number": "V1250625113", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625113_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625113_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC306F (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "BOLLARD STRUCTURE CAPTURE AS LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625114", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625114_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625114_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC605 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LSUP IN FULL PPE AT WHARF", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625115", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625115_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625115_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC510 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS Full PPE at wharf.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625116", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625116_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625116_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC506 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "CAMERA STRUCTURE CAPTURED AS LS", "is_false_positive": true, "vlm_decision": "dismissed", "vlm_response": "DISMISS", "confidence": 0.85}, {"case_number": "V1250625117", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625117_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625117_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC104F (VALO)", "infringement_type": "One man Lashing", "alert_status": "Invalid", "remarks": "Vessel structure wrongly captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625118", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625118_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625118_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC307F (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Ship Crew and LSUP captured as LS and Lsup just complete berthing the vessel.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625119", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625119_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625119_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC320 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS in full PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625120", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625120_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625120_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC506 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Crane structure captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625121", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625121_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625121_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC601 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "WOS / LS IN FULL PPE AT WHARF", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625122", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625122_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625122_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC516F (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "VESSEL SPOTLIGHT STRUCTURE CAPTURED  AS LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625123", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625123_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625123_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC506 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Crane structure captured as LS", "is_false_positive": true, "vlm_decision": "dismissed", "vlm_response": "DISMISS", "confidence": 0.85}, {"case_number": "V1250625124", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625124_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625124_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC313F (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "VESSEL STRUCTURE CAPTURED  AS LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625125", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625125_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625125_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC510 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "WTA  FULL PPE AT  WHARF", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625126", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625126_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625126_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC104F (VALO)", "infringement_type": "One man Lashing", "alert_status": "Invalid", "remarks": "no exception in observation.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625127", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625127_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625127_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC506 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "CRANE SPOTLIGHT STRUCTURE CAPTURED  AS LS", "is_false_positive": true, "vlm_decision": "dismissed", "vlm_response": "DISMISS", "confidence": 0.85}, {"case_number": "V1250625128", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625128_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625128_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC510 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS Full PPE At Wharf. Doing conning Ops", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625129", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625129_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625129_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC320 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "STA in full PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625130", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625130_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625130_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC104F (VALO)", "infringement_type": "One man Lashing", "alert_status": "Invalid", "remarks": "No exception in observation", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625131", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625131_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625131_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC104F (VALO)", "infringement_type": "One man Lashing", "alert_status": "Invalid", "remarks": "No exception in observation", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625132", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625132_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625132_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC506 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Crane structure captured as LS", "is_false_positive": true, "vlm_decision": "dismissed", "vlm_response": "DISMISS", "confidence": 0.85}, {"case_number": "V1250625133", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625133_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625133_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC104F (VALO)", "infringement_type": "STA Double-up", "alert_status": "Invalid", "remarks": "No exception in observation", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625134", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625134_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625134_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC313F (VALO)", "infringement_type": "One man Lashing", "alert_status": "Invalid", "remarks": "VESSEL STRUCTURE CAPTURED  AS LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625135", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625135_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625135_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC605 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "PMD Captured As Ls During The Change Shift", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625136", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625136_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625136_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC320 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "PMD Captured As Ls During The Change Shift", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625137", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625137_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625137_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC601 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS in full PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625138", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625138_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625138_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC506 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Crane structure captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625139", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625139_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625139_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC605 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS in full PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625140", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625140_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625140_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC510 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS Full PPE at wharf.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625141", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625141_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625141_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC523 WOS Cam (C) (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS Full PPE at wharf.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625142", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625142_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625142_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC605 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "WOS IN FULL PPE AT WHARF", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625143", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625143_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625143_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC506 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LSUP full PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625144", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625144_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625144_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC525F (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "PM structure captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625145", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625145_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625145_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC506 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS Full PPE at wharf.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625146", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625146_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625146_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC510 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS Full PPE at wharf.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625147", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625147_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625147_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC512 WOS Cam (C) (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS Full PPE at wharf.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625148", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625148_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625148_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC510 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS Full PPE at wharf.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625149", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625149_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625149_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC506 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Crane structure captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625150", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625150_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625150_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC611 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS in full PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625151", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625151_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625151_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC605 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "WOS IN FULL PPE AT WHARF", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625152", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625152_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625152_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC510 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "QCO in proper PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625153", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625153_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625153_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC512 WOS Cam (C) (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Spreader structure captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625154", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625154_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625154_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC510 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LSUP full PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625155", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625155_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625155_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC601 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "CRANE STRUCTURE CAPTURED  AS LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625156", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625156_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250625156_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC601 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "CRANE STRUCTURE CAPTURED  AS LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250625157", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/valid/V1250625157_cropped_valid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/valid/V1250625157_source_valid.JPEG", "terminal": "P2", "camera_id": "QC601 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "<PERSON><PERSON>", "remarks": "LS sitting at the spreader structure when doing spreader ride at the wharf side", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW - Valid violation", "confidence": 0.95}, {"case_number": "V1250626001", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626001_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626001_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC510 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "WOS in proper PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626002", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626002_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626002_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC601 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "CRANE STRUCTURE CAPTURED  AS LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626003", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626003_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626003_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC601 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "CRANE STRUCTURE CAPTURED  AS LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626004", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626004_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626004_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC601 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS in full PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626005", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626005_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626005_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC523 WOS Cam (C) (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "WTA  Full PPE At Wharf.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626006", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626006_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626006_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC601 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS in full PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626007", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626007_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626007_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC611 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS in full PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626008", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626008_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626008_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC523 WOS Cam (C) (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "WTA IN PROPER PPE AT WHARF", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626009", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626009_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626009_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC601 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "CRANE STRUCTURE CAPTURED  AS LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626010", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626010_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626010_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC601 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "CRANE STRUCTURE CAPTURED  AS LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626011", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626011_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626011_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC601 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS in full PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626012", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626012_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626012_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC110F (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "WOS in full PPE.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626013", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626013_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626013_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC519 WOS Cam (C) (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS Full PPE at wharf.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626014", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626014_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626014_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC106F (VALO)", "infringement_type": "2-Container Distance", "alert_status": "Invalid", "remarks": "Belt ops in progress.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626015", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626015_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626015_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC104F (VALO)", "infringement_type": "One man Lashing", "alert_status": "Invalid", "remarks": "Ls did not do lashing.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626016", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626016_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626016_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC104F (VALO)", "infringement_type": "Ex.Row Violation", "alert_status": "Invalid", "remarks": "LS not at extreme row.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626017", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626017_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626017_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC610F (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "VESSEL STRUCTURE CAPTURED  AS LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626018", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626018_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626018_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC601 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "CRANE STRUCTURE CAPTURED  AS LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626019", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626019_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626019_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC611 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS in full PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626020", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626020_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626020_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC316F (VALO)", "infringement_type": "One man Lashing", "alert_status": "Invalid", "remarks": "LS IN FULL PPE STANDING  AT WORKING BAY", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626021", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626021_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626021_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC109F (VALO)", "infringement_type": "Ex.Row Violation", "alert_status": "Invalid", "remarks": "2 LS in full PPE doing lashing at extreme row.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626022", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626022_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626022_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC104F (VALO)", "infringement_type": "One man Lashing", "alert_status": "Invalid", "remarks": "No exception in observation", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626023", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626023_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626023_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC506 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS Full PPE at wharf.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626024", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626024_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626024_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC104F (VALO)", "infringement_type": "Ex.Row Violation", "alert_status": "Invalid", "remarks": "No exception in observation", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626025", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626025_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626025_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC104F (VALO)", "infringement_type": "One man Lashing", "alert_status": "Invalid", "remarks": "No exception in observation", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626026", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626026_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626026_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC109F (VALO)", "infringement_type": "Ex.Row Violation", "alert_status": "Invalid", "remarks": "LS not at extreme row.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626027", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626027_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626027_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC109F (VALO)", "infringement_type": "One man Lashing", "alert_status": "Invalid", "remarks": "Ls did not do lashing.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626028", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626028_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626028_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC104F (VALO)", "infringement_type": "Ex.Row Violation", "alert_status": "Invalid", "remarks": "No exception in observation", "is_false_positive": true, "vlm_decision": "dismissed", "vlm_response": "DISMISS", "confidence": 0.85}, {"case_number": "V1250626029", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626029_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626029_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC104F (VALO)", "infringement_type": "One man Lashing", "alert_status": "Invalid", "remarks": "No exception in observation", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626030", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626030_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626030_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC104F (VALO)", "infringement_type": "Ex.Row Violation", "alert_status": "Invalid", "remarks": "No exception in observation", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626031", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626031_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626031_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC601 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "CRANE STRUCTURE CAPTURED  AS LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626032", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626032_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626032_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC104F (VALO)", "infringement_type": "Ex.Row Violation", "alert_status": "Invalid", "remarks": "No exception in observation", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626033", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626033_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626033_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC104F (VALO)", "infringement_type": "One man Lashing", "alert_status": "Invalid", "remarks": "No exception in observation", "is_false_positive": true, "vlm_decision": "dismissed", "vlm_response": "DISMISS", "confidence": 0.85}, {"case_number": "V1250626034", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626034_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626034_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC104F (VALO)", "infringement_type": "Ex.Row Violation", "alert_status": "Invalid", "remarks": "No exception in observation", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626035", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626035_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626035_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC104F (VALO)", "infringement_type": "One man Lashing", "alert_status": "Invalid", "remarks": "No exception in observation", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626036", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626036_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626036_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC104F (VALO)", "infringement_type": "Ex.Row Violation", "alert_status": "Invalid", "remarks": "No exception in observation", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626037", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626037_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626037_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC506 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Spreader structure captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626038", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626038_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626038_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC601 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "PM STRUCTURE CAPTURED  AS LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626039", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626039_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626039_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC506 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS Full PPE at wharf.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626040", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626040_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626040_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC611 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "WOS IN FULL PPE AT WHARF", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626041", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/valid/V1250626041_cropped_valid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/valid/V1250626041_source_valid.JPEG", "terminal": "P2", "camera_id": "QC605 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "<PERSON><PERSON>", "remarks": "LS walk at yellow line inside crane gantry path", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW - Valid violation", "confidence": 0.95}, {"case_number": "V1250626042", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626042_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626042_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC523 WOS Cam (C) (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS Full PPE at wharf.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626043", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626043_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626043_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC320 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Forklift structure captured as LS.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626044", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626044_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626044_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC510 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS Full PPE at wharf.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626045", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626045_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626045_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC320 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "PM STRUCTURE CAPTURED  AS LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626046", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626046_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626046_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC510 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "WOS in proper PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626047", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626047_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626047_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC528 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Crane structure captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626048", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626048_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626048_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC510 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Safety cone captured as LS", "is_false_positive": true, "vlm_decision": "dismissed", "vlm_response": "DISMISS", "confidence": 0.85}, {"case_number": "V1250626049", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626049_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626049_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC601 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS in full PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626050", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626050_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626050_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC601 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "CRANE STRUCTURE CAPTURED  AS LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626051", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626051_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626051_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC510 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "WOS in proper PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626052", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626052_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626052_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC320 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "PMD in Full PPE At Wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626053", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626053_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626053_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC605 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "PMD in Full PPE At Wharf( one people pre operation  check )", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626054", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/valid/V1250626054_cropped_valid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/valid/V1250626054_source_valid.JPEG", "terminal": "P2", "camera_id": "QC601 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "<PERSON><PERSON>", "remarks": "QC601 WOS walked over the gantry rail while the crane was still in gantry mode.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW - Valid violation", "confidence": 0.95}, {"case_number": "V1250626055", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626055_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626055_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC510 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "PM Driver Full PPE At Wharf.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626056", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626056_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626056_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC510F (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Vessel structure captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626057", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626057_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626057_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC506 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS Full PPE at wharf.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626058", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626058_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626058_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC510 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "WOS in proper PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626059", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626059_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626059_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC601 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS in full PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626060", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/valid/V1250626060_cropped_valid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/valid/V1250626060_source_valid.JPEG", "terminal": "P2", "camera_id": "QC611 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "<PERSON><PERSON>", "remarks": "exception: due to collect TL at the wharf while WOS guiding and was crane power off ...TDM 2 was aware.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW - Valid violation", "confidence": 0.95}, {"case_number": "V1250626061", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626061_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626061_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC109F (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS in proper PPE when taking spreader ride", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626062", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626062_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626062_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC601 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "PM STRUCTURE CAPTURED  AS LS", "is_false_positive": true, "vlm_decision": "dismissed", "vlm_response": "DISMISS", "confidence": 0.85}, {"case_number": "V1250626063", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626063_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626063_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC506 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS Full PPE at wharf.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626064", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626064_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626064_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC610F (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "VESSEL STRUCTURE CAPTURED  AS LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626065", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626065_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626065_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC509F (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Vessel structure captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626066", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626066_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626066_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC611 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS in full PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626067", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626067_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626067_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC528 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS Full PPE at wharf.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626068", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626068_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626068_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC320 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "technician in full PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626069", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/valid/V1250626069_cropped_valid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/valid/V1250626069_source_valid.JPEG", "terminal": "P2", "camera_id": "QC605 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "<PERSON><PERSON>", "remarks": "LS walked over the gantry path while crane was gantry ops ...", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW - Valid violation", "confidence": 0.95}, {"case_number": "V1250626070", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626070_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626070_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC601 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "External Contractors in full PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626071", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626071_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626071_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC313F (VALO)", "infringement_type": "One man Lashing", "alert_status": "Invalid", "remarks": "Container Structure Captured As Ls", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626072", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626072_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626072_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC109F (VALO)", "infringement_type": "2-Container Distance", "alert_status": "Invalid", "remarks": "LS taking spreader ride to container top", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626073", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/valid/V1250626073_cropped_valid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/valid/V1250626073_source_valid.JPEG", "terminal": "P3", "camera_id": "QC510 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "<PERSON><PERSON>", "remarks": "FM lorry pass by lane 7 during operation", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW - Valid violation", "confidence": 0.95}, {"case_number": "V1250626074", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626074_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626074_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC528 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "WOS in proper PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626075", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626075_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626075_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC605 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "technician in full PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626076", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626076_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626076_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC306F (VALO)", "infringement_type": "STA Double-up", "alert_status": "Invalid", "remarks": "STA in full PPE and lanyard hooked at anchor point  while guiding to  QCO in hatch ops", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626077", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626077_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626077_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC604F (VALO)", "infringement_type": "Ex.Row Violation", "alert_status": "Invalid", "remarks": "No video footage", "is_false_positive": true, "vlm_decision": "dismissed", "vlm_response": "DISMISS", "confidence": 0.85}, {"case_number": "V1250626078", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626078_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626078_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC604F (VALO)", "infringement_type": "Ex.Row Violation", "alert_status": "Invalid", "remarks": "CRANE STRUCTURE CAPTURED  AS LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626079", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626079_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626079_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC604F (VALO)", "infringement_type": "One man Lashing", "alert_status": "Invalid", "remarks": "CRANE STRUCTURE CAPTURED  AS LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626080", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626080_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626080_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC605 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "technician in full PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626081", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626081_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626081_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC109F (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Spreader flipper wrongly captured as L<PERSON>", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626082", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626082_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626082_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC308F (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "safety officers in full ppe at onboard", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626083", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626083_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626083_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC308F (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "safety officers in full ppe at onboard", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626084", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626084_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626084_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC510 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "WOS in proper PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626085", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626085_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626085_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC519 WOS Cam (C) (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS Full PPE at wharf.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626086", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626086_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626086_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC506 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS Full PPE at wharf.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626087", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626087_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626087_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC605 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "technician in full PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626088", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626088_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626088_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC528 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "External Contractor Captured As LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626089", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626089_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626089_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC510 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS Full PPE at wharf.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626090", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626090_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626090_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC510 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "WOS in proper PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626091", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626091_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626091_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC506 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS Full PPE at wharf.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626092", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626092_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626092_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC605 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "technician in full PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626093", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626093_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626093_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC107F (VALO)", "infringement_type": "2-Container Distance", "alert_status": "Invalid", "remarks": "Vessel structure wrongly captured as spreader", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626094", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626094_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626094_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC108F (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "2 LS in proper PPE with life jacket on when taking spreader ride", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626095", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626095_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626095_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC506 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS Full PPE at wharf.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626096", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626096_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626096_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC510 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "WOS in proper PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626097", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626097_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626097_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC308F (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "VESSEL STRUCTURE CAPTURED  AS LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626098", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/valid/V1250626098_cropped_valid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/valid/V1250626098_source_valid.JPEG", "terminal": "P2", "camera_id": "QC605 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "<PERSON><PERSON>", "remarks": "Several unidentified personnel standing behind a white van at QC605 lane one operational area without wearing any PPE.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW - Valid violation", "confidence": 0.95}, {"case_number": "V1250626099", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626099_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626099_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC308F (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "VESSEL STRUCTURE CAPTURED  AS LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626100", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626100_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626100_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC512 WOS Cam (C) (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS Full PPE at wharf.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626101", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626101_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626101_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC506 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS Full PPE at wharf.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626102", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626102_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626102_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC308F (VALO)", "infringement_type": "Ex.Row Violation", "alert_status": "Invalid", "remarks": "2 LS in full PPE working at center row", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626103", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626103_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626103_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC308F (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "2 LS in full PPE working at center row", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626104", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626104_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626104_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC512 WOS Cam (C) (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS Full PPE at wharf.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626105", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626105_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626105_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC313F (VALO)", "infringement_type": "Ex.Row Violation", "alert_status": "Invalid", "remarks": "Lashing  Materials Captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626106", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626106_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626106_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC313F (VALO)", "infringement_type": "One man Lashing", "alert_status": "Invalid", "remarks": "Lashing  Materials Captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626107", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626107_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626107_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC313F (VALO)", "infringement_type": "Ex.Row Violation", "alert_status": "Invalid", "remarks": "Lashing  Materials Captured as LS", "is_false_positive": true, "vlm_decision": "dismissed", "vlm_response": "DISMISS", "confidence": 0.85}, {"case_number": "V1250626108", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626108_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626108_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC525F (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Forklift structure captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626109", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626109_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626109_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC510 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS Full PPE at wharf.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626110", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626110_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626110_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC313F (VALO)", "infringement_type": "One man Lashing", "alert_status": "Invalid", "remarks": "Lashing  Materials Captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626111", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626111_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626111_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC510 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS Full PPE at wharf.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626112", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626112_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626112_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC506 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS Full PPE at wharf.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626113", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626113_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626113_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC512 WOS Cam (C) (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS Full PPE at wharf.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626114", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/valid/V1250626114_cropped_valid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/valid/V1250626114_source_valid.JPEG", "terminal": "P2", "camera_id": "QC604F (VALO)", "infringement_type": "Ex.Row Violation", "alert_status": "<PERSON><PERSON>", "remarks": "2 LS  doing unlashing together at extreme row but did not wearing life jacket and not secure his lanyard to the anchoring point", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW - Valid violation", "confidence": 0.95}, {"case_number": "V1250626115", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/valid/V1250626115_cropped_valid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/valid/V1250626115_source_valid.JPEG", "terminal": "P2", "camera_id": "QC604F (VALO)", "infringement_type": "One man Lashing", "alert_status": "<PERSON><PERSON>", "remarks": "2 LS  doing unlashing together at extreme row but did not wearing life jacket and not secure his lanyard to the anchoring point", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW - Valid violation", "confidence": 0.95}, {"case_number": "V1250626116", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626116_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626116_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC512 WOS Cam (C) (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS Full PPE at wharf.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626117", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626117_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626117_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC611 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS in full PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626118", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626118_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626118_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC506 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS Full PPE at wharf.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626119", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626119_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626119_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC528 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS Full PPE at wharf.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626120", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626120_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626120_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC610F (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Container Structure Captured As Ls", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626121", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626121_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626121_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC510 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS Full PPE at wharf.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626122", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626122_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626122_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC521F (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Vessel structure captured as LS", "is_false_positive": true, "vlm_decision": "dismissed", "vlm_response": "DISMISS", "confidence": 0.85}, {"case_number": "V1250626123", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626123_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626123_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC601 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Safety Cone Captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626124", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626124_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626124_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC611 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "wos /operator in full ppe at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626125", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626125_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626125_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC523 WOS Cam (C) (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS Full PPE at wharf.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626126", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626126_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626126_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC320 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS in full PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626127", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626127_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626127_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC523 WOS Cam (C) (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Crane structure captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626128", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/valid/V1250626128_cropped_valid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/valid/V1250626128_source_valid.JPEG", "terminal": "P2", "camera_id": "QC320 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "<PERSON><PERSON>", "remarks": "A ship chandler driver did not fasten up his safety vest and not wearing a life jacket when he was stepping beyond the life jacket zone at the gangway of the vessel.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW - Valid violation", "confidence": 0.95}, {"case_number": "V1250626129", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/valid/V1250626129_cropped_valid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/valid/V1250626129_source_valid.JPEG", "terminal": "P2", "camera_id": "QC605 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "<PERSON><PERSON>", "remarks": "Ls placed a safety cone ( in advance ) in front of PM When Qco did not fully disengage the spreader on the PM chassis..", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW - Valid violation", "confidence": 0.95}, {"case_number": "V1250626130", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626130_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626130_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC519 WOS Cam (C) (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Crane structure captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626131", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626131_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626131_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC320 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS / Driver in full PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626132", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626132_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626132_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC528 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Crane structure captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626133", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626133_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626133_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC523 WOS Cam (C) (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Crane structure captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626134", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626134_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626134_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC506 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS Full PPE at wharf.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626135", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/valid/V1250626135_cropped_valid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/valid/V1250626135_source_valid.JPEG", "terminal": "P2", "camera_id": "QC605 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "<PERSON><PERSON>", "remarks": "An unidentified guy walking along the zero lane towards QC606 without wearing any PPE", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW - Valid violation", "confidence": 0.95}, {"case_number": "V1250626136", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626136_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626136_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC306F (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "VESSEL STRUCTURE CAPTURED  AS LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626137", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626137_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626137_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC601 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "WOS IN FULL PPE AT WHARF", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626138", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626138_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626138_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC528 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Crane structure captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626139", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626139_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626139_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC306F (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "VESSEL STRUCTURE CAPTURED  AS LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626140", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626140_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626140_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC109F (VALO)", "infringement_type": "2-Container Distance", "alert_status": "Invalid", "remarks": "2 LS taking spreader ride to container top", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626141", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626141_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626141_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC109F (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "2 LS in proper PPE with life jacket on when taking spreader ride", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626142", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626142_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626142_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC523 WOS Cam (C) (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Crane structure captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626143", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626143_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626143_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC308F (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "VESSEL STRUCTURE CAPTURED  AS LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626144", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626144_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626144_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC108F (VALO)", "infringement_type": "2-Container Distance", "alert_status": "Invalid", "remarks": "2 LS taking spreader ride from container top", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626145", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626145_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626145_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC108F (VALO)", "infringement_type": "2-Container Distance", "alert_status": "Invalid", "remarks": "2 LS taking spreader ride from container top", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626146", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626146_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626146_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC510 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "WOS in proper PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626147", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626147_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626147_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC605 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS in full PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626148", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626148_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626148_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC320 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS Full PPE at wharf.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626149", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626149_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626149_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC605 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "QCO Captured As Ls During The Change Shift", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626150", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626150_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626150_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC506 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "PM structure captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626151", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626151_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626151_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC519 WOS Cam (C) (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "QCO in proper PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626152", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626152_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626152_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC510 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "<PERSON> Full PPE at wharf.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626153", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626153_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626153_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC611 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "WOS IN FULL PPE AT WHARF", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626154", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626154_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626154_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC510 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "PM structure captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626155", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626155_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626155_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC510 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "PM STRUCTURE CAPTURED  AS LS", "is_false_positive": true, "vlm_decision": "dismissed", "vlm_response": "DISMISS", "confidence": 0.85}, {"case_number": "V1250626156", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/valid/V1250626156_cropped_valid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/valid/V1250626156_source_valid.JPEG", "terminal": "P2", "camera_id": "QC320 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "<PERSON><PERSON>", "remarks": "LS passing 3 high gala from onboard vessel to wharf side (zero line)", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW - Valid violation", "confidence": 0.95}, {"case_number": "V1250626157", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626157_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626157_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC601 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "CAMERA STRUCTURE CAPTURED AS LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626158", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626158_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626158_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC615F (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS c/ STA in full PPE doing preparation or housekeeping", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626159", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626159_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626159_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC510 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "PM structure captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626160", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626160_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626160_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC601 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "WOS in full PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626161", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626161_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626161_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC509F (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Vessel structure captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626162", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626162_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626162_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC611 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "WOS IN FULL PPE AT WHARF", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626163", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626163_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626163_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC506 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "WOS / QCO IN FULL PPE AT WHARF", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626164", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626164_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626164_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC518F (VALO)", "infringement_type": "One man Lashing", "alert_status": "Invalid", "remarks": "video and image not clear", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626165", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626165_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626165_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC506 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS Full PPE at wharf.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626166", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626166_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626166_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC611 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "WOS IN FULL PPE AT WHARF", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626167", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626167_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626167_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC601 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Crane structure captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626168", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626168_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626168_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC316F (VALO)", "infringement_type": "One man Lashing", "alert_status": "Invalid", "remarks": "Ship crew captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626169", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626169_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626169_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC611 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "WOS in proper PPE at wharf", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626170", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626170_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626170_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC316F (VALO)", "infringement_type": "One man Lashing", "alert_status": "Invalid", "remarks": "LSUP / STA IN FULL PPE AND <PERSON><PERSON> ONBOARD VESSEL TAKING A FOTO STA FOR INSPECTION.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626171", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626171_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626171_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC601 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "CAMERA STRUCTURE CAPTURED AND LS DOING DECONNING OPS.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626172", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626172_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626172_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC320 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS in full PPE at wharf & doing coning ops", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626173", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626173_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626173_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC320 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS IN FULL PPE GOING TO ONBAORD VESSEL", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250626174", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626174_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250626174_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC601 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "CAMERA STRUCTURE CAPTURED AS LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250627001", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250627001_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250627001_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC601 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "WOS IN FULL PPE AT WHARF", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250627002", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250627002_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250627002_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC510 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "WOS IN FULL PPE AT WHARF", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250627003", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250627003_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250627003_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC110F (VALO)", "infringement_type": "One man Lashing", "alert_status": "Invalid", "remarks": "2 LS in full PPE doing lashing at centre row", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250627004", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250627004_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250627004_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC510 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "PM STRUCTURE CAPTURED  AS LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250627005", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250627005_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250627005_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC601 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS IS IN FULL PPE DOING CONNING OPS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250627006", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250627006_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250627006_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC104F (VALO)", "infringement_type": "Ex.Row Violation", "alert_status": "Invalid", "remarks": "No LS at the extreme row doing lashing", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250627007", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250627007_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250627007_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC601 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Crane structure captured as LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250627008", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250627008_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250627008_source_invalid.JPEG", "terminal": "P1", "camera_id": "QC104F (VALO)", "infringement_type": "2-Container Distance", "alert_status": "Invalid", "remarks": "Exception  handling jammed twistlock under supervision of berth foreman", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250627009", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250627009_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250627009_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC506 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "PM STRUCTURE CAPTURED  AS LS", "is_false_positive": true, "vlm_decision": "dismissed", "vlm_response": "DISMISS", "confidence": 0.85}, {"case_number": "V1250627010", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250627010_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250627010_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC601 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "CAMERA STRUCTURE CAPTURED AS LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250627011", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250627011_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250627011_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC605 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "CRANE STRUCTURE CAPTURED  AS LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250627012", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250627012_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250627012_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC604F (VALO)", "infringement_type": "Ex.Row Violation", "alert_status": "Invalid", "remarks": "2 LS IN FULL PPE AND THEY ARE DOING LASHING TOGETHER.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250627013", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250627013_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250627013_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC604F (VALO)", "infringement_type": "One man Lashing", "alert_status": "Invalid", "remarks": "2 LS IN FULL PPE AND THEY ARE DOING LASHING TOGETHER.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250627014", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250627014_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250627014_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC506 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "Safety cone captured as LS", "is_false_positive": true, "vlm_decision": "dismissed", "vlm_response": "DISMISS", "confidence": 0.85}, {"case_number": "V1250627015", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250627015_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250627015_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC512 WOS Cam (C) (VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS Full PPE at wharf.", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250627016", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250627016_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250627016_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC605 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "CRANE STRUCTURE CAPTURED  AS LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250627017", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250627017_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250627017_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC601 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "WOS IN FULL PPE AT WHARF", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250627018", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250627018_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250627018_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC506 WOS Cam (C) (VATO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS Full PPE At Wharf. Doing Deconning Ops", "is_false_positive": true, "vlm_decision": "dismissed", "vlm_response": "DISMISS", "confidence": 0.85}, {"case_number": "V1250627019", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250627019_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250627019_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC601 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "CAMERA STRUCTURE CAPTURED AS LS", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250627020", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250627020_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250627020_source_invalid.JPEG", "terminal": "P2", "camera_id": "QC614F (VALO)", "infringement_type": "2-Container Distance", "alert_status": "Invalid", "remarks": "Spreader Structure Captured As Ls and STA moving  did not double up", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}, {"case_number": "V1250627021", "cropped_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250627021_cropped_invalid.JPEG", "source_image": "ai_farm_images_fixed_250703/ai_farm_images_fixed/invalid/V1250627021_source_invalid.JPEG", "terminal": "P3", "camera_id": "QC510 WOS Cam (C) (VATO/VALO)", "infringement_type": "PPE Non-compliance", "alert_status": "Invalid", "remarks": "LS Full PPE At Wharf. Doing conning Ops", "is_false_positive": false, "vlm_decision": "flagged", "vlm_response": "FLAG FOR REVIEW", "confidence": 0.85}]