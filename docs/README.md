# AI-FARM Documentation

Comprehensive documentation for the AI-FARM false positive reduction system.

## Documentation Structure

### [Architecture](architecture/)
System design, data flow, and technical specifications
- **ai_farm_dev_spec.md** - Complete system specification and requirements
- High-level architecture diagrams
- Component interaction patterns
- Data pipeline documentation

### [API Documentation](api/)
REST API reference and integration guides
- Endpoint specifications
- Request/response schemas
- Authentication and rate limiting
- Integration examples

### [Development](development/)
Development environment setup and guidelines
- macOS to Ubuntu deployment workflow
- Configuration management
- Testing procedures
- Code contribution guidelines

### [Deployment](deployment/)
Production deployment guides and operations
- Ubuntu server setup
- Docker deployment
- Environment configuration
- Monitoring and maintenance

## Key Documents

### Project Overview
- **[PATTERN_ANALYSIS.md](PATTERN_ANALYSIS.md)** - Mathematical case_number ↔ pk_event pattern discovery
- **[SCRIPT_VERSION_LOG.md](SCRIPT_VERSION_LOG.md)** - Version tracking for all utility scripts

### Quick References
- **[../README.md](../README.md)** - Project overview and quick start
- **[../CLAUDE.md](../CLAUDE.md)** - Development standards and project guidance
- **[../backend/README.md](../backend/README.md)** - Backend setup and architecture
- **[../scripts/README.md](../scripts/README.md)** - Utility scripts documentation

## Business Context

AI-FARM addresses the critical problem of false positive alerts in safety monitoring systems:

### The Problem
- 97% false positive rate in current systems
- 862 hours/month wasted on manual review
- $43,100/month in salary costs
- Alert fatigue leading to missed real violations

### The Solution  
- 70% false positive reduction through VLM analysis
- $351,000 annual savings potential
- 3-4 month ROI payback period
- Focus teams on genuine safety violations

### Technical Approach
- OpenAI-compatible VLM API integration
- Auto-learning customer-specific patterns
- Real-time batch processing
- Mathematical pattern optimization
- Customer demonstration dashboards

## Target Audiences

### Developers
- Backend API development (Python/FastAPI)
- Frontend dashboard development (React/TypeScript)  
- System integration and testing
- DevOps and deployment

### Operations
- Production deployment on Ubuntu
- System monitoring and maintenance
- Customer data processing
- Performance optimization

### Business Users
- Customer demonstration preparation
- ROI calculations and business case development
- System capabilities and limitations
- Integration planning

## Development Workflow

1. **Setup**: Follow [development/setup.md](development/setup.md) for environment configuration
2. **Standards**: Adhere to guidelines in [../CLAUDE.md](../CLAUDE.md)
3. **Testing**: Use comprehensive test suite in [../backend/tests/](../backend/tests/)
4. **Deployment**: Follow [deployment/](deployment/) guides for production setup

## Getting Started

### For Developers
1. Review [architecture/ai_farm_dev_spec.md](architecture/ai_farm_dev_spec.md) for system understanding
2. Setup development environment per [development/setup.md](development/setup.md)
3. Explore API endpoints in [api/](api/) documentation
4. Run test suite to validate setup

### For Operations
1. Review [deployment/](deployment/) requirements
2. Setup Ubuntu production environment
3. Configure monitoring per operations guides
4. Test customer data processing workflows

### For Demonstrations
1. Use [../scripts/image-copy/](../scripts/image-copy/) to prepare customer data
2. Configure system per customer requirements
3. Run live demo scenarios
4. Generate ROI reports and metrics

## Support and Maintenance

### Documentation Updates
All documentation should be updated when:
- New features are added
- Configuration options change
- Deployment procedures are modified  
- API endpoints are updated

### Version Control
- Architecture changes documented in specifications
- Script versions tracked in SCRIPT_VERSION_LOG.md
- API changes reflected in endpoint documentation
- Configuration updates in setup guides

### Quality Assurance
- All code changes include documentation updates
- README files maintained for each component
- Examples tested and validated
- Business context kept current

This documentation provides comprehensive guidance for developing, deploying, and operating the AI-FARM system across all target environments.