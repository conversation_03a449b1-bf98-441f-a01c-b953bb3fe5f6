# VLM-38B-AWQ Integration Guide

This document details the integration of the VLM-38B-AWQ (Vision Language Model 38 Billion parameters with AWQ quantization) into the AI-FARM system.

## Overview

The VLM-38B-AWQ model is integrated as the primary vision-language analysis engine for detecting false positives in safety violation images. This model provides superior accuracy in distinguishing between genuine safety violations and false alerts caused by equipment misidentification.

## Configuration

### Environment Variables

Update your `.env` file with the following VLM-38B-AWQ specific settings:

```bash
# VLM-38B-AWQ API Configuration
VLM_API_BASE_URL=http://**************:9500/v1
VLM_API_KEY=token-abc123
VLM_MODEL_NAME=VLM-38B-AWQ
VLM_MAX_TOKENS=1000
VLM_TEMPERATURE=0.1
VLM_TIMEOUT_SECONDS=30
```

### Key Configuration Parameters

- **Base URL**: The endpoint URL for the VLM-38B-AWQ API service
- **API Key**: Authentication token for API access
- **Model Name**: Specific model identifier (VLM-38B-AWQ)
- **Max Tokens**: Maximum response length (default: 1000)
- **Temperature**: Controls response randomness (0.1 for consistent results)
- **Timeout**: API request timeout in seconds

## Architecture

### Integration Points

1. **VLM Service** (`backend/app/services/vlm_service.py`)
   - Handles all communication with the VLM-38B-AWQ endpoint
   - Manages image encoding and compression
   - Implements rate limiting and error handling

2. **Extended VLM Service** (`backend/app/services/vlm_service_extended.py`)
   - Provides enhanced compatibility for VLM-38B-AWQ
   - Includes endpoint validation and auto-correction
   - Improved health check functionality

3. **Batch Processing** (`backend/app/services/batch_processor.py`)
   - Utilizes VLM service for analyzing multiple images
   - Implements concurrent processing with rate limiting
   - Tracks progress and handles errors gracefully

### API Format

The VLM-38B-AWQ endpoint follows the OpenAI-compatible API format:

```python
{
    "model": "VLM-38B-AWQ",
    "messages": [
        {
            "role": "user",
            "content": [
                {
                    "type": "text",
                    "text": "Analysis prompt..."
                },
                {
                    "type": "image_url",
                    "image_url": {
                        "url": "data:image/jpeg;base64,..."
                    }
                }
            ]
        }
    ],
    "max_tokens": 1000,
    "temperature": 0.1
}
```

## Features

### 1. Image Processing
- Automatic image compression for large files (>10MB)
- Resizing to maximum 1920px dimension while maintaining aspect ratio
- Base64 encoding with JPEG compression (quality=85)

### 2. Rate Limiting
- Configurable concurrent request limits (default: 3)
- Async semaphore-based rate limiting
- Prevents API overload and ensures stable performance

### 3. Error Handling
- Comprehensive error catching and logging
- Fallback parsing for non-JSON responses
- Graceful degradation for failed analyses

### 4. Custom Prompts
- Support for customer-specific analysis prompts
- Pattern-based prompt generation
- Adaptable to different industry contexts

## Usage Examples

### Basic Image Analysis

```python
from app.services.vlm_service import vlm_service

# Analyze a single image
result = await vlm_service.analyze_image(
    image_path="path/to/image.jpg",
    case_number="V1250630118"
)

print(f"Detection Type: {result.detection_type}")
print(f"False Positive Likelihood: {result.false_positive_likelihood}%")
print(f"Recommendation: {result.recommendation}")
```

### Batch Processing

```python
# Analyze multiple images
results = await vlm_service.analyze_batch(
    image_paths=["image1.jpg", "image2.jpg", "image3.jpg"],
    case_numbers=["CASE-001", "CASE-002", "CASE-003"]
)

for i, result in enumerate(results):
    print(f"Image {i+1}: {result.detection_type} - {result.recommendation}")
```

### Custom Prompt Generation

```python
# Generate customer-specific prompt
customer_patterns = {
    'unique_crane_types': ['gantry crane', 'mobile crane'],
    'vessel_configurations': ['container ship', 'tanker'],
    'most_common_false_positive': 'crane structures'
}

custom_prompt = vlm_service.generate_custom_prompt(customer_patterns)
result = await vlm_service.analyze_image(
    image_path="image.jpg",
    case_number="CASE-001",
    custom_prompt=custom_prompt
)
```

## Testing

### Running Integration Tests

```bash
# Run VLM connectivity tests
cd backend
export $(cat ../.env | grep ^VLM_ | xargs)
python3 tests/test_vlm_connectivity.py

# Run comprehensive integration tests
python3 -m pytest tests/test_vlm_38b_integration.py -v
```

### Test Coverage

The integration includes comprehensive tests for:
- Service initialization and configuration
- Single image analysis
- Batch processing
- Large image handling and compression
- Error handling and network failures
- Custom prompt generation
- Rate limiting
- Text response parsing fallback

## Performance Considerations

### Response Times
- Average response time: 3-5 seconds per image
- Batch processing: Concurrent with rate limiting
- Health check response: <500ms

### Optimization Tips
1. Use batch processing for multiple images
2. Enable image compression for large files
3. Adjust concurrent request limits based on load
4. Monitor API usage and response times

## Troubleshooting

### Common Issues

1. **Connection Failed**
   - Verify VLM endpoint is accessible
   - Check network connectivity
   - Confirm API key is correct

2. **404 Errors**
   - Ensure base URL ends with `/v1`
   - Use the endpoint validation tool
   - Check API documentation for correct paths

3. **Slow Response Times**
   - Reduce image size before sending
   - Lower concurrent request limit
   - Check network latency

### Debug Mode

Enable debug logging for detailed troubleshooting:

```python
import logging
logging.getLogger("app.services.vlm_service").setLevel(logging.DEBUG)
```

## Security Considerations

1. **API Key Management**
   - Store API key in environment variables
   - Never commit API keys to version control
   - Rotate keys regularly

2. **Data Privacy**
   - Images are processed in memory
   - No permanent storage of customer images
   - Secure transmission over HTTPS (when available)

3. **Access Control**
   - API key authentication required
   - Rate limiting prevents abuse
   - Logging for audit trails

## Future Enhancements

1. **Caching Layer**
   - Redis integration for response caching
   - Duplicate image detection
   - Reduced API calls for repeated analyses

2. **Advanced Analytics**
   - Confidence score calibration
   - Pattern learning from results
   - Custom model fine-tuning support

3. **Monitoring Integration**
   - Prometheus metrics export
   - Response time tracking
   - Error rate monitoring

## Support

For issues with the VLM-38B-AWQ integration:
1. Check the troubleshooting section above
2. Review logs in `backend/logs/`
3. Run the connectivity test script
4. Contact the VLM API support team if needed

## References

- [OpenAI API Compatibility Guide](https://platform.openai.com/docs/api-reference)
- [AI-FARM Architecture Documentation](./AI-FARM-ARCHITECTURE-DIAGRAMS.md)
- [VLM Service Source Code](../backend/app/services/vlm_service.py)