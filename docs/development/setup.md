# Development Setup Guide

Complete setup guide for AI-FARM development from macOS to Ubuntu deployment.

## Overview

This guide covers:
- macOS development environment setup
- Ubuntu production deployment preparation
- Cross-platform considerations
- Configuration management

## Prerequisites

### macOS Development
- **Python 3.8+** (recommend using pyenv)
- **Node.js 16+** and npm (for frontend)
- **Git** for version control
- **Docker Desktop** (optional, for containerized testing)
- **VS Code** or preferred IDE

### Ubuntu Production Target
- **Ubuntu 20.04+ LTS**
- **Python 3.8+**
- **Node.js 16+**
- **Docker and docker-compose**
- **Nginx** (for reverse proxy)
- **systemd** (for service management)

## Development Environment Setup

### 1. Repository Setup

```bash
# Clone repository
git clone <repository-url>
cd VALO_AI-FARM_2025

# Verify structure
ls -la
# Should see: backend/, frontend/, scripts/, docs/, CLAUDE.md, etc.
```

### 2. Backend Setup (Python/FastAPI)

```bash
cd backend

# Create virtual environment (recommended approach)
python3 -m venv venv
source venv/bin/activate

# Upgrade pip
pip install --upgrade pip

# Install dependencies
pip install -r requirements.txt

# Verify installation
python -c "import fastapi; print('FastAPI installed successfully')"
```

### 3. Environment Configuration

```bash
# Copy environment template
cp .env.example .env

# Edit configuration (use your preferred editor)
nano .env
```

**Required Configuration:**
```bash
# OpenAI API Settings (update with your values)
VLM_API_BASE_URL=https://api.openai.com
VLM_API_KEY=your-openai-api-key-here
VLM_MODEL_NAME=gpt-4.1-2025-04-14

# Development settings
DEBUG=true
LOG_LEVEL=DEBUG
HOST=127.0.0.1
PORT=8000

# Database (SQLite for development)
DATABASE_URL=sqlite:///./ai_farm_dev.db
```

### 4. Database Setup

```bash
# Initialize database (automatic on first run)
python -c "from app.core.database import init_db; init_db()"

# Verify database creation
ls -la *.db
```

### 5. Run Development Server

```bash
# Start backend server
python run.py

# Server should start at http://127.0.0.1:8000
# API docs available at http://127.0.0.1:8000/docs
```

### 6. Test Backend Setup

```bash
# Run health check
curl http://127.0.0.1:8000/health

# Expected response:
# {"status":"healthy","timestamp":"...","version":"1.0.0"}

# Run test suite
python run_tests.py

# Expected: All tests pass with 90%+ coverage
```

### 7. Frontend Setup (React/TypeScript)

```bash
cd ../frontend

# Install Node.js dependencies
npm install

# Start development server
npm start

# Frontend should open at http://localhost:3000
```

## Configuration Management

### Environment Variables

The application uses environment-based configuration with multiple levels:

1. **`.env.example`** - Template with all available options
2. **`.env`** - Local development configuration (git-ignored)
3. **Production** - Server environment variables

### Key Configuration Areas

#### OpenAI API Integration
```bash
VLM_API_BASE_URL=https://api.openai.com
VLM_API_KEY=your-openai-api-key
VLM_MODEL_NAME=gpt-4.1-2025-04-14
VLM_MAX_TOKENS=1000
VLM_TEMPERATURE=0.1
```

#### Processing Thresholds (Customer Adjustable)
```bash
THRESHOLD_STRUCTURE_MISID=70
THRESHOLD_PROPER_PPE=65
THRESHOLD_NO_VIOLATION=75
THRESHOLD_DEFAULT=70
```

#### Performance Settings
```bash
BATCH_SIZE=10
MAX_CONCURRENT_REQUESTS=3
PROCESSING_TIMEOUT_MINUTES=60
WORKER_PROCESSES=4
```

## Cross-Platform Considerations

### Path Differences
- **macOS**: Use forward slashes, case-sensitive filesystem
- **Ubuntu**: Use forward slashes, case-sensitive filesystem  
- **File permissions**: Ensure scripts are executable (`chmod +x`)

### Python Environment
```bash
# macOS
python3 -m venv venv
source venv/bin/activate

# Ubuntu (same commands)
python3 -m venv venv
source venv/bin/activate
```

### System Dependencies
```bash
# Ubuntu additional packages
sudo apt update
sudo apt install python3-dev python3-venv build-essential

# For image processing
sudo apt install libjpeg-dev libpng-dev
```

## Production Deployment Preparation

### 1. Environment Variables for Production

Create production environment file:
```bash
# .env.production
VLM_API_BASE_URL=https://production-vlm-api
VLM_API_KEY=production-api-key
DATABASE_URL=postgresql://user:password@localhost/ai_farm
DEBUG=false
LOG_LEVEL=INFO
HOST=0.0.0.0
PORT=8000
```

### 2. Docker Setup (Optional)

```bash
# Build backend image
cd backend
docker build -t ai-farm-backend .

# Test locally
docker run -p 8000:8000 --env-file .env ai-farm-backend
```

### 3. Database Migration for Production

```bash
# For PostgreSQL production database
pip install psycopg2-binary

# Update DATABASE_URL in .env
DATABASE_URL=postgresql://user:password@localhost/ai_farm

# Initialize production database
python -c "from app.core.database import init_db; init_db()"
```

## Development Workflow

### 1. Daily Development
```bash
# Start development session
cd backend
source venv/bin/activate
python run.py

# In another terminal for frontend
cd frontend
npm start
```

### 2. Code Changes
- Follow standards in [../CLAUDE.md](../CLAUDE.md)
- Run tests before committing: `python run_tests.py`
- Update documentation for API changes
- Test cross-platform compatibility

### 3. Testing
```bash
# Run full test suite
python run_tests.py

# Run specific test categories
python run_tests.py unit
python run_tests.py integration
python run_tests.py vlm

# Generate coverage report
python run_tests.py --coverage
```

## Troubleshooting

### Common Issues

#### VLM API Connection
```bash
# Test API connectivity
curl -H "Authorization: Bearer $VLM_API_KEY" $VLM_API_BASE_URL/health

# Check configuration
python -c "from app.core.config import settings; print(settings.vlm_api_base_url)"
```

#### Database Issues
```bash
# Reset development database
rm ai_farm_dev.db
python -c "from app.core.database import init_db; init_db()"
```

#### Permission Issues (Ubuntu)
```bash
# Fix script permissions
chmod +x scripts/image-copy/*.sh

# Fix Python virtual environment
sudo chown -R $USER:$USER venv/
```

#### Port Conflicts
```bash
# Check what's using port 8000
lsof -i :8000

# Use different port
export PORT=8001
python run.py
```

### Environment-Specific Issues

#### macOS Specific
- Install Xcode command line tools: `xcode-select --install`
- Use Homebrew for system dependencies: `brew install python`

#### Ubuntu Specific
- Install system Python dependencies: `sudo apt install python3-dev`
- Configure firewall: `sudo ufw allow 8000`

## IDE Configuration

### VS Code Recommended Extensions
- Python
- Pylance (Python language server)
- Thunder Client (API testing)
- Docker
- GitLens

### VS Code Settings
```json
{
    "python.defaultInterpreterPath": "./backend/venv/bin/python",
    "python.linting.enabled": true,
    "python.linting.pylintEnabled": true,
    "python.formatting.provider": "black"
}
```

## Next Steps

After completing setup:
1. **Review Architecture**: Read [../architecture/ai_farm_dev_spec.md](../architecture/ai_farm_dev_spec.md)
2. **Explore APIs**: Visit http://127.0.0.1:8000/docs for interactive API documentation  
3. **Run Image Scripts**: Test with [../../scripts/image-copy/](../../scripts/image-copy/) scripts
4. **Configure VLM API**: Set up your actual VLM endpoint
5. **Prepare Demo Data**: Use VALO scripts to extract customer images

The development environment is now ready for AI-FARM development and testing.