# AI-FARM Script Version Log

## Version: 20250630_213833

### Purpose
Image copy script for AI-FARM POC - extracts and renames VALO system images for VLM processing.

### Files Created
- `copy_images_20250630_213833.sh` - Main bash script
- `copy_images_usage_20250630_213833.md` - Usage documentation

### Script Functionality
**Primary Purpose**: Copy safety violation images from VALO production server to temporary directory for AI-FARM processing.

**Key Features**:
- Processes CSV data: `VALO_SQL_DATA_250630.csv`
- Source path: `/video/data/VALO2SENFE1b/events/{event_id}/events_frames/`
- Destination: `/tmp/ai_farm_images/{valid|invalid}/`
- Naming convention: `{case_number}_{image_type}_{validation_status}.JPEG`

### Business Context
- **Problem Solved**: Prepare customer's actual safety violation data for AI-FARM demo
- **Customer Value**: Use real customer data to demonstrate 70% false positive reduction
- **Demo Impact**: Live processing of customer's own violation images during presentation

### Technical Specifications
- **Environment**: RedHat production server (air-gapped)
- **Dependencies**: None (pure bash)
- **Storage Required**: ~875MB (2,499 images estimated)
- **Image Format**: JPEG 1920x1080 resolution
- **Processing**: Both source and cropped images per case

### Data Mapping
```
CSV Structure:
pk_event,case_number,url,key
143881,V1250630118,/path/to/image,invalid

Output Naming:
V1250630118_source_invalid.JPEG
V1250630118_cropped_invalid.JPEG
```

### Validation Status
- `valid` - Confirmed safety violations
- `invalid` - False positive alerts
- Future: `invalid_false_positive` (manual classification)

### Safety Measures
- Comprehensive error logging
- Missing file handling
- Storage usage monitoring
- Progress tracking
- Non-destructive copying (original files preserved)

### Usage Context
This script is part of the AI-FARM POC demonstration pipeline:
1. **Extract** - This script copies customer images
2. **Process** - VLM analyzes images for false positives  
3. **Demo** - Live results shown to customer
4. **ROI** - Calculate savings based on actual data

### Next Steps After Execution
1. Archive copied images: `tar czf ai_farm_images.tar.gz ai_farm_images/`
2. Transfer to AI-FARM processing system
3. Load into VLM processing pipeline
4. Generate customer-specific demo results

### Created By
AI-FARM Development Team - Initial version for VALO customer demo preparation.

### Version Control
- Created: 2025-06-30 21:38:33
- CSV Data: VALO_SQL_DATA_250630.csv (2,500 records)
- Target Demo: Customer false positive reduction demonstration

---

## Version: 20250630_214855

### Purpose
Enhanced image copy script - works with case numbers only (no CSV required).

### Files Created
- `copy_images_by_case_20250630_214855.sh` - Case number input script
- `copy_images_by_case_usage_20250630_214855.md` - Usage documentation

### Script Functionality
**Primary Purpose**: Copy safety violation images using case numbers only, leveraging mathematical pattern discovery.

**Key Improvements**:
- **No CSV Required**: Uses pk_event calculation pattern
- **Flexible Input**: Single case, comma-separated batch, or file input
- **Mathematical Calculation**: Automatic pk_event generation from case numbers
- **Selective Processing**: Process only specific cases of interest

### Mathematical Pattern Integration
```bash
# June 23, 2025: pk_event = suffix + 142502
# June 30, 2025: pk_event = suffix + 143763
V1250630118 → 118 + 143763 = 143881
```

### Usage Examples
```bash
# Single case
./copy_images_by_case_20250630_214855.sh V1250630118

# Multiple cases
./copy_images_by_case_20250630_214855.sh "V1250630118,V1250630119,V1250630120"

# From file
./copy_images_by_case_20250630_214855.sh -f case_list.txt
```

### Business Benefits
- **Rapid Demo Prep**: Process specific customer cases quickly
- **No Database Dependency**: Works without SQL access
- **Flexible Analysis**: Target specific violation patterns
- **Development Efficiency**: Test with selected data subsets

### Output Structure
- Destination: `/tmp/ai_farm_images_by_case/`
- Status: `unknown` (requires manual classification)
- Format: `{case_number}_{image_type}_unknown.JPEG`

### Use Cases
1. **Selective Customer Demos**: Focus on specific violation types
2. **Development Testing**: Process targeted data subsets  
3. **Troubleshooting**: Analyze individual problematic cases
4. **Custom Analysis**: Study specific case patterns

### Technical Specifications
- **Dependencies**: None (pure bash)
- **Environment**: Air-gapped RedHat server compatible
- **Input Validation**: Case number format checking
- **Error Handling**: Comprehensive logging and validation
- **Pattern Support**: June 23 & June 30, 2025 date ranges

### Created By
AI-FARM Development Team - Enhanced version for flexible case-based processing.

### Version Control
- Created: 2025-06-30 21:48:55
- Pattern Base: Mathematical pk_event calculation
- Target Use: Selective case processing and demo preparation