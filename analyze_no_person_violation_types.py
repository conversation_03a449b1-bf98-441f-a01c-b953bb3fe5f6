#!/usr/bin/env python3
"""
Analyze what types of violations occur without a person present
"""

import pandas as pd
import json
import re
from collections import Counter

# Load the CSV to understand violation types
df = pd.read_csv('ai_farm_images_fixed_250703/psa_valo_violation_report_without_images_csv_2025-07-03_14-14-01.CSV')

# Get violation type distribution
print("📊 Violation Type Distribution in Dataset:")
print("="*50)
violation_counts = df['Type of Infringement'].value_counts()
print(violation_counts)

# Load the no-person analysis results
with open('no_person_filtering_analysis.json', 'r') as f:
    analysis = json.load(f)

print(f"\n🔍 Analysis of No-Person Cases:")
print(f"- No-person TRUE POSITIVES: {analysis['summary']['true_positives']['no_person']} cases")
print(f"- These are VALID violations that don't have a person in frame")

# Check specific violation types from CSV
print("\n📋 Sample Valid Cases by Violation Type:")
print("="*50)

# Filter for valid cases (not Invalid status)
valid_cases = df[df['Alert Status'] != 'Invalid']
print(f"\nTotal valid cases in CSV: {len(valid_cases)}")
print("\nValid case violation types:")
print(valid_cases['Type of Infringement'].value_counts())

# Sample some valid cases to understand
print("\n📝 Sample Valid Cases (first 10):")
for idx, row in valid_cases.head(10).iterrows():
    print(f"- {row['Case Int. ID']}: {row['Type of Infringement']} - {row['Remarks']}")

print("\n💡 Key Insight:")
print("The dataset contains equipment/structural violations that are valid")
print("even without a person present, such as:")
print("- Container distance violations")
print("- Equipment placement issues")  
print("- Structural safety violations")
print("\nThe 'no person = FP' rule would incorrectly filter these valid violations!")