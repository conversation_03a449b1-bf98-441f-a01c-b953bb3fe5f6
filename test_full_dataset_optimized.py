#!/usr/bin/env python3
"""
Optimized full dataset test - Tests all 1250 cases efficiently
With better error handling and concurrent processing
"""

import json
import asyncio
import aiohttp
import logging
import base64
from datetime import datetime
from typing import Dict, List, Optional
import time
import os

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(message)s'
)
logger = logging.getLogger(__name__)


class OptimizedFullTester:
    """Optimized tester for full dataset"""
    
    def __init__(self):
        self.vlm_endpoint = "http://100.106.127.35:9500/v1/chat/completions"
        self.model = "VLM-38B-AWQ"
        
        # Only test the most promising approaches
        self.approaches = {
            'assumption_based': """SAFETY ALERT ANALYSIS
DEFAULT ASSUMPTION: This is a false positive (97% probability)
To override this assumption, you need CLEAR evidence:
- Person clearly visible AND
- No helmet visible AND
- No safety vest visible

Is this a FALSE POSITIVE? YES/NO""",
            
            'alert_fatigue_prevention': """ALERT FATIGUE PREVENTION MODE
Too many false alerts = ignored real violations
Help reduce false alerts by being practical.
Mark as FALSE POSITIVE unless you see:
- Obvious person
- Clearly missing PPE
- No doubt whatsoever
Is this a FALSE POSITIVE? YES/NO"""
        }
    
    async def analyze_with_retry(self, session: aiohttp.ClientSession,
                                case: Dict, prompt: str, 
                                max_retries: int = 3) -> Optional[bool]:
        """Analyze with retry logic"""
        for attempt in range(max_retries):
            try:
                # Check if image exists
                if not os.path.exists(case['cropped_image']):
                    logger.warning(f"Image not found: {case['cropped_image']}")
                    return None
                
                with open(case['cropped_image'], 'rb') as f:
                    image_data = base64.b64encode(f.read()).decode('utf-8')
                
                payload = {
                    "model": self.model,
                    "messages": [{
                        "role": "user",
                        "content": [
                            {"type": "text", "text": prompt},
                            {"type": "image_url", 
                             "image_url": {"url": f"data:image/jpeg;base64,{image_data}"}}
                        ]
                    }],
                    "temperature": 0.1,
                    "max_tokens": 50
                }
                
                timeout = aiohttp.ClientTimeout(total=30)
                async with session.post(self.vlm_endpoint, json=payload, 
                                      timeout=timeout) as response:
                    if response.status == 200:
                        result = await response.json()
                        content = result['choices'][0]['message']['content'].upper()
                        return "YES" in content[:50] or "FALSE POSITIVE" in content
                    elif response.status == 429:  # Rate limit
                        await asyncio.sleep(2 ** attempt)  # Exponential backoff
                    else:
                        logger.warning(f"API error {response.status} for {case['case_number']}")
                        return None
                        
            except asyncio.TimeoutError:
                logger.warning(f"Timeout for {case['case_number']} (attempt {attempt+1})")
                await asyncio.sleep(1)
            except Exception as e:
                logger.error(f"Error for {case['case_number']}: {str(e)[:50]}")
                return None
        
        return None
    
    async def process_batch(self, session: aiohttp.ClientSession,
                          batch: List[Dict], prompt: str) -> List[Dict]:
        """Process a batch of cases concurrently"""
        tasks = []
        for case in batch:
            task = self.analyze_with_retry(session, case, prompt)
            tasks.append(task)
        
        predictions = await asyncio.gather(*tasks)
        
        results = []
        for case, prediction in zip(batch, predictions):
            if prediction is not None:
                results.append({
                    'case_number': case['case_number'],
                    'actual_fp': case['is_false_positive'],
                    'predicted_fp': prediction,
                    'correct': case['is_false_positive'] == prediction
                })
        
        return results
    
    async def test_approach_optimized(self, approach_name: str, 
                                    prompt: str, test_cases: List[Dict],
                                    batch_size: int = 20) -> Dict:
        """Test approach with optimized batching"""
        logger.info(f"\nTesting {approach_name} on {len(test_cases)} cases...")
        start_time = time.time()
        
        all_results = []
        processed = 0
        errors = 0
        
        # Create connection pool for better performance
        connector = aiohttp.TCPConnector(limit=30, limit_per_host=10)
        timeout = aiohttp.ClientTimeout(total=300, connect=30)
        
        async with aiohttp.ClientSession(connector=connector, timeout=timeout) as session:
            for i in range(0, len(test_cases), batch_size):
                batch = test_cases[i:i+batch_size]
                
                try:
                    batch_results = await self.process_batch(session, batch, prompt)
                    all_results.extend(batch_results)
                    processed += len(batch)
                    errors += len(batch) - len(batch_results)
                    
                except Exception as e:
                    logger.error(f"Batch error: {e}")
                    errors += len(batch)
                    processed += len(batch)
                
                # Progress update
                if processed % 100 == 0:
                    elapsed = time.time() - start_time
                    rate = processed / elapsed if elapsed > 0 else 0
                    eta = (len(test_cases) - processed) / rate if rate > 0 else 0
                    
                    # Current metrics
                    if all_results:
                        tp = sum(1 for r in all_results if r['actual_fp'] and r['predicted_fp'])
                        fp_total = sum(1 for r in all_results if r['actual_fp'])
                        fp_rate = (tp / fp_total * 100) if fp_total > 0 else 0
                    else:
                        fp_rate = 0
                    
                    logger.info(f"  Progress: {processed}/{len(test_cases)} | "
                               f"FP: {fp_rate:.1f}% | "
                               f"Errors: {errors} | "
                               f"Rate: {rate:.1f}/s | "
                               f"ETA: {eta/60:.1f}m")
                
                # Rate limiting
                await asyncio.sleep(0.5)
        
        # Calculate final metrics
        duration = time.time() - start_time
        
        if not all_results:
            return {
                'approach': approach_name,
                'error': 'No successful results',
                'total_cases': len(test_cases),
                'errors': errors,
                'duration': duration
            }
        
        # Calculate metrics
        tp = sum(1 for r in all_results if r['actual_fp'] and r['predicted_fp'])
        tn = sum(1 for r in all_results if not r['actual_fp'] and not r['predicted_fp'])
        fp = sum(1 for r in all_results if not r['actual_fp'] and r['predicted_fp'])
        fn = sum(1 for r in all_results if r['actual_fp'] and not r['predicted_fp'])
        
        fp_total = sum(1 for r in all_results if r['actual_fp'])
        valid_total = sum(1 for r in all_results if not r['actual_fp'])
        
        return {
            'approach': approach_name,
            'total_cases': len(test_cases),
            'successful_cases': len(all_results),
            'errors': errors,
            'error_rate': (errors / len(test_cases) * 100),
            'duration': duration,
            'cases_per_second': len(all_results) / duration,
            
            'fp_detection_rate': (tp / fp_total * 100) if fp_total > 0 else 0,
            'valid_protection_rate': (tn / valid_total * 100) if valid_total > 0 else 100,
            'overall_accuracy': ((tp + tn) / len(all_results) * 100),
            
            'confusion_matrix': {
                'tp': tp, 'tn': tn, 'fp': fp, 'fn': fn
            },
            
            'detailed_stats': {
                'total_fp_in_results': fp_total,
                'total_valid_in_results': valid_total,
                'correctly_identified_fp': tp,
                'correctly_protected_valid': tn
            }
        }


async def main():
    """Run optimized full dataset test"""
    print("\n" + "="*70)
    print("OPTIMIZED FULL DATASET TEST - ALL 1250 CASES")
    print("="*70)
    
    # Load the corrected dataset
    logger.info("\nLoading dataset...")
    
    # First, let's check what data we actually have
    with open('valo_batch_round3_complete.json', 'r') as f:
        data = json.load(f)
    
    # The stats show the real numbers
    real_stats = data['stats']
    logger.info(f"\nActual dataset statistics from file:")
    logger.info(f"Total cases: {real_stats['total_cases']}")
    logger.info(f"Valid cases (real violations): {real_stats['valid_cases_total']}")
    logger.info(f"FP cases (false positives): {real_stats['fp_cases_total']}")
    logger.info(f"FP percentage: {real_stats['fp_cases_total']/real_stats['total_cases']*100:.1f}%")
    
    # Load test cases with CORRECT interpretation
    test_cases = []
    for case in data['results']:
        test_cases.append({
            'case_number': case['case_number'],
            'cropped_image': case['cropped_image'],
            'is_false_positive': case['is_false_positive']  # This is already correct
        })
    
    # Verify our loading
    fp_count = sum(1 for c in test_cases if c['is_false_positive'])
    valid_count = sum(1 for c in test_cases if not c['is_false_positive'])
    
    logger.info(f"\nLoaded cases verification:")
    logger.info(f"Total: {len(test_cases)}")
    logger.info(f"False positives: {fp_count} ({fp_count/len(test_cases)*100:.1f}%)")
    logger.info(f"Valid violations: {valid_count} ({valid_count/len(test_cases)*100:.1f}%)")
    
    # Initialize tester
    tester = OptimizedFullTester()
    results = {}
    
    # Test approaches
    for approach_name, prompt in tester.approaches.items():
        result = await tester.test_approach_optimized(
            approach_name, prompt, test_cases, batch_size=20
        )
        results[approach_name] = result
        
        # Save intermediate results
        with open(f'full_test_{approach_name}_complete.json', 'w') as f:
            json.dump(result, f, indent=2)
        
        logger.info(f"\n{approach_name} Complete:")
        logger.info(f"  FP Detection: {result.get('fp_detection_rate', 0):.2f}%")
        logger.info(f"  Valid Protection: {result.get('valid_protection_rate', 0):.2f}%")
        logger.info(f"  Overall Accuracy: {result.get('overall_accuracy', 0):.2f}%")
        logger.info(f"  Success Rate: {result.get('successful_cases', 0)}/{result['total_cases']}")
        logger.info(f"  Time: {result['duration']:.1f}s ({result.get('cases_per_second', 0):.1f} cases/s)")
        
        await asyncio.sleep(5)
    
    # Final report
    print("\n" + "="*70)
    print("FINAL RESULTS - FULL 1250 CASE DATASET")
    print("="*70)
    
    # Compare approaches
    print("\nApproach Comparison:")
    print("─"*60)
    print(f"{'Approach':<25} {'FP Detection':<15} {'Valid Protection':<18} {'Accuracy'}")
    print("─"*60)
    
    for name, result in results.items():
        if 'fp_detection_rate' in result:
            print(f"{name:<25} {result['fp_detection_rate']:>10.2f}%    "
                  f"{result['valid_protection_rate']:>10.2f}%        "
                  f"{result['overall_accuracy']:>6.2f}%")
        else:
            print(f"{name:<25} ERROR - {result.get('error', 'Unknown error')}")
    
    # Production estimates
    print("\n" + "="*70)
    print("PRODUCTION PERFORMANCE ESTIMATES")
    print("="*70)
    
    print("\nWith 15% degradation:")
    for name, result in results.items():
        if 'fp_detection_rate' in result:
            prod_estimate = result['fp_detection_rate'] * 0.85
            print(f"  {name}: {prod_estimate:.1f}% FP detection")
    
    print("\nWith 20% degradation (conservative):")
    for name, result in results.items():
        if 'fp_detection_rate' in result:
            prod_estimate = result['fp_detection_rate'] * 0.80
            print(f"  {name}: {prod_estimate:.1f}% FP detection")
    
    # Save comprehensive report
    final_report = {
        'test_date': datetime.now().isoformat(),
        'dataset': {
            'total_cases': len(test_cases),
            'false_positives': fp_count,
            'valid_violations': valid_count,
            'fp_percentage': fp_count/len(test_cases)*100
        },
        'results': results,
        'production_estimates': {
            'optimistic_15pct': {name: result.get('fp_detection_rate', 0) * 0.85 
                                for name, result in results.items()},
            'conservative_20pct': {name: result.get('fp_detection_rate', 0) * 0.80 
                                  for name, result in results.items()}
        }
    }
    
    with open('FINAL_OPTIMIZED_FULL_TEST_RESULTS.json', 'w') as f:
        json.dump(final_report, f, indent=2)
    
    print("\n" + "="*70)
    print("TEST COMPLETE")
    print("Results saved to: FINAL_OPTIMIZED_FULL_TEST_RESULTS.json")
    print("="*70)


if __name__ == "__main__":
    asyncio.run(main())