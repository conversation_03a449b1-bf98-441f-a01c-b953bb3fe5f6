#!/usr/bin/env python3
"""
Test improved approaches based on failure analysis
"""

import json
import base64
import requests
import os
import time
from datetime import datetime

class ImprovedApproachTester:
    def __init__(self):
        self.vlm_url = "http://**************:9500/v1/chat/completions"
        self.vlm_model = "VLM-38B-AWQ"
        self.session = requests.Session()
        
    def encode_image(self, image_path):
        """Encode image to base64"""
        try:
            if os.path.exists(image_path):
                with open(image_path, 'rb') as f:
                    return base64.b64encode(f.read()).decode('utf-8')
        except:
            pass
        return None
    
    def test_two_step_descriptive(self, case):
        """Two-step approach with descriptive first step"""
        prompt = """Look at these safety monitoring images and answer:

1. What is the main subject? (e.g., "crane", "vessel", "person", "truck", "spreader", "container yard equipment")
2. Are there any people visible? (YES/NO)
3. If people are visible, are they wearing safety gear (helmet AND vest)? (YES/NO/NA)

Based on your answers:
- If main subject is equipment/structure with NO people → FALSE POSITIVE
- If people visible with complete safety gear → FALSE POSITIVE  
- Otherwise → POTENTIAL VIOLATION

Final decision: Is this a FALSE POSITIVE? (YES/NO)"""
        
        return self._query_vlm(case, prompt, max_tokens=200)
    
    def test_structured_analysis(self, case):
        """Structured analysis with clear categories"""
        prompt = """Analyze these safety alert images step by step:

IDENTIFY the main content:
□ Industrial equipment only (crane/vessel/truck/spreader)
□ Person(s) in the scene
□ Both equipment and people

IF PEOPLE are present, check:
□ Helmet visible: YES/NO
□ High-vis vest visible: YES/NO

CLASSIFY as:
- FALSE POSITIVE if: Equipment only OR Person with helmet+vest
- VALID ALERT if: Person without complete PPE

Your classification: FALSE POSITIVE or VALID ALERT?"""
        
        return self._query_vlm(case, prompt, max_tokens=150)
    
    def test_complex_with_proper_tokens(self, case):
        """Test the original complex prompt with adequate tokens"""
        # Load the complex prompt
        if os.path.exists('FINAL_PRODUCTION_PROMPT.txt'):
            with open('FINAL_PRODUCTION_PROMPT.txt', 'r') as f:
                prompt = f.read()
        else:
            prompt = """[Complex prompt would be loaded here]"""
        
        return self._query_vlm(case, prompt, max_tokens=800)
    
    def _query_vlm(self, case, prompt, max_tokens):
        """Query VLM with given prompt"""
        source_b64 = self.encode_image(case['source_image'])
        cropped_b64 = self.encode_image(case['cropped_image'])
        
        if not source_b64 or not cropped_b64:
            return None
        
        payload = {
            "model": self.vlm_model,
            "messages": [{
                "role": "user",
                "content": [
                    {"type": "text", "text": prompt},
                    {"type": "text", "text": "\n\nSOURCE IMAGE:"},
                    {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{source_b64}"}},
                    {"type": "text", "text": "\n\nCROPPED IMAGE:"},
                    {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{cropped_b64}"}}
                ]
            }],
            "temperature": 0.1,
            "max_tokens": max_tokens
        }
        
        try:
            response = self.session.post(self.vlm_url, json=payload, timeout=45)
            if response.status_code == 200:
                return response.json()['choices'][0]['message']['content']
        except:
            pass
        
        return None
    
    def parse_response(self, response, approach):
        """Parse response to determine if it's a false positive"""
        if not response:
            return None
        
        response_upper = response.upper()
        
        # Look for explicit decisions
        if "FALSE POSITIVE" in response_upper:
            if "YES" in response_upper or "FALSE POSITIVE:" in response_upper:
                return True
        
        if "VALID ALERT" in response_upper or "VALID VIOLATION" in response_upper:
            return False
        
        # Fallback parsing
        if approach == "two_step":
            # If it mentions equipment and no people
            if any(equip in response_upper for equip in ["CRANE", "VESSEL", "TRUCK", "SPREADER"]):
                if "NO PEOPLE" in response_upper or "NO" in response_upper.split("PEOPLE")[-1][:20]:
                    return True
        
        return False
    
    def test_approaches_on_sample(self, sample_size=50):
        """Test different approaches on a sample"""
        print("="*80)
        print("TESTING IMPROVED APPROACHES")
        print("="*80)
        
        # Load test data
        with open('valo_batch_round3_complete.json', 'r') as f:
            all_cases = json.load(f)['results'][:sample_size]
        
        approaches = {
            'two_step_descriptive': {
                'name': 'Two-Step Descriptive',
                'func': self.test_two_step_descriptive,
                'results': []
            },
            'structured_analysis': {
                'name': 'Structured Analysis', 
                'func': self.test_structured_analysis,
                'results': []
            }
        }
        
        # Test each approach
        for approach_key, approach_info in approaches.items():
            print(f"\nTesting: {approach_info['name']}")
            print("-" * 40)
            
            start_time = time.time()
            
            for i, case in enumerate(all_cases):
                if i % 10 == 0:
                    print(f"Progress: {i}/{len(all_cases)}")
                
                response = approach_info['func'](case)
                
                if response:
                    predicted_fp = self.parse_response(response, approach_key)
                    actual_fp = case['is_false_positive']
                    
                    result = {
                        'case_number': case['case_number'],
                        'correct': predicted_fp == actual_fp if predicted_fp is not None else False,
                        'predicted_fp': predicted_fp,
                        'actual_fp': actual_fp,
                        'response_length': len(response),
                        'response_sample': response[:200]
                    }
                    
                    approach_info['results'].append(result)
                    
                    # Show sample
                    if i < 3:
                        status = "✓" if result['correct'] else "✗"
                        print(f"  {status} {case['case_number']}: {response[:100]}...")
                
                time.sleep(0.5)  # Rate limiting
            
            # Calculate metrics
            results = approach_info['results']
            if results:
                correct = sum(r['correct'] for r in results)
                accuracy = correct / len(results) * 100
                
                print(f"\n{approach_info['name']} Results:")
                print(f"  Accuracy: {accuracy:.1f}% ({correct}/{len(results)})")
                print(f"  Time: {(time.time() - start_time)/60:.1f} minutes")
        
        # Save comparison
        comparison = {
            'timestamp': datetime.now().isoformat(),
            'sample_size': sample_size,
            'approaches': {}
        }
        
        for approach_key, approach_info in approaches.items():
            results = approach_info['results']
            if results:
                correct = sum(r['correct'] for r in results)
                comparison['approaches'][approach_key] = {
                    'name': approach_info['name'],
                    'accuracy': correct / len(results) * 100,
                    'total_cases': len(results),
                    'sample_results': results[:10]
                }
        
        with open('improved_approaches_comparison.json', 'w') as f:
            json.dump(comparison, f, indent=2)
        
        print("\n" + "="*80)
        print("COMPARISON SUMMARY")
        print("="*80)
        
        for approach_key, data in comparison['approaches'].items():
            print(f"{data['name']}: {data['accuracy']:.1f}% accuracy")
        
        # Find best
        if comparison['approaches']:
            best = max(comparison['approaches'].items(), 
                      key=lambda x: x[1]['accuracy'])
            print(f"\nBest approach: {best[1]['name']} with {best[1]['accuracy']:.1f}%")
            
            if best[1]['accuracy'] > 70:
                print("✓ This approach meets the 70% target!")
            else:
                print("✗ Still below 70% target. May need the complex prompt.")

if __name__ == "__main__":
    tester = ImprovedApproachTester()
    tester.test_approaches_on_sample(sample_size=50)