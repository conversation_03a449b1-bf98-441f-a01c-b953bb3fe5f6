#!/usr/bin/env python3
"""
VALO Auto-Learning Test System - Monitored Version
Runs with progress saving and status monitoring
"""

import json
import base64
import requests
import os
from datetime import datetime
import time
from collections import defaultdict
import sys

class MonitoredAutoLearningTest:
    def __init__(self):
        self.vlm_url = "http://100.106.127.35:9500/v1/chat/completions"
        self.vlm_model = "VLM-38B-AWQ"
        self.session = requests.Session()
        
        # Learning configuration
        self.max_rounds = 5
        
        # Performance targets
        self.targets = {
            'valid_protection': 98.0,
            'fp_detection': 75.0,
        }
        
        # Dynamic confidence thresholds
        self.thresholds = {
            'structure': 90,
            'person': 50,
            'ppe_compliant': 70,
            'behavioral': 60
        }
        
        # Progress tracking
        self.progress_file = 'auto_learning_progress.json'
        self.current_round = 1
        self.current_case = 0
        self.total_cases = 0
        
        # Load previous progress if exists
        self.load_progress()
        
    def load_progress(self):
        """Load previous progress if exists"""
        if os.path.exists(self.progress_file):
            with open(self.progress_file, 'r') as f:
                progress = json.load(f)
                self.current_round = progress.get('current_round', 1)
                self.current_case = progress.get('current_case', 0)
                self.thresholds = progress.get('thresholds', self.thresholds)
                print(f"Resuming from Round {self.current_round}, Case {self.current_case}")
    
    def save_progress(self):
        """Save current progress"""
        progress = {
            'current_round': self.current_round,
            'current_case': self.current_case,
            'total_cases': self.total_cases,
            'thresholds': self.thresholds,
            'timestamp': datetime.now().isoformat()
        }
        with open(self.progress_file, 'w') as f:
            json.dump(progress, f, indent=2)
    
    def encode_image(self, image_path):
        """Encode image to base64"""
        try:
            if os.path.exists(image_path):
                with open(image_path, 'rb') as f:
                    return base64.b64encode(f.read()).decode('utf-8')
        except:
            pass
        return None
    
    def create_adaptive_prompt(self, round_num):
        """Create prompt with adaptive thresholds"""
        
        # Load base prompt
        with open('FINAL_PRODUCTION_PROMPT.txt', 'r') as f:
            base_prompt = f.read()
        
        # Add adaptive thresholds
        adaptive_section = f"""

ADAPTIVE LEARNING ROUND {round_num}
Current Confidence Thresholds:
- Structure identification: {self.thresholds['structure']}%
- Person detection: {self.thresholds['person']}%
- PPE compliance: {self.thresholds['ppe_compliant']}%
- Behavioral violations: {self.thresholds['behavioral']}%

Apply these thresholds when making decisions."""
        
        # Insert before OUTPUT FORMAT
        prompt = base_prompt.replace("OUTPUT EXACTLY IN THIS FORMAT:", 
                                    adaptive_section + "\n\nOUTPUT EXACTLY IN THIS FORMAT:")
        
        return prompt
    
    def test_single_case(self, case, prompt):
        """Test a single case with retry logic"""
        
        # Encode images
        source_b64 = self.encode_image(case['source_image'])
        cropped_b64 = self.encode_image(case['cropped_image'])
        
        if not source_b64 or not cropped_b64:
            return None
        
        payload = {
            "model": self.vlm_model,
            "messages": [{
                "role": "user",
                "content": [
                    {"type": "text", "text": prompt},
                    {"type": "text", "text": "\n\nSOURCE IMAGE:"},
                    {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{source_b64}"}},
                    {"type": "text", "text": "\n\nCROPPED IMAGE:"},
                    {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{cropped_b64}"}}
                ]
            }],
            "temperature": 0.1,
            "max_tokens": 250
        }
        
        # Try with shorter timeout and retry
        for attempt in range(3):
            try:
                timeout = 10 + (attempt * 5)
                response = self.session.post(self.vlm_url, json=payload, timeout=timeout)
                if response.status_code == 200:
                    vlm_response = response.json()['choices'][0]['message']['content']
                    
                    # Parse response
                    predicted_fp = 'YES' in vlm_response.split('FALSE POSITIVE:')[1][:5]
                    
                    return {
                        'case_number': case['case_number'],
                        'actual_fp': case['is_false_positive'],
                        'predicted_fp': predicted_fp,
                        'correct': predicted_fp == case['is_false_positive'],
                        'response': vlm_response
                    }
            except:
                if attempt < 2:
                    time.sleep(2)
                continue
        
        return None
    
    def run_single_round(self, round_num, all_cases):
        """Run a single round with progress saving"""
        
        print(f"\n{'='*70}")
        print(f"LEARNING ROUND {round_num}")
        print(f"{'='*70}")
        
        # Create adaptive prompt
        prompt = self.create_adaptive_prompt(round_num)
        
        print(f"Current thresholds:")
        for key, value in self.thresholds.items():
            print(f"  {key}: {value}%")
        
        results = []
        errors = []
        
        # Start from current_case if resuming
        start_case = self.current_case if round_num == self.current_round else 0
        
        print(f"\nTesting cases {start_case+1} to {len(all_cases)}...")
        start_time = time.time()
        
        for i in range(start_case, len(all_cases)):
            case = all_cases[i]
            self.current_case = i
            
            # Progress update every 50 cases
            if i % 50 == 0 and i > start_case:
                elapsed = time.time() - start_time
                rate = (i - start_case) / elapsed
                eta = (len(all_cases) - i) / rate / 60
                print(f"Progress: {i}/{len(all_cases)} ({i/len(all_cases)*100:.1f}%) - ETA: {eta:.1f} min")
                
                # Save progress
                self.save_progress()
            
            result = self.test_single_case(case, prompt)
            if result:
                results.append(result)
                if not result['correct']:
                    errors.append(result)
            
            time.sleep(0.2)  # Rate limit
        
        # Calculate metrics
        if results:
            metrics = self.calculate_metrics(results)
            
            print(f"\nRound {round_num} Results:")
            print(f"├─ Overall Accuracy: {metrics['accuracy']:.1f}%")
            print(f"├─ FP Detection: {metrics['fp_detection']:.1f}% ({metrics['fp_detected']}/{metrics['fp_total']})")
            print(f"└─ Valid Protection: {metrics['valid_protection']:.1f}% ({metrics['valid_protected']}/{metrics['valid_total']})")
            
            # Analyze and adjust thresholds
            self.analyze_and_adjust(results, metrics)
            
            # Save round results
            round_data = {
                'round': round_num,
                'metrics': metrics,
                'thresholds': self.thresholds.copy(),
                'errors': len(errors),
                'timestamp': datetime.now().isoformat()
            }
            
            with open(f'valo_auto_round_{round_num}_results.json', 'w') as f:
                json.dump(round_data, f, indent=2)
            
            return metrics
        
        return None
    
    def calculate_metrics(self, results):
        """Calculate performance metrics"""
        
        total = len(results)
        correct = sum(r['correct'] for r in results)
        accuracy = correct / total * 100
        
        # FP metrics
        actual_fps = [r for r in results if r['actual_fp']]
        fp_total = len(actual_fps)
        fp_detected = sum(r['predicted_fp'] for r in actual_fps)
        fp_rate = fp_detected / fp_total * 100 if fp_total > 0 else 0
        
        # Valid metrics
        actual_valid = [r for r in results if not r['actual_fp']]
        valid_total = len(actual_valid)
        valid_protected = sum(not r['predicted_fp'] for r in actual_valid)
        protection_rate = valid_protected / valid_total * 100 if valid_total > 0 else 100
        
        return {
            'accuracy': accuracy,
            'fp_detection': fp_rate,
            'fp_total': fp_total,
            'fp_detected': fp_detected,
            'valid_protection': protection_rate,
            'valid_total': valid_total,
            'valid_protected': valid_protected
        }
    
    def analyze_and_adjust(self, results, metrics):
        """Analyze errors and adjust thresholds"""
        
        # Count error types
        false_negatives = 0  # Valid violations marked as FP
        false_positives = 0  # FPs not detected
        
        for r in results:
            if not r['correct']:
                if not r['actual_fp'] and r['predicted_fp']:
                    false_negatives += 1
                elif r['actual_fp'] and not r['predicted_fp']:
                    false_positives += 1
        
        print(f"\nError Analysis:")
        print(f"├─ False Negatives (valid missed): {false_negatives}")
        print(f"└─ False Positives (FP not caught): {false_positives}")
        
        # Priority: Protect valid violations
        if false_negatives > 5:
            print(f"\n⚠️  Too many valid violations missed!")
            self.thresholds['structure'] = min(95, self.thresholds['structure'] + 3)
            self.thresholds['behavioral'] = max(50, self.thresholds['behavioral'] - 5)
            print(f"  → Adjusted structure threshold to {self.thresholds['structure']}%")
            print(f"  → Adjusted behavioral threshold to {self.thresholds['behavioral']}%")
        
        # Secondary: Improve FP detection
        elif false_positives > 20 and false_negatives < 3:
            print(f"\n📊 Can improve FP detection")
            self.thresholds['structure'] = max(85, self.thresholds['structure'] - 2)
            self.thresholds['ppe_compliant'] = min(80, self.thresholds['ppe_compliant'] + 3)
            print(f"  → Adjusted thresholds for better FP detection")
    
    def run(self):
        """Run the complete auto-learning process"""
        
        print("VALO AUTO-LEARNING TEST SYSTEM - MONITORED VERSION")
        print("="*70)
        print(f"Targets:")
        print(f"├─ Valid Protection: {self.targets['valid_protection']}%")
        print(f"└─ FP Detection: {self.targets['fp_detection']}%")
        
        # Load all data
        print("\nLoading test data...")
        with open('valo_batch_round3_complete.json', 'r') as f:
            all_cases = json.load(f)['results']
        
        self.total_cases = len(all_cases)
        print(f"Loaded {self.total_cases} cases")
        
        # Run learning rounds
        for round_num in range(self.current_round, self.max_rounds + 1):
            self.current_round = round_num
            self.current_case = 0 if round_num > self.current_round else self.current_case
            
            metrics = self.run_single_round(round_num, all_cases)
            
            if metrics:
                # Check if targets met
                if metrics['valid_protection'] >= self.targets['valid_protection'] and \
                   metrics['fp_detection'] >= self.targets['fp_detection']:
                    print(f"\n✅ TARGETS ACHIEVED IN ROUND {round_num}!")
                    break
        
        # Generate final report
        print("\n" + "="*70)
        print("AUTO-LEARNING COMPLETE")
        print("="*70)
        
        # Clean up progress file
        if os.path.exists(self.progress_file):
            os.remove(self.progress_file)
        
        print("\nFinal results saved to: valo_auto_round_*.json files")

def main():
    tester = MonitoredAutoLearningTest()
    tester.run()

if __name__ == "__main__":
    main()