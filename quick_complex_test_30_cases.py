#!/usr/bin/env python3
"""
Quick test of complex prompt with 800 tokens on 30 cases
To validate hypothesis before full test
"""

import json
import base64
import requests
import os
import time
from datetime import datetime

def quick_complex_test():
    """Quick validation test"""
    
    print("="*80)
    print("QUICK VALIDATION: Complex Prompt with 800 Tokens")
    print("="*80)
    print("Testing 30 cases to validate token limit hypothesis")
    print("Previous: 22.5% with 300 tokens")
    print("Expected: >70% with 800 tokens (if hypothesis correct)")
    print("="*80)
    
    vlm_url = "http://**************:9500/v1/chat/completions"
    vlm_model = "VLM-38B-AWQ"
    
    # Load complex prompt
    if os.path.exists('FINAL_PRODUCTION_PROMPT.txt'):
        with open('FINAL_PRODUCTION_PROMPT.txt', 'r') as f:
            prompt = f.read()
    else:
        prompt = """Analyze safety violation images. 
FALSE POSITIVE if: Only equipment/structures OR Person with complete PPE.
Otherwise VALID VIOLATION.
Respond: FALSE POSITIVE: YES or NO"""
    
    # Load 30 test cases
    with open('valo_batch_round3_complete.json', 'r') as f:
        cases = json.load(f)['results'][:30]
    
    results = []
    session = requests.Session()
    
    for i, case in enumerate(cases):
        print(f"\rTesting case {i+1}/30...", end='', flush=True)
        
        # Encode images
        try:
            with open(case['source_image'], 'rb') as f:
                source_b64 = base64.b64encode(f.read()).decode('utf-8')
            with open(case['cropped_image'], 'rb') as f:
                cropped_b64 = base64.b64encode(f.read()).decode('utf-8')
        except:
            continue
        
        payload = {
            "model": vlm_model,
            "messages": [{
                "role": "user",
                "content": [
                    {"type": "text", "text": prompt},
                    {"type": "text", "text": "\n\nSOURCE IMAGE:"},
                    {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{source_b64}"}},
                    {"type": "text", "text": "\n\nCROPPED IMAGE:"},
                    {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{cropped_b64}"}}
                ]
            }],
            "temperature": 0.1,
            "max_tokens": 800
        }
        
        try:
            response = session.post(vlm_url, json=payload, timeout=60)
            if response.status_code == 200:
                vlm_response = response.json()['choices'][0]['message']['content']
                predicted_fp = "FALSE POSITIVE: YES" in vlm_response
                actual_fp = case['is_false_positive']
                
                results.append({
                    'correct': predicted_fp == actual_fp,
                    'response_length': len(vlm_response)
                })
                
                if i < 3:  # Show first 3
                    status = "✓" if results[-1]['correct'] else "✗"
                    print(f"\n  {status} Case {i+1}: {vlm_response[:100]}...")
        except:
            pass
        
        time.sleep(0.5)
    
    # Results
    print("\n\n" + "="*60)
    print("QUICK TEST RESULTS")
    print("="*60)
    
    if results:
        correct = sum(r['correct'] for r in results)
        accuracy = correct / len(results) * 100
        avg_length = sum(r['response_length'] for r in results) / len(results)
        
        print(f"Tested: {len(results)}/30 cases")
        print(f"Accuracy: {accuracy:.1f}% ({correct}/{len(results)})")
        print(f"Avg response: {avg_length:.0f} chars")
        
        print("\n" + "-"*60)
        print("COMPARISON:")
        print(f"300 tokens: 22.5% accuracy")
        print(f"800 tokens: {accuracy:.1f}% accuracy")
        print(f"Improvement: {accuracy - 22.5:+.1f}%")
        
        if accuracy > 70:
            print("\n✅ HYPOTHESIS CONFIRMED!")
            print("Token limit WAS the constraint!")
            print("→ Proceed with full 1250 test")
        elif accuracy > 50:
            print("\n⚠️ Partial improvement")
            print("Token limit helped but not enough")
            print("→ Try structured approach")
        else:
            print("\n❌ Token limit not the issue")
            print("Complex prompt still failing")
            print("→ Need different approach")
    
    return accuracy if results else 0

if __name__ == "__main__":
    accuracy = quick_complex_test()