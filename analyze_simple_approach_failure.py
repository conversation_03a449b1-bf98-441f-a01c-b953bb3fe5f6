#!/usr/bin/env python3
"""
Analyze why simple equipment detection is failing
"""

import json
import os

def analyze_failure():
    print("\n" + "="*80)
    print("ANALYZING SIMPLE APPROACH FAILURE")
    print("="*80)
    
    # Load results from different tests
    results = []
    
    # Load robust test results
    if os.path.exists('robust_test_results.json'):
        with open('robust_test_results.json', 'r') as f:
            data = json.load(f)
            results.extend(data['results'])
    
    if not results:
        print("No results to analyze yet")
        return
    
    print(f"\nAnalyzing {len(results)} test cases...")
    
    # Analyze error patterns
    errors = [r for r in results if not r['correct']]
    
    # Type 1: False Negatives (missed real FPs)
    false_negatives = [r for r in errors if r['actual_fp'] and not r['predicted_fp']]
    
    # Type 2: False Positives (marked valid as FP)
    false_positives = [r for r in errors if not r['actual_fp'] and r['predicted_fp']]
    
    print(f"\nError Analysis:")
    print(f"Total errors: {len(errors)}/{len(results)} ({len(errors)/len(results)*100:.1f}%)")
    print(f"False Negatives (missed FPs): {len(false_negatives)}")
    print(f"False Positives (flagged valid): {len(false_positives)}")
    
    # Show examples
    print("\nExample False Negatives (VLM said NO but was equipment):")
    for i, err in enumerate(false_negatives[:5]):
        print(f"  {err['case_number']}: VLM response: '{err['response']}'")
    
    print("\nExample False Positives (VLM said YES but had violations):")
    for i, err in enumerate(false_positives[:5]):
        print(f"  {err['case_number']}: VLM response: '{err['response']}'")
    
    # Key insights
    print("\n" + "="*80)
    print("KEY INSIGHTS:")
    print("="*80)
    
    print("\n1. ACCURACY BREAKDOWN:")
    correct = sum(r['correct'] for r in results)
    print(f"   Overall: {correct}/{len(results)} = {correct/len(results)*100:.1f}%")
    
    # Check if it's better at one type
    actual_fps = [r for r in results if r['actual_fp']]
    actual_valid = [r for r in results if not r['actual_fp']]
    
    if actual_fps:
        fp_correct = sum(r['correct'] for r in actual_fps)
        print(f"   On actual FPs: {fp_correct}/{len(actual_fps)} = {fp_correct/len(actual_fps)*100:.1f}%")
    
    if actual_valid:
        valid_correct = sum(r['correct'] for r in actual_valid)
        print(f"   On actual valid: {valid_correct}/{len(actual_valid)} = {valid_correct/len(actual_valid)*100:.1f}%")
    
    print("\n2. PROBLEM DIAGNOSIS:")
    
    if len(false_negatives) > len(false_positives):
        print("   ⚠️ Main issue: VLM is not recognizing equipment as equipment")
        print("   → The prompt may be too specific or VLM struggles with equipment ID")
    else:
        print("   ⚠️ Main issue: VLM is marking valid violations as equipment")
        print("   → The prompt may be too broad")
    
    print("\n3. WHY SIMPLE ISN'T WORKING:")
    print("   • Equipment detection requires visual understanding")
    print("   • Single YES/NO question may be too ambiguous")
    print("   • VLM might need more context about what to look for")
    
    print("\n4. RECOMMENDATIONS:")
    print("   ✓ Try two-step approach: 1) What's in image? 2) Any people?")
    print("   ✓ Test with more specific equipment descriptions")
    print("   ✓ Consider the complex prompt with proper token limits")
    print("   ✓ May need to combine multiple signals")
    
    print("\n" + "="*80)
    print("CONCLUSION: Simple approach alone is insufficient.")
    print("Need to test more sophisticated approaches.")
    print("="*80 + "\n")

if __name__ == "__main__":
    analyze_failure()