#!/usr/bin/env python3
"""
Monitor enhanced prompt test until completion
"""

import json
import os
import time
from datetime import datetime

def monitor_completion():
    """Monitor test until it completes"""
    
    progress_file = 'enhanced_1250_progress.json'
    last_count = 0
    stall_counter = 0
    
    print("MONITORING ENHANCED PROMPT TEST UNTIL COMPLETION")
    print("="*60)
    print(f"Start: {datetime.now().strftime('%H:%M:%S')}")
    
    while True:
        if os.path.exists(progress_file):
            with open(progress_file, 'r') as f:
                data = json.load(f)
            
            total = data.get('total_cases', 0)
            
            if total > last_count:
                stall_counter = 0  # Reset stall counter
                
                # Calculate metrics
                results = data.get('results', [])
                if results:
                    correct = sum(r['correct'] for r in results)
                    accuracy = correct / total * 100
                    
                    fps = [r for r in results if r['actual_fp']]
                    fp_detected = sum(r['predicted_fp'] for r in fps)
                    fp_rate = fp_detected / len(fps) * 100 if fps else 0
                    
                    print(f"\n[{datetime.now().strftime('%H:%M:%S')}] Progress: {total}/1250 ({total/1250*100:.1f}%)")
                    print(f"├─ Accuracy: {accuracy:.1f}%")
                    print(f"├─ FP Detection: {fp_rate:.1f}% (+{fp_rate-76.3:.1f}%)")
                    print(f"└─ Rate: {(total-last_count)/30:.1f} cases/min")
                
                last_count = total
                
                # Check if complete
                if total >= 1250:
                    print("\n✅ TEST COMPLETE!")
                    print(f"End: {datetime.now().strftime('%H:%M:%S')}")
                    
                    # Final summary
                    print(f"\nFINAL RESULTS:")
                    print(f"├─ Total cases: {total}")
                    print(f"├─ Accuracy: {accuracy:.1f}%")
                    print(f"├─ FP Detection: {fp_rate:.1f}%")
                    print(f"└─ Improvement: {fp_rate-76.3:+.1f}%")
                    
                    # Check for final report
                    if os.path.exists('enhanced_prompt_1250_final_report.json'):
                        print("\nFinal report available: enhanced_prompt_1250_final_report.json")
                    
                    break
            else:
                stall_counter += 1
                if stall_counter > 10:  # No progress for 5 minutes
                    print(f"\n⚠ Test appears to have stalled at {total}/1250 cases")
                    print("Check the process status")
                    break
        
        time.sleep(30)  # Check every 30 seconds

if __name__ == "__main__":
    monitor_completion()