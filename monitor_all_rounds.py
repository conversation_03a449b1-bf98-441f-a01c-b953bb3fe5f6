#!/usr/bin/env python3
"""Monitor progress of all rounds to 70% FP reduction"""

import json
import time
import os
from datetime import datetime

def get_progress():
    """Get current progress across all rounds"""
    status = {
        'round3': {'complete': False, 'progress': 0, 'fp_rate': 0},
        'round4': {'complete': False, 'progress': 0, 'fp_rate': 0},
        'round5': {'complete': False, 'progress': 0, 'fp_rate': 0},
        'round6': {'complete': False, 'progress': 0, 'fp_rate': 0},
        'final': {'achieved': False, 'fp_rate': 0}
    }
    
    # Check Round 3
    if os.path.exists('valo_intelligent_round3_progress.json'):
        with open('valo_intelligent_round3_progress.json', 'r') as f:
            data = json.load(f)
            status['round3']['progress'] = data.get('cases_processed', 0)
            status['round3']['fp_rate'] = data.get('fp_detection_rate', 0)
    
    if os.path.exists('valo_batch_round3_complete.json'):
        try:
            with open('valo_batch_round3_complete.json', 'r') as f:
                data = json.load(f)
                if data.get('stats', {}).get('total_cases') == 1250:
                    status['round3']['complete'] = True
                    status['round3']['fp_rate'] = data['stats'].get('fp_detection_rate', 0)
        except:
            pass
    
    # Check subsequent rounds
    for round_num in range(4, 8):
        if os.path.exists(f'round{round_num}_progress.json'):
            with open(f'round{round_num}_progress.json', 'r') as f:
                data = json.load(f)
                status[f'round{round_num}'] = {
                    'complete': False,
                    'progress': data.get('cases_processed', 0),
                    'fp_rate': data.get('fp_detection_rate', 0)
                }
        
        if os.path.exists(f'valo_batch_round{round_num}_complete.json'):
            with open(f'valo_batch_round{round_num}_complete.json', 'r') as f:
                data = json.load(f)
                status[f'round{round_num}']['complete'] = True
                status[f'round{round_num}']['fp_rate'] = data['stats'].get('fp_detection_rate', 0)
    
    # Check final results
    if os.path.exists('valo_multi_round_final_results.json'):
        with open('valo_multi_round_final_results.json', 'r') as f:
            data = json.load(f)
            status['final']['achieved'] = True
            status['final']['fp_rate'] = data['final_stats'].get('fp_detection_rate', 0)
    
    return status

def display_progress():
    """Display progress dashboard"""
    print("\033[2J\033[H")  # Clear screen
    print("="*60)
    print("VALO AI-FARM MULTI-ROUND PROGRESS MONITOR")
    print(f"Target: 70% FP Detection | Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("="*60)
    
    status = get_progress()
    
    # Round 3
    if status['round3']['complete']:
        print(f"✓ Round 3: COMPLETE | FP Detection: {status['round3']['fp_rate']:.1f}%")
    else:
        print(f"⏳ Round 3: {status['round3']['progress']}/1250 cases | FP: {status['round3']['fp_rate']:.1f}%")
    
    # Subsequent rounds
    for round_num in range(4, 8):
        round_key = f'round{round_num}'
        if round_key in status:
            if status[round_key]['complete']:
                print(f"✓ Round {round_num}: COMPLETE | FP Detection: {status[round_key]['fp_rate']:.1f}%")
            elif status[round_key]['progress'] > 0:
                print(f"⏳ Round {round_num}: In Progress | FP: {status[round_key]['fp_rate']:.1f}%")
            else:
                print(f"⏸  Round {round_num}: Pending")
    
    # Final status
    print("-"*60)
    if status['final']['achieved']:
        print(f"🎯 TARGET ACHIEVED! Final FP Detection: {status['final']['fp_rate']:.1f}%")
    else:
        current_best = max([v['fp_rate'] for v in status.values() if isinstance(v, dict) and 'fp_rate' in v] or [0])
        print(f"📊 Current Best: {current_best:.1f}% | Gap to target: {70-current_best:.1f}%")
    
    return status['final']['achieved']

def main():
    """Main monitoring loop"""
    print("Starting multi-round progress monitor...")
    print("Press Ctrl+C to stop")
    
    try:
        while True:
            achieved = display_progress()
            if achieved:
                print("\n✅ 70% target achieved! Monitoring complete.")
                break
            time.sleep(5)
    except KeyboardInterrupt:
        print("\n\nMonitoring stopped by user.")

if __name__ == "__main__":
    main()