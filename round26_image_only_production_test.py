#!/usr/bin/env python3
"""
Round 26: PRODUCTION REALITY TEST - Image Only (No Remarks)
Testing what happens without human-validated remarks
"""
import asyncio
import json
import aiohttp
import logging
from datetime import datetime
import os

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def analyze_case_image_only(session, case, vlm_endpoint):
    """
    Analyze case using ONLY image - no remarks (production scenario)
    """
    try:
        # In production, we only know:
        # 1. It's a PPE violation alert
        # 2. We have an image
        # NO human remarks available!
        
        prompt = """PRODUCTION PPE VIOLATION ANALYSIS

You are analyzing an image flagged by the system for potential PPE non-compliance.

IMPORTANT: In production, we do NOT have human remarks. We must analyze the image alone.

Please analyze this image and determine:
1. Can you see a person in the image?
2. If yes, are they wearing proper PPE (helmet, vest, safety gear)?
3. Is this a FALSE POSITIVE (worker is actually compliant)?

Key insight: Workers wearing FULL/PROPER PPE are COMPLIANT, not violators.

Answer: Is this a FALSE POSITIVE? YES/NO with reasoning based on what you see."""

        payload = {
            "model": "VLM-38B-AWQ",
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": prompt},
                        {"type": "image_url", "image_url": {"url": f"file://{case['cropped_image']}"}}
                    ]
                }
            ],
            "temperature": 0.1,
            "max_tokens": 200
        }
        
        async with session.post(vlm_endpoint, json=payload) as response:
            if response.status == 200:
                result = await response.json()
                content = result['choices'][0]['message']['content']
                
                # Parse decision
                decision = "YES" in content.upper()[:100]
                
                return {
                    'case_number': case['case_number'],
                    'is_false_positive': case['is_false_positive'],
                    'predicted_fp': decision,
                    'vlm_response': content,
                    'used_remarks': False,  # Critical: NO remarks used
                    'ground_truth_remarks': case.get('remarks', ''),  # For comparison only
                    'processing_error': False
                }
            else:
                logger.error(f"VLM API error: {response.status}")
                return None
                
    except Exception as e:
        logger.error(f"Error processing case {case.get('case_number', 'unknown')}: {e}")
        return None

async def main():
    vlm_endpoint = "http://100.106.127.35:9500/v1/chat/completions"
    
    # Load test data
    with open('valo_batch_round3_complete.json', 'r') as f:
        data = json.load(f)
        all_cases = data['results']
    
    # Get sample of cases for testing
    test_cases = all_cases[:500]  # Test on 500 cases
    
    logger.info("="*80)
    logger.info("ROUND 26: PRODUCTION REALITY TEST - IMAGE ONLY")
    logger.info("NO REMARKS USED - SIMULATING REAL PRODUCTION")
    logger.info("="*80)
    logger.info(f"Testing on {len(test_cases)} cases")
    
    results = []
    errors = 0
    
    async with aiohttp.ClientSession() as session:
        # Process in batches
        batch_size = 10
        for i in range(0, len(test_cases), batch_size):
            batch = test_cases[i:i+batch_size]
            tasks = [analyze_case_image_only(session, case, vlm_endpoint) for case in batch]
            batch_results = await asyncio.gather(*tasks)
            
            for result in batch_results:
                if result:
                    results.append(result)
                else:
                    errors += 1
            
            # Progress update
            if (i + batch_size) % 50 == 0:
                processed = len(results)
                if processed > 0:
                    tp = sum(1 for r in results if r['is_false_positive'] and r['predicted_fp'])
                    fp_total = sum(1 for r in results if r['is_false_positive'])
                    fp_rate = (tp / fp_total * 100) if fp_total > 0 else 0
                    logger.info(f"Progress: {processed}/{len(test_cases)} | FP Detection: {fp_rate:.1f}% | Errors: {errors}")
            
            # Small delay to avoid overwhelming API
            await asyncio.sleep(0.1)
    
    # Final analysis
    logger.info("\n" + "="*80)
    logger.info("FINAL RESULTS - IMAGE ONLY (NO REMARKS)")
    logger.info("="*80)
    
    # Calculate metrics
    tp = sum(1 for r in results if r['is_false_positive'] and r['predicted_fp'])
    tn = sum(1 for r in results if not r['is_false_positive'] and not r['predicted_fp'])
    fp = sum(1 for r in results if not r['is_false_positive'] and r['predicted_fp'])
    fn = sum(1 for r in results if r['is_false_positive'] and not r['predicted_fp'])
    
    fp_total = sum(1 for r in results if r['is_false_positive'])
    valid_total = sum(1 for r in results if not r['is_false_positive'])
    
    fp_detection_rate = (tp / fp_total * 100) if fp_total > 0 else 0
    valid_protection_rate = (tn / valid_total * 100) if valid_total > 0 else 100
    
    logger.info(f"False Positive Detection: {fp_detection_rate:.1f}%")
    logger.info(f"Valid Case Protection: {valid_protection_rate:.1f}%")
    logger.info(f"Total Cases Processed: {len(results)}")
    logger.info(f"Errors: {errors}")
    
    # Compare with remarks-based performance
    logger.info("\n" + "-"*40)
    logger.info("COMPARISON:")
    logger.info(f"Round 6 (WITH remarks): 92.6% FP detection")
    logger.info(f"Round 26 (NO remarks): {fp_detection_rate:.1f}% FP detection")
    logger.info(f"Performance Drop: {92.6 - fp_detection_rate:.1f}%")
    
    # Analyze what types of cases fail without remarks
    logger.info("\n" + "-"*40)
    logger.info("ANALYSIS OF FAILED CASES (Sample):")
    
    missed_fps = [r for r in results if r['is_false_positive'] and not r['predicted_fp']][:10]
    for case in missed_fps:
        logger.info(f"\nCase {case['case_number']}:")
        logger.info(f"  Ground Truth Remarks: {case['ground_truth_remarks']}")
        logger.info(f"  VLM Response: {case['vlm_response'][:100]}...")
    
    # Save results
    output = {
        'round': 26,
        'test_type': 'PRODUCTION_REALITY_NO_REMARKS',
        'stats': {
            'fp_detection_rate': fp_detection_rate,
            'valid_protection_rate': valid_protection_rate,
            'total_cases': len(results),
            'errors': errors,
            'comparison': {
                'round6_with_remarks': 92.6,
                'round26_no_remarks': fp_detection_rate,
                'performance_drop': 92.6 - fp_detection_rate
            }
        },
        'timestamp': datetime.now().isoformat(),
        'key_finding': 'Testing PPE detection using ONLY images without human remarks',
        'sample_results': results[:50]
    }
    
    with open('valo_round26_image_only_production_test.json', 'w') as f:
        json.dump(output, f, indent=2)
    
    logger.info(f"\nResults saved to: valo_round26_image_only_production_test.json")
    
    # Critical conclusion
    logger.info("\n" + "="*80)
    logger.info("CRITICAL FINDING:")
    if fp_detection_rate < 70:
        logger.info("⚠️ WITHOUT REMARKS, PERFORMANCE DROPS BELOW 70% TARGET!")
        logger.info("Round 6 success was partially dependent on human-validated remarks")
        logger.info("Need new approach for production deployment")
    else:
        logger.info("✅ IMAGE-ONLY ANALYSIS STILL ACHIEVES TARGET!")
        logger.info("PPE detection works even without remarks")
    logger.info("="*80)

if __name__ == "__main__":
    asyncio.run(main())