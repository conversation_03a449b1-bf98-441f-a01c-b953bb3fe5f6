#!/usr/bin/env python3
"""
Intelligent VLM prompt system with self-learning capabilities
Addresses top false positive causes from 5,056 case analysis
"""

import json
import base64
import os
from datetime import datetime

# PPE Compliance Patterns from real-world data
PPE_COMPLIANT_COMBINATIONS = [
    {
        "coveralls": ["yellow", "white", "orange", "blue"],
        "vest": ["orange", "yellow", "green", "high-visibility", "reflective", "life vest"],
        "helmet": ["white", "orange", "red", "yellow", "hard hat"],
        "additional": ["safety harness", "lanyard", "life jacket", "TLAD"]
    }
]

# Structure patterns that cause false positives
STRUCTURE_PATTERNS = [
    "crane structure",
    "vessel structure", 
    "PM structure",
    "spreader structure",
    "spreader flipper",
    "barge structure",
    "equipment",
    "machinery",
    "metal framework",
    "ship structure"
]

class IntelligentVLMPrompt:
    def __init__(self):
        self.confidence_thresholds = {
            "person_detection": 70,
            "structure_detection": 80,
            "ppe_detection": 65
        }
        self.camera_specific_params = {}
        
    def create_prompt_v1(self, violation_type, camera_id=None):
        """Create initial intelligent prompt"""
        
        prompt = f"""INTELLIGENT SAFETY ANALYSIS - {violation_type}

CRITICAL: Analyze BOTH the source image (full view) and cropped image (zoomed area).
The cropped image should contain a PERSON for a valid safety violation.

STEP 1 - PERSON vs STRUCTURE DETECTION:
Examine the cropped image carefully. Is this:
A) A PERSON (human worker)
B) A STRUCTURE (crane/vessel/equipment/machinery)
C) UNCLEAR/AMBIGUOUS

Common false positive structures to identify:
- Crane structures (metal beams, framework)
- Vessel/ship structures  
- PM (Preventive Maintenance) equipment
- Spreader mechanisms and flippers
- Any mechanical equipment or machinery

If STRUCTURE detected → FALSE POSITIVE

STEP 2 - PPE COMPLIANCE CHECK (only if PERSON detected):
Check if the person is wearing proper PPE. Common COMPLIANT combinations include:
1. Yellow/white/orange coveralls + orange/yellow/green vest + hard hat
2. High-visibility vest (any color) + hard hat (any color)
3. Life vest/jacket + hard hat
4. Full PPE with safety harness/lanyard

PPE Components that indicate COMPLIANCE:
- Coveralls: yellow, white, orange, blue
- Vests: orange, yellow, green, high-visibility, reflective, life vest
- Helmets: white, orange, red, yellow hard hats
- Additional: safety harness, lanyard, life jacket, TLAD

STEP 3 - CONTEXT ANALYSIS:
Consider the work context from the source image:
- Workers "at wharf" in safe zones
- Multiple workers present (often safer)
- Workers standing/walking (not violating)

DECISION FRAMEWORK:
1. If STRUCTURE in cropped image → FALSE POSITIVE
2. If PERSON with FULL PPE → FALSE POSITIVE  
3. If PERSON without clear PPE violation → FALSE POSITIVE
4. If UNCLEAR/POOR QUALITY → FALSE POSITIVE
5. Only flag as VALID if: PERSON clearly visible + NO PPE + In hazardous area

FINAL ANSWER: Is this a FALSE POSITIVE? YES/NO

Confidence: [0-100]%
Reason: [Brief explanation]"""

        return prompt
    
    def create_prompt_v2_with_learning(self, violation_type, camera_id, learning_data):
        """Enhanced prompt with camera-specific learning"""
        
        # Get camera-specific parameters
        cam_params = self.camera_specific_params.get(camera_id, self.confidence_thresholds)
        
        prompt = f"""INTELLIGENT SAFETY ANALYSIS - {violation_type}
Camera: {camera_id}

DUAL IMAGE ANALYSIS REQUIRED:
- SOURCE IMAGE: Full context view
- CROPPED IMAGE: Specific area of concern (should contain a person)

STEP 1 - ENTITY CLASSIFICATION (Confidence threshold: {cam_params.get('person_detection', 70)}%):
What is in the CROPPED image?
□ PERSON - Human worker clearly visible
□ STRUCTURE - Equipment/machinery/crane/vessel parts
□ AMBIGUOUS - Cannot determine with confidence

STRUCTURE INDICATORS (learned patterns):
{chr(10).join(f'- {pattern}' for pattern in STRUCTURE_PATTERNS)}

STEP 2 - PPE COMPLIANCE ASSESSMENT (if PERSON detected):
Scan for PPE elements:
□ Head Protection: {', '.join(['white', 'orange', 'red', 'yellow'])} hard hat
□ Body Protection: {', '.join(['yellow', 'white', 'orange', 'blue'])} coveralls
□ Visibility Gear: {', '.join(['orange', 'yellow', 'green'])} vest/life vest
□ Additional Safety: harness, lanyard, life jacket

COMPLIANT if ANY combination present.

STEP 3 - CONTEXTUAL VALIDATION:
From SOURCE image, check:
□ Location: At wharf/safe zone?
□ Activity: Standing/walking vs. active violation?
□ Personnel: Multiple workers present?

DECISION MATRIX:
┌─────────────────┬────────────┬──────────────┐
│ Cropped Content │ PPE Status │ Result       │
├─────────────────┼────────────┼──────────────┤
│ Structure       │ N/A        │ FALSE POS    │
│ Person          │ Compliant  │ FALSE POS    │
│ Person          │ No Clear   │ FALSE POS    │
│ Person          │ Violation  │ VALID        │
│ Unclear         │ N/A        │ FALSE POS    │
└─────────────────┴────────────┴──────────────┘

OUTPUT FORMAT:
FALSE POSITIVE: [YES/NO]
Entity Type: [PERSON/STRUCTURE/UNCLEAR]
PPE Status: [COMPLIANT/NON-COMPLIANT/N/A]
Confidence: [0-100]%
Key Observation: [One line description]"""

        return prompt
    
    def create_violation_specific_prompt(self, violation_type, camera_id=None):
        """Create violation-specific prompts based on analysis"""
        
        prompts = {
            "PPE Non-compliance": self._ppe_prompt(),
            "One man Lashing": self._one_man_lashing_prompt(),
            "Ex.Row Violation": self._ex_row_prompt(),
            "2-Container Distance": self._container_distance_prompt(),
            "STA Double-up": self._sta_double_prompt(),
            "Spreader Ride": self._spreader_ride_prompt()
        }
        
        return prompts.get(violation_type, self.create_prompt_v1(violation_type))
    
    def _ppe_prompt(self):
        return """PPE COMPLIANCE CHECK

FOCUS: Person detection and PPE verification in CROPPED image.

1. IS THIS A PERSON?
   - Human features visible? (head, arms, body)
   - Or is it crane/vessel/equipment structure?
   - 52.1% of PPE alerts are workers ALREADY wearing PPE
   - 37.3% are structures misidentified as people

2. PPE CHECKLIST (if person):
   ✓ Hard hat (any color: white, orange, red, yellow)
   ✓ High-vis vest/coveralls (yellow, orange, green)
   ✓ Safety equipment visible
   
   If ANY PPE visible → FALSE POSITIVE

3. COMMON FALSE PATTERNS:
   - "LS Full PPE at wharf" (428 cases)
   - "CRANE STRUCTURE CAPTURED AS LS" (351 cases)
   - Workers in safe zones

DECISION: FALSE POSITIVE? YES/NO"""
    
    def _one_man_lashing_prompt(self):
        return """ONE MAN LASHING CHECK

FOCUS: Count people and verify actual lashing activity.

1. ENTITY CHECK:
   - Is cropped image a PERSON or STRUCTURE?
   - 29.5% are vessel/crane structures
   - 19.9% are workers in full PPE

2. PEOPLE COUNT (if person):
   - How many people visible in SOURCE image?
   - 2+ people = FALSE POSITIVE (safer)
   - 13.7% of alerts have multiple workers

3. ACTIVITY CHECK:
   - Are they ACTUALLY doing lashing?
   - Or just standing/walking nearby?
   - "Not doing lashing" = FALSE POSITIVE

DECISION: FALSE POSITIVE? YES/NO"""
    
    def _container_distance_prompt(self):
        return """2-CONTAINER DISTANCE CHECK

FOCUS: Identify if cropped image shows person or equipment.

1. CRITICAL CHECK:
   - 41.4% are spreader/vessel structures
   - Look for mechanical parts, not people
   
2. IF PERSON DETECTED:
   - Are they wearing PPE? (15.1% have full PPE)
   - Multiple workers present? (4.1% have 2+ workers)
   
3. STRUCTURE INDICATORS:
   - Spreader mechanisms
   - Container edges
   - Mechanical equipment

DECISION: FALSE POSITIVE? YES/NO"""
    
    def _ex_row_prompt(self):
        return """EXTREME ROW VIOLATION CHECK

1. IMAGE QUALITY:
   - 19.2% have camera/footage issues
   - If unclear → FALSE POSITIVE

2. PERSON CHECK:
   - 25.3% show workers in full PPE
   - 18.4% have multiple workers (safer)

3. ACTIVITY VERIFICATION:
   - Actually at extreme row?
   - Actually doing unsafe work?
   - Or just standing/walking?

DECISION: FALSE POSITIVE? YES/NO"""
    
    def _sta_double_prompt(self):
        return """STA DOUBLE-UP CHECK

Simple checks:
1. Is cropped image a PERSON? (not structure)
2. If yes, wearing PPE? (24.4% have full PPE)
3. Multiple people = Often FALSE POSITIVE

DECISION: FALSE POSITIVE? YES/NO"""
    
    def _spreader_ride_prompt(self):
        return """SPREADER RIDE CHECK

Key patterns (100% of false positives have full PPE):
1. Person visible in cropped image?
2. Wearing PPE? → FALSE POSITIVE
3. Is it spreader structure? → FALSE POSITIVE

DECISION: FALSE POSITIVE? YES/NO"""

def generate_test_prompt():
    """Generate the recommended prompt for testing"""
    prompter = IntelligentVLMPrompt()
    
    # Start with V1 for initial testing
    base_prompt = prompter.create_prompt_v1("General Safety Violation")
    
    print("RECOMMENDED INITIAL PROMPT FOR TESTING:")
    print("="*80)
    print(base_prompt)
    print("\n" + "="*80)
    
    # Save prompt
    with open('intelligent_vlm_prompt.txt', 'w') as f:
        f.write(base_prompt)
    
    return base_prompt

if __name__ == "__main__":
    generate_test_prompt()