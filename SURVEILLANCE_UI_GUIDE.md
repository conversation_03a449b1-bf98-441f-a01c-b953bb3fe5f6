# AI-FARM Surveillance UI Implementation Guide

## Overview

The AI-FARM Surveillance UI is a professional-grade, dark-themed interface designed specifically for PSA VALO's false positive reduction system. It provides real-time monitoring, analytics, and ROI calculations in a control-room style interface.

## Key Features Implemented

### 1. **Professional Surveillance Theme**
- Dark background (#0a0a0a) with high contrast
- Neon accent colors for critical information
- Professional typography and spacing
- Responsive design for control room displays

### 2. **Real-Time Alert Monitoring**
- Live VALO alert feed with auto-refresh
- Alert categorization and confidence scoring
- Terminal-specific filtering (P1, P2, P3)
- WebSocket integration for real-time updates

### 3. **Full Backend Integration**
- Connected to existing FastAPI backend
- All API endpoints properly integrated
- File upload with progress tracking
- Batch processing status monitoring

### 4. **PSA VALO Context**
- Terminal-specific metrics and analysis
- Maritime safety violation categories
- Equipment vs personnel detection
- Site-specific false positive patterns

## How to Run

### Quick Start
```bash
cd /home/<USER>/VALO_AI-FARM_2025/VALO_AI-FARM_2025
./start-surveillance.sh
```

### Manual Start
```bash
# Backend (if not running)
cd backend
python run.py

# Surveillance Frontend
cd frontend-surveillance
npm install
npm start
```

### Access Points
- **Surveillance UI**: http://localhost:3000
- **Backend API**: http://localhost:8000
- **API Docs**: http://localhost:8000/docs

## Page Structure

### 1. Landing Page
- Crisis statistics display
- Solution benefits
- Call-to-action for demo

### 2. Upload Center
- Drag-and-drop file upload
- CSV and ZIP file support
- Processing configuration options
- Real-time upload progress

### 3. Processing Monitor
- Pipeline stage visualization
- Live processing logs
- Progress metrics
- Estimated completion time

### 4. Surveillance Dashboard
- **Alert Feed**: Real-time VALO alerts with filtering status
- **Metrics Display**: Processing stats, filter rate, cost savings
- **Terminal Status**: P1, P2, P3 performance comparison
- **VLM Pipeline**: Processing status indicators

### 5. Analytics Dashboard
- Processing results distribution (pie chart)
- Confidence score distribution (bar chart)
- False positive categories breakdown
- Performance statistics

### 6. ROI Calculator
- Customizable input parameters
- Real-time calculation
- 5-year projection chart
- Cost breakdown analysis

### 7. Auto-Learning Insights
- Detected patterns (equipment, temporal, location, environmental)
- Optimization progress timeline
- Threshold adjustments
- Business value metrics

## Key Components

### AlertFeed Component
```typescript
- Displays real-time VALO alerts
- Shows processing status (AI Filtered, Human Review, Urgent)
- Terminal and camera identification
- Confidence scoring
```

### MetricsDisplay Component
```typescript
- Live performance metrics
- Filter rate tracking
- Cost savings calculation
- Processing speed monitoring
```

### TerminalStatus Component
```typescript
- Terminal-specific false positive rates
- P1: Quay Cranes (98.2% FP rate)
- P2: Vessel Operations (96.7% FP rate)
- P3: Container Yard (96.6% FP rate)
```

## API Integration

### Upload Endpoint
```typescript
POST /api/batch/upload
- Handles CSV and image uploads
- Returns batch_id for tracking
```

### Status Endpoint
```typescript
GET /api/batch/status/{batch_id}
- Real-time processing status
- Progress tracking
```

### Metrics Endpoints
```typescript
GET /api/metrics/dashboard
GET /api/metrics/terminals
GET /api/metrics/roi/calculate
GET /api/metrics/auto-learning
```

## Testing

### Run E2E Tests
```bash
node test-surveillance-e2e.js
```

### Test Coverage
- Landing page navigation
- File upload functionality
- Dashboard rendering
- Alert feed display
- Terminal status cards
- Analytics charts
- ROI calculations
- Auto-learning insights
- Backend API health

## Customization

### Theme Colors
Edit `src/styles/surveillance.css`:
```css
:root {
    --bg-primary: #0a0a0a;
    --accent-blue: #00d4ff;
    --accent-green: #00ff88;
    --accent-red: #ff4757;
    --accent-orange: #ffa502;
}
```

### Terminal Configuration
Update terminal data in `TerminalStatus.tsx`

### Alert Categories
Modify categories in `api.ts` and backend models

## Performance Optimization

- WebSocket for real-time updates
- Pagination for large alert lists
- Lazy loading for charts
- Optimized re-renders with React hooks

## Security Considerations

- CORS configured for localhost
- API authentication ready (add tokens)
- Input validation on uploads
- XSS protection in React

## Future Enhancements

1. **Multi-language Support**
2. **Export Functionality** (PDF reports)
3. **User Authentication**
4. **Alert Filtering UI**
5. **Historical Data Views**
6. **Mobile Responsive Design**

## Troubleshooting

### Frontend won't start
```bash
cd frontend-surveillance
rm -rf node_modules package-lock.json
npm install
```

### Backend connection issues
- Check backend is running on port 8000
- Verify CORS settings
- Check .env configuration

### Missing data
- Ensure backend database is initialized
- Run seed scripts if needed
- Check API endpoints in browser

## Support

For issues or questions:
1. Check the logs in the terminal
2. Review API documentation at http://localhost:8000/docs
3. Verify all services are running
4. Check browser console for errors

The surveillance UI provides a complete, professional interface for monitoring and managing false positive alerts in real-time, with full integration to the existing AI-FARM backend.