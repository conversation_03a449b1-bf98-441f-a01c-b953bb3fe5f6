# AI-FARM Environment Configuration - Friendli AI VLM Alternative
# This configuration uses the working Friendli AI API instead of the unreachable **************:9500

# ======================
# DATABASE CONFIGURATION
# ======================
POSTGRES_DB=ai_farm
POSTGRES_USER=ai_farm_user
POSTGRES_PASSWORD=ai_farm_password_change_me
DATABASE_ECHO=false

# ======================
# SERVER CONFIGURATION
# ======================
DEBUG=false
LOG_LEVEL=INFO
HOST=0.0.0.0
PORT=8000

# ======================
# VLM API CONFIGURATION - FRIENDLI AI (WORKING ALTERNATIVE)
# ======================
VLM_API_BASE_URL=https://api.friendli.ai/dedicated/v1
VLM_API_KEY=flp_Xf21SJG60333K9KZ2qPOs5Uh0CMwltZ5c4xEefBO2pRe2
VLM_MODEL_NAME=nhyws8db6r6t
VLM_MAX_TOKENS=1000
VLM_TEMPERATURE=0.1
VLM_TIMEOUT_SECONDS=30

# ======================
# PROCESSING CONFIGURATION
# ======================
BATCH_SIZE=10
MAX_CONCURRENT_REQUESTS=3
PROCESSING_TIMEOUT_MINUTES=60
IMAGE_MAX_SIZE_MB=10

# ======================
# CONFIDENCE THRESHOLDS
# ======================
THRESHOLD_STRUCTURE_MISID=70
THRESHOLD_PROPER_PPE=65
THRESHOLD_NO_VIOLATION=75
THRESHOLD_DEFAULT=70

# ======================
# AUTO-LEARNING CONFIGURATION
# ======================
ENABLE_AUTO_LEARNING=true
LEARNING_BATCH_SIZE=50
CONFIDENCE_CALIBRATION_ENABLED=true
PATTERN_DETECTION_ENABLED=true

# ======================
# CUSTOMER DATA PROCESSING
# ======================
CUSTOMER_DATA_RETENTION_HOURS=24
ENABLE_CUSTOMER_DATA_CLEANUP=true
SECURE_TEMP_STORAGE=true

# ======================
# PERFORMANCE SETTINGS
# ======================
WORKER_PROCESSES=4
KEEP_ALIVE_TIMEOUT=65
MAX_REQUEST_SIZE_MB=100

# ======================
# MONITORING
# ======================
ENABLE_METRICS=true
METRICS_PORT=9090
ENABLE_HEALTH_CHECK=true

# ======================
# FRONTEND CONFIGURATION
# ======================
REACT_APP_API_URL=http://localhost:8000
REACT_APP_ENV=development

# ======================
# DOCKER CONFIGURATION
# ======================
COMPOSE_PROJECT_NAME=ai-farm

# ======================
# FRIENDLI AI SPECIFIC NOTES
# ======================
# - API Endpoint: https://api.friendli.ai/dedicated/v1/chat/completions
# - Model: nhyws8db6r6t (Vision-capable model)
# - Authentication: Bearer token authentication
# - Capabilities: Text + Vision analysis, Safety violation detection
# - Status: TESTED AND WORKING (2025-07-05)
# - Response Format: OpenAI-compatible chat completions
# 
# To use this configuration:
# 1. cp .env.friendli .env
# 2. Restart backend service
# 3. Test VLM connectivity: curl http://localhost:8000/api/v1/vlm/health

# Security Notice: 
# - Change all default passwords before production deployment
# - Keep API keys secure and never commit them to version control
# - Use strong passwords for database credentials
# - Friendli AI API key is for testing purposes - replace with your own key
