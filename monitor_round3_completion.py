#!/usr/bin/env python3
"""Monitor Round 3 completion progress"""

import json
import os
import time
import subprocess
from datetime import datetime

while True:
    print(f"\n{'='*60}")
    print(f"Round 3 Completion Monitor - {datetime.now().strftime('%H:%M:%S')}")
    print('='*60)
    
    # Check if process is running
    try:
        result = subprocess.run(['ps', 'aux'], capture_output=True, text=True)
        round3_procs = [line for line in result.stdout.split('\n') if 'round3_fast' in line and 'grep' not in line]
        if round3_procs:
            print("✓ Round 3 fast completion is RUNNING")
            for proc in round3_procs:
                parts = proc.split()
                if len(parts) > 10:
                    pid = parts[1]
                    cpu = parts[2]
                    mem = parts[3]
                    print(f"  PID: {pid} | CPU: {cpu}% | MEM: {mem}%")
        else:
            print("✗ Round 3 process not found")
    except:
        pass
    
    # Check current Round 3 status
    if os.path.exists('valo_batch_round3_complete.json'):
        with open('valo_batch_round3_complete.json', 'r') as f:
            data = json.load(f)
            stats = data['stats']
            print(f"\nCurrent Round 3 status:")
            print(f"  Total cases: {stats['total_cases']}/1250")
            print(f"  Timestamp: {stats['timestamp']}")
            print(f"  FP Detection: {stats['fp_detection_rate']:.1f}%")
            print(f"  Valid Protection: {stats['valid_protection_rate']:.1f}%")
    
    # Check log file
    if os.path.exists('round3_fast_output.log'):
        size = os.path.getsize('round3_fast_output.log')
        print(f"\nLog file size: {size} bytes")
        if size > 0:
            with open('round3_fast_output.log', 'r') as f:
                lines = f.readlines()
                if lines:
                    print("Latest log entries:")
                    for line in lines[-5:]:
                        print(f"  {line.strip()}")
    
    # Check for completion
    if os.path.exists('valo_batch_round3_complete.json'):
        with open('valo_batch_round3_complete.json', 'r') as f:
            data = json.load(f)
            if data['stats']['total_cases'] == 1250:
                print(f"\n✅ ROUND 3 FULLY COMPLETE!")
                print(f"Final FP Detection: {data['stats']['fp_detection_rate']:.1f}%")
                break
    
    time.sleep(10)  # Check every 10 seconds

print("\nRound 3 monitoring complete.")