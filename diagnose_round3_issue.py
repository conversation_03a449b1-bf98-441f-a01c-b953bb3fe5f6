#!/usr/bin/env python3
"""Diagnose why Round 3 is stuck at 540 cases"""

import json
import os
import re

print("Diagnosing Round 3 Issue...")
print("="*60)

# 1. Check existing Round 3 results
with open('valo_batch_round3_complete.json', 'r') as f:
    data = json.load(f)
    existing_results = data['results']
    existing_cases = {r['case_number'] for r in existing_results}

print(f"1. Current Round 3 has {len(existing_cases)} cases")

# Get case number range
case_nums = sorted([int(c[1:]) for c in existing_cases if c.startswith('V')])
print(f"   Case range: V{case_nums[0]} to V{case_nums[-1]}")

# 2. Check available images
print("\n2. Checking available images...")
all_image_cases = set()
image_paths = {}

for root, dirs, files in os.walk('ai_farm_images_fixed_250703/'):
    for file in files:
        if file.endswith(('.jpg', '.JPG', '.jpeg', '.JPEG')):
            match = re.search(r'(V\d+)', file)
            if match:
                case = match.group(1)
                all_image_cases.add(case)
                if case not in image_paths:
                    image_paths[case] = []
                image_paths[case].append(os.path.join(root, file))

print(f"   Found {len(all_image_cases)} unique cases with images")

# Find missing cases
missing_cases = sorted(all_image_cases - existing_cases)
print(f"   Missing from Round 3: {len(missing_cases)} cases")

if missing_cases:
    print(f"   Missing range: {missing_cases[0]} to {missing_cases[-1]}")
    print(f"   First 5 missing: {missing_cases[:5]}")

# 3. Check CSV data
print("\n3. Checking CSV data...")
csv_path = "ai_farm_images_fixed_250703/psa_valo_violation_report_without_images_csv_2025-07-03_14-14-01.CSV"
csv_cases = set()

if os.path.exists(csv_path):
    with open(csv_path, 'r', encoding='utf-8') as f:
        headers = f.readline()
        for line in f:
            parts = line.strip().split(',')
            if len(parts) > 0 and parts[0].startswith('V'):
                csv_cases.add(parts[0])
    print(f"   CSV has {len(csv_cases)} case entries")
else:
    print(f"   CSV not found at: {csv_path}")

# 4. Find discrepancies
print("\n4. Data discrepancies:")
in_images_not_csv = all_image_cases - csv_cases
in_csv_not_images = csv_cases - all_image_cases

print(f"   Cases with images but not in CSV: {len(in_images_not_csv)}")
print(f"   Cases in CSV but no images: {len(in_csv_not_images)}")

# 5. Check missing cases have proper images
print("\n5. Checking missing case images...")
sample_missing = missing_cases[:10] if missing_cases else []
for case in sample_missing:
    if case in image_paths:
        print(f"   {case}: {len(image_paths[case])} images")
        for path in image_paths[case]:
            print(f"      - {os.path.basename(path)}")

# 6. Recommendations
print("\n6. Recommendations:")
print(f"   - Need to process {len(missing_cases)} missing cases")
print(f"   - All missing cases have images: {all(c in image_paths for c in missing_cases)}")
print(f"   - Missing cases have CSV data: {all(c in csv_cases for c in missing_cases[:10])}")

# Save diagnosis
diagnosis = {
    'existing_cases': len(existing_cases),
    'total_image_cases': len(all_image_cases),
    'missing_cases': len(missing_cases),
    'missing_case_numbers': missing_cases[:20],  # First 20
    'has_all_images': all(c in image_paths for c in missing_cases),
    'csv_coverage': sum(1 for c in missing_cases if c in csv_cases) / len(missing_cases) if missing_cases else 1.0
}

with open('round3_diagnosis.json', 'w') as f:
    json.dump(diagnosis, f, indent=2)

print("\nDiagnosis saved to round3_diagnosis.json")