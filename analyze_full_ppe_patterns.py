#!/usr/bin/env python3
"""
Analyze full PPE false positives and learn visual patterns
Push examples to VLM to understand what proper PPE looks like
"""

import json
import base64
import requests
import os
from collections import defaultdict

VLM_API_URL = "http://100.106.127.35:9500/v1/chat/completions"
VLM_MODEL = "VLM-38B-AWQ"

class PPEPatternAnalyzer:
    def __init__(self):
        self.session = requests.Session()
        self.ppe_patterns = {
            'FULL_PPE': [],
            'PROPER_PPE': [],
            'LIFE_JACKET': [],
            'SAFETY_HARNESS': [],
            'SPECIALIZED_PPE': []
        }
        
    def encode_image(self, image_path):
        """Encode image to base64"""
        try:
            if os.path.exists(image_path):
                with open(image_path, 'rb') as f:
                    return base64.b64encode(f.read()).decode('utf-8')
        except:
            pass
        return None
    
    def analyze_ppe_with_vlm(self, image_b64, ppe_type, remark):
        """Ask VLM to describe PPE compliance patterns"""
        
        prompt = f"""You are analyzing an image of a worker wearing PROPER PPE (Personal Protective Equipment).
This was incorrectly flagged as a violation but the worker is actually in FULL COMPLIANCE.

Original remark: "{remark}"

Please describe in detail:
1. What PPE items are visible on the worker?
2. What are the visual characteristics of proper PPE compliance?
3. List specific visual indicators that show this is SAFE/COMPLIANT:
   - Colors of PPE items
   - How the PPE is worn
   - Body positioning
   - Work context

Be specific about:
- Helmet/hard hat appearance
- Vest/coverall colors and fit
- Additional safety equipment
- Overall appearance of a properly equipped worker

Provide a concise description that could help identify proper PPE compliance."""

        payload = {
            "model": VLM_MODEL,
            "messages": [{
                "role": "user",
                "content": [
                    {"type": "text", "text": prompt},
                    {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{image_b64}"}}
                ]
            }],
            "temperature": 0.3,
            "max_tokens": 300
        }
        
        try:
            response = self.session.post(VLM_API_URL, json=payload, timeout=30)
            if response.status_code == 200:
                result = response.json()
                return result['choices'][0]['message']['content'].strip()
        except:
            pass
        return None
    
    def collect_ppe_examples(self):
        """Collect examples of full PPE compliance from false positives"""
        
        print("COLLECTING FULL PPE FALSE POSITIVE EXAMPLES")
        print("="*60)
        
        # Load data
        with open('enhanced_1250_progress.json', 'r') as f:
            data = json.load(f)
        
        # Load batch data for image paths
        with open('valo_batch_round3_complete.json', 'r') as f:
            batch_data = json.load(f)
        
        # Create lookup
        batch_lookup = {case['case_number']: case for case in batch_data['results']}
        
        # PPE compliance patterns to look for
        ppe_patterns = {
            'FULL_PPE': ['FULL PPE', 'IN FULL PPE', 'WITH FULL PPE', 'WEARING FULL PPE'],
            'PROPER_PPE': ['PROPER PPE', 'PPE COMPLIANT', 'IN PROPER PPE'],
            'LIFE_JACKET': ['LIFE JACKET', 'LIFE VEST', 'WITH LIFE JACKET'],
            'SAFETY_HARNESS': ['SAFETY HARNESS', 'LANYARD', 'HARNESS HOOKED', 'TLAD'],
            'SPECIALIZED_PPE': ['COVERALLS', 'HIGH VIS', 'REFLECTIVE', 'SAFETY GEAR']
        }
        
        # Collect examples
        for ppe_type, search_terms in ppe_patterns.items():
            print(f"\nSearching for {ppe_type} examples...")
            examples = []
            
            for result in data['results']:
                if result['actual_fp'] and result.get('remarks'):
                    remark = result['remarks'].upper()
                    case_num = result['case_number']
                    
                    # Check if this matches our pattern
                    if any(term in remark for term in search_terms):
                        if case_num in batch_lookup:
                            batch_case = batch_lookup[case_num]
                            examples.append({
                                'case_number': case_num,
                                'remark': result['remarks'],
                                'cropped_image': batch_case['cropped_image'],
                                'source_image': batch_case['source_image'],
                                'entity_type': result.get('entity_type', 'UNKNOWN')
                            })
                            
                            if len(examples) >= 5:  # Get 5 examples of each
                                break
            
            self.ppe_patterns[ppe_type] = examples
            print(f"Found {len(examples)} examples")
    
    def analyze_all_ppe_patterns(self):
        """Analyze PPE examples with VLM to learn compliance patterns"""
        
        print("\n\nANALYZING PPE PATTERNS WITH VLM")
        print("="*60)
        
        ppe_descriptions = {}
        
        for ppe_type, examples in self.ppe_patterns.items():
            if not examples:
                continue
                
            print(f"\n\nAnalyzing {ppe_type}:")
            print("-"*40)
            
            descriptions = []
            
            # Analyze first 3 examples of each type
            for i, example in enumerate(examples[:3]):
                # Only analyze if detected as PERSON (not structure)
                if example['entity_type'] not in ['PERSON', 'UNKNOWN']:
                    continue
                    
                print(f"\nExample {i+1}: {example['case_number']}")
                print(f"Remark: {example['remark']}")
                
                # Encode cropped image
                image_b64 = self.encode_image(example['cropped_image'])
                if not image_b64:
                    continue
                
                # Get VLM description
                description = self.analyze_ppe_with_vlm(image_b64, ppe_type, example['remark'])
                if description:
                    print(f"\nVLM Analysis:")
                    print(description[:300] + "..." if len(description) > 300 else description)
                    descriptions.append(description)
            
            # Synthesize patterns
            if descriptions:
                ppe_descriptions[ppe_type] = self.synthesize_ppe_patterns(descriptions, ppe_type)
        
        return ppe_descriptions
    
    def synthesize_ppe_patterns(self, descriptions, ppe_type):
        """Synthesize common PPE compliance patterns"""
        
        # Extract common themes
        all_text = ' '.join(descriptions).lower()
        
        # PPE-specific keywords
        ppe_keywords = {
            'helmet': ['helmet', 'hard hat', 'white helmet', 'orange helmet', 'yellow helmet'],
            'vest': ['vest', 'high-vis', 'reflective', 'orange vest', 'yellow vest', 'life vest'],
            'coveralls': ['coveralls', 'uniform', 'work clothes', 'protective clothing'],
            'equipment': ['harness', 'lanyard', 'gloves', 'safety gear', 'tlad'],
            'appearance': ['properly worn', 'fastened', 'secured', 'fitted', 'visible']
        }
        
        pattern_summary = f"\n{ppe_type} VISUAL CHARACTERISTICS:\n"
        
        for category, terms in ppe_keywords.items():
            found = [term for term in terms if term in all_text]
            if found:
                pattern_summary += f"- {category.title()}: {', '.join(found)}\n"
        
        return pattern_summary
    
    def create_ppe_enhanced_prompt(self, ppe_descriptions):
        """Create enhanced prompt with PPE compliance descriptions"""
        
        prompt = """SAFETY VIOLATION DETECTION WITH PPE RECOGNITION

CRITICAL: Recognize PROPER PPE COMPLIANCE to avoid false positives.

ANALYZE BOTH IMAGES:
- SOURCE: Full context
- CROPPED: Worker/area of concern

STEP 1 - ENTITY DETECTION:
A) STRUCTURE (>90% confidence needed)
B) PERSON (any human features)
C) UNCLEAR

STEP 2 - PPE COMPLIANCE RECOGNITION:

PROPER PPE COMPLIANCE LOOKS LIKE:

"""
        
        # Add PPE descriptions
        for ppe_type, description in ppe_descriptions.items():
            prompt += description + "\n"
        
        prompt += """
VISUAL INDICATORS OF COMPLIANCE:
✓ Helmet/hard hat clearly visible on head
✓ Vest worn and properly fastened
✓ Appropriate coveralls/uniform
✓ Additional safety equipment as required
✓ Professional appearance
✓ Working in designated areas

COMMON COMPLIANT COMBINATIONS:
1. White/orange/yellow helmet + Orange/yellow vest + Coveralls
2. Hard hat + Life vest + Safety harness (vessel work)
3. Full coveralls + Reflective vest + Safety equipment
4. Any proper PPE + professional work posture

STEP 3 - VIOLATION ASSESSMENT:

For STRUCTURE → FALSE POSITIVE

For PERSON with PROPER PPE (as described above):
- Check for BEHAVIORAL violations:
  • Mobile phone use
  • Missing specific equipment
  • Unsafe operations
  • Wrong location
  
- If NO behavioral violations → FALSE POSITIVE

For PERSON without proper PPE → VALID VIOLATION

For UNCLEAR → VALID VIOLATION (safety first)

Remember: Workers in full PPE are usually FALSE POSITIVES unless they have behavioral violations.

OUTPUT:
FALSE POSITIVE: [YES/NO]
Detection: [STRUCTURE/PERSON/UNCLEAR]
PPE Status: [COMPLIANT/NON-COMPLIANT]
Violations: [List any found]"""

        return prompt
    
    def save_ppe_analysis(self, ppe_descriptions, enhanced_prompt):
        """Save PPE analysis and enhanced prompt"""
        
        # Save PPE patterns
        with open('ppe_compliance_patterns.json', 'w') as f:
            json.dump(ppe_descriptions, f, indent=2)
        
        # Save enhanced prompt
        with open('ppe_enhanced_safety_prompt.txt', 'w') as f:
            f.write(enhanced_prompt)
        
        print("\n\nPPE ANALYSIS COMPLETE")
        print("Files created:")
        print("- ppe_compliance_patterns.json")
        print("- ppe_enhanced_safety_prompt.txt")

def main():
    analyzer = PPEPatternAnalyzer()
    
    # Collect PPE examples
    analyzer.collect_ppe_examples()
    
    # Analyze with VLM
    ppe_descriptions = analyzer.analyze_all_ppe_patterns()
    
    # Create enhanced prompt
    if ppe_descriptions:
        enhanced_prompt = analyzer.create_ppe_enhanced_prompt(ppe_descriptions)
        analyzer.save_ppe_analysis(ppe_descriptions, enhanced_prompt)
        
        print("\n\nKEY IMPROVEMENTS:")
        print("1. VLM now understands visual appearance of proper PPE")
        print("2. Can distinguish compliant PPE from violations")
        print("3. Reduces false positives on properly equipped workers")
        print("4. Still catches behavioral violations")
    else:
        print("\nNo PPE patterns analyzed")

if __name__ == "__main__":
    main()