#!/usr/bin/env python3
"""
Debug Round 3 data structure
"""

import json

# Load Round 3 results
with open('valo_batch_round3_complete.json', 'r') as f:
    round3_data = json.load(f)

print("Round 3 Data Structure:")
print(f"  Keys: {list(round3_data.keys())}")
print(f"  Stats keys: {list(round3_data['stats'].keys())}")
print(f"  Number of results: {len(round3_data['results'])}")

# Sample a few results
print("\nFirst 3 results structure:")
for i, result in enumerate(round3_data['results'][:3]):
    print(f"\nResult {i+1}:")
    print(f"  Keys: {list(result.keys())}")
    for key, value in result.items():
        if isinstance(value, str) and len(value) > 100:
            print(f"  {key}: {value[:50]}...")
        else:
            print(f"  {key}: {value}")

# Count decisions
decisions = {}
for result in round3_data['results']:
    decision_key = None
    for key in result.keys():
        if 'decision' in key.lower():
            decision_key = key
            break
    
    if decision_key:
        decision = result[decision_key]
        decisions[decision] = decisions.get(decision, 0) + 1

print(f"\nDecision counts:")
for decision, count in decisions.items():
    print(f"  {decision}: {count}")

# Check if we need to recalculate stats
valid_count = sum(1 for r in round3_data['results'] if r.get('alert_status') == 'Valid')
invalid_count = sum(1 for r in round3_data['results'] if r.get('alert_status') != 'Valid')
print(f"\nManual count:")
print(f"  Valid cases: {valid_count}")
print(f"  Invalid cases: {invalid_count}")
print(f"  Total: {valid_count + invalid_count}")