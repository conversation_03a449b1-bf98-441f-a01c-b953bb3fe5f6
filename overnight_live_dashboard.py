#!/usr/bin/env python3
"""
Live monitoring dashboard for overnight 25-round optimization
"""
import json
import time
import os
import glob
from datetime import datetime

def clear_screen():
    os.system('clear' if os.name == 'posix' else 'cls')

def get_latest_round_info():
    """Find the latest completed round"""
    latest_round = 5
    latest_fp = 52.7
    
    for i in range(25, 5, -1):
        files = glob.glob(f'valo_round{i}_*_complete.json')
        if files:
            try:
                with open(files[0], 'r') as f:
                    data = json.load(f)
                    latest_round = i
                    latest_fp = data['stats']['fp_detection_rate']
                break
            except:
                pass
    
    return latest_round, latest_fp

def display_dashboard():
    """Display the dashboard"""
    while True:
        try:
            clear_screen()
            
            # Header
            print("="*80)
            print("VALO AI-FARM OVERNIGHT OPTIMIZATION DASHBOARD".center(80))
            print(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}".center(80))
            print("="*80)
            
            # Get current status
            latest_round, latest_fp = get_latest_round_info()
            
            # Dashboard data
            if os.path.exists('overnight_dashboard.json'):
                with open('overnight_dashboard.json', 'r') as f:
                    dashboard = json.load(f)
                    elapsed = dashboard.get('elapsed_hours', 0)
            else:
                elapsed = 0
            
            # Status display
            print(f"\n📊 CURRENT STATUS:")
            print(f"   Latest Completed Round: {latest_round}")
            print(f"   Current FP Detection: {latest_fp:.1f}%")
            print(f"   Gap to 70% Target: {max(0, 70 - latest_fp):.1f}%")
            print(f"   Gap to 90% Target: {max(0, 90 - latest_fp):.1f}%")
            print(f"   Runtime: {elapsed:.1f} hours")
            
            # Progress bar
            progress = min(100, (latest_fp - 6.4) / (90 - 6.4) * 100)
            bar_length = 50
            filled = int(bar_length * progress / 100)
            bar = "█" * filled + "░" * (bar_length - filled)
            print(f"\n📈 PROGRESS TO 90%:")
            print(f"   [{bar}] {progress:.1f}%")
            print(f"   6.4% ────────────────────────────────────────── 90%")
            
            # Round status
            print(f"\n🔄 ROUND STATUS:")
            round_names = {
                6: "Full PPE Intelligence",
                7: "Camera Calibration",
                8: "Multi-Factor",
                9: "Aggressive",
                10: "Final Push",
                11: "Ensemble",
                12: "Meta-Learning",
                13: "Active Learning",
                14: "Synthetic",
                15: "Hierarchical"
            }
            
            for i in range(6, min(latest_round + 3, 16)):
                if i <= latest_round:
                    status = "✅ Complete"
                elif i == latest_round + 1:
                    status = "🔄 Running"
                else:
                    status = "⏳ Pending"
                
                print(f"   Round {i:2d}: {round_names.get(i, 'Advanced')} - {status}")
            
            # Recent status
            print(f"\n📝 RECENT STATUS:")
            if os.path.exists('overnight_status.txt'):
                with open('overnight_status.txt', 'r') as f:
                    lines = f.readlines()
                    for line in lines[-5:]:
                        print(f"   {line.strip()}")
            
            # Achievements
            if latest_fp >= 70:
                print(f"\n🎯 ACHIEVEMENTS:")
                print(f"   ✅ 70% Target Achieved!")
                if latest_fp >= 80:
                    print(f"   ✅ 80% Milestone Reached!")
                if latest_fp >= 90:
                    print(f"   ✅ 90% TARGET ACHIEVED! 🎉")
            
            # Health status
            if os.path.exists('health_status.json'):
                with open('health_status.json', 'r') as f:
                    health = json.load(f)
                    print(f"\n💚 SYSTEM HEALTH:")
                    print(f"   VLM API: {'✅ OK' if health['vlm_api']['status'] else '❌ DOWN'}")
                    print(f"   Memory: {health['system']['memory_percent']:.1f}%")
                    print(f"   CPU: {health['system']['cpu_percent']:.1f}%")
                    if health['alerts']:
                        print(f"   ⚠️  Alerts: {', '.join(health['alerts'])}")
            
            print("\n" + "="*80)
            print("Press Ctrl+C to exit | Refreshing every 10 seconds...")
            
            time.sleep(10)
            
        except KeyboardInterrupt:
            print("\n\nDashboard closed.")
            break
        except Exception as e:
            print(f"\nError: {e}")
            time.sleep(5)

if __name__ == "__main__":
    display_dashboard()