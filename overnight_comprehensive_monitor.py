#!/usr/bin/env python3
"""
Comprehensive overnight monitor for all 25 rounds
Shows status, progress, and results
"""

import time
import os
import json
import glob
from datetime import datetime
import subprocess

def get_round_status(round_num):
    """Check status of a specific round"""
    # Check if complete
    files = glob.glob(f'valo_round{round_num}_*_complete.json')
    if files:
        with open(files[0], 'r') as f:
            data = json.load(f)
            stats = data.get('stats', {})
            return {
                'status': 'complete',
                'fp_rate': stats.get('fp_detection_rate', 0),
                'valid_rate': stats.get('valid_protection_rate', 100)
            }
    
    # Check if running
    ps_output = subprocess.run(['ps', 'aux'], capture_output=True, text=True).stdout
    if f'round{round_num}' in ps_output.lower():
        # Try to get progress from log
        log_files = [f'round{round_num}_*.log', f'round{round_num}.log']
        for pattern in log_files:
            for log_file in glob.glob(pattern):
                if os.path.exists(log_file):
                    try:
                        with open(log_file, 'r') as f:
                            lines = f.readlines()
                            for line in reversed(lines[-10:]):
                                if 'Progress:' in line and 'FP:' in line:
                                    return {'status': 'running', 'last_log': line.strip()}
                    except:
                        pass
        return {'status': 'running'}
    
    return {'status': 'pending'}

def display_dashboard():
    """Display comprehensive dashboard"""
    os.system('clear')
    
    print("="*100)
    print("VALO AI-FARM OVERNIGHT COMPREHENSIVE MONITOR".center(100))
    print(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}".center(100))
    print("="*100)
    
    # Define all rounds
    rounds_info = {
        3: "Safety-First Baseline",
        4: "Equipment Pattern Recognition", 
        5: "Context Analysis",
        6: "Full PPE Intelligence",
        7: "Camera-Specific Calibration",
        8: "Combined Multi-Factor",
        9: "Aggressive Optimization",
        10: "Final Push",
        11: "Ensemble Multi-Model",
        12: "Meta-Learning",
        13: "Active Learning",
        14: "Synthetic Augmentation",
        15: "Hierarchical Trees",
        16: "Parameter Sweep",
        17: "Transfer Learning",
        18: "Anomaly Detection",
        19: "Reinforcement Learning",
        20: "Neural Architecture Search",
        21: "Confidence Calibration",
        22: "Error Feedback",
        23: "Final Ensemble",
        24: "Safety Verification",
        25: "Production Optimization"
    }
    
    # Check each round
    complete_count = 0
    running_count = 0
    best_fp = 0
    best_round = 0
    
    print("\n📊 ROUND STATUS:")
    print("-"*100)
    print(f"{'Round':<6} {'Strategy':<30} {'Status':<12} {'FP Rate':<10} {'Valid':<10} {'Notes':<30}")
    print("-"*100)
    
    for round_num in range(3, 26):
        status_info = get_round_status(round_num)
        status = status_info['status']
        
        if status == 'complete':
            complete_count += 1
            fp_rate = status_info['fp_rate']
            valid_rate = status_info['valid_rate']
            
            if fp_rate > best_fp:
                best_fp = fp_rate
                best_round = round_num
            
            status_icon = "✅"
            fp_str = f"{fp_rate:.1f}%"
            valid_str = f"{valid_rate:.1f}%"
            notes = "🏆 BEST!" if round_num == best_round else ""
            
        elif status == 'running':
            running_count += 1
            status_icon = "🔄"
            fp_str = "-"
            valid_str = "-"
            notes = status_info.get('last_log', 'Processing...')[:30]
            
        else:
            status_icon = "⏳"
            fp_str = "-"
            valid_str = "-"
            notes = "Waiting..."
        
        print(f"{round_num:<6} {rounds_info.get(round_num, 'Unknown'):<30} {status_icon} {status:<10} {fp_str:<10} {valid_str:<10} {notes:<30}")
    
    # Summary
    print("\n" + "="*100)
    print(f"SUMMARY: {complete_count}/23 rounds complete | {running_count} running | Best: Round {best_round} ({best_fp:.1f}% FP)")
    
    # Check for final report
    if os.path.exists('ALL_ROUNDS_COMPLETE_REPORT.json'):
        print("\n🎉 ALL ROUNDS COMPLETE! Final report available.")
        with open('ALL_ROUNDS_COMPLETE_REPORT.json', 'r') as f:
            report = json.load(f)
        print(f"   Best approach: Round {report['best_round']['number']} - {report['best_round']['strategy']}")
        print(f"   Achievement: {report['best_round']['fp_rate']:.1f}% FP reduction")
    
    # System health
    print("\n💚 SYSTEM STATUS:")
    ps_count = len(subprocess.run(['ps', 'aux'], capture_output=True, text=True).stdout.splitlines())
    print(f"   Active processes: {ps_count}")
    
    if os.path.exists('health_status.json'):
        try:
            with open('health_status.json', 'r') as f:
                health = json.load(f)
                print(f"   VLM API: {'✅' if health['vlm_api']['status'] else '❌'}")
                print(f"   Last health check: {health['timestamp'][-8:]}")
        except:
            pass

# Main loop
print("Starting comprehensive monitor...")
print("This will update every 30 seconds. Press Ctrl+C to exit.")
time.sleep(2)

try:
    while True:
        display_dashboard()
        
        # Check if all complete
        if os.path.exists('ALL_ROUNDS_COMPLETE_REPORT.json'):
            print("\n\n✅ All rounds complete! Monitoring will continue...")
        
        time.sleep(30)
        
except KeyboardInterrupt:
    print("\n\nMonitoring stopped.")