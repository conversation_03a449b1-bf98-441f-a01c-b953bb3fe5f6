#!/usr/bin/env python3
"""
Complete ALL remaining rounds tonight (Rounds 7-25)
Coordinates with already running Round 11 and uses existing orchestrator
"""

import json
import subprocess
import time
import os
import glob
from datetime import datetime

print("="*80)
print("COMPLETING ALL REMAINING ROUNDS TONIGHT")
print(f"Current time: {datetime.now().strftime('%H:%M:%S')}")
print("="*80)

# Check what's already complete
completed_rounds = []
for i in range(1, 26):
    if glob.glob(f'valo_round{i}_*_complete.json'):
        completed_rounds.append(i)

print(f"\nCompleted rounds: {completed_rounds}")

# Check what's running
running_processes = subprocess.run(['ps', 'aux'], capture_output=True, text=True).stdout
round11_running = 'round11' in running_processes
print(f"Round 11 running: {round11_running}")

# Rounds to complete tonight
all_rounds = list(range(7, 26))
rounds_to_complete = [r for r in all_rounds if r not in completed_rounds]

# If Round 11 is running, remove it from the list
if round11_running and 11 in rounds_to_complete:
    rounds_to_complete.remove(11)
    print("Round 11 is already running, will wait for completion")

print(f"\nRounds to complete: {rounds_to_complete}")
print(f"Total rounds remaining: {len(rounds_to_complete)}")

# Create a modified orchestrator that skips completed rounds
modified_orchestrator = '''#!/usr/bin/env python3
"""
Modified orchestrator to complete specific rounds only
"""
import json
import asyncio
import subprocess
import os
import time
import logging
from datetime import datetime

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(message)s',
    handlers=[
        logging.FileHandler('complete_all_rounds.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

ROUNDS_TO_COMPLETE = ''' + str(rounds_to_complete) + '''

# Import the original orchestrator's round definitions
import sys
sys.path.append('/home/<USER>/VALO_AI-FARM_2025')
from overnight_orchestrator_25rounds import OvernightOrchestrator25

class CompleteAllRoundsOrchestrator(OvernightOrchestrator25):
    async def run_all_rounds(self):
        """Run only the remaining rounds"""
        self.log_status("="*80)
        self.log_status("COMPLETING ALL REMAINING ROUNDS")
        self.log_status(f"Rounds to complete: {ROUNDS_TO_COMPLETE}")
        self.log_status("="*80)
        
        # Start health monitor if not running
        if not any('health_monitor' in line for line in subprocess.run(['ps', 'aux'], capture_output=True, text=True).stdout.splitlines()):
            subprocess.Popen(['python3', 'health_monitor.py'])
            self.log_status("Health monitor started")
        
        # Run each remaining round
        for round_num in ROUNDS_TO_COMPLETE:
            # Skip if already complete
            import glob
            if glob.glob(f'valo_round{round_num}_*_complete.json'):
                self.log_status(f"Round {round_num} already complete, skipping")
                continue
            
            # Run the round
            self.log_status(f"\\nStarting Round {round_num}")
            success = await self.run_round(round_num)
            
            if not success:
                self.log_status(f"Round {round_num} failed, continuing anyway")
            else:
                self.log_status(f"Round {round_num} complete")
            
            # Brief pause
            await asyncio.sleep(5)
        
        # Generate final report
        self.generate_comprehensive_report()
    
    def generate_comprehensive_report(self):
        """Generate report of all rounds"""
        all_results = {}
        
        # Load all completed rounds
        import glob
        for i in range(3, 26):
            files = glob.glob(f'valo_round{i}_*_complete.json')
            if files:
                with open(files[0], 'r') as f:
                    data = json.load(f)
                    stats = data.get('stats', {})
                    all_results[i] = {
                        'fp_rate': stats.get('fp_detection_rate', 0),
                        'valid_protection': stats.get('valid_protection_rate', 100),
                        'strategy': data.get('strategy', 'Unknown')
                    }
        
        # Find best round
        best_round = max(all_results.items(), key=lambda x: x[1]['fp_rate'])
        
        report = {
            'completion_time': datetime.now().isoformat(),
            'rounds_completed': list(all_results.keys()),
            'best_round': {
                'number': best_round[0],
                'fp_rate': best_round[1]['fp_rate'],
                'strategy': best_round[1]['strategy']
            },
            'all_results': all_results,
            'recommendation': f"Use Round {best_round[0]} ({best_round[1]['strategy']}) for production with {best_round[1]['fp_rate']:.1f}% FP reduction"
        }
        
        with open('ALL_ROUNDS_COMPLETE_REPORT.json', 'w') as f:
            json.dump(report, f, indent=2)
        
        self.log_status("\\n" + "="*80)
        self.log_status("ALL ROUNDS COMPLETE!")
        self.log_status(f"Best performance: Round {best_round[0]} with {best_round[1]['fp_rate']:.1f}% FP reduction")
        self.log_status("Full report: ALL_ROUNDS_COMPLETE_REPORT.json")
        self.log_status("="*80)

async def main():
    orchestrator = CompleteAllRoundsOrchestrator()
    await orchestrator.run_all_rounds()

if __name__ == "__main__":
    asyncio.run(main())
'''

# Save the modified orchestrator
with open('complete_all_rounds_orchestrator.py', 'w') as f:
    f.write(modified_orchestrator)

os.chmod('complete_all_rounds_orchestrator.py', 0o755)

print("\n" + "="*80)
print("LAUNCHING SYSTEM TO COMPLETE ALL ROUNDS")
print("="*80)

# If Round 11 is running, wait for it
if round11_running:
    print("\nWaiting for Round 11 to complete...")
    while not glob.glob('valo_round11_*_complete.json'):
        time.sleep(30)
        print(".", end='', flush=True)
    print(" Round 11 complete!")

# Launch the orchestrator
print("\nLaunching orchestrator for remaining rounds...")
subprocess.Popen(['python3', 'complete_all_rounds_orchestrator.py'])

print("\n✅ System launched!")
print("\nMonitor progress:")
print("  tail -f complete_all_rounds.log")
print("\nExpected completion time: 4-6 hours")
print("All results will be in: ALL_ROUNDS_COMPLETE_REPORT.json")
print("="*80)