#!/usr/bin/env python3
"""
Run comprehensive approach testing with monitoring and progress tracking
"""

import subprocess
import time
import json
import os
from datetime import datetime

def monitor_progress():
    """Monitor the progress of the comprehensive test"""
    
    progress_file = 'multi_approach_progress.json'
    
    print("="*80)
    print("COMPREHENSIVE APPROACH TESTING - MONITOR")
    print("="*80)
    print("Starting test process...")
    
    # Start the main test in subprocess
    process = subprocess.Popen(['python3', 'test_multiple_approaches_all_1250.py'], 
                             stdout=subprocess.PIPE, 
                             stderr=subprocess.PIPE,
                             universal_newlines=True,
                             bufsize=1)
    
    last_update = None
    
    try:
        while True:
            # Check if process is still running
            if process.poll() is not None:
                print("\n✓ Process completed!")
                break
            
            # Check progress file
            if os.path.exists(progress_file):
                with open(progress_file, 'r') as f:
                    progress = json.load(f)
                
                # Show update if changed
                current_update = json.dumps(progress.get('results', {}))
                if current_update != last_update:
                    last_update = current_update
                    
                    print(f"\n[{datetime.now().strftime('%H:%M:%S')}] Progress Update:")
                    
                    for approach, result in progress['results'].items():
                        print(f"\n✓ {result['name']}: COMPLETE")
                        metrics = result['metrics']
                        print(f"  - Accuracy: {metrics['accuracy']:.1f}%")
                        print(f"  - FP Detection: {metrics['fp_detection']:.1f}%")
                        print(f"  - Valid Protection: {metrics['valid_protection']:.1f}%")
            
            # Read any output
            line = process.stdout.readline()
            if line:
                print(line.strip())
            
            time.sleep(2)
            
    except KeyboardInterrupt:
        print("\n\nInterrupted by user. Terminating process...")
        process.terminate()
        
    except Exception as e:
        print(f"\nError monitoring: {e}")
        process.terminate()
    
    # Get final output
    stdout, stderr = process.communicate()
    if stdout:
        print("\nFinal output:")
        print(stdout)
    if stderr:
        print("\nErrors:")
        print(stderr)
    
    # Show final results if available
    if os.path.exists('multi_approach_comparison_report.json'):
        print("\n" + "="*80)
        print("FINAL RESULTS AVAILABLE")
        print("="*80)
        print("View full report: multi_approach_comparison_report.json")
        
        with open('multi_approach_comparison_report.json', 'r') as f:
            report = json.load(f)
        
        print(f"\nBest approach: {report['best_approach']}")
        print("\nConclusions:")
        for conclusion in report['conclusions']:
            print(f"  - {conclusion}")

if __name__ == "__main__":
    monitor_progress()