#!/usr/bin/env python3
"""
Analyze how many false positives can be filtered by "no person = FP" rule
Check if valid alerts are still correctly identified
"""

import json
import re
from pathlib import Path
from datetime import datetime

class NoPersonFilterAnalyzer:
    def __init__(self):
        self.results = {
            'total_analyzed': 0,
            'no_person_cases': 0,
            'with_person_cases': 0,
            'filterable_fps': 0,
            'critical_errors': 0,
            'accuracy_improvement': 0,
            'details': {
                'no_person_in_fp': [],
                'no_person_in_tp': [],  # Critical - these would be wrongly filtered
                'person_in_fp': [],     # Missed opportunities
                'person_in_tp': []      # Correct detections
            }
        }
        
    def load_comprehensive_data(self):
        """Load the comprehensive VLM analysis data"""
        all_cases = {}
        
        # Load pattern analysis which has person presence data
        pattern_file = Path('valo_comprehensive_data/analysis/pattern_analysis.json')
        if pattern_file.exists():
            with open(pattern_file, 'r') as f:
                pattern_data = json.load(f)
                return pattern_data
                
        return None
        
    def analyze_no_person_rule(self):
        """Analyze effectiveness of no-person filtering rule"""
        pattern_data = self.load_comprehensive_data()
        
        if not pattern_data:
            print("Pattern data not found, loading from markdown files...")
            self.analyze_from_markdown()
            return
            
        # Extract person presence patterns
        fp_no_person = pattern_data.get('false_positive_patterns', {}).get('no_person_cases', 0)
        fp_total = pattern_data.get('false_positive_patterns', {}).get('total_cases', 0)
        tp_no_person = pattern_data.get('true_positive_patterns', {}).get('no_person_cases', 0)
        tp_total = pattern_data.get('true_positive_patterns', {}).get('total_cases', 0)
        
        print(f"\n📊 No-Person Filtering Analysis")
        print(f"{'='*50}")
        print(f"\nFALSE POSITIVES (Can be filtered):")
        print(f"  - Total FP cases: {fp_total}")
        print(f"  - FP with NO person: {fp_no_person} ({fp_no_person/fp_total*100:.1f}%)")
        print(f"  - These can be safely filtered! ✅")
        
        print(f"\nTRUE POSITIVES (Must NOT filter):")
        print(f"  - Total TP cases: {tp_total}")
        print(f"  - TP with NO person: {tp_no_person} ({tp_no_person/tp_total*100:.1f}%)")
        if tp_no_person > 0:
            print(f"  - ⚠️ WARNING: {tp_no_person} valid cases would be wrongly filtered!")
        else:
            print(f"  - ✅ Good: All valid cases have people")
            
        # Calculate effectiveness
        total_cases = fp_total + tp_total
        correctly_filtered = fp_no_person
        wrongly_filtered = tp_no_person
        net_improvement = correctly_filtered - wrongly_filtered
        
        print(f"\n📈 FILTERING EFFECTIVENESS:")
        print(f"  - Cases correctly filtered: {correctly_filtered}")
        print(f"  - Cases wrongly filtered: {wrongly_filtered}")
        print(f"  - Net improvement: {net_improvement} cases")
        print(f"  - FP reduction rate: {correctly_filtered/fp_total*100:.1f}%")
        
        # Save results
        self.results['total_analyzed'] = total_cases
        self.results['filterable_fps'] = correctly_filtered
        self.results['critical_errors'] = wrongly_filtered
        self.results['fp_reduction_rate'] = correctly_filtered/fp_total*100 if fp_total > 0 else 0
        
    def analyze_from_markdown(self):
        """Analyze directly from markdown files"""
        # Parse FALSE POSITIVE cases
        fp_file = Path('valo_comprehensive_data/false_positives/false_positive_analysis_20250725_232934.md')
        tp_file = Path('valo_comprehensive_data/true_positives/true_positive_analysis_20250725_232934.md')
        
        fp_no_person = 0
        fp_with_person = 0
        tp_no_person = 0
        tp_with_person = 0
        
        # Analyze FP cases
        if fp_file.exists():
            with open(fp_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
            cases = re.split(r'\n## Case:', content)
            for case in cases[1:]:  # Skip header
                # Check for person presence indicators
                case_lower = case.lower()
                
                # Strong indicators of NO person
                no_person_indicators = [
                    '**person present**: no',
                    'person present: no',
                    'no person visible',
                    'no individuals visible',
                    'none visible',
                    'visible individuals:** none',
                    'number of individuals:** none',
                    'primary subject type:** equipment',
                    'primary subject type:** structure'
                ]
                
                # Check if NO person
                has_no_person = any(indicator in case_lower for indicator in no_person_indicators)
                
                if has_no_person:
                    fp_no_person += 1
                    # Extract case number
                    case_num_match = re.match(r'\s*(\w+)', case)
                    if case_num_match:
                        self.results['details']['no_person_in_fp'].append(case_num_match.group(1))
                else:
                    fp_with_person += 1
                    
        # Analyze TP cases
        if tp_file.exists():
            with open(tp_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
            cases = re.split(r'\n## Case:', content)
            for case in cases[1:]:  # Skip header
                case_lower = case.lower()
                
                # Same indicators
                no_person_indicators = [
                    '**person present**: no',
                    'person present: no',
                    'no person visible',
                    'no individuals visible',
                    'none visible',
                    'visible individuals:** none',
                    'number of individuals:** none',
                    'primary subject type:** equipment',
                    'primary subject type:** structure'
                ]
                
                has_no_person = any(indicator in case_lower for indicator in no_person_indicators)
                
                if has_no_person:
                    tp_no_person += 1
                    # Extract case number for critical errors
                    case_num_match = re.match(r'\s*(\w+)', case)
                    if case_num_match:
                        self.results['details']['no_person_in_tp'].append(case_num_match.group(1))
                else:
                    tp_with_person += 1
                    
        # Calculate and display results
        total_fp = fp_no_person + fp_with_person
        total_tp = tp_no_person + tp_with_person
        total_cases = total_fp + total_tp
        
        print(f"\n📊 No-Person Filtering Analysis (from raw data)")
        print(f"{'='*60}")
        
        print(f"\n🔴 FALSE POSITIVES (Invalid alerts):")
        print(f"  - Total: {total_fp}")
        print(f"  - NO person: {fp_no_person} ({fp_no_person/total_fp*100:.1f}%)")
        print(f"  - With person: {fp_with_person} ({fp_with_person/total_fp*100:.1f}%)")
        print(f"  ✅ Can filter {fp_no_person} false alarms!")
        
        print(f"\n🟢 TRUE POSITIVES (Valid violations):")
        print(f"  - Total: {total_tp}")
        print(f"  - NO person: {tp_no_person} ({tp_no_person/total_tp*100:.1f}%)")
        print(f"  - With person: {tp_with_person} ({tp_with_person/total_tp*100:.1f}%)")
        
        if tp_no_person > 0:
            print(f"\n⚠️  CRITICAL WARNING:")
            print(f"  {tp_no_person} valid violations have NO person!")
            print(f"  Sample cases: {self.results['details']['no_person_in_tp'][:5]}")
            print(f"  These might be equipment violations that don't require a person")
        
        print(f"\n📈 FILTERING EFFECTIVENESS:")
        print(f"  - False positives filtered: {fp_no_person} ({fp_no_person/total_fp*100:.1f}% of all FPs)")
        print(f"  - Valid cases preserved: {tp_with_person} ({tp_with_person/total_tp*100:.1f}% of all TPs)")
        print(f"  - Critical errors (valid filtered): {tp_no_person}")
        print(f"  - Net benefit: {fp_no_person - tp_no_person} fewer alerts")
        
        # Overall improvement
        current_fp_rate = total_fp / total_cases * 100
        after_filtering_fp = (total_fp - fp_no_person) / (total_cases - fp_no_person) * 100
        
        print(f"\n🎯 OVERALL IMPROVEMENT:")
        print(f"  - Current FP rate: {current_fp_rate:.1f}%")
        print(f"  - After filtering: {after_filtering_fp:.1f}%")
        print(f"  - FP rate reduction: {current_fp_rate - after_filtering_fp:.1f} percentage points")
        
        # Save detailed results
        self.save_results(fp_no_person, fp_with_person, tp_no_person, tp_with_person)
        
    def save_results(self, fp_no_person, fp_with_person, tp_no_person, tp_with_person):
        """Save analysis results"""
        results = {
            'timestamp': datetime.now().isoformat(),
            'summary': {
                'total_cases': fp_no_person + fp_with_person + tp_no_person + tp_with_person,
                'false_positives': {
                    'total': fp_no_person + fp_with_person,
                    'no_person': fp_no_person,
                    'with_person': fp_with_person,
                    'filterable_percentage': fp_no_person/(fp_no_person + fp_with_person)*100
                },
                'true_positives': {
                    'total': tp_no_person + tp_with_person,
                    'no_person': tp_no_person,
                    'with_person': tp_with_person,
                    'at_risk_percentage': tp_no_person/(tp_no_person + tp_with_person)*100
                },
                'filtering_effectiveness': {
                    'fps_filtered': fp_no_person,
                    'tps_wrongly_filtered': tp_no_person,
                    'net_improvement': fp_no_person - tp_no_person,
                    'accuracy_after_filtering': tp_with_person / (fp_with_person + tp_with_person) * 100
                }
            },
            'recommendation': self.get_recommendation(tp_no_person)
        }
        
        with open('no_person_filtering_analysis.json', 'w') as f:
            json.dump(results, f, indent=2)
            
        # Create simple report
        report = f"""# No-Person Filtering Analysis

## Executive Summary
By filtering alerts where no person is visible in the cropped image:
- **Can filter**: {fp_no_person} false positives ({fp_no_person/(fp_no_person + fp_with_person)*100:.1f}%)
- **Risk**: {tp_no_person} valid cases would be filtered
- **Net benefit**: {fp_no_person - tp_no_person} fewer false alarms

## Recommendation
{self.get_recommendation(tp_no_person)}

## Detailed Results
- False Positives: {fp_no_person + fp_with_person} total
  - No person: {fp_no_person} ✅ (can filter)
  - With person: {fp_with_person} (need other methods)
  
- True Positives: {tp_no_person + tp_with_person} total
  - No person: {tp_no_person} ⚠️ (risk of filtering valid cases)
  - With person: {tp_with_person} ✅ (correctly preserved)
"""
        
        with open('no_person_filtering_report.md', 'w') as f:
            f.write(report)
            
        print(f"\n📁 Results saved to:")
        print(f"   - no_person_filtering_analysis.json")
        print(f"   - no_person_filtering_report.md")
        
    def get_recommendation(self, tp_no_person):
        """Get recommendation based on results"""
        if tp_no_person == 0:
            return "✅ SAFE TO IMPLEMENT: No valid cases would be filtered. Implement 'no person = FP' rule immediately."
        elif tp_no_person < 10:
            return "⚠️ IMPLEMENT WITH REVIEW: Only a few valid cases at risk. Review these cases manually before implementing."
        else:
            return "🚫 NEEDS REFINEMENT: Too many valid cases would be filtered. Need to understand equipment-only violations first."

if __name__ == "__main__":
    analyzer = NoPersonFilterAnalyzer()
    analyzer.analyze_from_markdown()