# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

AI-FARM is a proof-of-concept system designed to solve the false positive crisis in safety violation detection systems. The system uses Vision Language Models (VLMs) to intelligently filter safety alerts, reducing false positives by 70% and saving significant manual review time.

**Core Problem**: Safety monitoring systems generate 97% false positive alerts, overwhelming safety teams with thousands of useless alarms while real violations might slip through.

**Solution**: AI-FARM processes safety alert images through VLM analysis to automatically identify and filter false positives, allowing teams to focus on genuine safety violations.

## Architecture

The system follows a 3-stage pipeline:
1. **Data Input**: CSV data + violation images + human validation results
2. **VLM Processing**: Batch analysis through InternVL3 38B model
3. **Customer Dashboard**: Live demo interface showing results and ROI

### Key Components

- **VLM Integration Service**: Interfaces with external VLM API for image analysis
- **Batch Processing Engine**: Handles CSV data processing and image analysis
- **Auto-Learning Engine**: Adapts to customer-specific patterns and optimizes thresholds
- **Customer Dashboard**: React-based interface for live demo presentations
- **ROI Calculator**: Real-time cost savings computation based on actual data

## Technology Stack

- **Backend**: Python + FastAPI + SQLAlchemy + Pydantic
- **Frontend**: React + TypeScript + Tailwind CSS + Create React App
- **Database**: PostgreSQL (production) / SQLite (development)
- **Deployment**: Docker + Docker Compose
- **Additional Services**: Redis (caching), MCP Server (Puppeteer automation)
- **VLM API**: VLM-38B-AWQ endpoint (http://**************:9500/v1)
- **Testing**: Pytest (backend), Jest + React Testing Library (frontend), Puppeteer (E2E)

## Data Structure

The system processes CSV files with the following structure:
- `pk_event`: Primary key for the event
- `case_number`: Unique case identifier (e.g., V1250630118)
- `url`: Path to violation image file
- `key`: Human validation result (`invalid` for false positives)

## Auto-Learning Capabilities

The system includes several adaptive mechanisms:
- **Pattern Recognition**: Identifies customer-specific false positive patterns
- **Threshold Optimization**: Adjusts confidence thresholds based on customer data
- **Real-Time Performance Tracking**: Updates metrics during live processing
- **Custom Prompt Generation**: Creates customer-specific VLM prompts

## Demo Flow

The system is designed for live customer demonstrations:
1. **Crisis Presentation**: Show current false positive problem
2. **Live Processing**: Customer uploads their actual data for real-time analysis
3. **Auto-Learning**: System learns and optimizes from customer data
4. **Results**: Display customized ROI and savings projections

## Development Commands

### Quick Start
```bash
# Start all services (development)
npm run dev

# Start with Docker
npm run docker:up

# Individual services
npm run dev:backend      # Start FastAPI backend
npm run dev:frontend     # Start React frontend
```

### Build Commands
```bash
npm run build           # Build frontend for production
npm run docker:build    # Build Docker containers
npm run start:production # Start production environment
```

### Testing Commands
```bash
npm run test           # Run all tests
npm run test:backend   # Backend unit tests
npm run test:frontend  # Frontend tests
npm run test:e2e       # End-to-end tests with Puppeteer
```

### Utility Commands
```bash
npm run clean          # Clean node_modules and cache
npm run install:all    # Install all dependencies
npm run mcp:setup      # Setup MCP servers
```

### Docker Commands
```bash
npm run docker:up     # Start all services
npm run docker:down   # Stop all services
npm run docker:build  # Build containers
```

## Key Business Metrics

- **Target**: 70% false positive reduction
- **ROI**: $300K+ annual savings potential
- **Payback**: 3-4 months
- **Processing Speed**: Real-time batch analysis capability

## Implementation Phases

1. **Phase 1**: Core VLM integration and batch processing
2. **Phase 2**: Customer dashboard and visualization
3. **Phase 3**: Demo polish and performance optimization

## Security Considerations

This is a defensive security tool designed to improve safety monitoring systems. The system processes safety violation images and metadata but does not contain malicious functionality.

## Development Rules & Standards

### Project Structure Rules

1. **Directory Organization** (Updated 2025)
   ```
   ai-farm/
   ├── backend/                 # Python FastAPI backend
   │   ├── app/
   │   │   ├── api/            # API endpoints and routes
   │   │   ├── core/           # Core business logic & config
   │   │   ├── models/         # SQLAlchemy data models
   │   │   ├── schemas/        # Pydantic validation schemas
   │   │   ├── services/       # External integrations (VLM, etc)
   │   │   └── utils/          # Utility functions
   │   ├── tests/              # Backend unit & integration tests
   │   ├── data/               # Backend data processing
   │   ├── logs/               # Application logs
   │   └── requirements.txt    # Python dependencies
   ├── frontend/               # React TypeScript dashboard
   │   ├── src/
   │   │   ├── components/     # Reusable UI components
   │   │   ├── pages/          # Application pages
   │   │   ├── services/       # API integration layer
   │   │   ├── hooks/          # Custom React hooks
   │   │   ├── types/          # TypeScript type definitions
   │   │   └── utils/          # Frontend utilities
   │   ├── public/             # Static assets
   │   ├── build/              # Production build output
   │   └── package.json        # Node dependencies
   ├── mcp-server/             # Model Context Protocol server
   │   ├── src/                # MCP implementation
   │   └── dist/               # Compiled output
   ├── docker/                 # Docker configurations
   │   ├── docker-compose.yml  # Development environment
   │   └── docker-compose.prod.yml # Production environment
   ├── database/               # Database configurations
   │   └── init/               # PostgreSQL init scripts
   ├── docs/                   # Project documentation
   │   ├── api/                # API documentation
   │   ├── architecture/       # System design docs
   │   └── deployment/         # Deployment guides
   ├── scripts/                # Utility scripts
   │   └── image-copy/         # Image processing utilities
   ├── data/                   # Sample and test data
   ├── logs/                   # Application logs
   ├── package.json            # Root project configuration
   └── .gitignore              # Git ignore patterns
   ```

2. **File Naming Conventions**
   - Python: `snake_case` for files, classes as `PascalCase`
   - JavaScript/React: `camelCase` for files, components as `PascalCase`
   - Configuration files: `kebab-case`
   - Documentation: `UPPERCASE.md` for important docs, `lowercase.md` for others

3. **Code Organization**
   - Each module should have a single responsibility
   - Services should be separated from business logic
   - API endpoints should be grouped by functionality
   - Shared utilities should be in dedicated modules

### Documentation Standards

1. **Code Documentation**
   - Every function/method must have docstrings explaining purpose, parameters, and return values
   - Complex algorithms require inline comments explaining the logic
   - All API endpoints need OpenAPI/Swagger documentation
   - Database models require field descriptions

2. **README Requirements**
   - Each major directory needs a README.md explaining its purpose
   - Include setup instructions, dependencies, and usage examples
   - Document API endpoints with request/response examples
   - Provide troubleshooting section for common issues

3. **Change Documentation**
   - Maintain CHANGELOG.md with version history
   - Document breaking changes and migration steps
   - Include performance improvements and bug fixes
   - Use semantic versioning (MAJOR.MINOR.PATCH)

4. **Architecture Documentation**
   - Create architecture diagrams showing data flow
   - Document VLM API integration patterns
   - Explain auto-learning algorithms and thresholds
   - Include database schema documentation

### Code Quality Rules

1. **Python Backend Standards**
   - Use type hints for all function parameters and return values
   - Follow PEP 8 style guide with line length of 88 characters
   - Use dataclasses or Pydantic models for data structures
   - Implement proper error handling with custom exceptions
   - Write unit tests with >80% coverage

2. **React Frontend Standards**
   - Use TypeScript for type safety
   - Follow React functional components with hooks
   - Implement proper error boundaries
   - Use consistent state management (Context API or Redux)
   - Write component tests with React Testing Library

3. **API Design Standards**
   - RESTful endpoint design with proper HTTP methods
   - Consistent response format with success/error patterns
   - Input validation on all endpoints
   - Rate limiting for VLM API calls
   - Proper HTTP status codes

### Development Workflow Rules

1. **Version Control**
   - Meaningful commit messages following conventional commits
   - Feature branches for new functionality
   - Code review required before merging
   - No direct commits to main branch

2. **Testing Requirements**
   - Unit tests for all business logic
   - Integration tests for VLM API integration
   - End-to-end tests for demo workflow
   - Performance tests for batch processing

3. **Environment Management**
   - Separate configuration for dev/staging/prod
   - Environment variables for sensitive data
   - Docker containers for consistent environments
   - Local development setup documentation

### Performance & Monitoring

1. **Performance Standards**
   - VLM API response time <5 seconds per image
   - Batch processing progress tracking
   - Frontend load time <3 seconds
   - Database query optimization

2. **Logging & Monitoring**
   - Structured logging with correlation IDs
   - Error tracking and alerting
   - Performance metrics collection
   - VLM API usage monitoring

### Demo-Specific Rules

1. **Demo Data Management**
   - Sanitized sample data for demonstrations
   - Reproducible demo scenarios
   - Backup demo data and configurations
   - Demo reset functionality

2. **Customer Data Handling**
   - No customer data persistence without consent
   - Secure temporary storage for demo uploads
   - Data cleanup after demo sessions
   - Privacy compliance documentation

### Maintenance Rules

1. **Dependency Management**
   - Regular security updates
   - Pin dependency versions
   - Document breaking changes
   - Test compatibility before updates

2. **Documentation Maintenance**
   - Update documentation with code changes
   - Regular review of setup instructions
   - Keep API documentation current
   - Maintain troubleshooting guides

## Common Workflows

### Development Setup
1. **Initial Setup**:
   ```bash
   npm run install:all    # Install all dependencies
   cp .env.example .env   # Configure environment
   npm run docker:up      # Start services with Docker
   ```

2. **Development Mode**:
   ```bash
   npm run dev           # Start both frontend and backend
   # OR start individually:
   npm run dev:backend   # Backend on :8000
   npm run dev:frontend  # Frontend on :3000
   ```

### Testing Workflow
1. **Unit Tests**: `npm run test:backend` and `npm run test:frontend`
2. **Integration Tests**: `npm run test:e2e`
3. **Manual Testing**: Use `/docs` endpoint for API testing

### Deployment Workflow
1. **Build**: `npm run build`
2. **Production**: `npm run start:production`
3. **Monitoring**: Check logs in `logs/` directory

### MCP Server Usage
The project includes a Model Context Protocol server for advanced automation:
```bash
npm run mcp:setup      # Install MCP server to Claude
./setup-mcp-servers.sh # Manual setup
```

### Image Processing Utilities
Use scripts in `scripts/image-copy/` for processing VALO violation images:
- Copy images by case number
- Organize image datasets
- Process customer data uploads

## Verification Protocols

### Multi-Agent Verification Guidelines
- Always use sub-agents for final verification
- No single agent should approve production readiness
- Puppeteer MCP must complete full E2E suite before handoff
- Sequential Thinking MCP should validate all logical flows

## Project Status (2025)

**Current Version**: 1.0.0
**Last Reorganization**: July 2025
**Project Size**: ~500MB (optimized from 2GB+)
**Architecture**: Clean, industry-standard structure
**Build Status**: All systems operational
**Test Coverage**: Comprehensive E2E and unit tests

These rules ensure the AI-FARM project remains maintainable, well-documented, and professional throughout development.