#!/usr/bin/env python3
"""
Monitor Round 3 Production Test Progress
Shows real-time progress and metrics
"""

import json
import os
import time
from datetime import datetime

def monitor_progress():
    """Monitor the Round 3 production test progress"""
    
    progress_file = 'round3_production_real_progress.json'
    
    print("ROUND 3 PRODUCTION TEST - REAL-TIME MONITOR")
    print("="*60)
    print("Press Ctrl+C to stop monitoring\n")
    
    last_update = None
    
    while True:
        try:
            if os.path.exists(progress_file):
                with open(progress_file, 'r') as f:
                    progress = json.load(f)
                
                # Get current stats
                total_tested = len(progress.get('completed_cases', []))
                results = progress.get('results', [])
                last_index = progress.get('last_index', 0)
                
                # Only update if changed
                current_update = f"{total_tested}-{last_index}"
                if current_update != last_update:
                    last_update = current_update
                    
                    # Clear screen
                    print("\033[2J\033[H")
                    print("ROUND 3 PRODUCTION TEST - REAL-TIME MONITOR")
                    print("="*60)
                    print(f"Last Update: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
                    
                    print(f"\nProgress: {total_tested} cases completed")
                    print(f"Current Index: {last_index}/1250")
                    
                    if results:
                        # Calculate metrics
                        correct = sum(r['correct'] for r in results)
                        actual_fps = [r for r in results if r['actual_fp']]
                        actual_valid = [r for r in results if not r['actual_fp']]
                        
                        fp_detected = sum(r['predicted_fp'] for r in actual_fps) if actual_fps else 0
                        valid_protected = sum(not r['predicted_fp'] for r in actual_valid) if actual_valid else 0
                        valid_missed = len(actual_valid) - valid_protected if actual_valid else 0
                        
                        accuracy = (correct / len(results) * 100) if results else 0
                        fp_rate = (fp_detected / len(actual_fps) * 100) if actual_fps else 0
                        valid_rate = (valid_protected / len(actual_valid) * 100) if actual_valid else 0
                        
                        print(f"\n📊 CURRENT METRICS:")
                        print(f"├─ Overall Accuracy: {accuracy:.2f}%")
                        print(f"├─ FP Detection: {fp_rate:.2f}% ({fp_detected}/{len(actual_fps)})")
                        print(f"├─ Valid Protection: {valid_rate:.2f}% ({valid_protected}/{len(actual_valid)})")
                        print(f"└─ Valid Missed: {valid_missed}")
                        
                        # Target status
                        print(f"\n🎯 TARGET STATUS:")
                        print(f"├─ FP Reduction: {'✅' if fp_rate >= 70 else '❌'} {fp_rate:.1f}% (Target: 70%)")
                        print(f"└─ Valid Protection: {'✅' if valid_rate >= 98 else '❌'} {valid_rate:.1f}% (Target: 98%)")
                        
                        # Recent results
                        print(f"\n📝 LAST 5 RESULTS:")
                        for r in results[-5:]:
                            status = "✓" if r['correct'] else "✗"
                            fp_status = "FP" if r['actual_fp'] else "Valid"
                            print(f"  {status} {r['case_number']} - {fp_status} - {r['entity']}")
                
                print("\n" + "-"*60)
                print("Monitoring... (updates every 5 seconds)")
            
            else:
                print("Waiting for test to start...")
            
            time.sleep(5)
            
        except KeyboardInterrupt:
            print("\n\nMonitoring stopped.")
            break
        except Exception as e:
            print(f"Error reading progress: {e}")
            time.sleep(5)

if __name__ == "__main__":
    monitor_progress()