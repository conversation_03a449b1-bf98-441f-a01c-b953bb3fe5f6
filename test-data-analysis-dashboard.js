#!/usr/bin/env node

const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

class DataAnalysisDashboardTester {
  constructor() {
    this.browser = null;
    this.page = null;
    this.screenshotDir = './data/screenshots';
    this.testResults = {
      timestamp: new Date().toISOString(),
      url: 'http://localhost:3002/data-analysis',
      backend_url: 'http://localhost:8000',
      tests: [],
      console_logs: [],
      network_requests: [],
      errors: []
    };
  }

  async init() {
    // Ensure screenshot directory exists
    if (!fs.existsSync(this.screenshotDir)) {
      fs.mkdirSync(this.screenshotDir, { recursive: true });
    }

    console.log('🚀 Launching browser...');
    this.browser = await puppeteer.launch({
      headless: false, // Set to true for headless mode
      defaultViewport: { width: 1920, height: 1080 },
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--no-first-run',
        '--no-zygote',
        '--disable-gpu'
      ]
    });

    this.page = await this.browser.newPage();
    
    // Set up console log capture
    this.page.on('console', (msg) => {
      const logEntry = {
        type: msg.type(),
        text: msg.text(),
        timestamp: new Date().toISOString()
      };
      this.testResults.console_logs.push(logEntry);
      console.log(`📝 Console [${msg.type()}]:`, msg.text());
    });

    // Set up error capture
    this.page.on('pageerror', (error) => {
      const errorEntry = {
        message: error.message,
        stack: error.stack,
        timestamp: new Date().toISOString()
      };
      this.testResults.errors.push(errorEntry);
      console.log('❌ Page Error:', error.message);
    });

    // Set up network request monitoring
    this.page.on('request', (request) => {
      const requestEntry = {
        url: request.url(),
        method: request.method(),
        timestamp: new Date().toISOString()
      };
      this.testResults.network_requests.push(requestEntry);
      console.log(`🌐 Request: ${request.method()} ${request.url()}`);
    });

    this.page.on('response', (response) => {
      if (response.status() >= 400) {
        console.log(`❌ Failed Request: ${response.status()} ${response.url()}`);
        this.testResults.errors.push({
          type: 'network_error',
          status: response.status(),
          url: response.url(),
          timestamp: new Date().toISOString()
        });
      }
    });
  }

  async testPageLoad() {
    console.log('🔍 Testing page load...');
    const testStart = Date.now();
    
    try {
      await this.page.goto(this.testResults.url, { 
        waitUntil: 'networkidle2',
        timeout: 30000 
      });
      
      const loadTime = Date.now() - testStart;
      
      // Take initial screenshot
      await this.page.screenshot({ 
        path: path.join(this.screenshotDir, 'data-analysis-dashboard-initial.png'),
        fullPage: true 
      });

      this.testResults.tests.push({
        name: 'Page Load',
        status: 'passed',
        duration: loadTime,
        details: `Page loaded in ${loadTime}ms`
      });

      console.log(`✅ Page loaded successfully in ${loadTime}ms`);
      return true;
    } catch (error) {
      this.testResults.tests.push({
        name: 'Page Load',
        status: 'failed',
        error: error.message,
        duration: Date.now() - testStart
      });
      console.log('❌ Page load failed:', error.message);
      return false;
    }
  }

  async testBackendConnectivity() {
    console.log('🔍 Testing backend connectivity...');
    
    try {
      // Test backend health endpoint
      const response = await this.page.evaluate(async () => {
        try {
          const res = await fetch('http://localhost:8000/health');
          return {
            status: res.status,
            ok: res.ok,
            data: await res.text()
          };
        } catch (error) {
          return {
            error: error.message
          };
        }
      });

      if (response.ok) {
        this.testResults.tests.push({
          name: 'Backend Connectivity',
          status: 'passed',
          details: `Backend health check passed: ${response.status}`
        });
        console.log('✅ Backend is accessible');
        return true;
      } else {
        this.testResults.tests.push({
          name: 'Backend Connectivity',
          status: 'failed',
          details: `Backend health check failed: ${JSON.stringify(response)}`
        });
        console.log('❌ Backend connectivity failed:', response);
        return false;
      }
    } catch (error) {
      this.testResults.tests.push({
        name: 'Backend Connectivity',
        status: 'failed',
        error: error.message
      });
      console.log('❌ Backend connectivity test failed:', error.message);
      return false;
    }
  }

  async testUIElements() {
    console.log('🔍 Testing UI elements...');
    
    try {
      // Wait for main content to load
      await this.page.waitForSelector('.surveillance-card', { timeout: 10000 });
      
      // Check for key elements
      const elements = await this.page.evaluate(() => {
        const results = {};
        
        // Check for main dashboard elements
        results.title = document.querySelector('h1')?.textContent || 'No title found';
        results.cards = document.querySelectorAll('.surveillance-card').length;
        results.buttons = document.querySelectorAll('button').length;
        results.autoRefreshToggle = document.querySelector('input[type="checkbox"]') ? true : false;
        results.loadingSpinners = document.querySelectorAll('.animate-spin').length;
        
        return results;
      });

      // Take screenshot after UI elements are loaded
      await this.page.screenshot({ 
        path: path.join(this.screenshotDir, 'data-analysis-dashboard-ui-loaded.png'),
        fullPage: true 
      });

      this.testResults.tests.push({
        name: 'UI Elements',
        status: 'passed',
        details: `Found ${elements.cards} cards, ${elements.buttons} buttons, auto-refresh: ${elements.autoRefreshToggle}`
      });

      console.log('✅ UI elements loaded:', elements);
      return true;
    } catch (error) {
      this.testResults.tests.push({
        name: 'UI Elements',
        status: 'failed',
        error: error.message
      });
      console.log('❌ UI elements test failed:', error.message);
      return false;
    }
  }

  async testInteractiveElements() {
    console.log('🔍 Testing interactive elements...');
    
    try {
      // Test auto-refresh toggle
      const autoRefreshToggle = await this.page.$('input[type="checkbox"]');
      if (autoRefreshToggle) {
        await autoRefreshToggle.click();
        await new Promise(resolve => setTimeout(resolve, 1000));
        console.log('✅ Auto-refresh toggle works');
      }

      // Test analysis buttons
      const analysisButton = await this.page.$('button:contains("Run Analysis")');
      if (analysisButton) {
        await analysisButton.click();
        await new Promise(resolve => setTimeout(resolve, 2000));
        console.log('✅ Analysis button clicked');
      }

      // Take screenshot after interactions
      await this.page.screenshot({ 
        path: path.join(this.screenshotDir, 'data-analysis-dashboard-after-interactions.png'),
        fullPage: true 
      });

      this.testResults.tests.push({
        name: 'Interactive Elements',
        status: 'passed',
        details: 'Interactive elements responded to clicks'
      });

      return true;
    } catch (error) {
      this.testResults.tests.push({
        name: 'Interactive Elements',
        status: 'failed',
        error: error.message
      });
      console.log('❌ Interactive elements test failed:', error.message);
      return false;
    }
  }

  async testDataLoading() {
    console.log('🔍 Testing data loading...');
    
    try {
      // Wait for data to potentially load
      await new Promise(resolve => setTimeout(resolve, 5000));
      
      // Check for data in the dashboard
      const dataStatus = await this.page.evaluate(() => {
        const results = {};
        
        // Check for metrics
        results.metricsCards = document.querySelectorAll('[class*="metric"]').length;
        results.chartElements = document.querySelectorAll('[class*="chart"]').length;
        results.tableRows = document.querySelectorAll('tr').length;
        results.loadingStates = document.querySelectorAll('.animate-pulse').length;
        results.errorMessages = document.querySelectorAll('[class*="error"]').length;
        
        return results;
      });

      // Take final screenshot
      await this.page.screenshot({ 
        path: path.join(this.screenshotDir, 'data-analysis-dashboard-final.png'),
        fullPage: true 
      });

      this.testResults.tests.push({
        name: 'Data Loading',
        status: 'passed',
        details: `Found ${dataStatus.metricsCards} metrics, ${dataStatus.chartElements} charts, ${dataStatus.loadingStates} loading states`
      });

      console.log('✅ Data loading test completed:', dataStatus);
      return true;
    } catch (error) {
      this.testResults.tests.push({
        name: 'Data Loading',
        status: 'failed',
        error: error.message
      });
      console.log('❌ Data loading test failed:', error.message);
      return false;
    }
  }

  async generateReport() {
    console.log('📊 Generating test report...');
    
    const reportPath = path.join(this.screenshotDir, 'test-report.json');
    fs.writeFileSync(reportPath, JSON.stringify(this.testResults, null, 2));
    
    console.log('\n📋 TEST SUMMARY:');
    console.log('================');
    this.testResults.tests.forEach(test => {
      const status = test.status === 'passed' ? '✅' : '❌';
      console.log(`${status} ${test.name}: ${test.details || test.error}`);
    });
    
    console.log(`\n📁 Screenshots saved to: ${this.screenshotDir}`);
    console.log(`📄 Full report saved to: ${reportPath}`);
    
    if (this.testResults.errors.length > 0) {
      console.log('\n🚨 ERRORS FOUND:');
      this.testResults.errors.forEach(error => {
        console.log(`- ${error.message || error.type}: ${error.url || error.stack}`);
      });
    }
  }

  async cleanup() {
    if (this.browser) {
      await this.browser.close();
    }
  }

  async runAllTests() {
    try {
      await this.init();
      
      console.log('🧪 Starting Data Analysis Dashboard Tests...\n');
      
      await this.testPageLoad();
      await this.testBackendConnectivity();
      await this.testUIElements();
      await this.testInteractiveElements();
      await this.testDataLoading();
      
      await this.generateReport();
      
    } catch (error) {
      console.error('❌ Test suite failed:', error);
    } finally {
      await this.cleanup();
    }
  }
}

// Run the tests
if (require.main === module) {
  const tester = new DataAnalysisDashboardTester();
  tester.runAllTests().then(() => {
    console.log('🏁 Test suite completed');
    process.exit(0);
  }).catch(error => {
    console.error('💥 Test suite crashed:', error);
    process.exit(1);
  });
}

module.exports = DataAnalysisDashboardTester;
