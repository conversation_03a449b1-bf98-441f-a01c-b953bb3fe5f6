#!/usr/bin/env python3
"""
Complete testing of all 1250 cases using Friendli AI endpoint
This addresses the VLM timeout issues and completes the user's request
"""

import json
import base64
import asyncio
import aiohttp
import os
from datetime import datetime
from pathlib import Path
import time
from collections import defaultdict

# Friendli AI Configuration from .env.friendli
FRIENDLI_API_URL = "https://api.friendli.ai/dedicated/v1/chat/completions"
FRIENDLI_API_KEY = "flp_Xf21SJG60333K9KZ2qPOs5Uh0CMwltZ5c4xEefBO2pRe2"
FRIENDLI_MODEL = "nhyws8db6r6t"

# The proven production prompt
ASSUMPTION_BASED_PROMPT = """SAFETY ALERT ANALYSIS
DEFAULT ASSUMPTION: This is a false positive (97% probability)
To override this assumption, you need CLEAR evidence:
- Person clearly visible AND  
- No helmet visible AND
- No safety vest visible

Is this a FALSE POSITIVE? YES/NO"""

async def encode_image(image_path):
    """Encode image to base64"""
    if not os.path.exists(image_path):
        return None
    try:
        with open(image_path, 'rb') as f:
            return base64.b64encode(f.read()).decode('utf-8')
    except Exception as e:
        print(f"Error encoding {image_path}: {e}")
        return None

async def call_friendli_vlm(session, image_base64, prompt, retry_count=3):
    """Call Friendli AI VLM with retry logic"""
    headers = {
        "Authorization": f"Bearer {FRIENDLI_API_KEY}",
        "Content-Type": "application/json"
    }
    
    payload = {
        "model": FRIENDLI_MODEL,
        "messages": [{
            "role": "user",
            "content": [
                {"type": "text", "text": prompt},
                {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{image_base64}"}}
            ]
        }],
        "temperature": 0.1,
        "max_tokens": 50
    }
    
    for attempt in range(retry_count):
        try:
            async with session.post(FRIENDLI_API_URL, json=payload, headers=headers, timeout=30) as response:
                if response.status == 200:
                    result = await response.json()
                    return result['choices'][0]['message']['content'].strip()
                else:
                    print(f"Friendli API error: {response.status}")
                    if attempt < retry_count - 1:
                        await asyncio.sleep(2 ** attempt)
        except asyncio.TimeoutError:
            print(f"Timeout on attempt {attempt + 1}")
            if attempt < retry_count - 1:
                await asyncio.sleep(2 ** attempt)
        except Exception as e:
            print(f"Error on attempt {attempt + 1}: {e}")
            if attempt < retry_count - 1:
                await asyncio.sleep(2 ** attempt)
    
    return None

async def process_case_batch(session, cases, start_idx):
    """Process a batch of cases"""
    results = []
    
    for i, case in enumerate(cases):
        case_num = case['case_number']
        cropped_path = case['cropped_image']
        is_valid = case['is_valid']
        
        print(f"\rProcessing {start_idx + i + 1}/1250: {case_num}", end='', flush=True)
        
        # Encode image
        image_base64 = await encode_image(cropped_path)
        if not image_base64:
            results.append({
                'case_number': case_num,
                'status': 'error',
                'error': 'Failed to encode image'
            })
            continue
        
        # Call Friendli VLM
        response = await call_friendli_vlm(session, image_base64, ASSUMPTION_BASED_PROMPT)
        
        if response:
            is_fp = 'YES' in response.upper()
            correct = (is_fp and not is_valid) or (not is_fp and is_valid)
            
            results.append({
                'case_number': case_num,
                'status': 'success',
                'response': response,
                'predicted_fp': is_fp,
                'actual_valid': is_valid,
                'correct': correct
            })
        else:
            results.append({
                'case_number': case_num,
                'status': 'error',
                'error': 'Failed to get response'
            })
    
    return results

async def complete_all_1250_cases():
    """Complete testing of all 1250 cases using Friendli AI"""
    print("\n" + "="*80)
    print("COMPLETING ALL 1250 CASES WITH FRIENDLI AI")
    print("="*80)
    
    # Load existing results to determine what needs testing
    print("\n1. Loading existing test data...")
    
    # Load the partial test results
    existing_tested = set()
    if os.path.exists('test_all_1250_partial_results.json'):
        with open('test_all_1250_partial_results.json', 'r') as f:
            partial_results = json.load(f)
            for result in partial_results['results']:
                if result['status'] == 'success':
                    existing_tested.add(result['case_number'])
    
    print(f"Already tested: {len(existing_tested)} cases")
    
    # Load full dataset
    with open('valo_batch_round3_complete.json', 'r') as f:
        full_data = json.load(f)
    
    all_cases = full_data['results']
    print(f"Total cases: {len(all_cases)}")
    
    # Identify cases to test
    cases_to_test = []
    for case in all_cases:
        if case['case_number'] not in existing_tested:
            cases_to_test.append(case)
    
    print(f"Remaining to test: {len(cases_to_test)} cases")
    
    if not cases_to_test:
        print("\nAll cases already tested!")
        return
    
    # Process in batches with Friendli AI
    print(f"\n2. Testing {len(cases_to_test)} cases with Friendli AI...")
    print("This will use the proven assumption-based approach")
    
    batch_size = 10
    all_results = []
    
    async with aiohttp.ClientSession() as session:
        # Test connection first
        print("\n3. Testing Friendli AI connection...")
        test_case = cases_to_test[0]
        test_image = await encode_image(test_case['cropped_image'])
        if test_image:
            test_response = await call_friendli_vlm(session, test_image, "Test connection. Reply with: OK")
            if test_response:
                print(f"✓ Friendli AI connected successfully: {test_response}")
            else:
                print("✗ Failed to connect to Friendli AI")
                return
        
        # Process all cases
        print(f"\n4. Processing {len(cases_to_test)} cases...")
        start_time = time.time()
        
        for i in range(0, len(cases_to_test), batch_size):
            batch = cases_to_test[i:i+batch_size]
            batch_results = await process_case_batch(session, batch, len(existing_tested) + i)
            all_results.extend(batch_results)
            
            # Save progress every 50 cases
            if (i + batch_size) % 50 == 0:
                save_progress(all_results, len(existing_tested))
    
    # Final analysis
    print(f"\n\n5. Final Analysis of Complete 1250 Dataset")
    print("-"*50)
    
    # Combine with existing results
    total_tested = len(existing_tested) + len(all_results)
    successful_new = sum(1 for r in all_results if r['status'] == 'success')
    
    print(f"Previously tested: {len(existing_tested)}")
    print(f"Newly tested: {len(all_results)}")
    print(f"Successful new tests: {successful_new}")
    print(f"Total tested: {total_tested}/1250")
    
    # Calculate performance
    correct_predictions = sum(1 for r in all_results if r.get('correct', False))
    fp_detected = sum(1 for r in all_results if r['status'] == 'success' and not r['actual_valid'] and r['predicted_fp'])
    total_fp = sum(1 for r in all_results if r['status'] == 'success' and not r['actual_valid'])
    valid_protected = sum(1 for r in all_results if r['status'] == 'success' and r['actual_valid'] and not r['predicted_fp'])
    total_valid = sum(1 for r in all_results if r['status'] == 'success' and r['actual_valid'])
    
    if successful_new > 0:
        accuracy = (correct_predictions / successful_new) * 100
        fp_rate = (fp_detected / total_fp) * 100 if total_fp > 0 else 0
        valid_rate = (valid_protected / total_valid) * 100 if total_valid > 0 else 0
        
        print(f"\nNew Results Performance:")
        print(f"  - Accuracy: {accuracy:.1f}%")
        print(f"  - FP Detection: {fp_rate:.1f}%")
        print(f"  - Valid Protection: {valid_rate:.1f}%")
    
    # Save complete results
    complete_results = {
        'test_date': datetime.now().isoformat(),
        'endpoint': 'Friendli AI',
        'total_cases': 1250,
        'tested_cases': total_tested,
        'previously_tested': len(existing_tested),
        'newly_tested': len(all_results),
        'successful_new': successful_new,
        'results': all_results,
        'performance': {
            'accuracy': accuracy if successful_new > 0 else 0,
            'fp_detection_rate': fp_rate if successful_new > 0 else 0,
            'valid_protection_rate': valid_rate if successful_new > 0 else 0
        }
    }
    
    with open('complete_1250_friendli_results.json', 'w') as f:
        json.dump(complete_results, f, indent=2)
    
    elapsed = time.time() - start_time
    print(f"\nProcessing completed in {elapsed:.1f} seconds")
    print(f"Average per case: {elapsed/len(all_results):.2f} seconds")
    print("\nResults saved to: complete_1250_friendli_results.json")

def save_progress(results, offset):
    """Save intermediate progress"""
    progress = {
        'timestamp': datetime.now().isoformat(),
        'cases_processed': len(results) + offset,
        'results': results
    }
    with open('friendli_progress.json', 'w') as f:
        json.dump(progress, f, indent=2)

if __name__ == "__main__":
    asyncio.run(complete_all_1250_cases())