#!/usr/bin/env python3
"""
Extract and analyze structure/equipment-only descriptions
These are critical for understanding false positives
"""

import json
import re
from datetime import datetime
from collections import Counter

class StructureDescriptionExtractor:
    def __init__(self):
        self.base_dir = "valo_comprehensive_data"
        self.fp_file = f"{self.base_dir}/false_positives/false_positive_analysis_20250725_232934.md"
        self.tp_file = f"{self.base_dir}/true_positives/true_positive_analysis_20250725_232934.md"
        
        # Structure categories
        self.structure_keywords = [
            'crane', 'vessel', 'ship', 'truck', 'pm', 'prime mover', 
            'spreader', 'container', 'equipment', 'machinery', 'structure',
            'quay crane', 'rtg', 'rmg', 'straddle carrier'
        ]
        
    def is_structure_only(self, case_data):
        """Determine if case is structure/equipment only"""
        # Check if main subject is equipment/structure
        main_subject = case_data.get('main_subject', '').lower()
        if any(keyword in main_subject for keyword in ['equipment', 'vehicle', 'structure']):
            if not case_data.get('person_present', False):
                return True
        
        # Check description for no person indicators
        desc_lower = case_data.get('description', '').lower()
        no_person_indicators = [
            'no person', 'no people', 'no individuals', 'no workers',
            'empty', 'unoccupied', 'unmanned', 'no one visible',
            'only equipment', 'only the crane', 'only the vessel'
        ]
        
        has_no_person = any(indicator in desc_lower for indicator in no_person_indicators)
        has_structure = any(keyword in desc_lower for keyword in self.structure_keywords)
        
        return has_no_person and has_structure
    
    def extract_case_data(self, file_path, is_fp=True):
        """Extract case data with structure detection"""
        cases = []
        
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        case_sections = content.split('## Case:')[1:]
        
        for section in case_sections:
            lines = section.strip().split('\n')
            if not lines:
                continue
                
            case_data = {
                'case_number': lines[0].strip(),
                'is_false_positive': is_fp,
                'description': '',
                'person_present': False,
                'main_subject': 'UNKNOWN',
                'false_positive_likelihood': 0,
                'is_structure_only': False
            }
            
            # Extract description
            desc_start = False
            desc_lines = []
            for i, line in enumerate(lines):
                if line.strip() == '```' and not desc_start:
                    desc_start = True
                    continue
                elif line.strip() == '```' and desc_start:
                    break
                elif desc_start:
                    desc_lines.append(line)
            
            case_data['description'] = '\n'.join(desc_lines)
            
            # Extract metrics
            for line in lines:
                if '**Person Present**:' in line:
                    case_data['person_present'] = 'YES' in line
                elif '**Main Subject**:' in line:
                    case_data['main_subject'] = line.split(':', 1)[1].strip()
                elif '**False Positive Likelihood**:' in line:
                    try:
                        case_data['false_positive_likelihood'] = int(re.search(r'(\d+)%', line).group(1))
                    except:
                        pass
            
            # Determine if structure only
            case_data['is_structure_only'] = self.is_structure_only(case_data)
            
            cases.append(case_data)
        
        return cases
    
    def analyze_structures(self):
        """Analyze all structure-only cases"""
        print("="*80)
        print("📐 STRUCTURE/EQUIPMENT DESCRIPTION ANALYSIS")
        print("="*80)
        
        # Extract all cases
        fp_cases = self.extract_case_data(self.fp_file, is_fp=True)
        tp_cases = self.extract_case_data(self.tp_file, is_fp=False)
        
        # Filter structure-only cases
        fp_structures = [c for c in fp_cases if c['is_structure_only']]
        tp_structures = [c for c in tp_cases if c['is_structure_only']]
        
        print(f"\n📊 Structure-Only Cases Found:")
        print(f"  FALSE POSITIVES: {len(fp_structures)} out of {len(fp_cases)} ({len(fp_structures)/len(fp_cases)*100:.1f}%)")
        print(f"  TRUE POSITIVES: {len(tp_structures)} out of {len(tp_cases)} ({len(tp_structures)/len(tp_cases)*100:.1f}%)")
        
        # Create structure analysis file
        structure_file = f"{self.base_dir}/structures/structure_only_analysis.md"
        
        with open(structure_file, 'w') as f:
            f.write("# Structure/Equipment-Only Cases Analysis\n")
            f.write(f"Generated: {datetime.now().isoformat()}\n\n")
            
            f.write("## Summary\n\n")
            f.write(f"- **Total structure-only cases**: {len(fp_structures) + len(tp_structures)}\n")
            f.write(f"- **In FALSE POSITIVES**: {len(fp_structures)} ({len(fp_structures)/len(fp_cases)*100:.1f}%)\n")
            f.write(f"- **In TRUE POSITIVES**: {len(tp_structures)} ({len(tp_structures)/len(tp_cases)*100:.1f}%)\n\n")
            
            if len(fp_structures) > len(tp_structures) * 5:
                f.write("✅ **STRONG PATTERN CONFIRMED**: Structure-only images are predominantly FALSE POSITIVES\n\n")
            
            # Analyze structure types
            f.write("## Structure Types in FALSE POSITIVES\n\n")
            
            structure_types = Counter()
            for case in fp_structures:
                desc = case['description'].lower()
                for struct_type in self.structure_keywords:
                    if struct_type in desc:
                        structure_types[struct_type] += 1
            
            for struct_type, count in structure_types.most_common():
                f.write(f"- {struct_type}: {count} occurrences\n")
            
            # Sample descriptions
            f.write("\n## Sample Structure-Only FALSE POSITIVE Descriptions\n\n")
            
            for i, case in enumerate(fp_structures[:10]):
                f.write(f"### Case {i+1}: {case['case_number']}\n")
                f.write(f"**Main Subject**: {case['main_subject']}\n")
                f.write(f"**FP Likelihood**: {case['false_positive_likelihood']}%\n")
                f.write(f"**Description excerpt**:\n")
                f.write("```\n")
                f.write(case['description'][:500] + "..." if len(case['description']) > 500 else case['description'])
                f.write("\n```\n\n")
            
            # Key patterns in structure descriptions
            f.write("## Key Patterns in Structure Descriptions\n\n")
            
            common_phrases = [
                'no person visible', 'no people present', 'empty area',
                'equipment only', 'crane operating', 'vessel docked',
                'container yard', 'industrial setting', 'machinery visible'
            ]
            
            phrase_counts = {phrase: 0 for phrase in common_phrases}
            
            for case in fp_structures:
                desc_lower = case['description'].lower()
                for phrase in common_phrases:
                    if phrase in desc_lower:
                        phrase_counts[phrase] += 1
            
            f.write("Common phrases in structure-only FALSE POSITIVES:\n")
            for phrase, count in sorted(phrase_counts.items(), key=lambda x: x[1], reverse=True):
                if count > 0:
                    percentage = count / len(fp_structures) * 100
                    f.write(f"- '{phrase}': {count} times ({percentage:.1f}%)\n")
            
            # Rule recommendation
            f.write("\n## 🎯 Recommended Rule for Structure Detection\n\n")
            f.write("```python\n")
            f.write("def is_structure_only_false_positive(description):\n")
            f.write("    desc_lower = description.lower()\n")
            f.write("    \n")
            f.write("    # Strong indicators of structure-only\n")
            f.write("    no_person_phrases = [\n")
            f.write("        'no person', 'no people', 'no individuals',\n")
            f.write("        'empty', 'unoccupied', 'unmanned'\n")
            f.write("    ]\n")
            f.write("    \n")
            f.write("    structure_keywords = [\n")
            f.write("        'crane', 'vessel', 'equipment', 'machinery',\n")
            f.write("        'container', 'truck', 'spreader'\n")
            f.write("    ]\n")
            f.write("    \n")
            f.write("    has_no_person = any(phrase in desc_lower for phrase in no_person_phrases)\n")
            f.write("    has_structure = any(keyword in desc_lower for keyword in structure_keywords)\n")
            f.write("    \n")
            f.write("    if has_no_person and has_structure:\n")
            f.write("        return True, 0.95  # 95% confidence FALSE POSITIVE\n")
            f.write("    \n")
            f.write("    return False, 0.0\n")
            f.write("```\n")
        
        print(f"\n✅ Structure analysis complete!")
        print(f"📄 Report saved to: {structure_file}")
        
        # Save structure cases data
        structure_data = {
            'fp_structure_cases': len(fp_structures),
            'tp_structure_cases': len(tp_structures),
            'fp_structure_rate': len(fp_structures)/len(fp_cases)*100,
            'tp_structure_rate': len(tp_structures)/len(tp_cases)*100,
            'structure_types': dict(structure_types),
            'sample_cases': [
                {
                    'case_number': c['case_number'],
                    'main_subject': c['main_subject'],
                    'fp_likelihood': c['false_positive_likelihood'],
                    'description_preview': c['description'][:200]
                } for c in fp_structures[:20]
            ]
        }
        
        with open(f"{self.base_dir}/structures/structure_analysis_data.json", 'w') as f:
            json.dump(structure_data, f, indent=2)
        
        print(f"📊 Data saved to: {self.base_dir}/structures/structure_analysis_data.json")
        
        return fp_structures, tp_structures

if __name__ == "__main__":
    extractor = StructureDescriptionExtractor()
    fp_structures, tp_structures = extractor.analyze_structures()