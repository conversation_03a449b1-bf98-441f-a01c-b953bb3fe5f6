#!/usr/bin/env python3
"""
Analyze the VALO uniform image to understand PPE requirements
"""

import base64
import requests
import json

# VLM endpoint
vlm_url = "http://100.106.127.35:9500/v1/chat/completions"
vlm_model = "VLM-38B-AWQ"

# Encode the image
image_path = 'ai_farm_images_fixed_250703/as.png'
with open(image_path, 'rb') as f:
    image_b64 = base64.b64encode(f.read()).decode('utf-8')

# Create comprehensive prompt
prompt = """Analyze this safety uniform reference image in detail. This appears to be a guide for recognizing different worker types and their required PPE.

Please describe:

1. TITLE AND PURPOSE
   - What is the title of this image?
   - What is its apparent purpose?

2. UNIFORM CATEGORIES
   For each person shown (left to right):
   - Position number (1-5)
   - Uniform color and type
   - PPE worn (helmet color, vest type, additional equipment)
   - Any text labels identifying their role
   - Special features (life jacket, harness, etc.)

3. PPE COMPONENTS VISIBLE
   - Head protection types and colors
   - High-visibility clothing variations
   - Additional safety equipment
   - Any specialized gear

4. RECOGNITION GUIDELINES
   - What text appears about recognition?
   - What roles need to be identified?
   - Any special instructions?

5. SAFETY STANDARDS IMPLIED
   - What constitutes "full PPE" based on these examples?
   - Are there role-specific PPE requirements?
   - What variations are acceptable?

Provide a detailed analysis that would help understand what proper PPE looks like for different worker categories."""

# Create payload
payload = {
    "model": vlm_model,
    "messages": [{
        "role": "user",
        "content": [
            {"type": "text", "text": prompt},
            {"type": "image_url", "image_url": {"url": f"data:image/png;base64,{image_b64}"}}
        ]
    }],
    "temperature": 0.1,
    "max_tokens": 2000
}

# Send request
print("Analyzing VALO uniform image...")
response = requests.post(vlm_url, json=payload, timeout=60)

if response.status_code == 200:
    result = response.json()
    content = result['choices'][0]['message']['content']
    
    print("\n" + "="*80)
    print("VALO UNIFORM IMAGE ANALYSIS")
    print("="*80)
    print(content)
    
    # Save to file
    with open('valo_uniform_analysis.txt', 'w') as f:
        f.write(content)
    
    print("\n" + "="*80)
    print("Analysis saved to: valo_uniform_analysis.txt")
    
else:
    print(f"Error: {response.status_code}")
    print(response.text)