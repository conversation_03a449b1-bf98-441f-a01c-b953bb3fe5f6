#!/usr/bin/env python3
"""
Enhanced VALO Processor - Robust Version with Better Error Handling
Handles timeouts and retries for more stable processing
"""

import json
import base64
import requests
import pandas as pd
import os
import time
from datetime import datetime
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
import threading
from flask import Flask, jsonify, render_template_string
from flask_cors import CORS
import logging
from requests.adapters import HTTPAdapter
from requests.packages.urllib3.util.retry import Retry

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class RobustEnhancedProcessor:
    def __init__(self):
        self.vlm_url = "http://100.106.127.35:9500/v1/chat/completions"
        self.vlm_model = "VLM-38B-AWQ"
        
        # Create session with retry strategy
        self.session = requests.Session()
        retry_strategy = Retry(
            total=3,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504]
        )
        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)
        
        # Load CSV data
        self.csv_path = 'ai_farm_images_fixed_250703/psa_valo_violation_report_without_images_csv_2025-07-03_14-14-01.CSV'
        self.df = pd.read_csv(self.csv_path)
        
        # Create case lookup
        self.case_lookup = {}
        for idx, row in self.df.iterrows():
            case_num = row['Case Int. ID']
            self.case_lookup[case_num] = {
                'infringement_type': row['Type of Infringement'],
                'alert_status': row['Alert Status'],
                'remarks': row['Remarks']
            }
        
        # Processing state
        self.results = []
        self.failed_cases = []
        self.progress = {
            'total': 0,
            'processed': 0,
            'fp_detected': 0,
            'tp_protected': 0,
            'errors': 0,
            'timeouts': 0,
            'start_time': None,
            'current_case': None,
            'fp_rate': 0,
            'protection_rate': 0,
            'error_rate': 0
        }
        
        # Flask app
        self.app = Flask(__name__)
        CORS(self.app)
        self.setup_routes()
        
    def setup_routes(self):
        """Setup Flask routes"""
        @self.app.route('/')
        def dashboard():
            return render_template_string(DASHBOARD_HTML)
        
        @self.app.route('/api/progress')
        def get_progress():
            return jsonify(self.progress)
        
        @self.app.route('/api/results')
        def get_results():
            return jsonify({
                'results': self.results[-10:],
                'failed_cases': self.failed_cases[-5:],
                'summary': self.get_summary()
            })
    
    def get_summary(self):
        """Calculate summary statistics"""
        if not self.results:
            return {}
        
        fp_cases = [r for r in self.results if r['ground_truth'] == 'Invalid']
        tp_cases = [r for r in self.results if r['ground_truth'] == 'Valid']
        
        fp_detected = sum(1 for r in fp_cases if r.get('fp_likelihood', 0) >= 70)
        tp_protected = sum(1 for r in tp_cases if r.get('fp_likelihood', 100) < 50)
        
        return {
            'total_processed': len(self.results),
            'fp_detection_rate': (fp_detected / len(fp_cases) * 100) if fp_cases else 0,
            'tp_protection_rate': (tp_protected / len(tp_cases) * 100) if tp_cases else 0,
            'error_rate': (self.progress['errors'] / self.progress['total'] * 100) if self.progress['total'] > 0 else 0,
            'structure_detections': sum(1 for r in self.results if 'Structure' in r.get('main_subject', '')),
            'person_with_ppe_fps': sum(1 for r in fp_cases if r.get('ppe_compliance') == 'COMPLETE')
        }
    
    def encode_image(self, image_path):
        """Encode image to base64"""
        try:
            if os.path.exists(image_path):
                with open(image_path, 'rb') as f:
                    return base64.b64encode(f.read()).decode('utf-8')
        except Exception as e:
            logger.error(f"Error encoding {image_path}: {str(e)}")
        return None
    
    def create_enhanced_prompt(self, infringement_type):
        """Create the enhanced prompt"""
        return f"""Analyze this safety alert image in extreme detail. The reported violation type is: {infringement_type}

1. MAIN SUBJECT IDENTIFICATION
   - Primary subject type: Person/Vessel Structure/Crane Structure/Spreader Structure/PM Structure/Others/Equipment
   - If Person: Number of individuals, gender if visible, apparent role
   - If Vessel Structure: Ship deck, cargo holds, vessel railings, bulkheads, hatch covers
   - If Crane Structure: Ship-to-shore cranes, gantry cranes, mobile cranes, crane booms, jib arms, crane cabins
   - If Spreader Structure: Container spreaders, twist locks, spreader bars, lifting attachments, hydraulic components
   - If PM Structure: Prime mover equipment, terminal tractors, yard trucks, chassis handling equipment
   - If Others/Equipment: Winches, cable drums, wire ropes, slings, sacks/bags, bulk handling equipment, machinery, vehicles, tools

2. PERSON DETAILS (if any visible)
   - Exact position and posture
   - Clothing description (colors, type, condition)
   - Safety equipment worn:
     * Head protection (helmet/hard hat) - color, style, properly worn?
     * High-visibility clothing - color, reflective strips visible?

3. ACTIVITY ANALYSIS
   - What specific action is being performed?
   - Tools or equipment being used
   - Body mechanics and positioning

4. ENVIRONMENT AND CONTEXT
   - Exact location (vessel deck/quay/yard/height/confined space)
   - Surrounding equipment and structures
   - Weather/lighting conditions
   - Potential hazards in vicinity

5. SAFETY ASSESSMENT
   - Primary safety concern visible
   - Violation type if apparent
   - Severity estimation

Provide the most detailed, factual description possible.

---

After completing the above detailed description, now analyze the same image for accuracy and confidence assessment:

6. DESCRIPTION ACCURACY (0-100%):
   How accurately does the above description match what you see in the image?

7. SUBJECT CONFIRMATION:
   - Is there definitely a PERSON in this image? YES/NO
   - If NO, what is the main subject?
   - Confidence in subject identification: (0-100%)

8. PPE COMPLIANCE CHECK (if person present):
   - Helmet/Hard hat present? YES/NO/PARTIAL/NA
   - Helmet confidence: (0-100%)
   - High-visibility vest present? YES/NO/PARTIAL/NA
   - Vest confidence: (0-100%)
   - Overall PPE compliance: COMPLETE/INCOMPLETE/NONE/NA
   - Overall PPE confidence: (0-100%)

9. SAFETY VIOLATION ASSESSMENT:
   - Is there a genuine safety violation visible? YES/NO/UNCERTAIN
   - If YES, describe the specific violation
   - Violation severity: MINOR/MODERATE/SEVERE/NA
   - Violation confidence: (0-100%)

10. FALSE POSITIVE DETECTION:
    Apply these core rules for "{infringement_type}" violations:
    - If NO person detected → likely False Positive (60-80%)
    - If ANY structure/others/equipment detected → likely False Positive (60-80%)
    - If NO person detected + ANY structure/others/equipment detected → high False Positive (80-95%)
    - If person detected with helmet/hardhat + vest BUT flagged for "PPE Non-compliance" → False Positive (85-95%)
    - List any additional factors suggesting false positive
    - Final false positive likelihood: (0-100%)

Format your final assessment EXACTLY as:

DESCRIPTION_ACCURACY: [X]%
PERSON_PRESENT: [YES/NO]
MAIN_SUBJECT: [Person/Vessel Structure/Crane Structure/Spreader Structure/PM Structure/Others/Equipment]
SUBJECT_CONFIDENCE: [X]%
HELMET_STATUS: [YES/NO/PARTIAL/NA]
HELMET_CONFIDENCE: [X]%
VEST_STATUS: [YES/NO/PARTIAL/NA]
VEST_CONFIDENCE: [X]%
PPE_COMPLIANCE: [COMPLETE/INCOMPLETE/NONE/NA]
PPE_CONFIDENCE: [X]%
SAFETY_VIOLATION: [YES/NO/UNCERTAIN]
VIOLATION_DESCRIPTION: [Description or NONE]
VIOLATION_SEVERITY: [MINOR/MODERATE/SEVERE/NA]
VIOLATION_CONFIDENCE: [X]%
FALSE_POSITIVE_LIKELIHOOD: [X]%
FP_INDICATORS: [List or NONE]"""
    
    def parse_vlm_response(self, response):
        """Parse the structured VLM response"""
        import re
        
        parsed = {
            'person_present': False,
            'main_subject': 'Unknown',
            'subject_confidence': 0,
            'helmet_status': 'NA',
            'vest_status': 'NA',
            'ppe_compliance': 'NA',
            'safety_violation': 'UNCERTAIN',
            'fp_likelihood': 50,
            'fp_indicators': []
        }
        
        patterns = {
            'person_present': r'PERSON_PRESENT:\s*(YES|NO)',
            'main_subject': r'MAIN_SUBJECT:\s*([^\n]+)',
            'subject_confidence': r'SUBJECT_CONFIDENCE:\s*(\d+)%',
            'helmet_status': r'HELMET_STATUS:\s*(\w+)',
            'vest_status': r'VEST_STATUS:\s*(\w+)',
            'ppe_compliance': r'PPE_COMPLIANCE:\s*(\w+)',
            'safety_violation': r'SAFETY_VIOLATION:\s*(YES|NO|UNCERTAIN)',
            'fp_likelihood': r'FALSE_POSITIVE_LIKELIHOOD:\s*(\d+)%',
            'fp_indicators': r'FP_INDICATORS:\s*([^\n]+)'
        }
        
        for field, pattern in patterns.items():
            match = re.search(pattern, response, re.IGNORECASE)
            if match:
                value = match.group(1).strip()
                if field in ['subject_confidence', 'fp_likelihood']:
                    parsed[field] = int(value)
                elif field == 'person_present':
                    parsed[field] = value.upper() == 'YES'
                elif field == 'fp_indicators':
                    parsed[field] = [i.strip() for i in value.split(',') if i.strip() and i.strip() != 'NONE']
                else:
                    parsed[field] = value
        
        return parsed
    
    def process_case_with_retry(self, case_data, max_retries=3):
        """Process a single case with retry logic"""
        case_number = case_data['case_number']
        
        for attempt in range(max_retries):
            try:
                # Get info from CSV
                csv_info = self.case_lookup.get(case_number, {})
                infringement_type = csv_info.get('infringement_type', 'Unknown')
                ground_truth = csv_info.get('alert_status', 'Unknown')
                
                # Update progress
                self.progress['current_case'] = case_number
                
                # Encode images
                cropped_b64 = self.encode_image(case_data['cropped_image'])
                source_b64 = self.encode_image(case_data['source_image'])
                
                if not cropped_b64:
                    logger.error(f"Failed to encode images for {case_number}")
                    self.progress['errors'] += 1
                    return None
                
                # Create prompt
                prompt = self.create_enhanced_prompt(infringement_type)
                
                # Build message
                content = [
                    {"type": "text", "text": prompt},
                    {"type": "text", "text": "\nANALYZING CROPPED ALERT IMAGE:"},
                    {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{cropped_b64}"}}
                ]
                
                if source_b64:
                    content.extend([
                        {"type": "text", "text": "\nFULL CONTEXT IMAGE FOR REFERENCE:"},
                        {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{source_b64}"}}
                    ])
                
                payload = {
                    "model": self.vlm_model,
                    "messages": [{"role": "user", "content": content}],
                    "temperature": 0.1,
                    "max_tokens": 600
                }
                
                # Send request with timeout
                response = self.session.post(
                    self.vlm_url, 
                    json=payload, 
                    timeout=(10, 90)  # (connection timeout, read timeout)
                )
                
                if response.status_code == 200:
                    vlm_response = response.json()['choices'][0]['message']['content']
                    parsed = self.parse_vlm_response(vlm_response)
                    
                    result = {
                        'case_number': case_number,
                        'infringement_type': infringement_type,
                        'ground_truth': ground_truth,
                        'remarks': csv_info.get('remarks', ''),
                        **parsed,
                        'attempts': attempt + 1,
                        'timestamp': datetime.now().isoformat()
                    }
                    
                    # Update progress
                    self.progress['processed'] += 1
                    
                    if ground_truth == 'Invalid' and parsed['fp_likelihood'] >= 70:
                        self.progress['fp_detected'] += 1
                    elif ground_truth == 'Valid' and parsed['fp_likelihood'] < 50:
                        self.progress['tp_protected'] += 1
                    
                    # Update rates
                    self.update_progress_rates()
                    
                    self.results.append(result)
                    return result
                    
                else:
                    logger.warning(f"HTTP {response.status_code} for {case_number}, attempt {attempt + 1}")
                    if attempt < max_retries - 1:
                        time.sleep(2 ** attempt)  # Exponential backoff
                        
            except requests.exceptions.Timeout:
                logger.warning(f"Timeout for {case_number}, attempt {attempt + 1}")
                self.progress['timeouts'] += 1
                if attempt < max_retries - 1:
                    time.sleep(5)  # Wait before retry
                    
            except Exception as e:
                logger.error(f"Error processing {case_number}, attempt {attempt + 1}: {str(e)}")
                if attempt < max_retries - 1:
                    time.sleep(2)
        
        # All retries failed
        self.progress['errors'] += 1
        self.failed_cases.append({
            'case_number': case_number,
            'error': 'Max retries exceeded',
            'timestamp': datetime.now().isoformat()
        })
        return None
    
    def update_progress_rates(self):
        """Update progress rates"""
        if self.progress['processed'] > 0:
            fp_total = sum(1 for r in self.results if r['ground_truth'] == 'Invalid')
            if fp_total > 0:
                self.progress['fp_rate'] = (self.progress['fp_detected'] / fp_total) * 100
            
            tp_total = sum(1 for r in self.results if r['ground_truth'] == 'Valid')
            if tp_total > 0:
                self.progress['protection_rate'] = (self.progress['tp_protected'] / tp_total) * 100
        
        if self.progress['total'] > 0:
            self.progress['error_rate'] = (self.progress['errors'] / self.progress['total']) * 100
    
    def run_dashboard(self):
        """Run the Flask dashboard"""
        def run_app():
            self.app.run(host='0.0.0.0', port=5001, debug=False, use_reloader=False)
        
        dashboard_thread = threading.Thread(target=run_app, daemon=True)
        dashboard_thread.start()
        logger.info("Dashboard running at http://localhost:5001")
    
    def process_all_cases(self, limit=None):
        """Process all cases with better error handling"""
        # Load cases
        with open('valo_batch_round3_complete.json', 'r') as f:
            data = json.load(f)
            cases = data['results']
        
        if limit:
            cases = cases[:limit]
        
        self.progress['total'] = len(cases)
        self.progress['start_time'] = time.time()
        
        logger.info(f"Starting processing of {len(cases)} cases...")
        
        # Sequential processing for better stability
        for i, case in enumerate(cases):
            self.process_case_with_retry(case)
            
            # Progress logging
            if (i + 1) % 10 == 0:
                elapsed = time.time() - self.progress['start_time']
                rate = self.progress['processed'] / elapsed if elapsed > 0 else 0
                eta = (self.progress['total'] - self.progress['processed']) / rate / 60 if rate > 0 else 0
                
                logger.info(f"Progress: {self.progress['processed']}/{self.progress['total']} "
                          f"({self.progress['processed']/self.progress['total']*100:.1f}%) "
                          f"FP: {self.progress['fp_rate']:.1f}% "
                          f"Protection: {self.progress['protection_rate']:.1f}% "
                          f"Errors: {self.progress['errors']} "
                          f"ETA: {eta:.1f} min")
            
            # Rate limiting
            time.sleep(1)  # Increased delay for stability
        
        # Save results
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_file = f"robust_enhanced_results_{timestamp}.json"
        
        with open(output_file, 'w') as f:
            json.dump({
                'results': self.results,
                'failed_cases': self.failed_cases,
                'progress': self.progress,
                'summary': self.get_summary()
            }, f, indent=2)
        
        logger.info(f"Processing complete! Results saved to {output_file}")
        logger.info(f"Final FP Detection Rate: {self.progress['fp_rate']:.1f}%")
        logger.info(f"Final Protection Rate: {self.progress['protection_rate']:.1f}%")
        logger.info(f"Total Errors: {self.progress['errors']} ({self.progress['error_rate']:.1f}%)")

# Enhanced Dashboard HTML
DASHBOARD_HTML = '''
<!DOCTYPE html>
<html>
<head>
    <title>VALO Processing Dashboard - Robust Version</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #1a1a1a; color: #fff; }
        .container { max-width: 1400px; margin: 0 auto; }
        .metrics { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 30px; }
        .metric { background: #2a2a2a; padding: 15px; border-radius: 8px; border: 1px solid #444; }
        .metric h3 { margin: 0 0 5px 0; color: #4CAF50; font-size: 14px; }
        .metric .value { font-size: 28px; font-weight: bold; }
        .metric .label { color: #888; font-size: 12px; }
        .progress-bar { background: #333; height: 30px; border-radius: 15px; overflow: hidden; margin: 20px 0; }
        .progress-fill { background: linear-gradient(90deg, #4CAF50, #45a049); height: 100%; transition: width 0.5s; display: flex; align-items: center; justify-content: center; color: #fff; font-weight: bold; }
        .results { background: #2a2a2a; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .result-item { padding: 8px; border-bottom: 1px solid #444; font-size: 13px; }
        .result-item:last-child { border: none; }
        .fp { color: #ff6b6b; }
        .tp { color: #4CAF50; }
        .structure { color: #ffa500; }
        .error { color: #ff4444; }
        h1, h2 { color: #fff; }
        .current-case { background: #333; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .errors { background: #3a2222; }
        .errors h3 { color: #ff6b6b; }
    </style>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <div class="container">
        <h1>🚀 VALO Real-time Processing Dashboard (Robust Version)</h1>
        
        <div class="metrics">
            <div class="metric">
                <h3>Progress</h3>
                <div class="value"><span id="processed">0</span>/<span id="total">0</span></div>
                <div class="label">Cases Processed</div>
            </div>
            
            <div class="metric">
                <h3>FP Detection</h3>
                <div class="value"><span id="fp-rate">0</span>%</div>
                <div class="label">False Positives</div>
            </div>
            
            <div class="metric">
                <h3>Protection</h3>
                <div class="value"><span id="protection-rate">0</span>%</div>
                <div class="label">Valid Protected</div>
            </div>
            
            <div class="metric">
                <h3>Speed</h3>
                <div class="value"><span id="speed">0</span></div>
                <div class="label">Cases/hour</div>
            </div>
            
            <div class="metric errors">
                <h3>Errors</h3>
                <div class="value"><span id="errors">0</span></div>
                <div class="label">Failed Cases</div>
            </div>
            
            <div class="metric">
                <h3>ETA</h3>
                <div class="value"><span id="eta">-</span></div>
                <div class="label">Minutes Left</div>
            </div>
        </div>
        
        <div class="progress-bar">
            <div class="progress-fill" id="progress-fill" style="width: 0%">0%</div>
        </div>
        
        <div class="current-case">
            <strong>Current Case:</strong> <span id="current-case">-</span> | 
            <strong>Timeouts:</strong> <span id="timeouts">0</span>
        </div>
        
        <h2>Recent Results</h2>
        <div class="results" id="results">
            <div class="result-item">Waiting for results...</div>
        </div>
        
        <h2>Failed Cases</h2>
        <div class="results" id="failed-cases">
            <div class="result-item">No failures yet...</div>
        </div>
    </div>
    
    <script>
        function updateDashboard() {
            $.get('/api/progress', function(data) {
                $('#processed').text(data.processed);
                $('#total').text(data.total);
                $('#fp-rate').text(data.fp_rate.toFixed(1));
                $('#protection-rate').text(data.protection_rate.toFixed(1));
                $('#current-case').text(data.current_case || '-');
                $('#errors').text(data.errors);
                $('#timeouts').text(data.timeouts);
                
                // Calculate progress
                if (data.total > 0) {
                    const progress = (data.processed / data.total * 100).toFixed(1);
                    $('#progress-fill').css('width', progress + '%').text(progress + '%');
                    
                    // Calculate speed and ETA
                    if (data.start_time && data.processed > 0) {
                        const elapsed = (Date.now() / 1000) - data.start_time;
                        const speed = (data.processed / elapsed * 3600).toFixed(0);
                        $('#speed').text(speed);
                        
                        const remaining = data.total - data.processed;
                        const eta = (remaining / (data.processed / elapsed) / 60).toFixed(0);
                        $('#eta').text(eta);
                    }
                }
            });
            
            $.get('/api/results', function(data) {
                if (data.results && data.results.length > 0) {
                    let html = '';
                    data.results.reverse().forEach(function(result) {
                        const fpClass = result.ground_truth === 'Invalid' ? 'fp' : 'tp';
                        const subjectClass = result.main_subject && result.main_subject.includes('Structure') ? 'structure' : '';
                        html += '<div class="result-item">';
                        html += '<strong>' + result.case_number + '</strong> - ';
                        html += '<span class="' + fpClass + '">' + result.ground_truth + '</span> - ';
                        html += '<span class="' + subjectClass + '">' + (result.main_subject || 'Unknown') + '</span> - ';
                        html += 'FP: ' + (result.fp_likelihood || '?') + '% - ';
                        html += result.infringement_type + ' - ';
                        html += 'Attempts: ' + (result.attempts || 1);
                        html += '</div>';
                    });
                    $('#results').html(html);
                }
                
                if (data.failed_cases && data.failed_cases.length > 0) {
                    let html = '';
                    data.failed_cases.forEach(function(failed) {
                        html += '<div class="result-item error">';
                        html += '<strong>' + failed.case_number + '</strong> - ';
                        html += failed.error;
                        html += '</div>';
                    });
                    $('#failed-cases').html(html);
                }
            });
        }
        
        // Update every 2 seconds
        setInterval(updateDashboard, 2000);
        updateDashboard();
    </script>
</body>
</html>
'''

if __name__ == "__main__":
    import sys
    
    processor = RobustEnhancedProcessor()
    
    # Start dashboard
    processor.run_dashboard()
    time.sleep(2)
    
    print("="*80)
    print("🚀 ROBUST ENHANCED VALO PROCESSOR")
    print("="*80)
    print("Dashboard URL: http://localhost:5001")
    print("Features:")
    print("  ✓ Automatic retry on timeout (3 attempts)")
    print("  ✓ Exponential backoff")
    print("  ✓ Better error tracking")
    print("  ✓ Sequential processing for stability")
    print("="*80)
    
    if len(sys.argv) > 1 and sys.argv[1] == 'test':
        print("Running test with 20 cases...")
        processor.process_all_cases(limit=20)
    else:
        print("\nUsage:")
        print("  Test mode (20 cases): python3 enhanced_processor_robust.py test")
        print("  Full mode (all cases): python3 enhanced_processor_robust.py full")
        
        if len(sys.argv) > 1 and sys.argv[1] == 'full':
            print("\nStarting full processing in 5 seconds...")
            time.sleep(5)
            processor.process_all_cases()