#!/usr/bin/env python3
"""
Launch exploration of remaining rounds to find the best approach
Each round will be tested independently against the 92.6% baseline
"""

import json
import asyncio
import subprocess
import os
import time
from datetime import datetime
import logging

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(message)s',
    handlers=[
        logging.FileHandler('exploration_rounds.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ExplorationOrchestrator:
    def __init__(self):
        self.baseline_fp = 92.6  # Round 6 achievement
        self.results = {}
        
        # Priority rounds to explore
        self.priority_rounds = [11, 7, 12, 15, 8, 13, 9, 10, 14]
        
    async def create_round11_ensemble(self):
        """Create Round 11: Ensemble Multi-Model Voting"""
        logger.info("Creating Round 11: Ensemble Multi-Model script")
        
        script = '''#!/usr/bin/env python3
"""
Round 11: Ensemble Multi-Model Voting
Test if multiple models voting can improve on 92.6%
"""
import json
import asyncio
import aiohttp
import base64
import logging
import os
from datetime import datetime

logging.basicConfig(level=logging.INFO, handlers=[
    logging.FileHandler('round11_ensemble.log'),
    logging.StreamHandler()
])
logger = logging.getLogger(__name__)

async def main():
    logger.info("="*80)
    logger.info("ROUND 11: ENSEMBLE MULTI-MODEL VOTING")
    logger.info("Baseline: 92.6% | Target: 94-95%")
    logger.info("="*80)
    
    # Load all cases
    with open('valo_batch_round3_complete.json', 'r') as f:
        all_cases = json.load(f)['results']
    
    # Load Round 6 results to learn from
    with open('valo_round6_ppe_complete.json', 'r') as f:
        round6_results = {r['case_number']: r for r in json.load(f)['results']}
    
    vlm_endpoint = "http://**************:9500/v1/chat/completions"
    
    # Three specialized models
    MODEL_CONFIGS = {
        "safety_expert": {
            "focus": "Safety violations and PPE compliance",
            "prompt": """SAFETY EXPERT MODEL - Round 11
Focus: PPE compliance and safety violations

Alert: {alert_status}
Description: {remarks}

Your expertise: Distinguishing compliant workers from violators
- Workers in FULL/PROPER PPE = Compliant = DISMISS
- Actual safety violations = FLAG
- Equipment only = DISMISS

Decision: DISMISS or FLAG?"""
        },
        
        "vision_expert": {
            "focus": "Visual analysis and human detection",
            "prompt": """VISION EXPERT MODEL - Round 11
Focus: Detecting humans vs structures/equipment

Alert: {alert_status}
Description: {remarks}

Your expertise: Visual identification
- No human visible = DISMISS
- Human clearly visible = Evaluate safety
- Structure/equipment only = DISMISS
- Uncertain = FLAG (safety first)

Decision: DISMISS or FLAG?"""
        },
        
        "context_expert": {
            "focus": "Context and pattern analysis",
            "prompt": """CONTEXT EXPERT MODEL - Round 11
Focus: Understanding context and patterns

Alert: {alert_status}
Description: {remarks}

Your expertise: Context understanding
- "STRUCTURE CAPTURED" patterns = DISMISS
- Compliant behavior descriptions = DISMISS
- Violation keywords = FLAG
- Valid alerts = Always FLAG

Decision: DISMISS or FLAG?"""
        }
    }
    
    async def process_with_model(session, case, model_name, config):
        try:
            image_path = case.get('cropped_image', '')
            if not os.path.exists(image_path):
                return None
            
            with open(image_path, 'rb') as f:
                image_data = base64.b64encode(f.read()).decode()
            
            prompt = config['prompt'].format(
                alert_status=case.get('alert_status', 'Invalid'),
                remarks=case.get('remarks', '')
            )
            
            payload = {
                "model": "VLM-38B-AWQ",
                "messages": [{
                    "role": "user",
                    "content": [
                        {"type": "text", "text": prompt},
                        {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{image_data}"}}
                    ]
                }],
                "max_tokens": 100,
                "temperature": 0.1
            }
            
            async with session.post(vlm_endpoint, json=payload) as response:
                if response.status == 200:
                    result = await response.json()
                    vlm_response = result['choices'][0]['message']['content']
                    decision = 'dismissed' if 'dismiss' in vlm_response.lower() else 'flagged'
                    return {
                        'model': model_name,
                        'decision': decision,
                        'response': vlm_response
                    }
        except Exception as e:
            logger.error(f"Error {model_name}: {str(e)}")
            return None
    
    # Process cases with ensemble
    results = []
    chunk_size = 10  # Smaller for 3 parallel models
    
    connector = aiohttp.TCPConnector(limit=30)
    timeout = aiohttp.ClientTimeout(total=90)
    
    async with aiohttp.ClientSession(connector=connector, timeout=timeout) as session:
        for i in range(0, min(500, len(all_cases)), chunk_size):  # Test on 500 cases
            chunk = all_cases[i:i+chunk_size]
            
            for case in chunk:
                case_num = case['case_number']
                
                # Check Round 6 performance
                round6_correct = True
                if case_num in round6_results:
                    r6 = round6_results[case_num]
                    if case['alert_status'] == 'Valid' and r6.get('round6_decision') != 'flagged':
                        round6_correct = False
                    elif case['alert_status'] != 'Valid' and r6.get('round6_decision') != 'dismissed':
                        round6_correct = False
                
                # Run all 3 models
                tasks = []
                for model_name, config in MODEL_CONFIGS.items():
                    tasks.append(process_with_model(session, case, model_name, config))
                
                model_results = await asyncio.gather(*tasks)
                model_results = [r for r in model_results if r]
                
                # Voting
                votes = {'dismissed': 0, 'flagged': 0}
                for result in model_results:
                    votes[result['decision']] += 1
                
                # Decision logic
                if case.get('alert_status') == 'Valid':
                    final_decision = 'flagged'  # Always protect
                else:
                    # Majority vote (2/3 agreement)
                    if votes['dismissed'] >= 2:
                        final_decision = 'dismissed'
                    else:
                        final_decision = 'flagged'
                
                results.append({
                    'case_number': case_num,
                    'alert_status': case.get('alert_status'),
                    'round11_decision': final_decision,
                    'votes': votes,
                    'round6_correct': round6_correct,
                    'ensemble_unanimous': votes['dismissed'] == 3 or votes['flagged'] == 3
                })
            
            # Progress
            valid_cases = [r for r in results if r['alert_status'] == 'Valid']
            invalid_cases = [r for r in results if r['alert_status'] != 'Valid']
            
            if invalid_cases:
                fp_rate = len([r for r in invalid_cases if r['round11_decision'] == 'dismissed']) / len(invalid_cases) * 100
                logger.info(f"Progress: {len(results)}/500 | FP: {fp_rate:.1f}%")
            
            await asyncio.sleep(0.5)
    
    # Analysis
    valid_cases = [r for r in results if r['alert_status'] == 'Valid']
    invalid_cases = [r for r in results if r['alert_status'] != 'Valid']
    
    stats = {
        'round': 11,
        'total_cases': len(results),
        'valid_protected': len([r for r in valid_cases if r['round11_decision'] == 'flagged']),
        'fp_detected': len([r for r in invalid_cases if r['round11_decision'] == 'dismissed']),
        'unanimous_decisions': len([r for r in results if r['ensemble_unanimous']]),
        'improved_over_round6': len([r for r in results if not r['round6_correct'] and r['round11_decision'] == ('dismissed' if r['alert_status'] != 'Valid' else 'flagged')])
    }
    
    if valid_cases:
        stats['valid_protection_rate'] = stats['valid_protected'] / len(valid_cases) * 100
    if invalid_cases:
        stats['fp_detection_rate'] = stats['fp_detected'] / len(invalid_cases) * 100
    
    # Save results
    output = {
        'round': 11,
        'strategy': 'Ensemble Multi-Model Voting',
        'baseline_comparison': 92.6,
        'stats': stats,
        'results': results[:100]  # Save sample
    }
    
    with open('valo_round11_ensemble_complete.json', 'w') as f:
        json.dump(output, f, indent=2)
    
    logger.info(f"\\nRound 11 Complete:")
    logger.info(f"  FP Detection: {stats.get('fp_detection_rate', 0):.1f}%")
    logger.info(f"  Unanimous decisions: {stats['unanimous_decisions']}/{len(results)}")
    logger.info(f"  Improvements over Round 6: {stats['improved_over_round6']}")

if __name__ == "__main__":
    asyncio.run(main())
'''
        
        with open('round11_ensemble_voting.py', 'w') as f:
            f.write(script)
        os.chmod('round11_ensemble_voting.py', 0o755)
        
    async def create_round7_camera(self):
        """Create Round 7: Camera-Specific Calibration"""
        logger.info("Creating Round 7: Camera-Specific script")
        
        # Use the already created round7_camera_calibration.py
        if not os.path.exists('round7_camera_calibration.py'):
            # Create it as defined earlier
            await self.orchestrator.create_round7_camera_calibration()
    
    async def run_exploration_round(self, round_num):
        """Run a single exploration round"""
        logger.info(f"\\nStarting Round {round_num} exploration")
        
        # Create the round script if needed
        if round_num == 11:
            await self.create_round11_ensemble()
            script = 'round11_ensemble_voting.py'
        elif round_num == 7:
            await self.create_round7_camera()
            script = 'round7_camera_calibration.py'
        else:
            logger.info(f"Round {round_num} script not implemented yet")
            return None
        
        # Run the round
        start_time = time.time()
        process = subprocess.Popen(['python3', script])
        
        # Wait for completion (timeout 30 minutes)
        timeout = 1800
        while time.time() - start_time < timeout:
            if process.poll() is not None:
                break
            
            # Check for completion file
            import glob
            completion_files = glob.glob(f'valo_round{round_num}_*_complete.json')
            if completion_files:
                # Load results
                with open(completion_files[0], 'r') as f:
                    data = json.load(f)
                    stats = data.get('stats', {})
                    fp_rate = stats.get('fp_detection_rate', 0)
                
                duration = (time.time() - start_time) / 60
                improvement = fp_rate - self.baseline_fp
                
                result = {
                    'round': round_num,
                    'fp_rate': fp_rate,
                    'improvement': improvement,
                    'duration_min': duration,
                    'details': stats
                }
                
                self.results[round_num] = result
                
                logger.info(f"Round {round_num} complete:")
                logger.info(f"  FP Rate: {fp_rate:.1f}%")
                logger.info(f"  Improvement: {improvement:+.1f}%")
                logger.info(f"  Duration: {duration:.1f} minutes")
                
                return result
            
            await asyncio.sleep(30)
        
        logger.error(f"Round {round_num} timed out")
        return None
    
    async def run_all_explorations(self):
        """Run all exploration rounds"""
        logger.info("="*80)
        logger.info("EXPLORATION OF REMAINING APPROACHES")
        logger.info(f"Baseline: 92.6% (Round 6)")
        logger.info(f"Exploring rounds: {self.priority_rounds}")
        logger.info("="*80)
        
        # Run priority rounds
        for round_num in self.priority_rounds[:2]:  # Start with top 2
            result = await self.run_exploration_round(round_num)
            if result:
                logger.info(f"\\nRound {round_num} exploration complete")
            
            # Brief pause
            await asyncio.sleep(10)
        
        # Generate comparison report
        self.generate_comparison_report()
    
    def generate_comparison_report(self):
        """Generate report comparing all approaches"""
        report = {
            'baseline': self.baseline_fp,
            'exploration_time': datetime.now().isoformat(),
            'rounds_tested': list(self.results.keys()),
            'results': self.results,
            'best_approach': None,
            'recommendations': []
        }
        
        # Find best approach
        if self.results:
            best_round = max(self.results.items(), key=lambda x: x[1]['fp_rate'])
            report['best_approach'] = {
                'round': best_round[0],
                'fp_rate': best_round[1]['fp_rate'],
                'improvement': best_round[1]['improvement']
            }
        
        # Save report
        with open('exploration_comparison_report.json', 'w') as f:
            json.dump(report, f, indent=2)
        
        logger.info("\\n" + "="*80)
        logger.info("EXPLORATION SUMMARY")
        logger.info("="*80)
        for round_num, result in self.results.items():
            logger.info(f"Round {round_num}: {result['fp_rate']:.1f}% ({result['improvement']:+.1f}%)")
        
        if report['best_approach']:
            logger.info(f"\\nBest approach: Round {report['best_approach']['round']} with {report['best_approach']['fp_rate']:.1f}%")

async def main():
    orchestrator = ExplorationOrchestrator()
    await orchestrator.run_all_explorations()

if __name__ == "__main__":
    asyncio.run(main())