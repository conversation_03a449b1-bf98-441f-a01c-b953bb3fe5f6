#!/usr/bin/env python3
"""
VALO Intelligent Self-Learning System
Runs multiple rounds of testing with automatic fine-tuning
"""

import json
import base64
import requests
import os
from datetime import datetime
import time
from collections import defaultdict
import numpy as np

class IntelligentLearningSystem:
    def __init__(self):
        self.vlm_url = "http://**************:9500/v1/chat/completions"
        self.vlm_model = "VLM-38B-AWQ"
        self.session = requests.Session()
        
        # Learning parameters
        self.max_rounds = 10  # Maximum learning rounds
        self.target_accuracy = 95.0  # Target overall accuracy
        self.target_fp_detection = 80.0  # Target FP detection rate
        self.target_valid_protection = 99.0  # Target valid protection rate
        
        # Initial confidence thresholds
        self.confidence_thresholds = {
            'structure': 90,  # Start with 90% for structure detection
            'person': 50,     # 50% for person detection
            'ppe_compliant': 70,  # 70% for PPE compliance
            'behavioral_violation': 60  # 60% for behavioral violations
        }
        
        # Learning history
        self.learning_history = []
        self.error_patterns = defaultdict(list)
        
    def load_initial_prompt(self):
        """Load the base production prompt"""
        with open('FINAL_PRODUCTION_PROMPT.txt', 'r') as f:
            return f.read()
    
    def enhance_prompt_with_confidence(self, base_prompt, thresholds):
        """Enhance prompt with specific confidence thresholds"""
        enhanced = base_prompt.replace(
            "A) INDUSTRIAL STRUCTURE (need >90% confidence)",
            f"A) INDUSTRIAL STRUCTURE (need >{thresholds['structure']}% confidence)"
        )
        
        # Add confidence scoring instructions
        confidence_section = f"""
CONFIDENCE SCORING:
- Structure identification: Must be >{thresholds['structure']}% confident
- Person detection: >{thresholds['person']}% confidence required
- PPE compliance assessment: >{thresholds['ppe_compliant']}% confidence
- Behavioral violation detection: >{thresholds['behavioral_violation']}% confidence

When making decisions, include confidence percentages for each assessment.
"""
        
        # Insert before STEP 4
        enhanced = enhanced.replace("STEP 4: MAKE SAFETY DECISION", 
                                  confidence_section + "\nSTEP 4: MAKE SAFETY DECISION")
        
        # Update output format
        enhanced = enhanced.replace(
            "Confidence: [High/Medium/Low]",
            """Confidence: [High/Medium/Low]
Structure Confidence: [X%]
Person Confidence: [X%]
PPE Assessment Confidence: [X%]
Violation Detection Confidence: [X%]"""
        )
        
        return enhanced
    
    def analyze_error_with_vlm(self, case, predicted, actual, image_paths):
        """Ask VLM why it made an incorrect decision"""
        
        error_analysis_prompt = f"""You previously analyzed this safety image and concluded:
FALSE POSITIVE: {'YES' if predicted else 'NO'}

However, the correct answer was:
FALSE POSITIVE: {'YES' if actual else 'NO'}

Original case details:
- Case: {case['case_number']}
- Type: {case['infringement_type']}
- Remark: {case.get('remarks', 'N/A')}

Please analyze WHY you made this error:
1. What did you see in the image that led to your decision?
2. What did you miss or misinterpret?
3. What confidence threshold or rule adjustment would help?
4. Suggest specific improvements to avoid this error.

Be specific about visual features and decision logic."""

        try:
            # Encode images
            source_b64 = self.encode_image(image_paths['source'])
            cropped_b64 = self.encode_image(image_paths['cropped'])
            
            if not source_b64 or not cropped_b64:
                return None
            
            payload = {
                "model": self.vlm_model,
                "messages": [{
                    "role": "user",
                    "content": [
                        {"type": "text", "text": error_analysis_prompt},
                        {"type": "text", "text": "\n\nSOURCE IMAGE:"},
                        {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{source_b64}"}},
                        {"type": "text", "text": "\n\nCROPPED IMAGE:"},
                        {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{cropped_b64}"}}
                    ]
                }],
                "temperature": 0.3,
                "max_tokens": 300
            }
            
            response = self.session.post(self.vlm_url, json=payload, timeout=30)
            if response.status_code == 200:
                return response.json()['choices'][0]['message']['content']
                
        except Exception as e:
            print(f"Error analysis failed: {e}")
        
        return None
    
    def extract_confidence_scores(self, response):
        """Extract confidence scores from VLM response"""
        scores = {
            'structure': 0,
            'person': 0,
            'ppe': 0,
            'violation': 0
        }
        
        lines = response.split('\n')
        for line in lines:
            if 'Structure Confidence:' in line:
                try:
                    scores['structure'] = int(line.split(':')[1].strip().rstrip('%'))
                except:
                    pass
            elif 'Person Confidence:' in line:
                try:
                    scores['person'] = int(line.split(':')[1].strip().rstrip('%'))
                except:
                    pass
            elif 'PPE Assessment Confidence:' in line:
                try:
                    scores['ppe'] = int(line.split(':')[1].strip().rstrip('%'))
                except:
                    pass
            elif 'Violation Detection Confidence:' in line:
                try:
                    scores['violation'] = int(line.split(':')[1].strip().rstrip('%'))
                except:
                    pass
        
        return scores
    
    def analyze_round_results(self, results):
        """Analyze results and determine threshold adjustments"""
        
        # Categorize errors
        false_negatives = []  # Valid violations marked as FP
        false_positives = []  # FPs not detected
        
        for r in results:
            if not r['correct']:
                if r['actual_fp'] and not r['predicted_fp']:
                    false_positives.append(r)
                elif not r['actual_fp'] and r['predicted_fp']:
                    false_negatives.append(r)
        
        # Analyze confidence patterns in errors
        adjustments = {}
        
        # If missing too many valid violations (false negatives)
        if len(false_negatives) > 5:
            # We're being too aggressive, lower thresholds
            adjustments['structure'] = min(95, self.confidence_thresholds['structure'] + 5)
            adjustments['behavioral_violation'] = max(40, self.confidence_thresholds['behavioral_violation'] - 5)
            print(f"Too many valid violations missed. Increasing structure threshold to {adjustments['structure']}%")
        
        # If not catching enough FPs (false positives)
        if len(false_positives) > 10:
            # We're being too conservative, adjust
            adjustments['structure'] = max(85, self.confidence_thresholds['structure'] - 5)
            adjustments['ppe_compliant'] = min(80, self.confidence_thresholds['ppe_compliant'] + 5)
            print(f"Too many FPs not detected. Decreasing structure threshold to {adjustments['structure']}%")
        
        return adjustments
    
    def encode_image(self, image_path):
        """Encode image to base64"""
        try:
            if os.path.exists(image_path):
                with open(image_path, 'rb') as f:
                    return base64.b64encode(f.read()).decode('utf-8')
        except:
            pass
        return None
    
    def test_single_case(self, case, prompt, batch_data):
        """Test a single case with the current prompt"""
        
        # Get image paths
        batch_case = next((c for c in batch_data if c['case_number'] == case['case_number']), None)
        if not batch_case:
            return None
        
        # Encode images
        source_b64 = self.encode_image(batch_case['source_image'])
        cropped_b64 = self.encode_image(batch_case['cropped_image'])
        
        if not source_b64 or not cropped_b64:
            return None
        
        # Call VLM
        payload = {
            "model": self.vlm_model,
            "messages": [{
                "role": "user",
                "content": [
                    {"type": "text", "text": prompt},
                    {"type": "text", "text": "\n\nSOURCE IMAGE:"},
                    {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{source_b64}"}},
                    {"type": "text", "text": "\n\nCROPPED IMAGE:"},
                    {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{cropped_b64}"}}
                ]
            }],
            "temperature": 0.1,
            "max_tokens": 250
        }
        
        try:
            response = self.session.post(self.vlm_url, json=payload, timeout=20)
            if response.status_code == 200:
                vlm_response = response.json()['choices'][0]['message']['content']
                
                # Parse response
                predicted_fp = 'YES' in vlm_response.split('FALSE POSITIVE:')[1][:5]
                confidence_scores = self.extract_confidence_scores(vlm_response)
                
                return {
                    'case_number': case['case_number'],
                    'actual_fp': case['is_false_positive'],
                    'predicted_fp': predicted_fp,
                    'correct': predicted_fp == case['is_false_positive'],
                    'confidence_scores': confidence_scores,
                    'response': vlm_response,
                    'image_paths': {
                        'source': batch_case['source_image'],
                        'cropped': batch_case['cropped_image']
                    }
                }
        except Exception as e:
            print(f"Error testing case {case['case_number']}: {e}")
        
        return None
    
    def run_learning_round(self, round_num, test_cases, batch_data, sample_size=100):
        """Run one round of learning"""
        
        print(f"\n{'='*70}")
        print(f"LEARNING ROUND {round_num}")
        print(f"{'='*70}")
        
        # Get current prompt with thresholds
        base_prompt = self.load_initial_prompt()
        current_prompt = self.enhance_prompt_with_confidence(base_prompt, self.confidence_thresholds)
        
        print(f"Current thresholds:")
        for key, value in self.confidence_thresholds.items():
            print(f"  - {key}: {value}%")
        
        # Test on sample
        import random
        sample_cases = random.sample(test_cases, min(sample_size, len(test_cases)))
        
        results = []
        errors = []
        
        print(f"\nTesting {len(sample_cases)} cases...")
        for i, case in enumerate(sample_cases):
            if i % 20 == 0:
                print(f"  Progress: {i}/{len(sample_cases)}")
            
            result = self.test_single_case(case, current_prompt, batch_data)
            if result:
                results.append(result)
                if not result['correct']:
                    errors.append(result)
            
            time.sleep(0.5)  # Rate limit
        
        # Calculate metrics
        metrics = self.calculate_metrics(results)
        
        print(f"\nRound {round_num} Results:")
        print(f"  - Overall Accuracy: {metrics['accuracy']:.1f}%")
        print(f"  - FP Detection: {metrics['fp_detection']:.1f}%")
        print(f"  - Valid Protection: {metrics['valid_protection']:.1f}%")
        
        # Analyze errors if we haven't reached targets
        if metrics['valid_protection'] < self.target_valid_protection or \
           metrics['fp_detection'] < self.target_fp_detection:
            
            print(f"\nAnalyzing {len(errors)} errors...")
            
            # Sample errors for detailed analysis
            error_sample = random.sample(errors, min(5, len(errors)))
            
            for error in error_sample:
                print(f"\n  Analyzing error: {error['case_number']}")
                
                # Ask VLM why it made this error
                analysis = self.analyze_error_with_vlm(
                    {'case_number': error['case_number'], 
                     'infringement_type': 'Unknown',
                     'remarks': 'N/A'},
                    error['predicted_fp'],
                    error['actual_fp'],
                    error['image_paths']
                )
                
                if analysis:
                    self.error_patterns[round_num].append({
                        'case': error['case_number'],
                        'analysis': analysis
                    })
                    print(f"    VLM Analysis: {analysis[:200]}...")
            
            # Determine threshold adjustments
            adjustments = self.analyze_round_results(results)
            
            # Apply adjustments
            for key, new_value in adjustments.items():
                if key in self.confidence_thresholds:
                    self.confidence_thresholds[key] = new_value
        
        # Save round results
        round_data = {
            'round': round_num,
            'timestamp': datetime.now().isoformat(),
            'thresholds': self.confidence_thresholds.copy(),
            'metrics': metrics,
            'sample_size': len(results),
            'errors': len(errors)
        }
        
        self.learning_history.append(round_data)
        
        return metrics
    
    def calculate_metrics(self, results):
        """Calculate performance metrics"""
        
        if not results:
            return {'accuracy': 0, 'fp_detection': 0, 'valid_protection': 0}
        
        correct = sum(r['correct'] for r in results)
        accuracy = correct / len(results) * 100
        
        # FP detection
        actual_fps = [r for r in results if r['actual_fp']]
        if actual_fps:
            fp_detected = sum(r['predicted_fp'] for r in actual_fps)
            fp_rate = fp_detected / len(actual_fps) * 100
        else:
            fp_rate = 0
        
        # Valid protection
        actual_valid = [r for r in results if not r['actual_fp']]
        if actual_valid:
            valid_protected = sum(not r['predicted_fp'] for r in actual_valid)
            protection_rate = valid_protected / len(actual_valid) * 100
        else:
            protection_rate = 100
        
        return {
            'accuracy': accuracy,
            'fp_detection': fp_rate,
            'valid_protection': protection_rate
        }
    
    def run_full_intelligent_learning(self):
        """Run the complete intelligent learning process"""
        
        print("VALO INTELLIGENT SELF-LEARNING SYSTEM")
        print("="*70)
        print(f"Targets:")
        print(f"  - Valid Protection: {self.target_valid_protection}%")
        print(f"  - FP Detection: {self.target_fp_detection}%")
        print(f"  - Overall Accuracy: {self.target_accuracy}%")
        
        # Load test data
        print("\nLoading test data...")
        with open('valo_batch_round3_complete.json', 'r') as f:
            batch_data = json.load(f)['results']
        
        # Load case results
        with open('enhanced_1250_progress.json', 'r') as f:
            case_data = json.load(f)['results']
        
        # Run learning rounds
        for round_num in range(1, self.max_rounds + 1):
            metrics = self.run_learning_round(round_num, case_data, batch_data)
            
            # Check if targets met
            if metrics['valid_protection'] >= self.target_valid_protection and \
               metrics['fp_detection'] >= self.target_fp_detection:
                print(f"\n✅ TARGETS ACHIEVED IN ROUND {round_num}!")
                break
            
            # Early stopping if no improvement
            if round_num > 3:
                recent_metrics = [h['metrics'] for h in self.learning_history[-3:]]
                if all(m['valid_protection'] < self.target_valid_protection - 5 for m in recent_metrics):
                    print(f"\n⚠️ No improvement in last 3 rounds. Stopping.")
                    break
        
        # Generate final report
        self.generate_final_report()
    
    def generate_final_report(self):
        """Generate comprehensive learning report"""
        
        report = {
            'timestamp': datetime.now().isoformat(),
            'total_rounds': len(self.learning_history),
            'final_thresholds': self.confidence_thresholds,
            'learning_history': self.learning_history,
            'error_patterns': dict(self.error_patterns),
            'best_round': max(self.learning_history, 
                            key=lambda x: x['metrics']['valid_protection'] * 0.7 + x['metrics']['fp_detection'] * 0.3)
        }
        
        with open('valo_intelligent_learning_report.json', 'w') as f:
            json.dump(report, f, indent=2)
        
        print("\n" + "="*70)
        print("INTELLIGENT LEARNING COMPLETE")
        print("="*70)
        
        best = report['best_round']
        print(f"\nBest Performance (Round {best['round']}):")
        print(f"  - Overall Accuracy: {best['metrics']['accuracy']:.1f}%")
        print(f"  - FP Detection: {best['metrics']['fp_detection']:.1f}%")
        print(f"  - Valid Protection: {best['metrics']['valid_protection']:.1f}%")
        
        print(f"\nOptimal Thresholds:")
        for key, value in best['thresholds'].items():
            print(f"  - {key}: {value}%")
        
        print(f"\nReport saved to: valo_intelligent_learning_report.json")

def main():
    system = IntelligentLearningSystem()
    system.run_full_intelligent_learning()

if __name__ == "__main__":
    main()