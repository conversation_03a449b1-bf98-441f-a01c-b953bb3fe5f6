#!/usr/bin/env python3
"""
Create an explicit safety prompt with clear output rules
No ambiguity - protect all valid violations
"""

def create_explicit_prompt():
    """Create crystal clear safety prompt"""
    
    prompt = """SAFETY VIOLATION DETECTION SYSTEM

ANALYZE BOTH PROVIDED IMAGES.

STEP 1: What is in the CROPPED image?
A) STRUCTURE ONLY (needs >90% confidence)
B) PERSON (any human features)  
C) UNCLEAR

STEP 2: Apply these EXACT rules:

IF STRUCTURE ONLY (>90% confident):
→ Output: FALSE POSITIVE: YES

IF PERSON DETECTED:
→ Check for ANY of these violations:
  • Missing helmet
  • Missing vest  
  • Vest not fastened
  • Mobile phone use
  • Missing required equipment
  • Wrong location
  • Unsafe behavior
  
  IF ANY VIOLATION FOUND:
  → Output: FALSE POSITIVE: NO
  
  IF NO VIOLATIONS:
  → Output: FALSE POSITIVE: YES

IF UNCLEAR:
→ Output: FALSE POSITIVE: NO (safety first)

IMPORTANT: The question is "Is this a FALSE POSITIVE?"
- Real violations must output: FALSE POSITIVE: NO
- Only non-violations output: FALSE POSITIVE: YES

OUTPUT FORMAT (use exactly):
FALSE POSITIVE: [YES or NO]
Detection: [STRUCTURE/PERSON/UNCLEAR]
Violations: [List any found, or "None"]"""

    return prompt

def create_test_examples():
    """Create clear examples"""
    
    examples = """EXAMPLES:

Example 1: Crane structure only
Detection: STRUCTURE
Violations: None
FALSE POSITIVE: YES

Example 2: Person with helmet and vest using mobile phone
Detection: PERSON  
Violations: Mobile phone use
FALSE POSITIVE: NO

Example 3: Person with all PPE, no violations
Detection: PERSON
Violations: None
FALSE POSITIVE: YES

Example 4: Person missing helmet
Detection: PERSON
Violations: Missing helmet
FALSE POSITIVE: NO

Example 5: Unclear image
Detection: UNCLEAR
Violations: Cannot assess
FALSE POSITIVE: NO"""

    return examples

def main():
    # Create explicit prompt
    prompt = create_explicit_prompt()
    examples = create_test_examples()
    
    # Combine
    full_prompt = prompt + "\n\n" + examples
    
    # Save
    with open('explicit_safety_prompt.txt', 'w') as f:
        f.write(full_prompt)
    
    print("EXPLICIT SAFETY PROMPT CREATED")
    print("="*60)
    print("\nKey improvements:")
    print("1. Crystal clear output rules")
    print("2. Explicit FALSE POSITIVE: NO for violations")
    print("3. Examples showing exact format")
    print("4. No ambiguity in decision logic")
    
    print("\nThis prompt should:")
    print("✓ Protect all valid violations")
    print("✓ Correctly identify false positives")
    print("✓ Default to safety when uncertain")

if __name__ == "__main__":
    main()