#!/usr/bin/env python3
"""
Final comprehensive test of ALL 1250 cases using assumption-based approach
Implements robust retry logic and incremental progress saving
"""

import json
import base64
import requests
import os
from datetime import datetime
import time
from pathlib import Path
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('test_1250_final.log'),
        logging.StreamHandler()
    ]
)

# VLM Configuration
VLM_API_URL = "http://100.106.127.35:9500/v1/chat/completions"
VLM_MODEL = "VLM-38B-AWQ"

# The proven assumption-based prompt
ASSUMPTION_BASED_PROMPT = """SAFETY ALERT ANALYSIS
DEFAULT ASSUMPTION: This is a false positive (97% probability)
To override this assumption, you need CLEAR evidence:
- Person clearly visible AND  
- No helmet visible AND
- No safety vest visible

Is this a FALSE POSITIVE? YES/NO"""

class ComprehensiveVLMTester:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({'Content-Type': 'application/json'})
        self.progress_file = 'all_1250_progress.json'
        self.results_file = 'all_1250_results.json'
        self.tested_cases = {}
        self.load_progress()
    
    def load_progress(self):
        """Load previous progress if exists"""
        if os.path.exists(self.progress_file):
            with open(self.progress_file, 'r') as f:
                progress = json.load(f)
                self.tested_cases = progress.get('tested_cases', {})
                logging.info(f"Loaded progress: {len(self.tested_cases)} cases already tested")
    
    def save_progress(self):
        """Save current progress"""
        progress = {
            'timestamp': datetime.now().isoformat(),
            'tested_cases': self.tested_cases,
            'total_tested': len(self.tested_cases),
            'total_dataset': 1250
        }
        with open(self.progress_file, 'w') as f:
            json.dump(progress, f, indent=2)
    
    def encode_image(self, image_path):
        """Encode image to base64"""
        if not os.path.exists(image_path):
            return None
        try:
            with open(image_path, 'rb') as f:
                return base64.b64encode(f.read()).decode('utf-8')
        except Exception as e:
            logging.error(f"Error encoding {image_path}: {e}")
            return None
    
    def call_vlm_with_retry(self, image_base64, max_retries=5):
        """Call VLM with exponential backoff and retry logic"""
        payload = {
            "model": VLM_MODEL,
            "messages": [{
                "role": "user",
                "content": [
                    {"type": "text", "text": ASSUMPTION_BASED_PROMPT},
                    {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{image_base64}"}}
                ]
            }],
            "temperature": 0.1,
            "max_tokens": 50
        }
        
        for attempt in range(max_retries):
            try:
                # Adaptive timeout based on attempt
                timeout = 20 + (attempt * 5)  # 20s, 25s, 30s, 35s, 40s
                
                response = self.session.post(VLM_API_URL, json=payload, timeout=timeout)
                if response.status_code == 200:
                    result = response.json()
                    return result['choices'][0]['message']['content'].strip()
                else:
                    logging.warning(f"API error {response.status_code} on attempt {attempt + 1}")
                    if attempt < max_retries - 1:
                        time.sleep(2 ** attempt)  # Exponential backoff
            except requests.Timeout:
                logging.warning(f"Timeout on attempt {attempt + 1} (timeout={timeout}s)")
                if attempt < max_retries - 1:
                    time.sleep(2 ** attempt)
            except Exception as e:
                logging.error(f"Error on attempt {attempt + 1}: {e}")
                if attempt < max_retries - 1:
                    time.sleep(2 ** attempt)
        
        return None
    
    def process_single_case(self, case):
        """Process a single case"""
        case_num = case['case_number']
        
        # Skip if already tested
        if case_num in self.tested_cases:
            return self.tested_cases[case_num]
        
        cropped_path = case['cropped_image']
        is_valid = not case['is_false_positive']
        
        # Encode image
        image_base64 = self.encode_image(cropped_path)
        if not image_base64:
            result = {
                'case_number': case_num,
                'status': 'error',
                'error': 'Failed to encode image',
                'timestamp': datetime.now().isoformat()
            }
            self.tested_cases[case_num] = result
            return result
        
        # Call VLM with retry
        response = self.call_vlm_with_retry(image_base64)
        
        if response:
            is_fp = 'YES' in response.upper()
            correct = (is_fp and not is_valid) or (not is_fp and is_valid)
            
            result = {
                'case_number': case_num,
                'status': 'success',
                'response': response,
                'predicted_fp': is_fp,
                'actual_valid': is_valid,
                'correct': correct,
                'timestamp': datetime.now().isoformat()
            }
        else:
            result = {
                'case_number': case_num,
                'status': 'failed',
                'error': 'VLM timeout after all retries',
                'timestamp': datetime.now().isoformat()
            }
        
        self.tested_cases[case_num] = result
        return result
    
    def test_all_1250_cases(self):
        """Test all 1250 cases with robust error handling"""
        logging.info("="*80)
        logging.info("TESTING ALL 1250 CASES WITH ASSUMPTION-BASED APPROACH")
        logging.info("="*80)
        
        # Load full dataset
        with open('valo_batch_round3_complete.json', 'r') as f:
            full_data = json.load(f)
        
        all_cases = full_data['results']
        total_cases = len(all_cases)
        
        logging.info(f"Total cases to test: {total_cases}")
        logging.info(f"Already tested: {len(self.tested_cases)}")
        logging.info(f"Remaining: {total_cases - len(self.tested_cases)}")
        
        # Process all cases
        start_time = time.time()
        last_save_time = time.time()
        
        for i, case in enumerate(all_cases):
            case_num = case['case_number']
            
            # Progress display
            total_tested = len(self.tested_cases)
            progress = (total_tested / total_cases) * 100
            
            print(f"\rProgress: {total_tested}/{total_cases} ({progress:.1f}%) | Current: {case_num}", 
                  end='', flush=True)
            
            # Process case
            result = self.process_single_case(case)
            
            # Log result
            if result['status'] == 'success':
                logging.info(f"✓ {case_num}: {result['response']} (Correct: {result['correct']})")
            else:
                logging.warning(f"✗ {case_num}: {result.get('error', 'Unknown error')}")
            
            # Save progress every 25 cases or every 5 minutes
            if len(self.tested_cases) % 25 == 0 or (time.time() - last_save_time) > 300:
                self.save_progress()
                self.calculate_current_stats()
                last_save_time = time.time()
        
        # Final save and analysis
        self.save_progress()
        self.generate_final_report()
        
        elapsed = time.time() - start_time
        logging.info(f"\n\nTotal processing time: {elapsed/60:.1f} minutes")
        logging.info(f"Average per case: {elapsed/len(self.tested_cases):.2f} seconds")
    
    def calculate_current_stats(self):
        """Calculate and display current statistics"""
        successful = sum(1 for r in self.tested_cases.values() if r['status'] == 'success')
        failed = sum(1 for r in self.tested_cases.values() if r['status'] == 'failed')
        errors = sum(1 for r in self.tested_cases.values() if r['status'] == 'error')
        
        if successful > 0:
            correct = sum(1 for r in self.tested_cases.values() 
                         if r['status'] == 'success' and r['correct'])
            fp_cases = sum(1 for r in self.tested_cases.values() 
                          if r['status'] == 'success' and not r.get('actual_valid', True))
            fp_detected = sum(1 for r in self.tested_cases.values() 
                            if r['status'] == 'success' and not r.get('actual_valid', True) 
                            and r.get('predicted_fp', False))
            valid_cases = sum(1 for r in self.tested_cases.values() 
                            if r['status'] == 'success' and r.get('actual_valid', False))
            valid_protected = sum(1 for r in self.tested_cases.values() 
                                if r['status'] == 'success' and r.get('actual_valid', False) 
                                and not r.get('predicted_fp', True))
            
            accuracy = (correct / successful) * 100
            fp_rate = (fp_detected / fp_cases) * 100 if fp_cases > 0 else 0
            valid_rate = (valid_protected / valid_cases) * 100 if valid_cases > 0 else 100
            
            logging.info(f"\n--- Current Stats ({len(self.tested_cases)} cases) ---")
            logging.info(f"Successful: {successful}, Failed: {failed}, Errors: {errors}")
            logging.info(f"Accuracy: {accuracy:.1f}%")
            logging.info(f"FP Detection: {fp_rate:.1f}% ({fp_detected}/{fp_cases})")
            logging.info(f"Valid Protection: {valid_rate:.1f}% ({valid_protected}/{valid_cases})")
            logging.info("-" * 40)
    
    def generate_final_report(self):
        """Generate comprehensive final report"""
        print("\n\n" + "="*80)
        print("FINAL REPORT: ALL 1250 CASES TESTED")
        print("="*80)
        
        # Calculate final statistics
        total_tested = len(self.tested_cases)
        successful = sum(1 for r in self.tested_cases.values() if r['status'] == 'success')
        failed = sum(1 for r in self.tested_cases.values() if r['status'] == 'failed')
        errors = sum(1 for r in self.tested_cases.values() if r['status'] == 'error')
        
        print(f"\n1. TESTING SUMMARY")
        print("-"*50)
        print(f"Total cases in dataset: 1,250")
        print(f"Total cases tested: {total_tested}")
        print(f"Successful tests: {successful}")
        print(f"Failed (timeout): {failed}")
        print(f"Errors: {errors}")
        print(f"Success rate: {(successful/total_tested)*100:.1f}%")
        
        if successful > 0:
            # Performance metrics
            correct = sum(1 for r in self.tested_cases.values() 
                         if r['status'] == 'success' and r['correct'])
            
            # Count FP and valid cases
            fp_results = [r for r in self.tested_cases.values() 
                         if r['status'] == 'success' and not r.get('actual_valid', True)]
            valid_results = [r for r in self.tested_cases.values() 
                           if r['status'] == 'success' and r.get('actual_valid', False)]
            
            fp_detected = sum(1 for r in fp_results if r.get('predicted_fp', False))
            valid_protected = sum(1 for r in valid_results if not r.get('predicted_fp', True))
            
            accuracy = (correct / successful) * 100
            fp_rate = (fp_detected / len(fp_results)) * 100 if fp_results else 0
            valid_rate = (valid_protected / len(valid_results)) * 100 if valid_results else 100
            
            print(f"\n2. PERFORMANCE METRICS")
            print("-"*50)
            print(f"Overall Accuracy: {accuracy:.1f}%")
            print(f"FP Detection Rate: {fp_rate:.1f}% ({fp_detected}/{len(fp_results)})")
            print(f"Valid Protection Rate: {valid_rate:.1f}% ({valid_protected}/{len(valid_results)})")
            
            # Production estimates
            print(f"\n3. PRODUCTION ESTIMATES")
            print("-"*50)
            print(f"Based on {fp_rate:.1f}% test performance:")
            print(f"  - Optimistic (15% degradation): {fp_rate * 0.85:.1f}%")
            print(f"  - Realistic (20% degradation): {fp_rate * 0.80:.1f}%")
            print(f"  - Conservative (25% degradation): {fp_rate * 0.75:.1f}%")
            
            # Target achievement
            meets_target = (fp_rate * 0.80) >= 70
            print(f"\n4. TARGET ACHIEVEMENT")
            print("-"*50)
            print(f"70% FP reduction target: {'✓ ACHIEVED' if meets_target else '✗ NOT ACHIEVED'}")
            print(f"Expected production performance: {fp_rate * 0.80:.1f}%")
        
        # Save final results
        final_report = {
            'test_date': datetime.now().isoformat(),
            'dataset_size': 1250,
            'cases_tested': total_tested,
            'successful_tests': successful,
            'failed_tests': failed,
            'error_tests': errors,
            'performance': {
                'accuracy': accuracy if successful > 0 else 0,
                'fp_detection_rate': fp_rate if successful > 0 else 0,
                'valid_protection_rate': valid_rate if successful > 0 else 0,
                'fp_detected_count': fp_detected if successful > 0 else 0,
                'fp_total_count': len(fp_results) if successful > 0 else 0,
                'valid_protected_count': valid_protected if successful > 0 else 0,
                'valid_total_count': len(valid_results) if successful > 0 else 0
            },
            'production_estimates': {
                'optimistic_15pct': fp_rate * 0.85 if successful > 0 else 0,
                'realistic_20pct': fp_rate * 0.80 if successful > 0 else 0,
                'conservative_25pct': fp_rate * 0.75 if successful > 0 else 0
            },
            'meets_70_target': meets_target if successful > 0 else False,
            'all_results': self.tested_cases
        }
        
        with open(self.results_file, 'w') as f:
            json.dump(final_report, f, indent=2)
        
        print(f"\n✓ Final results saved to: {self.results_file}")
        print(f"✓ Progress saved to: {self.progress_file}")
        print("="*80)

if __name__ == "__main__":
    tester = ComprehensiveVLMTester()
    tester.test_all_1250_cases()