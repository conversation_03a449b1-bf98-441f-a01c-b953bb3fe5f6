#!/usr/bin/env python3
"""
Monitor exploration rounds progress
"""
import time
import os
import json
import glob
from datetime import datetime

print("EXPLORATION ROUNDS MONITOR")
print("="*60)
print("Baseline: 92.6% (Round 6)")
print("Testing different approaches to find the best strategy")
print("="*60)

completed_rounds = {}

while True:
    try:
        # Check for completed rounds
        for round_num in range(7, 16):
            if round_num not in completed_rounds:
                files = glob.glob(f'valo_round{round_num}_*_complete.json')
                if files:
                    with open(files[0], 'r') as f:
                        data = json.load(f)
                        stats = data.get('stats', {})
                        fp_rate = stats.get('fp_detection_rate', 0)
                        completed_rounds[round_num] = fp_rate
                        
                        improvement = fp_rate - 92.6
                        print(f"\n✅ Round {round_num} Complete!")
                        print(f"   FP Rate: {fp_rate:.1f}%")
                        print(f"   vs Baseline: {improvement:+.1f}%")
        
        # Check exploration log
        if os.path.exists('exploration_rounds.log'):
            with open('exploration_rounds.log', 'r') as f:
                lines = f.readlines()
                for line in reversed(lines[-5:]):
                    if 'Round' in line and 'exploration' in line:
                        print(f"\r[{datetime.now().strftime('%H:%M:%S')}] {line.strip()}", end='', flush=True)
                        break
        
        # Check if exploration report exists
        if os.path.exists('exploration_comparison_report.json'):
            print("\n\n📊 EXPLORATION COMPLETE!")
            with open('exploration_comparison_report.json', 'r') as f:
                report = json.load(f)
                
            print("\nResults Summary:")
            for round_num, result in report['results'].items():
                print(f"  Round {round_num}: {result['fp_rate']:.1f}% ({result['improvement']:+.1f}%)")
            
            if report['best_approach']:
                print(f"\n🏆 Best Approach: Round {report['best_approach']['round']}")
                print(f"   Achievement: {report['best_approach']['fp_rate']:.1f}%")
            break
        
        time.sleep(5)
        
    except KeyboardInterrupt:
        print("\n\nMonitoring stopped.")
        break
    except Exception as e:
        print(f"\nError: {e}")
        time.sleep(5)