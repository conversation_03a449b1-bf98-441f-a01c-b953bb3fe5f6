{"analysis_timestamp": "2025-07-26T15:39:56.897582", "summary": {"total_cases": 1250, "agreements": 52, "disagreements": 1198, "vlm_accuracy": 4.16, "critical_errors": 0, "missed_fps": 1198, "disagreement_details": {"vlm_fp_human_tp": [], "vlm_tp_human_fp": ["V1250623121", "V1250623122", "V1250623123", "V1250623124", "V1250623125", "V1250623126", "V1250623127", "V1250623128", "V1250623129", "V1250623130", "V1250623131", "V1250623132", "V1250623133", "V1250623134", "V1250623135", "V1250623136", "V1250623138", "V1250623139", "V1250623140", "V1250623141", "V1250623142", "V1250623143", "V1250623145", "V1250623146", "V1250623147", "V1250623148", "V1250623149", "V1250623150", "V1250623151", "V1250623152", "V1250623153", "V1250623154", "V1250623155", "V1250623156", "V1250623157", "V1250623159", "V1250623160", "V1250623161", "V1250623162", "V1250623163", "V1250623164", "V1250623165", "V1250623166", "V1250623167", "V1250623168", "V1250623169", "V1250623170", "V1250623171", "V1250623172", "V1250623173", "V1250623175", "V1250623176", "V1250623177", "V1250623178", "V1250623179", "V1250623180", "V1250623181", "V1250623182", "V1250623183", "V1250623184", "V1250623185", "V1250623186", "V1250623187", "V1250623188", "V1250623189", "V1250623190", "V1250624001", "V1250624002", "V1250624003", "V1250624004", "V1250624005", "V1250624006", "V1250624007", "V1250624008", "V1250624009", "V1250624010", "V1250624011", "V1250624012", "V1250624013", "V1250624014", "V1250624015", "V1250624016", "V1250624017", "V1250624018", "V1250624019", "V1250624020", "V1250624021", "V1250624022", "V1250624025", "V1250624026", "V1250624027", "V1250624028", "V1250624029", "V1250624030", "V1250624031", "V1250624032", "V1250624033", "V1250624034", "V1250624035", "V1250624036", "V1250624037", "V1250624038", "V1250624039", "V1250624040", "V1250624041", "V1250624042", "V1250624043", "V1250624044", "V1250624045", "V1250624047", "V1250624048", "V1250624049", "V1250624050", "V1250624051", "V1250624052", "V1250624053", "V1250624054", "V1250624055", "V1250624056", "V1250624057", "V1250624058", "V1250624059", "V1250624060", "V1250624061", "V1250624062", "V1250624063", "V1250624064", "V1250624065", "V1250624066", "V1250624067", "V1250624068", "V1250624070", "V1250624072", "V1250624073", "V1250624074", "V1250624075", "V1250624076", "V1250624077", "V1250624079", "V1250624080", "V1250624081", "V1250624082", "V1250624084", "V1250624085", "V1250624086", "V1250624087", "V1250624088", "V1250624089", "V1250624090", "V1250624091", "V1250624092", "V1250624093", "V1250624094", "V1250624095", "V1250624096", "V1250624097", "V1250624098", "V1250624099", "V1250624100", "V1250624101", "V1250624102", "V1250624103", "V1250624104", "V1250624105", "V1250624106", "V1250624107", "V1250624108", "V1250624109", "V1250624110", "V1250624111", "V1250624112", "V1250624113", "V1250624114", "V1250624115", "V1250624116", "V1250624117", "V1250624118", "V1250624119", "V1250624120", "V1250624121", "V1250624122", "V1250624123", "V1250624124", "V1250624125", "V1250624126", "V1250624127", "V1250624128", "V1250624129", "V1250624130", "V1250624131", "V1250624132", "V1250624133", "V1250624134", "V1250624135", "V1250624136", "V1250624137", "V1250624138", "V1250624139", "V1250624140", "V1250624141", "V1250624142", "V1250624143", "V1250624144", "V1250624145", "V1250624146", "V1250624147", "V1250624148", "V1250624149", "V1250624150", "V1250624151", "V1250624152", "V1250624153", "V1250624154", "V1250624155", "V1250624157", "V1250624158", "V1250624159", "V1250624161", "V1250624162", "V1250624163", "V1250624164", "V1250624165", "V1250624166", "V1250624168", "V1250624169", "V1250624170", "V1250624171", "V1250624172", "V1250624173", "V1250624174", "V1250624175", "V1250624176", "V1250624177", "V1250624178", "V1250625001", "V1250625002", "V1250625003", "V1250625004", "V1250625005", "V1250625006", "V1250625007", "V1250625008", "V1250625009", "V1250625010", "V1250625011", "V1250625012", "V1250625013", "V1250625014", "V1250625015", "V1250625016", "V1250625017", "V1250625018", "V1250625019", "V1250625020", "V1250625021", "V1250625022", "V1250625023", "V1250625024", "V1250625025", "V1250625026", "V1250625028", "V1250625029", "V1250625030", "V1250625031", "V1250625032", "V1250625033", "V1250625034", "V1250625035", "V1250625036", "V1250625037", "V1250625038", "V1250625039", "V1250625040", "V1250625041", "V1250625042", "V1250625043", "V1250625044", "V1250625045", "V1250625047", "V1250625048", "V1250625049", "V1250625050", "V1250625051", "V1250625052", "V1250625053", "V1250625054", "V1250625055", "V1250625056", "V1250625057", "V1250625058", "V1250625059", "V1250625060", "V1250625061", "V1250625062", "V1250625064", "V1250625065", "V1250625066", "V1250625067", "V1250625068", "V1250625069", "V1250625070", "V1250625071", "V1250625072", "V1250625073", "V1250625074", "V1250625075", "V1250625076", "V1250625077", "V1250625078", "V1250625079", "V1250625080", "V1250625081", "V1250625082", "V1250625083", "V1250625084", "V1250625085", "V1250625086", "V1250625087", "V1250625088", "V1250625089", "V1250625090", "V1250625091", "V1250625092", "V1250625093", "V1250625094", "V1250625095", "V1250625096", "V1250625097", "V1250625098", "V1250625099", "V1250625100", "V1250625101", "V1250625102", "V1250625103", "V1250625104", "V1250625105", "V1250625106", "V1250625107", "V1250625108", "V1250625109", "V1250625110", "V1250625111", "V1250625112", "V1250625113", "V1250625114", "V1250625115", "V1250625116", "V1250625117", "V1250625118", "V1250625119", "V1250625120", "V1250625121", "V1250625122", "V1250625123", "V1250625124", "V1250625125", "V1250625126", "V1250625127", "V1250625128", "V1250625129", "V1250625130", "V1250625131", "V1250625132", "V1250625133", "V1250625134", "V1250625135", "V1250625136", "V1250625137", "V1250625138", "V1250625139", "V1250625140", "V1250625141", "V1250625142", "V1250625143", "V1250625144", "V1250625145", "V1250625146", "V1250625147", "V1250625148", "V1250625149", "V1250625150", "V1250625151", "V1250625152", "V1250625153", "V1250625154", "V1250625155", "V1250625156", "V1250626001", "V1250626002", "V1250626003", "V1250626004", "V1250626005", "V1250626006", "V1250626007", "V1250626008", "V1250626009", "V1250626010", "V1250626011", "V1250626012", "V1250626013", "V1250626014", "V1250626015", "V1250626016", "V1250626017", "V1250626018", "V1250626019", "V1250626020", "V1250626021", "V1250626022", "V1250626023", "V1250626024", "V1250626025", "V1250626026", "V1250626027", "V1250626028", "V1250626029", "V1250626030", "V1250626031", "V1250626032", "V1250626033", "V1250626034", "V1250626035", "V1250626036", "V1250626037", "V1250626038", "V1250626039", "V1250626040", "V1250626042", "V1250626043", "V1250626044", "V1250626045", "V1250626046", "V1250626047", "V1250626048", "V1250626049", "V1250626050", "V1250626051", "V1250626052", "V1250626053", "V1250626055", "V1250626057", "V1250626058", "V1250626059", "V1250626061", "V1250626062", "V1250626063", "V1250626064", "V1250626065", "V1250626066", "V1250626067", "V1250626068", "V1250626070", "V1250626071", "V1250626072", "V1250626074", "V1250626075", "V1250626076", "V1250626077", "V1250626078", "V1250626079", "V1250626080", "V1250626081", "V1250626082", "V1250626083", "V1250626084", "V1250626085", "V1250626086", "V1250626087", "V1250626088", "V1250626089", "V1250626090", "V1250626091", "V1250626092", "V1250626093", "V1250626094", "V1250626095", "V1250626096", "V1250626097", "V1250626099", "V1250626100", "V1250626101", "V1250626102", "V1250626103", "V1250626104", "V1250626105", "V1250626106", "V1250626107", "V1250626108", "V1250626109", "V1250626110", "V1250626111", "V1250626112", "V1250626113", "V1250626116", "V1250626117", "V1250626118", "V1250626119", "V1250626120", "V1250626121", "V1250626122", "V1250626123", "V1250626124", "V1250626125", "V1250626126", "V1250626127", "V1250626130", "V1250626131", "V1250626132", "V1250626133", "V1250626134", "V1250626136", "V1250626137", "V1250626138", "V1250626139", "V1250626140", "V1250626141", "V1250626142", "V1250626143", "V1250626144", "V1250626145", "V1250626146", "V1250626147", "V1250626148", "V1250626149", "V1250626150", "V1250626151", "V1250626152", "V1250626153", "V1250626154", "V1250626155", "V1250626157", "V1250626158", "V1250626159", "V1250626160", "V1250626161", "V1250626162", "V1250626163", "V1250626164", "V1250626165", "V1250626166", "V1250626167", "V1250626168", "V1250626169", "V1250626170", "V1250626171", "V1250626172", "V1250626173", "V1250626174", "V1250627001", "V1250627002", "V1250627003", "V1250627004", "V1250627005", "V1250627006", "V1250627007", "V1250627008", "V1250627009", "V1250627010", "V1250627011", "V1250627012", "V1250627013", "V1250627014", "V1250627015", "V1250627016", "V1250627017", "V1250627018", "V1250627019", "V1250627020", "V1250627021", "V1250627022", "V1250627023", "V1250627024", "V1250627025", "V1250627026", "V1250627028", "V1250627029", "V1250627030", "V1250627032", "V1250627033", "V1250627034", "V1250627035", "V1250627036", "V1250627037", "V1250627038", "V1250627039", "V1250627040", "V1250627041", "V1250627042", "V1250627043", "V1250627045", "V1250627046", "V1250627047", "V1250627048", "V1250627049", "V1250627050", "V1250627051", "V1250627052", "V1250627053", "V1250627054", "V1250627055", "V1250627056", "V1250627057", "V1250627058", "V1250627059", "V1250627060", "V1250627061", "V1250627062", "V1250627063", "V1250627064", "V1250627065", "V1250627066", "V1250627067", "V1250627068", "V1250627069", "V1250627070", "V1250627071", "V1250627072", "V1250627073", "V1250627074", "V1250627075", "V1250627076", "V1250627077", "V1250627078", "V1250627079", "V1250627080", "V1250627081", "V1250627082", "V1250627083", "V1250627084", "V1250627085", "V1250627086", "V1250627087", "V1250627088", "V1250627089", "V1250627090", "V1250627091", "V1250627092", "V1250627093", "V1250627094", "V1250627095", "V1250627096", "V1250627097", "V1250627098", "V1250627099", "V1250627100", "V1250627101", "V1250627102", "V1250627103", "V1250627104", "V1250627105", "V1250627106", "V1250627107", "V1250627108", "V1250627109", "V1250627110", "V1250627111", "V1250627112", "V1250627113", "V1250627114", "V1250627115", "V1250627116", "V1250627117", "V1250627118", "V1250627119", "V1250627120", "V1250627121", "V1250627122", "V1250627123", "V1250627124", "V1250627125", "V1250627126", "V1250627127", "V1250627128", "V1250627129", "V1250627130", "V1250627131", "V1250627132", "V1250627133", "V1250627134", "V1250627135", "V1250627136", "V1250627137", "V1250627138", "V1250627139", "V1250627140", "V1250627141", "V1250627142", "V1250627143", "V1250627144", "V1250627145", "V1250627146", "V1250627147", "V1250627148", "V1250627149", "V1250627150", "V1250627151", "V1250627152", "V1250627153", "V1250627154", "V1250627155", "V1250627156", "V1250627157", "V1250627158", "V1250627159", "V1250627160", "V1250627161", "V1250627162", "V1250627163", "V1250627164", "V1250627165", "V1250627166", "V1250627167", "V1250627168", "V1250627169", "V1250627170", "V1250627171", "V1250627172", "V1250627173", "V1250627174", "V1250627175", "V1250627176", "V1250627177", "V1250627178", "V1250627180", "V1250627181", "V1250627182", "V1250627183", "V1250627184", "V1250627185", "V1250627186", "V1250627187", "V1250627188", "V1250627189", "V1250627190", "V1250627191", "V1250627192", "V1250627193", "V1250627195", "V1250627196", "V1250627197", "V1250627198", "V1250627199", "V1250627200", "V1250627201", "V1250627202", "V1250627203", "V1250627205", "V1250627206", "V1250627207", "V1250627209", "V1250627210", "V1250627211", "V1250627212", "V1250627213", "V1250627214", "V1250627215", "V1250627216", "V1250627217", "V1250627218", "V1250627219", "V1250627220", "V1250627221", "V1250627222", "V1250627223", "V1250627224", "V1250627225", "V1250627226", "V1250627227", "V1250627228", "V1250627229", "V1250627230", "V1250627231", "V1250627232", "V1250627233", "V1250627234", "V1250627235", "V1250627236", "V1250627237", "V1250627238", "V1250627239", "V1250627240", "V1250627241", "V1250627242", "V1250627243", "V1250627244", "V1250627245", "V1250627246", "V1250628001", "V1250628002", "V1250628003", "V1250628004", "V1250628005", "V1250628006", "V1250628007", "V1250628008", "V1250628009", "V1250628010", "V1250628011", "V1250628012", "V1250628013", "V1250628014", "V1250628015", "V1250628016", "V1250628017", "V1250628018", "V1250628019", "V1250628020", "V1250628021", "V1250628022", "V1250628023", "V1250628024", "V1250628025", "V1250628026", "V1250628027", "V1250628028", "V1250628029", "V1250628031", "V1250628032", "V1250628033", "V1250628034", "V1250628035", "V1250628036", "V1250628037", "V1250628038", "V1250628039", "V1250628040", "V1250628041", "V1250628043", "V1250628044", "V1250628045", "V1250628046", "V1250628047", "V1250628048", "V1250628049", "V1250628050", "V1250628051", "V1250628052", "V1250628053", "V1250628054", "V1250628055", "V1250628056", "V1250628057", "V1250628058", "V1250628059", "V1250628060", "V1250628061", "V1250628062", "V1250628063", "V1250628064", "V1250628065", "V1250628066", "V1250628067", "V1250628068", "V1250628069", "V1250628070", "V1250628071", "V1250628072", "V1250628073", "V1250628074", "V1250628075", "V1250628076", "V1250628077", "V1250628078", "V1250628079", "V1250628080", "V1250628081", "V1250628082", "V1250628083", "V1250628084", "V1250628085", "V1250628086", "V1250628087", "V1250628088", "V1250628089", "V1250628090", "V1250628091", "V1250628092", "V1250628093", "V1250628094", "V1250628095", "V1250628096", "V1250628097", "V1250628098", "V1250628099", "V1250628100", "V1250628102", "V1250628103", "V1250628104", "V1250628105", "V1250628106", "V1250628107", "V1250628108", "V1250628109", "V1250628110", "V1250628111", "V1250628113", "V1250628114", "V1250628115", "V1250628116", "V1250628117", "V1250628118", "V1250628121", "V1250628122", "V1250628123", "V1250628124", "V1250628125", "V1250628126", "V1250628127", "V1250628128", "V1250628129", "V1250628130", "V1250628131", "V1250628132", "V1250628133", "V1250628134", "V1250628135", "V1250628136", "V1250628137", "V1250628138", "V1250628139", "V1250628140", "V1250628141", "V1250628142", "V1250628143", "V1250628144", "V1250628145", "V1250629001", "V1250629002", "V1250629003", "V1250629004", "V1250629005", "V1250629006", "V1250629007", "V1250629008", "V1250629009", "V1250629010", "V1250629011", "V1250629012", "V1250629013", "V1250629014", "V1250629015", "V1250629016", "V1250629017", "V1250629018", "V1250629019", "V1250629020", "V1250629021", "V1250629022", "V1250629023", "V1250629024", "V1250629025", "V1250629026", "V1250629027", "V1250629028", "V1250629029", "V1250629030", "V1250629031", "V1250629032", "V1250629033", "V1250629034", "V1250629035", "V1250629036", "V1250629037", "V1250629038", "V1250629039", "V1250629040", "V1250629041", "V1250629042", "V1250629043", "V1250629044", "V1250629045", "V1250629046", "V1250629047", "V1250629048", "V1250629049", "V1250629050", "V1250629052", "V1250629053", "V1250629054", "V1250629055", "V1250629056", "V1250629057", "V1250629058", "V1250629059", "V1250629060", "V1250629061", "V1250629062", "V1250629063", "V1250629064", "V1250629065", "V1250629066", "V1250629067", "V1250629068", "V1250629069", "V1250629070", "V1250629071", "V1250629072", "V1250629073", "V1250629074", "V1250629075", "V1250629076", "V1250629077", "V1250629078", "V1250629079", "V1250629080", "V1250629081", "V1250629082", "V1250629083", "V1250629085", "V1250629086", "V1250629087", "V1250629088", "V1250629089", "V1250629091", "V1250629092", "V1250629093", "V1250629094", "V1250629095", "V1250629096", "V1250629097", "V1250629098", "V1250629099", "V1250629100", "V1250629101", "V1250629103", "V1250629104", "V1250629105", "V1250629106", "V1250629107", "V1250629108", "V1250629109", "V1250629110", "V1250629111", "V1250629112", "V1250629113", "V1250629114", "V1250629115", "V1250629116", "V1250629117", "V1250629118", "V1250629120", "V1250629121", "V1250629122", "V1250629123", "V1250629124", "V1250629125", "V1250629126", "V1250629127", "V1250629128", "V1250629129", "V1250629130", "V1250629131", "V1250629132", "V1250629133", "V1250629134", "V1250629135", "V1250629136", "V1250629137", "V1250629138", "V1250629139", "V1250629140", "V1250629141", "V1250629142", "V1250629143", "V1250629144", "V1250629145", "V1250629146", "V1250629147", "V1250629148", "V1250629149", "V1250629150", "V1250629151", "V1250629152", "V1250629153", "V1250629154", "V1250629155", "V1250629156", "V1250629157", "V1250629158", "V1250629159", "V1250629160", "V1250629161", "V1250629162", "V1250630001", "V1250630002", "V1250630003", "V1250630004", "V1250630005", "V1250630006", "V1250630007", "V1250630008", "V1250630009", "V1250630010", "V1250630011", "V1250630013", "V1250630014", "V1250630015", "V1250630016", "V1250630017", "V1250630018", "V1250630020", "V1250630021", "V1250630022", "V1250630023", "V1250630024", "V1250630025", "V1250630026", "V1250630027", "V1250630028", "V1250630029", "V1250630030", "V1250630031", "V1250630032", "V1250630033", "V1250630034", "V1250630035", "V1250630036", "V1250630037", "V1250630038", "V1250630039", "V1250630040", "V1250630041", "V1250630042", "V1250630043", "V1250630044", "V1250630045", "V1250630046", "V1250630047", "V1250630048", "V1250630049", "V1250630050", "V1250630051", "V1250630052", "V1250630053", "V1250630054", "V1250630055", "V1250630056", "V1250630057", "V1250630058", "V1250630059", "V1250630060", "V1250630061", "V1250630062", "V1250630063", "V1250630064", "V1250630066", "V1250630067", "V1250630068", "V1250630069", "V1250630070", "V1250630071", "V1250630072", "V1250630073", "V1250630074", "V1250630075", "V1250630076", "V1250630077", "V1250630078", "V1250630079", "V1250630080", "V1250630081", "V1250630082", "V1250630083", "V1250630084", "V1250630085", "V1250630086", "V1250630087", "V1250630088", "V1250630089", "V1250630090", "V1250630091", "V1250630092", "V1250630093", "V1250630094", "V1250630095", "V1250630096", "V1250630097", "V1250630098", "V1250630099", "V1250630100", "V1250630101", "V1250630102", "V1250630103", "V1250630104", "V1250630105", "V1250630106", "V1250630107", "V1250630108", "V1250630109", "V1250630110", "V1250630111", "V1250630112", "V1250630113", "V1250630114", "V1250630115", "V1250630116", "V1250630117", "V1250630118"]}}, "disagreements": [{"case_number": "V1250623121", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250623122", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250623123", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 0, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250623124", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250623125", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250623126", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250623127", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250623128", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250623129", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250623130", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250623131", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250623132", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 0, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250623133", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250623134", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250623135", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250623136", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250623138", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250623139", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250623140", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250623141", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250623142", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 0, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250623143", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250623145", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250623146", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250623147", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "COMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250623148", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250623149", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250623150", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250623151", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250623152", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250623153", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250623154", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250623155", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250623156", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250623157", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250623159", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250623160", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250623161", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250623162", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250623163", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250623164", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250623165", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250623166", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250623167", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250623168", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250623169", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250623170", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250623171", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250623172", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250623173", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250623175", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250623176", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250623177", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250623178", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 0, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250623179", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250623180", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250623181", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 0, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250623182", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250623183", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 0, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250623184", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250623185", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250623186", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250623187", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250623188", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250623189", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 40, "person_present": true, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250623190", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624001", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624002", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624003", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624004", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624005", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624006", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624007", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624008", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624009", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624010", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624011", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624012", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 0, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624013", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 0, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624014", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624015", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624016", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624017", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "NONE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624018", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624019", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624020", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624021", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624022", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624025", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624026", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624027", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624028", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 0, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624029", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624030", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624031", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624032", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624033", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624034", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624035", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624036", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624037", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624038", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624039", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624040", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624041", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624042", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624043", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624044", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624045", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624047", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624048", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624049", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624050", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624051", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624052", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624053", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624054", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624055", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624056", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624057", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624058", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624059", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624060", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624061", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624062", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624063", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624064", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624065", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624066", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624067", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624068", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624070", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624072", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 0, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624073", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624074", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624075", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624076", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624077", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624079", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 0, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624080", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624081", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624082", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624084", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624085", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624086", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624087", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624088", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 0, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624089", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624090", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624091", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624092", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624093", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624094", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624095", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624096", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624097", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624098", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624099", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624100", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624101", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624102", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 0, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624103", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 50, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624104", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624105", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "NONE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624106", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 20, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624107", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "NONE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624108", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "COMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624109", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624110", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624111", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624112", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624113", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624114", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 0, "person_present": true, "ppe_compliance": "COMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624115", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624116", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624117", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624118", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624119", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 0, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624120", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 20, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624121", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624122", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 0, "person_present": true, "ppe_compliance": "COMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624123", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624124", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624125", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624126", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624127", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624128", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 0, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624129", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624130", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624131", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624132", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624133", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624134", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624135", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624136", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624137", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624138", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 0, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624139", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624140", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624141", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624142", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624143", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624144", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624145", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624146", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 0, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624147", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624148", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624149", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624150", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624151", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624152", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624153", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624154", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624155", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624157", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624158", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624159", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624161", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624162", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624163", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624164", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "COMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624165", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624166", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 30, "person_present": true, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624168", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624169", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624170", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624171", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624172", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624173", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624174", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624175", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624176", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624177", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250624178", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625001", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 0, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625002", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625003", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625004", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625005", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625006", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625007", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625008", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625009", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625010", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625011", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625012", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625013", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625014", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625015", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625016", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625017", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625018", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "NONE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625019", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625020", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625021", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625022", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "COMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625023", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625024", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625025", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625026", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625028", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625029", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625030", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 0, "person_present": true, "ppe_compliance": "COMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625031", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625032", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625033", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625034", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625035", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625036", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625037", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625038", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625039", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 20, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625040", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625041", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "NONE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625042", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625043", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625044", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625045", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625047", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625048", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625049", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625050", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625051", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625052", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625053", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625054", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625055", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625056", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625057", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625058", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625059", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625060", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625061", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625062", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625064", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625065", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625066", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625067", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625068", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625069", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625070", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625071", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625072", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625073", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625074", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "COMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625075", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625076", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625077", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625078", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625079", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625080", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625081", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625082", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625083", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625084", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 0, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625085", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625086", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625087", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625088", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625089", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625090", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625091", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625092", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625093", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625094", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625095", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625096", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 20, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625097", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625098", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 0, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625099", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625100", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625101", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 20, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625102", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625103", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625104", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625105", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625106", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625107", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625108", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625109", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625110", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625111", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625112", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625113", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625114", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625115", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625116", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625117", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625118", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625119", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625120", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625121", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 0, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625122", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625123", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625124", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625125", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 0, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625126", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625127", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625128", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625129", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625130", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625131", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625132", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625133", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625134", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625135", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625136", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625137", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "COMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625138", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625139", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625140", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625141", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625142", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625143", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625144", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625145", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625146", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "COMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625147", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625148", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625149", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "NONE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625150", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625151", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625152", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 0, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625153", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625154", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625155", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250625156", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626001", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 0, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626002", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626003", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626004", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626005", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626006", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626007", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626008", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626009", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626010", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626011", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "COMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626012", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626013", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626014", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626015", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626016", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626017", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626018", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626019", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626020", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626021", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626022", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626023", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626024", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626025", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626026", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626027", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626028", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626029", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626030", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626031", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626032", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626033", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626034", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626035", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626036", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626037", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626038", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626039", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626040", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626042", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "COMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626043", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 20, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626044", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626045", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626046", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626047", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626048", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 0, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626049", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626050", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626051", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626052", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626053", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626055", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "COMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626057", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626058", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626059", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626061", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626062", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626063", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626064", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "NONE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626065", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626066", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "COMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626067", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 0, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626068", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626070", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626071", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626072", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 0, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626074", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 0, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626075", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626076", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626077", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626078", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626079", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626080", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626081", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626082", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626083", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626084", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626085", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626086", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626087", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626088", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626089", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626090", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626091", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626092", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626093", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626094", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626095", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626096", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626097", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626099", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 20, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626100", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626101", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626102", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626103", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626104", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626105", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626106", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 20, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626107", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626108", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626109", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626110", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 20, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626111", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626112", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "COMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626113", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626116", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626117", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626118", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626119", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626120", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626121", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626122", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626123", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626124", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626125", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626126", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626127", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626130", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626131", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626132", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626133", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626134", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626136", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626137", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626138", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626139", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 20, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626140", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626141", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "NONE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626142", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626143", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626144", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626145", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 0, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626146", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 0, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626147", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626148", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626149", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626150", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 20, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626151", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626152", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626153", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626154", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626155", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626157", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626158", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626159", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626160", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "COMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626161", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626162", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626163", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626164", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626165", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626166", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626167", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626168", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626169", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626170", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "NONE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626171", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 20, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626172", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626173", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250626174", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627001", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627002", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 0, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627003", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627004", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 30, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627005", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627006", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627007", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627008", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 0, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627009", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 0, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627010", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627011", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627012", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627013", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627014", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627015", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627016", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627017", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627018", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 0, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627019", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627020", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627021", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 0, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627022", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627023", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627024", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627025", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627026", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627028", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627029", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627030", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627032", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627033", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627034", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627035", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627036", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627037", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627038", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627039", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627040", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627041", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627042", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627043", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627045", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627046", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627047", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627048", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627049", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627050", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627051", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627052", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627053", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627054", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 20, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627055", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627056", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 30, "person_present": true, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627057", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627058", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 0, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627059", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627060", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627061", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627062", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627063", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627064", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627065", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627066", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627067", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627068", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627069", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627070", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627071", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627072", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627073", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627074", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627075", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627076", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627077", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627078", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627079", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 0, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627080", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627081", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627082", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627083", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627084", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627085", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 0, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627086", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627087", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627088", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627089", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627090", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627091", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627092", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627093", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627094", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627095", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627096", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627097", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627098", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627099", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627100", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627101", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627102", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 0, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627103", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627104", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627105", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627106", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627107", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627108", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 0, "person_present": true, "ppe_compliance": "COMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627109", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627110", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 0, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627111", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627112", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627113", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627114", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627115", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627116", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627117", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627118", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627119", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627120", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 20, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627121", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627122", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627123", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627124", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627125", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627126", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 20, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627127", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "NONE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627128", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627129", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627130", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627131", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627132", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627133", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 20, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627134", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627135", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627136", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627137", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 0, "person_present": true, "ppe_compliance": "COMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627138", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627139", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627140", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627141", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627142", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627143", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627144", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627145", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 20, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627146", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627147", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627148", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627149", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627150", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627151", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 0, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627152", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627153", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627154", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627155", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627156", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "COMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627157", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "COMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627158", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627159", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627160", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627161", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627162", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627163", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 0, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627164", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627165", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627166", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627167", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627168", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627169", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627170", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627171", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 0, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627172", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627173", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627174", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627175", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627176", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627177", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627178", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627180", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627181", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627182", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "NONE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627183", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627184", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627185", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627186", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627187", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 0, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627188", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627189", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627190", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627191", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627192", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627193", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627195", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 0, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627196", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 0, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627197", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 0, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627198", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627199", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 0, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627200", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627201", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627202", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627203", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627205", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627206", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627207", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 20, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627209", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 0, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627210", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 20, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627211", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627212", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627213", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627214", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627215", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627216", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627217", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 20, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627218", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627219", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627220", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627221", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627222", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627223", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627224", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627225", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627226", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627227", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627228", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627229", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627230", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 30, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627231", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627232", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627233", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627234", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627235", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627236", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 20, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627237", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627238", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627239", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627240", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627241", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627242", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627243", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627244", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627245", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250627246", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628001", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628002", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628003", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628004", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628005", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628006", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628007", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628008", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628009", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 20, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628010", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628011", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628012", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628013", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628014", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628015", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 20, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628016", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628017", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628018", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628019", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628020", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628021", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628022", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628023", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628024", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628025", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628026", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628027", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628028", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628029", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628031", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628032", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628033", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628034", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628035", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628036", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 20, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628037", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628038", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 0, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628039", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628040", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628041", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 0, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628043", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628044", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628045", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628046", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628047", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628048", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628049", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628050", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628051", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 0, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628052", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 0, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628053", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628054", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628055", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628056", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628057", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628058", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628059", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 20, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628060", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628061", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628062", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628063", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628064", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628065", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628066", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "NONE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628067", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628068", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628069", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628070", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628071", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 20, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628072", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "COMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628073", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628074", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628075", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628076", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628077", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628078", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 20, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628079", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628080", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 30, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628081", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628082", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628083", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628084", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628085", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628086", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628087", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628088", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628089", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628090", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 0, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628091", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628092", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 0, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628093", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628094", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628095", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628096", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628097", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628098", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628099", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628100", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628102", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 0, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628103", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628104", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628105", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628106", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628107", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 0, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628108", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628109", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 0, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628110", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628111", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628113", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628114", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628115", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 0, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628116", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628117", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628118", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628121", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628122", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628123", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628124", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628125", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628126", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 20, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628127", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628128", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 0, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628129", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 20, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628130", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628131", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 20, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628132", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628133", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628134", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628135", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628136", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628137", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 0, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628138", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628139", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628140", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628141", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628142", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628143", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628144", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250628145", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "COMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629001", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629002", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629003", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629004", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629005", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629006", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629007", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629008", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629009", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629010", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629011", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629012", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629013", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629014", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629015", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629016", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629017", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629018", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629019", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629020", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629021", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629022", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629023", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629024", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629025", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629026", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629027", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629028", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 30, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629029", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 20, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629030", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "COMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629031", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 20, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629032", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629033", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629034", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629035", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 20, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629036", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629037", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629038", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629039", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629040", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 0, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629041", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629042", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629043", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629044", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629045", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629046", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629047", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629048", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629049", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629050", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629052", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629053", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629054", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629055", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629056", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629057", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629058", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629059", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629060", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629061", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629062", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629063", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 20, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629064", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629065", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629066", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629067", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "NONE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629068", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629069", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629070", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629071", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629072", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629073", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629074", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629075", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629076", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 0, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629077", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629078", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 20, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629079", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629080", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629081", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 20, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629082", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 20, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629083", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629085", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629086", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 20, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629087", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 20, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629088", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629089", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629091", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629092", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629093", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629094", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629095", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 0, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629096", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629097", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629098", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629099", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629100", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629101", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629103", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629104", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629105", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629106", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629107", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629108", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629109", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629110", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629111", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 0, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629112", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629113", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 0, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629114", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629115", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 0, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629116", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629117", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629118", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 0, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629120", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629121", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 0, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629122", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629123", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 0, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629124", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629125", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629126", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629127", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629128", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629129", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629130", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629131", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629132", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629133", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629134", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629135", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629136", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629137", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629138", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629139", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629140", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629141", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629142", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629143", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629144", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "COMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629145", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629146", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629147", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629148", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629149", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629150", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629151", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629152", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 20, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629153", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629154", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629155", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629156", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "COMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629157", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629158", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629159", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629160", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "NONE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629161", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250629162", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630001", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630002", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630003", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630004", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630005", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 20, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630006", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630007", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630008", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630009", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630010", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630011", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630013", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630014", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630015", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630016", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630017", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630018", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 0, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630020", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630021", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630022", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630023", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630024", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630025", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630026", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 20, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630027", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "COMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630028", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630029", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630030", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630031", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630032", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630033", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630034", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 0, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630035", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630036", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630037", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630038", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630039", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630040", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630041", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 0, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630042", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630043", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630044", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630045", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630046", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 0, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630047", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630048", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630049", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630050", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 20, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630051", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630052", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 0, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630053", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "COMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630054", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630055", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630056", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630057", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630058", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630059", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630060", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630061", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630062", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630063", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630064", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630066", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630067", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630068", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630069", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630070", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630071", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 0, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630072", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630073", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630074", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 0, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630075", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "COMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630076", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630077", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630078", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630079", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630080", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630081", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630082", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630083", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630084", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630085", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "COMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630086", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630087", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 0, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630088", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630089", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630090", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630091", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630092", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630093", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "COMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630094", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630095", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630096", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630097", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630098", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630099", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630100", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630101", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630102", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630103", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630104", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630105", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630106", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630107", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630108", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630109", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630110", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630111", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630112", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630113", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630114", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630115", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": true, "ppe_compliance": "INCOMPLETE", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630116", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630117", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 10, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}, {"case_number": "V1250630118", "ground_truth": "FALSE_POSITIVE", "vlm_prediction": "TRUE_POSITIVE", "is_critical": false, "metrics": {"fp_likelihood": 5, "person_present": false, "ppe_compliance": "NA", "violation_confidence": null, "violation_type": null}}]}