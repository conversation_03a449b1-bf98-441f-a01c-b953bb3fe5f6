#!/usr/bin/env python3
"""
Robust test runner that can resume and handle failures
"""

import json
import base64
import requests
import os
import time
from datetime import datetime
import signal
import sys

class RobustTestRunner:
    def __init__(self):
        self.vlm_url = "http://100.106.127.35:9500/v1/chat/completions"
        self.vlm_model = "VLM-38B-AWQ"
        self.session = requests.Session()
        self.session.timeout = 30
        
        # Progress tracking
        self.progress_file = 'robust_test_progress.json'
        self.results_file = 'robust_test_results.json'
        self.completed_cases = set()
        self.results = []
        
        # Load previous progress if exists
        self.load_progress()
        
        # Handle interruptions gracefully
        signal.signal(signal.SIGINT, self.handle_interrupt)
        
    def load_progress(self):
        """Load previous progress if exists"""
        if os.path.exists(self.results_file):
            with open(self.results_file, 'r') as f:
                data = json.load(f)
                self.results = data.get('results', [])
                self.completed_cases = {r['case_number'] for r in self.results}
                print(f"Resuming from {len(self.completed_cases)} completed cases")
    
    def save_progress(self):
        """Save current progress"""
        with open(self.results_file, 'w') as f:
            json.dump({
                'timestamp': datetime.now().isoformat(),
                'results': self.results
            }, f, indent=2)
        
        # Also save summary
        if self.results:
            correct = sum(r['correct'] for r in self.results)
            accuracy = correct / len(self.results) * 100
            
            with open(self.progress_file, 'w') as f:
                json.dump({
                    'timestamp': datetime.now().isoformat(),
                    'total_cases': len(self.results),
                    'correct': correct,
                    'accuracy': accuracy,
                    'last_case': self.results[-1]['case_number']
                }, f, indent=2)
    
    def handle_interrupt(self, signum, frame):
        """Handle Ctrl+C gracefully"""
        print("\n\nInterrupted! Saving progress...")
        self.save_progress()
        self.print_summary()
        sys.exit(0)
    
    def encode_image(self, image_path):
        """Encode image with error handling"""
        try:
            if os.path.exists(image_path):
                with open(image_path, 'rb') as f:
                    return base64.b64encode(f.read()).decode('utf-8')
        except:
            pass
        return None
    
    def test_simple_equipment(self, case):
        """Test simple equipment detection"""
        prompt = "Is this image primarily showing industrial equipment (crane, vessel, truck, or spreader) with no people visible? Answer only YES or NO."
        
        source_b64 = self.encode_image(case['source_image'])
        cropped_b64 = self.encode_image(case['cropped_image'])
        
        if not source_b64 or not cropped_b64:
            return None
        
        payload = {
            "model": self.vlm_model,
            "messages": [{
                "role": "user",
                "content": [
                    {"type": "text", "text": prompt},
                    {"type": "text", "text": "\n\nSOURCE IMAGE:"},
                    {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{source_b64}"}},
                    {"type": "text", "text": "\n\nCROPPED IMAGE:"},
                    {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{cropped_b64}"}}
                ]
            }],
            "temperature": 0.1,
            "max_tokens": 50
        }
        
        # Retry logic with exponential backoff
        for attempt in range(3):
            try:
                response = self.session.post(self.vlm_url, json=payload, timeout=30)
                
                if response.status_code == 200:
                    vlm_response = response.json()['choices'][0]['message']['content']
                    is_equipment = "YES" in vlm_response.upper()[:10]
                    predicted_fp = is_equipment
                    actual_fp = case['is_false_positive']
                    
                    return {
                        'case_number': case['case_number'],
                        'actual_fp': actual_fp,
                        'predicted_fp': predicted_fp,
                        'correct': predicted_fp == actual_fp,
                        'response': vlm_response.strip(),
                        'approach': 'simple_equipment'
                    }
                
                elif response.status_code == 429:
                    wait_time = min(60, 2 ** attempt)
                    print(f"Rate limit, waiting {wait_time}s...")
                    time.sleep(wait_time)
                
                else:
                    print(f"API error {response.status_code}: {response.text[:100]}")
                    
            except requests.exceptions.Timeout:
                print(f"Timeout attempt {attempt+1}/3")
                
            except Exception as e:
                print(f"Error attempt {attempt+1}/3: {str(e)[:100]}")
            
            if attempt < 2:
                time.sleep(2 ** attempt)
        
        return None
    
    def print_summary(self):
        """Print current summary"""
        if not self.results:
            return
        
        total = len(self.results)
        correct = sum(r['correct'] for r in self.results)
        
        # Separate by type
        actual_fps = [r for r in self.results if r['actual_fp']]
        actual_valid = [r for r in self.results if not r['actual_fp']]
        
        if actual_fps:
            fp_detected = sum(r['predicted_fp'] for r in actual_fps)
            fp_rate = fp_detected / len(actual_fps) * 100
        else:
            fp_rate = 0
        
        if actual_valid:
            valid_protected = sum(not r['predicted_fp'] for r in actual_valid)
            valid_rate = valid_protected / len(actual_valid) * 100
        else:
            valid_rate = 0
        
        print(f"\n{'='*60}")
        print(f"CURRENT RESULTS SUMMARY")
        print(f"{'='*60}")
        print(f"Total processed: {total}")
        print(f"Overall Accuracy: {correct/total*100:.1f}%")
        print(f"FP Detection: {fp_rate:.1f}%")
        print(f"Valid Protection: {valid_rate:.1f}%")
    
    def run_comprehensive_test(self):
        """Run test on all cases"""
        print("="*80)
        print("ROBUST COMPREHENSIVE TEST")
        print("="*80)
        
        # Load all cases
        with open('valo_batch_round3_complete.json', 'r') as f:
            all_cases = json.load(f)['results']
        
        # Filter out completed cases
        remaining_cases = [c for c in all_cases if c['case_number'] not in self.completed_cases]
        
        print(f"Total cases: {len(all_cases)}")
        print(f"Already completed: {len(self.completed_cases)}")
        print(f"Remaining: {len(remaining_cases)}")
        
        if not remaining_cases:
            print("All cases already completed!")
            self.print_summary()
            return
        
        start_time = time.time()
        
        for i, case in enumerate(remaining_cases):
            # Progress update
            if i % 10 == 0:
                elapsed = time.time() - start_time
                total_processed = len(self.completed_cases) + i
                
                if i > 0:
                    rate = i / elapsed
                    eta = (len(remaining_cases) - i) / rate / 60
                else:
                    eta = 0
                
                print(f"\nProgress: {total_processed}/{len(all_cases)} "
                      f"({total_processed/len(all_cases)*100:.1f}%) - ETA: {eta:.1f} min")
                
                self.save_progress()
                
                # Show running accuracy
                if len(self.results) > 0:
                    acc = sum(r['correct'] for r in self.results) / len(self.results) * 100
                    print(f"Running accuracy: {acc:.1f}%")
            
            # Test case
            result = self.test_simple_equipment(case)
            
            if result:
                self.results.append(result)
                self.completed_cases.add(case['case_number'])
                
                # Show sample results
                if i < 5 or i % 100 == 0:
                    status = "✓" if result['correct'] else "✗"
                    print(f"{status} {result['case_number']}: {result['response']}")
            
            # Rate limiting
            time.sleep(0.5)
        
        # Final save and summary
        self.save_progress()
        
        print("\n" + "="*80)
        print("FINAL RESULTS")
        print("="*80)
        self.print_summary()
        
        # Save detailed report
        self.generate_final_report()
    
    def generate_final_report(self):
        """Generate comprehensive final report"""
        if not self.results:
            return
        
        total = len(self.results)
        correct = sum(r['correct'] for r in self.results)
        
        # Detailed metrics
        actual_fps = [r for r in self.results if r['actual_fp']]
        actual_valid = [r for r in self.results if not r['actual_fp']]
        
        fp_detected = sum(r['predicted_fp'] for r in actual_fps) if actual_fps else 0
        valid_protected = sum(not r['predicted_fp'] for r in actual_valid) if actual_valid else 0
        
        report = {
            'test_name': 'Simple Equipment Detection (Robust)',
            'timestamp': datetime.now().isoformat(),
            'total_cases': total,
            'correct': correct,
            'accuracy': correct/total*100,
            'false_positive_metrics': {
                'total_fp_cases': len(actual_fps),
                'fp_detected': fp_detected,
                'fp_detection_rate': fp_detected/len(actual_fps)*100 if actual_fps else 0
            },
            'valid_violation_metrics': {
                'total_valid_cases': len(actual_valid),
                'valid_protected': valid_protected,
                'valid_protection_rate': valid_protected/len(actual_valid)*100 if actual_valid else 0
            },
            'sample_results': self.results[:50],
            'conclusion': self.generate_conclusion()
        }
        
        with open('simple_equipment_comprehensive_report.json', 'w') as f:
            json.dump(report, f, indent=2)
        
        print(f"\nComprehensive report saved to: simple_equipment_comprehensive_report.json")
    
    def generate_conclusion(self):
        """Generate conclusion based on results"""
        if not self.results:
            return "No results to analyze"
        
        acc = sum(r['correct'] for r in self.results) / len(self.results) * 100
        
        if acc > 70:
            return "Simple equipment detection is highly effective and ready for production."
        elif acc > 60:
            return "Simple approach shows promise but needs refinement."
        elif acc > 50:
            return "Moderate performance - consider hybrid approach."
        else:
            return "Simple approach underperforms - complex prompts or different strategy needed."

if __name__ == "__main__":
    runner = RobustTestRunner()
    try:
        runner.run_comprehensive_test()
    except Exception as e:
        print(f"\nUnexpected error: {e}")
        runner.save_progress()
        runner.print_summary()