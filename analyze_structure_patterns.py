#!/usr/bin/env python3
"""
Analyze structure false positives to create visual descriptions
Push examples to VLM to learn what structures look like
"""

import json
import base64
import requests
import os
from collections import defaultdict
import pandas as pd

VLM_API_URL = "http://**************:9500/v1/chat/completions"
VLM_MODEL = "VLM-38B-AWQ"

class StructurePatternAnalyzer:
    def __init__(self):
        self.session = requests.Session()
        self.structure_types = {
            'CRANE STRUCTURE': [],
            'VESSEL STRUCTURE': [],
            'PM STRUCTURE': [],
            'SPREADER': [],
            'EQUIPMENT': []
        }
        self.visual_patterns = defaultdict(list)
        
    def encode_image(self, image_path):
        """Encode image to base64"""
        try:
            if os.path.exists(image_path):
                with open(image_path, 'rb') as f:
                    return base64.b64encode(f.read()).decode('utf-8')
        except:
            pass
        return None
    
    def analyze_structure_with_vlm(self, image_b64, structure_type):
        """Ask VLM to describe what the structure looks like"""
        
        prompt = f"""You are analyzing an image that was incorrectly identified as a person but is actually a {structure_type}.

Please describe in detail:
1. What visual features make this look like it could be mistaken for a person?
2. What are the key characteristics that identify this as a {structure_type}?
3. List specific visual patterns (shapes, angles, colors, textures) that distinguish this from a human.

Be specific about:
- Geometric shapes present
- Metal/material appearance
- Angles and lines
- Any repetitive patterns
- Color characteristics
- Size/scale indicators

Provide a concise description that could help distinguish this structure from a person."""

        payload = {
            "model": VLM_MODEL,
            "messages": [{
                "role": "user",
                "content": [
                    {"type": "text", "text": prompt},
                    {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{image_b64}"}}
                ]
            }],
            "temperature": 0.3,
            "max_tokens": 300
        }
        
        try:
            response = self.session.post(VLM_API_URL, json=payload, timeout=30)
            if response.status_code == 200:
                result = response.json()
                return result['choices'][0]['message']['content'].strip()
        except:
            pass
        return None
    
    def collect_structure_examples(self):
        """Collect examples of each structure type from the dataset"""
        
        print("COLLECTING STRUCTURE FALSE POSITIVES")
        print("="*60)
        
        # Load the CSV with remarks
        df = pd.read_csv('./ai_farm_images_fixed_250703/psa_valo_violation_report_without_images_csv_2025-07-03_14-14-01.CSV')
        
        # Filter false positives only
        fp_df = df[df['Alert Status'] == 'Invalid']
        
        # Load case data with image paths
        with open('valo_batch_round3_complete.json', 'r') as f:
            case_data = json.load(f)
        
        # Create case lookup
        case_lookup = {case['case_number']: case for case in case_data['results']}
        
        # Structure patterns to search for
        patterns = {
            'CRANE STRUCTURE': ['CRANE STRUCTURE', 'CRANE'],
            'VESSEL STRUCTURE': ['VESSEL STRUCTURE', 'VESSEL'],
            'PM STRUCTURE': ['PM STRUCTURE', 'PM'],
            'SPREADER': ['SPREADER', 'FLIPPER'],
            'EQUIPMENT': ['EQUIPMENT', 'MACHINE', 'BARGE']
        }
        
        # Collect examples
        for struct_type, search_terms in patterns.items():
            print(f"\nSearching for {struct_type} examples...")
            examples = []
            
            for _, row in fp_df.iterrows():
                remark = str(row['Remarks']).upper()
                case_num = row['Case Int. ID']
                
                # Check if this remark matches our pattern
                if any(term in remark for term in search_terms):
                    if case_num in case_lookup:
                        case = case_lookup[case_num]
                        examples.append({
                            'case_number': case_num,
                            'remark': row['Remarks'],
                            'cropped_image': case['cropped_image'],
                            'source_image': case['source_image']
                        })
                        
                        if len(examples) >= 5:  # Get 5 examples of each
                            break
            
            self.structure_types[struct_type] = examples
            print(f"Found {len(examples)} examples")
    
    def analyze_all_structures(self):
        """Analyze all structure examples with VLM"""
        
        print("\n\nANALYZING STRUCTURES WITH VLM")
        print("="*60)
        
        structure_descriptions = {}
        
        for struct_type, examples in self.structure_types.items():
            if not examples:
                continue
                
            print(f"\n\nAnalyzing {struct_type}:")
            print("-"*40)
            
            descriptions = []
            
            for i, example in enumerate(examples[:3]):  # Analyze first 3 of each type
                print(f"\nExample {i+1}: {example['case_number']}")
                print(f"Remark: {example['remark']}")
                
                # Encode cropped image
                image_b64 = self.encode_image(example['cropped_image'])
                if not image_b64:
                    continue
                
                # Get VLM description
                description = self.analyze_structure_with_vlm(image_b64, struct_type)
                if description:
                    print(f"\nVLM Analysis:")
                    print(description)
                    descriptions.append(description)
            
            # Synthesize common patterns
            if descriptions:
                structure_descriptions[struct_type] = self.synthesize_patterns(descriptions, struct_type)
    
        return structure_descriptions
    
    def synthesize_patterns(self, descriptions, struct_type):
        """Synthesize common patterns from multiple descriptions"""
        
        # Extract common themes
        common_patterns = []
        
        # Look for repeated phrases/patterns
        all_text = ' '.join(descriptions).lower()
        
        keywords = {
            'geometric': ['angular', 'rectangular', 'straight lines', 'geometric', 'rigid'],
            'metal': ['metal', 'steel', 'metallic', 'industrial'],
            'color': ['yellow', 'orange', 'gray', 'dark', 'painted'],
            'pattern': ['repetitive', 'uniform', 'grid', 'framework'],
            'scale': ['large', 'massive', 'industrial scale', 'heavy']
        }
        
        pattern_summary = f"\n{struct_type} VISUAL CHARACTERISTICS:\n"
        
        for category, terms in keywords.items():
            found = [term for term in terms if term in all_text]
            if found:
                pattern_summary += f"- {category.title()}: {', '.join(found)}\n"
        
        return pattern_summary
    
    def create_enhanced_prompt(self, structure_descriptions):
        """Create enhanced prompt with visual descriptions"""
        
        prompt = """INTELLIGENT SAFETY ANALYSIS WITH STRUCTURE RECOGNITION

DUAL IMAGE ANALYSIS REQUIRED:
- SOURCE IMAGE: Full context view
- CROPPED IMAGE: Specific area (must contain PERSON for valid violation)

STEP 1 - ADVANCED ENTITY DETECTION:
Examine the cropped image. Identify if this is:

A) PERSON - Human characteristics:
   - Organic body shape with head, torso, limbs
   - Clothing/PPE visible (vests, helmets, coveralls)
   - Natural human proportions and posture
   - Skin tones or face visible

B) STRUCTURE - Industrial equipment characteristics:

"""
        
        # Add specific structure descriptions
        for struct_type, description in structure_descriptions.items():
            prompt += description + "\n"
        
        prompt += """
C) UNCLEAR - Cannot confidently determine

KEY DIFFERENTIATION RULES:
- Structures have rigid, geometric patterns vs human organic shapes
- Metal/industrial materials vs clothing/fabric
- Repetitive/uniform patterns vs human irregularity
- Fixed/static positioning vs human movement capability

If confidence <80% that it's a PERSON → FALSE POSITIVE

STEP 2 - PPE COMPLIANCE (only if PERSON with >80% confidence):
[Rest of original prompt...]

FINAL ANSWER: Is this a FALSE POSITIVE? YES/NO
Entity Detected: [PERSON/STRUCTURE/UNCLEAR]
Structure Type (if applicable): [CRANE/VESSEL/PM/SPREADER/OTHER]
Confidence: [0-100]%
Reason: [Brief explanation]"""

        return prompt
    
    def save_enhanced_prompt(self, prompt):
        """Save the enhanced prompt"""
        with open('intelligent_prompt_with_structures.txt', 'w') as f:
            f.write(prompt)
        
        print("\n\nENHANCED PROMPT SAVED TO: intelligent_prompt_with_structures.txt")

def main():
    analyzer = StructurePatternAnalyzer()
    
    # Collect structure examples
    analyzer.collect_structure_examples()
    
    # Analyze with VLM
    descriptions = analyzer.analyze_all_structures()
    
    # Create enhanced prompt
    if descriptions:
        enhanced_prompt = analyzer.create_enhanced_prompt(descriptions)
        analyzer.save_enhanced_prompt(enhanced_prompt)
        
        print("\n\nSTRUCTURE ANALYSIS COMPLETE")
        print("Enhanced prompt includes visual descriptions of:")
        for struct_type in descriptions:
            print(f"- {struct_type}")

if __name__ == "__main__":
    main()