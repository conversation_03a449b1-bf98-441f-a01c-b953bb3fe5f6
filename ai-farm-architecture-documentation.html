<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="AI-FARM Architecture Documentation - Comprehensive technical documentation for the AI-FARM false positive reduction system">
    <meta name="keywords" content="AI-FARM, VLM, false positive reduction, PSA VALO, architecture documentation">
    <meta name="author" content="AI-FARM Development Team">
    <title>AI-FARM Architecture Documentation</title>
    
    <!-- Mermaid.js for diagrams -->
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #7c3aed;
            --accent-color: #10b981;
            --bg-gradient-start: #1e3a8a;
            --bg-gradient-end: #312e81;
            --sidebar-bg: #1f2937;
            --content-bg: #ffffff;
            --text-primary: #111827;
            --text-secondary: #6b7280;
            --border-color: #e5e7eb;
            --code-bg: #f3f4f6;
            --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: var(--text-primary);
            background: linear-gradient(135deg, var(--bg-gradient-start) 0%, var(--bg-gradient-end) 100%);
            min-height: 100vh;
        }

        /* Layout */
        .container {
            display: flex;
            min-height: 100vh;
        }

        /* Sidebar Navigation */
        .sidebar {
            width: 300px;
            background: var(--sidebar-bg);
            padding: 2rem 1rem;
            overflow-y: auto;
            position: fixed;
            height: 100vh;
            transition: transform 0.3s ease;
            z-index: 1000;
        }

        .sidebar.collapsed {
            transform: translateX(-100%);
        }

        .sidebar-header {
            color: white;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .sidebar-header h2 {
            font-size: 1.5rem;
            font-weight: 700;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .nav-section {
            margin-bottom: 1.5rem;
        }

        .nav-section-title {
            color: rgba(255, 255, 255, 0.6);
            font-size: 0.75rem;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            margin-bottom: 0.5rem;
        }

        .nav-link {
            display: block;
            color: rgba(255, 255, 255, 0.9);
            text-decoration: none;
            padding: 0.5rem 1rem;
            border-radius: 0.375rem;
            transition: all 0.2s;
            margin-bottom: 0.25rem;
            position: relative;
        }

        .nav-link:hover {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            transform: translateX(4px);
        }

        .nav-link.active {
            background: var(--primary-color);
            color: white;
        }

        .nav-link.active::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 3px;
            background: var(--accent-color);
            border-radius: 0 3px 3px 0;
        }

        .nav-subsection {
            margin-left: 1rem;
            margin-top: 0.25rem;
        }

        .nav-subsection .nav-link {
            font-size: 0.875rem;
            padding: 0.375rem 1rem;
        }

        /* Main Content */
        .main-content {
            flex: 1;
            margin-left: 300px;
            padding: 2rem;
            background: var(--content-bg);
            min-height: 100vh;
            margin: 1rem 1rem 1rem 316px;
            border-radius: 1rem;
            box-shadow: var(--shadow);
        }

        .main-content.expanded {
            margin-left: 1rem;
        }

        /* Mobile Toggle */
        .mobile-toggle {
            display: none;
            position: fixed;
            top: 1rem;
            left: 1rem;
            z-index: 1001;
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 0.75rem;
            border-radius: 0.5rem;
            cursor: pointer;
            box-shadow: var(--shadow);
        }

        /* Content Styles */
        .section {
            margin-bottom: 4rem;
            scroll-margin-top: 2rem;
        }

        .section-header {
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid var(--border-color);
        }

        h1 {
            font-size: 2.5rem;
            font-weight: 800;
            color: var(--text-primary);
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 0.5rem;
        }

        h2 {
            font-size: 2rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 1rem;
        }

        h3 {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.75rem;
            margin-top: 2rem;
        }

        h4 {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
            margin-top: 1.5rem;
        }

        p {
            margin-bottom: 1rem;
            color: var(--text-secondary);
            line-height: 1.8;
        }

        /* Code Blocks */
        pre {
            background: var(--code-bg);
            padding: 1rem;
            border-radius: 0.5rem;
            overflow-x: auto;
            margin-bottom: 1rem;
            border: 1px solid var(--border-color);
        }

        code {
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 0.875rem;
            background: var(--code-bg);
            padding: 0.125rem 0.375rem;
            border-radius: 0.25rem;
        }

        pre code {
            background: none;
            padding: 0;
        }

        /* Lists */
        ul, ol {
            margin-bottom: 1rem;
            margin-left: 2rem;
            color: var(--text-secondary);
        }

        li {
            margin-bottom: 0.5rem;
        }

        /* Tables */
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 1rem;
            background: white;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border-radius: 0.5rem;
            overflow: hidden;
        }

        th {
            background: var(--code-bg);
            font-weight: 600;
            text-align: left;
            padding: 0.75rem 1rem;
            border-bottom: 2px solid var(--border-color);
        }

        td {
            padding: 0.75rem 1rem;
            border-bottom: 1px solid var(--border-color);
        }

        tr:last-child td {
            border-bottom: none;
        }

        /* Mermaid Diagrams */
        .mermaid-container {
            background: white;
            padding: 2rem;
            border-radius: 0.5rem;
            margin: 1rem 0 2rem 0;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            overflow-x: auto;
        }

        .mermaid {
            text-align: center;
        }

        /* Cards and Highlights */
        .highlight-box {
            background: linear-gradient(135deg, rgba(37, 99, 235, 0.1), rgba(124, 58, 237, 0.1));
            border-left: 4px solid var(--primary-color);
            padding: 1rem 1.5rem;
            border-radius: 0.5rem;
            margin: 1rem 0;
        }

        .metric-card {
            background: white;
            padding: 1.5rem;
            border-radius: 0.5rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            margin-bottom: 1rem;
            border-top: 3px solid var(--accent-color);
        }

        .metric-value {
            font-size: 2rem;
            font-weight: 700;
            color: var(--primary-color);
        }

        .metric-label {
            color: var(--text-secondary);
            font-size: 0.875rem;
        }

        /* Back to Top Button */
        .back-to-top {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            background: var(--primary-color);
            color: white;
            padding: 0.75rem 1rem;
            border-radius: 0.5rem;
            text-decoration: none;
            opacity: 0;
            transition: opacity 0.3s;
            box-shadow: var(--shadow);
        }

        .back-to-top.visible {
            opacity: 1;
        }

        /* Progress Indicator */
        .progress-indicator {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: rgba(255, 255, 255, 0.2);
            z-index: 999;
        }

        .progress-bar {
            height: 100%;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
            width: 0%;
            transition: width 0.3s;
        }

        /* Search Box */
        .search-container {
            margin-bottom: 1rem;
            position: relative;
        }

        .search-input {
            width: 100%;
            padding: 0.5rem 2.5rem 0.5rem 1rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
            background: rgba(255, 255, 255, 0.1);
            border-radius: 0.375rem;
            color: white;
            font-size: 0.875rem;
        }

        .search-input::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }

        .search-icon {
            position: absolute;
            right: 0.75rem;
            top: 50%;
            transform: translateY(-50%);
            color: rgba(255, 255, 255, 0.5);
        }

        /* Print Styles */
        @media print {
            .sidebar, .mobile-toggle, .back-to-top, .progress-indicator {
                display: none !important;
            }
            
            .main-content {
                margin: 0 !important;
                padding: 1rem !important;
            }
            
            body {
                background: white !important;
            }
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .sidebar {
                width: 280px;
            }
            
            .main-content {
                margin-left: 0;
                margin: 0.5rem;
                padding: 1rem;
            }
            
            .mobile-toggle {
                display: block;
            }
            
            h1 {
                font-size: 2rem;
            }
            
            h2 {
                font-size: 1.5rem;
            }
            
            .metric-card {
                padding: 1rem;
            }
            
            .metric-value {
                font-size: 1.5rem;
            }
        }

        /* Loading Animation */
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(37, 99, 235, 0.3);
            border-radius: 50%;
            border-top-color: var(--primary-color);
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* Collapsible Sections */
        .collapsible {
            cursor: pointer;
            user-select: none;
        }

        .collapsible::before {
            content: '▼';
            display: inline-block;
            margin-right: 0.5rem;
            transition: transform 0.3s;
        }

        .collapsible.collapsed::before {
            transform: rotate(-90deg);
        }

        .collapsible-content {
            overflow: hidden;
            transition: max-height 0.3s ease;
        }

        .collapsible-content.collapsed {
            max-height: 0;
        }
    </style>
</head>
<body>
    <!-- Progress Indicator -->
    <div class="progress-indicator">
        <div class="progress-bar" id="progressBar"></div>
    </div>

    <!-- Mobile Toggle Button -->
    <button class="mobile-toggle" id="mobileToggle" onclick="toggleSidebar()">
        ☰
    </button>

    <div class="container">
        <!-- Sidebar Navigation -->
        <nav class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <h2>AI-FARM Documentation</h2>
            </div>

            <!-- Search Box -->
            <div class="search-container">
                <input type="text" class="search-input" id="searchInput" placeholder="Search documentation..." onkeyup="searchContent()">
                <span class="search-icon">🔍</span>
            </div>

            <!-- Navigation Sections -->
            <div class="nav-section">
                <div class="nav-section-title">Overview</div>
                <a href="#project-overview" class="nav-link">Project Knowledge Creation</a>
                <a href="#vlm-implementation" class="nav-link">VLM Implementation</a>
                <a href="#vlm-filter" class="nav-link">VLM False Positive Filter</a>
                <a href="#development-spec" class="nav-link">Development Specification</a>
            </div>

            <div class="nav-section">
                <div class="nav-section-title">Architecture</div>
                <a href="#architecture-diagrams" class="nav-link">Architecture Diagrams</a>
                <div class="nav-subsection">
                    <a href="#high-level-architecture" class="nav-link">System Architecture</a>
                    <a href="#backend-flow" class="nav-link">Backend Processing</a>
                    <a href="#frontend-architecture" class="nav-link">Frontend Components</a>
                    <a href="#data-flow" class="nav-link">Data Flow</a>
                    <a href="#api-interaction" class="nav-link">API Interactions</a>
                </div>
                <a href="#architecture-summary" class="nav-link">Architecture Summary</a>
                <a href="#validation-report" class="nav-link">Validation Report</a>
            </div>
        </nav>

        <!-- Main Content Area -->
        <main class="main-content" id="mainContent">
            <!-- Header Section -->
            <div class="section-header">
                <h1>AI-FARM Architecture Documentation</h1>
                <p>Comprehensive technical documentation for the AI-FARM false positive reduction system</p>
            </div>

            <!-- Project Overview Section -->
            <section id="project-overview" class="section">
                <h2>AI-FARM Project Knowledge Creation Session Documentation</h2>
                
                <div class="highlight-box">
                    <h3>Executive Summary</h3>
                    <p>AI-FARM is a proof-of-concept system designed to solve the false positive crisis in safety violation detection systems. The system uses Vision Language Models (VLMs) to intelligently filter safety alerts, reducing false positives by 70% and saving significant manual review time.</p>
                </div>

                <h3>Project Mission</h3>
                <p>To demonstrate how AI can dramatically reduce false positive alerts in safety monitoring systems, allowing security teams to focus on genuine violations while saving substantial costs.</p>

                <h3>Business Case</h3>
                <div class="metric-card">
                    <div class="metric-value">97%</div>
                    <div class="metric-label">Current False Positive Rate</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">70%</div>
                    <div class="metric-label">Target Reduction in False Positives</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">$300K+</div>
                    <div class="metric-label">Annual Savings Potential</div>
                </div>

                <h3>Implementation Phases</h3>
                <ol>
                    <li><strong>Phase 1: Core Infrastructure</strong>
                        <ul>
                            <li>Backend API with FastAPI</li>
                            <li>Database schema and models</li>
                            <li>VLM integration service</li>
                        </ul>
                    </li>
                    <li><strong>Phase 2: Processing Engine</strong>
                        <ul>
                            <li>Batch processing system</li>
                            <li>Auto-learning algorithms</li>
                            <li>Result aggregation</li>
                        </ul>
                    </li>
                    <li><strong>Phase 3: User Interface</strong>
                        <ul>
                            <li>React dashboard</li>
                            <li>Real-time monitoring</li>
                            <li>ROI calculator</li>
                        </ul>
                    </li>
                </ol>

                <h3>Key Artifacts Created</h3>
                <ul>
                    <li>Complete backend API with 20+ endpoints</li>
                    <li>VLM integration with OpenAI-compatible APIs</li>
                    <li>Auto-learning engine with pattern detection</li>
                    <li>Two frontend applications (standard + surveillance)</li>
                    <li>Comprehensive test suite</li>
                    <li>Docker deployment configuration</li>
                </ul>

                <h3>ROI Analysis</h3>
                <table>
                    <thead>
                        <tr>
                            <th>Metric</th>
                            <th>Current State</th>
                            <th>With AI-FARM</th>
                            <th>Improvement</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Monthly Alerts</td>
                            <td>17,268</td>
                            <td>5,180</td>
                            <td>70% reduction</td>
                        </tr>
                        <tr>
                            <td>Review Hours/Month</td>
                            <td>862</td>
                            <td>259</td>
                            <td>603 hours saved</td>
                        </tr>
                        <tr>
                            <td>Monthly Cost</td>
                            <td>$43,100</td>
                            <td>$12,950</td>
                            <td>$30,150 saved</td>
                        </tr>
                        <tr>
                            <td>Annual Savings</td>
                            <td>-</td>
                            <td>-</td>
                            <td>$361,800</td>
                        </tr>
                    </tbody>
                </table>

                <h3>Strategic Recommendations</h3>
                <ol>
                    <li><strong>Immediate Implementation</strong>: Deploy AI-FARM for high-volume, low-risk areas first</li>
                    <li><strong>Phased Rollout</strong>: Start with one terminal, then expand based on results</li>
                    <li><strong>Continuous Learning</strong>: Use feedback to improve accuracy over time</li>
                    <li><strong>Integration Planning</strong>: Plan for VALO system integration in Phase 2</li>
                </ol>
            </section>

            <!-- VLM Implementation Section -->
            <section id="vlm-implementation" class="section">
                <h2>AI-FARM VLM Implementation - Knowledge Session #16</h2>
                
                <h3>Technical VLM Implementation Details</h3>
                <p>The VLM (Vision Language Model) integration is the core of AI-FARM's ability to analyze safety violation images and determine false positives with high accuracy.</p>

                <h4>Key Implementation Features</h4>
                <ul>
                    <li><strong>Model</strong>: InternVL3 38B AWQ (OpenAI-compatible API)</li>
                    <li><strong>Processing</strong>: Batch analysis with rate limiting</li>
                    <li><strong>Optimization</strong>: Image compression for files >10MB</li>
                    <li><strong>Error Handling</strong>: Retry logic with exponential backoff</li>
                </ul>

                <h4>97% False Positive Analysis Findings</h4>
                <div class="highlight-box">
                    <p><strong>Discovery</strong>: Analysis of 50,000+ historical alerts revealed that 97% were false positives, with common patterns including:</p>
                    <ul>
                        <li>Equipment misidentification (cranes, forklifts)</li>
                        <li>Weather-related false triggers (rain, shadows)</li>
                        <li>Structural elements mistaken for violations</li>
                        <li>Authorized activities flagged as violations</li>
                    </ul>
                </div>

                <h4>Production-Ready VLM Prompt Engineering</h4>
                <pre><code>Analyze this safety violation image (Case: {case_number}).
Determine if this is a false positive alert.

Focus on:
1. Equipment identification accuracy
2. Structural elements vs actual violations  
3. Weather/lighting conditions
4. Authorized vs unauthorized activities

Provide:
- Detection type classification
- False positive likelihood (1-100)
- Specific reasoning
- Recommendation (DISMISS_ALERT or REQUIRES_REVIEW)</code></pre>

                <h4>Performance Improvement Projections</h4>
                <table>
                    <thead>
                        <tr>
                            <th>Metric</th>
                            <th>Baseline</th>
                            <th>Month 1</th>
                            <th>Month 3</th>
                            <th>Month 6</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Accuracy</td>
                            <td>-</td>
                            <td>85%</td>
                            <td>90%</td>
                            <td>95%</td>
                        </tr>
                        <tr>
                            <td>False Positive Reduction</td>
                            <td>0%</td>
                            <td>60%</td>
                            <td>65%</td>
                            <td>70%</td>
                        </tr>
                        <tr>
                            <td>Processing Time</td>
                            <td>-</td>
                            <td>5s/image</td>
                            <td>4s/image</td>
                            <td>3s/image</td>
                        </tr>
                        <tr>
                            <td>Cost Savings</td>
                            <td>$0</td>
                            <td>$25K</td>
                            <td>$28K</td>
                            <td>$30K</td>
                        </tr>
                    </tbody>
                </table>
            </section>

            <!-- VLM Filter System Section -->
            <section id="vlm-filter" class="section">
                <h2>VLM False Positive Filter System</h2>

                <h3>Complete VLM Analysis Prompt</h3>
                <p>The system uses a carefully crafted prompt to analyze each image:</p>

                <pre><code>{
  "model": "gpt-4-vision-preview",
  "messages": [
    {
      "role": "system",
      "content": "You are an expert safety violation analyst for PSA VALO maritime terminals."
    },
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "Analyze this safety violation image (Case: V1250630118)..."
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,{base64_image}"
          }
        }
      ]
    }
  ],
  "max_tokens": 500,
  "temperature": 0.3
}</code></pre>

                <h3>Implementation Mechanism</h3>
                <div class="mermaid-container">
                    <div class="mermaid">
flowchart LR
    Image[Violation Image] --> Encode[Base64 Encoding]
    Encode --> Compress{Size > 10MB?}
    Compress -->|Yes| Optimize[Compress Image]
    Compress -->|No| API[VLM API Call]
    Optimize --> API
    API --> Parse[Parse Response]
    Parse --> Threshold{Apply Thresholds}
    Threshold -->|Score < 60| Dismiss[DISMISS_ALERT]
    Threshold -->|Score >= 60| Review[REQUIRES_REVIEW]
                    </div>
                </div>

                <h3>Configuration Settings</h3>
                <pre><code># Threshold Configuration
threshold_default: int = 60
threshold_structure_misid: int = 60
threshold_equipment_misid: int = 60
threshold_weather_related: int = 40
threshold_authorized_activity: int = 30
threshold_minor_violation: int = 70
threshold_safety_violation: int = 80

# VLM Settings
vlm_model: str = "gpt-4-vision-preview"
vlm_max_tokens: int = 500
vlm_temperature: float = 0.3
vlm_timeout_seconds: int = 30</code></pre>

                <h3>Expected Outcomes</h3>
                <ul>
                    <li><strong>Primary Goal</strong>: Reduce false positives by 70%</li>
                    <li><strong>Secondary Goals</strong>:
                        <ul>
                            <li>Maintain 99%+ detection of real violations</li>
                            <li>Process images in under 5 seconds</li>
                            <li>Provide clear reasoning for decisions</li>
                            <li>Enable continuous improvement through learning</li>
                        </ul>
                    </li>
                </ul>

                <h3>Detection Type Classifications</h3>
                <table>
                    <thead>
                        <tr>
                            <th>Detection Type</th>
                            <th>Description</th>
                            <th>Default Threshold</th>
                            <th>Example</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>STRUCTURE_MISIDENTIFIED</td>
                            <td>Fixed structures mistaken for violations</td>
                            <td>60</td>
                            <td>Crane arm flagged as intrusion</td>
                        </tr>
                        <tr>
                            <td>EQUIPMENT_MISIDENTIFIED</td>
                            <td>Equipment incorrectly identified</td>
                            <td>60</td>
                            <td>Forklift seen as unauthorized vehicle</td>
                        </tr>
                        <tr>
                            <td>WEATHER_RELATED</td>
                            <td>Weather causing false alerts</td>
                            <td>40</td>
                            <td>Rain drops triggering motion</td>
                        </tr>
                        <tr>
                            <td>AUTHORIZED_ACTIVITY</td>
                            <td>Permitted actions flagged</td>
                            <td>30</td>
                            <td>Worker in designated area</td>
                        </tr>
                        <tr>
                            <td>MINOR_VIOLATION</td>
                            <td>Low-risk violations</td>
                            <td>70</td>
                            <td>Minor boundary crossing</td>
                        </tr>
                        <tr>
                            <td>SAFETY_VIOLATION</td>
                            <td>Genuine safety concerns</td>
                            <td>80</td>
                            <td>Person in dangerous zone</td>
                        </tr>
                    </tbody>
                </table>
            </section>

            <!-- Development Specification Section -->
            <section id="development-spec" class="section">
                <h2>AI-FARM Development Specification for Claude Code</h2>

                <h3>POC Mission</h3>
                <p>Create a compelling proof-of-concept that demonstrates AI's ability to solve the false positive crisis in safety monitoring systems through live, interactive demonstrations with real customer data.</p>

                <h3>Demo Story Arc</h3>
                <ol>
                    <li><strong>Crisis Presentation</strong> (2 minutes)
                        <ul>
                            <li>Show overwhelming false positive statistics</li>
                            <li>Demonstrate cost impact visualization</li>
                            <li>Highlight team burnout metrics</li>
                        </ul>
                    </li>
                    <li><strong>Live Processing</strong> (5 minutes)
                        <ul>
                            <li>Customer uploads their CSV + images</li>
                            <li>Real-time processing with progress updates</li>
                            <li>Live statistics updating as processing occurs</li>
                        </ul>
                    </li>
                    <li><strong>Auto-Learning Demo</strong> (3 minutes)
                        <ul>
                            <li>Show pattern detection results</li>
                            <li>Display threshold optimization</li>
                            <li>Present custom recommendations</li>
                        </ul>
                    </li>
                    <li><strong>Results & ROI</strong> (5 minutes)
                        <ul>
                            <li>Interactive results dashboard</li>
                            <li>Customized ROI calculations</li>
                            <li>Export ready reports</li>
                        </ul>
                    </li>
                </ol>

                <h3>Auto-Learning Mechanisms</h3>
                <div class="highlight-box">
                    <h4>1. Pattern Detection</h4>
                    <p>Analyzes VLM reasoning to identify customer-specific patterns:</p>
                    <ul>
                        <li>Common false positive triggers</li>
                        <li>Site-specific equipment</li>
                        <li>Environmental factors</li>
                    </ul>
                </div>

                <div class="highlight-box">
                    <h4>2. Threshold Optimization</h4>
                    <p>Dynamically adjusts confidence thresholds based on:</p>
                    <ul>
                        <li>F1 score optimization</li>
                        <li>Customer validation data</li>
                        <li>Detection type performance</li>
                    </ul>
                </div>

                <div class="highlight-box">
                    <h4>3. Confidence Calibration</h4>
                    <p>Ensures prediction confidence matches actual accuracy:</p>
                    <ul>
                        <li>Expected Calibration Error (ECE) calculation</li>
                        <li>Calibration curve generation</li>
                        <li>Confidence score adjustment</li>
                    </ul>
                </div>

                <h3>Technical Components</h3>
                <table>
                    <thead>
                        <tr>
                            <th>Component</th>
                            <th>Technology</th>
                            <th>Purpose</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Backend API</td>
                            <td>Python + FastAPI</td>
                            <td>Core processing engine</td>
                            <td>✅ Complete</td>
                        </tr>
                        <tr>
                            <td>VLM Service</td>
                            <td>OpenAI-compatible API</td>
                            <td>Image analysis</td>
                            <td>✅ Complete</td>
                        </tr>
                        <tr>
                            <td>Database</td>
                            <td>SQLite/PostgreSQL</td>
                            <td>Data persistence</td>
                            <td>✅ Complete</td>
                        </tr>
                        <tr>
                            <td>Frontend</td>
                            <td>React + TypeScript</td>
                            <td>User interface</td>
                            <td>✅ Complete</td>
                        </tr>
                        <tr>
                            <td>Auto-Learning</td>
                            <td>Python ML algorithms</td>
                            <td>Pattern detection</td>
                            <td>✅ Complete</td>
                        </tr>
                        <tr>
                            <td>Deployment</td>
                            <td>Docker + Docker Compose</td>
                            <td>Easy deployment</td>
                            <td>✅ Complete</td>
                        </tr>
                    </tbody>
                </table>

                <h3>Development Timeline</h3>
                <ul>
                    <li><strong>Week 1-2</strong>: Core infrastructure and VLM integration</li>
                    <li><strong>Week 3-4</strong>: Batch processing and auto-learning</li>
                    <li><strong>Week 5-6</strong>: Frontend development and UI polish</li>
                    <li><strong>Week 7-8</strong>: Testing, optimization, and demo preparation</li>
                </ul>
            </section>

            <!-- Architecture Diagrams Section -->
            <section id="architecture-diagrams" class="section">
                <h2>AI-FARM Architecture Diagrams</h2>
                <p>Comprehensive visual representation of the AI-FARM system architecture, showing all components and their interactions.</p>

                <!-- High-Level Architecture -->
                <div id="high-level-architecture" class="subsection">
                    <h3>1. High-Level System Architecture</h3>
                    <div class="mermaid-container">
                        <div class="mermaid">
graph TB
    subgraph "Frontend Layer"
        UI1[Main Frontend<br/>React + TypeScript]
        UI2[Surveillance Frontend<br/>React + TypeScript]
    end
    
    subgraph "API Gateway"
        API[FastAPI REST API<br/>Port 8001]
    end
    
    subgraph "Backend Services"
        BS1[Batch Processor<br/>Service]
        BS2[VLM Service<br/>OpenAI Compatible]
        BS3[Auto-Learning<br/>Engine]
        BS4[Background Task<br/>Manager]
    end
    
    subgraph "Data Layer"
        DB[(SQLite/PostgreSQL<br/>Database)]
        FS[File Storage<br/>Images/CSVs]
    end
    
    subgraph "External Services"
        VLM[VLM API<br/>InternVL3 38B]
    end
    
    UI1 -->|HTTP/REST| API
    UI2 -->|HTTP/WebSocket| API
    
    API --> BS1
    API --> BS3
    API --> BS4
    
    BS1 --> BS2
    BS1 --> DB
    BS1 --> FS
    
    BS2 -->|API Calls| VLM
    
    BS3 --> DB
    BS4 --> BS1
    
    classDef frontend fill:#2196F3,stroke:#1976D2,color:#fff
    classDef backend fill:#4CAF50,stroke:#388E3C,color:#fff
    classDef data fill:#FF9800,stroke:#F57C00,color:#fff
    classDef external fill:#9C27B0,stroke:#7B1FA2,color:#fff
    
    class UI1,UI2 frontend
    class API,BS1,BS2,BS3,BS4 backend
    class DB,FS data
    class VLM external
                        </div>
                    </div>
                </div>

                <!-- Backend Processing Flow -->
                <div id="backend-flow" class="subsection">
                    <h3>2. Backend Processing Flow</h3>
                    <div class="mermaid-container">
                        <div class="mermaid">
flowchart TD
    Start([User Uploads CSV/Images])
    
    Upload[upload_and_process_batch<br/>API Endpoint]
    Validate[Validate Input Files]
    CreateBatch[Create Batch ID<br/>& Temp Directory]
    
    BG[Background Task:<br/>process_batch_background]
    
    Parse[CSV Processor:<br/>parse_csv_file]
    ValidateImg[Validate Image Paths]
    
    CreateDB[Create Database Record<br/>BatchProcessing]
    
    ProcessBatch[Process Cases in<br/>Mini-Batches]
    
    subgraph "VLM Processing"
        EncodeImg[Encode Image<br/>to Base64]
        CompressImg{Image > 10MB?}
        Compress[Compress Image]
        CallVLM[Call VLM API]
        ParseResp[Parse VLM Response]
    end
    
    CreateResult[Create Processing Result]
    SaveResult[Save to Database]
    
    UpdateProgress[Update Batch Progress]
    
    MoreCases{More Cases?}
    
    AutoLearn[Auto-Learning Engine:<br/>Learn from Results]
    
    subgraph "Auto-Learning"
        Pattern[Pattern Detection]
        Threshold[Threshold Optimization]
        Calibrate[Confidence Calibration]
        GenRec[Generate Recommendations]
    end
    
    Complete[Mark Batch Complete]
    
    End([Results Available])
    
    Start --> Upload
    Upload --> Validate
    Validate -->|Valid| CreateBatch
    Validate -->|Invalid| End
    
    CreateBatch --> BG
    BG --> Parse
    Parse --> ValidateImg
    ValidateImg --> CreateDB
    CreateDB --> ProcessBatch
    
    ProcessBatch --> EncodeImg
    EncodeImg --> CompressImg
    CompressImg -->|Yes| Compress
    CompressImg -->|No| CallVLM
    Compress --> CallVLM
    CallVLM --> ParseResp
    
    ParseResp --> CreateResult
    CreateResult --> SaveResult
    SaveResult --> UpdateProgress
    
    UpdateProgress --> MoreCases
    MoreCases -->|Yes| ProcessBatch
    MoreCases -->|No| AutoLearn
    
    AutoLearn --> Pattern
    Pattern --> Threshold
    Threshold --> Calibrate
    Calibrate --> GenRec
    
    GenRec --> Complete
    Complete --> End
    
    classDef api fill:#2196F3,stroke:#1976D2,color:#fff
    classDef process fill:#4CAF50,stroke:#388E3C,color:#fff
    classDef decision fill:#FF9800,stroke:#F57C00,color:#fff
    classDef external fill:#9C27B0,stroke:#7B1FA2,color:#fff
    
    class Upload,BG api
    class Parse,ValidateImg,ProcessBatch,EncodeImg,Compress,CreateResult,SaveResult,UpdateProgress,Pattern,Threshold,Calibrate,GenRec process
    class CompressImg,MoreCases decision
    class CallVLM,ParseResp external
                        </div>
                    </div>
                </div>

                <!-- Frontend Architecture -->
                <div id="frontend-architecture" class="subsection">
                    <h3>3. Frontend Component Architecture</h3>
                    <div class="mermaid-container">
                        <div class="mermaid">
graph TD
    subgraph "Main Frontend"
        App1[App.tsx<br/>Router & Layout]
        
        subgraph "Pages"
            Landing[LandingPage]
            Upload[UploadPage]
            Processing[ProcessingPage]
            Dashboard[DashboardPage]
            Results[ResultsPage]
            ROI[ROIPage]
            Insights[InsightsPage]
        end
        
        subgraph "Components"
            FileUp[FileUpload]
            Progress[ProcessingProgress]
            MetricCards[MetricCard]
            Charts[ResultsChart]
        end
        
        subgraph "Services"
            API1[ApiClient]
            BatchSvc[BatchService]
            MetricsSvc[MetricsService]
        end
    end
    
    subgraph "Surveillance Frontend"
        App2[App.tsx<br/>State-based Navigation]
        
        subgraph "Surveillance Pages"
            SLanding[LandingPage]
            SUpload[UploadPage]
            SDashboard[DashboardPage]
            SAnalytics[AnalyticsPage]
        end
        
        subgraph "Surveillance Components"
            AlertFeed[AlertFeed]
            TerminalStatus[TerminalStatus]
            MetricsDisplay[MetricsDisplay]
        end
        
        API2[API Service<br/>+ WebSocket]
    end
    
    App1 --> Landing
    App1 --> Upload
    App1 --> Processing
    App1 --> Dashboard
    
    Upload --> FileUp
    Processing --> Progress
    Dashboard --> MetricCards
    Results --> Charts
    
    Landing --> BatchSvc
    Upload --> BatchSvc
    Processing --> BatchSvc
    Dashboard --> MetricsSvc
    
    BatchSvc --> API1
    MetricsSvc --> API1
    
    App2 --> SLanding
    App2 --> SUpload
    App2 --> SDashboard
    
    SDashboard --> AlertFeed
    SDashboard --> TerminalStatus
    SAnalytics --> MetricsDisplay
    
    AlertFeed --> API2
    TerminalStatus --> API2
    
    classDef app fill:#E91E63,stroke:#C2185B,color:#fff
    classDef page fill:#2196F3,stroke:#1976D2,color:#fff
    classDef component fill:#4CAF50,stroke:#388E3C,color:#fff
    classDef service fill:#FF9800,stroke:#F57C00,color:#fff
    
    class App1,App2 app
    class Landing,Upload,Processing,Dashboard,Results,ROI,Insights,SLanding,SUpload,SDashboard,SAnalytics page
    class FileUp,Progress,MetricCards,Charts,AlertFeed,TerminalStatus,MetricsDisplay component
    class API1,API2,BatchSvc,MetricsSvc service
                        </div>
                    </div>
                </div>

                <!-- Data Flow -->
                <div id="data-flow" class="subsection">
                    <h3>4. Data Flow and State Management</h3>
                    <div class="mermaid-container">
                        <div class="mermaid">
stateDiagram-v2
    [*] --> UserUpload: User initiates upload
    
    UserUpload --> ValidationState: CSV + Images
    
    ValidationState --> ProcessingQueue: Valid data
    ValidationState --> ErrorState: Invalid data
    
    ProcessingQueue --> ActiveProcessing: Background task starts
    
    ActiveProcessing --> VLMAnalysis: Process mini-batch
    
    VLMAnalysis --> ResultsAccumulation: Store results
    
    ResultsAccumulation --> ProgressUpdate: Update progress
    
    ProgressUpdate --> ActiveProcessing: More cases
    ProgressUpdate --> AutoLearning: All cases done
    
    AutoLearning --> PatternAnalysis: Analyze patterns
    PatternAnalysis --> ThresholdOptimization: Optimize thresholds
    ThresholdOptimization --> RecommendationGeneration: Generate insights
    
    RecommendationGeneration --> CompletedState: Processing complete
    
    CompletedState --> ResultsDisplay: User views results
    
    ErrorState --> [*]: Error handling
    ResultsDisplay --> [*]: Process complete
    
    note right of ValidationState
        Validates:
        - CSV format
        - Required columns
        - Image paths exist
        - Case number format
    end note
    
    note right of VLMAnalysis
        For each image:
        - Encode to base64
        - Call VLM API
        - Parse response
        - Apply thresholds
    end note
    
    note right of AutoLearning
        Learns from:
        - VLM reasoning
        - Detection patterns
        - Confidence scores
        - Historical data
    end note
                        </div>
                    </div>
                </div>

                <!-- API Interaction -->
                <div id="api-interaction" class="subsection">
                    <h3>5. API Endpoint Flow</h3>
                    <div class="mermaid-container">
                        <div class="mermaid">
sequenceDiagram
    participant User
    participant Frontend
    participant API
    participant BatchProcessor
    participant VLMService
    participant Database
    participant BackgroundTasks
    
    User->>Frontend: Upload CSV + Images
    Frontend->>API: POST /api/v1/batch/upload
    
    API->>API: Validate files
    API->>API: Create batch_id
    API->>BackgroundTasks: Queue processing task
    API-->>Frontend: Return batch_id
    
    Frontend->>Frontend: Navigate to processing page
    
    BackgroundTasks->>BatchProcessor: process_batch_from_csv()
    
    BatchProcessor->>Database: Create batch record
    
    loop For each mini-batch
        BatchProcessor->>VLMService: analyze_batch()
        
        loop For each image
            VLMService->>VLMService: encode_image()
            VLMService->>VLMService: make_api_request()
            VLMService-->>BatchProcessor: VLM result
        end
        
        BatchProcessor->>Database: Save results
        BatchProcessor->>Database: Update progress
    end
    
    Frontend->>API: GET /api/v1/batch/{batch_id}
    API->>Database: Query batch status
    Database-->>API: Batch data
    API-->>Frontend: Status update
    
    BatchProcessor->>BatchProcessor: Calculate summary
    BatchProcessor->>Database: Mark complete
    
    Frontend->>API: GET /api/v1/batch/{batch_id}/results
    API->>Database: Query results
    Database-->>API: Processing results
    API-->>Frontend: Results data
    
    Frontend->>User: Display results
                        </div>
                    </div>
                </div>

                <!-- More diagrams would continue here... -->
                <p class="highlight-box">
                    <strong>Note:</strong> The complete architecture documentation includes 15 detailed Mermaid diagrams. The remaining diagrams cover:
                    <ul>
                        <li>Function Call Hierarchy</li>
                        <li>VLM Integration Flow</li>
                        <li>Error Handling and Recovery Flow</li>
                        <li>Detailed Backend Service Interactions</li>
                        <li>Auto-Learning Process Flow</li>
                        <li>Database Schema and Relationships</li>
                        <li>Frontend User Flow</li>
                        <li>Component State Flow</li>
                        <li>API Client Service Architecture</li>
                        <li>Complete Request-Response Lifecycle</li>
                    </ul>
                </p>
            </section>

            <!-- Architecture Summary Section -->
            <section id="architecture-summary" class="section">
                <h2>Architecture Analysis Summary</h2>

                <h3>System Architecture Overview</h3>
                <p>AI-FARM follows a <strong>layered microservices-oriented architecture</strong> with clear separation of concerns:</p>

                <div class="highlight-box">
                    <h4>Architecture Layers</h4>
                    <ol>
                        <li><strong>Frontend Layer</strong>: Two React TypeScript applications
                            <ul>
                                <li>Main frontend with routing and comprehensive features</li>
                                <li>Surveillance frontend with state-based navigation</li>
                            </ul>
                        </li>
                        <li><strong>API Gateway</strong>: FastAPI REST API on port 8001
                            <ul>
                                <li>RESTful endpoints for all operations</li>
                                <li>Background task processing</li>
                                <li>Comprehensive error handling</li>
                            </ul>
                        </li>
                        <li><strong>Service Layer</strong>: Core business logic
                            <ul>
                                <li>BatchProcessor: Orchestrates CSV processing</li>
                                <li>VLMService: Integrates with Vision Language Model API</li>
                                <li>AutoLearningEngine: Analyzes patterns and optimizes thresholds</li>
                                <li>TaskManager: Handles background job execution</li>
                            </ul>
                        </li>
                        <li><strong>Data Layer</strong>: SQLAlchemy with SQLite/PostgreSQL
                            <ul>
                                <li>Well-defined schema with proper relationships</li>
                                <li>Transaction management and session handling</li>
                            </ul>
                        </li>
                    </ol>
                </div>

                <h3>Core Logic Flow</h3>
                <p>The system processes safety violation images through this pipeline:</p>
                <ol>
                    <li><strong>Upload</strong>: User uploads CSV with image paths</li>
                    <li><strong>Validation</strong>: System validates CSV format and image availability</li>
                    <li><strong>Batch Processing</strong>: Images processed in mini-batches for efficiency</li>
                    <li><strong>VLM Analysis</strong>: Each image analyzed by external VLM API</li>
                    <li><strong>Result Creation</strong>: Thresholds applied to determine recommendations</li>
                    <li><strong>Auto-Learning</strong>: System learns from results to optimize future processing</li>
                    <li><strong>Results Display</strong>: Interactive dashboard shows analysis and ROI</li>
                </ol>

                <h3>Key Design Patterns</h3>
                <table>
                    <thead>
                        <tr>
                            <th>Pattern</th>
                            <th>Implementation</th>
                            <th>Purpose</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Singleton Services</td>
                            <td>Global instances for core services</td>
                            <td>Ensure single instance per service</td>
                        </tr>
                        <tr>
                            <td>Repository Pattern</td>
                            <td>DatabaseOperations abstracts data access</td>
                            <td>Separation of data logic</td>
                        </tr>
                        <tr>
                            <td>Dependency Injection</td>
                            <td>FastAPI's DI for database sessions</td>
                            <td>Loose coupling, testability</td>
                        </tr>
                        <tr>
                            <td>Context Managers</td>
                            <td>Database session management</td>
                            <td>Proper resource cleanup</td>
                        </tr>
                        <tr>
                            <td>Async/Await</td>
                            <td>Extensive use for I/O operations</td>
                            <td>Non-blocking operations</td>
                        </tr>
                    </tbody>
                </table>

                <h3>Critical Functions</h3>
                <div class="metric-card">
                    <h4>Most Important Functions</h4>
                    <ol>
                        <li><code>process_batch_from_csv()</code> - Main processing orchestrator</li>
                        <li><code>analyze_image()</code> - VLM integration point</li>
                        <li><code>learn_from_processing_results()</code> - Auto-learning coordinator</li>
                        <li><code>create_processing_result()</code> - Business logic for recommendations</li>
                    </ol>
                </div>

                <div class="metric-card">
                    <h4>Most Called Functions</h4>
                    <ol>
                        <li><code>analyze_image()</code> - Called for every image</li>
                        <li><code>record_case_result()</code> - Database persistence</li>
                        <li><code>_encode_image()</code> - Image preprocessing</li>
                        <li><code>get_database_session()</code> - Session management</li>
                    </ol>
                </div>

                <h3>Architecture Quality Assessment</h3>
                <div class="highlight-box">
                    <h4>Strengths</h4>
                    <ul>
                        <li>✅ Clear separation of concerns</li>
                        <li>✅ Robust error handling</li>
                        <li>✅ Scalable batch processing</li>
                        <li>✅ Well-structured codebase</li>
                        <li>✅ Comprehensive API design</li>
                        <li>✅ Type-safe frontend with TypeScript</li>
                    </ul>
                </div>

                <div class="highlight-box">
                    <h4>Areas for Enhancement</h4>
                    <ul>
                        <li>WebSocket implementation for real-time updates</li>
                        <li>Authentication/authorization layer</li>
                        <li>Caching layer for performance</li>
                        <li>More comprehensive test coverage</li>
                        <li>API versioning strategy</li>
                    </ul>
                </div>
            </section>

            <!-- Validation Report Section -->
            <section id="validation-report" class="section">
                <h2>Architecture Validation Report</h2>

                <h3>Validation Overview</h3>
                <p>A comprehensive cross-reference of the architecture diagrams with the actual codebase was performed to ensure accuracy and completeness.</p>

                <h3>Key Findings</h3>

                <div class="metric-card">
                    <h4>Overall Architecture Accuracy</h4>
                    <p>The diagrams are largely accurate and reflect the actual implementation well. Most components, services, and flows match the codebase.</p>
                </div>

                <h3>Main Discrepancies Found</h3>
                <table>
                    <thead>
                        <tr>
                            <th>Category</th>
                            <th>Discrepancy</th>
                            <th>Details</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Function Names</td>
                            <td>Minor naming differences</td>
                            <td>Several function names in diagrams don't exactly match (e.g., <code>process_batch_background</code> vs <code>process_batch_from_csv</code>)</td>
                        </tr>
                        <tr>
                            <td>Database Fields</td>
                            <td>Field name variations</td>
                            <td>Some field names differ (e.g., <code>vlm_status</code> vs <code>processing_status</code>)</td>
                        </tr>
                        <tr>
                            <td>Configuration</td>
                            <td>Hardcoded vs configurable</td>
                            <td>Diagrams show hardcoded values but code uses configuration settings</td>
                        </tr>
                        <tr>
                            <td>Service Structure</td>
                            <td>Frontend simplification</td>
                            <td>Surveillance frontend has simpler structure than depicted</td>
                        </tr>
                        <tr>
                            <td>Processing</td>
                            <td>Implementation details</td>
                            <td>Mini-batch processing uses semaphore rate limiting rather than explicit batching</td>
                        </tr>
                    </tbody>
                </table>

                <h3>Verified Components</h3>
                <ul>
                    <li>✅ All major architectural components exist as shown</li>
                    <li>✅ Database schema structure matches (with minor field name differences)</li>
                    <li>✅ API endpoints and routing are correct</li>
                    <li>✅ VLM integration flow is accurate</li>
                    <li>✅ Auto-learning engine implementation matches</li>
                    <li>✅ Both frontend applications exist with the shown pages</li>
                </ul>

                <h3>Notable Implementation Details</h3>
                <ul>
                    <li>The system uses configuration-based settings extensively</li>
                    <li>Error handling is more robust than shown in diagrams</li>
                    <li>Background task processing is implemented via FastAPI's BackgroundTasks</li>
                    <li>The surveillance frontend uses state-based navigation instead of routing</li>
                </ul>

                <h3>Recommendations</h3>
                <ol>
                    <li><strong>Update Function Names</strong>: Align diagram function names with actual implementation</li>
                    <li><strong>Clarify Frontend Architecture</strong>: Show single app with surveillance features</li>
                    <li><strong>Add Configuration Details</strong>: Include environment variables and settings</li>
                    <li><strong>Document API Authentication</strong>: Add any auth flows if implemented</li>
                    <li><strong>Version the Diagrams</strong>: Add version numbers and update dates</li>
                </ol>

                <div class="highlight-box">
                    <h4>Overall Assessment</h4>
                    <p>The documentation provides a <strong>comprehensive and mostly accurate</strong> representation of the AI-FARM system architecture. While there are some inaccuracies and missing elements, the diagrams effectively communicate the system's structure and data flows.</p>
                    <p><strong>Score: 8/10</strong> - Very good documentation with room for minor improvements to achieve complete accuracy and coverage.</p>
                </div>
            </section>
        </main>
    </div>

    <!-- Back to Top Button -->
    <a href="#" class="back-to-top" id="backToTop">↑ Top</a>

    <script>
        // Initialize Mermaid
        mermaid.initialize({ 
            startOnLoad: true,
            theme: 'default',
            themeVariables: {
                primaryColor: '#2563eb',
                primaryTextColor: '#fff',
                primaryBorderColor: '#1e40af',
                lineColor: '#6b7280',
                secondaryColor: '#7c3aed',
                tertiaryColor: '#10b981',
                background: '#f3f4f6',
                mainBkg: '#ffffff',
                secondBkg: '#f9fafb',
                tertiaryBkg: '#e5e7eb',
                primaryGradient: 'linear-gradient(135deg, #2563eb 0%, #7c3aed 100%)'
            }
        });

        // Mobile Sidebar Toggle
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            const mainContent = document.getElementById('mainContent');
            sidebar.classList.toggle('collapsed');
            mainContent.classList.toggle('expanded');
        }

        // Active Navigation Tracking
        const sections = document.querySelectorAll('.section');
        const navLinks = document.querySelectorAll('.nav-link');
        
        function updateActiveNav() {
            let current = '';
            sections.forEach(section => {
                const sectionTop = section.offsetTop;
                const sectionHeight = section.clientHeight;
                if (window.pageYOffset >= sectionTop - 100) {
                    current = section.getAttribute('id');
                }
            });

            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href').substring(1) === current) {
                    link.classList.add('active');
                }
            });
        }

        // Progress Bar
        function updateProgressBar() {
            const winScroll = document.body.scrollTop || document.documentElement.scrollTop;
            const height = document.documentElement.scrollHeight - document.documentElement.clientHeight;
            const scrolled = (winScroll / height) * 100;
            document.getElementById("progressBar").style.width = scrolled + "%";
        }

        // Back to Top Button
        const backToTop = document.getElementById('backToTop');
        
        function checkBackToTop() {
            if (window.pageYOffset > 300) {
                backToTop.classList.add('visible');
            } else {
                backToTop.classList.remove('visible');
            }
        }

        // Search Functionality
        function searchContent() {
            const input = document.getElementById('searchInput');
            const filter = input.value.toLowerCase();
            const sections = document.querySelectorAll('.section');
            
            sections.forEach(section => {
                const text = section.textContent.toLowerCase();
                if (text.includes(filter)) {
                    section.style.display = '';
                } else {
                    section.style.display = 'none';
                }
            });
        }

        // Smooth Scrolling for Navigation Links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Event Listeners
        window.addEventListener('scroll', () => {
            updateActiveNav();
            updateProgressBar();
            checkBackToTop();
        });

        // Collapsible Sections
        document.querySelectorAll('.collapsible').forEach(element => {
            element.addEventListener('click', function() {
                this.classList.toggle('collapsed');
                const content = this.nextElementSibling;
                if (content && content.classList.contains('collapsible-content')) {
                    content.classList.toggle('collapsed');
                }
            });
        });

        // Initialize on Load
        document.addEventListener('DOMContentLoaded', () => {
            updateActiveNav();
            updateProgressBar();
            checkBackToTop();
        });

        // Print Functionality
        window.addEventListener('beforeprint', () => {
            // Expand all collapsed sections for printing
            document.querySelectorAll('.collapsed').forEach(element => {
                element.classList.remove('collapsed');
            });
        });
    </script>
</body>
</html>