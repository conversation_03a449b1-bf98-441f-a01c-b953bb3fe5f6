# AI-FARM Environment Configuration
# Copy this file to .env and update the values as needed

# ======================
# DATABASE CONFIGURATION
# ======================
POSTGRES_DB=ai_farm
POSTGRES_USER=ai_farm_user
POSTGRES_PASSWORD=ai_farm_password_change_me
DATABASE_ECHO=false

# ======================
# SERVER CONFIGURATION
# ======================
DEBUG=false
LOG_LEVEL=INFO
HOST=0.0.0.0
PORT=8000

# ======================
# VLM API CONFIGURATION
# ======================
VLM_API_BASE_URL=https://api.openai.com
VLM_API_KEY=your-openai-api-key-here
VLM_MODEL_NAME=gpt-4o-2024-11-20
VLM_MAX_TOKENS=1000
VLM_TEMPERATURE=0.1
VLM_TIMEOUT_SECONDS=30

# ======================
# PROCESSING CONFIGURATION
# ======================
BATCH_SIZE=10
MAX_CONCURRENT_REQUESTS=3
PROCESSING_TIMEOUT_MINUTES=60
IMAGE_MAX_SIZE_MB=10

# ======================
# CONFIDENCE THRESHOLDS
# ======================
THRESHOLD_STRUCTURE_MISID=70
THRESHOLD_PROPER_PPE=65
THRESHOLD_NO_VIOLATION=75
THRESHOLD_DEFAULT=70

# ======================
# AUTO-LEARNING CONFIGURATION
# ======================
ENABLE_AUTO_LEARNING=true
LEARNING_BATCH_SIZE=50
CONFIDENCE_CALIBRATION_ENABLED=true
PATTERN_DETECTION_ENABLED=true

# ======================
# CUSTOMER DATA PROCESSING
# ======================
CUSTOMER_DATA_RETENTION_HOURS=24
ENABLE_CUSTOMER_DATA_CLEANUP=true
SECURE_TEMP_STORAGE=true

# ======================
# PERFORMANCE SETTINGS
# ======================
WORKER_PROCESSES=4
KEEP_ALIVE_TIMEOUT=65
MAX_REQUEST_SIZE_MB=100

# ======================
# MONITORING
# ======================
ENABLE_METRICS=true
METRICS_PORT=9090
ENABLE_HEALTH_CHECK=true

# ======================
# FRONTEND CONFIGURATION
# ======================
REACT_APP_API_URL=http://localhost:8000
REACT_APP_ENV=development

# ======================
# DOCKER CONFIGURATION
# ======================
COMPOSE_PROJECT_NAME=ai-farm

# Security Notice: 
# - Change all default passwords before production deployment
# - Keep API keys secure and never commit them to version control
# - Use strong passwords for database credentials