#!/usr/bin/env python3
"""
Quick analysis of Round 3 progress and failures
"""

import json
import os


def quick_analysis():
    """Quick analysis of Round 3 status"""
    
    # Check if intermediate file exists
    intermediate_file = "/home/<USER>/VALO_AI-FARM_2025/valo_batch_round3_intermediate.json"
    
    if not os.path.exists(intermediate_file):
        print("Round 3 intermediate results not found!")
        return
    
    # Load data
    with open(intermediate_file, 'r') as f:
        data = json.load(f)
    
    # Handle both possible formats
    if isinstance(data, list):
        results = data
    else:
        results = data.get('detailed_results', [])
    
    # Quick stats
    total_cases = len(results)
    valid_cases = [r for r in results if not r['is_false_positive']]
    fp_cases = [r for r in results if r['is_false_positive']]
    
    # Valid case analysis
    valid_protected = [r for r in valid_cases if not r['is_false_positive_predicted']]
    valid_dismissed = [r for r in valid_cases if r['is_false_positive_predicted']]
    
    # FP analysis
    fp_detected = [r for r in fp_cases if r['is_false_positive_predicted']]
    fp_missed = [r for r in fp_cases if not r['is_false_positive_predicted']]
    
    print("\n" + "="*60)
    print("ROUND 3 QUICK ANALYSIS")
    print("="*60)
    
    print(f"\nTotal cases processed: {total_cases}")
    print(f"Valid cases: {len(valid_cases)}")
    print(f"False positive cases: {len(fp_cases)}")
    
    print(f"\n🚨 CRITICAL METRIC - Valid Protection Rate:")
    print(f"   Protected: {len(valid_protected)}/{len(valid_cases)}")
    print(f"   Rate: {len(valid_protected)/len(valid_cases)*100:.1f}%")
    print(f"   FAILURES: {len(valid_dismissed)} valid cases dismissed!")
    
    print(f"\n📊 False Positive Detection:")
    print(f"   Detected: {len(fp_detected)}/{len(fp_cases)}")
    print(f"   Rate: {len(fp_detected)/len(fp_cases)*100:.1f}%")
    
    if valid_dismissed:
        print(f"\n\n🔴 VALID CASES INCORRECTLY DISMISSED:")
        print("-"*60)
        for idx, case in enumerate(valid_dismissed[:5], 1):  # Show first 5
            print(f"\n{idx}. {case['case_number']} - {case['infringement_type']}")
            print(f"   Remarks: {case['remarks']}")
            print(f"   VLM said: {case['person_detected']} person, {case['confidence']}% conf")
            print(f"   Decision: {case['vlm_decision']}")
        
        if len(valid_dismissed) > 5:
            print(f"\n   ... and {len(valid_dismissed) - 5} more failures")
    
    # Check progress file
    progress_file = "/home/<USER>/VALO_AI-FARM_2025/valo_intelligent_round3_progress.json"
    if os.path.exists(progress_file):
        with open(progress_file, 'r') as f:
            progress = json.load(f)
        print(f"\n\n📈 CURRENT PROGRESS:")
        print(f"   Cases processed: {progress['cases_processed']}/{1250}")
        print(f"   Remaining: {progress['remaining_cases']}")
        print(f"   Last update: {progress['timestamp']}")


if __name__ == "__main__":
    quick_analysis()