#!/bin/bash
#
# Ensure ALL rounds 7-25 complete tonight
# This script will run each round sequentially with proper error handling
#

echo "=========================================="
echo "ENSURING ALL ROUNDS COMPLETE TONIGHT"
echo "Current time: $(date)"
echo "=========================================="

# Function to run a round
run_round() {
    ROUND_NUM=$1
    SCRIPT_NAME=$2
    TIMEOUT_MIN=$3
    
    echo -e "\n--- Starting Round $ROUND_NUM ---"
    
    # Check if already complete
    if ls valo_round${ROUND_NUM}_*_complete.json 2>/dev/null; then
        echo "Round $ROUND_NUM already complete, skipping"
        return 0
    fi
    
    # Create the script if it doesn't exist
    if [ ! -f "$SCRIPT_NAME" ]; then
        echo "Creating $SCRIPT_NAME..."
        python3 -c "
from overnight_orchestrator_25rounds import OvernightOrchestrator25
import asyncio

orchestrator = OvernightOrchestrator25()
asyncio.run(orchestrator.create_round_script($ROUND_NUM))
"
    fi
    
    # Run the round with timeout
    echo "Running Round $ROUND_NUM (timeout: ${TIMEOUT_MIN} minutes)..."
    timeout ${TIMEOUT_MIN}m python3 $SCRIPT_NAME
    
    # Check if completed
    if ls valo_round${ROUND_NUM}_*_complete.json 2>/dev/null; then
        echo "✅ Round $ROUND_NUM complete!"
    else
        echo "⚠️  Round $ROUND_NUM did not complete, continuing anyway"
    fi
    
    # Brief pause
    sleep 10
}

# Wait for Round 11 to complete (if running)
if pgrep -f "round11" > /dev/null; then
    echo "Waiting for Round 11 to complete..."
    while pgrep -f "round11" > /dev/null && ! ls valo_round11_*_complete.json 2>/dev/null; do
        sleep 30
        echo -n "."
    done
    echo " Done!"
fi

# Run all remaining rounds
echo -e "\n========== RUNNING REMAINING ROUNDS =========="

# Already complete: 3, 4, 5, 6
# Possibly running: 11
# To run: 7-10, 12-25

run_round 7 "round7_camera_calibration.py" 60
run_round 8 "round8_multi_factor.py" 60
run_round 9 "round9_aggressive.py" 60
run_round 10 "round10_final_push.py" 60

# Skip 11 if already complete
if ! ls valo_round11_*_complete.json 2>/dev/null; then
    run_round 11 "round11_ensemble_voting.py" 60
fi

run_round 12 "round12_meta_learning.py" 60
run_round 13 "round13_active_learning.py" 60
run_round 14 "round14_synthetic.py" 60
run_round 15 "round15_hierarchical.py" 60
run_round 16 "round16_parameter_sweep.py" 90
run_round 17 "round17_transfer.py" 60
run_round 18 "round18_anomaly.py" 60
run_round 19 "round19_reinforcement.py" 60
run_round 20 "round20_nas.py" 60

# Final rounds can be quicker
run_round 21 "round21_calibration.py" 30
run_round 22 "round22_error_feedback.py" 30
run_round 23 "round23_final_ensemble.py" 45
run_round 24 "round24_safety_verify.py" 30
run_round 25 "round25_production.py" 30

# Generate final report
echo -e "\n========== GENERATING FINAL REPORT =========="

python3 << 'EOF'
import json
import glob
from datetime import datetime

# Collect all results
all_results = {}
for i in range(3, 26):
    files = glob.glob(f'valo_round{i}_*_complete.json')
    if files:
        with open(files[0], 'r') as f:
            data = json.load(f)
            stats = data.get('stats', {})
            all_results[i] = {
                'fp_rate': stats.get('fp_detection_rate', 0),
                'valid_protection': stats.get('valid_protection_rate', 100),
                'completed': True
            }
    else:
        all_results[i] = {'completed': False}

# Find best
completed_rounds = {k: v for k, v in all_results.items() if v.get('completed', False)}
if completed_rounds:
    best_round = max(completed_rounds.items(), key=lambda x: x[1].get('fp_rate', 0))
    
    print(f"\n✅ FINAL RESULTS:")
    print(f"   Rounds completed: {len(completed_rounds)}/23")
    print(f"   Best round: Round {best_round[0]} with {best_round[1]['fp_rate']:.1f}% FP reduction")
    print(f"   All rounds status saved to: overnight_final_status.json")
    
    # Save report
    with open('overnight_final_status.json', 'w') as f:
        json.dump({
            'completion_time': datetime.now().isoformat(),
            'rounds_completed': len(completed_rounds),
            'best_round': best_round[0],
            'best_fp_rate': best_round[1]['fp_rate'],
            'all_results': all_results
        }, f, indent=2)
EOF

echo -e "\n=========================================="
echo "ALL ROUNDS PROCESSING COMPLETE!"
echo "Time: $(date)"
echo "Results: overnight_final_status.json"
echo "=========================================="