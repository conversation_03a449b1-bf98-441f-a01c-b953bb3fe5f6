# AI-FARM End-to-End Testing Guide

This guide explains how to use the comprehensive E2E testing setup with MCP (Model Context Protocol) integration for the VALO AI-FARM 2025 project.

## 🚀 Quick Start

### 1. Start the Application
```bash
# Use the unified startup script
./ai-farm.sh start

# Or use the Python launcher
python launch-ai-farm.py start

# For Windows
ai-farm.bat start
```

### 2. Run E2E Tests

#### Option A: MCP-Based Tests (Recommended)
```bash
# Run tests using MCP server tools
./run-mcp-tests.sh

# Or run directly
node test-with-mcp.js
```

#### Option B: Direct Puppeteer Tests
```bash
# Run tests using direct Puppeteer
./run-e2e-tests.sh

# Or run directly
node test-e2e-mcp.js
```

## 📦 What's Included

### Startup Scripts
- **`ai-farm.sh`** - Main unified script for starting/stopping all services
- **`ai-farm.bat`** - Windows version
- **`launch-ai-farm.py`** - Cross-platform launcher that auto-detects OS

### Test Scripts
- **`test-with-mcp.js`** - E2E tests using MCP server tools (AI-powered automation)
- **`run-mcp-tests.sh`** - Runner for MCP-based tests
- **`test-e2e-mcp.js`** - Direct Puppeteer E2E test suite
- **`run-e2e-tests.sh`** - Runner for direct Puppeteer tests
- **`test-webapp.js`** - Original Puppeteer tests (legacy)

### MCP Server
- **`mcp-server/`** - Model Context Protocol server for AI automation
- Configured with 8 tools for web automation:
  - `screenshot` - Capture webpage screenshots
  - `scrape` - Extract webpage content
  - `fill_form` - Fill and submit forms
  - `click_element` - Click webpage elements
  - `generate_pdf` - Convert webpages to PDF
  - `generate_aifarm_report` - Generate AI-FARM reports
  - `monitor_aifarm_kpis` - Monitor AI-FARM KPIs
  - `check_valorant_status` - Check VALORANT server status

## 🧪 Test Coverage

The E2E test suite covers:

1. **Backend Tests**
   - Health check endpoint
   - Metrics API endpoint
   - API response validation

2. **Frontend Tests**
   - All 7 pages (Landing, Dashboard, Upload, Processing, Results, Insights, ROI)
   - React error boundary detection
   - Page load verification

3. **Integration Tests**
   - Upload workflow (CSV file processing)
   - Dashboard metrics display
   - Frontend-Backend API communication
   - MCP server integration

4. **UI/UX Tests**
   - Responsive design (mobile, tablet, desktop)
   - Navigation functionality
   - Form interactions

## 📊 Test Results

### MCP-Based Tests Results
- **Screenshots & PDFs** in `./test-results-mcp/`
- **Markdown Report** at `./test-results-mcp/mcp-test-report.md`
- **JSON Report** at `./test-results-mcp/mcp-test-report.json`

### Direct Puppeteer Tests Results
- **Screenshots** in `./test-screenshots-e2e/`
- **Markdown Report** at `./test-screenshots-e2e/test-report.md`
- **JSON Report** at `./test-screenshots-e2e/test-report.json`

## 🔧 MCP Server Usage

### Starting MCP Server Separately
```bash
cd mcp-server
npm start
```

### Integrating with Claude Desktop
1. Copy the configuration:
   ```bash
   cat mcp-server/claude.json
   ```

2. Add to Claude Desktop's MCP configuration

3. Use the tools in Claude for automated testing and monitoring

## 🐛 Troubleshooting

### Services Not Starting
```bash
# Check logs
./ai-farm.sh logs backend
./ai-farm.sh logs frontend

# Check status
./ai-farm.sh status
```

### Port Conflicts
- Backend uses port 8000
- Frontend uses port 3000
- Make sure these ports are free

### Missing Dependencies
```bash
# Install Python dependencies
cd backend
python -m pip install -r requirements.txt

# Install Node dependencies
npm install

# Install MCP server dependencies
cd mcp-server
npm install
```

### Test Failures
1. Check if services are running: `./ai-farm.sh status`
2. Review test screenshots in `./test-screenshots-e2e/`
3. Check the test report for specific error messages
4. Ensure all dependencies are installed

## 📝 Running Specific Tests

To run only specific tests, modify `test-e2e-mcp.js`:

```javascript
// Comment out tests you don't want to run
// await this.testBackendHealth();
// await this.testBackendMetrics();
await this.testFrontendPages(); // Run only this test
```

## 🔄 Continuous Testing

For continuous testing during development:
```bash
# Watch mode (requires modification of test script)
while true; do
  ./run-e2e-tests.sh
  sleep 300 # Run every 5 minutes
done
```

## 📚 Additional Resources

- Original project documentation: `README.md`
- Architecture details: `docs/architecture/ai_farm_dev_spec.md`
- MCP Server docs: `mcp-server/README.md`
- Backend API docs: `backend/README.md`
- Frontend docs: `frontend/README.md`

## ✅ Pre-flight Checklist

Before running tests:
1. ✓ Python 3.x installed
2. ✓ Node.js and npm installed
3. ✓ All dependencies installed (`npm install`)
4. ✓ Services running (`./ai-farm.sh start`)
5. ✓ Ports 3000 and 8000 available
6. ✓ MCP server built (`cd mcp-server && npm install && npm run build`)

## 🤖 MCP vs Direct Puppeteer Tests

### When to Use MCP-Based Tests
- Testing through AI-powered automation tools
- Generating PDF reports and artifacts
- Simulating Claude's interaction with your app
- Testing MCP tool integration
- Advanced automation scenarios

### When to Use Direct Puppeteer Tests
- Faster execution for simple checks
- More granular control over browser
- Debugging specific UI issues
- Performance testing
- CI/CD pipeline integration

## 🎯 Best Practices

1. Always run tests before deploying
2. Review screenshot outputs for visual regressions
3. Keep test data minimal and focused
4. Update tests when adding new features
5. Use MCP tools for complex automation scenarios

Happy Testing! 🚀