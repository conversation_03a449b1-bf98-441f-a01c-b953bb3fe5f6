#!/usr/bin/env python3
"""
Test the complex 93-line prompt with 800 tokens on 100 cases
This directly tests if token limit was the constraint
"""

import json
import base64
import requests
import os
import time
from datetime import datetime

class ComplexPromptTester:
    def __init__(self):
        self.vlm_url = "http://100.106.127.35:9500/v1/chat/completions"
        self.vlm_model = "VLM-38B-AWQ"
        self.session = requests.Session()
        
        # Load the complex prompt
        self.complex_prompt = self.load_complex_prompt()
        
    def load_complex_prompt(self):
        """Load the 93-line production prompt"""
        if os.path.exists('FINAL_PRODUCTION_PROMPT.txt'):
            with open('FINAL_PRODUCTION_PROMPT.txt', 'r') as f:
                return f.read()
        else:
            # Fallback to a comprehensive prompt
            return """You are analyzing safety violation alert images from a port terminal.

STEP 1: STRUCTURE DETECTION
First, identify if the image primarily shows industrial structures/equipment:
- Quay Crane (QC): Large crane for loading/unloading ships
- Vessel/Ship: Container ships or cargo vessels  
- Prime Mover (PM)/Truck: Port vehicles for container transport
- Spreader: Equipment attached to cranes for lifting containers

If the image shows ONLY these structures with NO people visible, it's a FALSE POSITIVE.

STEP 2: PERSON DETECTION
If people are visible in the image, check for:
- PPE Compliance: Person MUST wear BOTH hard hat/helmet AND high-visibility vest
- If person has complete PPE → likely FALSE POSITIVE
- If person missing PPE → likely VALID VIOLATION

STEP 3: BEHAVIORAL VIOLATIONS
Even with PPE, check for unsafe behaviors:
- One-man lashing (single person securing containers)
- Phone usage while working
- Unsafe positioning near equipment
- Riding on spreaders
- Working at heights without protection

STEP 4: FINAL CLASSIFICATION
Classify as FALSE POSITIVE if:
1. Only equipment/structures visible (no people)
2. Person wearing complete PPE with no unsafe behavior

Otherwise, classify as VALID VIOLATION.

Analyze both the source and cropped images provided.
Respond with: FALSE POSITIVE: YES or FALSE POSITIVE: NO

Provide brief reasoning for your decision."""
        
    def encode_image(self, image_path):
        """Encode image to base64"""
        try:
            if os.path.exists(image_path):
                with open(image_path, 'rb') as f:
                    return base64.b64encode(f.read()).decode('utf-8')
        except:
            pass
        return None
    
    def test_single_case(self, case):
        """Test a single case with the complex prompt"""
        source_b64 = self.encode_image(case['source_image'])
        cropped_b64 = self.encode_image(case['cropped_image'])
        
        if not source_b64 or not cropped_b64:
            return None
        
        payload = {
            "model": self.vlm_model,
            "messages": [{
                "role": "user",
                "content": [
                    {"type": "text", "text": self.complex_prompt},
                    {"type": "text", "text": "\n\nSOURCE IMAGE:"},
                    {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{source_b64}"}},
                    {"type": "text", "text": "\n\nCROPPED IMAGE:"},
                    {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{cropped_b64}"}}
                ]
            }],
            "temperature": 0.1,
            "max_tokens": 800  # KEY CHANGE: 800 tokens instead of 300!
        }
        
        try:
            response = self.session.post(self.vlm_url, json=payload, timeout=60)
            
            if response.status_code == 200:
                vlm_response = response.json()['choices'][0]['message']['content']
                
                # Parse response
                predicted_fp = "FALSE POSITIVE: YES" in vlm_response
                actual_fp = case['is_false_positive']
                
                return {
                    'case_number': case['case_number'],
                    'actual_fp': actual_fp,
                    'predicted_fp': predicted_fp,
                    'correct': predicted_fp == actual_fp,
                    'response': vlm_response,
                    'response_length': len(vlm_response)
                }
                
        except Exception as e:
            print(f"Error processing {case['case_number']}: {str(e)[:50]}")
            
        return None
    
    def run_test(self, num_cases=100):
        """Run test on specified number of cases"""
        print("="*80)
        print("TESTING COMPLEX PROMPT WITH 800 TOKENS")
        print("="*80)
        print(f"Testing hypothesis: Token limit (300→800) was constraining performance")
        print(f"Previous result: 22.5% accuracy with 300 tokens")
        print(f"Testing on: {num_cases} cases")
        print("="*80)
        
        # Load test data
        with open('valo_batch_round3_complete.json', 'r') as f:
            all_cases = json.load(f)['results'][:num_cases]
        
        results = []
        start_time = time.time()
        
        for i, case in enumerate(all_cases):
            if i % 10 == 0:
                elapsed = time.time() - start_time
                if i > 0:
                    rate = i / elapsed
                    eta = (num_cases - i) / rate / 60
                    acc = sum(r['correct'] for r in results) / len(results) * 100 if results else 0
                    print(f"\nProgress: {i}/{num_cases} - Accuracy: {acc:.1f}% - ETA: {eta:.1f} min")
            
            result = self.test_single_case(case)
            
            if result:
                results.append(result)
                
                # Show first few examples
                if i < 5:
                    status = "✓" if result['correct'] else "✗"
                    print(f"\n{status} {result['case_number']}:")
                    print(f"   Response ({result['response_length']} chars): {result['response'][:150]}...")
                    print(f"   Predicted: {'FP' if result['predicted_fp'] else 'Valid'}, "
                          f"Actual: {'FP' if result['actual_fp'] else 'Valid'}")
            
            time.sleep(0.5)  # Rate limiting
        
        # Calculate final metrics
        print("\n" + "="*80)
        print("RESULTS: COMPLEX PROMPT WITH 800 TOKENS")
        print("="*80)
        
        if results:
            total = len(results)
            correct = sum(r['correct'] for r in results)
            
            # Separate metrics
            actual_fps = [r for r in results if r['actual_fp']]
            actual_valid = [r for r in results if not r['actual_fp']]
            
            fp_detected = sum(r['predicted_fp'] for r in actual_fps) if actual_fps else 0
            valid_protected = sum(not r['predicted_fp'] for r in actual_valid) if actual_valid else 0
            
            accuracy = correct/total*100
            
            print(f"Total processed: {total}/{num_cases}")
            print(f"Overall Accuracy: {accuracy:.1f}%")
            
            if actual_fps:
                print(f"FP Detection: {fp_detected}/{len(actual_fps)} ({fp_detected/len(actual_fps)*100:.1f}%)")
            if actual_valid:
                print(f"Valid Protection: {valid_protected}/{len(actual_valid)} ({valid_protected/len(actual_valid)*100:.1f}%)")
            
            print(f"Average response length: {sum(r['response_length'] for r in results)/len(results):.0f} chars")
            print(f"Time taken: {(time.time() - start_time)/60:.1f} minutes")
            
            # Compare with previous results
            print("\n" + "-"*60)
            print("COMPARISON:")
            print(f"With 300 tokens: 22.5% accuracy")
            print(f"With 800 tokens: {accuracy:.1f}% accuracy")
            
            if accuracy > 70:
                print("\n✅ SUCCESS! Token limit WAS the issue!")
                print("Your hypothesis was CORRECT!")
                print("The complex prompt works when given enough tokens.")
                recommendation = "Use the complex prompt with 800+ tokens in production."
            elif accuracy > 50:
                print("\n⚠️ IMPROVED but still below target")
                print("Token limit was part of the issue, but not the only one.")
                recommendation = "Consider refining the prompt or trying hybrid approach."
            else:
                print("\n❌ Token limit was NOT the main issue")
                print("Complex prompt still underperforms even with more tokens.")
                recommendation = "Need simpler approach or different strategy."
            
            # Save report
            report = {
                'test_name': 'Complex Prompt with 800 Tokens',
                'timestamp': datetime.now().isoformat(),
                'num_cases': num_cases,
                'processed': total,
                'accuracy': accuracy,
                'token_limit': 800,
                'previous_token_limit': 300,
                'previous_accuracy': 22.5,
                'improvement': accuracy - 22.5,
                'metrics': {
                    'fp_detection_rate': fp_detected/len(actual_fps)*100 if actual_fps else 0,
                    'valid_protection_rate': valid_protected/len(actual_valid)*100 if actual_valid else 0,
                    'avg_response_length': sum(r['response_length'] for r in results)/len(results)
                },
                'conclusion': recommendation,
                'sample_results': results[:10]
            }
            
            with open('complex_prompt_800_tokens_report.json', 'w') as f:
                json.dump(report, f, indent=2)
            
            print(f"\n📊 Report saved to: complex_prompt_800_tokens_report.json")
            print(f"\n🎯 RECOMMENDATION: {recommendation}")

if __name__ == "__main__":
    tester = ComplexPromptTester()
    tester.run_test(num_cases=100)