# AI-FARM - AI-Powered Safety Violation Detection System

AI-FARM is a comprehensive system designed to solve the false positive crisis in safety violation detection systems. Using Vision Language Models (VLMs), it intelligently filters safety alerts, reducing false positives by 70% and saving significant manual review time.

## 🚀 Quick Start

### Method 1: NPM Scripts (Recommended)
```bash
# Install dependencies
npm run install:all

# Configure environment
cp .env.example .env
# Edit .env and add your VLM API key

# Start development environment
npm run dev
```

### Method 2: Docker (Production-like)
```bash
# Start with Docker Compose
npm run docker:up

# Or manually:
docker-compose -f docker/docker-compose.yml up -d
```

### Method 3: Legacy Scripts
```bash
# Traditional startup (still supported)
./start.sh
```

**Access Points:**
- 🌐 **Web Application**: http://localhost:3000
- 🔧 **Backend API**: http://localhost:8000
- 📚 **API Documentation**: http://localhost:8000/docs
- 📊 **Health Check**: http://localhost:8000/health

### Stop Application

```bash
# NPM scripts
npm run docker:down

# Or legacy script
./stop.sh
```

## 📁 Project Structure

```
ai-farm/
├── backend/                 # Python FastAPI backend
│   ├── app/
│   │   ├── api/            # REST API endpoints
│   │   ├── core/           # Configuration & database
│   │   ├── models/         # SQLAlchemy data models
│   │   ├── schemas/        # Pydantic validation schemas
│   │   ├── services/       # Business logic (VLM, batch processing)
│   │   └── utils/          # Utility functions
│   ├── tests/              # Backend tests (pytest)
│   ├── data/               # Data processing
│   ├── logs/               # Application logs
│   └── requirements.txt    # Python dependencies
├── frontend/               # React TypeScript application
│   ├── src/
│   │   ├── components/     # Reusable UI components
│   │   ├── pages/          # Application pages
│   │   ├── services/       # API integration
│   │   ├── hooks/          # Custom React hooks
│   │   └── types/          # TypeScript definitions
│   ├── public/             # Static assets
│   └── package.json        # Frontend dependencies
├── mcp-server/             # Model Context Protocol server
│   ├── src/                # MCP implementation (TypeScript)
│   └── dist/               # Compiled output
├── docker/                 # Docker configurations
│   ├── docker-compose.yml  # Development environment
│   └── docker-compose.prod.yml # Production environment
├── database/               # Database setup
│   └── init/               # PostgreSQL initialization scripts
├── docs/                   # Comprehensive documentation
│   ├── architecture/       # System design
│   ├── api/                # API reference
│   ├── deployment/         # Deployment guides
│   └── development/        # Development setup
├── scripts/                # Utility scripts
│   └── image-copy/         # VALO image processing tools
├── data/                   # Sample and test data
├── logs/                   # Runtime logs
├── package.json            # Root project configuration & scripts
├── .gitignore              # Git ignore patterns
└── CLAUDE.md               # Claude Code development guidance
```

## Core Problem & Solution

**Problem**: Safety monitoring systems generate 97% false positive alerts, overwhelming safety teams with thousands of useless alarms while real violations might slip through.

**Solution**: AI-FARM processes safety alert images through VLM analysis to automatically identify and filter false positives, allowing teams to focus on genuine safety violations.

## Key Features

- **VLM Integration**: OpenAI-compatible API for image analysis
- **Batch Processing**: Handle thousands of images efficiently
- **Auto-Learning**: Adapts to customer-specific patterns
- **Real-time Dashboard**: Live demo interface for customer presentations
- **ROI Calculator**: Automatic cost savings calculations
- **Mathematical Pattern**: Direct case_number ↔ pk_event mapping

## Demo Metrics

- **Target**: 70% false positive reduction
- **ROI**: $300K+ annual savings potential
- **Payback**: 3-4 months
- **Processing**: Real-time batch analysis

## 🛠️ Technology Stack

- **Backend**: Python + FastAPI + SQLAlchemy + Pydantic
- **Frontend**: React + TypeScript + Tailwind CSS + Create React App
- **Database**: PostgreSQL (production) / SQLite (development)
- **Additional Services**: Redis (caching), MCP Server (Puppeteer automation)
- **VLM API**: VLM-38B-AWQ endpoint (OpenAI-compatible)
- **Deployment**: Docker + Docker Compose
- **Testing**: Pytest (backend), Jest + React Testing Library (frontend), Puppeteer (E2E)
- **Development**: Hot reload, concurrent development servers, comprehensive linting

## 🛠️ Development Commands

### Available NPM Scripts
```bash
# Development
npm run dev                 # Start both frontend and backend
npm run dev:backend         # Start FastAPI backend only
npm run dev:frontend        # Start React frontend only

# Building
npm run build              # Build frontend for production
npm run build:frontend     # Same as above

# Testing
npm run test               # Run all tests
npm run test:backend       # Backend unit tests (pytest)
npm run test:frontend      # Frontend tests (Jest)
npm run test:e2e           # End-to-end tests (Puppeteer)

# Docker
npm run docker:up          # Start all services with Docker
npm run docker:down        # Stop all Docker services
npm run docker:build       # Build Docker containers
npm run start:production   # Start production environment

# Utilities
npm run install:all        # Install all dependencies
npm run clean              # Clean node_modules and cache
npm run mcp:setup          # Setup MCP servers for Claude
```

## 📚 Quick Links

- **[Development Setup](docs/development/setup.md)** - Complete development environment setup
- **[API Documentation](docs/api/)** - REST API reference
- **[Architecture Overview](docs/architecture/)** - System design and data flow
- **[VLM-38B-AWQ Integration](docs/VLM_38B_AWQ_INTEGRATION.md)** - Vision Language Model integration guide
- **[Image Copy Scripts](scripts/image-copy/)** - VALO system data extraction
- **[Deployment Guide](docs/deployment/)** - Ubuntu production deployment

## Business Impact

### Current State (Customer Pain)
- 17,268 alerts/month with 97% false positive rate
- 862 hours/month wasted on manual review
- $43,100/month in salary costs
- Alert fatigue and missed real violations

### AI-FARM Solution
- 70% false positive reduction
- 277 hours vs 862 hours monthly review time
- $351,000 annual savings
- Focus on genuine safety violations

## Getting Help

- Check [docs/](docs/) for comprehensive documentation
- Review [CLAUDE.md](CLAUDE.md) for development standards
- See [scripts/image-copy/](scripts/image-copy/) for data extraction tools
- Test suite available in [backend/tests/](backend/tests/)

## License

Internal Sensen project - All rights reserved.