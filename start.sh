#!/bin/bash

# AI-FARM Complete Application Launcher
echo "🚀 Starting AI-FARM Complete Application"
echo "========================================"

# Navigate to project directory
cd "$(dirname "$0")"

# Check .env file
if [ ! -f ".env" ]; then
    echo "❌ .env file missing"
    echo "📝 Creating from template..."
    cp .env.example .env
    echo ""
    echo "⚠️  SETUP REQUIRED:"
    echo "   Edit .env and add your OpenAI API key:"
    echo "   VLM_API_KEY=your-openai-api-key-here"
    echo ""
    exit 1
fi

# Check VLM API configuration
if grep -q "your-openai-api-key-here" .env || ! grep -q "VLM_API_BASE_URL=" .env || ! grep -q "VLM_API_KEY=" .env; then
    echo "❌ VLM API configuration not set"
    echo "📝 Please verify .env file has VLM settings configured:"
    echo "   VLM_API_BASE_URL should be set to a valid endpoint"
    echo "   VLM_API_KEY should be set to a valid API key"
    echo "   VLM_MODEL_NAME should be set to a valid model name"
    exit 1
fi

# Check that VLM configuration values are not empty or placeholder
VLM_BASE_URL=$(grep "VLM_API_BASE_URL=" .env | cut -d'=' -f2)
VLM_KEY=$(grep "VLM_API_KEY=" .env | cut -d'=' -f2)
if [ -z "$VLM_BASE_URL" ] || [ -z "$VLM_KEY" ] || [ "$VLM_KEY" = "your-api-key-here" ]; then
    echo "❌ VLM API configuration incomplete"
    echo "📝 Please set valid values for VLM_API_BASE_URL and VLM_API_KEY in .env file"
    exit 1
fi

echo "✅ Configuration ready"

# Function to check if port is in use
port_in_use() {
    lsof -i :$1 >/dev/null 2>&1
}

# Start Backend
echo "🔧 Starting Backend..."
if port_in_use 8001; then
    echo "✅ Backend already running on port 8001"
else
    cd backend
    
    # Setup Python environment
    if [ ! -d "venv" ]; then
        echo "📦 Creating Python environment..."
        python3 -m venv venv
    fi
    
    source venv/bin/activate
    
    if ! python -c "import fastapi" 2>/dev/null; then
        echo "📦 Installing Python dependencies..."
        pip install -q --upgrade pip
        pip install -q -r requirements-simple.txt
    fi
    
    echo "🚀 Starting backend server..."
    nohup python3 -m uvicorn app.main:app --host 0.0.0.0 --port 8001 \
        --limit-max-requests 1000 \
        --limit-concurrency 100 \
        --timeout-keep-alive 65 \
        --h11-max-incomplete-event-size ********** > ../logs/backend.log 2>&1 &
    echo $! > ../logs/backend.pid

    # Wait for backend
    for i in {1..15}; do
        if curl -s http://localhost:8001/health >/dev/null 2>&1; then
            echo "✅ Backend ready on http://localhost:8001"
            break
        fi
        sleep 1
    done
    
    cd ..
fi

# Start Frontend (React Application)
echo "🎨 Starting Frontend..."
if port_in_use 3000; then
    echo "✅ Frontend already running on port 3000"
else
    cd frontend
    
    if [ ! -d "node_modules" ]; then
        echo "📦 Installing Node dependencies..."
        npm install
    fi
    
    echo "🚀 Starting React frontend..."
    nohup npm start > ../logs/frontend.log 2>&1 &
    echo $! > ../logs/frontend.pid
    
    # Wait for frontend
    for i in {1..30}; do
        if curl -s http://localhost:3000 >/dev/null 2>&1; then
            echo "✅ Frontend ready on http://localhost:3000"
            break
        fi
        sleep 2
    done
    
    cd ..
fi

echo ""
echo "🎉 AI-FARM System Started Successfully!"
echo "======================================="
echo "🌐 Web Application: http://localhost:3000"
echo "🔧 Backend API:           http://localhost:8001"
echo "📚 API Documentation:     http://localhost:8001/docs"
echo ""
echo "📊 Key Features:"
echo "  - AI-powered safety violation detection"
echo "  - Real-time image processing and analysis"
echo "  - Batch processing capabilities"
echo "  - ROI calculations and insights"
echo ""
echo "💡 To stop: ./stop.sh"
echo "📋 Logs: tail -f logs/backend.log logs/frontend.log"