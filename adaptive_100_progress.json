{"timestamp": "2025-07-25T14:03:28.337563", "current_thresholds": {"structure": 101, "person": 34, "ppe_compliant": 90, "behavioral": 40}, "exploration_history": [{"round": 1, "thresholds": {"structure": 99, "person": 30, "ppe_compliant": 90, "behavioral": 40}, "metrics": {"valid_protection": 100.0, "valid_protected": 15, "valid_total": 15, "valid_missed": 0, "fp_detection": 0.0, "fp_detected": 0, "fp_total": 15}, "sample_size": 30}, {"round": 2, "thresholds": {"structure": 100, "person": 32, "ppe_compliant": 90, "behavioral": 40}, "metrics": {"valid_protection": 100.0, "valid_protected": 15, "valid_total": 15, "valid_missed": 0, "fp_detection": 0.0, "fp_detected": 0, "fp_total": 15}, "sample_size": 30}], "best_config": {"round": 1, "thresholds": {"structure": 99, "person": 30, "ppe_compliant": 90, "behavioral": 40}, "metrics": {"valid_protection": 100.0, "valid_protected": 15, "valid_total": 15, "valid_missed": 0, "fp_detection": 0.0, "fp_detected": 0, "fp_total": 15}}}