#!/bin/bash

# VALO Integrated System Startup Script
# Quick launcher for the VALO system from root directory

echo "=========================================="
echo "VALO Integrated System Startup"
echo "=========================================="
echo

# Check if Docker is available
if command -v docker-compose &> /dev/null; then
    echo "🐳 Docker Compose detected"
    echo
    echo "Choose startup method:"
    echo "1) Docker (Recommended - Full stack with PostgreSQL)"
    echo "2) Local Python (Advanced - Requires manual database setup)"
    echo "3) Setup only (Install dependencies and configure)"
    echo
    read -p "Enter your choice (1-3): " choice
    
    case $choice in
        1)
            echo "Starting VALO system with Docker..."
            echo
            
            # Copy environment file if doesn't exist
            if [ ! -f ".env" ]; then
                echo "Creating .env file from template..."
                cp .env.example .env
                echo "⚠️  Please edit .env file with your PostgreSQL password"
                echo
            fi
            
            echo "Building and starting containers..."
            docker-compose up --build
            ;;
        2)
            echo "Starting local Python setup..."
            cd valo_integrated_system
            
            # Check if setup was run
            if [ ! -f "config.yaml" ]; then
                echo "Running setup first..."
                ./setup.sh
            fi
            
            # Check virtual environment
            if [ ! -d "venv" ]; then
                echo "Creating virtual environment..."
                python3 -m venv venv
                source venv/bin/activate
                pip install -r requirements.txt
            else
                source venv/bin/activate
            fi
            
            # Start the system
            python orchestrator.py dashboard
            ;;
        3)
            echo "Running setup..."
            cd valo_integrated_system
            ./setup.sh
            echo
            echo "Setup complete! Run this script again to start the system."
            ;;
        *)
            echo "Invalid choice. Exiting."
            exit 1
            ;;
    esac
else
    echo "Docker not detected. Using local Python setup..."
    cd valo_integrated_system
    
    if [ ! -f "config.yaml" ]; then
        echo "Running setup..."
        ./setup.sh
    fi
    
    # Activate virtual environment and start
    source venv/bin/activate 2>/dev/null || {
        echo "Creating virtual environment..."
        python3 -m venv venv
        source venv/bin/activate
        pip install -r requirements.txt
    }
    
    python orchestrator.py dashboard
fi