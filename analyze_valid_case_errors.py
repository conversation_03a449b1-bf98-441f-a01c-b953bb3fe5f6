#!/usr/bin/env python3
"""
Analyze why valid violations are being incorrectly marked as false positives
Critical for safety - we cannot miss real violations!
"""

import json
from collections import defaultdict

def analyze_valid_errors():
    """Analyze patterns in valid cases marked as FP"""
    
    print("ANALYZING VALID VIOLATION ERRORS")
    print("="*60)
    print("CRITICAL: System is marking 94% of real violations as false alarms!")
    print()
    
    # Load results
    with open('enhanced_1250_progress.json', 'r') as f:
        data = json.load(f)
    
    results = data['results']
    
    # Get valid cases that were incorrectly marked as FP
    valid_errors = []
    for r in results:
        if not r['actual_fp'] and r['predicted_fp']:  # Valid case marked as FP
            valid_errors.append(r)
    
    print(f"Total valid violations: {sum(1 for r in results if not r['actual_fp'])}")
    print(f"Incorrectly marked as FP: {len(valid_errors)}")
    print(f"Error rate: {len(valid_errors)/sum(1 for r in results if not r['actual_fp'])*100:.1f}%")
    
    # Analyze by violation type
    print("\n\nERROR PATTERNS BY VIOLATION TYPE:")
    print("-"*60)
    
    violation_errors = defaultdict(list)
    for error in valid_errors:
        violation_errors[error['infringement_type']].append(error)
    
    for vtype, errors in sorted(violation_errors.items(), key=lambda x: len(x[1]), reverse=True):
        print(f"\n{vtype}: {len(errors)} errors")
        
        # Analyze entity detection
        entities = defaultdict(int)
        ppe_statuses = defaultdict(int)
        
        for e in errors:
            entities[e.get('entity_type', 'UNKNOWN')] += 1
            ppe_statuses[e.get('ppe_status', 'N/A')] += 1
        
        print(f"  Entity detection:")
        for entity, count in sorted(entities.items(), key=lambda x: x[1], reverse=True):
            print(f"    - {entity}: {count}")
        
        print(f"  PPE status:")
        for status, count in sorted(ppe_statuses.items(), key=lambda x: x[1], reverse=True):
            print(f"    - {status}: {count}")
        
        # Show sample remarks
        print(f"  Sample remarks:")
        for e in errors[:5]:
            if e.get('remarks'):
                print(f"    - {e['remarks'][:80]}...")
    
    # Analyze common patterns
    print("\n\nCOMMON PATTERNS IN ERRORS:")
    print("-"*60)
    
    # Count entity types in errors
    entity_counts = defaultdict(int)
    ppe_counts = defaultdict(int)
    
    for e in valid_errors:
        entity_counts[e.get('entity_type', 'UNKNOWN')] += 1
        ppe_counts[e.get('ppe_status', 'N/A')] += 1
    
    print("\nEntity detection in valid violations:")
    for entity, count in sorted(entity_counts.items(), key=lambda x: x[1], reverse=True):
        pct = count / len(valid_errors) * 100
        print(f"  {entity}: {count} ({pct:.1f}%)")
    
    print("\nPPE status in valid violations:")
    for status, count in sorted(ppe_counts.items(), key=lambda x: x[1], reverse=True):
        pct = count / len(valid_errors) * 100
        print(f"  {status}: {count} ({pct:.1f}%)")
    
    # Find specific problem cases
    print("\n\nSPECIFIC PROBLEM PATTERNS:")
    print("-"*60)
    
    # Cases where person detected but still marked as FP
    person_fp = [e for e in valid_errors if e.get('entity_type') == 'PERSON']
    print(f"\n1. PERSON detected but marked as FP: {len(person_fp)} cases")
    for e in person_fp[:5]:
        print(f"   - {e['case_number']}: {e.get('ppe_status', 'N/A')} - {e.get('remarks', '')[:60]}...")
    
    # Cases marked as structure but are valid violations
    structure_valid = [e for e in valid_errors if e.get('entity_type') == 'STRUCTURE']
    print(f"\n2. STRUCTURE detected on valid violation: {len(structure_valid)} cases")
    for e in structure_valid[:5]:
        print(f"   - {e['case_number']}: {e['infringement_type']} - {e.get('remarks', '')[:60]}...")
    
    # Save detailed analysis
    analysis = {
        'total_valid_cases': sum(1 for r in results if not r['actual_fp']),
        'valid_errors': len(valid_errors),
        'error_rate': len(valid_errors)/sum(1 for r in results if not r['actual_fp'])*100,
        'entity_distribution': dict(entity_counts),
        'ppe_distribution': dict(ppe_counts),
        'violation_breakdown': {k: len(v) for k, v in violation_errors.items()},
        'sample_errors': valid_errors[:20]  # Save 20 examples
    }
    
    with open('valid_violation_error_analysis.json', 'w') as f:
        json.dump(analysis, f, indent=2)
    
    print(f"\n\nDetailed analysis saved to: valid_violation_error_analysis.json")
    
    # Recommendations
    print("\n\nRECOMMENDATIONS:")
    print("-"*60)
    print("1. The prompt is too aggressive - marking everything as FP")
    print("2. Even when PERSON is detected, it's still marking as FP")
    print("3. Need to adjust confidence thresholds and decision logic")
    print("4. Consider violation-specific prompts with different thresholds")

if __name__ == "__main__":
    analyze_valid_errors()