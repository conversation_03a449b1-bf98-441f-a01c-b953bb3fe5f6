#!/usr/bin/env python3
"""
Comprehensive test script for VLM API integration at **************:9500
Tests both with and without API key authentication to determine requirements
"""

import asyncio
import base64
import httpx
import json
import sys
from typing import Optional


def create_test_image_base64():
    """Create a minimal test image as base64"""
    # 1x1 blue pixel PNG
    return "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="


async def test_vlm_endpoint_with_auth(api_key: Optional[str] = None):
    """Test the VLM endpoint with optional authentication"""
    auth_type = "with API key" if api_key else "without API key"
    print(f"🔍 Testing VLM endpoint {auth_type}")
    print("-" * 50)
    
    base64_image = create_test_image_base64()
    
    # OpenAI-compatible request format
    payload = {
        "model": "VLM-38B-AWQ",
        "messages": [
            {
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": "Describe this image briefly"
                    },
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": f"data:image/png;base64,{base64_image}"
                        }
                    }
                ]
            }
        ],
        "temperature": 0,
        "max_tokens": 100
    }
    
    # Headers with optional authentication
    headers = {
        "Content-Type": "application/json"
    }
    if api_key:
        headers["Authorization"] = f"Bearer {api_key}"
    
    print(f"📤 Request URL: http://**************:9500/v1/chat/completions")
    print(f"📤 Request Headers: {headers}")
    print(f"📤 Model: {payload['model']}")
    print()
    
    try:
        async with httpx.AsyncClient(timeout=30) as client:
            print("🔄 Sending request...")
            response = await client.post(
                "http://**************:9500/v1/chat/completions",
                headers=headers,
                json=payload
            )
            
            print(f"📥 Response Status: {response.status_code}")
            print(f"📥 Response Headers: {dict(response.headers)}")
            print()
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ SUCCESS! VLM API responded correctly {auth_type}")
                print(f"📥 Response Body: {json.dumps(result, indent=2)}")
                
                # Validate response format
                if "choices" in result and len(result["choices"]) > 0:
                    message_content = result["choices"][0].get("message", {}).get("content", "")
                    print(f"🤖 AI Response: {message_content}")
                    print(f"✅ Response format is valid OpenAI-compatible format")
                    return True, result
                else:
                    print("⚠️  Response format may not be standard OpenAI format")
                    return True, result
            else:
                print(f"❌ FAILED! HTTP {response.status_code} {auth_type}")
                print(f"📥 Error Response: {response.text}")
                return False, None
                
    except Exception as e:
        print(f"❌ ERROR {auth_type}: {str(e)}")
        return False, None


async def test_models_endpoint(api_key: Optional[str] = None):
    """Test the models endpoint"""
    auth_type = "with API key" if api_key else "without API key"
    print(f"\n🔍 Testing models endpoint {auth_type}")
    print("-" * 40)
    
    headers = {}
    if api_key:
        headers["Authorization"] = f"Bearer {api_key}"
    
    try:
        async with httpx.AsyncClient(timeout=10) as client:
            response = await client.get(
                "http://**************:9500/v1/models",
                headers=headers
            )
            
            if response.status_code == 200:
                models = response.json()
                print(f"✅ Models endpoint available {auth_type}")
                print(f"📥 Models: {json.dumps(models, indent=2)}")
                return True, models
            else:
                print(f"⚠️  Models endpoint returned HTTP {response.status_code} {auth_type}")
                print(f"📥 Response: {response.text}")
                return False, None
                
    except Exception as e:
        print(f"⚠️  Models endpoint error {auth_type}: {str(e)}")
        return False, None


async def test_backend_integration():
    """Test backend VLM service integration"""
    print(f"\n🔍 Testing Backend VLM Service Integration")
    print("-" * 50)
    
    try:
        # Import backend modules
        import sys
        from pathlib import Path
        backend_path = Path(__file__).parent / "backend"
        sys.path.insert(0, str(backend_path))
        
        from app.core.config import Settings
        
        # Create settings instance to test configuration loading
        settings = Settings()
        
        print(f"✅ Backend Configuration Loaded:")
        print(f"   - VLM API Base URL: {settings.vlm_api_base_url}")
        print(f"   - VLM API Key: {'***' if settings.vlm_api_key else 'Not set'}")
        print(f"   - VLM Model Name: {settings.vlm_model_name}")
        print(f"   - VLM Max Tokens: {settings.vlm_max_tokens}")
        print(f"   - VLM Temperature: {settings.vlm_temperature}")
        print(f"   - VLM Timeout: {settings.vlm_timeout_seconds}")
        print(f"   - VLM API Headers: {settings.vlm_api_headers}")
        
        # Test if configuration matches expected values
        expected_base_url = "http://**************:9500/v1"
        expected_model = "VLM-38B-AWQ"
        
        config_ok = (
            settings.vlm_api_base_url == expected_base_url and
            settings.vlm_model_name == expected_model
        )
        
        if config_ok:
            print(f"✅ Backend configuration is correct")
            return True
        else:
            print(f"❌ Backend configuration mismatch:")
            print(f"   Expected Base URL: {expected_base_url}")
            print(f"   Actual Base URL: {settings.vlm_api_base_url}")
            print(f"   Expected Model: {expected_model}")
            print(f"   Actual Model: {settings.vlm_model_name}")
            return False
            
    except Exception as e:
        print(f"❌ Backend integration test failed: {str(e)}")
        return False


async def main():
    """Main test function"""
    print("🚀 VALO AI-FARM External VLM Integration Test")
    print("Testing VLM API at **************:9500")
    print("=" * 60)
    
    # Test with API key
    print("🔑 Testing WITH API Key (token-abc123)")
    print("=" * 60)
    chat_with_key, response_with_key = await test_vlm_endpoint_with_auth("token-abc123")
    models_with_key, models_data_with_key = await test_models_endpoint("token-abc123")
    
    # Test without API key
    print("\n🔓 Testing WITHOUT API Key")
    print("=" * 60)
    chat_without_key, response_without_key = await test_vlm_endpoint_with_auth(None)
    models_without_key, models_data_without_key = await test_models_endpoint(None)
    
    # Test backend integration
    backend_ok = await test_backend_integration()
    
    print("\n" + "=" * 60)
    print("📊 Test Results Summary:")
    print(f"{'✅' if chat_with_key else '❌'} Chat Completions WITH API Key: {'PASS' if chat_with_key else 'FAIL'}")
    print(f"{'✅' if chat_without_key else '❌'} Chat Completions WITHOUT API Key: {'PASS' if chat_without_key else 'FAIL'}")
    print(f"{'✅' if models_with_key else '⚠️ '} Models Endpoint WITH API Key: {'PASS' if models_with_key else 'NOT AVAILABLE'}")
    print(f"{'✅' if models_without_key else '⚠️ '} Models Endpoint WITHOUT API Key: {'PASS' if models_without_key else 'NOT AVAILABLE'}")
    print(f"{'✅' if backend_ok else '❌'} Backend Configuration: {'PASS' if backend_ok else 'FAIL'}")
    
    # Determine authentication requirement
    auth_required = chat_with_key and not chat_without_key
    auth_optional = chat_with_key and chat_without_key
    auth_not_working = not chat_with_key and not chat_without_key
    
    print(f"\n🔐 Authentication Analysis:")
    if auth_required:
        print("✅ API Key REQUIRED - Server only accepts requests with valid API key")
        recommended_key = "token-abc123"
    elif auth_optional:
        print("✅ API Key OPTIONAL - Server accepts requests with or without API key")
        recommended_key = "token-abc123"  # Use key for consistency
    elif auth_not_working:
        print("❌ API UNAVAILABLE - Server not responding to either authentication method")
        recommended_key = None
    else:
        print("⚠️  UNUSUAL - Server only works without API key")
        recommended_key = None
    
    if chat_with_key or chat_without_key:
        print(f"\n🎉 SUCCESS! VLM API integration is working.")
        print(f"✅ Server: **************:9500")
        print(f"✅ Model: VLM-38B-AWQ")
        print(f"✅ Format: OpenAI-compatible")
        if recommended_key:
            print(f"✅ Recommended API Key: {recommended_key}")
        else:
            print(f"✅ No API Key required")
        
        print(f"\n🔧 Configuration Status:")
        print(f"   - Backend config: {'✅ CORRECT' if backend_ok else '❌ NEEDS UPDATE'}")
        print(f"   - Ready for production: {'✅ YES' if backend_ok else '❌ NO'}")
        
        return 0 if backend_ok else 1
    else:
        print(f"\n❌ FAILED! VLM API is not accessible.")
        print(f"🔧 Check server status and network connectivity")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
