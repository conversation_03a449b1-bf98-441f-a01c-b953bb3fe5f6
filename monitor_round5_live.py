#!/usr/bin/env python3
"""
Monitor Round 5 Context Analysis Progress
"""

import time
import os
import json
from datetime import datetime

print("="*80)
print("MONITORING ROUND 5: ADVANCED CONTEXT ANALYSIS")
print("Target: 55% FP Detection with 100% Valid Protection")
print("="*80)
print("\nCurrent baseline:")
print("  Round 3: 6.4% FP detection")
print("  Round 4: 34.4% FP detection")
print("  Gap to 70%: 35.6%")
print("\n" + "-"*80)

start_time = time.time()
last_progress = 0

while True:
    # Check if Round 5 completed
    if os.path.exists('valo_round5_context_complete.json'):
        print("\n\n✅ ROUND 5 COMPLETE!")
        
        with open('valo_round5_context_complete.json', 'r') as f:
            data = json.load(f)
            stats = data['stats']
        
        print(f"\nFinal Statistics:")
        print(f"  Total cases: {stats['total_cases']}")
        print(f"  Valid Protection: {stats['valid_protection_rate']:.1f}%")
        print(f"  FP Detection: {stats['fp_detection_rate']:.1f}%")
        print(f"  Improvement over Round 4: +{stats['improvement_over_round4']:.1f}%")
        print(f"  Total improvement: +{stats['total_improvement']:.1f}%")
        
        # Show top patterns
        if 'pattern_effectiveness' in stats:
            print("\nTop effective patterns:")
            patterns = stats['pattern_effectiveness']
            sorted_patterns = sorted(patterns.items(), key=lambda x: x[1], reverse=True)[:5]
            for pattern, count in sorted_patterns:
                print(f"  {pattern}: {count} dismissals")
        
        if stats['fp_detection_rate'] >= 70:
            print("\n🎯 TARGET ACHIEVED! 70% FP reduction reached!")
        else:
            print(f"\nGap to 70%: {70 - stats['fp_detection_rate']:.1f}%")
            print("Need Round 6 to continue optimization")
        
        print(f"\nTime taken: {(time.time() - start_time)/60:.1f} minutes")
        break
    
    # Check for 70% achievement file
    if os.path.exists('VALO_70_PERCENT_ACHIEVED.json'):
        print("\n\n🎯 70% TARGET ACHIEVED!")
        with open('VALO_70_PERCENT_ACHIEVED.json', 'r') as f:
            achievement = json.load(f)
        print(f"Rounds completed: {achievement['rounds_completed']}")
        if 'round_history' in achievement:
            print("\nRound progression:")
            for round_name, stats in achievement['round_history'].items():
                print(f"  {round_name}: {stats['fp_detection']:.1f}% FP, {stats['valid_protection']:.1f}% Valid")
        break
    
    # Read log file for progress
    if os.path.exists('round5_context_analysis.log'):
        try:
            with open('round5_context_analysis.log', 'r') as f:
                lines = f.readlines()
                
            # Find latest progress line
            for line in reversed(lines[-50:]):
                if 'Progress:' in line and '| Valid:' in line:
                    # Extract progress
                    try:
                        parts = line.strip().split('Progress: ')[1].split(' | ')
                        progress_str = parts[0]
                        current, total = map(int, progress_str.split('/'))
                        
                        # Extract rates
                        valid_rate = float(parts[1].split(': ')[1].replace('%', ''))
                        fp_rate = float(parts[2].split(': ')[1].replace('%', ''))
                        
                        # Calculate ETA
                        if current > last_progress and current > 100:
                            elapsed = time.time() - start_time
                            rate = current / elapsed
                            remaining = (total - current) / rate if rate > 0 else 0
                            eta_min = remaining / 60
                            
                            # Show progress
                            progress_pct = current/total*100
                            print(f"\r[{datetime.now().strftime('%H:%M:%S')}] Progress: {current}/{total} ({progress_pct:.1f}%) | Valid: {valid_rate:.1f}% | FP: {fp_rate:.1f}% | ETA: {eta_min:.1f} min", end='', flush=True)
                            
                            # Show milestone messages
                            if current % 250 == 0 and current != last_progress:
                                improvement = fp_rate - 34.4  # vs Round 4
                                print(f"\n  → At {current} cases: +{improvement:.1f}% improvement over Round 4")
                            
                            last_progress = current
                        break
                    except:
                        pass
                    
        except Exception as e:
            pass
    
    time.sleep(3)

print("\n\nRound 5 monitoring complete!")