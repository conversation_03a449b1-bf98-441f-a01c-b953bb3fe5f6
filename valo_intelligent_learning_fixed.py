#!/usr/bin/env python3
"""
VALO Intelligent Learning Processor - FIXED VERSION
Priority: 100% Valid Protection Rate (NEVER dismiss valid safety violations)
"""

import asyncio
import json
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import os
import redis
from collections import defaultdict

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class SafetyFirstIntelligentProcessor:
    """Fixed intelligent learning processor with 100% valid protection priority"""
    
    def __init__(self):
        self.data_dir = Path("/home/<USER>/VALO_AI-FARM_2025/ai_farm_images_fixed_250703/ai_farm_images_fixed")
        self.vlm_service = None
        self.all_cases = []
        self.all_results = []
        self.chunk_size = 8
        
        # Initialize Redis for caching
        self.redis_client = redis.Redis(host='localhost', port=6379, decode_responses=True)
        
        # Learning parameters - SAFETY FIRST
        self.learning_params = {
            'confidence_thresholds': {
                'PPE Non-compliance': 95,  # Increased from 90
                'One man Lashing': 95,      # Increased from 90
                'Ex.Row Violation': 95,     # Increased from 90
                '2-Container Distance': 95,  # Increased from 90
                'default': 95               # Increased from 85
            },
            'safety_keywords': [
                'person', 'worker', 'operator', 'driver', 'human', 'individual',
                'man', 'woman', 'employee', 'staff', 'personnel'
            ],
            'critical_safety_terms': [
                'not fasten', 'no helmet', 'no vest', 'without ppe', 'missing safety',
                'violation', 'unsafe', 'dangerous', 'risk', 'hazard'
            ]
        }
        
        # Initialize reasoning patterns
        self.reasoning_patterns = {
            'false_positive_indicators': defaultdict(int),
            'true_positive_indicators': defaultdict(int),
            'equipment_keywords': set(),
            'safety_violation_keywords': set(),
            'common_misinterpretations': defaultdict(list),
            'context_clues': defaultdict(list)
        }
    
    def load_all_cases(self) -> List[Dict]:
        """Load all cases from CSV data"""
        csv_path = "/home/<USER>/VALO_AI-FARM_2025/ai_farm_images_fixed_250703/psa_valo_violation_report_2025-07-03_14-14-01.CSV"
        logger.info(f"Loading cases from: {csv_path}")
        
        cases = []
        try:
            with open(csv_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                
            headers = lines[0].strip().split(',')
            
            for line in lines[1:]:
                parts = line.strip().split(',')
                if len(parts) >= len(headers):
                    case_data = dict(zip(headers, parts))
                    
                    case_number = case_data.get('case_number', '').strip()
                    if not case_number:
                        continue
                    
                    # Determine if false positive based on key field
                    is_false_positive = case_data.get('key', '').strip().lower() == 'invalid'
                    folder = 'invalid' if is_false_positive else 'valid'
                    
                    # Build file paths
                    cropped_path = self.data_dir / folder / f"{case_number}_cropped_{folder}.JPEG"
                    source_path = self.data_dir / folder / f"{case_number}_source_{folder}.JPEG"
                    
                    if cropped_path.exists():
                        case = {
                            'case_number': case_number,
                            'cropped_image': str(cropped_path),
                            'source_image': str(source_path),
                            'terminal': case_data.get('terminal', '').strip(),
                            'camera_id': case_data.get('device_id', '').strip(),
                            'infringement_type': case_data.get('infringement_type', '').strip(),
                            'alert_status': case_data.get('alert_status', '').strip(),
                            'remarks': case_data.get('remarks', '').strip(),
                            'is_false_positive': is_false_positive
                        }
                        cases.append(case)
                
        except Exception as e:
            logger.error(f"Error loading cases: {e}")
            
        logger.info(f"Loaded {len(cases)} cases")
        return cases
    
    async def analyze_with_vlm(self, image_path: str, prompt: str, case: Dict) -> Dict:
        """Analyze image with VLM using safety-first approach"""
        if not self.vlm_service:
            from backend.app.services.vlm_service import VLMService
            self.vlm_service = VLMService()
        
        try:
            # Analyze with VLM
            result = await self.vlm_service.analyze_safety_image(
                image_path=image_path,
                prompt=prompt
            )
            
            # Parse VLM response with SAFETY FIRST bias
            person_detected = result.get('person_detected', 'possibly')
            confidence = result.get('confidence', 50)
            reasoning = result.get('reasoning', '')
            
            # CRITICAL: Check for safety violation keywords
            has_safety_keywords = any(
                keyword.lower() in reasoning.lower() 
                for keyword in self.learning_params['critical_safety_terms']
            )
            
            # Check human remarks for critical safety info
            remarks_lower = case.get('remarks', '').lower()
            has_critical_remarks = any(
                term in remarks_lower 
                for term in ['not fasten', 'no helmet', 'no vest', 'without', 'missing']
            )
            
            # SAFETY FIRST DECISION LOGIC
            if has_safety_keywords or has_critical_remarks:
                # ALWAYS flag if safety terms detected
                is_fp_predicted = False
                vlm_decision = "FLAG_FOR_REVIEW"
                confidence = min(confidence, 50)  # Lower confidence to ensure review
            elif person_detected == 'yes' or person_detected == 'possibly':
                # Person detected - ALWAYS flag for safety
                is_fp_predicted = False
                vlm_decision = "FLAG_FOR_REVIEW"
            elif person_detected == 'no' and confidence >= 90:
                # Only dismiss if VERY confident no person present
                is_fp_predicted = True
                vlm_decision = "DISMISS_WITH_CAUTION"
            else:
                # When in doubt, FLAG FOR REVIEW
                is_fp_predicted = False
                vlm_decision = "FLAG_FOR_REVIEW"
            
            return {
                'vlm_decision': vlm_decision,
                'person_detected': person_detected,
                'confidence': confidence,
                'is_false_positive_predicted': is_fp_predicted,
                'reasoning': reasoning,
                'has_safety_keywords': has_safety_keywords,
                'has_critical_remarks': has_critical_remarks,
                'processing_error': False
            }
            
        except Exception as e:
            logger.error(f"VLM analysis error for {case['case_number']}: {e}")
            # On error, ALWAYS flag for review
            return {
                'vlm_decision': 'FLAG_FOR_REVIEW',
                'person_detected': 'error',
                'confidence': 0,
                'is_false_positive_predicted': False,
                'reasoning': f'Error during analysis: {str(e)}',
                'processing_error': True
            }
    
    def generate_safety_first_prompt(self, case: Dict, round_num: int) -> str:
        """Generate prompt with 100% valid protection priority"""
        
        prompt = f"""
🚨 SAFETY FIRST ANALYSIS - CRITICAL PRIORITY 🚨

YOUR #1 RULE: NEVER DISMISS A VALID SAFETY VIOLATION
- If unsure, ALWAYS flag for human review
- Valid safety violations must NEVER be filtered out
- It's better to flag 100 false positives than miss 1 real violation

ROUND {round_num} SAFETY ANALYSIS
Current Performance:
- Valid Protection Rate: MUST BE 100%
- False Positive Detection: Secondary priority only

⚠️ CRITICAL SAFETY CHECKS:
1. Is there ANY possibility of a person in the image?
2. Are there ANY safety-related keywords in the description?
3. Is there ANY doubt about the safety status?

If ANY answer is YES → FLAG FOR REVIEW

SAFETY KEYWORDS TO WATCH FOR:
- Person/Worker/Driver/Operator/Human/Individual
- Not fastened/No helmet/No vest/Without PPE/Missing safety
- Violation/Unsafe/Dangerous/Risk/Hazard

CURRENT CASE DETAILS:
- Case: {case['case_number']}
- Terminal: {case['terminal']}
- Camera: {case['camera_id']}
- Violation Type: {case['infringement_type']}
- Human Assessment: {case['alert_status']}
- Human Notes: {case.get('remarks', 'None')}

⚠️ SPECIAL ATTENTION TO REMARKS ⚠️
Human notes often contain critical safety information.
If remarks mention ANY safety concern → FLAG FOR REVIEW

DECISION FRAMEWORK (IN ORDER):
1. Check for any person/human presence → If YES or POSSIBLY → FLAG
2. Check for safety keywords in remarks → If YES → FLAG
3. Check if image is unclear/ambiguous → If YES → FLAG
4. Only if 100% certain it's equipment only → Consider dismissing

ONLY DISMISS IF ALL TRUE:
- Absolutely NO person visible (100% certain)
- Clearly shows ONLY equipment/machinery
- NO safety keywords in description
- Crystal clear image quality
- NO ambiguity whatsoever

Output Format:
PERSON_DETECTED: [yes/no/possibly]
CONFIDENCE: [0-100]
IS_FALSE_POSITIVE: [true/false]
SAFETY_DECISION: [FLAG_FOR_REVIEW/DISMISS_WITH_CAUTION]
REASONING: [Explain why, focusing on safety first]

REMEMBER: When in doubt, ALWAYS FLAG FOR REVIEW!
"""
        return prompt
    
    async def run_safety_first_round(self, round_num: int = 4):
        """Run a single round with safety-first approach"""
        logger.info(f"\n{'='*60}")
        logger.info(f"SAFETY FIRST ROUND {round_num}")
        logger.info(f"Priority: 100% Valid Protection Rate")
        logger.info(f"{'='*60}")
        
        # Load cases if not already loaded
        if not self.all_cases:
            self.all_cases = self.load_all_cases()
        
        round_results = []
        total_chunks = (len(self.all_cases) + self.chunk_size - 1) // self.chunk_size
        
        # Process statistics
        valid_cases_total = 0
        valid_cases_protected = 0
        fp_cases_total = 0
        fp_cases_detected = 0
        
        for chunk_idx in range(total_chunks):
            start_idx = chunk_idx * self.chunk_size
            end_idx = min(start_idx + self.chunk_size, len(self.all_cases))
            chunk = self.all_cases[start_idx:end_idx]
            
            logger.info(f"Processing chunk {chunk_idx + 1}/{total_chunks}")
            
            for case in chunk:
                # Generate safety-first prompt
                prompt = self.generate_safety_first_prompt(case, round_num)
                
                # Get VLM analysis
                vlm_result = await self.analyze_with_vlm(case['cropped_image'], prompt, case)
                
                # Combine results
                full_result = {**case, **vlm_result, 'round': round_num}
                
                # Calculate accuracy with safety priority
                if case['is_false_positive']:
                    # This is a false positive case
                    fp_cases_total += 1
                    full_result['correct_prediction'] = vlm_result['is_false_positive_predicted']
                    if vlm_result['is_false_positive_predicted']:
                        fp_cases_detected += 1
                else:
                    # This is a VALID case - MUST be protected
                    valid_cases_total += 1
                    full_result['correct_prediction'] = not vlm_result['is_false_positive_predicted']
                    full_result['valid_case_protected'] = not vlm_result['is_false_positive_predicted']
                    if not vlm_result['is_false_positive_predicted']:
                        valid_cases_protected += 1
                
                round_results.append(full_result)
            
            # Log progress
            if valid_cases_total > 0:
                valid_protection_rate = (valid_cases_protected / valid_cases_total) * 100
            else:
                valid_protection_rate = 100.0
                
            if fp_cases_total > 0:
                fp_detection_rate = (fp_cases_detected / fp_cases_total) * 100
            else:
                fp_detection_rate = 0.0
            
            logger.info(f"Progress: Valid Protection: {valid_protection_rate:.1f}% | FP Detection: {fp_detection_rate:.1f}%")
            
            # Save intermediate results
            if chunk_idx % 5 == 0:
                self.save_intermediate_results(round_results, round_num)
        
        # Save final round results
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"/home/<USER>/VALO_AI-FARM_2025/valo_safety_first_round{round_num}_results_{timestamp}.json"
        
        final_stats = {
            'round': round_num,
            'total_cases': len(round_results),
            'valid_cases_total': valid_cases_total,
            'valid_cases_protected': valid_cases_protected,
            'valid_protection_rate': valid_protection_rate,
            'fp_cases_total': fp_cases_total,
            'fp_cases_detected': fp_cases_detected,
            'fp_detection_rate': fp_detection_rate,
            'detailed_results': round_results,
            'timestamp': timestamp
        }
        
        with open(filename, 'w') as f:
            json.dump(final_stats, f, indent=2)
        
        logger.info(f"\n{'='*60}")
        logger.info(f"ROUND {round_num} COMPLETE")
        logger.info(f"Valid Protection Rate: {valid_protection_rate:.1f}% (Target: 100%)")
        logger.info(f"FP Detection Rate: {fp_detection_rate:.1f}%")
        logger.info(f"Results saved to: {filename}")
        logger.info(f"{'='*60}")
        
        return final_stats
    
    def save_intermediate_results(self, results: List[Dict], round_num: int):
        """Save intermediate results for monitoring"""
        filename = f"/home/<USER>/VALO_AI-FARM_2025/valo_safety_first_round{round_num}_intermediate.json"
        with open(filename, 'w') as f:
            json.dump({
                'round': round_num,
                'cases_processed': len(results),
                'timestamp': datetime.now().isoformat(),
                'results': results
            }, f, indent=2)


async def main():
    """Run safety-first intelligent learning"""
    processor = SafetyFirstIntelligentProcessor()
    
    try:
        # Run Round 4 with safety-first approach
        await processor.run_safety_first_round(round_num=4)
    except Exception as e:
        logger.error(f"Safety-first processing failed: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(main())