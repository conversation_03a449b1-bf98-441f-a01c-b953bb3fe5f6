#!/usr/bin/env python3
"""
VALO Adaptive 100% Valid Protection Finder
Starts with ultra-conservative settings and gradually relaxes
thresholds to find the best balance that maintains 100% valid protection
"""

import json
import base64
import requests
import os
from datetime import datetime
import time
import random

class Adaptive100PercentFinder:
    def __init__(self):
        self.vlm_url = "http://100.106.127.35:9500/v1/chat/completions"
        self.vlm_model = "VLM-38B-AWQ"
        self.session = requests.Session()
        
        # Start ultra-conservative
        self.thresholds = {
            'structure': 99,      # Start nearly impossible
            'person': 30,         # Very easy to detect person
            'ppe_compliant': 90,  # Very strict PPE
            'behavioral': 40      # Very sensitive
        }
        
        # Adjustment steps
        self.adjustment_steps = {
            'structure': -1,      # Gradually make it easier to identify structures
            'person': +2,         # Gradually require more confidence for person
            'ppe_compliant': -2,  # Gradually relax PPE strictness
            'behavioral': +2      # Gradually less sensitive
        }
        
        # Limits
        self.threshold_limits = {
            'structure': {'min': 85, 'max': 99},
            'person': {'min': 30, 'max': 70},
            'ppe_compliant': {'min': 60, 'max': 95},
            'behavioral': {'min': 40, 'max': 80}
        }
        
        self.exploration_history = []
        self.best_config = None
        self.cases_per_round = 30  # Test 30 cases per round
        
    def encode_image(self, image_path):
        """Encode image to base64"""
        try:
            if os.path.exists(image_path):
                with open(image_path, 'rb') as f:
                    return base64.b64encode(f.read()).decode('utf-8')
        except:
            pass
        return None
    
    def create_adaptive_prompt(self):
        """Create prompt with current thresholds"""
        
        # Load base prompt
        with open('FINAL_PRODUCTION_PROMPT.txt', 'r') as f:
            base_prompt = f.read()
        
        # Update main structure threshold
        prompt = base_prompt.replace(
            "A) INDUSTRIAL STRUCTURE (need >90% confidence)",
            f"A) INDUSTRIAL STRUCTURE (need >{self.thresholds['structure']}% confidence)"
        )
        
        # Add comprehensive threshold rules
        adaptive_rules = f"""

ADAPTIVE CONFIDENCE THRESHOLDS (ROUND-SPECIFIC):
================================
1. STRUCTURE IDENTIFICATION: >{self.thresholds['structure']}% confidence required
   - If confidence <{self.thresholds['structure']}%, it's NOT a structure
   - Even if it looks like equipment, low confidence = possible person

2. PERSON DETECTION: >{self.thresholds['person']}% confidence required
   - Any human-like features with >{self.thresholds['person']}% confidence = PERSON
   - Below threshold = UNCLEAR (default to safety)

3. PPE COMPLIANCE: >{self.thresholds['ppe_compliant']}% confidence required
   - Must be >{self.thresholds['ppe_compliant']}% sure PPE is properly worn
   - Any doubt about PPE = NON-COMPLIANT

4. BEHAVIORAL VIOLATIONS: >{self.thresholds['behavioral']}% confidence triggers
   - Any behavior with >{self.thresholds['behavioral']}% confidence = VIOLATION
   - This includes phone use, missing equipment, unsafe acts

CRITICAL SAFETY RULE:
When your confidence is between thresholds, DEFAULT TO SAFETY:
- Not sure if structure? → Might be a person → VALID VIOLATION
- Not sure about PPE? → Assume non-compliant → VALID VIOLATION
- Any uncertainty? → VALID VIOLATION

OUTPUT MUST INCLUDE:
- Your confidence percentages for each assessment
- Clear reasoning for your decision"""
        
        # Insert before decision section
        prompt = prompt.replace(
            "STEP 4: MAKE SAFETY DECISION",
            adaptive_rules + "\n\nSTEP 4: MAKE SAFETY DECISION"
        )
        
        return prompt
    
    def test_single_case(self, case, prompt):
        """Test a single case with detailed feedback"""
        
        # Encode images
        source_b64 = self.encode_image(case['source_image'])
        cropped_b64 = self.encode_image(case['cropped_image'])
        
        if not source_b64 or not cropped_b64:
            return None
        
        payload = {
            "model": self.vlm_model,
            "messages": [{
                "role": "user",
                "content": [
                    {"type": "text", "text": prompt},
                    {"type": "text", "text": "\n\nSOURCE IMAGE:"},
                    {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{source_b64}"}},
                    {"type": "text", "text": "\n\nCROPPED IMAGE:"},
                    {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{cropped_b64}"}}
                ]
            }],
            "temperature": 0.1,
            "max_tokens": 350
        }
        
        try:
            response = self.session.post(self.vlm_url, json=payload, timeout=30)
            if response.status_code == 200:
                vlm_response = response.json()['choices'][0]['message']['content']
                
                # Parse response
                predicted_fp = 'YES' in vlm_response.split('FALSE POSITIVE:')[1][:5]
                
                # Extract confidence values if mentioned
                confidence_info = self.extract_confidences(vlm_response)
                
                return {
                    'case_number': case['case_number'],
                    'actual_fp': case['is_false_positive'],
                    'predicted_fp': predicted_fp,
                    'correct': predicted_fp == case['is_false_positive'],
                    'confidence_info': confidence_info,
                    'response_snippet': vlm_response[:200]
                }
        except Exception as e:
            return None
    
    def extract_confidences(self, response):
        """Extract confidence values from response"""
        confidence = {}
        
        # Look for patterns like "X% confident" or "confidence: X%"
        import re
        patterns = [
            r'(\d+)%\s*confident',
            r'confidence[:\s]+(\d+)%',
            r'(\d+)%\s*sure',
            r'(\d+)%\s*certain'
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, response, re.IGNORECASE)
            if matches:
                confidence['mentioned'] = [int(m) for m in matches]
                break
        
        return confidence
    
    def test_round(self, round_num):
        """Test current thresholds on a sample of cases"""
        
        print(f"\n{'='*70}")
        print(f"ADAPTIVE ROUND {round_num}")
        print(f"{'='*70}")
        
        print(f"Current Thresholds:")
        for key, value in self.thresholds.items():
            print(f"  {key}: {value}%")
        
        # Load test data
        with open('valo_batch_round3_complete.json', 'r') as f:
            all_cases = json.load(f)['results']
        
        # Get balanced sample
        valid_cases = [c for c in all_cases if not c['is_false_positive']]
        fp_cases = [c for c in all_cases if c['is_false_positive']]
        
        # Sample cases
        test_valid = random.sample(valid_cases, min(15, len(valid_cases)))
        test_fp = random.sample(fp_cases, min(15, len(fp_cases)))
        test_cases = test_valid + test_fp
        random.shuffle(test_cases)
        
        print(f"\nTesting {len(test_cases)} cases ({len(test_valid)} valid, {len(test_fp)} FP)...")
        
        # Create prompt
        prompt = self.create_adaptive_prompt()
        
        # Test cases
        results = []
        for i, case in enumerate(test_cases):
            if i % 5 == 0:
                print(f"  Progress: {i}/{len(test_cases)}")
            
            result = self.test_single_case(case, prompt)
            if result:
                results.append(result)
            
            time.sleep(0.8)  # Rate limit
        
        # Calculate metrics
        metrics = self.calculate_metrics(results)
        
        print(f"\nResults:")
        print(f"├─ Valid Protection: {metrics['valid_protection']:.1f}% ({metrics['valid_protected']}/{metrics['valid_total']})")
        print(f"├─ FP Detection: {metrics['fp_detection']:.1f}% ({metrics['fp_detected']}/{metrics['fp_total']})")
        print(f"└─ Valid Violations Missed: {metrics['valid_missed']}")
        
        # Store round results
        round_data = {
            'round': round_num,
            'thresholds': self.thresholds.copy(),
            'metrics': metrics,
            'sample_size': len(results)
        }
        
        self.exploration_history.append(round_data)
        
        return metrics
    
    def calculate_metrics(self, results):
        """Calculate detailed metrics"""
        
        if not results:
            return {'valid_protection': 0, 'fp_detection': 0, 'valid_missed': 0}
        
        # Separate by actual type
        valid_results = [r for r in results if not r['actual_fp']]
        fp_results = [r for r in results if r['actual_fp']]
        
        # Calculate protection rates
        valid_protected = sum(not r['predicted_fp'] for r in valid_results) if valid_results else 0
        valid_missed = len(valid_results) - valid_protected if valid_results else 0
        
        fp_detected = sum(r['predicted_fp'] for r in fp_results) if fp_results else 0
        
        return {
            'valid_protection': (valid_protected / len(valid_results) * 100) if valid_results else 100,
            'valid_protected': valid_protected,
            'valid_total': len(valid_results),
            'valid_missed': valid_missed,
            'fp_detection': (fp_detected / len(fp_results) * 100) if fp_results else 0,
            'fp_detected': fp_detected,
            'fp_total': len(fp_results)
        }
    
    def adjust_thresholds(self, metrics):
        """Intelligently adjust thresholds based on results"""
        
        print("\nAdjusting thresholds...")
        
        # If we're not at 100% valid protection, tighten up
        if metrics['valid_protection'] < 100:
            print(f"  ⚠️ Valid protection at {metrics['valid_protection']:.1f}% - tightening thresholds")
            
            # Make it harder to identify structures
            self.thresholds['structure'] = min(99, self.thresholds['structure'] + 2)
            
            # Make it easier to detect violations
            self.thresholds['behavioral'] = max(35, self.thresholds['behavioral'] - 5)
            
            return True  # Continue searching
        
        # We have 100% valid protection, try to improve FP detection
        print(f"  ✓ Valid protection at 100% - optimizing FP detection")
        
        # Check if we can still improve
        can_adjust = False
        
        # Try to relax structure threshold
        if self.thresholds['structure'] > self.threshold_limits['structure']['min']:
            self.thresholds['structure'] -= self.adjustment_steps['structure']
            can_adjust = True
            print(f"    Relaxing structure threshold to {self.thresholds['structure']}%")
        
        # Try to increase person threshold
        if self.thresholds['person'] < self.threshold_limits['person']['max']:
            self.thresholds['person'] += self.adjustment_steps['person']
            can_adjust = True
            print(f"    Increasing person threshold to {self.thresholds['person']}%")
        
        return can_adjust
    
    def find_optimal_100_percent(self):
        """Find optimal thresholds that maintain 100% valid protection"""
        
        print("="*70)
        print("ADAPTIVE 100% VALID PROTECTION FINDER")
        print("="*70)
        print("Goal: Find thresholds that achieve 100% valid protection")
        print("      while maximizing false positive detection")
        
        max_rounds = 10
        found_100_percent = False
        
        for round_num in range(1, max_rounds + 1):
            metrics = self.test_round(round_num)
            
            # Check if we achieved 100% valid protection
            if metrics['valid_protection'] == 100:
                if not found_100_percent:
                    print(f"\n🎯 ACHIEVED 100% VALID PROTECTION!")
                    found_100_percent = True
                
                # Update best config if this has better FP detection
                if not self.best_config or metrics['fp_detection'] > self.best_config['metrics']['fp_detection']:
                    self.best_config = {
                        'round': round_num,
                        'thresholds': self.thresholds.copy(),
                        'metrics': metrics
                    }
                    print(f"  📈 New best FP detection: {metrics['fp_detection']:.1f}%")
            
            # Adjust thresholds
            can_continue = self.adjust_thresholds(metrics)
            
            if not can_continue:
                print("\n📊 Reached threshold limits - stopping exploration")
                break
            
            # Save progress
            self.save_progress()
        
        # Final report
        self.generate_final_report()
    
    def save_progress(self):
        """Save current progress"""
        progress = {
            'timestamp': datetime.now().isoformat(),
            'current_thresholds': self.thresholds,
            'exploration_history': self.exploration_history,
            'best_config': self.best_config
        }
        
        with open('adaptive_100_progress.json', 'w') as f:
            json.dump(progress, f, indent=2)
    
    def generate_final_report(self):
        """Generate comprehensive final report"""
        
        print("\n" + "="*70)
        print("FINAL REPORT: 100% VALID PROTECTION OPTIMIZATION")
        print("="*70)
        
        if self.best_config:
            print(f"\n✅ BEST CONFIGURATION ACHIEVING 100% VALID PROTECTION:")
            print(f"\nRound {self.best_config['round']} Results:")
            print(f"├─ Valid Protection: 100% ✓")
            print(f"├─ FP Detection: {self.best_config['metrics']['fp_detection']:.1f}%")
            print(f"└─ Trade-off: {100 - self.best_config['metrics']['fp_detection']:.1f}% of FPs still generate alerts")
            
            print(f"\nOptimal Thresholds:")
            for key, value in self.best_config['thresholds'].items():
                print(f"├─ {key}: {value}%")
        else:
            print("\n⚠️ Could not achieve 100% valid protection with current approach")
        
        # Show progression
        print("\n📈 LEARNING PROGRESSION:")
        for round_data in self.exploration_history[-5:]:  # Last 5 rounds
            print(f"\nRound {round_data['round']}:")
            print(f"├─ Valid Protection: {round_data['metrics']['valid_protection']:.1f}%")
            print(f"└─ FP Detection: {round_data['metrics']['fp_detection']:.1f}%")
        
        # Save final report
        report = {
            'timestamp': datetime.now().isoformat(),
            'goal': '100% valid protection with maximum FP detection',
            'best_configuration': self.best_config,
            'exploration_history': self.exploration_history,
            'recommendations': self.generate_recommendations()
        }
        
        with open('adaptive_100_final_report.json', 'w') as f:
            json.dump(report, f, indent=2)
        
        print(f"\n📄 Final report saved to: adaptive_100_final_report.json")
    
    def generate_recommendations(self):
        """Generate recommendations based on findings"""
        
        recommendations = []
        
        if self.best_config:
            if self.best_config['metrics']['fp_detection'] >= 50:
                recommendations.append({
                    'type': 'production_viable',
                    'message': f"100% valid protection with {self.best_config['metrics']['fp_detection']:.1f}% FP detection is production-viable",
                    'thresholds': self.best_config['thresholds']
                })
            else:
                recommendations.append({
                    'type': 'not_recommended',
                    'message': f"100% valid protection reduces FP detection to {self.best_config['metrics']['fp_detection']:.1f}% - too low for production",
                    'alternative': "Use Round 3 configuration (99.1% valid, 81.3% FP) for better balance"
                })
        
        recommendations.append({
            'type': 'best_practice',
            'message': "For production, prioritize 99%+ valid protection with 70%+ FP detection",
            'reasoning': "This balance provides safety while maintaining operational efficiency"
        })
        
        return recommendations

def main():
    finder = Adaptive100PercentFinder()
    finder.find_optimal_100_percent()

if __name__ == "__main__":
    main()