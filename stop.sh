#!/bin/bash

# AI-FARM Application Stopper
echo "🛑 Stopping AI-FARM Application"
echo "==============================="

cd "$(dirname "$0")"

# Function to kill process on port
kill_port() {
    local port=$1
    local name=$2
    
    # Find PIDs using the port
    local pids=$(lsof -ti :$port 2>/dev/null)
    
    if [ ! -z "$pids" ]; then
        echo "🔧 Stopping $name on port $port (PIDs: $pids)..."
        for pid in $pids; do
            kill -TERM $pid 2>/dev/null || true
            sleep 1
            # Force kill if still running
            if kill -0 $pid 2>/dev/null; then
                kill -9 $pid 2>/dev/null || true
            fi
        done
    fi
}

# Stop AI-FARM backend on port 8000
kill_port 8000 "AI-FARM backend"

# Stop frontend on port 3000
kill_port 3000 "frontend"

# Stop backend using PID file
if [ -f "logs/backend.pid" ]; then
    BACKEND_PID=$(cat logs/backend.pid)
    if kill -0 "$BACKEND_PID" 2>/dev/null; then
        echo "🔧 Stopping backend via PID file (PID: $BACKEND_PID)..."
        kill -TERM "$BACKEND_PID" 2>/dev/null || true
        sleep 1
        kill -9 "$BACKEND_PID" 2>/dev/null || true
    fi
    rm -f logs/backend.pid
fi

# Stop frontend using PID file
if [ -f "logs/frontend.pid" ]; then
    FRONTEND_PID=$(cat logs/frontend.pid)
    if kill -0 "$FRONTEND_PID" 2>/dev/null; then
        echo "🎨 Stopping frontend via PID file (PID: $FRONTEND_PID)..."
        kill -TERM "$FRONTEND_PID" 2>/dev/null || true
        sleep 1
        kill -9 "$FRONTEND_PID" 2>/dev/null || true
    fi
    rm -f logs/frontend.pid
fi

# Clean up any remaining processes
echo "🧹 Cleaning up remaining processes..."
pkill -f "uvicorn app.main:app" 2>/dev/null || true
pkill -f "python.*run.py" 2>/dev/null || true
pkill -f "python3.*run.py" 2>/dev/null || true
pkill -f "react-scripts start" 2>/dev/null || true
pkill -f "vite" 2>/dev/null || true
pkill -f "npm start" 2>/dev/null || true

# Additional cleanup for node processes on port 3000
for pid in $(lsof -ti :3000 2>/dev/null); do
    kill -9 $pid 2>/dev/null || true
done

# Additional cleanup for python processes on port 8000
for pid in $(lsof -ti :8000 2>/dev/null); do
    kill -9 $pid 2>/dev/null || true
done

# Clean up AI-FARM specific processes
pkill -f "cd backend && PORT=8000" 2>/dev/null || true

# Wait a moment to ensure processes are stopped
sleep 2

# Verify ports are free
echo ""
echo "📊 Port Status:"
echo "=============="

if lsof -Pi :3000 -sTCP:LISTEN -t >/dev/null 2>&1; then
    echo "⚠️  Warning: Port 3000 is still in use"
else
    echo "✅ Port 3000 is free"
fi

if lsof -Pi :8000 -sTCP:LISTEN -t >/dev/null 2>&1; then
    echo "⚠️  Warning: Port 8000 is still in use"
else
    echo "✅ Port 8000 is free (AI-FARM backend)"
fi

echo ""
echo "✅ AI-FARM Application stopped"
echo "💡 To restart: ./start.sh"