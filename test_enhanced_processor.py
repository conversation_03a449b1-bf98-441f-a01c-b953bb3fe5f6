#!/usr/bin/env python3
"""
Test the enhanced processor with a few cases first
"""

import json
import time
import requests
import threading
import webbrowser
from enhanced_valo_processor import <PERSON>hancedVA<PERSON>OProcessor

def test_processor():
    """Test with a small subset of cases"""
    processor = EnhancedVALOProcessor()
    
    # Load just 10 cases for testing
    with open('valo_batch_round3_complete.json', 'r') as f:
        data = json.load(f)
        test_cases = data['results'][:10]
    
    print("="*80)
    print("TESTING ENHANCED PROCESSOR WITH 10 CASES")
    print("="*80)
    
    # Start dashboard
    processor.run_dashboard()
    time.sleep(2)
    
    print(f"Dashboard running at: http://localhost:5001")
    print("Opening dashboard in browser...")
    
    # Try to open browser
    try:
        webbrowser.open('http://localhost:5001')
    except:
        print("Please open http://localhost:5001 in your browser")
    
    print("\nProcessing 10 test cases...")
    print("-"*80)
    
    # Update progress for test
    processor.progress['total'] = len(test_cases)
    processor.progress['start_time'] = time.time()
    
    # Process test cases
    for i, case in enumerate(test_cases):
        print(f"\nProcessing case {i+1}/10: {case['case_number']}")
        
        result = processor.process_case(case)
        
        if result:
            print(f"  ✓ Main Subject: {result['main_subject']}")
            print(f"  ✓ Person Present: {result['person_present']}")
            print(f"  ✓ FP Likelihood: {result['fp_likelihood']}%")
            print(f"  ✓ Ground Truth: {result['ground_truth']}")
            print(f"  ✓ Infringement: {result['infringement_type']}")
            
            # Check if correctly identified
            if result['ground_truth'] == 'Invalid' and result['fp_likelihood'] >= 70:
                print(f"  ✅ FALSE POSITIVE CORRECTLY DETECTED!")
            elif result['ground_truth'] == 'Valid' and result['fp_likelihood'] < 50:
                print(f"  ✅ VALID CASE CORRECTLY PROTECTED!")
            else:
                print(f"  ⚠️  Needs improvement")
        else:
            print(f"  ❌ Error processing case")
    
    # Print summary
    print("\n" + "="*80)
    print("TEST SUMMARY")
    print("="*80)
    
    summary = processor.get_summary()
    print(f"Total Processed: {processor.progress['processed']}")
    print(f"FP Detection Rate: {processor.progress['fp_rate']:.1f}%")
    print(f"Protection Rate: {processor.progress['protection_rate']:.1f}%")
    print(f"Structure Detections: {summary.get('structure_detections', 0)}")
    print(f"Person with PPE FPs: {summary.get('person_with_ppe_fps', 0)}")
    
    # Save test results
    with open('test_enhanced_results.json', 'w') as f:
        json.dump({
            'results': processor.results,
            'progress': processor.progress,
            'summary': summary
        }, f, indent=2)
    
    print(f"\nTest results saved to: test_enhanced_results.json")
    print(f"\nDashboard will remain running. Press Ctrl+C to stop.")
    
    # Keep dashboard running
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\nStopping dashboard...")

if __name__ == "__main__":
    test_processor()