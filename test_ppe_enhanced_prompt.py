#!/usr/bin/env python3
"""
Test PPE-enhanced prompt on full PPE false positives
Should improve FP detection while maintaining safety
"""

import json
import base64
import requests
import os

VLM_API_URL = "http://100.106.127.35:9500/v1/chat/completions"
VLM_MODEL = "VLM-38B-AWQ"

def test_ppe_enhanced_prompt():
    """Test the PPE-enhanced prompt"""
    
    print("TESTING PPE-ENHANCED PROMPT")
    print("="*60)
    print("Focus: Full PPE false positives that should be correctly identified\n")
    
    # Load PPE-enhanced prompt
    with open('ppe_enhanced_safety_prompt.txt', 'r') as f:
        prompt = f.read()
    
    # Test cases - Full PPE false positives and some valid violations
    test_cases = {
        # Full PPE false positives (should output FALSE POSITIVE: YES)
        "V1250627135": "WOS IN FULL PPE AT WHARF",
        "V1250627142": "WOS IN FULL PPE AT WHARF",
        "V1250627151": "LS Full PPE at wharf",
        "V1250627174": "LS in full PPE at wharf",
        "V1250627203": "LS Full PPE at wharf",
        
        # Valid violations (should output FALSE POSITIVE: NO)
        "V1250627179": "Missing GO/STOP bat",
        "V1250627208": "Mobile phone use",
        
        # Structure false positives (should output FALSE POSITIVE: YES)
        "V1250627134": "Crane structure"
    }
    
    # Load data
    with open('valo_batch_round3_complete.json', 'r') as f:
        data = json.load(f)
    
    session = requests.Session()
    results = []
    
    for case_num, description in test_cases.items():
        case = next((c for c in data['results'] if c['case_number'] == case_num), None)
        if not case:
            continue
        
        print(f"\nCase: {case_num}")
        print(f"Description: {description}")
        print(f"Actual: {'False Positive' if case['is_false_positive'] else 'Valid Violation'}")
        
        # Encode images
        try:
            with open(case['source_image'], 'rb') as f:
                source_b64 = base64.b64encode(f.read()).decode('utf-8')
            with open(case['cropped_image'], 'rb') as f:
                cropped_b64 = base64.b64encode(f.read()).decode('utf-8')
        except:
            print("Error encoding images")
            continue
        
        # Call VLM
        payload = {
            "model": VLM_MODEL,
            "messages": [{
                "role": "user",
                "content": [
                    {"type": "text", "text": prompt},
                    {"type": "text", "text": "\n\nSOURCE IMAGE:"},
                    {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{source_b64}"}},
                    {"type": "text", "text": "\n\nCROPPED IMAGE:"},
                    {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{cropped_b64}"}}
                ]
            }],
            "temperature": 0.1,
            "max_tokens": 200
        }
        
        try:
            response = session.post(VLM_API_URL, json=payload, timeout=25)
            if response.status_code == 200:
                result = response.json()
                vlm_response = result['choices'][0]['message']['content'].strip()
                
                # Extract key parts
                lines = vlm_response.split('\n')
                fp_line = next((l for l in lines if 'FALSE POSITIVE:' in l), '')
                detection_line = next((l for l in lines if 'Detection:' in l), '')
                ppe_line = next((l for l in lines if 'PPE Status:' in l), '')
                
                print(f"Response:")
                print(f"  {fp_line}")
                print(f"  {detection_line}")
                print(f"  {ppe_line}")
                
                # Parse result
                predicted_fp = 'YES' in fp_line
                correct = predicted_fp == case['is_false_positive']
                
                print(f"Result: {'✅ CORRECT' if correct else '❌ WRONG'}")
                
                results.append({
                    'case': case_num,
                    'correct': correct,
                    'is_fp': case['is_false_positive'],
                    'predicted_fp': predicted_fp,
                    'category': 'full_ppe' if 'FULL PPE' in description else 'other'
                })
                
        except Exception as e:
            print(f"Error: {e}")
    
    # Summary
    print("\n" + "="*60)
    print("SUMMARY")
    print("="*60)
    
    if results:
        # Overall accuracy
        correct = sum(1 for r in results if r['correct'])
        print(f"\nOverall: {correct}/{len(results)} correct ({correct/len(results)*100:.0f}%)")
        
        # Full PPE performance
        ppe_results = [r for r in results if r['category'] == 'full_ppe']
        if ppe_results:
            ppe_correct = sum(1 for r in ppe_results if r['correct'])
            print(f"\nFull PPE Cases: {ppe_correct}/{len(ppe_results)} correct ({ppe_correct/len(ppe_results)*100:.0f}%)")
        
        # Valid protection
        valid_results = [r for r in results if not r['is_fp']]
        if valid_results:
            protected = sum(1 for r in valid_results if not r['predicted_fp'])
            print(f"Valid Protection: {protected}/{len(valid_results)} protected ({protected/len(valid_results)*100:.0f}%)")
        
        # FP detection
        fp_results = [r for r in results if r['is_fp']]
        if fp_results:
            detected = sum(1 for r in fp_results if r['predicted_fp'])
            print(f"FP Detection: {detected}/{len(fp_results)} detected ({detected/len(fp_results)*100:.0f}%)")
        
        print("\n✨ KEY INSIGHT:")
        if ppe_correct > len(ppe_results) * 0.8:
            print("PPE recognition is working! The VLM now understands full PPE compliance.")
        else:
            print("PPE recognition needs more tuning.")

if __name__ == "__main__":
    test_ppe_enhanced_prompt()