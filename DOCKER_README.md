# AI-FARM Docker Configuration

A complete Docker setup for the AI-FARM (False Positive Alert Reduction System) application with FastAPI backend, React frontend, PostgreSQL database, and Redis caching.

## Quick Start

Get the entire AI-FARM stack running with just a few commands:

```bash
# 1. Clone the repository (if not already done)
git clone <repository-url>
cd VALO_AI-FARM_2025

# 2. Copy and configure environment variables
cp .env.example .env
# Edit .env file with your specific configuration

# 3. Start the complete stack
docker-compose up

# 4. Access the application
# Frontend: http://localhost:3000
# Backend API: http://localhost:8000
# API Documentation: http://localhost:8000/docs
# Database: localhost:5432
```

That's it! The complete AI-FARM application is now running.

## Stack Overview

| Service    | Port | Description                    |
|------------|------|--------------------------------|
| Frontend   | 3000 | React application with <PERSON>inx   |
| Backend    | 8000 | FastAPI application            |
| Database   | 5432 | PostgreSQL database            |
| Redis      | 6379 | Caching and session management |

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   React SPA     │───▶│   FastAPI API   │───▶│   PostgreSQL    │
│   (Frontend)    │    │   (Backend)     │    │   (Database)    │
│   Port: 3000    │    │   Port: 8000    │    │   Port: 5432    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌─────────────────┐
                       │     Redis       │
                       │   (Caching)     │
                       │   Port: 6379    │
                       └─────────────────┘
```

## Configuration

### Environment Variables

The application uses environment variables for configuration. Key variables include:

#### Database Configuration
- `POSTGRES_DB`: Database name (default: ai_farm)
- `POSTGRES_USER`: Database user (default: ai_farm_user)
- `POSTGRES_PASSWORD`: Database password (required in production)

#### VLM API Configuration
- `VLM_API_BASE_URL`: Base URL for VLN API
- `VLM_API_KEY`: API key for VLM service
- `VLM_MODEL_NAME`: Model name to use (default: internvl3-38b)

#### Processing Configuration
- `BATCH_SIZE`: Batch size for processing (default: 10)
- `MAX_CONCURRENT_REQUESTS`: Max concurrent requests (default: 3)
- `PROCESSING_TIMEOUT_MINUTES`: Processing timeout (default: 60)

See `.env.example` for all available configuration options.

## Docker Files

### Backend Dockerfile (`backend/Dockerfile`)
- Multi-stage build for optimized production images
- Non-root user for security
- Health checks included
- Development stage with hot reload

### Frontend Dockerfile (`frontend/Dockerfile`)
- Multi-stage build with Node.js build stage
- Production stage with Nginx serving
- Development stage with hot reload
- Security headers and optimizations

### Nginx Configuration (`frontend/nginx.conf`)
- Optimized for React SPA routing
- API proxy to backend
- Security headers
- Gzip compression
- Caching for static assets

## Docker Compose Files

### Development (`docker-compose.yml`)
- Hot reload for both frontend and backend
- Development-friendly configurations
- Volume mounts for live code changes
- Debug logging enabled

### Production (`docker-compose.prod.yml`)
- Optimized for production deployment
- Resource limits and health checks
- Multi-replica backend deployment
- Monitoring services (Prometheus, Grafana)
- SSL/TLS support ready

## Commands

### Development Commands

```bash
# Start all services
docker-compose up

# Start services in background
docker-compose up -d

# Start specific service
docker-compose up backend

# View logs
docker-compose logs -f backend

# Stop all services
docker-compose down

# Rebuild and start
docker-compose up --build

# Remove volumes (clean slate)
docker-compose down -v
```

### Production Commands

```bash
# Production deployment
docker-compose -f docker-compose.prod.yml up -d

# Scale backend service
docker-compose -f docker-compose.prod.yml up -d --scale backend=3

# Production with monitoring
docker-compose -f docker-compose.prod.yml --profile monitoring up -d

# Production logs
docker-compose -f docker-compose.prod.yml logs -f

# Production shutdown
docker-compose -f docker-compose.prod.yml down
```

### Database Commands

```bash
# Access database shell
docker-compose exec database psql -U ai_farm_user -d ai_farm

# Run database migrations
docker-compose exec backend python -c "from app.core.database import init_database; init_database()"

# Database backup
docker-compose exec database pg_dump -U ai_farm_user ai_farm > backup.sql

# Database restore
docker-compose exec -T database psql -U ai_farm_user -d ai_farm < backup.sql
```

### Utility Commands

```bash
# View running containers
docker-compose ps

# Execute command in container
docker-compose exec backend bash
docker-compose exec frontend sh

# Check service health
docker-compose exec backend curl http://localhost:8000/health
docker-compose exec frontend curl http://localhost:3000/health

# View resource usage
docker stats

# Clean up unused Docker resources
docker system prune -a
```

## Health Checks

All services include comprehensive health checks:

- **Backend**: HTTP health endpoint (`/health`)
- **Frontend**: Nginx status check
- **Database**: PostgreSQL connection check
- **Redis**: Redis ping command

## Volume Management

The setup includes persistent volumes for:

- `postgres_data`: Database data persistence
- `redis_data`: Redis data persistence
- `ai_farm_data`: Application data files
- `ai_farm_logs`: Application logs
- `ai_farm_exports`: Export files

## Monitoring (Production)

The production setup includes optional monitoring services:

```bash
# Start with monitoring
docker-compose -f docker-compose.prod.yml --profile monitoring up -d

# Access monitoring
# Prometheus: http://localhost:9090
# Grafana: http://localhost:3001 (admin/admin123)
```

## Security Considerations

### Development
- Default passwords (change for production)
- Debug mode enabled
- All services accessible from host

### Production
- Non-root users in containers
- Resource limits enforced
- Security headers configured
- Environment-based secrets
- Network isolation

## Troubleshooting

### Common Issues

1. **Port conflicts**
   ```bash
   # Check if ports are in use
   netstat -tulpn | grep :3000
   netstat -tulpn | grep :8000
   netstat -tulpn | grep :5432
   ```

2. **Permission issues**
   ```bash
   # Fix file permissions
   sudo chown -R $USER:$USER .
   chmod -R 755 .
   ```

3. **Database connection issues**
   ```bash
   # Check database logs
   docker-compose logs database
   
   # Test database connection
   docker-compose exec backend python -c "from app.core.database import db_manager; print(db_manager.get_health_status())"
   ```

4. **Frontend build issues**
   ```bash
   # Clear node modules and rebuild
   docker-compose down
   docker-compose build --no-cache frontend
   docker-compose up frontend
   ```

5. **Backend startup issues**
   ```bash
   # Check backend logs
   docker-compose logs backend
   
   # Test backend directly
   docker-compose exec backend python -c "from app.main import app; print('Backend loaded successfully')"
   ```

### Log Locations

- Backend logs: `/app/logs/` in container
- Nginx logs: `/var/log/nginx/` in container
- Database logs: Docker logs (`docker-compose logs database`)

### Debugging

```bash
# Enter container for debugging
docker-compose exec backend bash
docker-compose exec frontend sh
docker-compose exec database bash

# Check container resource usage
docker stats

# Inspect container configuration
docker inspect ai-farm-backend
```

## Performance Tuning

### Development Optimizations
- Use volume mounts for hot reload
- Reduce batch sizes for faster feedback
- Enable debug logging

### Production Optimizations
- Multi-replica deployment
- Resource limits and reservations
- Connection pooling
- Caching strategies
- Load balancing

## Backup and Recovery

### Database Backup
```bash
# Create backup
docker-compose exec database pg_dump -U ai_farm_user ai_farm > ai_farm_backup_$(date +%Y%m%d_%H%M%S).sql

# Automated backup script
# Add to crontab: 0 2 * * * /path/to/backup_script.sh
```

### Volume Backup
```bash
# Backup volumes
docker run --rm -v ai-farm_postgres_data:/data -v $(pwd):/backup alpine tar czf /backup/postgres_data_backup.tar.gz -C /data .
```

## SSL/TLS Configuration (Production)

For production with SSL:

1. Obtain SSL certificates
2. Update nginx configuration
3. Configure environment variables
4. Update docker-compose.prod.yml ports

## Contributing

When adding new services or modifying the Docker configuration:

1. Test changes in development environment
2. Update documentation
3. Verify health checks work
4. Test production deployment
5. Update environment variable examples

## License

This Docker configuration is part of the AI-FARM project. See the main project README for license information.