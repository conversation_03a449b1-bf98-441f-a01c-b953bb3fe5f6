#!/usr/bin/env python3
"""
Health monitoring service for overnight run
Checks VLM API, memory, disk space, and process health
"""

import requests
import psutil
import json
import time
import os
from datetime import datetime

class HealthMonitor:
    def __init__(self):
        self.vlm_endpoint = "http://100.106.127.35:9500/v1/chat/completions"
        self.check_interval = 300  # 5 minutes
        self.alert_threshold = {
            'memory_percent': 90,
            'disk_percent': 95,
            'api_response_time': 10  # seconds
        }
        
    def check_vlm_api(self):
        """Test VLM API health"""
        try:
            test_payload = {
                "model": "VLM-38B-AWQ",
                "messages": [{"role": "user", "content": "test"}],
                "max_tokens": 10
            }
            
            start_time = time.time()
            response = requests.post(
                self.vlm_endpoint,
                json=test_payload,
                timeout=15
            )
            response_time = time.time() - start_time
            
            return {
                'status': response.status_code == 200,
                'response_time': response_time,
                'error': None if response.status_code == 200 else response.text
            }
        except Exception as e:
            return {
                'status': False,
                'response_time': -1,
                'error': str(e)
            }
    
    def check_system_resources(self):
        """Check memory and disk usage"""
        return {
            'memory_percent': psutil.virtual_memory().percent,
            'disk_percent': psutil.disk_usage('/').percent,
            'cpu_percent': psutil.cpu_percent(interval=1)
        }
    
    def check_running_processes(self):
        """Check if round processes are running"""
        round_processes = []
        
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                cmdline = ' '.join(proc.info['cmdline'] or [])
                if 'round' in cmdline and 'python' in proc.info['name']:
                    round_processes.append({
                        'pid': proc.info['pid'],
                        'command': cmdline[:100]
                    })
            except:
                pass
        
        return round_processes
    
    def run_health_check(self):
        """Run all health checks"""
        timestamp = datetime.now().isoformat()
        
        # Check VLM API
        api_health = self.check_vlm_api()
        
        # Check system resources
        system_health = self.check_system_resources()
        
        # Check running processes
        processes = self.check_running_processes()
        
        # Compile health report
        health_report = {
            'timestamp': timestamp,
            'vlm_api': api_health,
            'system': system_health,
            'active_processes': len(processes),
            'alerts': []
        }
        
        # Generate alerts
        if not api_health['status']:
            health_report['alerts'].append(f"VLM API DOWN: {api_health['error']}")
        
        if api_health['response_time'] > self.alert_threshold['api_response_time']:
            health_report['alerts'].append(f"VLM API SLOW: {api_health['response_time']:.1f}s")
        
        if system_health['memory_percent'] > self.alert_threshold['memory_percent']:
            health_report['alerts'].append(f"HIGH MEMORY: {system_health['memory_percent']:.1f}%")
        
        if system_health['disk_percent'] > self.alert_threshold['disk_percent']:
            health_report['alerts'].append(f"LOW DISK: {system_health['disk_percent']:.1f}% used")
        
        # Save report
        with open('health_status.json', 'w') as f:
            json.dump(health_report, f, indent=2)
        
        # Log alerts
        if health_report['alerts']:
            with open('health_alerts.log', 'a') as f:
                for alert in health_report['alerts']:
                    f.write(f"{timestamp} - ALERT: {alert}\n")
            
            # Also print to console
            print(f"\n⚠️  HEALTH ALERTS at {timestamp}:")
            for alert in health_report['alerts']:
                print(f"   - {alert}")
        else:
            print(f"✅ Health check OK at {timestamp}")
        
        return health_report
    
    def monitor_loop(self):
        """Main monitoring loop"""
        print("="*60)
        print("HEALTH MONITOR STARTED")
        print(f"Checking every {self.check_interval} seconds")
        print("="*60)
        
        while True:
            try:
                self.run_health_check()
                time.sleep(self.check_interval)
            except KeyboardInterrupt:
                print("\nHealth monitor stopped")
                break
            except Exception as e:
                print(f"Monitor error: {e}")
                time.sleep(60)

if __name__ == "__main__":
    monitor = HealthMonitor()
    monitor.monitor_loop()