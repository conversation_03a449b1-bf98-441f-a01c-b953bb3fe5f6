#!/usr/bin/env python3
"""
Round 26: CRITICAL TEST - What happens without remarks?
Testing a few clear cases to understand the reality
"""
import asyncio
import json
import aiohttp
import logging
from datetime import datetime
import base64

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_specific_cases():
    """Test specific cases we know have clear PPE patterns"""
    vlm_endpoint = "http://100.106.127.35:9500/v1/chat/completions"
    
    # Test cases with known PPE compliance from remarks
    test_cases = [
        {
            'case_number': 'V1250627135',
            'remarks': 'WOS IN FULL PPE AT WHARF',  # Hidden from VLM
            'expected': 'Should detect PPE compliance from image alone'
        },
        {
            'case_number': 'V1250627140', 
            'remarks': 'WOS in proper PPE at wharf',  # Hidden from VLM
            'expected': 'Should detect PPE compliance from image alone'
        },
        {
            'case_number': 'V1250630118',
            'remarks': 'WORKER IN FULL PPE NEAR CRANE OPERATION',  # Hidden from VLM
            'expected': 'Clear PPE case from Round 6'
        }
    ]
    
    logger.info("="*80)
    logger.info("CRITICAL TEST: Can VLM detect PPE from images alone?")
    logger.info("="*80)
    
    async with aiohttp.ClientSession() as session:
        for test in test_cases:
            logger.info(f"\nTesting case {test['case_number']}:")
            logger.info(f"Hidden remarks: {test['remarks']}")
            logger.info(f"Expected: {test['expected']}")
            
            # Find the actual file
            with open('valo_batch_round3_complete.json', 'r') as f:
                data = json.load(f)
                case_data = next((c for c in data['results'] if c['case_number'] == test['case_number']), None)
            
            if not case_data:
                logger.error(f"Case {test['case_number']} not found!")
                continue
                
            # Test 1: With remarks (Round 6 approach)
            prompt_with_remarks = f"""PPE COMPLIANCE CHECK

Context from remarks: {test['remarks']}

Key insight: Workers wearing FULL/PROPER PPE are COMPLIANT, not violators.

Is this a FALSE POSITIVE (compliant worker incorrectly flagged)?
Answer: YES/NO"""

            # Test 2: Without remarks (Production reality)
            prompt_no_remarks = """PPE VIOLATION ANALYSIS

This image was flagged for potential PPE non-compliance.

Please analyze the image:
1. Can you see any people?
2. If yes, are they wearing proper safety gear (helmet, vest)?
3. If they have proper PPE, this is a false positive.

Is this a FALSE POSITIVE? YES/NO"""

            # Test both approaches
            for approach, prompt in [("WITH REMARKS", prompt_with_remarks), ("NO REMARKS", prompt_no_remarks)]:
                try:
                    # Read image and encode
                    with open(case_data['cropped_image'], 'rb') as img_file:
                        image_data = base64.b64encode(img_file.read()).decode('utf-8')
                    
                    payload = {
                        "model": "VLM-38B-AWQ",
                        "messages": [{
                            "role": "user",
                            "content": [
                                {"type": "text", "text": prompt},
                                {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{image_data}"}}
                            ]
                        }],
                        "temperature": 0.1,
                        "max_tokens": 200
                    }
                    
                    async with session.post(vlm_endpoint, json=payload) as response:
                        if response.status == 200:
                            result = await response.json()
                            content = result['choices'][0]['message']['content']
                            decision = "YES" in content.upper()[:50]
                            
                            logger.info(f"\n{approach}:")
                            logger.info(f"Decision: {'FALSE POSITIVE' if decision else 'NOT FALSE POSITIVE'}")
                            logger.info(f"Response: {content[:150]}...")
                        else:
                            logger.error(f"API error: {response.status}")
                            
                except Exception as e:
                    logger.error(f"Error: {e}")
            
            logger.info("-"*60)
    
    logger.info("\n" + "="*80)
    logger.info("CONCLUSION:")
    logger.info("If NO REMARKS approach fails to detect PPE compliance,")
    logger.info("then Round 6's success depends on human-provided context")
    logger.info("and won't work in production!")
    logger.info("="*80)

if __name__ == "__main__":
    asyncio.run(test_specific_cases())