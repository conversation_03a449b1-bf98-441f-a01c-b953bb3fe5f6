#!/usr/bin/env python3
"""
Test enhanced prompt with detailed structure descriptions
Compare performance against baseline to measure improvement
"""

import json
import base64
import requests
import os
from datetime import datetime
import time

VLM_API_URL = "http://**************:9500/v1/chat/completions"
VLM_MODEL = "VLM-38B-AWQ"

class EnhancedPromptTester:
    def __init__(self):
        self.session = requests.Session()
        self.results = {
            'baseline': [],
            'enhanced': []
        }
        
    def encode_image(self, image_path):
        """Encode image to base64"""
        try:
            if os.path.exists(image_path):
                with open(image_path, 'rb') as f:
                    return base64.b64encode(f.read()).decode('utf-8')
        except:
            pass
        return None
    
    def load_enhanced_prompt(self):
        """Load the enhanced prompt with structure descriptions"""
        with open('intelligent_prompt_enhanced_structures.txt', 'r') as f:
            return f.read()
    
    def get_baseline_prompt(self):
        """Simple baseline prompt without structure details"""
        return """SAFETY VIOLATION ANALYSIS

Analyze the cropped image to determine if this shows a safety violation.

1. Is there a person in the image?
2. If yes, are they wearing proper PPE (helmet and vest)?
3. If no PPE visible, this is a VALID violation
4. If PPE present or no person visible, this is a FALSE POSITIVE

Answer: Is this a FALSE POSITIVE? YES/NO"""

    def call_vlm_with_prompt(self, source_b64, cropped_b64, prompt, timeout=20):
        """Call VLM with dual images and specific prompt"""
        
        payload = {
            "model": VLM_MODEL,
            "messages": [{
                "role": "user",
                "content": [
                    {"type": "text", "text": prompt},
                    {"type": "text", "text": "\n\nSOURCE IMAGE (full view):"},
                    {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{source_b64}"}},
                    {"type": "text", "text": "\n\nCROPPED IMAGE (area of concern):"},
                    {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{cropped_b64}"}}
                ]
            }],
            "temperature": 0.1,
            "max_tokens": 200
        }
        
        try:
            response = self.session.post(VLM_API_URL, json=payload, timeout=timeout)
            if response.status_code == 200:
                result = response.json()
                return result['choices'][0]['message']['content'].strip()
        except Exception as e:
            print(f"\nVLM Error: {e}")
        
        return None
    
    def test_structure_cases(self, num_cases=50):
        """Test specifically on structure false positives"""
        
        print("\nTESTING ENHANCED PROMPT ON STRUCTURE FALSE POSITIVES")
        print("="*60)
        
        # Load data
        with open('valo_batch_round3_complete.json', 'r') as f:
            data = json.load(f)
        
        # Find structure-related false positives
        structure_keywords = ['CRANE STRUCTURE', 'VESSEL STRUCTURE', 'PM STRUCTURE', 
                            'SPREADER', 'EQUIPMENT', 'MACHINE', 'BARGE']
        
        structure_cases = []
        for case in data['results']:
            if case['is_false_positive'] and case.get('remarks'):
                remark_upper = case['remarks'].upper()
                if any(keyword in remark_upper for keyword in structure_keywords):
                    structure_cases.append(case)
        
        print(f"Found {len(structure_cases)} structure false positive cases")
        
        # Test a sample
        test_cases = structure_cases[:num_cases]
        
        # Load prompts
        baseline_prompt = self.get_baseline_prompt()
        enhanced_prompt = self.load_enhanced_prompt()
        
        for i, case in enumerate(test_cases):
            print(f"\n\nCase {i+1}/{len(test_cases)}: {case['case_number']}")
            print(f"Remark: {case['remarks']}")
            print(f"Type: {case['infringement_type']}")
            
            # Encode images
            source_b64 = self.encode_image(case['source_image'])
            cropped_b64 = self.encode_image(case['cropped_image'])
            
            if not source_b64 or not cropped_b64:
                continue
            
            # Test baseline prompt
            print("\nBaseline prompt test...")
            baseline_response = self.call_vlm_with_prompt(source_b64, cropped_b64, baseline_prompt)
            if baseline_response:
                baseline_correct = 'YES' in baseline_response.upper()
                self.results['baseline'].append({
                    'case': case['case_number'],
                    'correct': baseline_correct,
                    'response': baseline_response
                })
                print(f"Baseline: {'✓' if baseline_correct else '✗'}")
            
            # Test enhanced prompt  
            print("Enhanced prompt test...")
            enhanced_response = self.call_vlm_with_prompt(source_b64, cropped_b64, enhanced_prompt)
            if enhanced_response:
                enhanced_correct = 'YES' in enhanced_response.upper()
                self.results['enhanced'].append({
                    'case': case['case_number'],
                    'correct': enhanced_correct,
                    'response': enhanced_response,
                    'entity_type': self._extract_entity_type(enhanced_response)
                })
                print(f"Enhanced: {'✓' if enhanced_correct else '✗'}")
                
                # Show entity detection
                entity = self._extract_entity_type(enhanced_response)
                if entity:
                    print(f"Detected as: {entity}")
            
            # Rate limit
            time.sleep(1)
    
    def _extract_entity_type(self, response):
        """Extract detected entity type from response"""
        if 'Entity Type: CRANE' in response:
            return 'CRANE'
        elif 'Entity Type: VESSEL' in response:
            return 'VESSEL'
        elif 'Entity Type: PM' in response:
            return 'PM'
        elif 'Entity Type: SPREADER' in response:
            return 'SPREADER'
        elif 'Entity Type: STRUCTURE' in response:
            return 'STRUCTURE'
        elif 'Entity Type: PERSON' in response:
            return 'PERSON'
        return None
    
    def test_mixed_cases(self, num_cases=50):
        """Test on mixed cases including valid violations"""
        
        print("\n\nTESTING ON MIXED CASES")
        print("="*60)
        
        # Load data
        with open('valo_batch_round3_complete.json', 'r') as f:
            data = json.load(f)
        
        # Get mix of FP and valid cases
        fp_cases = [c for c in data['results'] if c['is_false_positive']][:25]
        valid_cases = [c for c in data['results'] if not c['is_false_positive']][:25]
        
        test_cases = fp_cases + valid_cases
        import random
        random.shuffle(test_cases)
        
        # Load prompts
        baseline_prompt = self.get_baseline_prompt()
        enhanced_prompt = self.load_enhanced_prompt()
        
        mixed_results = {'baseline': [], 'enhanced': []}
        
        for i, case in enumerate(test_cases[:num_cases]):
            print(f"\n\nCase {i+1}/{num_cases}: {case['case_number']}")
            print(f"Actual: {'False Positive' if case['is_false_positive'] else 'Valid Violation'}")
            
            # Encode images
            source_b64 = self.encode_image(case['source_image'])
            cropped_b64 = self.encode_image(case['cropped_image'])
            
            if not source_b64 or not cropped_b64:
                continue
            
            # Test both prompts
            for prompt_type, prompt in [('baseline', baseline_prompt), ('enhanced', enhanced_prompt)]:
                response = self.call_vlm_with_prompt(source_b64, cropped_b64, prompt)
                if response:
                    predicted_fp = 'YES' in response.upper()
                    correct = predicted_fp == case['is_false_positive']
                    mixed_results[prompt_type].append({
                        'correct': correct,
                        'is_fp': case['is_false_positive']
                    })
            
            time.sleep(1)
        
        return mixed_results
    
    def generate_report(self):
        """Generate comparison report"""
        
        print("\n\n" + "="*60)
        print("ENHANCED PROMPT PERFORMANCE REPORT")
        print("="*60)
        
        # Structure cases performance
        if self.results['baseline'] and self.results['enhanced']:
            baseline_acc = sum(r['correct'] for r in self.results['baseline']) / len(self.results['baseline']) * 100
            enhanced_acc = sum(r['correct'] for r in self.results['enhanced']) / len(self.results['enhanced']) * 100
            
            print(f"\nSTRUCTURE FALSE POSITIVE DETECTION:")
            print(f"Baseline Accuracy: {baseline_acc:.1f}%")
            print(f"Enhanced Accuracy: {enhanced_acc:.1f}%")
            print(f"Improvement: {enhanced_acc - baseline_acc:+.1f}%")
            
            # Entity detection analysis
            entity_counts = {}
            for r in self.results['enhanced']:
                entity = r.get('entity_type')
                if entity:
                    entity_counts[entity] = entity_counts.get(entity, 0) + 1
            
            if entity_counts:
                print("\nENTITY DETECTION BREAKDOWN:")
                for entity, count in sorted(entity_counts.items(), key=lambda x: x[1], reverse=True):
                    print(f"- {entity}: {count} cases")
        
        # Save detailed results
        report = {
            'timestamp': datetime.now().isoformat(),
            'structure_test_results': self.results,
            'baseline_accuracy': baseline_acc if 'baseline_acc' in locals() else 0,
            'enhanced_accuracy': enhanced_acc if 'enhanced_acc' in locals() else 0,
            'improvement': (enhanced_acc - baseline_acc) if 'enhanced_acc' in locals() else 0
        }
        
        with open('enhanced_prompt_test_report.json', 'w') as f:
            json.dump(report, f, indent=2)
        
        print(f"\nDetailed report saved to: enhanced_prompt_test_report.json")

def main():
    tester = EnhancedPromptTester()
    
    # Test on structure cases
    tester.test_structure_cases(num_cases=30)
    
    # Generate report
    tester.generate_report()
    
    print("\n\nNEXT STEPS:")
    print("1. If improvement > 5%, proceed with full 1250 case test")
    print("2. Tune confidence thresholds based on results")
    print("3. Implement camera-specific optimizations")

if __name__ == "__main__":
    main()