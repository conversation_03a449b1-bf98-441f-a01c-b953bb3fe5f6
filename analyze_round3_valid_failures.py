#!/usr/bin/env python3
"""
Analyze Round 3 results to find why valid protection is only 60.5%
"""

import json
import os
from collections import defaultdict

print("="*80)
print("ANALYZING ROUND 3 VALID PROTECTION FAILURES")
print("="*80)

# Load Round 3 results
with open('valo_batch_round3_complete.json', 'r') as f:
    round3_data = json.load(f)

results = round3_data['results']
stats = round3_data['stats']

print(f"\nOverall Statistics:")
print(f"  Total cases: {stats['total_cases']}")
print(f"  Valid cases: {stats['valid_cases_total']}")
print(f"  Invalid cases: {stats['fp_cases_total']}")
print(f"  Valid Protection Rate: {stats['valid_protection_rate']:.1f}%")
print(f"  FP Detection Rate: {stats['fp_detection_rate']:.1f}%")

# Analyze valid cases that were incorrectly dismissed
valid_dismissed = []
valid_flagged = []
invalid_dismissed = []
invalid_flagged = []

for result in results:
    case_num = result['case_number']
    alert_status = result['alert_status']
    decision = result.get('round3_decision', 'unknown')
    
    if alert_status == 'Valid':
        if decision == 'dismissed':
            valid_dismissed.append(result)
        elif decision == 'flagged':
            valid_flagged.append(result)
    else:
        if decision == 'dismissed':
            invalid_dismissed.append(result)
        elif decision == 'flagged':
            invalid_flagged.append(result)

print(f"\n\nDetailed Breakdown:")
print(f"  Valid cases correctly flagged: {len(valid_flagged)}")
print(f"  Valid cases INCORRECTLY dismissed: {len(valid_dismissed)} ❌")
print(f"  Invalid cases correctly dismissed: {len(invalid_dismissed)}")
print(f"  Invalid cases incorrectly flagged: {len(invalid_flagged)}")

# Analyze VLM responses for incorrectly dismissed valid cases
if valid_dismissed:
    print(f"\n\nAnalyzing {len(valid_dismissed)} Valid Cases That Were Incorrectly Dismissed:")
    print("-"*80)
    
    response_patterns = defaultdict(int)
    
    for i, case in enumerate(valid_dismissed[:10]):  # Show first 10
        print(f"\nCase {i+1}: {case['case_number']}")
        print(f"  Alert Status: {case['alert_status']} (VALID)")
        print(f"  Decision: {case['round3_decision']} ❌")
        print(f"  VLM Response: {case.get('vlm_response', 'No response')[:200]}")
        
        # Count response patterns
        vlm_resp = case.get('vlm_response', '').lower()
        if 'dismiss' in vlm_resp:
            response_patterns['contains_dismiss'] += 1
        if 'flag' in vlm_resp:
            response_patterns['contains_flag'] += 1
        if 'equipment' in vlm_resp:
            response_patterns['mentions_equipment'] += 1
        if 'no person' in vlm_resp or 'no people' in vlm_resp:
            response_patterns['no_person_visible'] += 1
    
    print(f"\n\nResponse Pattern Analysis (all {len(valid_dismissed)} failures):")
    for case in valid_dismissed:
        vlm_resp = case.get('vlm_response', '').lower()
        if 'dismiss' in vlm_resp:
            response_patterns['contains_dismiss'] += 1
        if 'flag' in vlm_resp:
            response_patterns['contains_flag'] += 1
        if 'equipment' in vlm_resp:
            response_patterns['mentions_equipment'] += 1
        if 'no person' in vlm_resp or 'no people' in vlm_resp:
            response_patterns['no_person_visible'] += 1
    
    for pattern, count in response_patterns.items():
        print(f"  {pattern}: {count} ({count/len(valid_dismissed)*100:.1f}%)")

# Save detailed failure analysis
failure_analysis = {
    'total_valid_cases': stats['valid_cases_total'],
    'valid_correctly_flagged': len(valid_flagged),
    'valid_incorrectly_dismissed': len(valid_dismissed),
    'valid_protection_rate': stats['valid_protection_rate'],
    'failed_cases': valid_dismissed
}

with open('round3_valid_failures_analysis.json', 'w') as f:
    json.dump(failure_analysis, f, indent=2)

print(f"\n\nSummary:")
print(f"  {len(valid_dismissed)} out of {stats['valid_cases_total']} valid cases were incorrectly dismissed")
print(f"  This represents a {len(valid_dismissed)/stats['valid_cases_total']*100:.1f}% failure rate")
print(f"  These MUST be protected in future rounds!")

print("\nDetailed analysis saved to: round3_valid_failures_analysis.json")