#!/usr/bin/env python3
"""
Check if all required services are available
"""

import requests
import os
import json
import pandas as pd

def check_services():
    """Check all required services and files"""
    print("="*80)
    print("CHECKING REQUIRED SERVICES AND FILES")
    print("="*80)
    
    all_good = True
    
    # 1. Check VLM endpoint
    print("\n1. Checking VLM endpoint...")
    vlm_url = "http://100.106.127.35:9500/v1/chat/completions"
    try:
        # Simple test request
        test_payload = {
            "model": "VLM-38B-AWQ",
            "messages": [{"role": "user", "content": "test"}],
            "max_tokens": 10
        }
        response = requests.post(vlm_url, json=test_payload, timeout=10)
        if response.status_code == 200:
            print("   ✅ VLM endpoint is accessible")
        else:
            print(f"   ❌ VLM endpoint returned status {response.status_code}")
            all_good = False
    except Exception as e:
        print(f"   ❌ VLM endpoint not accessible: {str(e)}")
        all_good = False
    
    # 2. Check CSV file
    print("\n2. Checking CSV file...")
    csv_path = 'ai_farm_images_fixed_250703/psa_valo_violation_report_without_images_csv_2025-07-03_14-14-01.CSV'
    if os.path.exists(csv_path):
        df = pd.read_csv(csv_path)
        print(f"   ✅ CSV file found with {len(df)} records")
    else:
        print(f"   ❌ CSV file not found at {csv_path}")
        all_good = False
    
    # 3. Check JSON data file
    print("\n3. Checking JSON data file...")
    json_path = 'valo_batch_round3_complete.json'
    if os.path.exists(json_path):
        with open(json_path, 'r') as f:
            data = json.load(f)
            print(f"   ✅ JSON file found with {len(data.get('results', []))} cases")
    else:
        print(f"   ❌ JSON file not found at {json_path}")
        all_good = False
    
    # 4. Check image directories
    print("\n4. Checking image directories...")
    image_base = 'ai_farm_images_fixed_250703/ai_farm_images_fixed'
    if os.path.exists(image_base):
        valid_dir = os.path.join(image_base, 'valid')
        invalid_dir = os.path.join(image_base, 'invalid')
        
        valid_count = len(os.listdir(valid_dir)) if os.path.exists(valid_dir) else 0
        invalid_count = len(os.listdir(invalid_dir)) if os.path.exists(invalid_dir) else 0
        
        print(f"   ✅ Image directories found")
        print(f"      - Valid images: {valid_count}")
        print(f"      - Invalid images: {invalid_count}")
    else:
        print(f"   ❌ Image directory not found at {image_base}")
        all_good = False
    
    # 5. Check Flask is available
    print("\n5. Checking Flask availability...")
    try:
        import flask
        import flask_cors
        print("   ✅ Flask and Flask-CORS are installed")
    except ImportError as e:
        print(f"   ❌ Missing required package: {e}")
        all_good = False
    
    print("\n" + "="*80)
    if all_good:
        print("✅ ALL SERVICES AND FILES ARE READY!")
        print("You can now run: python3 test_enhanced_processor.py")
    else:
        print("❌ Some services or files are missing. Please check the errors above.")
    print("="*80)
    
    return all_good

if __name__ == "__main__":
    check_services()