version: '3.8'

services:
  # PostgreSQL Database
  database:
    image: postgres:15-alpine
    container_name: ai-farm-db-prod
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-ai_farm}
      POSTGRES_USER: ${POSTGRES_USER:-ai_farm_user}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      PGDATA: /var/lib/postgresql/data/pgdata
    volumes:
      - postgres_data_prod:/var/lib/postgresql/data
      - ../database/init:/docker-entrypoint-initdb.d
      - ../database/backup:/backup
    networks:
      - ai-farm-network-prod
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-ai_farm_user} -d ${POSTGRES_DB:-ai_farm}"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    restart: always
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M

  # FastAPI Backend
  backend:
    build:
      context: ../backend
      dockerfile: Dockerfile
      target: production
    container_name: ai-farm-backend-prod
    environment:
      # Database Configuration
      DATABASE_URL: postgresql://${POSTGRES_USER:-ai_farm_user}:${POSTGRES_PASSWORD}@database:5432/${POSTGRES_DB:-ai_farm}
      DATABASE_ECHO: false
      
      # Server Configuration
      HOST: 0.0.0.0
      PORT: 8000
      DEBUG: false
      LOG_LEVEL: ${LOG_LEVEL:-INFO}
      
      # VLM API Configuration
      VLM_API_BASE_URL: ${VLM_API_BASE_URL}
      VLM_API_KEY: ${VLM_API_KEY}
      VLM_MODEL_NAME: ${VLM_MODEL_NAME:-internvl3-38b}
      VLM_MAX_TOKENS: ${VLM_MAX_TOKENS:-1000}
      VLM_TEMPERATURE: ${VLM_TEMPERATURE:-0.1}
      VLM_TIMEOUT_SECONDS: ${VLM_TIMEOUT_SECONDS:-30}
      
      # Processing Configuration
      BATCH_SIZE: ${BATCH_SIZE:-10}
      MAX_CONCURRENT_REQUESTS: ${MAX_CONCURRENT_REQUESTS:-3}
      PROCESSING_TIMEOUT_MINUTES: ${PROCESSING_TIMEOUT_MINUTES:-60}
      IMAGE_MAX_SIZE_MB: ${IMAGE_MAX_SIZE_MB:-10}
      
      # Confidence Thresholds
      THRESHOLD_STRUCTURE_MISID: ${THRESHOLD_STRUCTURE_MISID:-70}
      THRESHOLD_PROPER_PPE: ${THRESHOLD_PROPER_PPE:-65}
      THRESHOLD_NO_VIOLATION: ${THRESHOLD_NO_VIOLATION:-75}
      THRESHOLD_DEFAULT: ${THRESHOLD_DEFAULT:-70}
      
      # Auto-Learning Configuration
      ENABLE_AUTO_LEARNING: ${ENABLE_AUTO_LEARNING:-true}
      LEARNING_BATCH_SIZE: ${LEARNING_BATCH_SIZE:-50}
      CONFIDENCE_CALIBRATION_ENABLED: ${CONFIDENCE_CALIBRATION_ENABLED:-true}
      PATTERN_DETECTION_ENABLED: ${PATTERN_DETECTION_ENABLED:-true}
      
      # Customer Data Processing
      CUSTOMER_DATA_RETENTION_HOURS: ${CUSTOMER_DATA_RETENTION_HOURS:-24}
      ENABLE_CUSTOMER_DATA_CLEANUP: ${ENABLE_CUSTOMER_DATA_CLEANUP:-true}
      SECURE_TEMP_STORAGE: ${SECURE_TEMP_STORAGE:-true}
      
      # Performance Settings
      WORKER_PROCESSES: ${WORKER_PROCESSES:-4}
      KEEP_ALIVE_TIMEOUT: ${KEEP_ALIVE_TIMEOUT:-65}
      MAX_REQUEST_SIZE_MB: ${MAX_REQUEST_SIZE_MB:-100}
      
      # Monitoring
      ENABLE_METRICS: ${ENABLE_METRICS:-true}
      METRICS_PORT: ${METRICS_PORT:-9090}
      ENABLE_HEALTH_CHECK: ${ENABLE_HEALTH_CHECK:-true}
    volumes:
      - ai_farm_data_prod:/app/data
      - ai_farm_logs_prod:/app/logs
      - ai_farm_exports_prod:/app/exports
      - ai_farm_temp_prod:/tmp/ai_farm_uploads
    networks:
      - ai-farm-network-prod
    depends_on:
      database:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    restart: always
    deploy:
      replicas: 2
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M

  # React Frontend with Nginx
  frontend:
    build:
      context: ../frontend
      dockerfile: Dockerfile
      target: production
    container_name: ai-farm-frontend-prod
    networks:
      - ai-farm-network-prod
    depends_on:
      - backend
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 5s
      retries: 3
      start_period: 30s
    restart: always
    deploy:
      resources:
        limits:
          memory: 256M
        reservations:
          memory: 128M

  # Reverse Proxy (Nginx)
  nginx:
    image: nginx:alpine
    container_name: ai-farm-nginx-prod
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - ./nginx/logs:/var/log/nginx
    networks:
      - ai-farm-network-prod
    depends_on:
      - frontend
      - backend
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 5s
      retries: 3
      start_period: 30s
    restart: always
    deploy:
      resources:
        limits:
          memory: 128M
        reservations:
          memory: 64M

  # Redis (for caching and session management)
  redis:
    image: redis:7-alpine
    container_name: ai-farm-redis-prod
    volumes:
      - redis_data_prod:/data
      - ./redis/redis.conf:/usr/local/etc/redis/redis.conf:ro
    networks:
      - ai-farm-network-prod
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 5s
      retries: 3
      start_period: 30s
    restart: always
    deploy:
      resources:
        limits:
          memory: 256M
        reservations:
          memory: 128M
    command: redis-server /usr/local/etc/redis/redis.conf

  # Monitoring - Prometheus (optional)
  prometheus:
    image: prom/prometheus:latest
    container_name: ai-farm-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data_prod:/prometheus
    networks:
      - ai-farm-network-prod
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    restart: unless-stopped
    profiles:
      - monitoring

  # Monitoring - Grafana (optional)
  grafana:
    image: grafana/grafana:latest
    container_name: ai-farm-grafana
    ports:
      - "3001:3000"
    environment:
      GF_SECURITY_ADMIN_PASSWORD: ${GRAFANA_ADMIN_PASSWORD:-admin123}
      GF_USERS_ALLOW_SIGN_UP: false
    volumes:
      - grafana_data_prod:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning
    networks:
      - ai-farm-network-prod
    restart: unless-stopped
    profiles:
      - monitoring

volumes:
  postgres_data_prod:
    driver: local
  redis_data_prod:
    driver: local
  ai_farm_data_prod:
    driver: local
  ai_farm_logs_prod:
    driver: local
  ai_farm_exports_prod:
    driver: local
  ai_farm_temp_prod:
    driver: local
  prometheus_data_prod:
    driver: local
  grafana_data_prod:
    driver: local

networks:
  ai-farm-network-prod:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16