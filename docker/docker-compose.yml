version: '3.8'

services:
  # PostgreSQL Database
  database:
    image: postgres:15-alpine
    container_name: ai-farm-db
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-ai_farm}
      POSTGRES_USER: ${POSTGRES_USER:-ai_farm_user}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-ai_farm_password}
      PGDATA: /var/lib/postgresql/data/pgdata
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ../database/init:/docker-entrypoint-initdb.d
    networks:
      - ai-farm-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-ai_farm_user} -d ${POSTGRES_DB:-ai_farm}"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    restart: unless-stopped

  # FastAPI Backend
  backend:
    build:
      context: ../backend
      dockerfile: Dockerfile
      target: development
    container_name: ai-farm-backend
    environment:
      # Database Configuration
      DATABASE_URL: postgresql://${POSTGRES_USER:-ai_farm_user}:${POSTGRES_PASSWORD:-ai_farm_password}@database:5432/${POSTGRES_DB:-ai_farm}
      DATABASE_ECHO: ${DATABASE_ECHO:-false}
      
      # Server Configuration
      HOST: 0.0.0.0
      PORT: 8000
      DEBUG: ${DEBUG:-true}
      LOG_LEVEL: ${LOG_LEVEL:-INFO}
      
      # VLM API Configuration
      VLM_API_BASE_URL: ${VLM_API_BASE_URL:-http://localhost:8080}
      VLM_API_KEY: ${VLM_API_KEY:-}
      VLM_MODEL_NAME: ${VLM_MODEL_NAME:-internvl3-38b}
      VLM_MAX_TOKENS: ${VLM_MAX_TOKENS:-1000}
      VLM_TEMPERATURE: ${VLM_TEMPERATURE:-0.1}
      VLM_TIMEOUT_SECONDS: ${VLM_TIMEOUT_SECONDS:-30}
      
      # Processing Configuration
      BATCH_SIZE: ${BATCH_SIZE:-10}
      MAX_CONCURRENT_REQUESTS: ${MAX_CONCURRENT_REQUESTS:-3}
      PROCESSING_TIMEOUT_MINUTES: ${PROCESSING_TIMEOUT_MINUTES:-60}
      IMAGE_MAX_SIZE_MB: ${IMAGE_MAX_SIZE_MB:-10}
      
      # Confidence Thresholds
      THRESHOLD_STRUCTURE_MISID: ${THRESHOLD_STRUCTURE_MISID:-70}
      THRESHOLD_PROPER_PPE: ${THRESHOLD_PROPER_PPE:-65}
      THRESHOLD_NO_VIOLATION: ${THRESHOLD_NO_VIOLATION:-75}
      THRESHOLD_DEFAULT: ${THRESHOLD_DEFAULT:-70}
      
      # Auto-Learning Configuration
      ENABLE_AUTO_LEARNING: ${ENABLE_AUTO_LEARNING:-true}
      LEARNING_BATCH_SIZE: ${LEARNING_BATCH_SIZE:-50}
      CONFIDENCE_CALIBRATION_ENABLED: ${CONFIDENCE_CALIBRATION_ENABLED:-true}
      PATTERN_DETECTION_ENABLED: ${PATTERN_DETECTION_ENABLED:-true}
      
      # Customer Data Processing
      CUSTOMER_DATA_RETENTION_HOURS: ${CUSTOMER_DATA_RETENTION_HOURS:-24}
      ENABLE_CUSTOMER_DATA_CLEANUP: ${ENABLE_CUSTOMER_DATA_CLEANUP:-true}
      SECURE_TEMP_STORAGE: ${SECURE_TEMP_STORAGE:-true}
      
      # Performance Settings
      WORKER_PROCESSES: ${WORKER_PROCESSES:-4}
      KEEP_ALIVE_TIMEOUT: ${KEEP_ALIVE_TIMEOUT:-65}
      MAX_REQUEST_SIZE_MB: ${MAX_REQUEST_SIZE_MB:-100}
      
      # Monitoring
      ENABLE_METRICS: ${ENABLE_METRICS:-true}
      METRICS_PORT: ${METRICS_PORT:-9090}
      ENABLE_HEALTH_CHECK: ${ENABLE_HEALTH_CHECK:-true}
    ports:
      - "8000:8000"
    volumes:
      - ../backend:/app
      - ai_farm_data:/app/data
      - ai_farm_logs:/app/logs
      - ai_farm_exports:/app/exports
      - /tmp/ai_farm_uploads:/tmp/ai_farm_uploads
    networks:
      - ai-farm-network
    depends_on:
      database:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    restart: unless-stopped
    develop:
      watch:
        - action: sync
          path: ../backend
          target: /app
          ignore:
            - __pycache__/
            - "*.pyc"
            - .pytest_cache/
            - logs/

  # React Frontend
  frontend:
    build:
      context: ../frontend
      dockerfile: Dockerfile
      target: development
    container_name: ai-farm-frontend
    environment:
      - REACT_APP_API_URL=http://localhost:8000
      - REACT_APP_ENV=development
      - CHOKIDAR_USEPOLLING=true
      - WDS_SOCKET_HOST=0.0.0.0
      - WDS_SOCKET_PORT=0
    ports:
      - "3000:3000"
    volumes:
      - ../frontend:/app
      - /app/node_modules
    networks:
      - ai-farm-network
    depends_on:
      - backend
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    restart: unless-stopped
    develop:
      watch:
        - action: sync
          path: ../frontend/src
          target: /app/src
        - action: sync
          path: ../frontend/public
          target: /app/public

  # Redis (for caching and session management)
  redis:
    image: redis:7-alpine
    container_name: ai-farm-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - ai-farm-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 3
      start_period: 10s
    restart: unless-stopped
    command: redis-server --appendonly yes

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  ai_farm_data:
    driver: local
  ai_farm_logs:
    driver: local
  ai_farm_exports:
    driver: local

networks:
  ai-farm-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16