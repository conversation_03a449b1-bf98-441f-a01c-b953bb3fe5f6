# Docker Compose Override for Local Development
# This file provides additional development-specific overrides
# It will be automatically loaded by docker-compose

version: '3.8'

services:
  backend:
    environment:
      # Development-specific environment variables
      - PYTHONUNBUFFERED=1
      - PYTHONDONTWRITEBYTECODE=1
      - WATCHDOG_POLL_INTERVAL=1
    volumes:
      # Additional volume mounts for development
      - ./backend/logs:/app/logs
      - ./backend/exports:/app/exports
    ports:
      # Additional ports for debugging
      - "8001:8001"  # Debug port if needed
    command: ["python", "-m", "uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000", "--reload", "--log-level", "debug"]

  frontend:
    environment:
      # Development environment variables
      - GENERATE_SOURCEMAP=true
      - REACT_APP_DEBUG=true
    volumes:
      # Ensure node_modules is not overridden
      - /app/node_modules
    stdin_open: true
    tty: true

  database:
    environment:
      # Additional development database settings
      - POSTGRES_INITDB_ARGS=--auth-host=scram-sha-256
    ports:
      # Expose database port for local development tools
      - "5432:5432"
    volumes:
      # Additional volume for database backups
      - ./database/backups:/backups

  redis:
    ports:
      # Expose Redis port for local development tools
      - "6379:6379"
    command: redis-server --appendonly yes --maxmemory 128mb --maxmemory-policy allkeys-lru