# AI-FARM Docker Ignore File

# Git
.git
.gitignore

# Documentation
*.md
docs/

# Node modules (will be installed in container)
frontend/node_modules
frontend/build

# Python cache
**/__pycache__
**/*.pyc
**/*.pyo
**/*.pyd
.Python
pip-log.txt
pip-delete-this-directory.txt
.tox
.coverage
.coverage.*
.pytest_cache
nosetests.xml
coverage.xml
*.cover
*.log
.cache

# Virtual environments
venv/
env/
ENV/

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Environment files (should be configured separately)
.env
.env.local
.env.development
.env.production

# Logs
logs/
*.log

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Database files
*.db
*.sqlite
*.sqlite3

# Temporary files
tmp/
temp/
.tmp/

# Backup files
backup/
*.bak
*.backup

# Test coverage
htmlcov/
.coverage
coverage.xml

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Application specific
data/images/
exports/
processed/