SAFETY VIOLATION ANALYSIS - HYBRID APPROACH

This is a critical safety system. Valid violations must be protected.

ANALYZE BOTH IMAGES:
- SOURCE: Full context view  
- CROPPED: Area of concern

STEP 1 - INITIAL CLASSIFICATION:

A) DEFINITE STRUCTURE (>90% confidence):
   Clear industrial equipment with NO human features:
   - Crane beams, vessel rails, spreader frames
   - Camera structures, cell guides
   - Pure mechanical/geometric forms
   
B) DEFINITE PERSON (any confidence):
   Any human characteristics visible:
   - Human shape, clothing, PPE
   - Organic forms, movement
   
C) MIXED/UNCERTAIN:
   - Both person and structure
   - Cannot determine clearly

STEP 2 - VIOLATION ASSESSMENT:

For DEFINITE STRUCTURE:
→ FALSE POSITIVE (unless person visible anywhere)

For DEFINITE PERSON:
→ Check ALL violations:

PPE CHECKS:
- Helmet present and worn properly?
- Vest present and fastened?
- Required equipment (life jacket, GO/STOP bat)?

BEHAVIORAL CHECKS:
- Mobile device use?
- Proper location/authorization?
- Following safety procedures?
- Maintaining safe distances?

For MIXED/UNCERTAIN:
→ Default to VALID VIOLATION

DECISION MATRIX:
┌─────────────────────┬────────────────┬──────────┐
│ Detection           │ Violations     │ Result   │
├─────────────────────┼────────────────┼──────────┤
│ Structure (>90%)    │ None possible  │ FALSE POS│
│ Person              │ Any violation  │ VALID    │
│ Person              │ None found     │ FALSE POS│
│ Uncertain           │ Any concern    │ VALID    │
└─────────────────────┴────────────────┴──────────┘

OUTPUT:
FALSE POSITIVE: [YES/NO]
Detection: [STRUCTURE/PERSON/UNCERTAIN]
Violations: [List any found]
Confidence: [%]