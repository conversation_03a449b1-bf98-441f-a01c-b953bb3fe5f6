#!/usr/bin/env python3
"""
Monitor progress of enhanced prompt testing
"""

import json
import os
import time
from datetime import datetime

def monitor_progress():
    """Monitor the enhanced prompt testing progress"""
    
    progress_file = 'enhanced_1250_progress.json'
    
    print("MONITORING ENHANCED PROMPT TEST PROGRESS")
    print("="*60)
    print(f"Start time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("\nPress Ctrl+C to stop monitoring\n")
    
    last_count = 0
    
    try:
        while True:
            if os.path.exists(progress_file):
                with open(progress_file, 'r') as f:
                    progress = json.load(f)
                
                total_cases = progress.get('total_cases', 0)
                results = progress.get('results', [])
                
                if total_cases > last_count:
                    # Calculate metrics
                    correct = sum(r['correct'] for r in results)
                    accuracy = correct / total_cases * 100 if total_cases > 0 else 0
                    
                    # FP detection rate
                    actual_fps = [r for r in results if r['actual_fp']]
                    if actual_fps:
                        fp_detected = sum(r['predicted_fp'] for r in actual_fps)
                        fp_rate = fp_detected / len(actual_fps) * 100
                    else:
                        fp_rate = 0
                    
                    # Valid protection rate
                    actual_valid = [r for r in results if not r['actual_fp']]
                    if actual_valid:
                        valid_protected = sum(not r['predicted_fp'] for r in actual_valid)
                        protection_rate = valid_protected / len(actual_valid) * 100
                    else:
                        protection_rate = 0
                    
                    # Entity detection stats
                    entity_counts = {}
                    for r in results:
                        entity = r.get('entity_type', 'UNKNOWN')
                        entity_counts[entity] = entity_counts.get(entity, 0) + 1
                    
                    # Clear screen and show update
                    os.system('clear' if os.name == 'posix' else 'cls')
                    
                    print("ENHANCED PROMPT TEST PROGRESS")
                    print("="*60)
                    print(f"Time: {datetime.now().strftime('%H:%M:%S')}")
                    print(f"\nProgress: {total_cases}/1250 ({total_cases/1250*100:.1f}%)")
                    print(f"Rate: {(total_cases - last_count)/30:.1f} cases/min")
                    
                    print(f"\nPERFORMANCE METRICS:")
                    print(f"├─ Overall Accuracy: {accuracy:.1f}%")
                    print(f"├─ FP Detection Rate: {fp_rate:.1f}% (Baseline: 76.3%)")
                    print(f"├─ Valid Protection: {protection_rate:.1f}%")
                    print(f"└─ Improvement: {fp_rate - 76.3:+.1f}%")
                    
                    print(f"\nENTITY DETECTION:")
                    for entity, count in sorted(entity_counts.items(), key=lambda x: x[1], reverse=True):
                        pct = count / total_cases * 100
                        print(f"├─ {entity}: {count} ({pct:.1f}%)")
                    
                    # Show recent cases
                    print(f"\nRECENT CASES:")
                    for r in results[-5:]:
                        status = "✓" if r['correct'] else "✗"
                        print(f"{status} {r['case_number']} - {r['entity_type']}")
                    
                    if fp_rate > 76.3:
                        print(f"\n✨ EXCEEDING BASELINE BY {fp_rate - 76.3:+.1f}%!")
                    
                    last_count = total_cases
            
            time.sleep(30)  # Check every 30 seconds
            
    except KeyboardInterrupt:
        print("\n\nMonitoring stopped")
        
        # Show final stats if available
        if os.path.exists(progress_file):
            with open(progress_file, 'r') as f:
                progress = json.load(f)
            
            total_cases = progress.get('total_cases', 0)
            print(f"\nFinal count: {total_cases}/1250 cases")
            
            if total_cases == 1250:
                print("\n✓ TEST COMPLETE! Check enhanced_prompt_1250_final_report.json for results")

if __name__ == "__main__":
    monitor_progress()