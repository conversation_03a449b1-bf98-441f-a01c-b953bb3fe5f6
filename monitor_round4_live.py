#!/usr/bin/env python3
"""
Monitor Round 4 progress in real-time
"""

import time
import os
import json
from datetime import datetime

print("="*80)
print("MONITORING ROUND 4: EQUIPMENT PATTERN RECOGNITION")
print("Target: 40% FP Detection with 100% Valid Protection")
print("="*80)

start_time = time.time()
last_progress = 0

while True:
    # Check if Round 4 completed
    if os.path.exists('valo_round4_full_complete.json'):
        print("\n✅ ROUND 4 COMPLETE!")
        
        with open('valo_round4_full_complete.json', 'r') as f:
            data = json.load(f)
            stats = data['stats']
        
        print(f"\nFinal Statistics:")
        print(f"  Total cases: {stats['total_cases']}")
        print(f"  Valid Protection: {stats['valid_protection_rate']:.1f}%")
        print(f"  FP Detection: {stats['fp_detection_rate']:.1f}%")
        print(f"  Improvement over Round 3: +{stats['improvement_over_round3']:.1f}%")
        
        if stats['fp_detection_rate'] >= 70:
            print("\n🎯 TARGET ACHIEVED! 70% FP reduction reached!")
        else:
            print(f"\nGap to 70%: {70 - stats['fp_detection_rate']:.1f}%")
        
        print(f"\nTime taken: {(time.time() - start_time)/60:.1f} minutes")
        break
    
    # Read log file for progress
    if os.path.exists('round4_full_dataset.log'):
        try:
            with open('round4_full_dataset.log', 'r') as f:
                lines = f.readlines()
                
            # Find latest progress line
            for line in reversed(lines[-50:]):
                if 'Progress:' in line and '| Valid:' in line:
                    # Extract progress
                    parts = line.strip().split('Progress: ')[1].split(' | ')
                    progress_str = parts[0]
                    current, total = map(int, progress_str.split('/'))
                    
                    # Extract rates
                    valid_rate = float(parts[1].split(': ')[1].replace('%', ''))
                    fp_rate = float(parts[2].split(': ')[1].replace('%', ''))
                    
                    # Calculate ETA
                    if current > last_progress:
                        elapsed = time.time() - start_time
                        rate = current / elapsed
                        remaining = (total - current) / rate if rate > 0 else 0
                        eta_min = remaining / 60
                        
                        print(f"\r[{datetime.now().strftime('%H:%M:%S')}] Progress: {current}/{total} ({current/total*100:.1f}%) | Valid: {valid_rate:.1f}% | FP: {fp_rate:.1f}% | ETA: {eta_min:.1f} min", end='', flush=True)
                        last_progress = current
                    break
                    
        except Exception as e:
            pass
    
    # Check for 70% achievement file
    if os.path.exists('VALO_70_PERCENT_ACHIEVED.json'):
        print("\n\n🎯 70% TARGET ACHIEVED FILE DETECTED!")
        with open('VALO_70_PERCENT_ACHIEVED.json', 'r') as f:
            achievement = json.load(f)
        print(f"Rounds completed: {achievement['rounds_completed']}")
        print(f"Final FP Detection: {achievement['final_stats']['fp_detection_rate']:.1f}%")
        break
    
    time.sleep(5)