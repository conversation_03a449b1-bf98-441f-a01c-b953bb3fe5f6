"""
End-to-End test for VLM-38B-AWQ workflow
Tests the complete pipeline from CSV upload to results retrieval
"""

import asyncio
import pandas as pd
import requests
import time
import json
from pathlib import Path
import logging
import os
import sys

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Configuration
API_BASE_URL = "http://localhost:8000"
TIMEOUT = 300  # 5 minutes for processing


def create_test_csv():
    """Create a test CSV file with sample data"""
    test_data = {
        'pk_event': [1001, 1002, 1003, 1004, 1005],
        'case_number': [
            'V1250630118', 
            'V1250630119', 
            'V1250630120',
            'V1250630121',
            'V1250630122'
        ],
        'url': [
            'data/sample_images/test_image.jpg',
            'data/sample_images/batch_test_red.jpg',
            'data/sample_images/batch_test_green.jpg',
            'data/sample_images/batch_test_blue.jpg',
            'data/sample_images/test_image.jpg'
        ],
        'key': ['invalid', 'invalid', 'valid', 'invalid', 'invalid']
    }
    
    df = pd.DataFrame(test_data)
    csv_path = Path("test_batch_data.csv")
    df.to_csv(csv_path, index=False)
    logger.info(f"Created test CSV with {len(df)} records")
    return csv_path


def test_health_check():
    """Test API health check"""
    logger.info("=" * 60)
    logger.info("Testing API Health Check")
    logger.info("=" * 60)
    
    try:
        response = requests.get(f"{API_BASE_URL}/health")
        if response.status_code == 200:
            # Try to parse as JSON, fall back to text
            try:
                data = response.json()
                logger.info(f"✅ API is healthy: {data}")
                logger.info(f"  - Status: {data.get('status', 'OK')}")
                logger.info(f"  - VLM API Status: {data.get('vlm_api_status', 'N/A')}")
                logger.info(f"  - Database Status: {data.get('database_status', 'N/A')}")
                logger.info(f"  - Version: {data.get('version', 'N/A')}")
            except:
                # Simple text response
                logger.info(f"✅ API is healthy: {response.text}")
            return True
        else:
            logger.error(f"❌ Health check failed: {response.status_code}")
            return False
    except Exception as e:
        logger.error(f"❌ Health check error: {str(e)}")
        return False


def test_batch_upload(csv_path):
    """Test batch upload endpoint"""
    logger.info("\n" + "=" * 60)
    logger.info("Testing Batch Upload")
    logger.info("=" * 60)
    
    try:
        with open(csv_path, 'rb') as f:
            files = {'csv_file': ('test_batch.csv', f, 'text/csv')}
            response = requests.post(f"{API_BASE_URL}/api/v1/batch/upload", files=files)
        
        if response.status_code == 200:
            data = response.json()
            batch_id = data['batch_id']
            logger.info(f"✅ Batch uploaded successfully")
            logger.info(f"  - Batch ID: {batch_id}")
            logger.info(f"  - Total Count: {data['total_count']}")
            logger.info(f"  - Status: {data['status']}")
            return batch_id
        else:
            logger.error(f"❌ Batch upload failed: {response.status_code}")
            logger.error(f"Response: {response.text}")
            return None
    except Exception as e:
        logger.error(f"❌ Batch upload error: {str(e)}")
        return None


def wait_for_processing(batch_id, timeout=TIMEOUT):
    """Wait for batch processing to complete"""
    logger.info("\n" + "=" * 60)
    logger.info("Waiting for Batch Processing")
    logger.info("=" * 60)
    
    start_time = time.time()
    last_processed = 0
    
    while time.time() - start_time < timeout:
        try:
            response = requests.get(f"{API_BASE_URL}/api/v1/batch/{batch_id}")
            if response.status_code == 200:
                data = response.json()
                status = data['status']
                processed = data.get('processed_count', 0)
                total = data.get('total_count', 0)
                
                if processed > last_processed:
                    logger.info(f"Processing: {processed}/{total} ({processed/total*100:.1f}%)")
                    last_processed = processed
                
                if status == 'completed':
                    logger.info(f"✅ Processing completed in {time.time() - start_time:.1f}s")
                    return True
                elif status == 'failed':
                    logger.error(f"❌ Processing failed: {data.get('error_message', 'Unknown error')}")
                    return False
            else:
                logger.error(f"Failed to get batch status: {response.status_code}")
                
        except Exception as e:
            logger.error(f"Error checking status: {str(e)}")
        
        time.sleep(5)  # Check every 5 seconds
    
    logger.error(f"❌ Processing timeout after {timeout}s")
    return False


def get_batch_results(batch_id):
    """Get and analyze batch results"""
    logger.info("\n" + "=" * 60)
    logger.info("Retrieving Batch Results")
    logger.info("=" * 60)
    
    try:
        response = requests.get(f"{API_BASE_URL}/api/v1/batch/{batch_id}/results")
        if response.status_code == 200:
            data = response.json()
            results = data['results']
            
            logger.info(f"✅ Retrieved {len(results)} results")
            
            # Analyze results
            false_positives = 0
            true_violations = 0
            errors = 0
            
            for result in results:
                if result['detection_type'] == 'ERROR':
                    errors += 1
                elif result['recommendation'] == 'DISMISS_ALERT':
                    false_positives += 1
                else:
                    true_violations += 1
                
                logger.info(f"\nCase {result['case_number']}:")
                logger.info(f"  - Detection: {result['detection_type']}")
                logger.info(f"  - FP Likelihood: {result['false_positive_likelihood']}%")
                logger.info(f"  - Recommendation: {result['recommendation']}")
                logger.info(f"  - Confidence: {result['confidence_score']}")
                logger.info(f"  - Processing Time: {result.get('processing_time_ms', 0)}ms")
            
            # Summary statistics
            logger.info("\n" + "-" * 40)
            logger.info("Summary Statistics:")
            logger.info(f"  - False Positives Detected: {false_positives}")
            logger.info(f"  - True Violations: {true_violations}")
            logger.info(f"  - Errors: {errors}")
            logger.info(f"  - FP Reduction Rate: {false_positives/len(results)*100:.1f}%")
            
            return results
        else:
            logger.error(f"❌ Failed to get results: {response.status_code}")
            return None
    except Exception as e:
        logger.error(f"❌ Error getting results: {str(e)}")
        return None


def test_metrics_endpoint(batch_id):
    """Test metrics endpoint"""
    logger.info("\n" + "=" * 60)
    logger.info("Testing Metrics Endpoint")
    logger.info("=" * 60)
    
    try:
        response = requests.get(f"{API_BASE_URL}/api/v1/metrics")
        if response.status_code == 200:
            data = response.json()
            logger.info("✅ Metrics retrieved successfully")
            logger.info(f"  - Total Batches: {data['total_batches']}")
            logger.info(f"  - Total Images: {data['total_images_processed']}")
            logger.info(f"  - Avg FP Reduction: {data['average_false_positive_reduction']:.1f}%")
            logger.info(f"  - Processing Success Rate: {data['processing_success_rate']:.1f}%")
            return True
        else:
            logger.error(f"❌ Failed to get metrics: {response.status_code}")
            return False
    except Exception as e:
        logger.error(f"❌ Error getting metrics: {str(e)}")
        return False


def cleanup_test_files():
    """Clean up test files"""
    test_files = [
        "test_batch_data.csv",
        "data/sample_images/test_image.jpg",
        "data/sample_images/batch_test_red.jpg",
        "data/sample_images/batch_test_green.jpg",
        "data/sample_images/batch_test_blue.jpg"
    ]
    
    for file in test_files:
        path = Path(file)
        if path.exists():
            path.unlink()
            logger.info(f"Cleaned up: {file}")


def main():
    """Run complete E2E test workflow"""
    logger.info("\n" + "🚀 Starting VLM-38B-AWQ End-to-End Test 🚀")
    logger.info("=" * 60)
    
    # Check if API is running
    logger.info("Checking if API server is running...")
    try:
        requests.get(f"{API_BASE_URL}/health", timeout=5)
    except:
        logger.error("❌ API server is not running. Please start it with: npm run dev:backend")
        return
    
    all_tests_passed = True
    
    # Test 1: Health Check
    if not test_health_check():
        logger.error("Health check failed. Cannot continue.")
        return
    
    # Test 2: Create test data
    csv_path = create_test_csv()
    
    # Test 3: Upload batch
    batch_id = test_batch_upload(csv_path)
    if not batch_id:
        all_tests_passed = False
        logger.error("Batch upload failed. Cannot continue.")
        return
    
    # Test 4: Wait for processing
    if not wait_for_processing(batch_id):
        all_tests_passed = False
        logger.error("Batch processing failed or timed out.")
    
    # Test 5: Get results
    results = get_batch_results(batch_id)
    if not results:
        all_tests_passed = False
    
    # Test 6: Check metrics
    if not test_metrics_endpoint(batch_id):
        all_tests_passed = False
    
    # Summary
    logger.info("\n" + "=" * 60)
    if all_tests_passed:
        logger.info("✅ All E2E tests PASSED!")
        logger.info("The VLM-38B-AWQ integration is working end-to-end.")
    else:
        logger.info("❌ Some E2E tests FAILED.")
        logger.info("Please check the errors above.")
    logger.info("=" * 60)
    
    # Cleanup
    cleanup_test_files()


if __name__ == "__main__":
    # Set environment variables if not already set
    if 'VLM_API_BASE_URL' not in os.environ:
        os.environ['VLM_API_BASE_URL'] = 'http://100.106.127.35:9500/v1'
        os.environ['VLM_API_KEY'] = 'token-abc123'
        os.environ['VLM_MODEL_NAME'] = 'VLM-38B-AWQ'
    
    main()