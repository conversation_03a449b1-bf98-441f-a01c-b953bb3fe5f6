"""
Comprehensive tests for Configuration System
Tests settings validation, environment variable handling, and configuration methods
"""

import pytest
import os
import tempfile
from pathlib import Path
from unittest.mock import patch, Mock
from pydantic import ValidationError

from app.core.config import Settings, settings


class TestSettingsInitialization:
    """Test Settings class initialization and defaults"""
    
    def test_default_settings(self):
        """Test default settings are correctly initialized"""
        test_settings = Settings()
        
        # VLM API Configuration
        assert test_settings.vlm_api_base_url == "http://localhost:8080"
        assert test_settings.vlm_api_key == ""
        assert test_settings.vlm_model_name == "internvl3-38b"
        assert test_settings.vlm_max_tokens == 1000
        assert test_settings.vlm_temperature == 0.1
        assert test_settings.vlm_timeout_seconds == 30
        
        # Processing Configuration
        assert test_settings.batch_size == 10
        assert test_settings.max_concurrent_requests == 3
        assert test_settings.processing_timeout_minutes == 60
        assert test_settings.image_max_size_mb == 10
        
        # Confidence Thresholds
        assert test_settings.threshold_structure_misid == 70
        assert test_settings.threshold_proper_ppe == 65
        assert test_settings.threshold_no_violation == 75
        assert test_settings.threshold_default == 70
        
        # Auto-Learning Configuration
        assert test_settings.enable_auto_learning is True
        assert test_settings.learning_batch_size == 50
        assert test_settings.confidence_calibration_enabled is True
        assert test_settings.pattern_detection_enabled is True
        
        # Database Configuration
        assert test_settings.database_url == "sqlite:///./ai_farm.db"
        assert test_settings.database_echo is False
        
        # Server Configuration
        assert test_settings.host == "0.0.0.0"
        assert test_settings.port == 8000
        assert test_settings.debug is False
        assert test_settings.log_level == "INFO"
    
    def test_settings_with_custom_values(self):
        """Test settings initialization with custom values"""
        custom_settings = Settings(
            vlm_api_base_url="https://custom-api.example.com",
            vlm_api_key="custom-key-123",
            vlm_model_name="custom-model",
            batch_size=20,
            debug=True,
            log_level="DEBUG"
        )
        
        assert custom_settings.vlm_api_base_url == "https://custom-api.example.com"
        assert custom_settings.vlm_api_key == "custom-key-123"
        assert custom_settings.vlm_model_name == "custom-model"
        assert custom_settings.batch_size == 20
        assert custom_settings.debug is True
        assert custom_settings.log_level == "DEBUG"


class TestEnvironmentVariables:
    """Test environment variable handling"""
    
    def test_env_var_loading(self):
        """Test loading settings from environment variables"""
        with patch.dict(os.environ, {
            'VLM_API_BASE_URL': 'https://env-api.example.com',
            'VLM_API_KEY': 'env-key-456',
            'VLM_MODEL_NAME': 'env-model',
            'BATCH_SIZE': '15',
            'DEBUG': 'true',
            'LOG_LEVEL': 'WARNING'
        }):
            env_settings = Settings()
            
            assert env_settings.vlm_api_base_url == 'https://env-api.example.com'
            assert env_settings.vlm_api_key == 'env-key-456'
            assert env_settings.vlm_model_name == 'env-model'
            assert env_settings.batch_size == 15
            assert env_settings.debug is True
            assert env_settings.log_level == 'WARNING'
    
    def test_case_insensitive_env_vars(self):
        """Test case insensitive environment variable handling"""
        with patch.dict(os.environ, {
            'vlm_api_base_url': 'https://lowercase.example.com',
            'VLM_API_KEY': 'UPPERCASE_KEY',
            'Vlm_Model_Name': 'MixedCase_Model'
        }):
            env_settings = Settings()
            
            assert env_settings.vlm_api_base_url == 'https://lowercase.example.com'
            assert env_settings.vlm_api_key == 'UPPERCASE_KEY'
            assert env_settings.vlm_model_name == 'MixedCase_Model'
    
    def test_invalid_env_var_types(self):
        """Test handling of invalid environment variable types"""
        with patch.dict(os.environ, {
            'BATCH_SIZE': 'not_a_number',
            'DEBUG': 'not_a_boolean',
            'VLM_TIMEOUT_SECONDS': 'invalid_int'
        }):
            with pytest.raises(ValidationError):
                Settings()


class TestFieldValidation:
    """Test field validation and constraints"""
    
    def test_valid_field_values(self):
        """Test valid field values are accepted"""
        valid_settings = Settings(
            vlm_max_tokens=2000,
            vlm_temperature=0.8,
            batch_size=50,
            max_concurrent_requests=10,
            threshold_structure_misid=80,
            threshold_proper_ppe=70,
            threshold_no_violation=85,
            threshold_default=75
        )
        
        assert valid_settings.vlm_max_tokens == 2000
        assert valid_settings.vlm_temperature == 0.8
        assert valid_settings.batch_size == 50
        assert valid_settings.max_concurrent_requests == 10
        assert valid_settings.threshold_structure_misid == 80
    
    def test_path_field_validation(self):
        """Test Path field validation"""
        with tempfile.TemporaryDirectory() as temp_dir:
            test_settings = Settings(
                demo_data_path=Path(temp_dir) / "demo",
                temp_upload_path=Path(temp_dir) / "uploads",
                logs_path=Path(temp_dir) / "logs"
            )
            
            assert isinstance(test_settings.demo_data_path, Path)
            assert isinstance(test_settings.temp_upload_path, Path)
            assert isinstance(test_settings.logs_path, Path)


class TestVLMAPIHeaders:
    """Test VLM API headers property"""
    
    def test_headers_without_api_key(self):
        """Test headers when no API key is provided"""
        test_settings = Settings(vlm_api_key="")
        headers = test_settings.vlm_api_headers
        
        assert headers == {"Content-Type": "application/json"}
        assert "Authorization" not in headers
    
    def test_headers_with_api_key(self):
        """Test headers when API key is provided"""
        test_settings = Settings(vlm_api_key="test-key-123")
        headers = test_settings.vlm_api_headers
        
        assert headers["Content-Type"] == "application/json"
        assert headers["Authorization"] == "Bearer test-key-123"
    
    def test_headers_with_empty_api_key(self):
        """Test headers with empty API key"""
        test_settings = Settings(vlm_api_key="   ")
        headers = test_settings.vlm_api_headers
        
        # Should include authorization header even with whitespace
        assert "Authorization" in headers
        assert headers["Authorization"] == "Bearer    "


class TestThresholdMethods:
    """Test threshold-related methods"""
    
    def test_get_threshold_for_known_categories(self):
        """Test getting thresholds for known categories"""
        test_settings = Settings(
            threshold_structure_misid=75,
            threshold_proper_ppe=68,
            threshold_no_violation=80,
            threshold_default=72
        )
        
        assert test_settings.get_threshold_for_category("structure_misid") == 75
        assert test_settings.get_threshold_for_category("proper_ppe") == 68
        assert test_settings.get_threshold_for_category("no_violation") == 80
    
    def test_get_threshold_for_unknown_category(self):
        """Test getting threshold for unknown category returns default"""
        test_settings = Settings(
            threshold_structure_misid=75,
            threshold_proper_ppe=68,
            threshold_no_violation=80,
            threshold_default=72
        )
        
        assert test_settings.get_threshold_for_category("unknown_category") == 72
        assert test_settings.get_threshold_for_category("") == 72
        assert test_settings.get_threshold_for_category(None) == 72
    
    def test_get_threshold_case_sensitivity(self):
        """Test threshold method case sensitivity"""
        test_settings = Settings(threshold_default=70)
        
        # Should be case sensitive
        assert test_settings.get_threshold_for_category("structure_misid") == 70
        assert test_settings.get_threshold_for_category("STRUCTURE_MISID") == 70
        assert test_settings.get_threshold_for_category("Structure_Misid") == 70


class TestDirectoryCreation:
    """Test directory creation functionality"""
    
    def test_create_directories_success(self):
        """Test successful directory creation"""
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            test_settings = Settings(
                demo_data_path=temp_path / "demo",
                demo_images_path=temp_path / "images",
                temp_upload_path=temp_path / "uploads",
                processed_images_path=temp_path / "processed",
                logs_path=temp_path / "logs",
                export_path=temp_path / "exports"
            )
            
            # Directories shouldn't exist yet
            assert not test_settings.demo_data_path.exists()
            assert not test_settings.demo_images_path.exists()
            
            # Create directories
            test_settings.create_directories()
            
            # All directories should now exist
            assert test_settings.demo_data_path.exists()
            assert test_settings.demo_images_path.exists()
            assert test_settings.temp_upload_path.exists()
            assert test_settings.processed_images_path.exists()
            assert test_settings.logs_path.exists()
            assert test_settings.export_path.exists()
    
    def test_create_directories_already_exist(self):
        """Test directory creation when directories already exist"""
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            test_settings = Settings(
                demo_data_path=temp_path / "demo",
                demo_images_path=temp_path / "images"
            )
            
            # Create directories manually first
            test_settings.demo_data_path.mkdir(parents=True)
            test_settings.demo_images_path.mkdir(parents=True)
            
            # Should not raise error when directories already exist
            test_settings.create_directories()
            
            assert test_settings.demo_data_path.exists()
            assert test_settings.demo_images_path.exists()
    
    def test_create_directories_nested_paths(self):
        """Test directory creation with nested paths"""
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            test_settings = Settings(
                demo_data_path=temp_path / "deep" / "nested" / "demo",
                demo_images_path=temp_path / "very" / "deep" / "nested" / "images"
            )
            
            test_settings.create_directories()
            
            assert test_settings.demo_data_path.exists()
            assert test_settings.demo_images_path.exists()
    
    def test_create_directories_permission_error(self):
        """Test directory creation with permission error"""
        # Try to create directory in system root (should fail)
        test_settings = Settings(
            demo_data_path=Path("/root/forbidden/demo"),
            demo_images_path=Path("/root/forbidden/images")
        )
        
        # Should raise PermissionError on most systems
        with pytest.raises(PermissionError):
            test_settings.create_directories()


class TestGlobalSettingsInstance:
    """Test global settings instance"""
    
    def test_global_settings_instance(self):
        """Test global settings instance is properly initialized"""
        from app.core.config import settings as global_settings
        
        assert isinstance(global_settings, Settings)
        assert hasattr(global_settings, 'vlm_api_base_url')
        assert hasattr(global_settings, 'batch_size')
        assert hasattr(global_settings, 'create_directories')
        assert hasattr(global_settings, 'vlm_api_headers')
        assert hasattr(global_settings, 'get_threshold_for_category')


class TestSettingsIntegration:
    """Integration tests for settings functionality"""
    
    def test_settings_with_env_file(self):
        """Test settings loading from .env file"""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.env', delete=False) as env_file:
            env_file.write("VLM_API_BASE_URL=https://file.example.com\n")
            env_file.write("VLM_API_KEY=file-key-789\n")
            env_file.write("BATCH_SIZE=25\n")
            env_file.write("DEBUG=true\n")
            env_file_path = env_file.name
        
        try:
            # Test settings with custom env file
            file_settings = Settings(_env_file=env_file_path)
            
            assert file_settings.vlm_api_base_url == "https://file.example.com"
            assert file_settings.vlm_api_key == "file-key-789"
            assert file_settings.batch_size == 25
            assert file_settings.debug is True
        finally:
            os.unlink(env_file_path)
    
    def test_env_var_override_file(self):
        """Test environment variables override .env file"""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.env', delete=False) as env_file:
            env_file.write("VLM_API_BASE_URL=https://file.example.com\n")
            env_file.write("BATCH_SIZE=25\n")
            env_file_path = env_file.name
        
        try:
            with patch.dict(os.environ, {
                'VLM_API_BASE_URL': 'https://env-override.example.com',
                'VLM_API_KEY': 'env-override-key'
            }):
                override_settings = Settings(_env_file=env_file_path)
                
                # Environment variable should override file
                assert override_settings.vlm_api_base_url == "https://env-override.example.com"
                assert override_settings.vlm_api_key == "env-override-key"
                # File value should be used when no env var
                assert override_settings.batch_size == 25
        finally:
            os.unlink(env_file_path)
    
    def test_complete_settings_workflow(self):
        """Test complete settings workflow with all features"""
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            
            # Initialize settings with custom paths
            workflow_settings = Settings(
                vlm_api_base_url="https://workflow.example.com",
                vlm_api_key="workflow-key",
                demo_data_path=temp_path / "demo",
                demo_images_path=temp_path / "images",
                threshold_structure_misid=85,
                threshold_proper_ppe=75,
                batch_size=15
            )
            
            # Test API headers
            headers = workflow_settings.vlm_api_headers
            assert headers["Authorization"] == "Bearer workflow-key"
            
            # Test threshold methods
            assert workflow_settings.get_threshold_for_category("structure_misid") == 85
            assert workflow_settings.get_threshold_for_category("proper_ppe") == 75
            assert workflow_settings.get_threshold_for_category("unknown") == 70  # default
            
            # Test directory creation
            workflow_settings.create_directories()
            assert workflow_settings.demo_data_path.exists()
            assert workflow_settings.demo_images_path.exists()
            
            # Test all expected attributes exist
            required_attrs = [
                'vlm_api_base_url', 'vlm_api_key', 'vlm_model_name',
                'batch_size', 'max_concurrent_requests', 'threshold_default',
                'enable_auto_learning', 'database_url', 'host', 'port',
                'demo_data_path', 'customer_data_retention_hours'
            ]
            
            for attr in required_attrs:
                assert hasattr(workflow_settings, attr)
                assert getattr(workflow_settings, attr) is not None


class TestSettingsEdgeCases:
    """Test edge cases and error conditions"""
    
    def test_zero_and_negative_values(self):
        """Test handling of zero and negative values"""
        # These should work (zero/negative might be valid for some fields)
        edge_settings = Settings(
            vlm_timeout_seconds=0,
            batch_size=1,
            max_concurrent_requests=1,
            vlm_temperature=0.0
        )
        
        assert edge_settings.vlm_timeout_seconds == 0
        assert edge_settings.batch_size == 1
        assert edge_settings.max_concurrent_requests == 1
        assert edge_settings.vlm_temperature == 0.0
    
    def test_extreme_values(self):
        """Test handling of extreme values"""
        extreme_settings = Settings(
            vlm_max_tokens=100000,
            batch_size=1000,
            vlm_temperature=1.0,
            customer_data_retention_hours=8760  # 1 year
        )
        
        assert extreme_settings.vlm_max_tokens == 100000
        assert extreme_settings.batch_size == 1000
        assert extreme_settings.vlm_temperature == 1.0
        assert extreme_settings.customer_data_retention_hours == 8760
    
    def test_unicode_and_special_characters(self):
        """Test handling of Unicode and special characters in string fields"""
        unicode_settings = Settings(
            vlm_api_key="key-with-unicode-ñ-中文-🔑",
            vlm_model_name="model-with-special-chars-@#$%",
            log_level="INFO"
        )
        
        assert unicode_settings.vlm_api_key == "key-with-unicode-ñ-中文-🔑"
        assert unicode_settings.vlm_model_name == "model-with-special-chars-@#$%"
        assert unicode_settings.log_level == "INFO"