"""
Integration tests for VLM-38B-AWQ endpoint
Tests the complete integration with proper mocking and real-world scenarios
"""

import pytest
import asyncio
import json
from unittest.mock import AsyncMock, patch, MagicMock
from pathlib import Path
import base64
from PIL import Image
import io

# Add parent directory to path
import sys
sys.path.append(str(Path(__file__).parent.parent))

from app.services.vlm_service import VLMService
from app.models.schemas import VLMAnalysisResult
from app.core.config import settings


@pytest.fixture
def vlm_service():
    """Create VLM service instance"""
    return VLMService()


@pytest.fixture
def sample_image_base64():
    """Create a sample base64 encoded image"""
    img = Image.new('RGB', (100, 100), color='red')
    buffer = io.BytesIO()
    img.save(buffer, format='JPEG')
    return base64.b64encode(buffer.getvalue()).decode('utf-8')


@pytest.fixture
def mock_vlm_response():
    """Mock VLM API response"""
    return {
        "id": "chatcmpl-test",
        "object": "chat.completion",
        "created": 1234567890,
        "model": "VLM-38B-AWQ",
        "choices": [{
            "index": 0,
            "message": {
                "role": "assistant",
                "content": json.dumps({
                    "detection_type": "STRUCTURE_MISIDENTIFIED",
                    "false_positive_likelihood": 85,
                    "true_violation_likelihood": 15,
                    "reasoning": "The object in the image appears to be a crane or heavy machinery, not a person. This is likely a false positive triggered by the equipment's shape.",
                    "recommendation": "DISMISS_ALERT",
                    "confidence_score": 0.92
                })
            },
            "finish_reason": "stop"
        }],
        "usage": {
            "prompt_tokens": 100,
            "completion_tokens": 50,
            "total_tokens": 150
        }
    }


@pytest.mark.asyncio
async def test_vlm_service_initialization():
    """Test VLM service initialization with VLM-38B-AWQ settings"""
    service = VLMService()
    
    assert service.base_url == settings.vlm_api_base_url
    assert service.api_key == settings.vlm_api_key
    assert service.model_name == settings.vlm_model_name
    assert service.timeout == settings.vlm_timeout_seconds
    assert service.max_tokens == settings.vlm_max_tokens
    assert service.temperature == settings.vlm_temperature


@pytest.mark.asyncio
async def test_analyze_image_success(vlm_service, mock_vlm_response):
    """Test successful image analysis with VLM-38B-AWQ"""
    with patch('httpx.AsyncClient.post') as mock_post:
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = mock_vlm_response
        mock_response.elapsed.total_seconds.return_value = 0.1
        mock_post.return_value = mock_response
        
        # Create a test image
        test_image = Path("test_image.jpg")
        img = Image.new('RGB', (100, 100), color='blue')
        img.save(test_image)
        
        try:
            result = await vlm_service.analyze_image(
                str(test_image),
                "TEST-123"
            )
            
            # Verify result
            assert isinstance(result, VLMAnalysisResult)
            assert result.detection_type == "STRUCTURE_MISIDENTIFIED"
            assert result.false_positive_likelihood == 85
            assert result.true_violation_likelihood == 15
            assert result.recommendation == "DISMISS_ALERT"
            assert result.confidence_score == 0.92
            assert result.processing_time_ms > 0
            
            # Verify API call
            mock_post.assert_called_once()
            call_args = mock_post.call_args
            
            # Check URL construction
            expected_url = f"{settings.vlm_api_base_url}/chat/completions"
            assert call_args[0][0] == expected_url
            
            # Check headers
            headers = call_args[1]['headers']
            assert headers['Content-Type'] == 'application/json'
            assert headers['Authorization'] == f'Bearer {settings.vlm_api_key}'
            
            # Check payload
            payload = call_args[1]['json']
            assert payload['model'] == 'VLM-38B-AWQ'
            assert len(payload['messages']) == 1
            assert payload['messages'][0]['role'] == 'user'
            assert len(payload['messages'][0]['content']) == 2  # text + image
            
        finally:
            # Cleanup
            if test_image.exists():
                test_image.unlink()


@pytest.mark.asyncio
async def test_analyze_image_with_large_image(vlm_service, mock_vlm_response):
    """Test image analysis with large image that requires compression"""
    with patch('httpx.AsyncClient.post') as mock_post:
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = mock_vlm_response
        mock_response.elapsed.total_seconds.return_value = 0.1
        mock_post.return_value = mock_response
        
        # Create a large test image
        test_image = Path("large_test_image.jpg")
        img = Image.new('RGB', (3000, 3000), color='green')
        img.save(test_image)
        
        try:
            result = await vlm_service.analyze_image(
                str(test_image),
                "LARGE-TEST"
            )
            
            # Verify compression occurred
            assert isinstance(result, VLMAnalysisResult)
            
            # Check that image was compressed in the payload
            call_args = mock_post.call_args
            payload = call_args[1]['json']
            image_content = payload['messages'][0]['content'][1]['image_url']['url']
            
            # Verify that a base64 image was sent
            assert image_content.startswith('data:image/jpeg;base64,')
            # The actual compression happens in the service, so we just verify the image was sent
            
        finally:
            # Cleanup
            if test_image.exists():
                test_image.unlink()


@pytest.mark.asyncio
async def test_analyze_batch_success(vlm_service, mock_vlm_response):
    """Test batch image analysis"""
    with patch('httpx.AsyncClient.post') as mock_post:
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = mock_vlm_response
        mock_response.elapsed.total_seconds.return_value = 0.1
        mock_post.return_value = mock_response
        
        # Create test images
        test_images = []
        case_numbers = []
        
        for i in range(3):
            test_image = Path(f"batch_test_{i}.jpg")
            img = Image.new('RGB', (100, 100), color=['red', 'green', 'blue'][i])
            img.save(test_image)
            test_images.append(str(test_image))
            case_numbers.append(f"BATCH-{i:03d}")
        
        try:
            results = await vlm_service.analyze_batch(
                test_images,
                case_numbers
            )
            
            # Verify results
            assert len(results) == 3
            for result in results:
                assert isinstance(result, VLMAnalysisResult)
                assert result.detection_type == "STRUCTURE_MISIDENTIFIED"
            
            # Verify concurrent calls
            assert mock_post.call_count == 3
            
        finally:
            # Cleanup
            for test_image in test_images:
                path = Path(test_image)
                if path.exists():
                    path.unlink()


@pytest.mark.asyncio
async def test_error_handling_api_failure(vlm_service):
    """Test error handling when API returns error"""
    with patch('httpx.AsyncClient.post') as mock_post:
        mock_response = MagicMock()
        mock_response.status_code = 500
        mock_response.text = "Internal Server Error"
        mock_post.return_value = mock_response
        
        # Create a test image
        test_image = Path("error_test.jpg")
        img = Image.new('RGB', (100, 100), color='red')
        img.save(test_image)
        
        try:
            with pytest.raises(Exception) as exc_info:
                await vlm_service.analyze_image(
                    str(test_image),
                    "ERROR-TEST"
                )
            
            assert "VLM API request failed: 500" in str(exc_info.value)
            
        finally:
            # Cleanup
            if test_image.exists():
                test_image.unlink()


@pytest.mark.asyncio
async def test_error_handling_network_failure(vlm_service):
    """Test error handling for network failures"""
    with patch('httpx.AsyncClient.post') as mock_post:
        mock_post.side_effect = Exception("Network connection failed")
        
        # Create a test image
        test_image = Path("network_error_test.jpg")
        img = Image.new('RGB', (100, 100), color='red')
        img.save(test_image)
        
        try:
            with pytest.raises(Exception) as exc_info:
                await vlm_service.analyze_image(
                    str(test_image),
                    "NETWORK-ERROR"
                )
            
            assert "Network connection failed" in str(exc_info.value)
            
        finally:
            # Cleanup
            if test_image.exists():
                test_image.unlink()


@pytest.mark.asyncio
async def test_custom_prompt_generation(vlm_service):
    """Test custom prompt generation with customer patterns"""
    customer_patterns = {
        'unique_crane_types': ['gantry crane', 'mobile crane'],
        'vessel_configurations': ['container ship', 'tanker'],
        'port_layout': {'description': 'Terminal A with 4 berths'},
        'lighting_patterns': {'dominant_type': 'LED floodlights'},
        'most_common_false_positive': 'crane structures',
        'camera_angles': {'typical_angle': 'elevated 45 degrees'}
    }
    
    custom_prompt = vlm_service.generate_custom_prompt(customer_patterns)
    
    # Verify custom additions are included
    assert 'gantry crane' in custom_prompt
    assert 'container ship' in custom_prompt
    assert 'Terminal A with 4 berths' in custom_prompt
    assert 'LED floodlights' in custom_prompt
    assert 'crane structures' in custom_prompt
    assert 'elevated 45 degrees' in custom_prompt


def test_parse_text_response_fallback(vlm_service):
    """Test fallback text parsing when JSON parsing fails"""
    text_response = {
        "choices": [{
            "message": {
                "content": "This appears to be a crane structure, not a person. The shape and size indicate heavy machinery. Confidence is high that this is a false positive."
            }
        }]
    }
    
    result = vlm_service._parse_vlm_response(text_response, 100)
    
    assert result.detection_type == "STRUCTURE_MISIDENTIFIED"
    assert result.false_positive_likelihood == 60  # No confidence keyword match, defaults to 60
    assert result.recommendation == "REQUIRES_REVIEW"  # 60 < 70, so requires review
    assert result.confidence_score == 0.6
    assert "crane structure" in result.reasoning


@pytest.mark.asyncio
async def test_rate_limiting(vlm_service):
    """Test rate limiting with concurrent requests"""
    # Set max concurrent requests to 2 for testing
    vlm_service.semaphore = asyncio.Semaphore(2)
    
    with patch('httpx.AsyncClient.post') as mock_post:
        # Create mock responses
        mock_responses = []
        for i in range(5):
            mock_response = MagicMock()
            mock_response.status_code = 200
            mock_response.json.return_value = {
                "choices": [{
                    "message": {
                        "content": json.dumps({
                            "detection_type": "HUMAN_DETECTED",
                            "false_positive_likelihood": 10,
                            "true_violation_likelihood": 90,
                            "reasoning": "Test",
                            "recommendation": "REQUIRES_REVIEW",
                            "confidence_score": 0.9
                        })
                    }
                }]
            }
            mock_response.elapsed.total_seconds.return_value = 0.1
            mock_responses.append(mock_response)
        
        mock_post.side_effect = mock_responses
        
        # Create test images
        test_images = []
        case_numbers = []
        
        for i in range(5):
            test_image = Path(f"rate_limit_test_{i}.jpg")
            img = Image.new('RGB', (50, 50), color='red')
            img.save(test_image)
            test_images.append(str(test_image))
            case_numbers.append(f"RATE-{i:03d}")
        
        try:
            # Process batch
            results = await vlm_service.analyze_batch(
                test_images,
                case_numbers
            )
            
            # Verify all succeeded
            assert len(results) == 5
            
            # Verify semaphore was created properly
            assert vlm_service.semaphore._value <= 2
            
            # Verify all calls were made
            assert mock_post.call_count == 5
            
        finally:
            # Cleanup
            for test_image in test_images:
                path = Path(test_image)
                if path.exists():
                    path.unlink()


if __name__ == "__main__":
    pytest.main([__file__, "-v"])