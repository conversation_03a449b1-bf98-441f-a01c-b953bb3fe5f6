"""
Pytest configuration and shared fixtures for AI-FARM backend tests
"""

import pytest
import asyncio
import tempfile
import os
from pathlib import Path
from unittest.mock import Mock, AsyncMock
from PIL import Image
import io
import base64

from app.core.config import Settings
from app.services.vlm_service import VLMService


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def test_settings():
    """Test settings with safe defaults"""
    return Settings(
        vlm_api_base_url="http://test-api.example.com",
        vlm_api_key="test-key-123",
        vlm_model_name="test-model",
        vlm_timeout_seconds=10,
        database_url="sqlite:///:memory:",
        debug=True,
        temp_upload_path=Path("/tmp/test_ai_farm"),
        demo_data_path=Path("/tmp/test_data"),
        demo_images_path=Path("/tmp/test_images"),
        customer_data_retention_hours=1,
        max_concurrent_requests=2,
        image_max_size_mb=5
    )


@pytest.fixture
def mock_settings(test_settings, monkeypatch):
    """Mock the global settings with test settings"""
    monkeypatch.setattr("app.core.config.settings", test_settings)
    monkeypatch.setattr("app.services.vlm_service.settings", test_settings)
    return test_settings


@pytest.fixture
def sample_image_path():
    """Create a sample test image file"""
    # Create a simple test image
    img = Image.new('RGB', (100, 100), color='red')
    
    # Save to temporary file
    temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.jpg')
    img.save(temp_file.name, 'JPEG')
    
    yield temp_file.name
    
    # Cleanup
    try:
        os.unlink(temp_file.name)
    except FileNotFoundError:
        pass


@pytest.fixture
def large_sample_image_path():
    """Create a large test image file (>5MB)"""
    # Create a large test image
    img = Image.new('RGB', (3000, 3000), color='blue')
    
    # Save to temporary file
    temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.jpg')
    img.save(temp_file.name, 'JPEG', quality=100)
    
    yield temp_file.name
    
    # Cleanup
    try:
        os.unlink(temp_file.name)
    except FileNotFoundError:
        pass


@pytest.fixture
def sample_image_b64():
    """Generate a base64 encoded sample image"""
    img = Image.new('RGB', (50, 50), color='green')
    buffer = io.BytesIO()
    img.save(buffer, format='JPEG')
    image_data = buffer.getvalue()
    return base64.b64encode(image_data).decode('utf-8')


@pytest.fixture
def mock_vlm_service(mock_settings):
    """Mock VLM service for testing"""
    service = VLMService()
    return service


@pytest.fixture
def mock_httpx_client():
    """Mock httpx AsyncClient for API testing"""
    client_mock = AsyncMock()
    response_mock = Mock()
    response_mock.status_code = 200
    response_mock.json.return_value = {
        "choices": [{
            "message": {
                "content": '{"detection_type": "HUMAN_DETECTED", "false_positive_likelihood": 20, "true_violation_likelihood": 80, "reasoning": "Test reasoning", "recommendation": "REQUIRES_REVIEW", "confidence_score": 0.8}'
            }
        }]
    }
    client_mock.post.return_value = response_mock
    client_mock.get.return_value = response_mock
    return client_mock


@pytest.fixture
def sample_case_data():
    """Sample case data for testing"""
    return {
        "pk_event": 12345,
        "case_number": "V1250630001",
        "image_url": "/path/to/image.jpg",
        "validation_status": "invalid"
    }


@pytest.fixture
def sample_vlm_response():
    """Sample VLM API response"""
    return {
        "choices": [{
            "message": {
                "content": '''{
                    "detection_type": "STRUCTURE_MISIDENTIFIED",
                    "false_positive_likelihood": 85,
                    "true_violation_likelihood": 15,
                    "reasoning": "The image shows a crane structure that has been misidentified as a human figure. The lighting conditions and angle make the structure appear human-like, but detailed analysis reveals it's mechanical equipment.",
                    "recommendation": "DISMISS_ALERT",
                    "confidence_score": 0.9
                }'''
            }
        }]
    }


@pytest.fixture
def sample_vlm_text_response():
    """Sample VLM API text response (non-JSON)"""
    return {
        "choices": [{
            "message": {
                "content": "This appears to be a structure misidentification. The image shows what looks like a crane or similar equipment that has been incorrectly detected as a human. I am quite confident this is a false positive alert."
            }
        }]
    }


@pytest.fixture
def temp_test_dir():
    """Create temporary directory for test files"""
    temp_dir = tempfile.mkdtemp(prefix="ai_farm_test_")
    yield Path(temp_dir)
    
    # Cleanup
    import shutil
    try:
        shutil.rmtree(temp_dir)
    except FileNotFoundError:
        pass


@pytest.fixture
def mock_file_operations(monkeypatch):
    """Mock file operations for testing"""
    def mock_mkdir(self, parents=True, exist_ok=True):
        pass
    
    def mock_exists(self):
        return True
    
    def mock_stat(self):
        stat_mock = Mock()
        stat_mock.st_size = 1024 * 1024  # 1MB
        return stat_mock
    
    monkeypatch.setattr(Path, "mkdir", mock_mkdir)
    monkeypatch.setattr(Path, "exists", mock_exists)
    monkeypatch.setattr(Path, "stat", mock_stat)


@pytest.fixture
def sample_processing_results():
    """Sample processing results for testing"""
    return [
        {
            "case_number": "V1250630001",
            "pk_event": 12345,
            "images_processed": [{"path": "/test/image1.jpg", "size": "1MB"}],
            "vlm_results": [{
                "detection_type": "STRUCTURE_MISIDENTIFIED",
                "false_positive_likelihood": 85,
                "true_violation_likelihood": 15,
                "reasoning": "Test reasoning",
                "recommendation": "DISMISS_ALERT",
                "confidence_score": 0.9,
                "processing_time_ms": 1500
            }],
            "final_recommendation": "DISMISS_ALERT",
            "processing_status": "completed",
            "processing_time_total_ms": 1500
        }
    ]


@pytest.fixture
def invalid_image_path():
    """Path to non-existent image file"""
    return "/nonexistent/path/to/image.jpg"


@pytest.fixture
def corrupted_image_path():
    """Create a corrupted image file"""
    temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.jpg')
    temp_file.write(b"corrupted image data")
    temp_file.close()
    
    yield temp_file.name
    
    # Cleanup
    try:
        os.unlink(temp_file.name)
    except FileNotFoundError:
        pass