"""
Integration tests for configuration system
Tests real environment loading and directory creation
"""

import pytest
import os
import tempfile
from pathlib import Path
from unittest.mock import patch

from app.core.config import Settings


@pytest.mark.integration
class TestConfigurationIntegration:
    """Integration tests for configuration system"""
    
    def test_full_configuration_workflow(self):
        """Test complete configuration workflow with real environment"""
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            
            # Create test environment
            test_env = {
                'VLM_API_BASE_URL': 'https://integration-test.example.com',
                'VLM_API_KEY': 'integration-test-key-12345',
                'VLM_MODEL_NAME': 'integration-test-model',
                'BATCH_SIZE': '20',
                'DEBUG': 'true',
                'DEMO_DATA_PATH': str(temp_path / 'demo'),
                'DEMO_IMAGES_PATH': str(temp_path / 'images'),
                'TEMP_UPLOAD_PATH': str(temp_path / 'uploads'),
                'LOGS_PATH': str(temp_path / 'logs')
            }
            
            with patch.dict(os.environ, test_env, clear=False):
                # Initialize settings from environment
                settings = Settings()
                
                # Verify environment loading
                assert settings.vlm_api_base_url == 'https://integration-test.example.com'
                assert settings.vlm_api_key == 'integration-test-key-12345'
                assert settings.vlm_model_name == 'integration-test-model'
                assert settings.batch_size == 20
                assert settings.debug is True
                
                # Test path configuration
                assert settings.demo_data_path == temp_path / 'demo'
                assert settings.demo_images_path == temp_path / 'images'
                assert settings.temp_upload_path == temp_path / 'uploads'
                assert settings.logs_path == temp_path / 'logs'
                
                # Test directory creation
                settings.create_directories()
                
                # Verify directories exist
                assert settings.demo_data_path.exists()
                assert settings.demo_images_path.exists()
                assert settings.temp_upload_path.exists()
                assert settings.logs_path.exists()
                
                # Test API headers
                headers = settings.vlm_api_headers
                assert headers['Content-Type'] == 'application/json'
                assert headers['Authorization'] == 'Bearer integration-test-key-12345'
                
                # Test threshold methods
                assert settings.get_threshold_for_category('structure_misid') == 70
                assert settings.get_threshold_for_category('unknown_category') == 70
    
    def test_environment_override_precedence(self):
        """Test environment variables override defaults correctly"""
        # Test with minimal environment
        minimal_env = {
            'VLM_API_BASE_URL': 'https://minimal.example.com',
            'BATCH_SIZE': '15'
        }
        
        with patch.dict(os.environ, minimal_env, clear=False):
            settings = Settings()
            
            # Environment values should override defaults
            assert settings.vlm_api_base_url == 'https://minimal.example.com'
            assert settings.batch_size == 15
            
            # Defaults should remain for non-overridden values
            assert settings.vlm_model_name == 'internvl3-38b'  # default
            assert settings.debug is False  # default
    
    def test_case_insensitive_environment_loading(self):
        """Test case insensitive environment variable loading"""
        mixed_case_env = {
            'vlm_api_base_url': 'https://lowercase.example.com',
            'VLM_API_KEY': 'UPPERCASE_KEY',
            'Vlm_Model_Name': 'MixedCase_Model',
            'batch_SIZE': '25'
        }
        
        with patch.dict(os.environ, mixed_case_env, clear=False):
            settings = Settings()
            
            assert settings.vlm_api_base_url == 'https://lowercase.example.com'
            assert settings.vlm_api_key == 'UPPERCASE_KEY'
            assert settings.vlm_model_name == 'MixedCase_Model'
            assert settings.batch_size == 25
    
    def test_directory_creation_with_nested_paths(self):
        """Test directory creation with deeply nested paths"""
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            
            settings = Settings(
                demo_data_path=temp_path / 'level1' / 'level2' / 'demo',
                demo_images_path=temp_path / 'deep' / 'nested' / 'path' / 'images',
                temp_upload_path=temp_path / 'very' / 'deep' / 'nested' / 'uploads',
                processed_images_path=temp_path / 'another' / 'deep' / 'path' / 'processed',
                logs_path=temp_path / 'logging' / 'directory' / 'logs',
                export_path=temp_path / 'export' / 'data' / 'exports'
            )
            
            # Create all directories
            settings.create_directories()
            
            # Verify all nested directories exist
            assert settings.demo_data_path.exists()
            assert settings.demo_images_path.exists()
            assert settings.temp_upload_path.exists()
            assert settings.processed_images_path.exists()
            assert settings.logs_path.exists()
            assert settings.export_path.exists()
            
            # Verify they are directories
            assert settings.demo_data_path.is_dir()
            assert settings.demo_images_path.is_dir()
            assert settings.temp_upload_path.is_dir()
    
    def test_configuration_with_env_file(self):
        """Test configuration loading from .env file"""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.env', delete=False) as env_file:
            env_content = """
VLM_API_BASE_URL=https://envfile.example.com
VLM_API_KEY=envfile-key-789
VLM_MODEL_NAME=envfile-model
BATCH_SIZE=30
DEBUG=true
ENABLE_AUTO_LEARNING=false
THRESHOLD_STRUCTURE_MISID=85
THRESHOLD_PROPER_PPE=72
"""
            env_file.write(env_content)
            env_file_path = env_file.name
        
        try:
            # Load settings from .env file
            settings = Settings(_env_file=env_file_path)
            
            assert settings.vlm_api_base_url == 'https://envfile.example.com'
            assert settings.vlm_api_key == 'envfile-key-789'
            assert settings.vlm_model_name == 'envfile-model'
            assert settings.batch_size == 30
            assert settings.debug is True
            assert settings.enable_auto_learning is False
            assert settings.threshold_structure_misid == 85
            assert settings.threshold_proper_ppe == 72
        finally:
            os.unlink(env_file_path)
    
    def test_configuration_validation_errors(self):
        """Test configuration validation with invalid values"""
        invalid_env = {
            'BATCH_SIZE': 'not_a_number',
            'DEBUG': 'not_a_boolean',
            'VLM_TIMEOUT_SECONDS': 'invalid_timeout',
            'PORT': 'invalid_port'
        }
        
        with patch.dict(os.environ, invalid_env, clear=False):
            with pytest.raises(Exception):  # Should raise validation error
                Settings()
    
    def test_threshold_configuration_integration(self):
        """Test threshold configuration and retrieval integration"""
        threshold_env = {
            'THRESHOLD_STRUCTURE_MISID': '82',
            'THRESHOLD_PROPER_PPE': '68',
            'THRESHOLD_NO_VIOLATION': '78',
            'THRESHOLD_DEFAULT': '72'
        }
        
        with patch.dict(os.environ, threshold_env, clear=False):
            settings = Settings()
            
            # Test direct access
            assert settings.threshold_structure_misid == 82
            assert settings.threshold_proper_ppe == 68
            assert settings.threshold_no_violation == 78
            assert settings.threshold_default == 72
            
            # Test method access
            assert settings.get_threshold_for_category('structure_misid') == 82
            assert settings.get_threshold_for_category('proper_ppe') == 68
            assert settings.get_threshold_for_category('no_violation') == 78
            assert settings.get_threshold_for_category('unknown') == 72
    
    def test_api_headers_integration(self):
        """Test API headers generation with different configurations"""
        # Test without API key
        settings_no_key = Settings(vlm_api_key='')
        headers_no_key = settings_no_key.vlm_api_headers
        
        assert headers_no_key == {'Content-Type': 'application/json'}
        assert 'Authorization' not in headers_no_key
        
        # Test with API key
        settings_with_key = Settings(vlm_api_key='test-integration-key')
        headers_with_key = settings_with_key.vlm_api_headers
        
        assert headers_with_key['Content-Type'] == 'application/json'
        assert headers_with_key['Authorization'] == 'Bearer test-integration-key'
        
        # Test with API key from environment
        env_key = {'VLM_API_KEY': 'env-integration-key'}
        with patch.dict(os.environ, env_key, clear=False):
            settings_env_key = Settings()
            headers_env_key = settings_env_key.vlm_api_headers
            
            assert headers_env_key['Authorization'] == 'Bearer env-integration-key'
    
    def test_real_world_configuration_scenario(self):
        """Test realistic production-like configuration scenario"""
        production_env = {
            'VLM_API_BASE_URL': 'https://api.production.com',
            'VLM_API_KEY': 'prod-key-secure-12345',
            'VLM_MODEL_NAME': 'production-model-v2',
            'VLM_TIMEOUT_SECONDS': '45',
            'BATCH_SIZE': '50',
            'MAX_CONCURRENT_REQUESTS': '5',
            'IMAGE_MAX_SIZE_MB': '20',
            'DEBUG': 'false',
            'LOG_LEVEL': 'WARNING',
            'ENABLE_AUTO_LEARNING': 'true',
            'CUSTOMER_DATA_RETENTION_HOURS': '48',
            'ENABLE_METRICS': 'true',
            'METRICS_PORT': '9090'
        }
        
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            production_env.update({
                'DEMO_DATA_PATH': str(temp_path / 'production_demo'),
                'LOGS_PATH': str(temp_path / 'production_logs'),
                'EXPORT_PATH': str(temp_path / 'production_exports')
            })
            
            with patch.dict(os.environ, production_env, clear=False):
                settings = Settings()
                
                # Verify production configuration
                assert settings.vlm_api_base_url == 'https://api.production.com'
                assert settings.vlm_api_key == 'prod-key-secure-12345'
                assert settings.vlm_model_name == 'production-model-v2'
                assert settings.vlm_timeout_seconds == 45
                assert settings.batch_size == 50
                assert settings.max_concurrent_requests == 5
                assert settings.image_max_size_mb == 20
                assert settings.debug is False
                assert settings.log_level == 'WARNING'
                assert settings.enable_auto_learning is True
                assert settings.customer_data_retention_hours == 48
                assert settings.enable_metrics is True
                assert settings.metrics_port == 9090
                
                # Test directory creation in production scenario
                settings.create_directories()
                
                assert (temp_path / 'production_demo').exists()
                assert (temp_path / 'production_logs').exists()
                assert (temp_path / 'production_exports').exists()
                
                # Test API headers for production
                headers = settings.vlm_api_headers
                assert headers['Authorization'] == 'Bearer prod-key-secure-12345'
                assert headers['Content-Type'] == 'application/json'