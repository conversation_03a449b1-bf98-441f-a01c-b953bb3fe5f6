"""
Comprehensive tests for Pydantic Schemas
Tests data validation, serialization, and schema constraints
"""

import pytest
from datetime import datetime
from typing import Dict, Any
from pydantic import ValidationError

from app.models.schemas import (
    ValidationStatus, ImageType, ProcessingStatus,
    VLMAnalysisResult, CaseData, ProcessingRequest, ProcessingResult,
    BatchProcessingResponse, DemoMetrics, AutoLearningInsights,
    CustomerEnvironmentAnalysis, HealthCheckResponse, ErrorResponse
)


class TestEnums:
    """Test enum definitions and values"""
    
    def test_validation_status_enum(self):
        """Test ValidationStatus enum values"""
        assert ValidationStatus.VALID == "valid"
        assert ValidationStatus.INVALID == "invalid"
        assert ValidationStatus.INVALID_FALSE_POSITIVE == "invalid_false_positive"
        assert ValidationStatus.UNKNOWN == "unknown"
        
        # Test enum membership
        assert "valid" in ValidationStatus
        assert "invalid" in ValidationStatus
        assert "invalid_false_positive" in ValidationStatus
        assert "unknown" in ValidationStatus
        assert "nonexistent" not in ValidationStatus
    
    def test_image_type_enum(self):
        """Test ImageType enum values"""
        assert ImageType.SOURCE == "source"
        assert ImageType.CROPPED == "cropped"
        
        assert "source" in ImageType
        assert "cropped" in ImageType
    
    def test_processing_status_enum(self):
        """Test ProcessingStatus enum values"""
        assert ProcessingStatus.PENDING == "pending"
        assert ProcessingStatus.PROCESSING == "processing"
        assert ProcessingStatus.COMPLETED == "completed"
        assert ProcessingStatus.FAILED == "failed"


class TestVLMAnalysisResult:
    """Test VLMAnalysisResult schema validation"""
    
    def test_valid_vlm_analysis_result(self):
        """Test valid VLMAnalysisResult creation"""
        data = {
            "detection_type": "HUMAN_DETECTED",
            "false_positive_likelihood": 25,
            "true_violation_likelihood": 75,
            "reasoning": "Clear human figure detected in safety zone",
            "recommendation": "REQUIRES_REVIEW",
            "confidence_score": 0.85,
            "processing_time_ms": 1500
        }
        
        result = VLMAnalysisResult(**data)
        
        assert result.detection_type == "HUMAN_DETECTED"
        assert result.false_positive_likelihood == 25
        assert result.true_violation_likelihood == 75
        assert result.reasoning == "Clear human figure detected in safety zone"
        assert result.recommendation == "REQUIRES_REVIEW"
        assert result.confidence_score == 0.85
        assert result.processing_time_ms == 1500
    
    def test_vlm_analysis_result_required_fields(self):
        """Test VLMAnalysisResult requires all mandatory fields"""
        with pytest.raises(ValidationError) as exc_info:
            VLMAnalysisResult()
        
        error_dict = exc_info.value.errors()
        required_fields = {
            "detection_type", "false_positive_likelihood", 
            "true_violation_likelihood", "reasoning", 
            "recommendation", "confidence_score"
        }
        
        error_fields = {error["loc"][0] for error in error_dict}
        assert required_fields.issubset(error_fields)
    
    def test_likelihood_range_validation(self):
        """Test likelihood values must be 0-100"""
        base_data = {
            "detection_type": "HUMAN_DETECTED",
            "reasoning": "Test reasoning",
            "recommendation": "REQUIRES_REVIEW",
            "confidence_score": 0.8
        }
        
        # Valid ranges
        valid_data = {**base_data, "false_positive_likelihood": 0, "true_violation_likelihood": 100}
        result = VLMAnalysisResult(**valid_data)
        assert result.false_positive_likelihood == 0
        assert result.true_violation_likelihood == 100
        
        valid_data = {**base_data, "false_positive_likelihood": 50, "true_violation_likelihood": 50}
        result = VLMAnalysisResult(**valid_data)
        assert result.false_positive_likelihood == 50
        
        # Invalid ranges
        with pytest.raises(ValidationError):
            VLMAnalysisResult(**{**base_data, "false_positive_likelihood": -1, "true_violation_likelihood": 50})
        
        with pytest.raises(ValidationError):
            VLMAnalysisResult(**{**base_data, "false_positive_likelihood": 101, "true_violation_likelihood": 50})
        
        with pytest.raises(ValidationError):
            VLMAnalysisResult(**{**base_data, "false_positive_likelihood": 50, "true_violation_likelihood": -5})
    
    def test_confidence_score_range(self):
        """Test confidence score must be 0.0-1.0"""
        base_data = {
            "detection_type": "HUMAN_DETECTED",
            "false_positive_likelihood": 25,
            "true_violation_likelihood": 75,
            "reasoning": "Test reasoning",
            "recommendation": "REQUIRES_REVIEW"
        }
        
        # Valid ranges
        for score in [0.0, 0.5, 1.0]:
            result = VLMAnalysisResult(**{**base_data, "confidence_score": score})
            assert result.confidence_score == score
        
        # Invalid ranges
        for score in [-0.1, 1.1, 2.0]:
            with pytest.raises(ValidationError):
                VLMAnalysisResult(**{**base_data, "confidence_score": score})
    
    def test_recommendation_literal_validation(self):
        """Test recommendation must be specific literal values"""
        base_data = {
            "detection_type": "HUMAN_DETECTED",
            "false_positive_likelihood": 25,
            "true_violation_likelihood": 75,
            "reasoning": "Test reasoning",
            "confidence_score": 0.8
        }
        
        # Valid recommendations
        for rec in ["DISMISS_ALERT", "REQUIRES_REVIEW"]:
            result = VLMAnalysisResult(**{**base_data, "recommendation": rec})
            assert result.recommendation == rec
        
        # Invalid recommendations
        for rec in ["INVALID_REC", "dismiss_alert", "requires_review", ""]:
            with pytest.raises(ValidationError):
                VLMAnalysisResult(**{**base_data, "recommendation": rec})
    
    def test_likelihood_sum_validation(self):
        """Test custom validator for likelihood sum"""
        base_data = {
            "detection_type": "HUMAN_DETECTED",
            "reasoning": "Test reasoning",
            "recommendation": "REQUIRES_REVIEW",
            "confidence_score": 0.8
        }
        
        # Valid combinations (sum <= 100)
        valid_combinations = [
            (0, 0), (50, 50), (30, 70), (100, 0), (0, 100)
        ]
        
        for fp_likelihood, tv_likelihood in valid_combinations:
            result = VLMAnalysisResult(**{
                **base_data,
                "false_positive_likelihood": fp_likelihood,
                "true_violation_likelihood": tv_likelihood
            })
            assert result.false_positive_likelihood == fp_likelihood
            assert result.true_violation_likelihood == tv_likelihood
        
        # Invalid combinations (sum > 100)
        invalid_combinations = [
            (60, 50), (75, 75), (100, 1)
        ]
        
        for fp_likelihood, tv_likelihood in invalid_combinations:
            with pytest.raises(ValidationError) as exc_info:
                VLMAnalysisResult(**{
                    **base_data,
                    "false_positive_likelihood": fp_likelihood,
                    "true_violation_likelihood": tv_likelihood
                })
            
            assert "Combined likelihoods cannot exceed 100%" in str(exc_info.value)


class TestCaseData:
    """Test CaseData schema validation"""
    
    def test_valid_case_data(self):
        """Test valid CaseData creation"""
        data = {
            "pk_event": 12345,
            "case_number": "V1250630001",
            "image_url": "/path/to/image.jpg",
            "validation_status": "invalid"
        }
        
        case = CaseData(**data)
        
        assert case.pk_event == 12345
        assert case.case_number == "V1250630001"
        assert case.image_url == "/path/to/image.jpg"
        assert case.validation_status == ValidationStatus.INVALID
    
    def test_case_number_validation(self):
        """Test case number format validation"""
        base_data = {
            "pk_event": 12345,
            "image_url": "/path/to/image.jpg",
            "validation_status": "valid"
        }
        
        # Valid case numbers
        valid_case_numbers = ["V1250630001", "V1250630999", "V125123456789"]
        for case_num in valid_case_numbers:
            case = CaseData(**{**base_data, "case_number": case_num})
            assert case.case_number == case_num
        
        # Invalid case numbers
        invalid_case_numbers = ["V1240630001", "X1250630001", "1250630001", "v1250630001", ""]
        for case_num in invalid_case_numbers:
            with pytest.raises(ValidationError) as exc_info:
                CaseData(**{**base_data, "case_number": case_num})
            
            assert "Case number must start with V125" in str(exc_info.value)
    
    def test_validation_status_enum_conversion(self):
        """Test validation status string conversion to enum"""
        base_data = {
            "pk_event": 12345,
            "case_number": "V1250630001",
            "image_url": "/path/to/image.jpg"
        }
        
        status_mappings = {
            "valid": ValidationStatus.VALID,
            "invalid": ValidationStatus.INVALID,
            "invalid_false_positive": ValidationStatus.INVALID_FALSE_POSITIVE,
            "unknown": ValidationStatus.UNKNOWN
        }
        
        for status_str, expected_enum in status_mappings.items():
            case = CaseData(**{**base_data, "validation_status": status_str})
            assert case.validation_status == expected_enum
    
    def test_case_data_required_fields(self):
        """Test all fields are required"""
        with pytest.raises(ValidationError) as exc_info:
            CaseData()
        
        error_dict = exc_info.value.errors()
        required_fields = {"pk_event", "case_number", "image_url", "validation_status"}
        error_fields = {error["loc"][0] for error in error_dict}
        assert required_fields.issubset(error_fields)


class TestProcessingRequest:
    """Test ProcessingRequest schema validation"""
    
    def test_valid_processing_request(self):
        """Test valid ProcessingRequest creation"""
        data = {
            "case_numbers": ["V1250630001", "V1250630002"],
            "use_auto_learning": True,
            "custom_thresholds": {"structure_misid": 80, "proper_ppe": 70},
            "priority": "high"
        }
        
        request = ProcessingRequest(**data)
        
        assert request.case_numbers == ["V1250630001", "V1250630002"]
        assert request.use_auto_learning is True
        assert request.custom_thresholds == {"structure_misid": 80, "proper_ppe": 70}
        assert request.priority == "high"
    
    def test_processing_request_defaults(self):
        """Test ProcessingRequest default values"""
        request = ProcessingRequest(case_numbers=["V1250630001"])
        
        assert request.case_numbers == ["V1250630001"]
        assert request.use_auto_learning is True
        assert request.custom_thresholds is None
        assert request.priority == "normal"
    
    def test_priority_literal_validation(self):
        """Test priority must be specific literal values"""
        base_data = {"case_numbers": ["V1250630001"]}
        
        # Valid priorities
        for priority in ["low", "normal", "high"]:
            request = ProcessingRequest(**{**base_data, "priority": priority})
            assert request.priority == priority
        
        # Invalid priorities
        for priority in ["urgent", "medium", "LOW", "NORMAL", ""]:
            with pytest.raises(ValidationError):
                ProcessingRequest(**{**base_data, "priority": priority})
    
    def test_empty_case_numbers_list(self):
        """Test handling of empty case numbers list"""
        # Empty list should be valid (though perhaps not useful)
        request = ProcessingRequest(case_numbers=[])
        assert request.case_numbers == []


class TestProcessingResult:
    """Test ProcessingResult schema validation"""
    
    def test_valid_processing_result(self):
        """Test valid ProcessingResult creation"""
        vlm_result = {
            "detection_type": "STRUCTURE_MISIDENTIFIED",
            "false_positive_likelihood": 85,
            "true_violation_likelihood": 15,
            "reasoning": "Test reasoning",
            "recommendation": "DISMISS_ALERT",
            "confidence_score": 0.9,
            "processing_time_ms": 1200
        }
        
        data = {
            "case_number": "V1250630001",
            "pk_event": 12345,
            "images_processed": [{"path": "/test/image.jpg", "size": "2MB"}],
            "vlm_results": [vlm_result],
            "final_recommendation": "DISMISS_ALERT",
            "processing_status": "completed",
            "processing_time_total_ms": 1500,
            "error_message": None
        }
        
        result = ProcessingResult(**data)
        
        assert result.case_number == "V1250630001"
        assert result.pk_event == 12345
        assert len(result.images_processed) == 1
        assert len(result.vlm_results) == 1
        assert isinstance(result.vlm_results[0], VLMAnalysisResult)
        assert result.final_recommendation == "DISMISS_ALERT"
        assert result.processing_status == ProcessingStatus.COMPLETED
        assert result.processing_time_total_ms == 1500
    
    def test_processing_result_with_error(self):
        """Test ProcessingResult with error message"""
        data = {
            "case_number": "V1250630001",
            "pk_event": 12345,
            "images_processed": [],
            "vlm_results": [],
            "final_recommendation": "ERROR",
            "processing_status": "failed",
            "processing_time_total_ms": 0,
            "error_message": "Image processing failed"
        }
        
        result = ProcessingResult(**data)
        
        assert result.processing_status == ProcessingStatus.FAILED
        assert result.error_message == "Image processing failed"


class TestBatchProcessingResponse:
    """Test BatchProcessingResponse schema validation"""
    
    def test_valid_batch_processing_response(self):
        """Test valid BatchProcessingResponse creation"""
        start_time = datetime.now()
        
        data = {
            "batch_id": "batch-123456",
            "total_cases": 5,
            "status": "completed",
            "results": [],
            "summary": {"processed": 5, "dismissed": 3, "review_required": 2},
            "started_at": start_time,
            "completed_at": start_time
        }
        
        response = BatchProcessingResponse(**data)
        
        assert response.batch_id == "batch-123456"
        assert response.total_cases == 5
        assert response.status == ProcessingStatus.COMPLETED
        assert response.summary == {"processed": 5, "dismissed": 3, "review_required": 2}
        assert response.started_at == start_time
        assert response.completed_at == start_time
    
    def test_batch_response_default_results(self):
        """Test BatchProcessingResponse with default empty results"""
        data = {
            "batch_id": "batch-123456",
            "total_cases": 0,
            "status": "pending",
            "summary": {},
            "started_at": datetime.now()
        }
        
        response = BatchProcessingResponse(**data)
        
        assert response.results == []
        assert response.completed_at is None


class TestDemoMetrics:
    """Test DemoMetrics schema validation"""
    
    def test_valid_demo_metrics(self):
        """Test valid DemoMetrics creation"""
        data = {
            "total_alerts_processed": 1000,
            "false_positives_filtered": 700,
            "alerts_requiring_review": 300,
            "filter_rate_percentage": 70.0,
            "time_saved_hours": 175.5,
            "cost_savings_annual": 315000.00,
            "processing_time_avg_ms": 1250.5,
            "confidence_score_avg": 0.85
        }
        
        metrics = DemoMetrics(**data)
        
        assert metrics.total_alerts_processed == 1000
        assert metrics.false_positives_filtered == 700
        assert metrics.alerts_requiring_review == 300
        assert metrics.filter_rate_percentage == 70.0
        assert metrics.time_saved_hours == 175.5
        assert metrics.cost_savings_annual == 315000.00
        assert metrics.processing_time_avg_ms == 1250.5
        assert metrics.confidence_score_avg == 0.85
        
        # Verify calculated values make sense
        assert metrics.false_positives_filtered + metrics.alerts_requiring_review == metrics.total_alerts_processed


class TestAutoLearningInsights:
    """Test AutoLearningInsights schema validation"""
    
    def test_valid_auto_learning_insights(self):
        """Test valid AutoLearningInsights creation"""
        data = {
            "detected_patterns": {
                "common_structures": ["crane", "scaffold", "ladder"],
                "lighting_conditions": "low_light_frequent"
            },
            "optimized_thresholds": {
                "structure_misid": 82,
                "proper_ppe": 68,
                "no_violation": 78
            },
            "accuracy_improvement": 15.5,
            "confidence_calibration": {
                "before": 0.65,
                "after": 0.82,
                "improvement": 0.17
            },
            "recommendations": [
                "Increase structure misidentification threshold to 82%",
                "Focus on low-light condition training",
                "Implement crane-specific detection patterns"
            ]
        }
        
        insights = AutoLearningInsights(**data)
        
        assert insights.detected_patterns["common_structures"] == ["crane", "scaffold", "ladder"]
        assert insights.optimized_thresholds["structure_misid"] == 82
        assert insights.accuracy_improvement == 15.5
        assert len(insights.recommendations) == 3


class TestCustomerEnvironmentAnalysis:
    """Test CustomerEnvironmentAnalysis schema validation"""
    
    def test_valid_customer_environment_analysis(self):
        """Test valid CustomerEnvironmentAnalysis creation"""
        data = {
            "unique_structures": ["tower_crane", "mobile_crane", "scaffolding"],
            "common_false_positive_patterns": [
                "crane_arm_human_silhouette",
                "scaffold_shadow_person",
                "equipment_person_shape"
            ],
            "lighting_conditions": "variable_outdoor_industrial",
            "camera_angles": ["elevated_45deg", "ground_level", "overhead"],
            "equipment_types": ["construction_crane", "forklift", "excavator"],
            "optimization_opportunities": [
                "crane_specific_training",
                "shadow_detection_improvement",
                "lighting_normalization"
            ]
        }
        
        analysis = CustomerEnvironmentAnalysis(**data)
        
        assert len(analysis.unique_structures) == 3
        assert len(analysis.common_false_positive_patterns) == 3
        assert analysis.lighting_conditions == "variable_outdoor_industrial"
        assert len(analysis.camera_angles) == 3
        assert len(analysis.equipment_types) == 3
        assert len(analysis.optimization_opportunities) == 3


class TestHealthCheckResponse:
    """Test HealthCheckResponse schema validation"""
    
    def test_valid_health_check_response(self):
        """Test valid HealthCheckResponse creation"""
        timestamp = datetime.now()
        
        data = {
            "status": "healthy",
            "timestamp": timestamp,
            "version": "1.0.0",
            "vlm_api_status": "connected",
            "database_status": "connected",
            "uptime_seconds": 3600
        }
        
        health = HealthCheckResponse(**data)
        
        assert health.status == "healthy"
        assert health.timestamp == timestamp
        assert health.version == "1.0.0"
        assert health.vlm_api_status == "connected"
        assert health.database_status == "connected"
        assert health.uptime_seconds == 3600
    
    def test_health_check_status_literals(self):
        """Test health check status literal validation"""
        base_data = {
            "timestamp": datetime.now(),
            "version": "1.0.0",
            "vlm_api_status": "connected",
            "database_status": "connected",
            "uptime_seconds": 3600
        }
        
        # Valid statuses
        for status in ["healthy", "unhealthy"]:
            health = HealthCheckResponse(**{**base_data, "status": status})
            assert health.status == status
        
        # Valid connection statuses
        for conn_status in ["connected", "disconnected", "error"]:
            health = HealthCheckResponse(**{
                **base_data,
                "status": "healthy",
                "vlm_api_status": conn_status,
                "database_status": conn_status
            })
            assert health.vlm_api_status == conn_status
            assert health.database_status == conn_status
        
        # Invalid statuses
        with pytest.raises(ValidationError):
            HealthCheckResponse(**{**base_data, "status": "invalid_status"})


class TestErrorResponse:
    """Test ErrorResponse schema validation"""
    
    def test_valid_error_response(self):
        """Test valid ErrorResponse creation"""
        timestamp = datetime.now()
        
        data = {
            "error": "ValidationError",
            "message": "Invalid input data provided",
            "details": {"field": "case_number", "issue": "invalid format"},
            "timestamp": timestamp,
            "request_id": "req-123456"
        }
        
        error = ErrorResponse(**data)
        
        assert error.error == "ValidationError"
        assert error.message == "Invalid input data provided"
        assert error.details == {"field": "case_number", "issue": "invalid format"}
        assert error.timestamp == timestamp
        assert error.request_id == "req-123456"
    
    def test_error_response_defaults(self):
        """Test ErrorResponse with default values"""
        error = ErrorResponse(
            error="TestError",
            message="Test error message"
        )
        
        assert error.error == "TestError"
        assert error.message == "Test error message"
        assert error.details is None
        assert isinstance(error.timestamp, datetime)
        assert error.request_id is None


class TestSchemaIntegration:
    """Test schema integration and complex scenarios"""
    
    def test_nested_schema_validation(self):
        """Test validation of schemas with nested Pydantic models"""
        vlm_result_data = {
            "detection_type": "HUMAN_DETECTED",
            "false_positive_likelihood": 30,
            "true_violation_likelihood": 70,
            "reasoning": "Person detected in restricted area",
            "recommendation": "REQUIRES_REVIEW",
            "confidence_score": 0.85,
            "processing_time_ms": 1500
        }
        
        processing_result_data = {
            "case_number": "V1250630001",
            "pk_event": 12345,
            "images_processed": [{"path": "/test/image.jpg"}],
            "vlm_results": [vlm_result_data],  # Nested VLMAnalysisResult
            "final_recommendation": "REQUIRES_REVIEW",
            "processing_status": "completed",
            "processing_time_total_ms": 1500
        }
        
        # Should automatically validate and convert nested data
        result = ProcessingResult(**processing_result_data)
        
        assert isinstance(result.vlm_results[0], VLMAnalysisResult)
        assert result.vlm_results[0].detection_type == "HUMAN_DETECTED"
        assert result.vlm_results[0].confidence_score == 0.85
    
    def test_schema_serialization_deserialization(self):
        """Test schema can be serialized and deserialized"""
        original_data = {
            "detection_type": "STRUCTURE_MISIDENTIFIED",
            "false_positive_likelihood": 90,
            "true_violation_likelihood": 10,
            "reasoning": "Crane structure misidentified as human",
            "recommendation": "DISMISS_ALERT",
            "confidence_score": 0.95,
            "processing_time_ms": 1200
        }
        
        # Create model instance
        result = VLMAnalysisResult(**original_data)
        
        # Serialize to dict
        serialized = result.dict()
        
        # Deserialize back to model
        deserialized = VLMAnalysisResult(**serialized)
        
        # Should be identical
        assert result == deserialized
        assert result.dict() == deserialized.dict()
    
    def test_schema_json_compatibility(self):
        """Test schemas are JSON serializable"""
        data = {
            "detection_type": "HUMAN_DETECTED",
            "false_positive_likelihood": 25,
            "true_violation_likelihood": 75,
            "reasoning": "Clear human figure detected",
            "recommendation": "REQUIRES_REVIEW",
            "confidence_score": 0.8,
            "processing_time_ms": 1500
        }
        
        result = VLMAnalysisResult(**data)
        
        # Should be able to convert to JSON string
        json_str = result.json()
        assert isinstance(json_str, str)
        assert "HUMAN_DETECTED" in json_str
        
        # Should be able to parse back from JSON
        parsed = VLMAnalysisResult.parse_raw(json_str)
        assert parsed == result
    
    def test_schema_field_aliases_and_descriptions(self):
        """Test schema field descriptions are preserved"""
        # Check that field descriptions exist in schema
        schema = VLMAnalysisResult.schema()
        
        properties = schema.get("properties", {})
        
        # Verify some key fields have descriptions
        assert "description" in properties.get("detection_type", {})
        assert "description" in properties.get("false_positive_likelihood", {})
        assert "description" in properties.get("confidence_score", {})
        
        # Verify constraint information is preserved
        fp_likelihood = properties.get("false_positive_likelihood", {})
        assert fp_likelihood.get("minimum") == 0
        assert fp_likelihood.get("maximum") == 100