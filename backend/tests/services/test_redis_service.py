"""
Redis Service Tests
Comprehensive tests for Redis caching functionality
"""

import pytest
import json
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime

from app.services.redis_service import RedisService, redis_service
from app.models.schemas import VLMAnalysisResult


class TestRedisServiceInit:
    """Test Redis service initialization"""
    
    def test_service_initialization_disabled(self):
        """Test service initializes correctly when disabled"""
        with patch('app.services.redis_service.settings') as mock_settings:
            mock_settings.redis_enabled = False

            service = RedisService()
            assert not service.enabled
            assert service.redis_client is None
    
    def test_service_initialization_enabled(self):
        """Test service initializes correctly when enabled"""
        with patch('app.services.redis_service.settings') as mock_settings:
            mock_settings.redis_enabled = True
            mock_settings.redis_url = "redis://localhost:6379/0"
            mock_settings.redis_max_connections = 10
            mock_settings.redis_cache_ttl = 3600

            service = RedisService()
            assert service.enabled
            assert service.cache_ttl == 3600  # Default TTL


class TestRedisConnection:
    """Test Redis connection management"""
    
    @pytest.mark.asyncio
    async def test_initialize_success(self):
        """Test successful Redis initialization"""
        with patch('app.services.redis_service.settings') as mock_settings, \
             patch('redis.asyncio.from_url') as mock_redis:
            
            mock_settings.redis_enabled = True
            mock_settings.redis_url = "redis://localhost:6379/0"
            mock_settings.redis_max_connections = 10
            
            mock_client = AsyncMock()
            mock_redis.return_value = mock_client
            mock_client.ping.return_value = True
            
            service = RedisService()
            result = await service.initialize()
            
            assert result is True
            assert service.enabled is True
            mock_client.ping.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_initialize_connection_failure(self):
        """Test Redis initialization with connection failure"""
        with patch('app.services.redis_service.settings') as mock_settings, \
             patch('redis.asyncio.from_url') as mock_redis:
            
            mock_settings.redis_enabled = True
            mock_settings.redis_url = "redis://localhost:6379/0"
            mock_settings.redis_max_connections = 10
            
            mock_client = AsyncMock()
            mock_redis.return_value = mock_client
            mock_client.ping.side_effect = Exception("Connection failed")
            
            service = RedisService()
            result = await service.initialize()
            
            assert result is False
            assert service.enabled is False
    
    @pytest.mark.asyncio
    async def test_initialize_disabled(self):
        """Test initialization when Redis is disabled"""
        with patch('app.services.redis_service.settings') as mock_settings:
            mock_settings.redis_enabled = False
            
            service = RedisService()
            result = await service.initialize()
            
            assert result is False
            assert service.enabled is False


class TestVLMCaching:
    """Test VLM analysis result caching"""
    
    @pytest.mark.asyncio
    async def test_vlm_cache_set_success(self):
        """Test successful VLM cache set operation"""
        with patch('app.services.redis_service.settings') as mock_settings:
            mock_settings.redis_enabled = True

            service = RedisService()
            service.enabled = True
            service.redis_client = AsyncMock()

            test_result = {
                "detection_type": "STRUCTURE_MISIDENTIFIED",
                "confidence_score": 85.5,
                "reasoning": "Test reasoning"
            }

            result = await service.set_vlm_cache(
                "test_hash", "VLM-38B-AWQ", "test_prompt", test_result
            )

            assert result is True
            service.redis_client.setex.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_vlm_cache_get_hit(self):
        """Test successful VLM cache get operation (cache hit)"""
        with patch('app.services.redis_service.settings') as mock_settings:
            mock_settings.redis_enabled = True

            service = RedisService()
            service.enabled = True
            service.redis_client = AsyncMock()

            cached_data = {
                "detection_type": "STRUCTURE_MISIDENTIFIED",
                "confidence_score": 85.5,
                "reasoning": "Test reasoning"
            }

            service.redis_client.get.return_value = json.dumps(cached_data)

            result = await service.get_vlm_cache(
                "test_hash", "VLM-38B-AWQ", "test_prompt"
            )

            assert result == cached_data
            service.redis_client.get.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_vlm_cache_get_miss(self):
        """Test VLM cache get operation (cache miss)"""
        with patch('app.services.redis_service.settings') as mock_settings:
            mock_settings.redis_enabled = True

            service = RedisService()
            service.enabled = True
            service.redis_client = AsyncMock()

            service.redis_client.get.return_value = None

            result = await service.get_vlm_cache(
                "test_hash", "VLM-38B-AWQ", "test_prompt"
            )

            assert result is None
            service.redis_client.get.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_vlm_cache_disabled(self):
        """Test VLM caching when Redis is disabled"""
        service = RedisService()
        service.enabled = False
        
        # Set operation should return False
        result = await service.set_vlm_cache(
            "test_hash", "VLM-38B-AWQ", "test_prompt", {}
        )
        assert result is False
        
        # Get operation should return None
        result = await service.get_vlm_cache(
            "test_hash", "VLM-38B-AWQ", "test_prompt"
        )
        assert result is None


class TestInsightsCaching:
    """Test insights data caching"""
    
    @pytest.mark.asyncio
    async def test_insights_cache_set_success(self):
        """Test successful insights cache set operation"""
        with patch('app.services.redis_service.settings') as mock_settings:
            mock_settings.redis_enabled = True

            service = RedisService()
            service.enabled = True
            service.redis_client = AsyncMock()

            insights_data = {
                "batch_id": "test_batch",
                "analysis_metrics": {"total_cases": 100},
                "false_positive_patterns": []
            }

            result = await service.set_insights_cache("test_batch", insights_data)

            assert result is True
            service.redis_client.setex.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_insights_cache_get_hit(self):
        """Test successful insights cache get operation"""
        with patch('app.services.redis_service.settings') as mock_settings:
            mock_settings.redis_enabled = True

            service = RedisService()
            service.enabled = True
            service.redis_client = AsyncMock()

            cached_data = {
                "batch_id": "test_batch",
                "analysis_metrics": {"total_cases": 100}
            }

            service.redis_client.get.return_value = json.dumps(cached_data)

            result = await service.get_insights_cache("test_batch")

            assert result == cached_data
            service.redis_client.get.assert_called_once_with("insights:test_batch")


class TestHealthCheck:
    """Test Redis health check functionality"""
    
    @pytest.mark.asyncio
    async def test_health_check_success(self):
        """Test successful Redis health check"""
        with patch('app.services.redis_service.settings') as mock_settings:
            mock_settings.redis_enabled = True

            service = RedisService()
            service.enabled = True
            service.redis_client = AsyncMock()

            service.redis_client.ping.return_value = True
            service.redis_client.info.return_value = {
                "redis_version": "6.2.0",
                "used_memory_human": "1.5M",
                "connected_clients": 5,
                "total_commands_processed": 1000
            }

            result = await service.get_health_status()

            assert result["status"] == "connected"
            assert result["enabled"] is True
            assert result["connected"] is True
            assert "response_time_ms" in result
            assert result["redis_version"] == "6.2.0"
    
    @pytest.mark.asyncio
    async def test_health_check_disabled(self):
        """Test health check when Redis is disabled"""
        service = RedisService()
        service.enabled = False
        
        result = await service.get_health_status()
        
        assert result["status"] == "disabled"
        assert result["enabled"] is False


class TestCacheKeyGeneration:
    """Test cache key generation"""
    
    def test_generate_cache_key(self):
        """Test cache key generation for VLM results"""
        service = RedisService()

        key = service._generate_cache_key("vlm", "test_hash", "VLM-38B-AWQ", "test_prompt")

        # Should return MD5 hash
        assert len(key) == 32  # MD5 hash length
        assert isinstance(key, str)

        # Same inputs should generate same key
        key2 = service._generate_cache_key("vlm", "test_hash", "VLM-38B-AWQ", "test_prompt")
        assert key == key2

        # Different inputs should generate different keys
        key3 = service._generate_cache_key("vlm", "different_hash", "VLM-38B-AWQ", "test_prompt")
        assert key != key3


class TestErrorHandling:
    """Test error handling in Redis operations"""
    
    @pytest.mark.asyncio
    async def test_cache_set_error_handling(self):
        """Test error handling during cache set operations"""
        with patch('app.services.redis_service.settings') as mock_settings:
            mock_settings.redis_enabled = True

            service = RedisService()
            service.enabled = True
            service.redis_client = AsyncMock()

            # Import RedisError for proper exception handling
            from redis.exceptions import RedisError
            service.redis_client.setex.side_effect = RedisError("Redis error")

            result = await service.set_vlm_cache(
                "test_hash", "VLM-38B-AWQ", "test_prompt", {}
            )

            assert result is False
    
    @pytest.mark.asyncio
    async def test_cache_get_error_handling(self):
        """Test error handling during cache get operations"""
        with patch('app.services.redis_service.settings') as mock_settings:
            mock_settings.redis_enabled = True

            service = RedisService()
            service.enabled = True
            service.redis_client = AsyncMock()

            # Import RedisError for proper exception handling
            from redis.exceptions import RedisError
            service.redis_client.get.side_effect = RedisError("Redis error")

            result = await service.get_vlm_cache(
                "test_hash", "VLM-38B-AWQ", "test_prompt"
            )

            assert result is None
