"""
Comprehensive tests for VLM Service
Tests API request/response handling, image encoding, error handling, and rate limiting
"""

import pytest
import asyncio
import json
import base64
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from pathlib import Path
import httpx
from PIL import Image
import io

from app.services.vlm_service import VLMService
from app.models.schemas import VLMAnalysisResult


class TestVLMServiceInit:
    """Test VLMService initialization"""
    
    def test_service_initialization(self, mock_settings):
        """Test service initializes with correct settings"""
        service = VLMService()
        
        assert service.base_url == "http://test-api.example.com"
        assert service.api_key == "test-key-123"
        assert service.model_name == "test-model"
        assert service.timeout == 10
        assert service.max_tokens == 1000
        assert service.temperature == 0.1
        assert service.semaphore._value == 2  # max_concurrent_requests


class TestImageEncoding:
    """Test image encoding and compression functionality"""
    
    @pytest.mark.asyncio
    async def test_encode_small_image_success(self, mock_vlm_service, sample_image_path):
        """Test successful encoding of small image"""
        result = await mock_vlm_service._encode_image(sample_image_path)
        
        assert isinstance(result, str)
        assert len(result) > 0
        # Verify it's valid base64
        try:
            base64.b64decode(result)
        except Exception:
            pytest.fail("Result is not valid base64")
    
    @pytest.mark.asyncio
    async def test_encode_large_image_compression(self, mock_vlm_service, large_sample_image_path):
        """Test compression of large images"""
        with patch.object(mock_vlm_service, '_compress_and_encode_image') as mock_compress:
            mock_compress.return_value = "compressed_image_b64"
            
            result = await mock_vlm_service._encode_image(large_sample_image_path)
            
            mock_compress.assert_called_once_with(Path(large_sample_image_path))
            assert result == "compressed_image_b64"
    
    @pytest.mark.asyncio
    async def test_encode_nonexistent_image_failure(self, mock_vlm_service, invalid_image_path):
        """Test failure when image file doesn't exist"""
        with pytest.raises(FileNotFoundError):
            await mock_vlm_service._encode_image(invalid_image_path)
    
    @pytest.mark.asyncio
    async def test_compress_and_encode_image(self, mock_vlm_service, large_sample_image_path):
        """Test image compression and encoding"""
        result = await mock_vlm_service._compress_and_encode_image(Path(large_sample_image_path))
        
        assert isinstance(result, str)
        assert len(result) > 0
        
        # Verify the compressed image is valid
        try:
            image_data = base64.b64decode(result)
            img = Image.open(io.BytesIO(image_data))
            assert img.format == 'JPEG'
            # Should be resized if originally larger than 1920
            assert max(img.size) <= 1920
        except Exception as e:
            pytest.fail(f"Compressed image is invalid: {e}")
    
    @pytest.mark.asyncio
    async def test_compress_rgb_conversion(self, mock_vlm_service, temp_test_dir):
        """Test RGB conversion during compression"""
        # Create a RGBA image
        img = Image.new('RGBA', (100, 100), color=(255, 0, 0, 128))
        test_path = temp_test_dir / "test_rgba.png"
        img.save(test_path, 'PNG')
        
        result = await mock_vlm_service._compress_and_encode_image(test_path)
        
        # Verify conversion to RGB/JPEG
        image_data = base64.b64decode(result)
        processed_img = Image.open(io.BytesIO(image_data))
        assert processed_img.mode == 'RGB'
        assert processed_img.format == 'JPEG'
    
    @pytest.mark.asyncio
    async def test_compress_corrupted_image_failure(self, mock_vlm_service, corrupted_image_path):
        """Test failure with corrupted image file"""
        with pytest.raises(Exception):
            await mock_vlm_service._compress_and_encode_image(Path(corrupted_image_path))


class TestAPIRequests:
    """Test VLM API request handling"""
    
    @pytest.mark.asyncio
    async def test_make_api_request_success(self, mock_vlm_service, sample_image_b64, sample_vlm_response):
        """Test successful API request"""
        with patch('httpx.AsyncClient') as mock_client_class:
            mock_client = AsyncMock()
            mock_client_class.return_value.__aenter__.return_value = mock_client
            
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.json.return_value = sample_vlm_response
            mock_client.post.return_value = mock_response
            
            result = await mock_vlm_service._make_api_request(
                sample_image_b64, "test prompt", "V1250630001"
            )
            
            # Verify API call was made correctly
            mock_client.post.assert_called_once()
            call_args = mock_client.post.call_args
            
            assert call_args[0][0] == "http://test-api.example.com/v1/chat/completions"
            assert "Authorization" in call_args[1]["headers"]
            assert call_args[1]["headers"]["Authorization"] == "Bearer test-key-123"
            
            # Verify request payload
            payload = call_args[1]["json"]
            assert payload["model"] == "test-model"
            assert payload["max_tokens"] == 1000
            assert payload["temperature"] == 0.1
            assert len(payload["messages"]) == 1
            assert payload["metadata"]["case_number"] == "V1250630001"
            
            # Verify image in request
            content = payload["messages"][0]["content"]
            assert len(content) == 2
            assert content[0]["type"] == "text"
            assert content[1]["type"] == "image_url"
            assert sample_image_b64 in content[1]["image_url"]["url"]
            
            assert result == sample_vlm_response
    
    @pytest.mark.asyncio
    async def test_make_api_request_http_error(self, mock_vlm_service, sample_image_b64):
        """Test API request with HTTP error"""
        with patch('httpx.AsyncClient') as mock_client_class:
            mock_client = AsyncMock()
            mock_client_class.return_value.__aenter__.return_value = mock_client
            
            mock_response = Mock()
            mock_response.status_code = 500
            mock_response.text = "Internal Server Error"
            mock_client.post.return_value = mock_response
            
            with pytest.raises(Exception) as exc_info:
                await mock_vlm_service._make_api_request(
                    sample_image_b64, "test prompt", "V1250630001"
                )
            
            assert "VLM API request failed: 500" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_make_api_request_timeout(self, mock_vlm_service, sample_image_b64):
        """Test API request timeout"""
        with patch('httpx.AsyncClient') as mock_client_class:
            mock_client = AsyncMock()
            mock_client_class.return_value.__aenter__.return_value = mock_client
            mock_client.post.side_effect = httpx.TimeoutException("Request timed out")
            
            with pytest.raises(httpx.TimeoutException):
                await mock_vlm_service._make_api_request(
                    sample_image_b64, "test prompt", "V1250630001"
                )


class TestResponseParsing:
    """Test VLM response parsing"""
    
    def test_parse_json_response_success(self, mock_vlm_service, sample_vlm_response):
        """Test parsing valid JSON response"""
        result = mock_vlm_service._parse_vlm_response(sample_vlm_response, 1500)
        
        assert isinstance(result, VLMAnalysisResult)
        assert result.detection_type == "STRUCTURE_MISIDENTIFIED"
        assert result.false_positive_likelihood == 85
        assert result.true_violation_likelihood == 15
        assert result.recommendation == "DISMISS_ALERT"
        assert result.confidence_score == 0.9
        assert result.processing_time_ms == 1500
        assert "crane structure" in result.reasoning.lower()
    
    def test_parse_text_response_fallback(self, mock_vlm_service, sample_vlm_text_response):
        """Test parsing text response with fallback method"""
        result = mock_vlm_service._parse_vlm_response(sample_vlm_text_response, 1200)
        
        assert isinstance(result, VLMAnalysisResult)
        assert result.detection_type == "STRUCTURE_MISIDENTIFIED"
        assert result.processing_time_ms == 1200
        assert result.confidence_score > 0
        # Should extract original content as reasoning
        assert "structure misidentification" in result.reasoning.lower()
    
    def test_parse_malformed_response_error_handling(self, mock_vlm_service):
        """Test handling of malformed response"""
        malformed_response = {
            "choices": [{"message": {"content": "invalid json {"}}]
        }
        
        result = mock_vlm_service._parse_vlm_response(malformed_response, 1000)
        
        assert isinstance(result, VLMAnalysisResult)
        assert result.detection_type == "PARSE_ERROR"
        assert result.confidence_score == 0.0
        assert result.recommendation == "REQUIRES_REVIEW"
        assert "Failed to parse response" in result.reasoning
    
    def test_parse_text_response_keyword_detection(self, mock_vlm_service):
        """Test text response parsing with different keywords"""
        test_cases = [
            {
                "content": "I am certain this shows a human person in the image",
                "expected_type": "HUMAN_DETECTED",
                "expected_confidence": 0.8
            },
            {
                "content": "This appears to be a crane structure, not a person",
                "expected_type": "STRUCTURE_MISIDENTIFIED",
                "expected_confidence": 0.6
            },
            {
                "content": "I'm uncertain about what this shows, might be equipment",
                "expected_type": "UNKNOWN",
                "expected_confidence": 0.4
            }
        ]
        
        for case in test_cases:
            parsed = mock_vlm_service._parse_text_response(case["content"])
            
            assert parsed["detection_type"] == case["expected_type"]
            assert parsed["confidence_score"] == case["expected_confidence"]
            assert parsed["reasoning"] == case["content"]


class TestAnalyzeImage:
    """Test single image analysis"""
    
    @pytest.mark.asyncio
    async def test_analyze_image_success(self, mock_vlm_service, sample_image_path, sample_vlm_response):
        """Test successful image analysis"""
        with patch.object(mock_vlm_service, '_encode_image') as mock_encode, \
             patch.object(mock_vlm_service, '_make_api_request') as mock_api:
            
            mock_encode.return_value = "encoded_image"
            mock_api.return_value = sample_vlm_response
            
            result = await mock_vlm_service.analyze_image(
                sample_image_path, "V1250630001"
            )
            
            assert isinstance(result, VLMAnalysisResult)
            assert result.detection_type == "STRUCTURE_MISIDENTIFIED"
            assert result.processing_time_ms > 0
            
            mock_encode.assert_called_once_with(sample_image_path)
            mock_api.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_analyze_image_custom_prompt(self, mock_vlm_service, sample_image_path, sample_vlm_response):
        """Test image analysis with custom prompt"""
        custom_prompt = "Custom analysis prompt"
        
        with patch.object(mock_vlm_service, '_encode_image') as mock_encode, \
             patch.object(mock_vlm_service, '_make_api_request') as mock_api:
            
            mock_encode.return_value = "encoded_image"
            mock_api.return_value = sample_vlm_response
            
            await mock_vlm_service.analyze_image(
                sample_image_path, "V1250630001", custom_prompt
            )
            
            # Verify custom prompt was used
            call_args = mock_api.call_args[0]
            assert call_args[1] == custom_prompt
    
    @pytest.mark.asyncio
    async def test_analyze_image_encoding_failure(self, mock_vlm_service, sample_image_path):
        """Test image analysis with encoding failure"""
        with patch.object(mock_vlm_service, '_encode_image') as mock_encode:
            mock_encode.side_effect = FileNotFoundError("Image not found")
            
            with pytest.raises(FileNotFoundError):
                await mock_vlm_service.analyze_image(
                    sample_image_path, "V1250630001"
                )
    
    @pytest.mark.asyncio
    async def test_analyze_image_api_failure(self, mock_vlm_service, sample_image_path):
        """Test image analysis with API failure"""
        with patch.object(mock_vlm_service, '_encode_image') as mock_encode, \
             patch.object(mock_vlm_service, '_make_api_request') as mock_api:
            
            mock_encode.return_value = "encoded_image"
            mock_api.side_effect = Exception("API Error")
            
            with pytest.raises(Exception) as exc_info:
                await mock_vlm_service.analyze_image(
                    sample_image_path, "V1250630001"
                )
            
            assert "API Error" in str(exc_info.value)


class TestBatchAnalysis:
    """Test batch image analysis with rate limiting"""
    
    @pytest.mark.asyncio
    async def test_analyze_batch_success(self, mock_vlm_service, sample_vlm_response):
        """Test successful batch analysis"""
        image_paths = ["/path/img1.jpg", "/path/img2.jpg"]
        case_numbers = ["V1250630001", "V1250630002"]
        
        with patch.object(mock_vlm_service, 'analyze_image') as mock_analyze:
            # Mock successful analysis results
            mock_result = VLMAnalysisResult(
                detection_type="STRUCTURE_MISIDENTIFIED",
                false_positive_likelihood=85,
                true_violation_likelihood=15,
                reasoning="Test reasoning",
                recommendation="DISMISS_ALERT",
                confidence_score=0.9,
                processing_time_ms=1500
            )
            mock_analyze.return_value = mock_result
            
            results = await mock_vlm_service.analyze_batch(
                image_paths, case_numbers
            )
            
            assert len(results) == 2
            assert all(isinstance(r, VLMAnalysisResult) for r in results)
            assert mock_analyze.call_count == 2
    
    @pytest.mark.asyncio
    async def test_analyze_batch_mismatched_lengths(self, mock_vlm_service):
        """Test batch analysis with mismatched input lengths"""
        image_paths = ["/path/img1.jpg"]
        case_numbers = ["V1250630001", "V1250630002"]
        
        with pytest.raises(ValueError) as exc_info:
            await mock_vlm_service.analyze_batch(image_paths, case_numbers)
        
        assert "must match number of case numbers" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_analyze_batch_partial_failures(self, mock_vlm_service):
        """Test batch analysis with some failures"""
        image_paths = ["/path/img1.jpg", "/path/img2.jpg", "/path/img3.jpg"]
        case_numbers = ["V1250630001", "V1250630002", "V1250630003"]
        
        with patch.object(mock_vlm_service, 'analyze_image') as mock_analyze:
            # First succeeds, second fails, third succeeds
            success_result = VLMAnalysisResult(
                detection_type="HUMAN_DETECTED",
                false_positive_likelihood=20,
                true_violation_likelihood=80,
                reasoning="Success",
                recommendation="REQUIRES_REVIEW",
                confidence_score=0.8,
                processing_time_ms=1200
            )
            
            mock_analyze.side_effect = [
                success_result,
                Exception("Processing failed"),
                success_result
            ]
            
            results = await mock_vlm_service.analyze_batch(
                image_paths, case_numbers
            )
            
            assert len(results) == 3
            assert results[0].detection_type == "HUMAN_DETECTED"
            assert results[1].detection_type == "ERROR"
            assert results[1].confidence_score == 0.0
            assert results[1].recommendation == "REQUIRES_REVIEW"
            assert results[2].detection_type == "HUMAN_DETECTED"
    
    @pytest.mark.asyncio
    async def test_batch_rate_limiting(self, mock_vlm_service):
        """Test rate limiting with batch processing"""
        image_paths = [f"/path/img{i}.jpg" for i in range(5)]
        case_numbers = [f"V125063000{i}" for i in range(5)]
        
        # Track concurrent executions
        concurrent_count = 0
        max_concurrent = 0
        
        async def mock_analyze_with_tracking(*args, **kwargs):
            nonlocal concurrent_count, max_concurrent
            concurrent_count += 1
            max_concurrent = max(max_concurrent, concurrent_count)
            
            # Simulate processing time
            await asyncio.sleep(0.1)
            
            concurrent_count -= 1
            return VLMAnalysisResult(
                detection_type="TEST",
                false_positive_likelihood=50,
                true_violation_likelihood=50,
                reasoning="Test",
                recommendation="REQUIRES_REVIEW",
                confidence_score=0.5,
                processing_time_ms=100
            )
        
        with patch.object(mock_vlm_service, 'analyze_image', side_effect=mock_analyze_with_tracking):
            await mock_vlm_service.analyze_batch(image_paths, case_numbers)
            
            # Should not exceed max_concurrent_requests (2 in test settings)
            assert max_concurrent <= 2


class TestHealthCheck:
    """Test VLM service health check"""
    
    @pytest.mark.asyncio
    async def test_health_check_success(self, mock_vlm_service):
        """Test successful health check"""
        with patch('httpx.AsyncClient') as mock_client_class:
            mock_client = AsyncMock()
            mock_client_class.return_value.__aenter__.return_value = mock_client
            
            mock_response = Mock()
            mock_response.status_code = 200
            mock_client.get.return_value = mock_response
            
            result = await mock_vlm_service.health_check()
            
            assert result is True
            mock_client.get.assert_called_once_with("http://test-api.example.com/health")
    
    @pytest.mark.asyncio
    async def test_health_check_failure(self, mock_vlm_service):
        """Test health check failure"""
        with patch('httpx.AsyncClient') as mock_client_class:
            mock_client = AsyncMock()
            mock_client_class.return_value.__aenter__.return_value = mock_client
            mock_client.get.side_effect = Exception("Connection failed")
            
            result = await mock_vlm_service.health_check()
            
            assert result is False


class TestStandardPrompt:
    """Test standard prompt generation"""
    
    def test_get_standard_prompt(self, mock_vlm_service):
        """Test standard prompt contains required elements"""
        prompt = mock_vlm_service._get_standard_prompt()
        
        assert isinstance(prompt, str)
        assert len(prompt) > 0
        
        # Should contain key analysis instructions
        required_elements = [
            "safety violation analyst",
            "JSON format",
            "detection_type",
            "false_positive_likelihood",
            "recommendation",
            "DISMISS_ALERT",
            "REQUIRES_REVIEW"
        ]
        
        prompt_lower = prompt.lower()
        for element in required_elements:
            assert element.lower() in prompt_lower


class TestErrorHandling:
    """Test comprehensive error handling"""
    
    @pytest.mark.asyncio
    async def test_semaphore_cleanup_on_error(self, mock_vlm_service, sample_image_path):
        """Test semaphore is properly released on error"""
        initial_value = mock_vlm_service.semaphore._value
        
        with patch.object(mock_vlm_service, '_encode_image') as mock_encode:
            mock_encode.side_effect = Exception("Encoding failed")
            
            with pytest.raises(Exception):
                await mock_vlm_service.analyze_image(sample_image_path, "V1250630001")
            
            # Semaphore should be released even after error
            assert mock_vlm_service.semaphore._value == initial_value
    
    @pytest.mark.asyncio
    async def test_multiple_concurrent_errors(self, mock_vlm_service):
        """Test handling multiple concurrent errors"""
        image_paths = ["/path/img1.jpg", "/path/img2.jpg"]
        case_numbers = ["V1250630001", "V1250630002"]
        
        with patch.object(mock_vlm_service, 'analyze_image') as mock_analyze:
            mock_analyze.side_effect = [
                Exception("Error 1"),
                Exception("Error 2")
            ]
            
            results = await mock_vlm_service.analyze_batch(image_paths, case_numbers)
            
            assert len(results) == 2
            assert all(r.detection_type == "ERROR" for r in results)
            assert all(r.confidence_score == 0.0 for r in results)
            assert "Error 1" in results[0].reasoning
            assert "Error 2" in results[1].reasoning