"""
Test VLM-38B-AWQ Connectivity and Health Check
This script verifies the connection to the new VLM endpoint
"""

import asyncio
import logging
from pathlib import Path
import sys

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent))

from app.services.vlm_service_extended import ExtendedVLMService
from app.core.config import settings

# Create extended VLM service instance
vlm_service = ExtendedVLMService()

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


async def validate_endpoint_configuration():
    """Validate and potentially auto-correct endpoint configuration"""
    logger.info("=" * 60)
    logger.info("Validating VLM Endpoint Configuration")
    logger.info("=" * 60)
    
    validation_result = await vlm_service.validate_endpoint_configuration()
    
    logger.info(f"Original Base URL: {validation_result['original_base_url']}")
    logger.info(f"Validation Status: {validation_result['status']}")
    
    if validation_result['validated']:
        logger.info(f"✅ Working Endpoint: {validation_result['working_endpoint']}")
    
    if validation_result['recommendations']:
        logger.info("\nRecommendations:")
        for rec in validation_result['recommendations']:
            logger.info(f"  - {rec}")
    
    return validation_result['validated']


async def test_health_check():
    """Test VLM API health check"""
    logger.info("=" * 60)
    logger.info("Testing VLM-38B-AWQ API Health Check")
    logger.info("=" * 60)
    
    logger.info(f"VLM API Base URL: {settings.vlm_api_base_url}")
    logger.info(f"VLM Model Name: {settings.vlm_model_name}")
    logger.info(f"API Key: {'***' + settings.vlm_api_key[-4:] if settings.vlm_api_key else 'Not set'}")
    
    try:
        health_status = await vlm_service.health_check()
        logger.info(f"\nHealth Check Result:")
        for key, value in health_status.items():
            logger.info(f"  {key}: {value}")
        
        if health_status['status'] == 'connected':
            logger.info("\n✅ VLM API is connected and healthy!")
            return True
        else:
            logger.error(f"\n❌ VLM API connection failed: {health_status.get('error', 'Unknown error')}")
            return False
            
    except Exception as e:
        logger.error(f"\n❌ Health check failed with exception: {str(e)}")
        return False


async def test_image_analysis():
    """Test VLM image analysis with a sample image"""
    logger.info("\n" + "=" * 60)
    logger.info("Testing VLM-38B-AWQ Image Analysis")
    logger.info("=" * 60)
    
    # Check if we have sample images
    sample_images_dir = Path("./data/sample_images")
    if not sample_images_dir.exists():
        logger.warning("Sample images directory not found. Creating test image...")
        # Create a simple test image
        from PIL import Image
        import io
        import base64
        
        # Create a test image
        img = Image.new('RGB', (640, 480), color='red')
        buffer = io.BytesIO()
        img.save(buffer, format='JPEG')
        
        # Save test image
        sample_images_dir.mkdir(parents=True, exist_ok=True)
        test_image_path = sample_images_dir / "test_image.jpg"
        with open(test_image_path, 'wb') as f:
            f.write(buffer.getvalue())
        logger.info(f"Created test image at: {test_image_path}")
    else:
        # Use existing sample image if available
        sample_images = list(sample_images_dir.glob("*.jpg")) + list(sample_images_dir.glob("*.png"))
        if sample_images:
            test_image_path = sample_images[0]
            logger.info(f"Using existing sample image: {test_image_path}")
        else:
            logger.error("No sample images found in data/sample_images/")
            return False
    
    try:
        # Test single image analysis
        logger.info("\nAnalyzing test image...")
        result = await vlm_service.analyze_image(
            str(test_image_path),
            "TEST-001",
            custom_prompt="Analyze this image and describe what you see. If this is a safety monitoring image, identify any potential safety violations or false positives."
        )
        
        logger.info(f"\nAnalysis Result:")
        logger.info(f"  Detection Type: {result.detection_type}")
        logger.info(f"  False Positive Likelihood: {result.false_positive_likelihood}%")
        logger.info(f"  True Violation Likelihood: {result.true_violation_likelihood}%")
        logger.info(f"  Recommendation: {result.recommendation}")
        logger.info(f"  Confidence Score: {result.confidence_score}")
        logger.info(f"  Processing Time: {result.processing_time_ms}ms")
        logger.info(f"  Reasoning: {result.reasoning[:200]}...")
        
        logger.info("\n✅ VLM image analysis completed successfully!")
        return True
        
    except Exception as e:
        logger.error(f"\n❌ Image analysis failed: {str(e)}")
        return False


async def test_batch_processing():
    """Test VLM batch processing capabilities"""
    logger.info("\n" + "=" * 60)
    logger.info("Testing VLM-38B-AWQ Batch Processing")
    logger.info("=" * 60)
    
    # Create multiple test images
    sample_images_dir = Path("./data/sample_images")
    sample_images_dir.mkdir(parents=True, exist_ok=True)
    
    test_images = []
    case_numbers = []
    
    # Create 3 test images with different colors
    colors = ['red', 'green', 'blue']
    for i, color in enumerate(colors):
        from PIL import Image
        img = Image.new('RGB', (640, 480), color=color)
        image_path = sample_images_dir / f"batch_test_{color}.jpg"
        img.save(image_path)
        test_images.append(str(image_path))
        case_numbers.append(f"BATCH-{i+1:03d}")
        logger.info(f"Created test image: {image_path}")
    
    try:
        # Test batch processing
        logger.info(f"\nProcessing batch of {len(test_images)} images...")
        results = await vlm_service.analyze_batch(test_images, case_numbers)
        
        logger.info(f"\nBatch Processing Results:")
        for i, (case_num, result) in enumerate(zip(case_numbers, results)):
            logger.info(f"\n  Image {i+1} ({case_num}):")
            logger.info(f"    Detection Type: {result.detection_type}")
            logger.info(f"    False Positive Likelihood: {result.false_positive_likelihood}%")
            logger.info(f"    Recommendation: {result.recommendation}")
            logger.info(f"    Processing Time: {result.processing_time_ms}ms")
        
        successful_results = [r for r in results if r.detection_type != "ERROR"]
        logger.info(f"\n✅ Batch processing completed: {len(successful_results)}/{len(results)} successful")
        return len(successful_results) == len(results)
        
    except Exception as e:
        logger.error(f"\n❌ Batch processing failed: {str(e)}")
        return False


async def main():
    """Run all connectivity tests"""
    logger.info("\n" + "🚀 Starting VLM-38B-AWQ Integration Tests " + "🚀")
    logger.info("=" * 60)
    
    all_tests_passed = True
    
    # First validate endpoint configuration
    if not await validate_endpoint_configuration():
        logger.error("\n⚠️  Endpoint validation failed. Cannot proceed with tests.")
        return
    
    # Test 1: Health Check
    if not await test_health_check():
        all_tests_passed = False
        logger.error("\n⚠️  Health check failed. Please verify:")
        logger.error("1. The VLM endpoint is accessible at http://100.106.127.35:9500/v1")
        logger.error("2. The API key 'token-abc123' is correct")
        logger.error("3. Your network can reach the endpoint")
        return
    
    # Test 2: Single Image Analysis
    if not await test_image_analysis():
        all_tests_passed = False
        logger.error("\n⚠️  Image analysis failed. Check the VLM API logs for details.")
    
    # Test 3: Batch Processing
    if not await test_batch_processing():
        all_tests_passed = False
        logger.error("\n⚠️  Batch processing failed. This might indicate rate limiting issues.")
    
    # Summary
    logger.info("\n" + "=" * 60)
    if all_tests_passed:
        logger.info("✅ All VLM-38B-AWQ integration tests PASSED!")
        logger.info("The integration is ready for use.")
    else:
        logger.info("❌ Some tests FAILED. Please review the errors above.")
    logger.info("=" * 60)


if __name__ == "__main__":
    asyncio.run(main())