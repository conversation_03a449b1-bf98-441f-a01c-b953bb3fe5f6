#!/usr/bin/env python3
"""
Test validation script to check test structure and syntax
"""

import ast
import sys
from pathlib import Path


def validate_python_syntax(file_path):
    """Validate Python syntax of a file"""
    try:
        with open(file_path, 'r') as f:
            content = f.read()
        
        ast.parse(content)
        return True, None
    except SyntaxError as e:
        return False, str(e)
    except Exception as e:
        return False, str(e)


def count_test_methods(file_path):
    """Count test methods in a test file"""
    try:
        with open(file_path, 'r') as f:
            content = f.read()
        
        tree = ast.parse(content)
        test_count = 0
        
        for node in ast.walk(tree):
            if isinstance(node, ast.FunctionDef) and node.name.startswith('test_'):
                test_count += 1
        
        return test_count
    except:
        return 0


def main():
    """Validate all test files"""
    test_dir = Path(__file__).parent
    
    print("AI-FARM Backend Test Validation Report")
    print("=" * 50)
    
    test_files = list(test_dir.rglob("test_*.py"))
    total_tests = 0
    valid_files = 0
    
    for test_file in test_files:
        relative_path = test_file.relative_to(test_dir)
        
        # Validate syntax
        is_valid, error = validate_python_syntax(test_file)
        
        if is_valid:
            test_count = count_test_methods(test_file)
            total_tests += test_count
            valid_files += 1
            
            print(f"✅ {relative_path}: {test_count} tests")
        else:
            print(f"❌ {relative_path}: Syntax error - {error}")
    
    print("\n" + "=" * 50)
    print(f"Summary:")
    print(f"Total test files: {len(test_files)}")
    print(f"Valid test files: {valid_files}")
    print(f"Total test methods: {total_tests}")
    print(f"Syntax validation: {'PASSED' if valid_files == len(test_files) else 'FAILED'}")
    
    # Validate test structure
    expected_files = [
        "core/test_config.py",
        "models/test_schemas.py", 
        "services/test_vlm_service.py",
        "integration/test_config_integration.py"
    ]
    
    print(f"\nTest Structure Validation:")
    for expected in expected_files:
        expected_path = test_dir / expected
        if expected_path.exists():
            print(f"✅ {expected}")
        else:
            print(f"❌ {expected} - Missing")
    
    # Check for required files
    required_files = ["conftest.py", "pytest.ini"]
    for required in required_files:
        required_path = test_dir.parent / required if required == "pytest.ini" else test_dir / required
        if required_path.exists():
            print(f"✅ {required}")
        else:
            print(f"❌ {required} - Missing")
    
    print(f"\nTest validation complete!")
    return 0 if valid_files == len(test_files) else 1


if __name__ == "__main__":
    sys.exit(main())