#!/usr/bin/env python3
"""
AI-FARM Application Startup Script
Simple script to run the AI-FARM FastAPI application
"""

import sys
import os
from pathlib import Path

# Add the backend directory to the Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

try:
    import uvicorn
    from app.main import app
    from app.core.config import settings
    
    if __name__ == "__main__":
        # Create logs directory if it doesn't exist
        settings.logs_path.mkdir(parents=True, exist_ok=True)
        
        # Run the application
        # Always use string import to avoid worker/reload issues
        uvicorn.run(
            "app.main:app",
            host=settings.host,
            port=settings.port,
            log_level=settings.log_level.lower(),
            reload=settings.debug,
            workers=1 if settings.debug else settings.worker_processes
        )
        
except ImportError as e:
    print(f"Error importing required modules: {e}")
    print("Please install requirements: pip install -r requirements.txt")
    sys.exit(1)
except Exception as e:
    print(f"Error starting AI-FARM application: {e}")
    sys.exit(1)