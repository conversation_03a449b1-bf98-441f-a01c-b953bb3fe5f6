#!/usr/bin/env python3
"""
Test runner script for AI-FARM backend tests
Provides convenient test execution with different options
"""

import sys
import subprocess
import argparse


def run_tests(test_type="all", coverage=True, verbose=True, fail_fast=False):
    """Run tests with specified options"""
    
    cmd = ["python", "-m", "pytest"]
    
    # Add verbosity
    if verbose:
        cmd.append("-v")
    
    # Add fail fast
    if fail_fast:
        cmd.append("-x")
    
    # Add coverage
    if coverage:
        cmd.extend(["--cov=app", "--cov-report=term-missing", "--cov-report=html"])
    
    # Select test type
    if test_type == "unit":
        cmd.extend(["-m", "unit"])
    elif test_type == "integration":
        cmd.extend(["-m", "integration"])
    elif test_type == "vlm":
        cmd.extend(["-m", "vlm"])
    elif test_type == "fast":
        cmd.extend(["-m", "not slow"])
    elif test_type != "all":
        cmd.append(f"tests/{test_type}")
    
    print(f"Running command: {' '.join(cmd)}")
    
    # Run tests
    result = subprocess.run(cmd, cwd=".")
    return result.returncode


def main():
    parser = argparse.ArgumentParser(description="Run AI-FARM backend tests")
    parser.add_argument(
        "test_type", 
        nargs="?", 
        default="all",
        choices=["all", "unit", "integration", "vlm", "fast", "core", "models", "services"],
        help="Type of tests to run"
    )
    parser.add_argument(
        "--no-coverage", 
        action="store_true",
        help="Disable coverage reporting"
    )
    parser.add_argument(
        "--quiet", 
        action="store_true",
        help="Reduce verbosity"
    )
    parser.add_argument(
        "--fail-fast", 
        action="store_true",
        help="Stop on first failure"
    )
    
    args = parser.parse_args()
    
    exit_code = run_tests(
        test_type=args.test_type,
        coverage=not args.no_coverage,
        verbose=not args.quiet,
        fail_fast=args.fail_fast
    )
    
    sys.exit(exit_code)


if __name__ == "__main__":
    main()