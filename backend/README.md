# AI-FARM Backend

FastAPI-based backend service for AI-FARM false positive reduction system.

## Overview

The backend provides REST APIs for VLM integration, batch processing, and customer demo metrics. Built with FastAPI, SQLAlchemy, and designed for production deployment on Ubuntu servers.

## Architecture

```
backend/
├── app/
│   ├── api/                # REST API endpoints
│   │   ├── batch_processing.py    # File upload & processing requests
│   │   ├── health.py             # System health monitoring
│   │   ├── metrics.py            # Demo metrics & ROI calculations
│   │   └── status.py             # Real-time processing status
│   ├── core/
│   │   ├── config.py             # Environment configuration
│   │   └── database.py           # Database session management
│   ├── models/
│   │   ├── database.py           # SQLAlchemy models
│   │   └── schemas.py            # Pydantic API schemas
│   ├── services/
│   │   ├── vlm_service.py        # OpenAI-compatible VLM integration
│   │   ├── batch_processor.py    # CSV & image processing engine
│   │   └── auto_learning.py      # Pattern detection & optimization
│   └── utils/
│       ├── background_tasks.py   # Async task management
│       └── logging.py            # Structured logging
├── tests/                  # Comprehensive test suite (90%+ coverage)
└── requirements.txt        # Python dependencies
```

## Quick Start

### Development Setup (macOS)

```bash
cd backend

# Create virtual environment
python -m venv venv
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt

# Configure environment
cp ../.env.example ../.env
# Edit .env with your VLM API settings

# Run development server
python run.py
```

### Production Setup (Ubuntu)

```bash
# Install system dependencies
sudo apt update
sudo apt install python3 python3-pip python3-venv

# Setup application
cd backend
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt

# Configure for production
cp ../.env.example ../.env
# Set production values in .env

# Run with gunicorn
pip install gunicorn
gunicorn -w 4 -k uvicorn.workers.UvicornWorker app.main:app
```

## Configuration

All settings are managed through environment variables. Copy `.env.example` to `.env` and configure:

### Required Settings
- `VLM_API_BASE_URL` - Your VLM API endpoint
- `VLM_API_KEY` - API authentication key
- `VLM_MODEL_NAME` - Model name (default: internvl3-38b)

### Optional Settings
- Confidence thresholds (adjustable per customer)
- Processing limits and timeouts
- Storage paths and cleanup settings
- Performance and monitoring options

See [../docs/development/configuration.md](../docs/development/configuration.md) for complete reference.

## API Endpoints

### Health & Status
- `GET /health` - System health check
- `GET /api/v1/status/overview` - Processing overview
- `GET /api/v1/status/batch/{batch_id}` - Batch status

### Batch Processing
- `POST /api/v1/batch/upload` - Upload CSV/images
- `POST /api/v1/batch/process` - Start processing
- `GET /api/v1/batch/{batch_id}/results` - Get results

### Demo Metrics
- `GET /api/v1/metrics/demo` - Demo dashboard data
- `GET /api/v1/metrics/roi` - ROI calculations
- `GET /api/v1/metrics/learning` - Auto-learning insights

See [../docs/api/](../docs/api/) for complete API documentation.

## Key Features

### VLM Integration
- OpenAI-compatible API client
- Image compression and optimization
- Rate limiting and error recovery
- Custom prompt generation

### Batch Processing
- CSV data parsing and validation
- Mathematical case_number ↔ pk_event mapping
- Concurrent image processing
- Real-time progress tracking

### Auto-Learning Engine
- Customer-specific pattern detection
- Confidence threshold optimization
- Performance improvement tracking
- Accuracy calibration

### Production Ready
- Comprehensive error handling
- Structured logging with correlation IDs
- Database session management
- Background task processing
- Health monitoring endpoints

## Testing

Comprehensive test suite with 90%+ coverage:

```bash
# Run all tests
python run_tests.py

# Run specific test categories
python run_tests.py unit
python run_tests.py integration
python run_tests.py vlm

# Generate coverage report
python run_tests.py --coverage
```

Tests cover:
- API endpoints with mock data
- VLM service integration
- Database operations
- Configuration management
- Error handling scenarios

## Development

### Code Standards
- Type hints for all functions
- Pydantic models for data validation
- Comprehensive docstrings
- Error handling with custom exceptions
- Async/await patterns for I/O operations

### Database
- SQLAlchemy with declarative models
- Automatic migration support
- Connection pooling
- Session management with dependency injection

### Logging
- Structured JSON logging
- Request correlation IDs
- Performance metrics
- Error tracking and alerting

## Deployment

### Docker Support
```bash
# Build image
docker build -t ai-farm-backend .

# Run container
docker run -p 8000:8000 --env-file .env ai-farm-backend
```

### System Requirements (Ubuntu)
- Python 3.8+
- 4GB RAM minimum (8GB recommended)
- 10GB storage for processing
- Network access to VLM API

### Performance
- Handles 1000+ images per batch
- Concurrent VLM requests (configurable)
- Sub-second API response times
- Real-time progress tracking

## Monitoring

Health checks available at multiple levels:
- Application health (`/health`)
- Database connectivity
- VLM API accessibility
- Processing queue status
- System resource usage

Metrics exported for monitoring systems:
- Processing throughput
- API response times
- Error rates
- VLM API usage statistics

## Troubleshooting

Common issues and solutions documented in [../docs/development/troubleshooting.md](../docs/development/troubleshooting.md).

### Quick Checks
```bash
# Check configuration
python -c "from app.core.config import settings; print(settings.vlm_api_base_url)"

# Test VLM API connectivity
curl -H "Authorization: Bearer $VLM_API_KEY" $VLM_API_BASE_URL/health

# Check database
python -c "from app.core.database import engine; print(engine.url)"
```