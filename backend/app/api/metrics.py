"""
AI-FARM Demo Metrics API Endpoints
Customer-focused metrics and ROI calculations for demos
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional

from fastapi import APIRouter, HTTPException, Depends, Query
from sqlalchemy.orm import Session
from sqlalchemy import func

from ..core.config import settings
from ..core.database import get_database_session
from ..models.schemas import DemoMetrics, AutoLearningInsights
from ..services.auto_learning import auto_learning_engine

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/demo/{batch_id}", response_model=DemoMetrics)
async def get_demo_metrics(
    batch_id: str,
    hourly_rate: float = Query(50.0, description="Hourly rate for cost calculations"),
    db: Session = Depends(get_database_session)
):
    """
    Get demo-ready metrics for a specific batch for customer presentations
    """
    try:
        from ..models.database import BatchProcessing, CaseProcessingResult, VLMAnalysisResult
        
        # Get batch information
        batch_record = db.query(BatchProcessing).filter(
            BatchProcessing.batch_id == batch_id
        ).first()
        
        if not batch_record:
            raise HTTPException(status_code=404, detail=f"Batch {batch_id} not found")
        
        # Get case results
        case_results = db.query(CaseProcessingResult).filter(
            CaseProcessingResult.batch_id == batch_id,
            CaseProcessingResult.processing_status == "completed"
        ).all()
        
        if not case_results:
            raise HTTPException(status_code=400, detail="No completed results available for demo metrics")
        
        # Calculate basic metrics
        total_alerts = len(case_results)
        false_positives_filtered = len([
            r for r in case_results 
            if r.final_recommendation == "DISMISS_ALERT"
        ])
        alerts_requiring_review = total_alerts - false_positives_filtered
        filter_rate = (false_positives_filtered / total_alerts) * 100 if total_alerts > 0 else 0
        
        # Calculate time and cost savings
        # Assume 3 minutes per alert review on average
        minutes_saved = false_positives_filtered * 3
        time_saved_hours = minutes_saved / 60
        cost_savings_annual = time_saved_hours * hourly_rate * 365 / 30  # Monthly to annual
        
        # Calculate processing performance
        processing_times = [r.processing_time_ms for r in case_results if r.processing_time_ms]
        avg_processing_time = sum(processing_times) / len(processing_times) if processing_times else 0
        
        # Get VLM confidence scores
        vlm_results = []
        for case_result in case_results:
            vlm_results.extend(case_result.vlm_results)
        
        confidence_scores = [vlm.confidence_score for vlm in vlm_results if vlm.confidence_score]
        avg_confidence = sum(confidence_scores) / len(confidence_scores) if confidence_scores else 0
        
        return DemoMetrics(
            total_alerts_processed=total_alerts,
            false_positives_filtered=false_positives_filtered,
            alerts_requiring_review=alerts_requiring_review,
            filter_rate_percentage=filter_rate,
            time_saved_hours=time_saved_hours,
            cost_savings_annual=cost_savings_annual,
            processing_time_avg_ms=avg_processing_time,
            confidence_score_avg=avg_confidence
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get demo metrics for batch {batch_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get demo metrics: {str(e)}")


@router.get("/roi/{batch_id}")
async def calculate_roi_projection(
    batch_id: str,
    monthly_alerts: int = Query(17268, description="Customer's monthly alert volume"),
    hourly_rate: float = Query(50.0, description="Hourly rate for cost calculations"),
    implementation_cost: float = Query(225000.0, description="Implementation cost"),
    db: Session = Depends(get_database_session)
):
    """
    Calculate ROI projections based on demo results and customer parameters
    """
    try:
        # Get demo metrics
        demo_metrics = await get_demo_metrics(batch_id, hourly_rate, db)
        
        # Calculate projections based on customer scale
        monthly_false_positives = int(monthly_alerts * (demo_metrics.filter_rate_percentage / 100))
        monthly_time_saved = (monthly_false_positives * 3) / 60  # Hours
        monthly_cost_savings = monthly_time_saved * hourly_rate
        annual_cost_savings = monthly_cost_savings * 12
        
        # Calculate ROI
        net_annual_benefit = annual_cost_savings - (implementation_cost * 0.1)  # 10% annual maintenance
        roi_percentage = (net_annual_benefit / implementation_cost) * 100
        payback_months = implementation_cost / monthly_cost_savings if monthly_cost_savings > 0 else float('inf')
        
        # Calculate workload reduction
        original_review_hours = (monthly_alerts * 3) / 60
        new_review_hours = ((monthly_alerts - monthly_false_positives) * 3) / 60
        workload_reduction_percentage = ((original_review_hours - new_review_hours) / original_review_hours) * 100
        
        return {
            "batch_id": batch_id,
            "customer_parameters": {
                "monthly_alerts": monthly_alerts,
                "hourly_rate": hourly_rate,
                "implementation_cost": implementation_cost
            },
            "demo_performance": {
                "sample_size": demo_metrics.total_alerts_processed,
                "filter_rate_percentage": demo_metrics.filter_rate_percentage,
                "avg_confidence_score": demo_metrics.confidence_score_avg
            },
            "projected_impact": {
                "monthly_false_positives_filtered": monthly_false_positives,
                "monthly_time_saved_hours": monthly_time_saved,
                "monthly_cost_savings": monthly_cost_savings,
                "annual_cost_savings": annual_cost_savings,
                "workload_reduction_percentage": workload_reduction_percentage
            },
            "roi_analysis": {
                "implementation_cost": implementation_cost,
                "annual_net_benefit": net_annual_benefit,
                "roi_percentage": roi_percentage,
                "payback_period_months": min(payback_months, 999),  # Cap at 999 for display
                "break_even_point": "Year 1" if payback_months <= 12 else f"{payback_months/12:.1f} years"
            },
            "comparison": {
                "before": {
                    "monthly_alerts_to_review": monthly_alerts,
                    "monthly_review_hours": original_review_hours,
                    "monthly_cost": original_review_hours * hourly_rate
                },
                "after": {
                    "monthly_alerts_to_review": monthly_alerts - monthly_false_positives,
                    "monthly_review_hours": new_review_hours,
                    "monthly_cost": new_review_hours * hourly_rate
                }
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to calculate ROI for batch {batch_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to calculate ROI: {str(e)}")


@router.get("/auto-learning/{batch_id}", response_model=AutoLearningInsights)
async def get_auto_learning_insights(
    batch_id: str,
    db: Session = Depends(get_database_session)
):
    """
    Get auto-learning insights for a specific batch
    """
    try:
        from ..models.database import AutoLearningMetrics
        
        # Get auto-learning results from database
        learning_record = db.query(AutoLearningMetrics).filter(
            AutoLearningMetrics.batch_id == batch_id
        ).first()
        
        if not learning_record:
            raise HTTPException(status_code=404, detail=f"No auto-learning data found for batch {batch_id}")
        
        return AutoLearningInsights(
            detected_patterns=learning_record.detected_patterns or {},
            optimized_thresholds=learning_record.optimized_thresholds or {},
            accuracy_improvement=learning_record.accuracy_improvement or 0.0,
            confidence_calibration=learning_record.confidence_calibration or {},
            recommendations=learning_record.optimization_recommendations or []
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get auto-learning insights for batch {batch_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get insights: {str(e)}")


@router.get("/performance-comparison/{batch_id}")
async def get_performance_comparison(
    batch_id: str,
    baseline_filter_rate: float = Query(3.0, description="Baseline false positive filter rate (%)"),
    db: Session = Depends(get_database_session)
):
    """
    Compare AI-FARM performance against baseline system
    """
    try:
        # Get demo metrics
        demo_metrics = await get_demo_metrics(batch_id, 50.0, db)
        
        # Calculate improvement over baseline
        improvement_percentage = demo_metrics.filter_rate_percentage - baseline_filter_rate
        improvement_factor = demo_metrics.filter_rate_percentage / baseline_filter_rate if baseline_filter_rate > 0 else float('inf')
        
        # Calculate relative improvements
        workload_reduction = (improvement_percentage / 100) * demo_metrics.total_alerts_processed
        
        return {
            "batch_id": batch_id,
            "comparison": {
                "baseline_system": {
                    "filter_rate_percentage": baseline_filter_rate,
                    "alerts_filtered": int((baseline_filter_rate / 100) * demo_metrics.total_alerts_processed),
                    "alerts_for_review": demo_metrics.total_alerts_processed - int((baseline_filter_rate / 100) * demo_metrics.total_alerts_processed)
                },
                "ai_farm_system": {
                    "filter_rate_percentage": demo_metrics.filter_rate_percentage,
                    "alerts_filtered": demo_metrics.false_positives_filtered,
                    "alerts_for_review": demo_metrics.alerts_requiring_review
                }
            },
            "improvement_metrics": {
                "filter_rate_improvement_percentage": improvement_percentage,
                "improvement_factor": min(improvement_factor, 999),  # Cap for display
                "additional_alerts_filtered": int(workload_reduction),
                "workload_reduction_percentage": (improvement_percentage / (100 - baseline_filter_rate)) * 100 if baseline_filter_rate < 100 else 0
            },
            "performance_summary": {
                "total_alerts_analyzed": demo_metrics.total_alerts_processed,
                "average_processing_time_ms": demo_metrics.processing_time_avg_ms,
                "average_confidence_score": demo_metrics.confidence_score_avg,
                "system_reliability": "High" if demo_metrics.confidence_score_avg > 0.8 else "Medium"
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get performance comparison for batch {batch_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get comparison: {str(e)}")


@router.get("/dashboard/{batch_id}")
async def get_dashboard_data(
    batch_id: str,
    customer_name: Optional[str] = Query(None, description="Customer name for personalization"),
    db: Session = Depends(get_database_session)
):
    """
    Get comprehensive dashboard data for customer demo presentation
    """
    try:
        # Get all metrics
        demo_metrics = await get_demo_metrics(batch_id, 50.0, db)
        roi_data = await calculate_roi_projection(batch_id, 17268, 50.0, 225000.0, db)
        
        # Get auto-learning insights if available
        auto_learning_data = None
        try:
            auto_learning_data = await get_auto_learning_insights(batch_id, db)
        except:
            pass  # Auto-learning data might not be available
        
        # Get performance comparison
        performance_comparison = await get_performance_comparison(batch_id, 3.0, db)
        
        # Get batch timing information
        from ..models.database import BatchProcessing
        batch_record = db.query(BatchProcessing).filter(
            BatchProcessing.batch_id == batch_id
        ).first()
        
        # Create dashboard data
        dashboard_data = {
            "customer_info": {
                "name": customer_name or "Valued Customer",
                "demo_date": datetime.utcnow().strftime("%Y-%m-%d"),
                "batch_id": batch_id
            },
            "headline_metrics": {
                "false_positive_reduction": f"{demo_metrics.filter_rate_percentage:.1f}%",
                "alerts_filtered": demo_metrics.false_positives_filtered,
                "time_saved_monthly": f"{roi_data['projected_impact']['monthly_time_saved_hours']:.1f} hours",
                "annual_savings": f"${roi_data['projected_impact']['annual_cost_savings']:,.0f}",
                "roi_percentage": f"{roi_data['roi_analysis']['roi_percentage']:.0f}%",
                "payback_months": f"{roi_data['roi_analysis']['payback_period_months']:.1f}"
            },
            "processing_performance": {
                "total_alerts_processed": demo_metrics.total_alerts_processed,
                "processing_time_avg_ms": demo_metrics.processing_time_avg_ms,
                "confidence_score_avg": demo_metrics.confidence_score_avg,
                "batch_duration": (
                    (batch_record.completed_at - batch_record.started_at).total_seconds()
                    if batch_record and batch_record.completed_at and batch_record.started_at
                    else None
                )
            },
            "business_impact": {
                "workload_reduction_percentage": roi_data['projected_impact']['workload_reduction_percentage'],
                "monthly_cost_savings": roi_data['projected_impact']['monthly_cost_savings'],
                "annual_net_benefit": roi_data['roi_analysis']['annual_net_benefit'],
                "break_even_point": roi_data['roi_analysis']['break_even_point']
            },
            "comparison_vs_baseline": performance_comparison['improvement_metrics'],
            "auto_learning_summary": {
                "patterns_detected": len(auto_learning_data.detected_patterns) if auto_learning_data else 0,
                "accuracy_improvement": auto_learning_data.accuracy_improvement if auto_learning_data else 0,
                "recommendations_count": len(auto_learning_data.recommendations) if auto_learning_data else 0,
                "thresholds_optimized": len(auto_learning_data.optimized_thresholds) if auto_learning_data else 0
            } if auto_learning_data else None,
            "next_steps": [
                "Review detailed results and recommendations",
                "Discuss implementation timeline and requirements",
                "Plan pilot deployment with your data",
                "Configure system for your specific environment"
            ]
        }
        
        return dashboard_data
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get dashboard data for batch {batch_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get dashboard data: {str(e)}")


@router.get("/export/{batch_id}")
async def export_demo_results(
    batch_id: str,
    format: str = Query("json", description="Export format (json, csv)"),
    include_details: bool = Query(False, description="Include detailed VLM results"),
    db: Session = Depends(get_database_session)
):
    """
    Export demo results in various formats for customer review
    """
    try:
        if format not in ["json", "csv"]:
            raise HTTPException(status_code=400, detail="Format must be 'json' or 'csv'")
        
        # Get comprehensive data
        dashboard_data = await get_dashboard_data(batch_id, None, db)
        
        if format == "json":
            # Return JSON format
            export_data = {
                "export_info": {
                    "batch_id": batch_id,
                    "export_date": datetime.utcnow().isoformat(),
                    "format": "json",
                    "includes_details": include_details
                },
                "demo_results": dashboard_data
            }
            
            if include_details:
                # Add detailed results
                from ..api.batch_processing import get_batch_results
                detailed_results = await get_batch_results(batch_id, 1000, 0, db)
                export_data["detailed_results"] = detailed_results
            
            return export_data
        
        elif format == "csv":
            # Generate CSV data
            import pandas as pd
            import io
            
            # Create summary CSV
            summary_data = {
                "Metric": [
                    "Total Alerts Processed",
                    "False Positives Filtered",
                    "Filter Rate (%)",
                    "Time Saved (hours/month)",
                    "Annual Cost Savings ($)",
                    "ROI (%)",
                    "Payback Period (months)"
                ],
                "Value": [
                    dashboard_data["processing_performance"]["total_alerts_processed"],
                    dashboard_data["headline_metrics"]["alerts_filtered"],
                    dashboard_data["headline_metrics"]["false_positive_reduction"],
                    dashboard_data["headline_metrics"]["time_saved_monthly"],
                    dashboard_data["headline_metrics"]["annual_savings"],
                    dashboard_data["headline_metrics"]["roi_percentage"],
                    dashboard_data["headline_metrics"]["payback_months"]
                ]
            }
            
            df = pd.DataFrame(summary_data)
            csv_buffer = io.StringIO()
            df.to_csv(csv_buffer, index=False)
            
            from fastapi.responses import StreamingResponse
            csv_buffer.seek(0)
            
            return StreamingResponse(
                io.BytesIO(csv_buffer.getvalue().encode()),
                media_type="text/csv",
                headers={"Content-Disposition": f"attachment; filename=ai_farm_demo_results_{batch_id}.csv"}
            )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to export demo results for batch {batch_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to export results: {str(e)}")


@router.get("/historical")
async def get_historical_performance(
    days: int = Query(30, description="Number of days of historical data"),
    db: Session = Depends(get_database_session)
):
    """
    Get historical performance metrics across all batches
    """
    try:
        from ..models.database import BatchProcessing, CaseProcessingResult
        
        # Calculate date range
        end_date = datetime.utcnow()
        start_date = end_date - timedelta(days=days)
        
        # Get batches in date range
        batches = db.query(BatchProcessing).filter(
            BatchProcessing.started_at >= start_date,
            BatchProcessing.status == "completed"
        ).all()
        
        if not batches:
            return {
                "message": "No completed batches found in the specified date range",
                "date_range": {"start": start_date, "end": end_date}
            }
        
        # Calculate aggregate metrics
        total_cases = 0
        total_dismissed = 0
        total_processing_time = 0
        batch_count = len(batches)
        
        for batch in batches:
            case_results = db.query(CaseProcessingResult).filter(
                CaseProcessingResult.batch_id == batch.batch_id,
                CaseProcessingResult.processing_status == "completed"
            ).all()
            
            total_cases += len(case_results)
            total_dismissed += len([r for r in case_results if r.final_recommendation == "DISMISS_ALERT"])
            
            for result in case_results:
                if result.processing_time_ms:
                    total_processing_time += result.processing_time_ms
        
        # Calculate averages
        avg_filter_rate = (total_dismissed / total_cases) * 100 if total_cases > 0 else 0
        avg_processing_time = total_processing_time / total_cases if total_cases > 0 else 0
        
        return {
            "date_range": {
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat(),
                "days": days
            },
            "aggregate_metrics": {
                "total_batches": batch_count,
                "total_cases_processed": total_cases,
                "total_alerts_filtered": total_dismissed,
                "average_filter_rate_percentage": avg_filter_rate,
                "average_processing_time_ms": avg_processing_time
            },
            "performance_trends": {
                "system_stability": "Stable" if batch_count > 0 else "No data",
                "processing_efficiency": "Good" if avg_processing_time < 5000 else "Needs improvement",
                "filter_effectiveness": "High" if avg_filter_rate > 60 else "Moderate"
            }
        }
        
    except Exception as e:
        logger.error(f"Failed to get historical performance: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get historical data: {str(e)}")