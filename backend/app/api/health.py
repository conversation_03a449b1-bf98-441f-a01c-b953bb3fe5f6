"""
AI-FARM Health Check API Endpoints
Comprehensive health monitoring and system status
"""

import logging
import psutil
from datetime import datetime
from typing import Dict, Any

from fastapi import APIRouter, HTTPException, Depends
from sqlalchemy.orm import Session

from ..core.config import settings
from ..core.database import get_database_session, db_manager
from ..models.schemas import HealthCheckResponse
from ..services.vlm_service import vlm_service

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/", response_model=HealthCheckResponse)
async def comprehensive_health_check(db: Session = Depends(get_database_session)):
    """
    Comprehensive health check including all system components
    """
    try:
        # Check database connection
        db_health = db_manager.get_health_status()
        
        # Check VLM service
        vlm_health = await vlm_service.health_check()
        
        # Calculate uptime (assuming app started successfully)
        uptime_seconds = 0
        # This would be calculated from application startup time
        
        # Determine overall health
        is_healthy = (
            db_health["status"] == "connected" and
            vlm_health["status"] in ["connected", "error"]  # VLM can be temporarily unavailable
        )
        
        return HealthCheckResponse(
            status="healthy" if is_healthy else "unhealthy",
            timestamp=datetime.utcnow(),
            version="1.0.0",
            vlm_api_status=vlm_health["status"],
            database_status=db_health["status"],
            uptime_seconds=uptime_seconds
        )
        
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return HealthCheckResponse(
            status="unhealthy",
            timestamp=datetime.utcnow(),
            version="1.0.0",
            vlm_api_status="error",
            database_status="error",
            uptime_seconds=0
        )


@router.get("/detailed")
async def detailed_health_check(db: Session = Depends(get_database_session)):
    """
    Detailed health check with system metrics and component status
    """
    try:
        health_info = {
            "timestamp": datetime.utcnow().isoformat(),
            "overall_status": "healthy",
            "components": {},
            "system_metrics": {},
            "configuration": {}
        }
        
        # Database health
        try:
            db_health = db_manager.get_health_status()
            health_info["components"]["database"] = {
                "status": db_health["status"],
                "connection_successful": db_health["connection_successful"],
                "error": db_health.get("error")
            }
        except Exception as e:
            health_info["components"]["database"] = {
                "status": "error",
                "connection_successful": False,
                "error": str(e)
            }
        
        # VLM service health
        try:
            vlm_health = await vlm_service.health_check()
            health_info["components"]["vlm_service"] = vlm_health
        except Exception as e:
            health_info["components"]["vlm_service"] = {
                "status": "error",
                "error": str(e)
            }
        
        # System metrics
        try:
            health_info["system_metrics"] = {
                "memory_usage_mb": psutil.virtual_memory().used / (1024 * 1024),
                "memory_usage_percent": psutil.virtual_memory().percent,
                "cpu_usage_percent": psutil.cpu_percent(interval=1),
                "disk_usage_percent": psutil.disk_usage('/').percent,
                "load_average": psutil.getloadavg() if hasattr(psutil, 'getloadavg') else None
            }
        except Exception as e:
            health_info["system_metrics"] = {"error": str(e)}
        
        # Configuration status
        health_info["configuration"] = {
            "vlm_api_configured": bool(settings.vlm_api_base_url),
            "database_configured": bool(settings.database_url),
            "auto_learning_enabled": settings.enable_auto_learning,
            "debug_mode": settings.debug
        }
        
        # Determine overall status
        component_statuses = [
            comp.get("status", "unknown") 
            for comp in health_info["components"].values()
        ]
        
        if all(status in ["connected", "healthy"] for status in component_statuses):
            health_info["overall_status"] = "healthy"
        elif any(status == "error" for status in component_statuses):
            health_info["overall_status"] = "unhealthy"
        else:
            health_info["overall_status"] = "degraded"
        
        return health_info
        
    except Exception as e:
        logger.error(f"Detailed health check failed: {e}")
        return {
            "timestamp": datetime.utcnow().isoformat(),
            "overall_status": "unhealthy",
            "error": str(e)
        }


@router.get("/database")
async def database_health_check(db: Session = Depends(get_database_session)):
    """
    Specific database health check with connection testing
    """
    try:
        health_info = db_manager.get_health_status()
        
        # Additional database-specific checks
        try:
            # Test query execution
            db.execute("SELECT 1")
            
            # Get database statistics
            from ..models.database import BatchProcessing, CaseProcessingResult
            
            total_batches = db.query(BatchProcessing).count()
            total_cases = db.query(CaseProcessingResult).count()
            
            health_info.update({
                "query_execution": "successful",
                "statistics": {
                    "total_batches": total_batches,
                    "total_cases_processed": total_cases
                }
            })
            
        except Exception as e:
            health_info.update({
                "query_execution": "failed",
                "query_error": str(e)
            })
        
        return health_info
        
    except Exception as e:
        logger.error(f"Database health check failed: {e}")
        return {
            "status": "error",
            "connection_successful": False,
            "error": str(e)
        }


@router.get("/vlm")
async def vlm_service_health_check():
    """
    Specific VLM service health check with API testing
    """
    try:
        health_info = await vlm_service.health_check()
        
        # Additional VLM-specific checks
        try:
            # Get API usage statistics if available
            usage_stats = await vlm_service.get_api_usage_stats()
            if not usage_stats.get("error"):
                health_info["usage_statistics"] = usage_stats
            
        except Exception as e:
            health_info["usage_check_error"] = str(e)
        
        # Test configuration
        health_info["configuration"] = {
            "api_base_url": settings.vlm_api_base_url,
            "model_name": settings.vlm_model_name,
            "max_concurrent_requests": settings.max_concurrent_requests,
            "timeout_seconds": settings.vlm_timeout_seconds
        }
        
        return health_info
        
    except Exception as e:
        logger.error(f"VLM service health check failed: {e}")
        return {
            "status": "error",
            "error": str(e)
        }


@router.get("/performance")
async def performance_metrics():
    """
    System performance metrics and statistics
    """
    try:
        metrics = {}
        
        # System performance
        try:
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            metrics["system"] = {
                "cpu_usage_percent": cpu_percent,
                "memory_total_mb": memory.total / (1024 * 1024),
                "memory_used_mb": memory.used / (1024 * 1024),
                "memory_available_mb": memory.available / (1024 * 1024),
                "memory_usage_percent": memory.percent,
                "disk_total_gb": disk.total / (1024 * 1024 * 1024),
                "disk_used_gb": disk.used / (1024 * 1024 * 1024),
                "disk_free_gb": disk.free / (1024 * 1024 * 1024),
                "disk_usage_percent": (disk.used / disk.total) * 100
            }
            
            # Network I/O if available
            try:
                net_io = psutil.net_io_counters()
                metrics["network"] = {
                    "bytes_sent": net_io.bytes_sent,
                    "bytes_received": net_io.bytes_recv,
                    "packets_sent": net_io.packets_sent,
                    "packets_received": net_io.packets_recv
                }
            except:
                pass
                
        except Exception as e:
            metrics["system_error"] = str(e)
        
        # Application performance (would be tracked in real application)
        metrics["application"] = {
            "total_requests": 0,  # Would be tracked from middleware
            "average_response_time_ms": 0,
            "active_batch_jobs": 0,
            "completed_batch_jobs": 0,
            "failed_batch_jobs": 0
        }
        
        return metrics
        
    except Exception as e:
        logger.error(f"Performance metrics check failed: {e}")
        return {"error": str(e)}


@router.post("/maintenance/cleanup")
async def trigger_cleanup():
    """
    Trigger manual cleanup of old data and temporary files
    """
    try:
        if not settings.enable_customer_data_cleanup:
            raise HTTPException(
                status_code=400, 
                detail="Customer data cleanup is disabled in configuration"
            )
        
        # Run cleanup
        cleaned_sessions = db_manager.cleanup_old_sessions(
            settings.customer_data_retention_hours
        )
        
        # Additional cleanup tasks could be added here
        
        return {
            "status": "completed",
            "cleaned_demo_sessions": cleaned_sessions,
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Manual cleanup failed: {e}")
        raise HTTPException(status_code=500, detail=f"Cleanup failed: {str(e)}")


@router.get("/dependencies")
async def check_dependencies():
    """
    Check the status of external dependencies and services
    """
    try:
        dependencies = {}
        
        # Check VLM API
        try:
            vlm_health = await vlm_service.health_check()
            dependencies["vlm_api"] = {
                "name": "VLM API",
                "status": vlm_health["status"],
                "url": settings.vlm_api_base_url,
                "response_time_ms": vlm_health.get("response_time_ms"),
                "version": vlm_health.get("api_version")
            }
        except Exception as e:
            dependencies["vlm_api"] = {
                "name": "VLM API",
                "status": "error",
                "error": str(e)
            }
        
        # Check database
        try:
            db_health = db_manager.get_health_status()
            dependencies["database"] = {
                "name": "Database",
                "status": db_health["status"],
                "url": settings.database_url.split('@')[-1] if '@' in settings.database_url else settings.database_url,
                "connection_successful": db_health["connection_successful"]
            }
        except Exception as e:
            dependencies["database"] = {
                "name": "Database",
                "status": "error",
                "error": str(e)
            }
        
        # Overall dependency status
        all_healthy = all(
            dep.get("status") in ["connected", "healthy"] 
            for dep in dependencies.values()
        )
        
        return {
            "overall_status": "healthy" if all_healthy else "unhealthy",
            "dependencies": dependencies,
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Dependencies check failed: {e}")
        return {
            "overall_status": "error",
            "error": str(e),
            "timestamp": datetime.utcnow().isoformat()
        }