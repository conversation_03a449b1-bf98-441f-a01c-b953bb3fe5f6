"""
VALO AI-FARM Data Analysis API Endpoints
REST API for violation detection data analysis and false positive filtering
"""

from fastapi import APIRouter, HTTPException, Query
from fastapi.responses import JSONResponse
import logging
from typing import Dict, Any, List, Optional
import traceback

from ..services.data_analysis_service import data_analysis_service

logger = logging.getLogger(__name__)

# Create router
router = APIRouter()

@router.get('/health')
async def health_check():
    """Health check endpoint for data analysis service"""
    try:
        return {
            'status': 'healthy',
            'service': 'data_analysis',
            'timestamp': data_analysis_service.analysis_metrics.analysis_timestamp if data_analysis_service.analysis_metrics else None
        }
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(status_code=500, detail={'status': 'unhealthy', 'error': str(e)})

@router.post('/analyze')
async def run_analysis():
    """Run comprehensive data analysis on violation detection data"""
    try:
        logger.info("Starting comprehensive data analysis...")

        # Run the analysis
        analysis_result = data_analysis_service.analyze_false_positives()

        if not analysis_result:
            raise HTTPException(
                status_code=500,
                detail={
                    'success': False,
                    'error': 'Analysis failed - no data processed'
                }
            )

        logger.info("Data analysis completed successfully")
        return {
            'success': True,
            'message': 'Analysis completed successfully',
            'data': analysis_result
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Analysis failed: {e}")
        logger.error(traceback.format_exc())
        raise HTTPException(
            status_code=500,
            detail={
                'success': False,
                'error': str(e),
                'details': 'Check server logs for more information'
            }
        )

@router.get('/summary')
async def get_analysis_summary():
    """Get analysis summary and metrics"""
    try:
        summary = data_analysis_service.get_analysis_summary()

        return {
            'success': True,
            'data': summary
        }

    except Exception as e:
        logger.error(f"Failed to get analysis summary: {e}")
        raise HTTPException(
            status_code=500,
            detail={
                'success': False,
                'error': str(e)
            }
        )

@router.get('/metrics')
async def get_metrics():
    """Get key analysis metrics"""
    try:
        if not data_analysis_service.analysis_metrics:
            # Run analysis if not done yet
            data_analysis_service.analyze_false_positives()

        if not data_analysis_service.analysis_metrics:
            raise HTTPException(
                status_code=404,
                detail={
                    'success': False,
                    'error': 'No analysis data available'
                }
            )

        metrics = {
            'total_csv_cases': data_analysis_service.analysis_metrics.total_csv_cases,
            'total_image_sets': data_analysis_service.analysis_metrics.total_image_sets,
            'valid_detections': data_analysis_service.analysis_metrics.valid_detections,
            'invalid_detections': data_analysis_service.analysis_metrics.invalid_detections,
            'false_positive_rate': data_analysis_service.analysis_metrics.false_positive_rate,
            'filtering_effectiveness': data_analysis_service.analysis_metrics.filtering_effectiveness,
            'cases_with_images': data_analysis_service.analysis_metrics.cases_with_images,
            'cases_without_images': data_analysis_service.analysis_metrics.cases_without_images,
            'analysis_timestamp': data_analysis_service.analysis_metrics.analysis_timestamp
        }

        return {
            'success': True,
            'data': metrics
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get metrics: {e}")
        raise HTTPException(
            status_code=500,
            detail={
                'success': False,
                'error': str(e)
            }
        )

@router.get('/cases')
async def get_cases(
    has_images: Optional[bool] = Query(None, description="Filter by cases with images"),
    classification: Optional[str] = Query(None, description="Filter by classification (valid/invalid)"),
    follow_up: Optional[str] = Query(None, description="Filter by follow-up status"),
    camera: Optional[str] = Query(None, description="Filter by camera"),
    page: int = Query(1, ge=1, description="Page number"),
    per_page: int = Query(50, ge=1, le=1000, description="Items per page")
):
    """Get filtered violation cases"""
    try:
        # Build filters dictionary
        filters = {}

        if has_images is not None:
            filters['has_images'] = has_images

        if classification:
            filters['classification'] = classification

        if follow_up:
            filters['follow_up'] = follow_up

        if camera:
            filters['camera'] = camera

        # Get filtered cases
        cases = data_analysis_service.get_filtered_cases(filters)

        # Apply pagination
        start_idx = (page - 1) * per_page
        end_idx = start_idx + per_page
        paginated_cases = cases[start_idx:end_idx]

        return {
            'success': True,
            'data': {
                'cases': paginated_cases,
                'pagination': {
                    'page': page,
                    'per_page': per_page,
                    'total_cases': len(cases),
                    'total_pages': (len(cases) + per_page - 1) // per_page
                },
                'filters_applied': filters
            }
        }

    except Exception as e:
        logger.error(f"Failed to get cases: {e}")
        raise HTTPException(
            status_code=500,
            detail={
                'success': False,
                'error': str(e)
            }
        )

@router.get('/false-positives')
async def get_false_positives():
    """Get cases identified as false positives"""
    try:
        filters = {
            'has_images': True,
            'classification': 'invalid'
        }

        false_positive_cases = data_analysis_service.get_filtered_cases(filters)

        # Get additional analysis
        analysis_result = data_analysis_service.analyze_false_positives()

        return {
            'success': True,
            'data': {
                'false_positive_cases': false_positive_cases[:100],  # Limit to first 100
                'total_false_positives': len(false_positive_cases),
                'analysis': analysis_result.get('metrics', {}),
                'camera_breakdown': analysis_result.get('camera_analysis', {}),
                'infringement_breakdown': analysis_result.get('infringement_analysis', {})
            }
        }

    except Exception as e:
        logger.error(f"Failed to get false positives: {e}")
        raise HTTPException(
            status_code=500,
            detail={
                'success': False,
                'error': str(e)
            }
        )

@router.get('/valid-detections')
async def get_valid_detections():
    """Get cases identified as valid detections"""
    try:
        filters = {
            'has_images': True,
            'classification': 'valid'
        }

        valid_cases = data_analysis_service.get_filtered_cases(filters)

        return {
            'success': True,
            'data': {
                'valid_cases': valid_cases,
                'total_valid_detections': len(valid_cases)
            }
        }

    except Exception as e:
        logger.error(f"Failed to get valid detections: {e}")
        raise HTTPException(
            status_code=500,
            detail={
                'success': False,
                'error': str(e)
            }
        )

@router.get('/camera-analysis')
async def get_camera_analysis():
    """Get analysis breakdown by camera"""
    try:
        if not data_analysis_service.analysis_metrics:
            data_analysis_service.analyze_false_positives()

        analysis_result = data_analysis_service.analyze_false_positives()
        camera_analysis = analysis_result.get('camera_analysis', {})

        return {
            'success': True,
            'data': camera_analysis
        }

    except Exception as e:
        logger.error(f"Failed to get camera analysis: {e}")
        raise HTTPException(
            status_code=500,
            detail={
                'success': False,
                'error': str(e)
            }
        )

@router.get('/temporal-analysis')
async def get_temporal_analysis():
    """Get temporal analysis of violations"""
    try:
        if not data_analysis_service.analysis_metrics:
            data_analysis_service.analyze_false_positives()

        analysis_result = data_analysis_service.analyze_false_positives()
        temporal_analysis = analysis_result.get('temporal_analysis', {})

        return {
            'success': True,
            'data': temporal_analysis
        }

    except Exception as e:
        logger.error(f"Failed to get temporal analysis: {e}")
        raise HTTPException(
            status_code=500,
            detail={
                'success': False,
                'error': str(e)
            }
        )

@router.get('/infringement-analysis')
async def get_infringement_analysis():
    """Get analysis breakdown by infringement type"""
    try:
        if not data_analysis_service.analysis_metrics:
            data_analysis_service.analyze_false_positives()

        analysis_result = data_analysis_service.analyze_false_positives()
        infringement_analysis = analysis_result.get('infringement_analysis', {})

        return {
            'success': True,
            'data': infringement_analysis
        }

    except Exception as e:
        logger.error(f"Failed to get infringement analysis: {e}")
        raise HTTPException(
            status_code=500,
            detail={
                'success': False,
                'error': str(e)
            }
        )

@router.post('/reload-data')
async def reload_data():
    """Reload data from CSV and images"""
    try:
        logger.info("Reloading data from CSV and images...")

        # Clear existing data
        data_analysis_service.violation_cases = []
        data_analysis_service.analysis_metrics = None
        data_analysis_service.image_analysis_cache = {}

        # Reload and analyze
        cases = data_analysis_service.match_csv_with_images()
        analysis_result = data_analysis_service.analyze_false_positives()

        return {
            'success': True,
            'message': 'Data reloaded successfully',
            'data': {
                'total_cases_loaded': len(cases),
                'cases_with_images': len([c for c in cases if c.has_images]),
                'analysis_metrics': analysis_result.get('metrics', {})
            }
        }

    except Exception as e:
        logger.error(f"Failed to reload data: {e}")
        raise HTTPException(
            status_code=500,
            detail={
                'success': False,
                'error': str(e)
            }
        )

@router.get('/false-positive-patterns')
async def get_false_positive_patterns():
    """Get advanced false positive pattern analysis"""
    try:
        patterns = data_analysis_service.detect_false_positive_patterns()

        return {
            'success': True,
            'data': patterns
        }

    except Exception as e:
        logger.error(f"Failed to get false positive patterns: {e}")
        raise HTTPException(
            status_code=500,
            detail={
                'success': False,
                'error': str(e)
            }
        )

@router.get('/recommendations')
async def get_recommendations():
    """Get AI-driven recommendations for reducing false positives"""
    try:
        patterns = data_analysis_service.detect_false_positive_patterns()
        recommendations = patterns.get('recommendations', [])
        confidence_scores = patterns.get('confidence_scores', {})

        return {
            'success': True,
            'data': {
                'recommendations': recommendations,
                'confidence_scores': confidence_scores,
                'total_recommendations': len(recommendations),
                'high_priority_count': len([r for r in recommendations if r.get('priority') == 'high'])
            }
        }

    except Exception as e:
        logger.error(f"Failed to get recommendations: {e}")
        raise HTTPException(
            status_code=500,
            detail={
                'success': False,
                'error': str(e)
            }
        )

@router.get('/export')
async def export_analysis(export_format: str = Query('json', description="Export format (json or csv)")):
    """Export analysis results"""
    try:
        if export_format not in ['json', 'csv']:
            raise HTTPException(
                status_code=400,
                detail={
                    'success': False,
                    'error': 'Unsupported export format. Use json or csv.'
                }
            )

        # Get complete analysis
        analysis_result = data_analysis_service.analyze_false_positives()

        if export_format == 'json':
            return {
                'success': True,
                'data': analysis_result,
                'export_timestamp': data_analysis_service.analysis_metrics.analysis_timestamp if data_analysis_service.analysis_metrics else None
            }

        # CSV export would be implemented here
        raise HTTPException(
            status_code=501,
            detail={
                'success': False,
                'error': 'CSV export not yet implemented'
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to export analysis: {e}")
        raise HTTPException(
            status_code=500,
            detail={
                'success': False,
                'error': str(e)
            }
        )
