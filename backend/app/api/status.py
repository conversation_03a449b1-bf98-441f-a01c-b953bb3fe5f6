"""
AI-FARM Status Monitoring API Endpoints
Real-time progress tracking and status monitoring
"""

import logging
from datetime import datetime
from typing import Dict, Any, List, Optional

from fastapi import APIRouter, HTTPException, Depends, Query
from sqlalchemy.orm import Session

from ..core.database import get_database_session
from ..models.schemas import ProcessingStatus
from ..services.batch_processor import batch_processor

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/batch/{batch_id}")
async def get_batch_processing_status(
    batch_id: str,
    db: Session = Depends(get_database_session)
):
    """
    Get real-time status of a batch processing job
    """
    try:
        # Get status from batch processor
        batch_status = batch_processor.get_batch_status(batch_id)
        
        if not batch_status:
            raise HTTPException(status_code=404, detail=f"Batch {batch_id} not found")
        
        # Get additional details from database
        from ..models.database import BatchProcessing, CaseProcessingResult
        
        batch_record = db.query(BatchProcessing).filter(
            BatchProcessing.batch_id == batch_id
        ).first()
        
        if not batch_record:
            raise HTTPException(status_code=404, detail=f"Batch {batch_id} not found in database")
        
        # Get processing statistics
        total_cases = batch_record.total_cases
        processed_cases = batch_record.processed_cases
        failed_cases = batch_record.failed_cases
        
        # Calculate timing information
        elapsed_time = None
        estimated_remaining = None
        
        if batch_record.started_at:
            elapsed_seconds = (datetime.utcnow() - batch_record.started_at).total_seconds()
            elapsed_time = elapsed_seconds
            
            # Estimate remaining time if processing
            if batch_record.status == "processing" and processed_cases > 0:
                avg_time_per_case = elapsed_seconds / processed_cases
                remaining_cases = total_cases - processed_cases
                estimated_remaining = avg_time_per_case * remaining_cases
        
        # Get recent case results
        recent_results = db.query(CaseProcessingResult).filter(
            CaseProcessingResult.batch_id == batch_id
        ).order_by(CaseProcessingResult.processed_at.desc()).limit(5).all()
        
        recent_cases = []
        for result in recent_results:
            recent_cases.append({
                "case_number": result.case_number,
                "status": result.processing_status,
                "recommendation": result.final_recommendation,
                "processing_time_ms": result.processing_time_ms,
                "processed_at": result.processed_at
            })
        
        return {
            "batch_id": batch_id,
            "status": batch_record.status,
            "progress": {
                "total_cases": total_cases,
                "processed_cases": processed_cases,
                "failed_cases": failed_cases,
                "progress_percentage": batch_record.progress_percentage,
                "remaining_cases": total_cases - processed_cases - failed_cases
            },
            "timing": {
                "started_at": batch_record.started_at,
                "completed_at": batch_record.completed_at,
                "elapsed_time_seconds": elapsed_time,
                "estimated_remaining_seconds": estimated_remaining
            },
            "configuration": {
                "use_auto_learning": batch_record.use_auto_learning,
                "custom_thresholds": batch_record.custom_thresholds,
                "priority": batch_record.priority
            },
            "recent_results": recent_cases,
            "error_message": batch_record.error_message
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get batch status {batch_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get batch status: {str(e)}")


@router.get("/batch/{batch_id}/progress")
async def get_batch_progress_stream(
    batch_id: str,
    db: Session = Depends(get_database_session)
):
    """
    Get progress information suitable for real-time updates
    """
    try:
        batch_status = batch_processor.get_batch_status(batch_id)
        
        if not batch_status:
            raise HTTPException(status_code=404, detail=f"Batch {batch_id} not found")
        
        # Get current progress
        from ..models.database import BatchProcessing
        batch_record = db.query(BatchProcessing).filter(
            BatchProcessing.batch_id == batch_id
        ).first()
        
        if not batch_record:
            raise HTTPException(status_code=404, detail=f"Batch {batch_id} not found")
        
        # Calculate processing rate
        processing_rate = 0
        if batch_record.started_at and batch_record.processed_cases > 0:
            elapsed_seconds = (datetime.utcnow() - batch_record.started_at).total_seconds()
            processing_rate = batch_record.processed_cases / elapsed_seconds if elapsed_seconds > 0 else 0
        
        return {
            "batch_id": batch_id,
            "timestamp": datetime.utcnow(),
            "status": batch_record.status,
            "total_cases": batch_record.total_cases,
            "processed_cases": batch_record.processed_cases,
            "failed_cases": batch_record.failed_cases,
            "progress_percentage": batch_record.progress_percentage,
            "processing_rate_per_second": processing_rate,
            "is_complete": batch_record.status in ["completed", "failed", "cancelled"]
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get batch progress {batch_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get progress: {str(e)}")


@router.get("/batch/{batch_id}/summary")
async def get_batch_summary(
    batch_id: str,
    db: Session = Depends(get_database_session)
):
    """
    Get summary statistics for a completed batch
    """
    try:
        from ..models.database import BatchProcessing, CaseProcessingResult, VLMAnalysisResult
        
        batch_record = db.query(BatchProcessing).filter(
            BatchProcessing.batch_id == batch_id
        ).first()
        
        if not batch_record:
            raise HTTPException(status_code=404, detail=f"Batch {batch_id} not found")
        
        # Get case results
        case_results = db.query(CaseProcessingResult).filter(
            CaseProcessingResult.batch_id == batch_id
        ).all()
        
        if not case_results:
            return {
                "batch_id": batch_id,
                "status": batch_record.status,
                "message": "No results available yet"
            }
        
        # Calculate summary statistics
        total_cases = len(case_results)
        completed_cases = len([r for r in case_results if r.processing_status == "completed"])
        failed_cases = len([r for r in case_results if r.processing_status == "failed"])
        
        dismissed_alerts = len([r for r in case_results if r.final_recommendation == "DISMISS_ALERT"])
        review_required = len([r for r in case_results if r.final_recommendation == "REQUIRES_REVIEW"])
        
        # Calculate processing times
        processing_times = [r.processing_time_ms for r in case_results if r.processing_time_ms]
        avg_processing_time = sum(processing_times) / len(processing_times) if processing_times else 0
        
        # Get VLM statistics
        vlm_results = []
        for case_result in case_results:
            vlm_results.extend(case_result.vlm_results)
        
        confidence_scores = [vlm.confidence_score for vlm in vlm_results if vlm.confidence_score]
        avg_confidence = sum(confidence_scores) / len(confidence_scores) if confidence_scores else 0
        
        # Detection type distribution
        detection_types = [vlm.detection_type for vlm in vlm_results]
        from collections import Counter
        detection_distribution = dict(Counter(detection_types))
        
        return {
            "batch_id": batch_id,
            "status": batch_record.status,
            "processing_summary": {
                "total_cases": total_cases,
                "completed_cases": completed_cases,
                "failed_cases": failed_cases,
                "success_rate_percentage": (completed_cases / total_cases) * 100 if total_cases > 0 else 0
            },
            "filtering_results": {
                "dismissed_alerts": dismissed_alerts,
                "review_required": review_required,
                "filter_rate_percentage": (dismissed_alerts / total_cases) * 100 if total_cases > 0 else 0
            },
            "performance_metrics": {
                "average_processing_time_ms": avg_processing_time,
                "average_confidence_score": avg_confidence,
                "total_processing_time_seconds": sum(processing_times) / 1000 if processing_times else 0
            },
            "detection_analysis": {
                "detection_type_distribution": detection_distribution,
                "total_vlm_analyses": len(vlm_results)
            },
            "timing": {
                "started_at": batch_record.started_at,
                "completed_at": batch_record.completed_at,
                "total_duration_seconds": (
                    (batch_record.completed_at - batch_record.started_at).total_seconds()
                    if batch_record.started_at and batch_record.completed_at
                    else None
                )
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get batch summary {batch_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get summary: {str(e)}")


@router.get("/active")
async def get_active_batches(
    limit: int = Query(10, le=100),
    db: Session = Depends(get_database_session)
):
    """
    Get list of currently active batch processing jobs
    """
    try:
        from ..models.database import BatchProcessing
        
        active_batches = db.query(BatchProcessing).filter(
            BatchProcessing.status.in_(["pending", "processing"])
        ).order_by(BatchProcessing.started_at.desc()).limit(limit).all()
        
        batch_list = []
        for batch in active_batches:
            batch_list.append({
                "batch_id": batch.batch_id,
                "status": batch.status,
                "total_cases": batch.total_cases,
                "processed_cases": batch.processed_cases,
                "progress_percentage": batch.progress_percentage,
                "started_at": batch.started_at,
                "priority": batch.priority,
                "use_auto_learning": batch.use_auto_learning
            })
        
        return {
            "active_batches": batch_list,
            "total_active": len(batch_list),
            "timestamp": datetime.utcnow()
        }
        
    except Exception as e:
        logger.error(f"Failed to get active batches: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get active batches: {str(e)}")


@router.get("/recent")
async def get_recent_batches(
    limit: int = Query(20, le=100),
    status_filter: Optional[str] = Query(None, description="Filter by status (completed, failed, etc.)"),
    db: Session = Depends(get_database_session)
):
    """
    Get list of recent batch processing jobs
    """
    try:
        from ..models.database import BatchProcessing
        
        query = db.query(BatchProcessing)
        
        if status_filter:
            if status_filter not in ["pending", "processing", "completed", "failed", "cancelled"]:
                raise HTTPException(status_code=400, detail="Invalid status filter")
            query = query.filter(BatchProcessing.status == status_filter)
        
        recent_batches = query.order_by(
            BatchProcessing.started_at.desc()
        ).limit(limit).all()
        
        batch_list = []
        for batch in recent_batches:
            duration = None
            if batch.started_at and batch.completed_at:
                duration = (batch.completed_at - batch.started_at).total_seconds()
            
            batch_list.append({
                "batch_id": batch.batch_id,
                "status": batch.status,
                "total_cases": batch.total_cases,
                "processed_cases": batch.processed_cases,
                "failed_cases": batch.failed_cases,
                "progress_percentage": batch.progress_percentage,
                "started_at": batch.started_at,
                "completed_at": batch.completed_at,
                "duration_seconds": duration,
                "priority": batch.priority,
                "use_auto_learning": batch.use_auto_learning,
                "error_message": batch.error_message
            })
        
        return {
            "recent_batches": batch_list,
            "total_returned": len(batch_list),
            "status_filter": status_filter,
            "timestamp": datetime.utcnow()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get recent batches: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get recent batches: {str(e)}")


@router.get("/system")
async def get_system_status():
    """
    Get overall system processing status and statistics
    """
    try:
        from ..models.database import BatchProcessing, CaseProcessingResult, SystemMetrics
        from sqlalchemy import func
        
        # This would require a database session, but we'll create one locally
        from ..core.database import db_manager
        
        with db_manager.get_session_context() as db:
            # Get batch statistics
            total_batches = db.query(BatchProcessing).count()
            active_batches = db.query(BatchProcessing).filter(
                BatchProcessing.status.in_(["pending", "processing"])
            ).count()
            completed_batches = db.query(BatchProcessing).filter(
                BatchProcessing.status == "completed"
            ).count()
            failed_batches = db.query(BatchProcessing).filter(
                BatchProcessing.status == "failed"
            ).count()
            
            # Get case statistics
            total_cases = db.query(CaseProcessingResult).count()
            completed_cases = db.query(CaseProcessingResult).filter(
                CaseProcessingResult.processing_status == "completed"
            ).count()
            
            # Get processing statistics
            dismissed_alerts = db.query(CaseProcessingResult).filter(
                CaseProcessingResult.final_recommendation == "DISMISS_ALERT"
            ).count()
            
            # Calculate average processing time
            avg_processing_time = db.query(
                func.avg(CaseProcessingResult.processing_time_ms)
            ).scalar() or 0
            
            return {
                "system_status": "operational",
                "timestamp": datetime.utcnow(),
                "batch_statistics": {
                    "total_batches": total_batches,
                    "active_batches": active_batches,
                    "completed_batches": completed_batches,
                    "failed_batches": failed_batches,
                    "success_rate_percentage": (
                        (completed_batches / total_batches) * 100 
                        if total_batches > 0 else 0
                    )
                },
                "case_statistics": {
                    "total_cases_processed": total_cases,
                    "completed_cases": completed_cases,
                    "dismissed_alerts": dismissed_alerts,
                    "overall_filter_rate_percentage": (
                        (dismissed_alerts / total_cases) * 100 
                        if total_cases > 0 else 0
                    )
                },
                "performance_metrics": {
                    "average_processing_time_ms": avg_processing_time,
                    "system_load": "normal",  # Would be calculated from actual metrics
                    "processing_capacity": "available"
                }
            }
        
    except Exception as e:
        logger.error(f"Failed to get system status: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get system status: {str(e)}")