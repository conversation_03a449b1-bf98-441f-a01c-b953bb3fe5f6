"""
API endpoints for VALO batch processing with auto-learning
"""

from fastapi import APIRouter, HTTPException, BackgroundTasks
from pydantic import BaseModel
from typing import Dict, Optional, List
import json
import redis
import asyncio
import logging
from datetime import datetime
import sys
import os

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from app.services.valo_batch_processor import VALOBatchProcessor

router = APIRouter(prefix="/api/batch", tags=["batch_processing"])
logger = logging.getLogger(__name__)

# Redis client for status tracking
try:
    redis_client = redis.Redis(host='localhost', port=6379, db=0, decode_responses=True)
    redis_client.ping()  # Test connection
    redis_available = True
except:
    # Fallback to in-memory storage if Redis is not available
    redis_client = None
    redis_available = False
    logger.warning("Redis not available, using in-memory storage")

# Global processor instance
processor_instance = None

# In-memory storage for when Redis is not available
in_memory_storage = {}


class BatchProcessingRequest(BaseModel):
    num_rounds: int = 3
    chunk_size: Optional[int] = 5
    test_mode: Optional[bool] = False  # For testing with smaller dataset


class BatchProcessingResponse(BaseModel):
    status: str
    message: str
    job_id: str


class ProcessingStatus(BaseModel):
    status: str
    round: Optional[int]
    chunk: Optional[int]
    total_chunks: Optional[int]
    cases_processed: Optional[int]
    total_cases: Optional[int]
    progress_percentage: Optional[float]
    current_stats: Optional[Dict]


class BatchResults(BaseModel):
    processing_summary: Dict
    business_impact: Dict
    round_improvements: List[Dict]
    learning_parameters: Dict


async def run_batch_processing_task(job_id: str, num_rounds: int, chunk_size: int, test_mode: bool):
    """Background task to run batch processing"""
    global processor_instance
    
    try:
        # Update status
        job_data = {
            "status": "running",
            "started_at": datetime.now().isoformat(),
            "num_rounds": num_rounds
        }
        
        if redis_available and redis_client:
            redis_client.setex(
                f"batch_job_{job_id}",
                3600,
                json.dumps(job_data)
            )
        else:
            in_memory_storage[f"batch_job_{job_id}"] = job_data
        
        # Create processor
        processor_instance = VALOBatchProcessor()
        processor_instance.chunk_size = chunk_size
        
        # If test mode, limit to 50 cases
        if test_mode:
            logger.info("Running in test mode with limited cases")
            processor_instance.all_cases = processor_instance.load_all_cases()[:50]
        
        # Run processing
        await processor_instance.run_batch_processing(num_rounds=num_rounds)
        
        # Update status to complete
        complete_data = {
            "status": "completed",
            "completed_at": datetime.now().isoformat(),
            "num_rounds": num_rounds,
            "results_available": True
        }
        
        if redis_available and redis_client:
            redis_client.setex(
                f"batch_job_{job_id}",
                3600,
                json.dumps(complete_data)
            )
        else:
            in_memory_storage[f"batch_job_{job_id}"] = complete_data
        
    except Exception as e:
        logger.error(f"Batch processing failed: {e}")
        error_data = {
            "status": "failed",
            "error": str(e),
            "failed_at": datetime.now().isoformat()
        }
        
        if redis_available and redis_client:
            redis_client.setex(
                f"batch_job_{job_id}",
                3600,
                json.dumps(error_data)
            )
        else:
            in_memory_storage[f"batch_job_{job_id}"] = error_data


@router.post("/start", response_model=BatchProcessingResponse)
async def start_batch_processing(
    request: BatchProcessingRequest,
    background_tasks: BackgroundTasks
):
    """Start batch processing of all VALO cases"""
    
    # Check if processing is already running
    if redis_available and redis_client:
        existing_jobs = redis_client.keys("batch_job_*")
        for job_key in existing_jobs:
            job_data = json.loads(redis_client.get(job_key) or '{}')
            if job_data.get('status') == 'running':
                raise HTTPException(
                    status_code=400,
                    detail="Batch processing is already running"
                )
    else:
        # Check in-memory storage
        for key, job_data in in_memory_storage.items():
            if key.startswith("batch_job_") and job_data.get('status') == 'running':
                raise HTTPException(
                    status_code=400,
                    detail="Batch processing is already running"
                )
    
    # Generate job ID
    job_id = f"valo_batch_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    # Start background task
    background_tasks.add_task(
        run_batch_processing_task,
        job_id,
        request.num_rounds,
        request.chunk_size or 5,
        request.test_mode or False
    )
    
    return BatchProcessingResponse(
        status="started",
        message=f"Batch processing started with {request.num_rounds} rounds",
        job_id=job_id
    )


@router.get("/status/{job_id}", response_model=ProcessingStatus)
async def get_processing_status(job_id: str):
    """Get current processing status"""
    
    # Get job status
    if redis_available and redis_client:
        job_data = redis_client.get(f"batch_job_{job_id}")
        if job_data:
            job_info = json.loads(job_data)
        else:
            job_info = None
    else:
        job_info = in_memory_storage.get(f"batch_job_{job_id}")
    
    if not job_info:
        raise HTTPException(status_code=404, detail="Job not found")
    
    # Get current progress
    if redis_available and redis_client:
        progress_data = redis_client.get("valo_processing_progress")
        progress = json.loads(progress_data) if progress_data else {}
    else:
        progress = in_memory_storage.get("valo_processing_progress", {})
        # Also check processor instance storage
        if processor_instance and hasattr(processor_instance, 'progress_storage'):
            progress = processor_instance.progress_storage.get("valo_processing_progress", progress)
    
    # Calculate percentage
    progress_pct = 0
    if progress.get('total_cases'):
        progress_pct = (progress.get('cases_processed', 0) / progress['total_cases']) * 100
    
    # Get current round stats if available
    current_stats = None
    if processor_instance and processor_instance.learning_params.get('round_improvements'):
        current_stats = processor_instance.learning_params['round_improvements'][-1]
    
    return ProcessingStatus(
        status=job_info.get('status', 'unknown'),
        round=progress.get('round'),
        chunk=progress.get('chunk'),
        total_chunks=progress.get('total_chunks'),
        cases_processed=progress.get('cases_processed'),
        total_cases=progress.get('total_cases'),
        progress_percentage=progress_pct,
        current_stats=current_stats
    )


@router.get("/results/latest", response_model=BatchResults)
async def get_latest_results():
    """Get latest batch processing results"""
    
    # Try to get from Redis first
    if redis_available and redis_client:
        results_data = redis_client.get("valo_batch_results")
        if results_data:
            results = json.loads(results_data)
            return BatchResults(
                processing_summary=results['processing_summary'],
                business_impact=results['business_impact'],
                round_improvements=results['round_improvements'],
                learning_parameters=results['learning_parameters']
            )
    else:
        # Check in-memory storage
        results = in_memory_storage.get("valo_batch_results")
        # Also check processor instance storage
        if not results and processor_instance and hasattr(processor_instance, 'progress_storage'):
            results = processor_instance.progress_storage.get("valo_batch_results")
        
        if results:
            return BatchResults(
                processing_summary=results['processing_summary'],
                business_impact=results['business_impact'],
                round_improvements=results['round_improvements'],
                learning_parameters=results['learning_parameters']
            )
    
    # If not in Redis, try to load from file
    import glob
    result_files = glob.glob("/home/<USER>/VALO_AI-FARM_2025/valo_batch_final_results_*.json")
    if not result_files:
        raise HTTPException(status_code=404, detail="No results available")
    
    # Get most recent file
    latest_file = max(result_files, key=os.path.getctime)
    with open(latest_file, 'r') as f:
        results = json.load(f)
    
    return BatchResults(
        processing_summary=results['processing_summary'],
        business_impact=results['business_impact'],
        round_improvements=results['round_improvements'],
        learning_parameters=results['learning_parameters']
    )


@router.get("/results/details")
async def get_detailed_results(
    limit: int = 100,
    offset: int = 0,
    filter_valid_only: bool = False,
    filter_errors_only: bool = False
):
    """Get detailed case-by-case results"""
    
    # Get results from Redis or file
    results_data = redis_client.get("valo_batch_results")
    if not results_data:
        import glob
        result_files = glob.glob("/home/<USER>/VALO_AI-FARM_2025/valo_batch_final_results_*.json")
        if not result_files:
            raise HTTPException(status_code=404, detail="No results available")
        
        latest_file = max(result_files, key=os.path.getctime)
        with open(latest_file, 'r') as f:
            results = json.load(f)
    else:
        results = json.loads(results_data)
    
    # Get detailed results
    detailed = results.get('detailed_results', [])
    
    # Apply filters
    if filter_valid_only:
        detailed = [r for r in detailed if not r.get('is_false_positive', True)]
    
    if filter_errors_only:
        detailed = [r for r in detailed if not r.get('correct_prediction', True)]
    
    # Apply pagination
    total = len(detailed)
    detailed = detailed[offset:offset + limit]
    
    return {
        "total": total,
        "limit": limit,
        "offset": offset,
        "results": detailed
    }


@router.delete("/jobs/{job_id}")
async def cancel_job(job_id: str):
    """Cancel a running job"""
    
    job_data = redis_client.get(f"batch_job_{job_id}")
    if not job_data:
        raise HTTPException(status_code=404, detail="Job not found")
    
    job_info = json.loads(job_data)
    if job_info.get('status') != 'running':
        raise HTTPException(
            status_code=400,
            detail=f"Job is not running (status: {job_info.get('status')})"
        )
    
    # Update status
    redis_client.setex(
        f"batch_job_{job_id}",
        3600,
        json.dumps({
            "status": "cancelled",
            "cancelled_at": datetime.now().isoformat()
        })
    )
    
    return {"message": "Job cancelled successfully"}


@router.get("/stats/summary")
async def get_processing_summary():
    """Get high-level summary of all processing runs"""
    
    # Get all job keys
    job_keys = redis_client.keys("batch_job_*")
    
    completed_jobs = []
    for key in job_keys:
        job_data = json.loads(redis_client.get(key) or '{}')
        if job_data.get('status') == 'completed':
            completed_jobs.append({
                'job_id': key.replace('batch_job_', ''),
                'completed_at': job_data.get('completed_at'),
                'num_rounds': job_data.get('num_rounds')
            })
    
    # Get latest results summary
    try:
        latest_results = await get_latest_results()
        summary = latest_results.processing_summary
        impact = latest_results.business_impact
    except:
        summary = {}
        impact = {}
    
    return {
        "completed_jobs": len(completed_jobs),
        "latest_results": {
            "valid_protection_rate": summary.get('valid_protection_rate', 0),
            "fp_detection_rate": summary.get('fp_detection_rate', 0),
            "annual_cost_savings": impact.get('annual_cost_savings', 0)
        },
        "jobs": completed_jobs[-5:]  # Last 5 jobs
    }