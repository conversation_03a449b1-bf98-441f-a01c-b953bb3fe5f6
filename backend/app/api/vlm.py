"""
VLM (Vision Language Model) API endpoints
"""

import asyncio
import tempfile
import os
from typing import Optional
from fastapi import APIRouter, File, Form, HTTPException, UploadFile
from fastapi.responses import JSONResponse

from ..utils.logging import get_logger
from ..services.vlm_service import vlm_service
from ..models.schemas import VLMAnalysisResult

logger = get_logger(__name__)

router = APIRouter()


@router.post("/analyze", response_model=VLMAnalysisResult)
async def analyze_image(
    image: UploadFile = File(..., description="Image file to analyze"),
    case_number: str = Form(..., description="Case number for tracking"),
    custom_prompt: Optional[str] = Form(None, description="Custom analysis prompt")
):
    """
    Analyze an image using the VLM service
    
    Args:
        image: Uploaded image file
        case_number: Case number for tracking purposes
        custom_prompt: Optional custom prompt for analysis
        
    Returns:
        VLMAnalysisResult: Analysis results including detection type, confidence, etc.
    """
    temp_file_path = None
    
    try:
        logger.info(f"Starting VLM analysis for case {case_number}")
        
        # Validate image file
        if not image.content_type or not image.content_type.startswith('image/'):
            raise HTTPException(
                status_code=400,
                detail=f"Invalid file type. Expected image, got {image.content_type}"
            )
        
        # Save uploaded file to temporary location
        with tempfile.NamedTemporaryFile(delete=False, suffix='.png') as temp_file:
            temp_file_path = temp_file.name
            content = await image.read()
            temp_file.write(content)
        
        logger.info(f"Saved uploaded image to {temp_file_path} ({len(content)} bytes)")
        
        # Analyze image using VLM service
        result = await vlm_service.analyze_image(
            image_path=temp_file_path,
            case_number=case_number,
            custom_prompt=custom_prompt
        )
        
        logger.info(f"VLM analysis completed for case {case_number}: {result.detection_type}")
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"VLM analysis failed for case {case_number}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"VLM analysis failed: {str(e)}"
        )
    finally:
        # Clean up temporary file
        if temp_file_path and os.path.exists(temp_file_path):
            try:
                os.unlink(temp_file_path)
                logger.debug(f"Cleaned up temporary file: {temp_file_path}")
            except Exception as e:
                logger.warning(f"Failed to clean up temporary file {temp_file_path}: {e}")


@router.get("/health")
async def vlm_health_check():
    """
    Check VLM service health and connectivity
    
    Returns:
        Dict: Health status information
    """
    try:
        logger.debug("Performing VLM health check")
        
        # Get health status from VLM service
        health_status = await asyncio.wait_for(
            vlm_service.health_check(),
            timeout=10.0
        )
        
        logger.debug(f"VLM health check result: {health_status['status']}")
        
        return JSONResponse(
            status_code=200 if health_status["status"] == "connected" else 503,
            content=health_status
        )
        
    except asyncio.TimeoutError:
        logger.warning("VLM health check timed out")
        return JSONResponse(
            status_code=503,
            content={
                "status": "timeout",
                "response_time_ms": None,
                "api_version": None,
                "error": "Health check timed out after 10 seconds"
            }
        )
    except Exception as e:
        logger.error(f"VLM health check failed: {str(e)}")
        return JSONResponse(
            status_code=503,
            content={
                "status": "error",
                "response_time_ms": None,
                "api_version": None,
                "error": str(e)
            }
        )


@router.get("/models")
async def get_vlm_models():
    """
    Get available VLM models
    
    Returns:
        Dict: Available models information
    """
    try:
        logger.debug("Fetching VLM models information")
        
        # This would typically call the VLM service to get model info
        # For now, return the configured model
        from ..core.config import settings
        
        models_info = {
            "models": [
                {
                    "id": settings.vlm_model_name,
                    "name": settings.vlm_model_name,
                    "description": "Vision Language Model for image analysis",
                    "capabilities": [
                        "image_analysis",
                        "safety_detection",
                        "false_positive_detection"
                    ]
                }
            ],
            "default_model": settings.vlm_model_name,
            "api_base_url": settings.vlm_api_base_url
        }
        
        logger.debug(f"Returning VLM models info: {len(models_info['models'])} models")
        
        return models_info
        
    except Exception as e:
        logger.error(f"Failed to get VLM models: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get VLM models: {str(e)}"
        )


@router.get("/config")
async def get_vlm_config():
    """
    Get VLM service configuration (non-sensitive information only)
    
    Returns:
        Dict: VLM configuration information
    """
    try:
        from ..core.config import settings
        
        config_info = {
            "model_name": settings.vlm_model_name,
            "max_tokens": settings.vlm_max_tokens,
            "temperature": settings.vlm_temperature,
            "timeout_seconds": settings.vlm_timeout_seconds,
            "max_concurrent_requests": settings.max_concurrent_requests,
            "api_base_url": settings.vlm_api_base_url,
            "has_api_key": bool(settings.vlm_api_key)
        }
        
        logger.debug("Returning VLM configuration info")
        
        return config_info
        
    except Exception as e:
        logger.error(f"Failed to get VLM config: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get VLM config: {str(e)}"
        )
