"""
AI-FARM Data Models
Pydantic schemas for API requests, responses, and data validation
"""

from pydantic import BaseModel, Field, validator
from typing import Optional, List, Dict, Any, Literal
from datetime import datetime
from enum import Enum


class ValidationStatus(str, Enum):
    """Validation status for safety violations"""
    VALID = "valid"
    INVALID = "invalid"
    INVALID_FALSE_POSITIVE = "invalid_false_positive"
    UNKNOWN = "unknown"


class ImageType(str, Enum):
    """Type of violation image"""
    SOURCE = "source"
    CROPPED = "cropped"


class ProcessingStatus(str, Enum):
    """Processing status for cases"""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"


class VLMAnalysisResult(BaseModel):
    """VLM analysis result schema"""
    detection_type: str = Field(..., description="Type of detection (HUMAN_DETECTED, STRUCTURE_MISIDENTIFIED, etc.)")
    false_positive_likelihood: int = Field(..., ge=0, le=100, description="Likelihood of false positive (0-100)")
    true_violation_likelihood: int = Field(..., ge=0, le=100, description="Likelihood of true violation (0-100)")
    reasoning: str = Field(..., description="VLM reasoning for the decision")
    recommendation: Literal["DISMISS_ALERT", "REQUIRES_REVIEW"] = Field(..., description="Processing recommendation")
    confidence_score: float = Field(..., ge=0.0, le=1.0, description="Overall confidence score")
    processing_time_ms: Optional[int] = Field(None, description="Processing time in milliseconds")
    
    @validator('false_positive_likelihood', 'true_violation_likelihood')
    def validate_likelihood_sum(cls, v, values):
        """Ensure likelihoods are reasonable"""
        if 'false_positive_likelihood' in values:
            total = v + values['false_positive_likelihood']
            if total > 100:
                raise ValueError("Combined likelihoods cannot exceed 100%")
        return v


class CaseData(BaseModel):
    """Case data from CSV input"""
    pk_event: int = Field(..., description="Primary key event ID")
    case_number: str = Field(..., description="Customer case number")
    image_url: str = Field(..., description="Path to violation image")
    validation_status: ValidationStatus = Field(..., description="Human validation result")
    
    @validator('case_number')
    def validate_case_number(cls, v):
        """Validate case number format"""
        if not v.startswith('V125'):
            raise ValueError("Case number must start with V125")
        return v


class ProcessingRequest(BaseModel):
    """Request to process violation images"""
    case_numbers: List[str] = Field(..., description="List of case numbers to process")
    use_auto_learning: bool = Field(default=True, description="Enable auto-learning features")
    custom_thresholds: Optional[Dict[str, int]] = Field(None, description="Custom confidence thresholds")
    priority: Literal["low", "normal", "high"] = Field(default="normal", description="Processing priority")


class ProcessingResult(BaseModel):
    """Result of processing a single case"""
    case_number: str = Field(..., description="Case number")
    pk_event: int = Field(..., description="Primary key event ID")
    images_processed: List[Dict[str, Any]] = Field(..., description="List of processed images")
    vlm_results: List[VLMAnalysisResult] = Field(..., description="VLM analysis results")
    final_recommendation: str = Field(..., description="Final processing recommendation")
    processing_status: ProcessingStatus = Field(..., description="Processing status")
    processing_time_total_ms: int = Field(..., description="Total processing time")
    error_message: Optional[str] = Field(None, description="Error message if processing failed")


class BatchProcessingResponse(BaseModel):
    """Response for batch processing request"""
    batch_id: str = Field(..., description="Unique batch identifier")
    total_cases: int = Field(..., description="Total number of cases to process")
    status: ProcessingStatus = Field(..., description="Batch processing status")
    results: List[ProcessingResult] = Field(default_factory=list, description="Processing results")
    summary: Dict[str, Any] = Field(..., description="Processing summary statistics")
    started_at: datetime = Field(..., description="Processing start time")
    completed_at: Optional[datetime] = Field(None, description="Processing completion time")


class DemoMetrics(BaseModel):
    """Demo metrics for customer presentation"""
    total_alerts_processed: int = Field(..., description="Total alerts processed")
    false_positives_filtered: int = Field(..., description="False positives filtered out")
    alerts_requiring_review: int = Field(..., description="Alerts requiring human review")
    filter_rate_percentage: float = Field(..., description="False positive filter rate")
    time_saved_hours: float = Field(..., description="Estimated time saved in hours")
    cost_savings_annual: float = Field(..., description="Estimated annual cost savings")
    processing_time_avg_ms: float = Field(..., description="Average processing time per image")
    confidence_score_avg: float = Field(..., description="Average confidence score")


class AutoLearningInsights(BaseModel):
    """Auto-learning insights for customer-specific optimization"""
    detected_patterns: Dict[str, Any] = Field(..., description="Detected patterns in customer data")
    optimized_thresholds: Dict[str, int] = Field(..., description="Optimized confidence thresholds")
    accuracy_improvement: float = Field(..., description="Accuracy improvement percentage")
    confidence_calibration: Dict[str, float] = Field(..., description="Confidence calibration data")
    recommendations: List[str] = Field(..., description="System improvement recommendations")


class CustomerEnvironmentAnalysis(BaseModel):
    """Analysis of customer's specific environment"""
    unique_structures: List[str] = Field(..., description="Unique structures detected")
    common_false_positive_patterns: List[str] = Field(..., description="Common false positive patterns")
    lighting_conditions: str = Field(..., description="Dominant lighting conditions")
    camera_angles: List[str] = Field(..., description="Common camera angles")
    equipment_types: List[str] = Field(..., description="Equipment types identified")
    optimization_opportunities: List[str] = Field(..., description="Optimization opportunities")


class HealthCheckResponse(BaseModel):
    """Health check response"""
    status: Literal["healthy", "unhealthy"] = Field(..., description="Service health status")
    timestamp: datetime = Field(..., description="Health check timestamp")
    version: str = Field(..., description="Application version")
    vlm_api_status: Literal["connected", "disconnected", "error"] = Field(..., description="VLM API connection status")
    database_status: Literal["connected", "disconnected", "error"] = Field(..., description="Database connection status")
    redis_status: Literal["connected", "disabled", "error"] = Field(..., description="Redis cache connection status")
    uptime_seconds: int = Field(..., description="Service uptime in seconds")


class ErrorResponse(BaseModel):
    """Error response schema"""
    error: str = Field(..., description="Error type")
    message: str = Field(..., description="Error message")
    details: Optional[Dict[str, Any]] = Field(None, description="Additional error details")
    timestamp: datetime = Field(default_factory=datetime.now, description="Error timestamp")
    request_id: Optional[str] = Field(None, description="Request identifier for tracking")