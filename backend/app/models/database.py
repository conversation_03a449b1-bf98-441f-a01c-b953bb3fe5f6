"""
AI-FARM Database Models
SQLAlchemy models for data persistence
"""

from sqlalchemy import Column, Integer, String, Float, DateTime, Boolean, Text, JSON, ForeignKey
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from datetime import datetime
from typing import Optional

Base = declarative_base()


class BatchProcessing(Base):
    """Batch processing tracking table"""
    __tablename__ = "batch_processing"
    
    id = Column(Integer, primary_key=True, index=True)
    batch_id = Column(String(50), unique=True, index=True, nullable=False)
    total_cases = Column(Integer, nullable=False)
    processed_cases = Column(Integer, default=0)
    failed_cases = Column(Integer, default=0)
    status = Column(String(20), default="pending")  # pending, processing, completed, failed
    started_at = Column(DateTime(timezone=True), server_default=func.now())
    completed_at = Column(DateTime(timezone=True), nullable=True)
    error_message = Column(Text, nullable=True)
    
    # Processing configuration
    use_auto_learning = Column(Boolean, default=True)
    custom_thresholds = Column(JSON, nullable=True)
    priority = Column(String(10), default="normal")
    
    # Relationships
    case_results = relationship("CaseProcessingResult", back_populates="batch")
    
    @property
    def progress_percentage(self) -> float:
        """Calculate processing progress percentage"""
        if self.total_cases == 0:
            return 0.0
        return (self.processed_cases / self.total_cases) * 100


class CaseProcessingResult(Base):
    """Individual case processing results"""
    __tablename__ = "case_processing_results"
    
    id = Column(Integer, primary_key=True, index=True)
    batch_id = Column(String(50), ForeignKey("batch_processing.batch_id"), nullable=False)
    case_number = Column(String(50), nullable=False, index=True)
    pk_event = Column(Integer, nullable=False, index=True)
    
    # Original data
    image_url = Column(String(500), nullable=False)
    validation_status = Column(String(50), nullable=False)  # valid, invalid, invalid_false_positive
    
    # Processing results
    processing_status = Column(String(20), default="pending")  # pending, processing, completed, failed
    final_recommendation = Column(String(50), nullable=True)  # DISMISS_ALERT, REQUIRES_REVIEW
    processing_time_ms = Column(Integer, nullable=True)
    error_message = Column(Text, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    processed_at = Column(DateTime(timezone=True), nullable=True)
    
    # Relationships
    batch = relationship("BatchProcessing", back_populates="case_results")
    vlm_results = relationship("VLMAnalysisResult", back_populates="case_result")
    images = relationship("ProcessedImage", back_populates="case_result")


class VLMAnalysisResult(Base):
    """VLM analysis results for each case"""
    __tablename__ = "vlm_analysis_results"
    
    id = Column(Integer, primary_key=True, index=True)
    case_result_id = Column(Integer, ForeignKey("case_processing_results.id"), nullable=False)
    image_id = Column(Integer, ForeignKey("processed_images.id"), nullable=True)
    
    # VLM response data
    detection_type = Column(String(100), nullable=False)
    false_positive_likelihood = Column(Integer, nullable=False)
    true_violation_likelihood = Column(Integer, nullable=False)
    reasoning = Column(Text, nullable=False)
    recommendation = Column(String(50), nullable=False)  # DISMISS_ALERT, REQUIRES_REVIEW
    confidence_score = Column(Float, nullable=False)
    processing_time_ms = Column(Integer, nullable=True)
    
    # API details
    vlm_model_used = Column(String(50), nullable=True)
    api_response_raw = Column(JSON, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    case_result = relationship("CaseProcessingResult", back_populates="vlm_results")
    processed_image = relationship("ProcessedImage", back_populates="vlm_results")


class ProcessedImage(Base):
    """Images processed during analysis"""
    __tablename__ = "processed_images"
    
    id = Column(Integer, primary_key=True, index=True)
    case_result_id = Column(Integer, ForeignKey("case_processing_results.id"), nullable=False)
    
    # Image details
    original_path = Column(String(500), nullable=False)
    image_type = Column(String(20), default="source")  # source, cropped
    file_size_bytes = Column(Integer, nullable=True)
    image_width = Column(Integer, nullable=True)
    image_height = Column(Integer, nullable=True)
    
    # Processing details
    processed_at = Column(DateTime(timezone=True), server_default=func.now())
    processing_successful = Column(Boolean, default=False)
    error_message = Column(Text, nullable=True)
    
    # Relationships
    case_result = relationship("CaseProcessingResult", back_populates="images")
    vlm_results = relationship("VLMAnalysisResult", back_populates="processed_image")


class AutoLearningMetrics(Base):
    """Auto-learning system metrics and improvements"""
    __tablename__ = "auto_learning_metrics"
    
    id = Column(Integer, primary_key=True, index=True)
    batch_id = Column(String(50), ForeignKey("batch_processing.batch_id"), nullable=False)
    
    # Pattern detection results
    detected_patterns = Column(JSON, nullable=True)
    common_structures = Column(JSON, nullable=True)
    false_positive_patterns = Column(JSON, nullable=True)
    
    # Threshold optimization
    original_thresholds = Column(JSON, nullable=False)
    optimized_thresholds = Column(JSON, nullable=False)
    accuracy_improvement = Column(Float, default=0.0)
    
    # Environment analysis
    customer_environment = Column(JSON, nullable=True)
    optimization_recommendations = Column(JSON, nullable=True)
    
    # Performance metrics
    confidence_calibration = Column(JSON, nullable=True)
    processing_improvements = Column(JSON, nullable=True)
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())


class DemoSession(Base):
    """Demo session tracking for customer presentations"""
    __tablename__ = "demo_sessions"
    
    id = Column(Integer, primary_key=True, index=True)
    session_id = Column(String(50), unique=True, index=True, nullable=False)
    customer_name = Column(String(200), nullable=True)
    
    # Demo configuration
    demo_data_source = Column(String(100), nullable=True)  # customer_upload, sample_data
    total_images_processed = Column(Integer, default=0)
    
    # Demo results
    false_positives_filtered = Column(Integer, default=0)
    alerts_requiring_review = Column(Integer, default=0)
    filter_rate_percentage = Column(Float, default=0.0)
    
    # ROI calculations
    time_saved_hours = Column(Float, default=0.0)
    cost_savings_annual = Column(Float, default=0.0)
    processing_time_avg_ms = Column(Float, default=0.0)
    
    # Session tracking
    started_at = Column(DateTime(timezone=True), server_default=func.now())
    completed_at = Column(DateTime(timezone=True), nullable=True)
    demo_status = Column(String(20), default="active")  # active, completed, abandoned
    
    # Customer data handling
    customer_data_cleaned = Column(Boolean, default=False)
    cleanup_scheduled_at = Column(DateTime(timezone=True), nullable=True)


class SystemMetrics(Base):
    """System performance and usage metrics"""
    __tablename__ = "system_metrics"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # Performance metrics
    total_requests_processed = Column(Integer, default=0)
    total_images_analyzed = Column(Integer, default=0)
    total_processing_time_ms = Column(Integer, default=0)
    average_response_time_ms = Column(Float, default=0.0)
    
    # VLM API metrics
    vlm_api_calls_total = Column(Integer, default=0)
    vlm_api_calls_successful = Column(Integer, default=0)
    vlm_api_calls_failed = Column(Integer, default=0)
    vlm_api_avg_response_time_ms = Column(Float, default=0.0)
    
    # Error tracking
    total_errors = Column(Integer, default=0)
    processing_errors = Column(Integer, default=0)
    api_errors = Column(Integer, default=0)
    
    # System health
    uptime_seconds = Column(Integer, default=0)
    memory_usage_mb = Column(Float, default=0.0)
    cpu_usage_percentage = Column(Float, default=0.0)
    
    # Timestamp
    recorded_at = Column(DateTime(timezone=True), server_default=func.now())


class CustomerDataCleanup(Base):
    """Track customer data cleanup tasks"""
    __tablename__ = "customer_data_cleanup"
    
    id = Column(Integer, primary_key=True, index=True)
    session_id = Column(String(50), nullable=False)
    cleanup_type = Column(String(50), nullable=False)  # temp_files, uploaded_images, processing_results
    file_path = Column(String(500), nullable=True)
    
    scheduled_at = Column(DateTime(timezone=True), nullable=False)
    executed_at = Column(DateTime(timezone=True), nullable=True)
    status = Column(String(20), default="pending")  # pending, completed, failed
    error_message = Column(Text, nullable=True)
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())