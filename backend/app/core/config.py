"""
AI-FARM Configuration Management
Handles all environment variables and application settings
"""

from pydantic_settings import BaseSettings
from pydantic import Field
from typing import Optional
from pathlib import Path


class Settings(BaseSettings):
    """Application settings loaded from environment variables"""
    
    # VLM API Configuration
    vlm_api_base_url: str = Field(default="https://api.openai.com", description="VLM API base URL")
    vlm_api_key: str = Field(default="", description="VLM API key")
    vlm_model_name: str = Field(default="gpt-4.1-2025-04-14", description="VLM model name")
    vlm_max_tokens: int = Field(default=1000, description="Maximum tokens for VLM response")
    vlm_temperature: float = Field(default=0.1, description="VLM temperature setting")
    vlm_timeout_seconds: int = Field(default=30, description="VLM API timeout in seconds")
    
    # Processing Configuration
    batch_size: int = Field(default=10, description="Batch size for processing")
    max_concurrent_requests: int = Field(default=3, description="Maximum concurrent VLM requests")
    processing_timeout_minutes: int = Field(default=60, description="Processing timeout in minutes")
    image_max_size_mb: int = Field(default=10, description="Maximum image size in MB")
    
    # Confidence Thresholds
    threshold_structure_misid: int = Field(default=70, description="Threshold for structure misidentification")
    threshold_proper_ppe: int = Field(default=65, description="Threshold for proper PPE detection")
    threshold_no_violation: int = Field(default=75, description="Threshold for no violation cases")
    threshold_default: int = Field(default=70, description="Default confidence threshold")
    
    # Auto-Learning Configuration
    enable_auto_learning: bool = Field(default=True, description="Enable auto-learning features")
    learning_batch_size: int = Field(default=50, description="Batch size for learning processes")
    confidence_calibration_enabled: bool = Field(default=True, description="Enable confidence calibration")
    pattern_detection_enabled: bool = Field(default=True, description="Enable pattern detection")
    
    # Database Configuration
    database_url: str = Field(default="sqlite:///./ai_farm.db", description="Database connection URL")
    database_echo: bool = Field(default=False, description="Enable SQLAlchemy echo")

    # Redis Configuration
    redis_url: str = Field(default="redis://localhost:6379/0", description="Redis connection URL")
    redis_enabled: bool = Field(default=True, description="Enable Redis caching")
    redis_cache_ttl: int = Field(default=3600, description="Default cache TTL in seconds")
    redis_session_ttl: int = Field(default=86400, description="Session TTL in seconds (24 hours)")
    redis_max_connections: int = Field(default=20, description="Maximum Redis connections")
    
    # Server Configuration
    host: str = Field(default="0.0.0.0", description="Server host")
    port: int = Field(default=8001, description="Server port")
    debug: bool = Field(default=False, description="Debug mode")
    log_level: str = Field(default="INFO", description="Logging level")
    
    # Demo Configuration
    demo_data_path: Path = Field(default=Path("./data"), description="Demo data directory")
    demo_images_path: Path = Field(default=Path("./data/images"), description="Demo images directory")
    temp_upload_path: Path = Field(default=Path("/tmp/ai_farm_uploads"), description="Temporary upload directory")
    cleanup_temp_files: bool = Field(default=True, description="Cleanup temporary files")
    
    # Storage Paths
    processed_images_path: Path = Field(default=Path("./data/processed"), description="Processed images directory")
    logs_path: Path = Field(default=Path("./logs"), description="Logs directory")
    export_path: Path = Field(default=Path("./exports"), description="Export directory")
    
    # Customer Data Processing
    customer_data_retention_hours: int = Field(default=24, description="Customer data retention in hours")
    enable_customer_data_cleanup: bool = Field(default=True, description="Enable automatic customer data cleanup")
    secure_temp_storage: bool = Field(default=True, description="Use secure temporary storage")
    
    # Performance Settings
    worker_processes: int = Field(default=4, description="Number of worker processes")
    keep_alive_timeout: int = Field(default=65, description="Keep alive timeout")
    max_request_size_mb: int = Field(default=1024, description="Maximum request size in MB")
    upload_timeout_seconds: int = Field(default=300, description="Upload timeout in seconds (5 minutes)")
    
    # Monitoring
    enable_metrics: bool = Field(default=True, description="Enable metrics collection")
    metrics_port: int = Field(default=9090, description="Metrics server port")
    enable_health_check: bool = Field(default=True, description="Enable health check endpoint")
    
    class Config:
        env_file = "../.env"  # Look for .env file in parent directory (project root)
        env_file_encoding = "utf-8"
        case_sensitive = False
        extra = "ignore"  # Ignore extra environment variables not defined in the model
        
    def create_directories(self) -> None:
        """Create necessary directories if they don't exist"""
        directories = [
            self.demo_data_path,
            self.demo_images_path,
            self.temp_upload_path,
            self.processed_images_path,
            self.logs_path,
            self.export_path
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
    
    @property
    def vlm_api_headers(self) -> dict:
        """Get headers for VLM API requests"""
        headers = {
            "Content-Type": "application/json"
        }
        if self.vlm_api_key:
            headers["Authorization"] = f"Bearer {self.vlm_api_key}"
        return headers
    
    def get_threshold_for_category(self, category: str) -> int:
        """Get confidence threshold for specific category"""
        thresholds = {
            "structure_misid": self.threshold_structure_misid,
            "proper_ppe": self.threshold_proper_ppe,
            "no_violation": self.threshold_no_violation
        }
        return thresholds.get(category, self.threshold_default)


# Global settings instance
settings = Settings()