"""
AI-FARM Database Session Management
Handles database connections, sessions, and operations
"""

from sqlalchemy import create_engine, event, text
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import StaticPool
from contextlib import contextmanager
from typing import Generator, Optional
import logging

from .config import settings
from ..models.database import Base

logger = logging.getLogger(__name__)


class DatabaseManager:
    """Database connection and session management"""
    
    def __init__(self):
        self.engine = None
        self.SessionLocal = None
        self._initialize_database()
    
    def _initialize_database(self) -> None:
        """Initialize database engine and session factory"""
        try:
            # Create database engine
            if settings.database_url.startswith("sqlite"):
                # SQLite specific configuration
                self.engine = create_engine(
                    settings.database_url,
                    echo=settings.database_echo,
                    poolclass=StaticPool,
                    connect_args={
                        "check_same_thread": False,
                        "timeout": 30,
                        "isolation_level": None
                    }
                )
                
                # Enable foreign key support for SQLite
                @event.listens_for(self.engine, "connect")
                def set_sqlite_pragma(dbapi_connection, connection_record):
                    cursor = dbapi_connection.cursor()
                    cursor.execute("PRAGMA foreign_keys=ON")
                    cursor.execute("PRAGMA journal_mode=WAL")
                    cursor.execute("PRAGMA synchronous=NORMAL")
                    cursor.execute("PRAGMA cache_size=1000")
                    cursor.execute("PRAGMA temp_store=MEMORY")
                    cursor.close()
            else:
                # PostgreSQL or other database configuration
                self.engine = create_engine(
                    settings.database_url,
                    echo=settings.database_echo,
                    pool_pre_ping=True,
                    pool_recycle=3600
                )
            
            # Create session factory
            self.SessionLocal = sessionmaker(
                autocommit=False,
                autoflush=False,
                bind=self.engine
            )
            
            logger.info(f"Database initialized successfully: {settings.database_url}")
            
        except Exception as e:
            logger.error(f"Failed to initialize database: {e}")
            raise
    
    def create_tables(self) -> None:
        """Create all database tables"""
        try:
            Base.metadata.create_all(bind=self.engine)
            logger.info("Database tables created successfully")
        except Exception as e:
            logger.error(f"Failed to create database tables: {e}")
            raise
    
    def get_session(self) -> Session:
        """Get a database session"""
        if not self.SessionLocal:
            raise RuntimeError("Database not initialized")
        return self.SessionLocal()
    
    @contextmanager
    def get_session_context(self) -> Generator[Session, None, None]:
        """Get a database session with automatic cleanup"""
        session = self.get_session()
        try:
            yield session
            session.commit()
        except Exception as e:
            session.rollback()
            logger.error(f"Database session error: {e}")
            raise
        finally:
            session.close()
    
    def test_connection(self) -> bool:
        """Test database connection"""
        try:
            with self.get_session_context() as session:
                session.execute(text("SELECT 1"))
            return True
        except Exception as e:
            logger.error(f"Database connection test failed: {e}")
            return False
    
    def get_health_status(self) -> dict:
        """Get database health status"""
        try:
            with self.get_session_context() as session:
                result = session.execute(text("SELECT 1")).scalar()
                return {
                    "status": "connected" if result == 1 else "error",
                    "connection_successful": True,
                    "error": None
                }
        except Exception as e:
            return {
                "status": "error",
                "connection_successful": False,
                "error": str(e)
            }
    
    def cleanup_old_sessions(self, hours_old: int = 24) -> int:
        """Clean up old demo sessions and related data"""
        try:
            from ..models.database import DemoSession, CustomerDataCleanup
            from datetime import datetime, timedelta
            
            cutoff_time = datetime.utcnow() - timedelta(hours=hours_old)
            
            with self.get_session_context() as session:
                # Find old sessions
                old_sessions = session.query(DemoSession).filter(
                    DemoSession.started_at < cutoff_time,
                    DemoSession.customer_data_cleaned == False
                ).all()
                
                cleaned_count = 0
                for demo_session in old_sessions:
                    # Mark as cleaned
                    demo_session.customer_data_cleaned = True
                    demo_session.cleanup_scheduled_at = datetime.utcnow()
                    
                    # Create cleanup tasks
                    cleanup_task = CustomerDataCleanup(
                        session_id=demo_session.session_id,
                        cleanup_type="session_data",
                        scheduled_at=datetime.utcnow(),
                        status="pending"
                    )
                    session.add(cleanup_task)
                    cleaned_count += 1
                
                session.commit()
                logger.info(f"Scheduled cleanup for {cleaned_count} old demo sessions")
                return cleaned_count
                
        except Exception as e:
            logger.error(f"Failed to cleanup old sessions: {e}")
            return 0


# Global database manager instance
db_manager = DatabaseManager()


def get_database_session() -> Generator[Session, None, None]:
    """
    Dependency function to get database session for FastAPI
    Use this in FastAPI route dependencies
    """
    with db_manager.get_session_context() as session:
        yield session


def init_database() -> None:
    """Initialize database tables and perform startup tasks"""
    try:
        db_manager.create_tables()
        
        # Test connection
        if not db_manager.test_connection():
            raise RuntimeError("Database connection test failed")
        
        logger.info("Database initialization completed successfully")
        
    except Exception as e:
        logger.error(f"Database initialization failed: {e}")
        raise


def cleanup_database() -> None:
    """Cleanup database resources on shutdown"""
    try:
        if db_manager.engine:
            db_manager.engine.dispose()
        logger.info("Database cleanup completed")
    except Exception as e:
        logger.error(f"Database cleanup error: {e}")


class DatabaseOperations:
    """High-level database operations for the application"""
    
    @staticmethod
    def create_batch_processing_record(batch_id: str, total_cases: int, 
                                     use_auto_learning: bool = True,
                                     custom_thresholds: Optional[dict] = None,
                                     priority: str = "normal") -> str:
        """Create a new batch processing record"""
        from ..models.database import BatchProcessing
        
        try:
            with db_manager.get_session_context() as session:
                batch_record = BatchProcessing(
                    batch_id=batch_id,
                    total_cases=total_cases,
                    use_auto_learning=use_auto_learning,
                    custom_thresholds=custom_thresholds,
                    priority=priority,
                    status="pending"
                )
                session.add(batch_record)
                session.commit()
                
                logger.info(f"Created batch processing record: {batch_id}")
                return batch_id
                
        except Exception as e:
            logger.error(f"Failed to create batch processing record: {e}")
            raise
    
    @staticmethod
    def update_batch_status(batch_id: str, status: str, 
                          error_message: Optional[str] = None) -> bool:
        """Update batch processing status"""
        from ..models.database import BatchProcessing
        from datetime import datetime
        
        try:
            with db_manager.get_session_context() as session:
                batch_record = session.query(BatchProcessing).filter(
                    BatchProcessing.batch_id == batch_id
                ).first()
                
                if not batch_record:
                    logger.error(f"Batch record not found: {batch_id}")
                    return False
                
                batch_record.status = status
                if error_message:
                    batch_record.error_message = error_message
                
                if status in ["completed", "failed"]:
                    batch_record.completed_at = datetime.utcnow()
                
                session.commit()
                logger.info(f"Updated batch status: {batch_id} -> {status}")
                return True
                
        except Exception as e:
            logger.error(f"Failed to update batch status: {e}")
            return False
    
    @staticmethod
    def get_batch_progress(batch_id: str) -> Optional[dict]:
        """Get batch processing progress"""
        from ..models.database import BatchProcessing
        
        try:
            with db_manager.get_session_context() as session:
                batch_record = session.query(BatchProcessing).filter(
                    BatchProcessing.batch_id == batch_id
                ).first()
                
                if not batch_record:
                    return None
                
                return {
                    "batch_id": batch_record.batch_id,
                    "total_cases": batch_record.total_cases,
                    "processed_cases": batch_record.processed_cases,
                    "failed_cases": batch_record.failed_cases,
                    "progress_percentage": batch_record.progress_percentage,
                    "status": batch_record.status,
                    "started_at": batch_record.started_at,
                    "completed_at": batch_record.completed_at,
                    "error_message": batch_record.error_message
                }
                
        except Exception as e:
            logger.error(f"Failed to get batch progress: {e}")
            return None
    
    @staticmethod
    def record_case_result(batch_id: str, case_number: str, pk_event: int,
                          image_url: str, validation_status: str,
                          vlm_results: list, final_recommendation: str,
                          processing_time_ms: int) -> bool:
        """Record processing result for a case"""
        from ..models.database import CaseProcessingResult, VLMAnalysisResult
        from datetime import datetime
        
        try:
            with db_manager.get_session_context() as session:
                # Create case result record
                case_result = CaseProcessingResult(
                    batch_id=batch_id,
                    case_number=case_number,
                    pk_event=pk_event,
                    image_url=image_url,
                    validation_status=validation_status,
                    final_recommendation=final_recommendation,
                    processing_time_ms=processing_time_ms,
                    processing_status="completed",
                    processed_at=datetime.utcnow()
                )
                session.add(case_result)
                session.flush()  # Get the ID
                
                # Add VLM results
                for vlm_result in vlm_results:
                    vlm_record = VLMAnalysisResult(
                        case_result_id=case_result.id,
                        detection_type=vlm_result.get("detection_type", ""),
                        false_positive_likelihood=vlm_result.get("false_positive_likelihood", 0),
                        true_violation_likelihood=vlm_result.get("true_violation_likelihood", 0),
                        reasoning=vlm_result.get("reasoning", ""),
                        recommendation=vlm_result.get("recommendation", ""),
                        confidence_score=vlm_result.get("confidence_score", 0.0),
                        processing_time_ms=vlm_result.get("processing_time_ms"),
                        vlm_model_used=vlm_result.get("model_used"),
                        api_response_raw=vlm_result
                    )
                    session.add(vlm_record)
                
                # Update batch progress
                batch_record = session.query(BatchProcessing).filter(
                    BatchProcessing.batch_id == batch_id
                ).first()
                if batch_record:
                    batch_record.processed_cases += 1
                
                session.commit()
                logger.info(f"Recorded case result: {case_number}")
                return True
                
        except Exception as e:
            logger.error(f"Failed to record case result: {e}")
            return False