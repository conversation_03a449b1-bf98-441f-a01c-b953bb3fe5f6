"""
AI-FARM Logging Configuration and Utilities
Enhanced logging with structured output and error tracking
"""

import logging
import logging.handlers
import json
import sys
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional

from ..core.config import settings


class JSONFormatter(logging.Formatter):
    """JSON formatter for structured logging"""
    
    def format(self, record: logging.LogRecord) -> str:
        """Format log record as JSON"""
        log_entry = {
            "timestamp": datetime.utcnow().isoformat() + "Z",
            "level": record.levelname,
            "logger": record.name,
            "message": record.getMessage(),
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno
        }
        
        # Add exception information if present
        if record.exc_info:
            log_entry["exception"] = self.formatException(record.exc_info)
        
        # Add extra fields if present
        if hasattr(record, "request_id"):
            log_entry["request_id"] = record.request_id
        
        if hasattr(record, "batch_id"):
            log_entry["batch_id"] = record.batch_id
        
        if hasattr(record, "case_number"):
            log_entry["case_number"] = record.case_number
        
        return json.dumps(log_entry)


class AIFarmLogger:
    """Enhanced logger for AI-FARM with context tracking"""
    
    def __init__(self, name: str):
        self.logger = logging.getLogger(name)
        self.context: Dict[str, Any] = {}
    
    def set_context(self, **kwargs):
        """Set logging context for subsequent log messages"""
        self.context.update(kwargs)
    
    def clear_context(self):
        """Clear logging context"""
        self.context.clear()
    
    def _log_with_context(self, level: int, message: str, **kwargs):
        """Log message with context"""
        extra = {**self.context, **kwargs}
        self.logger.log(level, message, extra=extra)
    
    def debug(self, message: str, **kwargs):
        """Log debug message"""
        self._log_with_context(logging.DEBUG, message, **kwargs)
    
    def info(self, message: str, **kwargs):
        """Log info message"""
        self._log_with_context(logging.INFO, message, **kwargs)
    
    def warning(self, message: str, **kwargs):
        """Log warning message"""
        self._log_with_context(logging.WARNING, message, **kwargs)
    
    def error(self, message: str, **kwargs):
        """Log error message"""
        self._log_with_context(logging.ERROR, message, **kwargs)
    
    def critical(self, message: str, **kwargs):
        """Log critical message"""
        self._log_with_context(logging.CRITICAL, message, **kwargs)
    
    def batch_started(self, batch_id: str, total_cases: int):
        """Log batch processing start"""
        self.info(
            f"Batch processing started: {batch_id}",
            batch_id=batch_id,
            total_cases=total_cases,
            event_type="batch_started"
        )
    
    def batch_completed(self, batch_id: str, processed_cases: int, failed_cases: int, duration_seconds: float):
        """Log batch processing completion"""
        self.info(
            f"Batch processing completed: {batch_id}",
            batch_id=batch_id,
            processed_cases=processed_cases,
            failed_cases=failed_cases,
            duration_seconds=duration_seconds,
            event_type="batch_completed"
        )
    
    def case_processed(self, case_number: str, recommendation: str, processing_time_ms: int):
        """Log case processing completion"""
        self.info(
            f"Case processed: {case_number}",
            case_number=case_number,
            recommendation=recommendation,
            processing_time_ms=processing_time_ms,
            event_type="case_processed"
        )
    
    def vlm_api_call(self, case_number: str, response_time_ms: int, success: bool, error: Optional[str] = None):
        """Log VLM API call"""
        level = logging.INFO if success else logging.ERROR
        message = f"VLM API call for {case_number}: {'success' if success else 'failed'}"
        
        self._log_with_context(
            level, message,
            case_number=case_number,
            response_time_ms=response_time_ms,
            success=success,
            error=error,
            event_type="vlm_api_call"
        )
    
    def auto_learning_completed(self, batch_id: str, patterns_detected: int, accuracy_improvement: float):
        """Log auto-learning completion"""
        self.info(
            f"Auto-learning completed for batch {batch_id}",
            batch_id=batch_id,
            patterns_detected=patterns_detected,
            accuracy_improvement=accuracy_improvement,
            event_type="auto_learning_completed"
        )


def setup_logging():
    """Setup application logging configuration"""
    
    # Ensure logs directory exists
    settings.logs_path.mkdir(parents=True, exist_ok=True)
    
    # Root logger configuration
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, settings.log_level.upper()))
    
    # Clear existing handlers
    root_logger.handlers.clear()
    
    # Console handler
    console_handler = logging.StreamHandler(sys.stdout)
    if settings.debug:
        console_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
    else:
        console_formatter = JSONFormatter()
    console_handler.setFormatter(console_formatter)
    console_handler.setLevel(logging.INFO)
    root_logger.addHandler(console_handler)
    
    # File handler for all logs
    file_handler = logging.handlers.RotatingFileHandler(
        filename=settings.logs_path / "ai_farm.log",
        maxBytes=10 * 1024 * 1024,  # 10MB
        backupCount=5
    )
    file_formatter = JSONFormatter()
    file_handler.setFormatter(file_formatter)
    file_handler.setLevel(logging.DEBUG)
    root_logger.addHandler(file_handler)
    
    # Error handler for errors only
    error_handler = logging.handlers.RotatingFileHandler(
        filename=settings.logs_path / "errors.log",
        maxBytes=10 * 1024 * 1024,  # 10MB
        backupCount=10
    )
    error_formatter = JSONFormatter()
    error_handler.setFormatter(error_formatter)
    error_handler.setLevel(logging.ERROR)
    root_logger.addHandler(error_handler)
    
    # Performance handler for performance metrics
    perf_handler = logging.handlers.RotatingFileHandler(
        filename=settings.logs_path / "performance.log",
        maxBytes=5 * 1024 * 1024,  # 5MB
        backupCount=3
    )
    perf_formatter = JSONFormatter()
    perf_handler.setFormatter(perf_formatter)
    
    # Create performance logger
    perf_logger = logging.getLogger("performance")
    perf_logger.setLevel(logging.INFO)
    perf_logger.addHandler(perf_handler)
    perf_logger.propagate = False
    
    # Suppress noisy third-party loggers
    logging.getLogger("httpx").setLevel(logging.WARNING)
    logging.getLogger("urllib3").setLevel(logging.WARNING)
    logging.getLogger("PIL").setLevel(logging.WARNING)
    
    logging.info("Logging configuration completed")


def get_logger(name: str) -> AIFarmLogger:
    """Get an enhanced logger instance"""
    return AIFarmLogger(name)


def log_performance_metric(metric_name: str, value: float, unit: str = "", **context):
    """Log a performance metric"""
    perf_logger = logging.getLogger("performance")
    perf_logger.info(
        f"Performance metric: {metric_name}",
        extra={
            "metric_name": metric_name,
            "value": value,
            "unit": unit,
            "timestamp": datetime.utcnow().isoformat(),
            **context
        }
    )


def log_api_request(method: str, path: str, status_code: int, response_time_ms: float, request_id: str):
    """Log API request details"""
    logger = logging.getLogger("api")
    logger.info(
        f"{method} {path} - {status_code}",
        extra={
            "method": method,
            "path": path,
            "status_code": status_code,
            "response_time_ms": response_time_ms,
            "request_id": request_id,
            "event_type": "api_request"
        }
    )


def log_database_operation(operation: str, table: str, duration_ms: float, success: bool, error: Optional[str] = None):
    """Log database operation details"""
    logger = logging.getLogger("database")
    level = logging.DEBUG if success else logging.ERROR
    logger.log(
        level,
        f"Database {operation} on {table}: {'success' if success else 'failed'}",
        extra={
            "operation": operation,
            "table": table,
            "duration_ms": duration_ms,
            "success": success,
            "error": error,
            "event_type": "database_operation"
        }
    )


class LogContext:
    """Context manager for logging context"""
    
    def __init__(self, logger: AIFarmLogger, **context):
        self.logger = logger
        self.context = context
        self.original_context = None
    
    def __enter__(self):
        self.original_context = self.logger.context.copy()
        self.logger.set_context(**self.context)
        return self.logger
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.logger.context = self.original_context