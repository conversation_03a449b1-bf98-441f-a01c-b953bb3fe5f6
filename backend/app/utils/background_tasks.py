"""
AI-FARM Background Task Management
Handles long-running processes and task queuing
"""

import asyncio
import logging
import uuid
from datetime import datetime
from typing import Dict, Any, Callable, Optional, List
from enum import Enum
from dataclasses import dataclass, field
from concurrent.futures import ThreadPoolExecutor, Future

logger = logging.getLogger(__name__)


class TaskStatus(str, Enum):
    """Task execution status"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class TaskPriority(str, Enum):
    """Task priority levels"""
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    URGENT = "urgent"


@dataclass
class BackgroundTask:
    """Background task representation"""
    task_id: str
    name: str
    function: Callable
    args: tuple = field(default_factory=tuple)
    kwargs: dict = field(default_factory=dict)
    priority: TaskPriority = TaskPriority.NORMAL
    status: TaskStatus = TaskStatus.PENDING
    created_at: datetime = field(default_factory=datetime.utcnow)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    result: Any = None
    error: Optional[str] = None
    progress: float = 0.0
    progress_message: str = ""
    metadata: dict = field(default_factory=dict)


class TaskManager:
    """
    Background task manager with priority queuing and execution control
    """
    
    def __init__(self, max_workers: int = 4):
        self.max_workers = max_workers
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        self.tasks: Dict[str, BackgroundTask] = {}
        self.running_tasks: Dict[str, Future] = {}
        self.task_queue: asyncio.PriorityQueue = asyncio.PriorityQueue()
        self.running = False
        self.worker_task = None
        
        # Priority mapping for queue ordering
        self.priority_weights = {
            TaskPriority.URGENT: 0,
            TaskPriority.HIGH: 1,
            TaskPriority.NORMAL: 2,
            TaskPriority.LOW: 3
        }
    
    async def start(self):
        """Start the task manager"""
        if self.running:
            return
        
        self.running = True
        self.worker_task = asyncio.create_task(self._worker_loop())
        logger.info("Background task manager started")
    
    async def stop(self):
        """Stop the task manager and cleanup"""
        self.running = False
        
        if self.worker_task:
            self.worker_task.cancel()
            try:
                await self.worker_task
            except asyncio.CancelledError:
                pass
        
        # Cancel running tasks
        for task_id, future in self.running_tasks.items():
            future.cancel()
            self.tasks[task_id].status = TaskStatus.CANCELLED
        
        # Shutdown executor
        self.executor.shutdown(wait=True)
        logger.info("Background task manager stopped")
    
    async def submit_task(
        self,
        name: str,
        function: Callable,
        args: tuple = (),
        kwargs: dict = None,
        priority: TaskPriority = TaskPriority.NORMAL,
        metadata: dict = None
    ) -> str:
        """
        Submit a task for background execution
        
        Args:
            name: Task name for identification
            function: Function to execute
            args: Function arguments
            kwargs: Function keyword arguments
            priority: Task priority
            metadata: Additional metadata
            
        Returns:
            Task ID for tracking
        """
        if kwargs is None:
            kwargs = {}
        if metadata is None:
            metadata = {}
        
        task_id = str(uuid.uuid4())
        
        task = BackgroundTask(
            task_id=task_id,
            name=name,
            function=function,
            args=args,
            kwargs=kwargs,
            priority=priority,
            metadata=metadata
        )
        
        self.tasks[task_id] = task
        
        # Add to priority queue
        priority_weight = self.priority_weights[priority]
        await self.task_queue.put((priority_weight, task_id))
        
        logger.info(f"Submitted background task: {name} (ID: {task_id})")
        return task_id
    
    async def get_task_status(self, task_id: str) -> Optional[BackgroundTask]:
        """Get task status and details"""
        return self.tasks.get(task_id)
    
    async def cancel_task(self, task_id: str) -> bool:
        """Cancel a task"""
        if task_id not in self.tasks:
            return False
        
        task = self.tasks[task_id]
        
        if task.status == TaskStatus.PENDING:
            task.status = TaskStatus.CANCELLED
            task.completed_at = datetime.utcnow()
            return True
        
        if task.status == TaskStatus.RUNNING and task_id in self.running_tasks:
            future = self.running_tasks[task_id]
            future.cancel()
            task.status = TaskStatus.CANCELLED
            task.completed_at = datetime.utcnow()
            return True
        
        return False
    
    async def update_task_progress(self, task_id: str, progress: float, message: str = ""):
        """Update task progress"""
        if task_id in self.tasks:
            self.tasks[task_id].progress = max(0.0, min(100.0, progress))
            self.tasks[task_id].progress_message = message
    
    async def get_active_tasks(self) -> List[BackgroundTask]:
        """Get list of active (pending or running) tasks"""
        return [
            task for task in self.tasks.values()
            if task.status in [TaskStatus.PENDING, TaskStatus.RUNNING]
        ]
    
    async def get_completed_tasks(self, limit: int = 100) -> List[BackgroundTask]:
        """Get list of completed tasks"""
        completed = [
            task for task in self.tasks.values()
            if task.status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]
        ]
        
        # Sort by completion time, most recent first
        completed.sort(key=lambda t: t.completed_at or datetime.min, reverse=True)
        return completed[:limit]
    
    async def cleanup_old_tasks(self, hours_old: int = 24):
        """Clean up old completed tasks"""
        cutoff_time = datetime.utcnow() - asyncio.get_event_loop().time() + (hours_old * 3600)
        
        tasks_to_remove = []
        for task_id, task in self.tasks.items():
            if (task.status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED] and
                task.completed_at and task.completed_at.timestamp() < cutoff_time):
                tasks_to_remove.append(task_id)
        
        for task_id in tasks_to_remove:
            del self.tasks[task_id]
        
        logger.info(f"Cleaned up {len(tasks_to_remove)} old tasks")
        return len(tasks_to_remove)
    
    async def _worker_loop(self):
        """Main worker loop to process tasks"""
        while self.running:
            try:
                # Wait for task with timeout
                try:
                    priority, task_id = await asyncio.wait_for(
                        self.task_queue.get(), timeout=1.0
                    )
                except asyncio.TimeoutError:
                    continue
                
                if task_id not in self.tasks:
                    continue
                
                task = self.tasks[task_id]
                
                # Check if task was cancelled
                if task.status == TaskStatus.CANCELLED:
                    continue
                
                # Update task status
                task.status = TaskStatus.RUNNING
                task.started_at = datetime.utcnow()
                
                # Execute task in thread pool
                future = self.executor.submit(self._execute_task, task)
                self.running_tasks[task_id] = future
                
                # Wait for completion
                try:
                    result = await asyncio.wrap_future(future)
                    task.result = result
                    task.status = TaskStatus.COMPLETED
                    task.progress = 100.0
                    task.progress_message = "Completed"
                    
                except asyncio.CancelledError:
                    task.status = TaskStatus.CANCELLED
                    task.error = "Task was cancelled"
                    
                except Exception as e:
                    task.status = TaskStatus.FAILED
                    task.error = str(e)
                    logger.error(f"Task {task_id} failed: {e}")
                
                finally:
                    task.completed_at = datetime.utcnow()
                    if task_id in self.running_tasks:
                        del self.running_tasks[task_id]
                
                logger.info(f"Task completed: {task.name} (ID: {task_id}, Status: {task.status})")
                
            except Exception as e:
                logger.error(f"Worker loop error: {e}")
                await asyncio.sleep(1)
    
    def _execute_task(self, task: BackgroundTask) -> Any:
        """Execute a task synchronously"""
        try:
            if asyncio.iscoroutinefunction(task.function):
                # Handle async functions
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                try:
                    return loop.run_until_complete(task.function(*task.args, **task.kwargs))
                finally:
                    loop.close()
            else:
                # Handle sync functions
                return task.function(*task.args, **task.kwargs)
        
        except Exception as e:
            logger.error(f"Task execution error: {e}")
            raise


class TaskProgress:
    """Helper class for tracking task progress"""
    
    def __init__(self, task_manager: TaskManager, task_id: str):
        self.task_manager = task_manager
        self.task_id = task_id
    
    async def update(self, progress: float, message: str = ""):
        """Update task progress"""
        await self.task_manager.update_task_progress(self.task_id, progress, message)
    
    async def complete(self, message: str = "Task completed"):
        """Mark task as 100% complete"""
        await self.update(100.0, message)
    
    async def step(self, current: int, total: int, message: str = ""):
        """Update progress based on step completion"""
        progress = (current / total) * 100 if total > 0 else 0
        step_message = f"{message} ({current}/{total})" if message else f"Step {current}/{total}"
        await self.update(progress, step_message)


# Global task manager instance
task_manager = TaskManager()


async def start_task_manager():
    """Start the global task manager"""
    await task_manager.start()


async def stop_task_manager():
    """Stop the global task manager"""
    await task_manager.stop()


async def submit_background_task(
    name: str,
    function: Callable,
    args: tuple = (),
    kwargs: dict = None,
    priority: TaskPriority = TaskPriority.NORMAL,
    metadata: dict = None
) -> str:
    """
    Convenience function to submit a background task
    
    Args:
        name: Task name
        function: Function to execute
        args: Function arguments
        kwargs: Function keyword arguments
        priority: Task priority
        metadata: Additional metadata
        
    Returns:
        Task ID
    """
    return await task_manager.submit_task(
        name=name,
        function=function,
        args=args,
        kwargs=kwargs or {},
        priority=priority,
        metadata=metadata or {}
    )


async def get_task_progress(task_id: str) -> Optional[Dict[str, Any]]:
    """
    Get task progress information
    
    Args:
        task_id: Task identifier
        
    Returns:
        Progress information dictionary or None if task not found
    """
    task = await task_manager.get_task_status(task_id)
    if not task:
        return None
    
    return {
        "task_id": task.task_id,
        "name": task.name,
        "status": task.status.value,
        "progress": task.progress,
        "progress_message": task.progress_message,
        "created_at": task.created_at,
        "started_at": task.started_at,
        "completed_at": task.completed_at,
        "error": task.error,
        "metadata": task.metadata
    }


def create_progress_tracker(task_id: str) -> TaskProgress:
    """
    Create a progress tracker for a task
    
    Args:
        task_id: Task identifier
        
    Returns:
        TaskProgress instance
    """
    return TaskProgress(task_manager, task_id)