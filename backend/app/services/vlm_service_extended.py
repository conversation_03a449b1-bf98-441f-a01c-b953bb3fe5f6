"""
Extended VLM Service with VLM-38B-AWQ specific features
Adds compatibility improvements for the VLM-38B-AWQ endpoint
"""

from .vlm_service import VLMService
import httpx
import logging
from typing import Dict, Any
from ..core.config import settings

logger = logging.getLogger(__name__)


class ExtendedVLMService(VLMService):
    """Extended VLM Service with enhanced compatibility for VLM-38B-AWQ"""
    
    async def health_check(self) -> Dict[str, Any]:
        """
        Enhanced health check that works with VLM-38B-AWQ endpoint
        Tests the chat completions endpoint directly
        
        Returns:
            Dictionary with health status information
        """
        try:
            # For VLM-38B-AWQ, test with a minimal chat completion request
            async with httpx.AsyncClient(timeout=5) as client:
                headers = settings.vlm_api_headers
                
                # Minimal test payload
                test_payload = {
                    "model": self.model_name,
                    "messages": [
                        {
                            "role": "user",
                            "content": "Hello, this is a health check test."
                        }
                    ],
                    "max_tokens": 10,
                    "temperature": 0.0
                }
                
                # Try the chat completions endpoint
                try:
                    response = await client.post(
                        f"{self.base_url}/chat/completions",
                        headers=headers,
                        json=test_payload
                    )
                    
                    if response.status_code == 200:
                        return {
                            "status": "connected",
                            "response_time_ms": response.elapsed.total_seconds() * 1000,
                            "api_version": "VLM-38B-AWQ",
                            "model": self.model_name,
                            "endpoint": self.base_url,
                            "error": None
                        }
                    else:
                        return {
                            "status": "error",
                            "response_time_ms": response.elapsed.total_seconds() * 1000,
                            "api_version": None,
                            "error": f"HTTP {response.status_code}: {response.text}"
                        }
                except httpx.ConnectError:
                    # Try alternative endpoints if main one fails
                    # Try without /v1 suffix if it exists
                    if self.base_url.endswith('/v1'):
                        alt_base_url = self.base_url[:-3]
                        response = await client.post(
                            f"{alt_base_url}/v1/chat/completions",
                            headers=headers,
                            json=test_payload
                        )
                        if response.status_code == 200:
                            logger.info(f"Note: API is accessible at {alt_base_url}/v1 (not {self.base_url})")
                            return {
                                "status": "connected",
                                "response_time_ms": response.elapsed.total_seconds() * 1000,
                                "api_version": "VLM-38B-AWQ",
                                "model": self.model_name,
                                "endpoint": f"{alt_base_url}/v1",
                                "error": None,
                                "note": "Base URL adjusted for compatibility"
                            }
                    raise
                    
        except httpx.ConnectError as e:
            return {
                "status": "disconnected", 
                "response_time_ms": None,
                "api_version": None,
                "error": f"Connection failed: {str(e)}",
                "endpoint": self.base_url
            }
        except Exception as e:
            return {
                "status": "error",
                "response_time_ms": None,
                "api_version": None,
                "error": f"Health check failed: {str(e)}",
                "endpoint": self.base_url
            }
    
    async def validate_endpoint_configuration(self) -> Dict[str, Any]:
        """
        Validate and potentially auto-correct endpoint configuration
        
        Returns:
            Dictionary with validation results and recommendations
        """
        results = {
            "original_base_url": self.base_url,
            "validated": False,
            "recommendations": []
        }
        
        # Test current configuration
        health_result = await self.health_check()
        
        if health_result["status"] == "connected":
            results["validated"] = True
            results["working_endpoint"] = health_result.get("endpoint", self.base_url)
            results["status"] = "Configuration is correct"
        else:
            # Try various endpoint variations
            variations = []
            
            # If URL ends with /v1, try without it
            if self.base_url.endswith('/v1'):
                variations.append(self.base_url[:-3])
            
            # If URL doesn't end with /v1, try with it
            if not self.base_url.endswith('/v1'):
                variations.append(f"{self.base_url}/v1")
            
            # Test each variation
            for variant in variations:
                self.base_url = variant
                test_result = await self.health_check()
                if test_result["status"] == "connected":
                    results["validated"] = True
                    results["working_endpoint"] = variant
                    results["status"] = f"Found working endpoint: {variant}"
                    results["recommendations"].append(f"Update VLM_API_BASE_URL to: {variant}")
                    break
            
            # Restore original URL if no working variant found
            if not results["validated"]:
                self.base_url = results["original_base_url"]
                results["status"] = "No working endpoint found"
                results["recommendations"].append("Verify the VLM-38B-AWQ service is running")
                results["recommendations"].append("Check network connectivity to the endpoint")
                results["recommendations"].append("Confirm the API key is correct")
        
        return results