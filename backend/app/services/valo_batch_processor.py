"""
VALO Batch Processor with Auto-Learning
Processes all 1,250+ cases in small chunks with multi-round optimization
"""

import asyncio
import json
import os
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional
import pandas as pd
import httpx
import base64
from collections import defaultdict
try:
    import redis
    redis_available = True
except ImportError:
    redis_available = False
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class VALOBatchProcessor:
    def __init__(self):
        self.csv_path = "/home/<USER>/VALO_AI-FARM_2025/ai_farm_images_fixed_250703/psa_valo_violation_report_without_images_csv_2025-07-03_14-14-01.CSV"
        self.images_base = "/home/<USER>/VALO_AI-FARM_2025/ai_farm_images_fixed_250703/ai_farm_images_fixed"
        if redis_available:
            try:
                self.redis_client = redis.Redis(host='localhost', port=6379, db=0, decode_responses=True)
                self.redis_client.ping()
            except:
                self.redis_client = None
        else:
            self.redis_client = None
        
        # In-memory storage for progress when Redis is not available
        self.progress_storage = {}
        
        # Processing configuration
        self.chunk_size = 5  # Small chunks to avoid timeouts
        self.delay_between_chunks = 2  # Seconds
        self.max_retries = 3
        
        # Auto-learning parameters
        self.learning_params = {
            'confidence_thresholds': {
                'PPE Non-compliance': 95,
                'One man Lashing': 90,
                'Ex.Row Violation': 85,
                '2-Container Distance': 85,
                'STA Double-up': 90,
                'Spreader Ride': 95,
                'default': 90
            },
            'person_detection_weight': 0.8,
            'pattern_weights': {},
            'camera_accuracy': {},
            'round_improvements': []
        }
        
        # Results storage
        self.results = []
        self.current_round = 0
        
    def load_all_cases(self) -> List[Dict]:
        """Load all cases with matching images"""
        logger.info("Loading all cases from CSV and matching with images...")
        
        df = pd.read_csv(self.csv_path)
        cases = []
        
        # Get all available image case numbers
        image_cases = set()
        for subdir in ['invalid', 'valid']:
            subdir_path = os.path.join(self.images_base, subdir)
            if os.path.exists(subdir_path):
                for file in os.listdir(subdir_path):
                    if '_cropped_' in file:
                        case_number = file.split('_')[0]
                        image_cases.add(case_number)
        
        # Match with CSV data
        for case_number in image_cases:
            case_data = df[df['Case Int. ID'] == case_number]
            if not case_data.empty:
                row = case_data.iloc[0]
                
                # Determine image paths
                is_invalid = row['Alert Status'] == 'Invalid'
                status_dir = 'invalid' if is_invalid else 'valid'
                status_suffix = 'invalid' if is_invalid else 'valid'
                
                cropped_path = f"{self.images_base}/{status_dir}/{case_number}_cropped_{status_suffix}.JPEG"
                source_path = f"{self.images_base}/{status_dir}/{case_number}_source_{status_suffix}.JPEG"
                
                if os.path.exists(cropped_path):
                    cases.append({
                        'case_number': case_number,
                        'cropped_image': cropped_path,
                        'source_image': source_path if os.path.exists(source_path) else None,
                        'terminal': row['Terminal'],
                        'camera_id': row['Camera'],
                        'infringement_type': row['Type of Infringement'],
                        'alert_status': row['Alert Status'],
                        'remarks': row.get('Remarks', ''),
                        'is_false_positive': is_invalid
                    })
        
        logger.info(f"Loaded {len(cases)} cases with images")
        return sorted(cases, key=lambda x: x['case_number'])
    
    async def analyze_with_vlm(self, image_path: str, prompt: str, case_info: Dict) -> Dict:
        """Analyze single image with VLM"""
        try:
            # Read and encode image
            with open(image_path, 'rb') as f:
                image_data = f.read()
            base64_image = base64.b64encode(image_data).decode('utf-8')
            
            # Try primary endpoint first
            for attempt in range(self.max_retries):
                try:
                    async with httpx.AsyncClient(timeout=30.0) as client:
                        response = await client.post(
                            "http://100.106.127.35:9500/v1/chat/completions",
                            headers={"Content-Type": "application/json"},
                            json={
                                "model": "VLM-38B-AWQ",
                                "messages": [{
                                    "role": "user",
                                    "content": [
                                        {"type": "text", "text": prompt},
                                        {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{base64_image}"}}
                                    ]
                                }],
                                "max_tokens": 300,
                                "temperature": 0.1
                            }
                        )
                        
                        if response.status_code == 200:
                            result = response.json()
                            return self.parse_vlm_response(
                                result['choices'][0]['message']['content'],
                                case_info
                            )
                        
                except Exception as e:
                    if attempt == self.max_retries - 1:
                        logger.warning(f"Primary VLM failed for {case_info['case_number']}: {e}")
                    await asyncio.sleep(1)
            
            # Fallback to secondary endpoint
            try:
                async with httpx.AsyncClient(timeout=30.0) as client:
                    response = await client.post(
                        "https://api.friendli.ai/dedicated/v1/chat/completions",
                        headers={
                            "Content-Type": "application/json",
                            "Authorization": "Bearer flp_Xf21SJG60333K9KZ2qPOs5Uh0CMwltZ5c4xEefBO2pRe2"
                        },
                        json={
                            "model": "nhyws8db6r6t",
                            "messages": [{
                                "role": "user",
                                "content": [
                                    {"type": "text", "text": prompt},
                                    {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{base64_image}"}}
                                ]
                            }],
                            "max_tokens": 300,
                            "temperature": 0.1
                        }
                    )
                    
                    if response.status_code == 200:
                        result = response.json()
                        return self.parse_vlm_response(
                            result['choices'][0]['message']['content'],
                            case_info
                        )
                        
            except Exception as e:
                logger.error(f"Both VLM endpoints failed for {case_info['case_number']}: {e}")
            
            # Default response if both fail
            return {
                'case_number': case_info['case_number'],
                'vlm_decision': 'FLAG_FOR_REVIEW',
                'person_detected': 'unclear',
                'confidence': 0,
                'is_false_positive_predicted': False,
                'reasoning': 'VLM analysis failed - defaulting to safety',
                'processing_error': True
            }
            
        except Exception as e:
            logger.error(f"Error processing {case_info['case_number']}: {e}")
            return {
                'case_number': case_info['case_number'],
                'vlm_decision': 'FLAG_FOR_REVIEW',
                'person_detected': 'unclear',
                'confidence': 0,
                'is_false_positive_predicted': False,
                'reasoning': f'Processing error: {str(e)}',
                'processing_error': True
            }
    
    def parse_vlm_response(self, content: str, case_info: Dict) -> Dict:
        """Parse VLM response with safety-first approach"""
        content_lower = content.lower()
        
        # Extract key information
        person_detected = 'unclear'
        if 'person_detected: yes' in content_lower:
            person_detected = 'yes'
        elif 'person_detected: no' in content_lower:
            person_detected = 'no'
        elif 'person_detected: possibly' in content_lower:
            person_detected = 'possibly'
        
        # Extract confidence
        confidence = 50  # Default conservative
        try:
            if 'confidence:' in content_lower:
                conf_line = [line for line in content.split('\n') if 'confidence:' in line.lower()][0]
                confidence = int(''.join(filter(str.isdigit, conf_line.split(':')[1])))
        except:
            pass
        
        # Extract decision
        decision = 'FLAG_FOR_REVIEW'  # Default safe
        if 'safety_decision: dismiss_with_caution' in content_lower:
            decision = 'DISMISS_WITH_CAUTION'
        elif 'safety_decision: flag_for_review' in content_lower:
            decision = 'FLAG_FOR_REVIEW'
        
        # Determine if VLM thinks it's a false positive
        is_fp_predicted = False
        if decision == 'DISMISS_WITH_CAUTION' and confidence >= self.learning_params['confidence_thresholds'].get(
            case_info['infringement_type'], 90
        ):
            is_fp_predicted = True
        
        # Extract reasoning
        reasoning = ''
        if 'reasoning:' in content_lower:
            reasoning_start = content.lower().find('reasoning:')
            reasoning = content[reasoning_start:].split('\n')[0].replace('reasoning:', '').strip()
        
        return {
            'case_number': case_info['case_number'],
            'vlm_decision': decision,
            'person_detected': person_detected,
            'confidence': confidence,
            'is_false_positive_predicted': is_fp_predicted,
            'reasoning': reasoning,
            'processing_error': False
        }
    
    def generate_safety_first_prompt(self, case: Dict, round_num: int) -> str:
        """Generate safety-first prompt with learning from previous rounds"""
        
        base_prompt = f"""🚨 SAFETY-FIRST VIOLATION DETECTION - ROUND {round_num} 🚨

CRITICAL DIRECTIVE: When in doubt, FLAG FOR REVIEW. Never dismiss potential violations.

PRIORITY #1: PROTECT ALL VALID CASES (0% false negatives target)
PRIORITY #2: Filter obvious false positives ONLY with extreme confidence

Case Information:
- Case Number: {case['case_number']}
- Terminal: {case['terminal']}
- Violation Type: {case['infringement_type']}
- Camera: {case['camera_id']}
- Human Assessment: {case['alert_status']}
- Human Notes: {case.get('remarks', 'None')}
"""

        # Add learning from previous rounds
        if round_num > 1 and self.learning_params['round_improvements']:
            prev_round = self.learning_params['round_improvements'][-1]
            base_prompt += f"""
            
LEARNING FROM ROUND {round_num - 1}:
- Processed cases: {prev_round['total_processed']}
- Valid cases protected: {prev_round['valid_protected']}/{prev_round['total_valid']} ({prev_round['valid_protection_rate']:.1f}%)
- False positives detected: {prev_round['fp_detected']}/{prev_round['total_fp']} ({prev_round['fp_detection_rate']:.1f}%)
"""

        # Add camera-specific learning
        camera = case['camera_id']
        if camera in self.learning_params['camera_accuracy']:
            cam_acc = self.learning_params['camera_accuracy'][camera]
            if cam_acc['valid_missed'] > 0:
                base_prompt += f"\n⚠️ CRITICAL: Camera {camera} previously missed {cam_acc['valid_missed']} valid cases - BE EXTRA CAUTIOUS"

        # Add safety-first analysis protocol
        base_prompt += f"""

⚠️ CONSERVATIVE ANALYSIS PROTOCOL ⚠️

Step 1: CHECK PROTECTION RULES (If ANY apply → DO NOT DISMISS)
□ Any human-like shape or silhouette visible?
□ Image unclear, obstructed, or poor quality?
□ Any movement or activity indicators?
□ PPE-related violation type?
□ Critical safety area (lashing, stacking, vessel)?
□ Partial visibility or edge cases?

Step 2: PERSON DETECTION (Conservative)
- Default assumption: PERSON PRESENT unless 100% certain otherwise
- Even 10% chance of person = FLAG FOR REVIEW
- Equipment must be CLEARLY identifiable to dismiss

Step 3: VIOLATION ASSESSMENT (Ultra-Conservative)
Required confidence to dismiss as false positive:
- {case['infringement_type']}: {self.learning_params['confidence_thresholds'].get(case['infringement_type'], 90)}%+
- Current round adjustment: +{(round_num - 1) * 5}% extra caution

DECISION TREE:
1. Any human possibility? → FLAG FOR REVIEW
2. Protection rule triggered? → FLAG FOR REVIEW
3. Confidence < {self.learning_params['confidence_thresholds'].get(case['infringement_type'], 90)}%? → FLAG FOR REVIEW
4. Only if {self.learning_params['confidence_thresholds'].get(case['infringement_type'], 90)}%+ certain it's equipment → Consider dismissing

Output Format:
PERSON_DETECTED: [yes/no/possibly] (default: possibly)
CONFIDENCE: [0-100] (be conservative)
IS_FALSE_POSITIVE: [true/false] (default: false)
SAFETY_DECISION: [FLAG_FOR_REVIEW/DISMISS_WITH_CAUTION]
PROTECTION_RULES_TRIGGERED: [list any triggered rules]
REASONING: [Explain why flagging or rare dismissal]

⚠️ REMEMBER: Better to review 1000 false positives than miss 1 real violation ⚠️
"""
        
        return base_prompt
    
    async def process_chunk(self, cases: List[Dict], round_num: int, chunk_index: int, total_chunks: int) -> List[Dict]:
        """Process a small chunk of cases"""
        chunk_results = []
        
        logger.info(f"Round {round_num} - Processing chunk {chunk_index + 1}/{total_chunks} ({len(cases)} cases)")
        
        for i, case in enumerate(cases):
            # Generate prompt
            prompt = self.generate_safety_first_prompt(case, round_num)
            
            # Analyze with VLM
            vlm_result = await self.analyze_with_vlm(case['cropped_image'], prompt, case)
            
            # Combine with case info
            result = {
                **case,
                **vlm_result,
                'round': round_num,
                'chunk_index': chunk_index,
                'timestamp': datetime.now().isoformat()
            }
            
            # Calculate accuracy
            if case['is_false_positive']:  # Actually a false positive
                result['correct_prediction'] = vlm_result['is_false_positive_predicted']
            else:  # Actually a valid case
                result['correct_prediction'] = not vlm_result['is_false_positive_predicted']
                result['valid_case_protected'] = not vlm_result['is_false_positive_predicted']
            
            chunk_results.append(result)
            
            # Update progress in Redis
            progress = {
                'round': round_num,
                'chunk': chunk_index + 1,
                'total_chunks': total_chunks,
                'cases_processed': len(self.results) + len(chunk_results),
                'total_cases': len(self.all_cases)
            }
            if self.redis_client:
                self.redis_client.setex(
                    'valo_processing_progress',
                    300,  # 5 minute TTL
                    json.dumps(progress)
                )
            else:
                self.progress_storage['valo_processing_progress'] = progress
            
            # Small delay between cases
            if i < len(cases) - 1:
                await asyncio.sleep(0.5)
        
        return chunk_results
    
    def update_learning_parameters(self, round_results: List[Dict]):
        """Update learning parameters based on round results"""
        logger.info("Updating learning parameters...")
        
        # Calculate round statistics
        total_cases = len(round_results)
        valid_cases = [r for r in round_results if not r['is_false_positive']]
        fp_cases = [r for r in round_results if r['is_false_positive']]
        
        valid_protected = sum(1 for r in valid_cases if r.get('valid_case_protected', True))
        fp_detected = sum(1 for r in fp_cases if r.get('is_false_positive_predicted', False))
        
        # Store round improvement
        round_stats = {
            'round': self.current_round,
            'total_processed': total_cases,
            'total_valid': len(valid_cases),
            'valid_protected': valid_protected,
            'valid_protection_rate': (valid_protected / len(valid_cases) * 100) if valid_cases else 100,
            'total_fp': len(fp_cases),
            'fp_detected': fp_detected,
            'fp_detection_rate': (fp_detected / len(fp_cases) * 100) if fp_cases else 0
        }
        self.learning_params['round_improvements'].append(round_stats)
        
        # Update camera accuracy tracking
        camera_stats = defaultdict(lambda: {
            'total': 0, 'correct': 0, 'valid_missed': 0, 'fp_missed': 0
        })
        
        for result in round_results:
            camera = result['camera_id']
            camera_stats[camera]['total'] += 1
            
            if result.get('correct_prediction', False):
                camera_stats[camera]['correct'] += 1
            
            if not result['is_false_positive'] and result.get('is_false_positive_predicted', False):
                camera_stats[camera]['valid_missed'] += 1
            elif result['is_false_positive'] and not result.get('is_false_positive_predicted', False):
                camera_stats[camera]['fp_missed'] += 1
        
        # Update camera parameters
        for camera, stats in camera_stats.items():
            self.learning_params['camera_accuracy'][camera] = stats
        
        # Adjust confidence thresholds based on performance
        infringement_stats = defaultdict(lambda: {
            'total': 0, 'valid_missed': 0
        })
        
        for result in round_results:
            inf_type = result['infringement_type']
            infringement_stats[inf_type]['total'] += 1
            
            if not result['is_false_positive'] and result.get('is_false_positive_predicted', False):
                infringement_stats[inf_type]['valid_missed'] += 1
        
        # Increase thresholds for any type that missed valid cases
        for inf_type, stats in infringement_stats.items():
            if stats['valid_missed'] > 0:
                current = self.learning_params['confidence_thresholds'].get(inf_type, 90)
                self.learning_params['confidence_thresholds'][inf_type] = min(current + 5, 99)
                logger.warning(f"Increased threshold for {inf_type} to {self.learning_params['confidence_thresholds'][inf_type]}% due to missed valid cases")
    
    async def save_results(self, filename: str = None):
        """Save processing results"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"valo_batch_results_{timestamp}.json"
        
        filepath = f"/home/<USER>/VALO_AI-FARM_2025/{filename}"
        
        # Calculate final statistics
        valid_cases = [r for r in self.results if not r['is_false_positive']]
        fp_cases = [r for r in self.results if r['is_false_positive']]
        
        valid_protected = sum(1 for r in valid_cases if not r.get('is_false_positive_predicted', False))
        fp_detected = sum(1 for r in fp_cases if r.get('is_false_positive_predicted', False))
        
        summary = {
            'processing_summary': {
                'total_cases': len(self.results),
                'total_rounds': self.current_round,
                'total_valid_cases': len(valid_cases),
                'valid_cases_protected': valid_protected,
                'valid_protection_rate': (valid_protected / len(valid_cases) * 100) if valid_cases else 100,
                'total_false_positives': len(fp_cases),
                'false_positives_detected': fp_detected,
                'fp_detection_rate': (fp_detected / len(fp_cases) * 100) if fp_cases else 0,
                'processing_errors': sum(1 for r in self.results if r.get('processing_error', False))
            },
            'business_impact': {
                'annual_false_positives': int(len(fp_cases) * 12),
                'annual_fp_filtered': int(fp_detected * 12),
                'annual_time_saved_minutes': int(fp_detected * 12 * 5),
                'annual_cost_savings': int(fp_detected * 12 * 5 / 60 * 60),
                'valid_cases_at_risk': len(valid_cases) - valid_protected
            },
            'learning_parameters': self.learning_params,
            'round_improvements': self.learning_params['round_improvements'],
            'detailed_results': self.results
        }
        
        with open(filepath, 'w') as f:
            json.dump(summary, f, indent=2)
        
        # Also save to Redis for UI access
        if self.redis_client:
            self.redis_client.setex(
                'valo_batch_results',
                3600,  # 1 hour TTL
                json.dumps(summary)
            )
        else:
            self.progress_storage['valo_batch_results'] = summary
        
        logger.info(f"Results saved to {filepath}")
        return filepath
    
    async def run_batch_processing(self, num_rounds: int = 3):
        """Run multi-round batch processing on all cases"""
        logger.info(f"Starting batch processing with {num_rounds} rounds")
        
        # Load all cases
        self.all_cases = self.load_all_cases()
        total_cases = len(self.all_cases)
        
        if not self.all_cases:
            logger.error("No cases found to process")
            return
        
        # Process multiple rounds
        for round_num in range(1, num_rounds + 1):
            self.current_round = round_num
            logger.info(f"\n{'='*60}")
            logger.info(f"STARTING ROUND {round_num} OF {num_rounds}")
            logger.info(f"{'='*60}")
            
            round_results = []
            
            # Process in chunks
            total_chunks = (total_cases + self.chunk_size - 1) // self.chunk_size
            
            for chunk_index in range(total_chunks):
                start_idx = chunk_index * self.chunk_size
                end_idx = min(start_idx + self.chunk_size, total_cases)
                chunk_cases = self.all_cases[start_idx:end_idx]
                
                # Process chunk
                chunk_results = await self.process_chunk(
                    chunk_cases, round_num, chunk_index, total_chunks
                )
                round_results.extend(chunk_results)
                
                # Save intermediate results
                self.results = round_results
                await self.save_results(f"valo_batch_round{round_num}_intermediate.json")
                
                # Delay between chunks
                if chunk_index < total_chunks - 1:
                    logger.info(f"Waiting {self.delay_between_chunks} seconds before next chunk...")
                    await asyncio.sleep(self.delay_between_chunks)
            
            # Update learning parameters after each round
            self.update_learning_parameters(round_results)
            
            # Save round results
            self.results = round_results
            await self.save_results(f"valo_batch_round{round_num}_complete.json")
            
            logger.info(f"\nROUND {round_num} COMPLETE")
            if self.learning_params['round_improvements']:
                latest = self.learning_params['round_improvements'][-1]
                logger.info(f"Valid Protection Rate: {latest['valid_protection_rate']:.1f}%")
                logger.info(f"False Positive Detection Rate: {latest['fp_detection_rate']:.1f}%")
        
        # Save final results
        final_path = await self.save_results("valo_batch_final_results.json")
        logger.info(f"\n{'='*60}")
        logger.info(f"BATCH PROCESSING COMPLETE")
        logger.info(f"Final results saved to: {final_path}")
        logger.info(f"{'='*60}")


async def main():
    """Run the batch processor"""
    processor = VALOBatchProcessor()
    
    try:
        # Run 3 rounds of processing with auto-learning
        await processor.run_batch_processing(num_rounds=3)
        
    except Exception as e:
        logger.error(f"Batch processing failed: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(main())