"""
AI-FARM Batch Processing Engine
Handles CSV data parsing, image processing workflows, and case number pattern mapping
"""

import asyncio
import csv
import logging
import pandas as pd
import re
import uuid
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple, Callable
from concurrent.futures import ThreadPoolExecutor

from ..core.config import settings
from ..core.database import DatabaseOperations
from ..models.schemas import (
    CaseData, ProcessingResult, BatchProcessingResponse, 
    ProcessingStatus, ValidationStatus, VLMAnalysisResult
)
from ..services.vlm_service import vlm_service

logger = logging.getLogger(__name__)


class CaseNumberProcessor:
    """Handles mathematical case_number to pk_event pattern mapping"""
    
    @staticmethod
    def extract_pk_event_from_case_number(case_number: str) -> int:
        """
        Extract pk_event from case_number using mathematical pattern
        Example: V1250630118 -> pk_event calculation
        
        Args:
            case_number: Case number string (e.g., V1250630118)
            
        Returns:
            Calculated pk_event integer
        """
        try:
            # Remove 'V' prefix and extract numeric part
            if not case_number.startswith('V125'):
                raise ValueError(f"Invalid case number format: {case_number}")
            
            numeric_part = case_number[1:]  # Remove 'V'
            
            # Extract components: V125MMDDHHNN
            # 125 = constant prefix
            # MM = month (06)
            # DD = day (30)  
            # HH = hour (01)
            # NN = sequence number (18)
            
            if len(numeric_part) != 10:
                raise ValueError(f"Invalid case number length: {case_number}")
            
            prefix = numeric_part[:3]  # 125
            month = int(numeric_part[3:5])  # 06
            day = int(numeric_part[5:7])    # 30
            hour = int(numeric_part[7:9])   # 01
            sequence = int(numeric_part[9:11])  # 18
            
            # Mathematical pattern to generate pk_event
            # This is a simplified pattern - adjust based on actual requirements
            pk_event = (
                int(prefix) * 1000000 +  # 125000000
                month * 10000 +          # 60000
                day * 100 +              # 3000
                hour * 10 +              # 10
                sequence                 # 18
            )
            
            return pk_event
            
        except Exception as e:
            logger.error(f"Failed to extract pk_event from case number {case_number}: {e}")
            # Fallback: use hash of case number
            return abs(hash(case_number)) % 2147483647
    
    @staticmethod
    def generate_case_number(pk_event: int) -> str:
        """
        Generate case number from pk_event (reverse operation)
        
        Args:
            pk_event: Primary key event ID
            
        Returns:
            Generated case number
        """
        try:
            # Extract components from pk_event
            sequence = pk_event % 100
            hour = (pk_event // 10) % 100
            day = (pk_event // 100) % 100
            month = (pk_event // 10000) % 100
            
            # Format as case number
            case_number = f"V125{month:02d}{day:02d}{hour:02d}{sequence:02d}"
            return case_number
            
        except Exception as e:
            logger.error(f"Failed to generate case number from pk_event {pk_event}: {e}")
            return f"V125{pk_event % 100000000:08d}"


class CSVProcessor:
    """Handles CSV data parsing and validation"""
    
    def __init__(self):
        self.required_columns = ['pk_event', 'case_number', 'url', 'key']
        self.validation_status_mapping = {
            'invalid': ValidationStatus.INVALID_FALSE_POSITIVE,
            'valid': ValidationStatus.VALID,
            '': ValidationStatus.UNKNOWN
        }
    
    def parse_csv_file(self, csv_file_path: str) -> List[CaseData]:
        """
        Parse CSV file and extract case data
        
        Args:
            csv_file_path: Path to CSV file
            
        Returns:
            List of CaseData objects
        """
        try:
            csv_path = Path(csv_file_path)
            if not csv_path.exists():
                raise FileNotFoundError(f"CSV file not found: {csv_file_path}")
            
            # Read CSV with pandas for better handling
            df = pd.read_csv(csv_path)
            
            # Validate columns
            missing_columns = set(self.required_columns) - set(df.columns)
            if missing_columns:
                raise ValueError(f"Missing required columns: {missing_columns}")
            
            # Process each row
            case_data_list = []
            for index, row in df.iterrows():
                try:
                    case_data = self._process_csv_row(row, index)
                    if case_data:
                        case_data_list.append(case_data)
                except Exception as e:
                    logger.warning(f"Failed to process CSV row {index}: {e}")
                    continue
            
            logger.info(f"Parsed {len(case_data_list)} cases from CSV file")
            return case_data_list
            
        except Exception as e:
            logger.error(f"Failed to parse CSV file {csv_file_path}: {e}")
            raise
    
    def _process_csv_row(self, row: pd.Series, row_index: int) -> Optional[CaseData]:
        """
        Process a single CSV row into CaseData
        
        Args:
            row: Pandas Series representing a CSV row
            row_index: Row index for error reporting
            
        Returns:
            CaseData object or None if invalid
        """
        try:
            # Extract and validate pk_event
            pk_event = int(row['pk_event']) if pd.notna(row['pk_event']) else None
            if pk_event is None:
                logger.warning(f"Row {row_index}: Missing pk_event")
                return None
            
            # Extract case number
            case_number = str(row['case_number']).strip() if pd.notna(row['case_number']) else None
            if not case_number:
                # Generate case number from pk_event if missing
                case_number = CaseNumberProcessor.generate_case_number(pk_event)
                logger.info(f"Generated case number {case_number} for pk_event {pk_event}")
            
            # Validate case number format
            if not case_number.startswith('V125'):
                logger.warning(f"Row {row_index}: Invalid case number format: {case_number}")
                return None
            
            # Extract image URL
            image_url = str(row['url']).strip() if pd.notna(row['url']) else None
            if not image_url:
                logger.warning(f"Row {row_index}: Missing image URL")
                return None
            
            # Extract validation status
            key_value = str(row['key']).strip().lower() if pd.notna(row['key']) else ''
            validation_status = self.validation_status_mapping.get(key_value, ValidationStatus.UNKNOWN)
            
            # Create CaseData object
            case_data = CaseData(
                pk_event=pk_event,
                case_number=case_number,
                image_url=image_url,
                validation_status=validation_status
            )
            
            return case_data
            
        except Exception as e:
            logger.error(f"Failed to process CSV row {row_index}: {e}")
            return None
    
    def validate_image_paths(self, case_data_list: List[CaseData], 
                           images_base_path: Optional[str] = None) -> Tuple[List[CaseData], List[str]]:
        """
        Validate that image files exist and are accessible
        
        Args:
            case_data_list: List of CaseData objects
            images_base_path: Base path for images (if relative paths in CSV)
            
        Returns:
            Tuple of (valid_cases, error_messages)
        """
        valid_cases = []
        error_messages = []
        
        for case_data in case_data_list:
            try:
                # Construct full image path
                if images_base_path and not Path(case_data.image_url).is_absolute():
                    image_path = Path(images_base_path) / case_data.image_url
                else:
                    image_path = Path(case_data.image_url)
                
                # Check if file exists
                if not image_path.exists():
                    error_msg = f"Image not found for case {case_data.case_number}: {image_path}"
                    error_messages.append(error_msg)
                    logger.warning(error_msg)
                    continue
                
                # Check file size
                file_size_mb = image_path.stat().st_size / (1024 * 1024)
                if file_size_mb > settings.image_max_size_mb:
                    logger.warning(f"Large image file ({file_size_mb:.1f}MB): {image_path}")
                
                # Update case data with full path
                case_data.image_url = str(image_path)
                valid_cases.append(case_data)
                
            except Exception as e:
                error_msg = f"Error validating image for case {case_data.case_number}: {e}"
                error_messages.append(error_msg)
                logger.error(error_msg)
        
        logger.info(f"Validated {len(valid_cases)} out of {len(case_data_list)} cases")
        return valid_cases, error_messages


class BatchProcessor:
    """Main batch processing engine"""
    
    def __init__(self):
        self.csv_processor = CSVProcessor()
        self.case_number_processor = CaseNumberProcessor()
        self.active_batches: Dict[str, Dict[str, Any]] = {}
        self.progress_callbacks: Dict[str, List[Callable]] = {}
    
    async def process_batch_from_csv(
        self,
        csv_file_path: str,
        images_base_path: Optional[str] = None,
        use_auto_learning: bool = True,
        custom_thresholds: Optional[Dict[str, int]] = None,
        priority: str = "normal",
        progress_callback: Optional[Callable] = None
    ) -> BatchProcessingResponse:
        """
        Process a batch of cases from CSV file
        
        Args:
            csv_file_path: Path to CSV file
            images_base_path: Base path for images
            use_auto_learning: Enable auto-learning features
            custom_thresholds: Custom confidence thresholds
            priority: Processing priority
            progress_callback: Optional callback for progress updates
            
        Returns:
            BatchProcessingResponse with results
        """
        batch_id = str(uuid.uuid4())
        start_time = datetime.utcnow()
        
        try:
            logger.info(f"Starting batch processing {batch_id} from CSV: {csv_file_path}")
            
            # Parse CSV file
            case_data_list = self.csv_processor.parse_csv_file(csv_file_path)
            if not case_data_list:
                raise ValueError("No valid cases found in CSV file")
            
            # Validate image paths
            valid_cases, validation_errors = self.csv_processor.validate_image_paths(
                case_data_list, images_base_path
            )
            
            if not valid_cases:
                raise ValueError("No cases with valid images found")
            
            # Create database record
            DatabaseOperations.create_batch_processing_record(
                batch_id=batch_id,
                total_cases=len(valid_cases),
                use_auto_learning=use_auto_learning,
                custom_thresholds=custom_thresholds,
                priority=priority
            )
            
            # Register progress callback
            if progress_callback:
                self.progress_callbacks[batch_id] = [progress_callback]
            
            # Initialize batch tracking
            self.active_batches[batch_id] = {
                "status": ProcessingStatus.PROCESSING,
                "total_cases": len(valid_cases),
                "processed_cases": 0,
                "failed_cases": 0,
                "start_time": start_time,
                "validation_errors": validation_errors
            }
            
            # Process cases
            results = await self._process_cases_batch(
                batch_id, valid_cases, custom_thresholds
            )
            
            # Calculate summary
            summary = self._calculate_batch_summary(results, validation_errors)
            
            # Update batch status
            DatabaseOperations.update_batch_status(batch_id, "completed")
            
            # Create response
            response = BatchProcessingResponse(
                batch_id=batch_id,
                total_cases=len(valid_cases),
                status=ProcessingStatus.COMPLETED,
                results=results,
                summary=summary,
                started_at=start_time,
                completed_at=datetime.utcnow()
            )
            
            logger.info(f"Batch processing {batch_id} completed successfully")
            return response
            
        except Exception as e:
            logger.error(f"Batch processing {batch_id} failed: {e}")
            
            # Update batch status
            DatabaseOperations.update_batch_status(batch_id, "failed", str(e))
            
            # Create error response
            return BatchProcessingResponse(
                batch_id=batch_id,
                total_cases=len(case_data_list) if 'case_data_list' in locals() else 0,
                status=ProcessingStatus.FAILED,
                results=[],
                summary={"error": str(e), "validation_errors": validation_errors if 'validation_errors' in locals() else []},
                started_at=start_time,
                completed_at=datetime.utcnow()
            )
        
        finally:
            # Cleanup
            if batch_id in self.active_batches:
                del self.active_batches[batch_id]
            if batch_id in self.progress_callbacks:
                del self.progress_callbacks[batch_id]
    
    async def _process_cases_batch(
        self,
        batch_id: str,
        case_data_list: List[CaseData],
        custom_thresholds: Optional[Dict[str, int]] = None
    ) -> List[ProcessingResult]:
        """
        Process a batch of cases with VLM analysis
        
        Args:
            batch_id: Batch identifier
            case_data_list: List of cases to process
            custom_thresholds: Custom confidence thresholds
            
        Returns:
            List of ProcessingResult objects
        """
        results = []
        
        # Process in smaller batches to manage memory and API limits
        batch_size = settings.batch_size
        for i in range(0, len(case_data_list), batch_size):
            batch_cases = case_data_list[i:i + batch_size]
            
            # Process batch
            batch_results = await self._process_mini_batch(
                batch_id, batch_cases, custom_thresholds
            )
            results.extend(batch_results)
            
            # Update progress
            await self._update_progress(batch_id, len(results), len(case_data_list))
        
        return results
    
    async def _process_mini_batch(
        self,
        batch_id: str,
        case_data_list: List[CaseData],
        custom_thresholds: Optional[Dict[str, int]] = None
    ) -> List[ProcessingResult]:
        """
        Process a mini-batch of cases
        
        Args:
            batch_id: Batch identifier
            case_data_list: List of cases to process
            custom_thresholds: Custom confidence thresholds
            
        Returns:
            List of ProcessingResult objects
        """
        results = []
        
        # Prepare for VLM analysis
        image_paths = [case.image_url for case in case_data_list]
        case_numbers = [case.case_number for case in case_data_list]
        
        try:
            # Run VLM analysis
            vlm_results = await vlm_service.analyze_batch(image_paths, case_numbers)
            
            # Process results
            for case_data, vlm_result in zip(case_data_list, vlm_results):
                processing_result = await self._create_processing_result(
                    case_data, vlm_result, custom_thresholds
                )
                results.append(processing_result)
                
                # Record in database
                DatabaseOperations.record_case_result(
                    batch_id=batch_id,
                    case_number=case_data.case_number,
                    pk_event=case_data.pk_event,
                    image_url=case_data.image_url,
                    validation_status=case_data.validation_status.value,
                    vlm_results=[vlm_result.dict()],
                    final_recommendation=processing_result.final_recommendation,
                    processing_time_ms=processing_result.processing_time_total_ms
                )
            
        except Exception as e:
            logger.error(f"Failed to process mini-batch: {e}")
            
            # Create error results for failed cases
            for case_data in case_data_list:
                error_result = ProcessingResult(
                    case_number=case_data.case_number,
                    pk_event=case_data.pk_event,
                    images_processed=[{"path": case_data.image_url, "status": "failed"}],
                    vlm_results=[],
                    final_recommendation="REQUIRES_REVIEW",
                    processing_status=ProcessingStatus.FAILED,
                    processing_time_total_ms=0,
                    error_message=str(e)
                )
                results.append(error_result)
        
        return results
    
    async def _create_processing_result(
        self,
        case_data: CaseData,
        vlm_result: VLMAnalysisResult,
        custom_thresholds: Optional[Dict[str, int]] = None
    ) -> ProcessingResult:
        """
        Create processing result from case data and VLM analysis
        
        Args:
            case_data: Original case data
            vlm_result: VLM analysis result
            custom_thresholds: Custom confidence thresholds
            
        Returns:
            ProcessingResult object
        """
        # Determine final recommendation using thresholds
        threshold = self._get_threshold_for_case(vlm_result.detection_type, custom_thresholds)
        
        if vlm_result.false_positive_likelihood >= threshold:
            final_recommendation = "DISMISS_ALERT"
        else:
            final_recommendation = "REQUIRES_REVIEW"
        
        # Create processing result
        processing_result = ProcessingResult(
            case_number=case_data.case_number,
            pk_event=case_data.pk_event,
            images_processed=[{
                "path": case_data.image_url,
                "status": "completed",
                "size_mb": Path(case_data.image_url).stat().st_size / (1024 * 1024)
            }],
            vlm_results=[vlm_result],
            final_recommendation=final_recommendation,
            processing_status=ProcessingStatus.COMPLETED,
            processing_time_total_ms=vlm_result.processing_time_ms or 0,
            error_message=None
        )
        
        return processing_result
    
    def _get_threshold_for_case(self, detection_type: str, 
                               custom_thresholds: Optional[Dict[str, int]] = None) -> int:
        """
        Get confidence threshold for a specific detection type
        
        Args:
            detection_type: Type of detection
            custom_thresholds: Custom threshold overrides
            
        Returns:
            Confidence threshold value
        """
        if custom_thresholds:
            return custom_thresholds.get(detection_type, settings.threshold_default)
        
        # Map detection types to threshold categories
        threshold_mapping = {
            "STRUCTURE_MISIDENTIFIED": settings.threshold_structure_misid,
            "EQUIPMENT_MISIDENTIFIED": settings.threshold_structure_misid,
            "HUMAN_DETECTED": settings.threshold_proper_ppe,
            "GENUINE_VIOLATION": settings.threshold_no_violation
        }
        
        return threshold_mapping.get(detection_type, settings.threshold_default)
    
    async def _update_progress(self, batch_id: str, processed: int, total: int) -> None:
        """
        Update batch processing progress
        
        Args:
            batch_id: Batch identifier
            processed: Number of processed cases
            total: Total number of cases
        """
        if batch_id in self.active_batches:
            self.active_batches[batch_id]["processed_cases"] = processed
            
            # Call progress callbacks
            if batch_id in self.progress_callbacks:
                progress_data = {
                    "batch_id": batch_id,
                    "processed": processed,
                    "total": total,
                    "percentage": (processed / total) * 100 if total > 0 else 0
                }
                
                for callback in self.progress_callbacks[batch_id]:
                    try:
                        await callback(progress_data)
                    except Exception as e:
                        logger.error(f"Progress callback error: {e}")
    
    def _calculate_batch_summary(self, results: List[ProcessingResult], 
                                validation_errors: List[str]) -> Dict[str, Any]:
        """
        Calculate summary statistics for batch processing
        
        Args:
            results: List of processing results
            validation_errors: List of validation errors
            
        Returns:
            Summary statistics dictionary
        """
        total_cases = len(results)
        completed_cases = len([r for r in results if r.processing_status == ProcessingStatus.COMPLETED])
        failed_cases = len([r for r in results if r.processing_status == ProcessingStatus.FAILED])
        
        dismissed_alerts = len([r for r in results if r.final_recommendation == "DISMISS_ALERT"])
        review_required = len([r for r in results if r.final_recommendation == "REQUIRES_REVIEW"])
        
        # Calculate processing times
        processing_times = [r.processing_time_total_ms for r in results if r.processing_time_total_ms > 0]
        avg_processing_time = sum(processing_times) / len(processing_times) if processing_times else 0
        
        # Calculate confidence scores
        confidence_scores = []
        for result in results:
            for vlm_result in result.vlm_results:
                confidence_scores.append(vlm_result.confidence_score)
        
        avg_confidence = sum(confidence_scores) / len(confidence_scores) if confidence_scores else 0
        
        return {
            "total_cases": total_cases,
            "completed_cases": completed_cases,
            "failed_cases": failed_cases,
            "dismissed_alerts": dismissed_alerts,
            "review_required": review_required,
            "filter_rate_percentage": (dismissed_alerts / total_cases) * 100 if total_cases > 0 else 0,
            "avg_processing_time_ms": avg_processing_time,
            "avg_confidence_score": avg_confidence,
            "validation_errors_count": len(validation_errors),
            "validation_errors": validation_errors[:10]  # Limit to first 10 errors
        }
    
    def get_batch_status(self, batch_id: str) -> Optional[Dict[str, Any]]:
        """
        Get current status of a batch
        
        Args:
            batch_id: Batch identifier
            
        Returns:
            Batch status dictionary or None if not found
        """
        # Check active batches
        if batch_id in self.active_batches:
            return self.active_batches[batch_id]
        
        # Check database
        return DatabaseOperations.get_batch_progress(batch_id)


# Global batch processor instance
batch_processor = BatchProcessor()