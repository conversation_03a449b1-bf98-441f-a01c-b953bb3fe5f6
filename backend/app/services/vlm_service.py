"""
VLM Service - OpenAI Compatible API Integration
Handles communication with Vision Language Model API for image analysis
"""

import httpx
import asyncio
import base64
import hashlib
import logging
from typing import Dict, Any, Optional, List
from pathlib import Path
from PIL import Image
import io
import time

from ..core.config import settings
from ..models.schemas import VLMAnalysisResult
from .redis_service import redis_service


logger = logging.getLogger(__name__)


class VLMService:
    """Service for interacting with OpenAI-compatible VLM API with flexible endpoint switching"""

    def __init__(self):
        self.base_url = settings.vlm_api_base_url.rstrip('/')
        self.api_key = settings.vlm_api_key
        self.model_name = settings.vlm_model_name
        self.timeout = settings.vlm_timeout_seconds
        self.max_tokens = settings.vlm_max_tokens
        self.temperature = settings.vlm_temperature

        # Rate limiting
        self.semaphore = asyncio.Semaphore(settings.max_concurrent_requests)

        # Keep-alive mechanism for Friendli AI (DISABLED to allow endpoint sleep and save costs)
        self.last_keepalive = 0
        self.keepalive_interval = 15 * 60  # 15 minutes in seconds
        self.is_friendli_api = "friendli.ai" in self.base_url.lower()

        # VLM endpoint configuration
        self.primary_endpoint = getattr(settings, 'vlm_primary_endpoint', self.base_url)
        self.fallback_endpoint = getattr(settings, 'vlm_fallback_endpoint', None)
        self.current_endpoint = self.base_url

        logger.info(f"VLM Service initialized with endpoint: {self.base_url}")
        logger.info(f"Keep-alive disabled to allow endpoint sleep and save costs")

        # Keep-alive task disabled - endpoint will sleep after 5 minutes of inactivity
        # This allows the Friendli AI endpoint to go to sleep and save costs
        # if self.is_friendli_api:
        #     try:
        #         asyncio.create_task(self._start_keepalive_task())
        #     except RuntimeError:
        #         # No event loop running (e.g., during testing)
        #         pass

    async def _start_keepalive_task(self):
        """Start the keep-alive task for Friendli AI endpoint"""
        try:
            while True:
                await asyncio.sleep(self.keepalive_interval)
                await self._send_keepalive()
        except Exception as e:
            logger.error(f"Keep-alive task error: {e}")

    async def _send_keepalive(self):
        """Send a lightweight keep-alive request to prevent endpoint sleep"""
        try:
            current_time = time.time()
            if current_time - self.last_keepalive < self.keepalive_interval:
                return

            logger.info("Sending VLM keep-alive request...")

            async with httpx.AsyncClient(timeout=10.0) as client:
                headers = self._get_headers()

                # Lightweight keep-alive request
                payload = {
                    "model": self.model_name,
                    "messages": [
                        {
                            "role": "user",
                            "content": "ping"
                        }
                    ],
                    "max_tokens": 5,
                    "stream": False
                }

                response = await client.post(
                    f"{self.base_url}/chat/completions",
                    json=payload,
                    headers=headers
                )

                if response.status_code == 200:
                    self.last_keepalive = current_time
                    logger.info("VLM keep-alive successful")
                else:
                    logger.warning(f"VLM keep-alive failed: {response.status_code}")

        except Exception as e:
            logger.error(f"Keep-alive request failed: {e}")

    def _get_headers(self) -> Dict[str, str]:
        """Get appropriate headers for the current VLM endpoint"""
        headers = {"Content-Type": "application/json"}

        if self.is_friendli_api:
            # Friendli AI uses Bearer token
            headers["Authorization"] = f"Bearer {self.api_key}"
        else:
            # Original VLM server uses API key header
            headers["Authorization"] = f"Bearer {self.api_key}"

        return headers

    async def switch_endpoint(self, endpoint_url: str, api_key: str = None):
        """Switch to a different VLM endpoint at runtime"""
        try:
            old_endpoint = self.current_endpoint
            self.current_endpoint = endpoint_url.rstrip('/')
            self.base_url = self.current_endpoint

            if api_key:
                self.api_key = api_key

            # Update Friendli AI detection
            self.is_friendli_api = "friendli.ai" in self.base_url.lower()

            logger.info(f"Switched VLM endpoint from {old_endpoint} to {self.current_endpoint}")

            # Test the new endpoint
            health_status = await self.health_check()
            if health_status.get("status") == "connected":
                logger.info("New VLM endpoint is healthy")
                return True
            else:
                logger.warning("New VLM endpoint health check failed")
                return False

        except Exception as e:
            logger.error(f"Failed to switch VLM endpoint: {e}")
            return False

    async def analyze_image(
        self,
        image_path: str,
        case_number: str,
        custom_prompt: Optional[str] = None
    ) -> VLMAnalysisResult:
        """
        Analyze a single violation image using VLM API with Redis caching

        Args:
            image_path: Path to the image file
            case_number: Case number for tracking
            custom_prompt: Optional custom prompt override

        Returns:
            VLMAnalysisResult with analysis results
        """
        async with self.semaphore:
            start_time = time.time()

            try:
                # Prepare prompt
                prompt = custom_prompt or self._get_standard_prompt()

                # Generate image hash for caching
                image_hash = await self._generate_image_hash(image_path)

                # Check Redis cache first
                cached_result = await redis_service.get_vlm_cache(image_hash, self.model_name, prompt)
                if cached_result:
                    logger.info(f"VLM cache hit for case {case_number} (hash: {image_hash[:8]}...)")
                    # Convert cached result back to VLMAnalysisResult
                    return VLMAnalysisResult(**cached_result)

                # Cache miss - proceed with API call
                logger.debug(f"VLM cache miss for case {case_number} (hash: {image_hash[:8]}...)")

                # Encode image to base64
                image_b64 = await self._encode_image(image_path)

                # Make API request
                response_data = await self._make_api_request(image_b64, prompt, case_number)

                # Process response
                processing_time = int((time.time() - start_time) * 1000)
                result = self._parse_vlm_response(response_data, processing_time)

                # Cache the result for future use
                await redis_service.set_vlm_cache(
                    image_hash,
                    self.model_name,
                    prompt,
                    result.dict()
                )

                logger.info(f"VLM analysis completed for case {case_number} in {processing_time}ms")
                return result

            except Exception as e:
                logger.error(f"VLM analysis failed for case {case_number}: {str(e)}")
                raise
    
    async def analyze_batch(
        self, 
        image_paths: List[str], 
        case_numbers: List[str],
        custom_prompt: Optional[str] = None
    ) -> List[VLMAnalysisResult]:
        """
        Analyze multiple images in batch with rate limiting
        
        Args:
            image_paths: List of image file paths
            case_numbers: List of corresponding case numbers
            custom_prompt: Optional custom prompt override
            
        Returns:
            List of VLMAnalysisResult objects
        """
        if len(image_paths) != len(case_numbers):
            raise ValueError("Number of image paths must match number of case numbers")
        
        tasks = []
        for image_path, case_number in zip(image_paths, case_numbers):
            task = self.analyze_image(image_path, case_number, custom_prompt)
            tasks.append(task)
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Handle exceptions in results
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"Failed to process case {case_numbers[i]}: {str(result)}")
                # Create error result
                error_result = VLMAnalysisResult(
                    detection_type="ERROR",
                    false_positive_likelihood=0,
                    true_violation_likelihood=0,
                    reasoning=f"Processing failed: {str(result)}",
                    recommendation="REQUIRES_REVIEW",
                    confidence_score=0.0,
                    processing_time_ms=0
                )
                processed_results.append(error_result)
            else:
                processed_results.append(result)
        
        return processed_results
    
    async def _generate_image_hash(self, image_path: str) -> str:
        """
        Generate a hash for the image file for caching purposes

        Args:
            image_path: Path to the image file

        Returns:
            SHA256 hash of the image file
        """
        try:
            with open(image_path, 'rb') as f:
                image_data = f.read()
                return hashlib.sha256(image_data).hexdigest()
        except Exception as e:
            logger.error(f"Failed to generate hash for image {image_path}: {str(e)}")
            # Fallback to path-based hash if file reading fails
            return hashlib.sha256(image_path.encode()).hexdigest()

    async def _encode_image(self, image_path: str) -> str:
        """
        Encode image to base64 with size optimization
        
        Args:
            image_path: Path to image file
            
        Returns:
            Base64 encoded image string
        """
        try:
            image_path = Path(image_path)
            
            if not image_path.exists():
                raise FileNotFoundError(f"Image file not found: {image_path}")
            
            # Check file size
            file_size_mb = image_path.stat().st_size / (1024 * 1024)
            if file_size_mb > settings.image_max_size_mb:
                logger.warning(f"Image {image_path} is {file_size_mb:.1f}MB, compressing...")
                return await self._compress_and_encode_image(image_path)
            
            # Read and encode image
            with open(image_path, "rb") as image_file:
                image_data = image_file.read()
                return base64.b64encode(image_data).decode('utf-8')
                
        except Exception as e:
            logger.error(f"Failed to encode image {image_path}: {str(e)}")
            raise
    
    async def _compress_and_encode_image(self, image_path: Path) -> str:
        """
        Compress image if it's too large and encode to base64
        
        Args:
            image_path: Path to image file
            
        Returns:
            Base64 encoded compressed image
        """
        try:
            with Image.open(image_path) as img:
                # Convert to RGB if necessary
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                
                # Resize if too large (maintain aspect ratio)
                max_dimension = 1920
                if max(img.size) > max_dimension:
                    ratio = max_dimension / max(img.size)
                    new_size = tuple(int(dim * ratio) for dim in img.size)
                    img = img.resize(new_size, Image.Resampling.LANCZOS)
                
                # Save to bytes with compression
                buffer = io.BytesIO()
                img.save(buffer, format='JPEG', quality=85, optimize=True)
                image_data = buffer.getvalue()
                
                return base64.b64encode(image_data).decode('utf-8')
                
        except Exception as e:
            logger.error(f"Failed to compress image {image_path}: {str(e)}")
            raise
    
    async def _make_api_request(self, image_b64: str, prompt: str, case_number: str) -> Dict[str, Any]:
        """
        Make API request to VLM endpoint
        
        Args:
            image_b64: Base64 encoded image
            prompt: Analysis prompt
            case_number: Case number for tracking
            
        Returns:
            API response data
        """
        headers = self._get_headers()
        
        # OpenAI-compatible request format
        payload = {
            "model": self.model_name,
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": prompt
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/jpeg;base64,{image_b64}"
                            }
                        }
                    ]
                }
            ],
            "max_tokens": self.max_tokens,
            "temperature": self.temperature,
            "metadata": {
                "case_number": case_number,
                "service": "ai-farm"
            }
        }
        
        # Handle URL construction properly - check if base_url already ends with /v1
        if self.base_url.endswith('/v1'):
            api_url = f"{self.base_url}/chat/completions"
        else:
            api_url = f"{self.base_url}/v1/chat/completions"
        
        async with httpx.AsyncClient(timeout=self.timeout) as client:
            response = await client.post(
                api_url,
                headers=headers,
                json=payload
            )
            
            if response.status_code != 200:
                raise Exception(f"VLM API request failed: {response.status_code} - {response.text}")
            
            return response.json()
    
    def _parse_vlm_response(self, response_data: Dict[str, Any], processing_time_ms: int) -> VLMAnalysisResult:
        """
        Parse VLM API response into structured result
        
        Args:
            response_data: Raw API response
            processing_time_ms: Processing time in milliseconds
            
        Returns:
            Structured VLMAnalysisResult
        """
        try:
            # Extract content from OpenAI-compatible response
            content = response_data['choices'][0]['message']['content']
            
            # Parse the structured response (assuming JSON format)
            import json
            if content.strip().startswith('{'):
                parsed_content = json.loads(content)
            else:
                # Fallback parsing for non-JSON responses
                parsed_content = self._parse_text_response(content)
            
            # Create structured result
            result = VLMAnalysisResult(
                detection_type=parsed_content.get('detection_type', 'UNKNOWN'),
                false_positive_likelihood=parsed_content.get('false_positive_likelihood', 50),
                true_violation_likelihood=parsed_content.get('true_violation_likelihood', 50),
                reasoning=parsed_content.get('reasoning', content[:500]),
                recommendation=parsed_content.get('recommendation', 'REQUIRES_REVIEW'),
                confidence_score=parsed_content.get('confidence_score', 0.5),
                processing_time_ms=processing_time_ms
            )
            
            return result
            
        except Exception as e:
            logger.error(f"Failed to parse VLM response: {str(e)}")
            # Return fallback result
            return VLMAnalysisResult(
                detection_type="PARSE_ERROR",
                false_positive_likelihood=50,
                true_violation_likelihood=50,
                reasoning=f"Failed to parse response: {str(e)}",
                recommendation="REQUIRES_REVIEW",
                confidence_score=0.0,
                processing_time_ms=processing_time_ms
            )
    
    def _parse_text_response(self, content: str) -> Dict[str, Any]:
        """
        Parse text-based VLM response (fallback method)
        
        Args:
            content: Raw text response
            
        Returns:
            Parsed response dictionary
        """
        # Simple keyword-based parsing for fallback
        content_lower = content.lower()
        
        # Determine detection type
        if 'structure' in content_lower or 'crane' in content_lower:
            detection_type = 'STRUCTURE_MISIDENTIFIED'
        elif 'human' in content_lower or 'person' in content_lower:
            detection_type = 'HUMAN_DETECTED'
        else:
            detection_type = 'UNKNOWN'
        
        # Estimate confidence based on keywords
        confidence_keywords = ['certain', 'confident', 'clear', 'obvious']
        uncertainty_keywords = ['uncertain', 'unclear', 'possible', 'might']
        
        if any(keyword in content_lower for keyword in confidence_keywords):
            confidence = 0.8
            false_positive_likelihood = 20 if 'false' in content_lower else 80
        elif any(keyword in content_lower for keyword in uncertainty_keywords):
            confidence = 0.4
            false_positive_likelihood = 50
        else:
            confidence = 0.6
            false_positive_likelihood = 60
        
        # Determine recommendation
        if false_positive_likelihood > 70:
            recommendation = 'DISMISS_ALERT'
        else:
            recommendation = 'REQUIRES_REVIEW'
        
        return {
            'detection_type': detection_type,
            'false_positive_likelihood': false_positive_likelihood,
            'true_violation_likelihood': 100 - false_positive_likelihood,
            'reasoning': content[:500],
            'recommendation': recommendation,
            'confidence_score': confidence
        }
    
    def _get_standard_prompt(self) -> str:
        """
        Get standard VLM analysis prompt
        
        Returns:
            Standard analysis prompt
        """
        return """
        You are an expert safety violation analyst. Analyze this safety monitoring image and determine if it shows a genuine safety violation or a false positive alert.

        Please provide your analysis in the following JSON format:
        {
            "detection_type": "HUMAN_DETECTED|STRUCTURE_MISIDENTIFIED|EQUIPMENT_MISIDENTIFIED|GENUINE_VIOLATION",
            "false_positive_likelihood": <0-100>,
            "true_violation_likelihood": <0-100>,
            "reasoning": "Detailed explanation of your analysis",
            "recommendation": "DISMISS_ALERT|REQUIRES_REVIEW",
            "confidence_score": <0.0-1.0>
        }

        Focus on:
        1. Is there actually a person in the image or is it equipment/structures?
        2. If there is a person, are they in a genuinely dangerous situation?
        3. Are safety protocols being followed (PPE, procedures, etc.)?
        4. Consider lighting conditions, image quality, and potential misidentifications.

        Be thorough but concise in your reasoning.
        """
    
    async def health_check(self) -> Dict[str, Any]:
        """
        Check if VLM API is accessible and get detailed status

        Returns:
            Dictionary with health status information
        """
        try:
            logger.info(f"VLM health check starting - endpoint: {self.base_url}, is_friendli: {self.is_friendli_api}")

            # For OpenAI API, test with models endpoint
            # Handle URL construction properly - check if base_url already ends with /v1
            if self.base_url.endswith('/v1'):
                models_url = f"{self.base_url}/models"
            else:
                models_url = f"{self.base_url}/v1/models"

            async with httpx.AsyncClient(timeout=10) as client:  # Increased timeout
                headers = self._get_headers()
                logger.info(f"VLM health check headers: {headers}")

                # For Friendli AI, use chat/completions endpoint instead of models
                if self.is_friendli_api:
                    test_url = f"{self.base_url}/chat/completions"
                    # Simple test request
                    test_payload = {
                        "model": self.model_name,
                        "messages": [{"role": "user", "content": "test"}],
                        "max_tokens": 1
                    }
                    logger.info(f"Testing Friendli AI endpoint: {test_url}")
                    response = await client.post(test_url, headers=headers, json=test_payload)
                else:
                    logger.info(f"Testing standard VLM endpoint: {models_url}")
                    response = await client.get(models_url, headers=headers)
                logger.info(f"VLM health check response: {response.status_code}")
                if response.status_code == 200:
                    logger.info("VLM health check successful")
                    return {
                        "status": "connected",
                        "response_time_ms": response.elapsed.total_seconds() * 1000,
                        "api_version": response.headers.get("X-API-Version", "friendli-ai" if self.is_friendli_api else "unknown"),
                        "error": None
                    }
                else:
                    logger.warning(f"VLM health check failed: {response.status_code} - {response.text}")
                    return {
                        "status": "error",
                        "response_time_ms": response.elapsed.total_seconds() * 1000,
                        "api_version": None,
                        "error": f"HTTP {response.status_code}: {response.text}"
                    }
        except Exception as e:
            logger.error(f"VLM health check exception: {e}")
            return {
                "status": "disconnected",
                "response_time_ms": None,
                "api_version": None,
                "error": str(e)
            }
    
    def generate_custom_prompt(self, customer_patterns: Optional[Dict[str, Any]] = None) -> str:
        """
        Generate customer-specific VLM prompt based on detected patterns
        
        Args:
            customer_patterns: Dictionary of customer-specific patterns
            
        Returns:
            Customized analysis prompt
        """
        base_prompt = self._get_standard_prompt()
        
        if not customer_patterns:
            return base_prompt
        
        # Add customer-specific guidance
        custom_additions = "\n\nCUSTOMER-SPECIFIC PATTERNS TO RECOGNIZE:\n"
        
        if customer_patterns.get('unique_crane_types'):
            custom_additions += f"- Crane types commonly seen: {', '.join(customer_patterns['unique_crane_types'])}\n"
        
        if customer_patterns.get('vessel_configurations'):
            custom_additions += f"- Vessel configurations: {', '.join(customer_patterns['vessel_configurations'])}\n"
        
        if customer_patterns.get('port_layout'):
            custom_additions += f"- Port layout specifics: {customer_patterns['port_layout'].get('description', '')}\n"
        
        if customer_patterns.get('lighting_patterns'):
            custom_additions += f"- Lighting conditions: {customer_patterns['lighting_patterns'].get('dominant_type', '')}\n"
        
        if customer_patterns.get('most_common_false_positive'):
            custom_additions += f"\nENHANCED ACCURACY GUIDANCE:\n"
            custom_additions += f"- Be especially alert for {customer_patterns['most_common_false_positive']} misidentifications\n"
        
        if customer_patterns.get('camera_angles'):
            custom_additions += f"- Account for {customer_patterns['camera_angles'].get('typical_angle', '')} camera perspective\n"
        
        return base_prompt + custom_additions
    
    async def get_api_usage_stats(self) -> Dict[str, Any]:
        """
        Get VLM API usage statistics
        
        Returns:
            Dictionary with usage statistics
        """
        try:
            async with httpx.AsyncClient(timeout=10) as client:
                response = await client.get(
                    f"{self.base_url}/v1/usage",
                    headers=settings.vlm_api_headers
                )
                if response.status_code == 200:
                    return response.json()
                else:
                    return {"error": f"Failed to get usage stats: {response.status_code}"}
        except Exception as e:
            return {"error": f"Failed to get usage stats: {str(e)}"}


# Global VLM service instance
vlm_service = VLMService()