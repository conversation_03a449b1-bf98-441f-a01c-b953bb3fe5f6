"""
Redis Service for Caching and Session Management
Provides caching for VLM analysis results, session management for large uploads,
and progress tracking for long-running batch jobs.
"""

import json
import logging
import hashlib
from typing import Any, Dict, List, Optional, Union
from datetime import datetime, timedelta

import redis.asyncio as redis
from redis.asyncio import Redis
from redis.exceptions import RedisError, ConnectionError

from ..core.config import settings
from ..utils.logging import get_logger

logger = get_logger(__name__)


class RedisService:
    """
    Redis service for caching and session management
    """
    
    def __init__(self):
        self.redis_client: Optional[Redis] = None
        self.enabled = settings.redis_enabled
        self.cache_ttl = settings.redis_cache_ttl
        self.session_ttl = settings.redis_session_ttl
        
    async def initialize(self) -> bool:
        """
        Initialize Redis connection
        """
        if not self.enabled:
            logger.info("Redis caching is disabled")
            return False
            
        try:
            self.redis_client = redis.from_url(
                settings.redis_url,
                max_connections=settings.redis_max_connections,
                retry_on_timeout=True,
                socket_keepalive=True,
                socket_keepalive_options={},
                health_check_interval=30
            )
            
            # Test connection
            await self.redis_client.ping()
            logger.info(f"Redis connection established: {settings.redis_url}")
            return True
            
        except ConnectionError as e:
            logger.warning(f"Redis connection failed: {e}. Caching disabled.")
            self.enabled = False
            return False
        except Exception as e:
            logger.error(f"Redis initialization error: {e}")
            self.enabled = False
            return False
    
    async def close(self):
        """
        Close Redis connection
        """
        if self.redis_client:
            await self.redis_client.close()
            logger.info("Redis connection closed")
    
    def _generate_cache_key(self, prefix: str, *args) -> str:
        """
        Generate a cache key from prefix and arguments
        """
        key_data = f"{prefix}:{':'.join(str(arg) for arg in args)}"
        return hashlib.md5(key_data.encode()).hexdigest()
    
    async def get_vlm_cache(self, image_hash: str, model_name: str, prompt: str) -> Optional[Dict[str, Any]]:
        """
        Get cached VLM analysis result
        """
        if not self.enabled or not self.redis_client:
            return None
            
        try:
            cache_key = self._generate_cache_key("vlm", image_hash, model_name, prompt)
            cached_data = await self.redis_client.get(cache_key)
            
            if cached_data:
                result = json.loads(cached_data)
                logger.debug(f"VLM cache hit for key: {cache_key}")
                return result
                
        except (RedisError, json.JSONDecodeError) as e:
            logger.warning(f"VLM cache get error: {e}")
            
        return None
    
    async def set_vlm_cache(self, image_hash: str, model_name: str, prompt: str, result: Dict[str, Any], ttl: Optional[int] = None) -> bool:
        """
        Cache VLM analysis result
        """
        if not self.enabled or not self.redis_client:
            return False
            
        try:
            cache_key = self._generate_cache_key("vlm", image_hash, model_name, prompt)
            cache_data = json.dumps(result, default=str)
            
            ttl = ttl or self.cache_ttl
            await self.redis_client.setex(cache_key, ttl, cache_data)
            
            logger.debug(f"VLM result cached with key: {cache_key}")
            return True
            
        except (RedisError, json.JSONDecodeError) as e:
            logger.warning(f"VLM cache set error: {e}")
            return False
    
    async def get_batch_progress(self, batch_id: str) -> Optional[Dict[str, Any]]:
        """
        Get batch processing progress
        """
        if not self.enabled or not self.redis_client:
            return None
            
        try:
            progress_key = f"batch_progress:{batch_id}"
            progress_data = await self.redis_client.get(progress_key)
            
            if progress_data:
                return json.loads(progress_data)
                
        except (RedisError, json.JSONDecodeError) as e:
            logger.warning(f"Batch progress get error: {e}")
            
        return None
    
    async def set_batch_progress(self, batch_id: str, progress: Dict[str, Any]) -> bool:
        """
        Set batch processing progress
        """
        if not self.enabled or not self.redis_client:
            return False
            
        try:
            progress_key = f"batch_progress:{batch_id}"
            progress_data = json.dumps(progress, default=str)
            
            # Progress data expires after 24 hours
            await self.redis_client.setex(progress_key, 86400, progress_data)
            return True
            
        except (RedisError, json.JSONEncodeError) as e:
            logger.warning(f"Batch progress set error: {e}")
            return False
    
    async def get_upload_session(self, session_id: str) -> Optional[Dict[str, Any]]:
        """
        Get upload session data
        """
        if not self.enabled or not self.redis_client:
            return None
            
        try:
            session_key = f"upload_session:{session_id}"
            session_data = await self.redis_client.get(session_key)
            
            if session_data:
                return json.loads(session_data)
                
        except (RedisError, json.JSONDecodeError) as e:
            logger.warning(f"Upload session get error: {e}")
            
        return None
    
    async def set_upload_session(self, session_id: str, session_data: Dict[str, Any]) -> bool:
        """
        Set upload session data
        """
        if not self.enabled or not self.redis_client:
            return False
            
        try:
            session_key = f"upload_session:{session_id}"
            data = json.dumps(session_data, default=str)
            
            await self.redis_client.setex(session_key, self.session_ttl, data)
            return True
            
        except (RedisError, json.JSONEncodeError) as e:
            logger.warning(f"Upload session set error: {e}")
            return False
    
    async def delete_upload_session(self, session_id: str) -> bool:
        """
        Delete upload session data
        """
        if not self.enabled or not self.redis_client:
            return False
            
        try:
            session_key = f"upload_session:{session_id}"
            await self.redis_client.delete(session_key)
            return True
            
        except RedisError as e:
            logger.warning(f"Upload session delete error: {e}")
            return False
    
    async def get_insights_cache(self, batch_id: str) -> Optional[Dict[str, Any]]:
        """
        Get cached insights data
        """
        if not self.enabled or not self.redis_client:
            return None
            
        try:
            cache_key = f"insights:{batch_id}"
            cached_data = await self.redis_client.get(cache_key)
            
            if cached_data:
                return json.loads(cached_data)
                
        except (RedisError, json.JSONDecodeError) as e:
            logger.warning(f"Insights cache get error: {e}")
            
        return None
    
    async def set_insights_cache(self, batch_id: str, insights_data: Dict[str, Any], ttl: Optional[int] = None) -> bool:
        """
        Cache insights data
        """
        if not self.enabled or not self.redis_client:
            return False
            
        try:
            cache_key = f"insights:{batch_id}"
            data = json.dumps(insights_data, default=str)
            
            ttl = ttl or self.cache_ttl
            await self.redis_client.setex(cache_key, ttl, data)
            return True
            
        except (RedisError, json.JSONDecodeError) as e:
            logger.warning(f"Insights cache set error: {e}")
            return False
    
    async def increment_counter(self, key: str, expire: Optional[int] = None) -> int:
        """
        Increment a counter and optionally set expiration
        """
        if not self.enabled or not self.redis_client:
            return 0
            
        try:
            count = await self.redis_client.incr(key)
            if expire and count == 1:  # Only set expire on first increment
                await self.redis_client.expire(key, expire)
            return count
            
        except RedisError as e:
            logger.warning(f"Counter increment error: {e}")
            return 0
    
    async def get_health_status(self) -> Dict[str, Any]:
        """
        Get Redis health status
        """
        if not self.enabled:
            return {"status": "disabled", "enabled": False}
            
        if not self.redis_client:
            return {"status": "not_initialized", "enabled": True, "connected": False}
            
        try:
            start_time = datetime.utcnow()
            await self.redis_client.ping()
            response_time = (datetime.utcnow() - start_time).total_seconds() * 1000
            
            info = await self.redis_client.info()
            
            return {
                "status": "connected",
                "enabled": True,
                "connected": True,
                "response_time_ms": response_time,
                "redis_version": info.get("redis_version"),
                "used_memory": info.get("used_memory_human"),
                "connected_clients": info.get("connected_clients"),
                "total_commands_processed": info.get("total_commands_processed")
            }
            
        except RedisError as e:
            return {
                "status": "error",
                "enabled": True,
                "connected": False,
                "error": str(e)
            }


# Global Redis service instance
redis_service = RedisService()
