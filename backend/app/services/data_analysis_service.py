"""
VALO AI-FARM Data Analysis Service
Comprehensive analysis of violation detection data with real CSV and image processing
"""

import os
import csv
import json
import logging
from typing import Dict, List, Tuple, Optional, Any
from datetime import datetime
from pathlib import Path
import pandas as pd
from PIL import Image
import numpy as np
from dataclasses import dataclass, asdict
import re
import statistics
from collections import Counter, defaultdict

logger = logging.getLogger(__name__)

@dataclass
class ViolationCase:
    """Data structure for violation case"""
    s_no: int
    alert_id: str
    case_int_id: str
    camera: str
    terminal: str
    alert_status: str
    acknowledged_by: str
    alert_start_time: str
    alert_acknowledged_timestamp: str
    reviewed_by: str
    alert_reviewed_timestamp: str
    edited_by: str
    alert_edited_timestamp: str
    type_of_infringement: str
    follow_up: str
    remarks: str
    has_images: bool = False
    source_image_path: Optional[str] = None
    cropped_image_path: Optional[str] = None
    image_classification: Optional[str] = None  # 'valid' or 'invalid'

@dataclass
class AnalysisMetrics:
    """Analysis metrics and statistics"""
    total_csv_cases: int
    total_image_sets: int
    valid_detections: int
    invalid_detections: int
    false_positive_rate: float
    filtering_effectiveness: float
    cases_with_images: int
    cases_without_images: int
    analysis_timestamp: str

class DataAnalysisService:
    """Service for analyzing VALO AI-FARM violation detection data"""
    
    def __init__(self):
        self.csv_file_path = "ai_farm_images_fixed_250703/psa_valo_violation_report_without_images_csv_2025-07-03_14-14-01.CSV"
        self.images_base_path = "ai_farm_images_fixed_250703/ai_farm_images_fixed"
        self.violation_cases: List[ViolationCase] = []
        self.analysis_metrics: Optional[AnalysisMetrics] = None
        self.image_analysis_cache: Dict[str, Dict] = {}
        
    def load_csv_data(self) -> List[ViolationCase]:
        """Load and parse CSV violation report data"""
        try:
            violation_cases = []
            
            if not os.path.exists(self.csv_file_path):
                logger.error(f"CSV file not found: {self.csv_file_path}")
                return []
            
            with open(self.csv_file_path, 'r', encoding='utf-8') as file:
                csv_reader = csv.DictReader(file)
                
                for row in csv_reader:
                    try:
                        case = ViolationCase(
                            s_no=int(row.get('S.No', 0)),
                            alert_id=row.get('Alert ID', ''),
                            case_int_id=row.get('Case Int. ID', ''),
                            camera=row.get('Camera', ''),
                            terminal=row.get('Terminal', ''),
                            alert_status=row.get('Alert Status', ''),
                            acknowledged_by=row.get('Acknowledged By', ''),
                            alert_start_time=row.get('Alert Start Time', ''),
                            alert_acknowledged_timestamp=row.get('Alert Acknowledged Timestamp', ''),
                            reviewed_by=row.get('Reviewed By', ''),
                            alert_reviewed_timestamp=row.get('Alert Reviewed Timestamp', ''),
                            edited_by=row.get('Edited By', ''),
                            alert_edited_timestamp=row.get('Alert Edited Timestamp', ''),
                            type_of_infringement=row.get('Type of Infringement', ''),
                            follow_up=row.get('Follow Up', ''),
                            remarks=row.get('Remarks', '')
                        )
                        violation_cases.append(case)
                    except Exception as e:
                        logger.warning(f"Error parsing CSV row {row.get('S.No', 'unknown')}: {e}")
                        continue
            
            logger.info(f"Loaded {len(violation_cases)} violation cases from CSV")
            return violation_cases
            
        except Exception as e:
            logger.error(f"Error loading CSV data: {e}")
            return []
    
    def scan_image_directory(self) -> Dict[str, Dict]:
        """Scan image directory and catalog available images"""
        try:
            image_catalog = {}
            
            if not os.path.exists(self.images_base_path):
                logger.error(f"Images directory not found: {self.images_base_path}")
                return {}
            
            # Scan valid and invalid directories
            for classification in ['valid', 'invalid']:
                class_dir = os.path.join(self.images_base_path, classification)
                if not os.path.exists(class_dir):
                    continue
                
                for filename in os.listdir(class_dir):
                    if filename.endswith('.JPEG'):
                        # Extract case ID from filename (e.g., V1250623121_cropped_invalid.JPEG)
                        match = re.match(r'(V\d+)_(source|cropped)_(valid|invalid)\.JPEG', filename)
                        if match:
                            case_id, image_type, status = match.groups()
                            
                            if case_id not in image_catalog:
                                image_catalog[case_id] = {
                                    'case_id': case_id,
                                    'classification': status,
                                    'source_image': None,
                                    'cropped_image': None,
                                    'has_both_images': False
                                }
                            
                            full_path = os.path.join(class_dir, filename)
                            if image_type == 'source':
                                image_catalog[case_id]['source_image'] = full_path
                            elif image_type == 'cropped':
                                image_catalog[case_id]['cropped_image'] = full_path
                            
                            # Check if we have both images
                            if (image_catalog[case_id]['source_image'] and 
                                image_catalog[case_id]['cropped_image']):
                                image_catalog[case_id]['has_both_images'] = True
            
            logger.info(f"Cataloged {len(image_catalog)} image sets")
            return image_catalog
            
        except Exception as e:
            logger.error(f"Error scanning image directory: {e}")
            return {}
    
    def match_csv_with_images(self) -> List[ViolationCase]:
        """Match CSV cases with available images"""
        try:
            # Load CSV data
            csv_cases = self.load_csv_data()
            
            # Scan images
            image_catalog = self.scan_image_directory()
            
            # Match cases with images
            matched_cases = []
            for case in csv_cases:
                case_id = case.case_int_id
                
                if case_id in image_catalog:
                    image_info = image_catalog[case_id]
                    case.has_images = True
                    case.source_image_path = image_info['source_image']
                    case.cropped_image_path = image_info['cropped_image']
                    case.image_classification = image_info['classification']
                
                matched_cases.append(case)
            
            self.violation_cases = matched_cases
            logger.info(f"Matched {len([c for c in matched_cases if c.has_images])} cases with images")
            return matched_cases
            
        except Exception as e:
            logger.error(f"Error matching CSV with images: {e}")
            return []
    
    def analyze_false_positives(self) -> Dict[str, Any]:
        """Analyze false positive patterns and detection accuracy"""
        try:
            if not self.violation_cases:
                self.match_csv_with_images()
            
            # Count cases by category
            total_cases = len(self.violation_cases)
            cases_with_images = len([c for c in self.violation_cases if c.has_images])
            cases_without_images = total_cases - cases_with_images
            
            # Count valid vs invalid detections (from images)
            valid_detections = len([c for c in self.violation_cases 
                                  if c.has_images and c.image_classification == 'valid'])
            invalid_detections = len([c for c in self.violation_cases 
                                    if c.has_images and c.image_classification == 'invalid'])
            
            # Calculate metrics
            false_positive_rate = (invalid_detections / cases_with_images * 100) if cases_with_images > 0 else 0
            filtering_effectiveness = (invalid_detections / cases_with_images * 100) if cases_with_images > 0 else 0
            
            # Analyze follow-up patterns
            follow_up_analysis = {}
            for case in self.violation_cases:
                follow_up = case.follow_up or 'Unknown'
                if follow_up not in follow_up_analysis:
                    follow_up_analysis[follow_up] = {'count': 0, 'with_images': 0}
                follow_up_analysis[follow_up]['count'] += 1
                if case.has_images:
                    follow_up_analysis[follow_up]['with_images'] += 1
            
            # Create analysis metrics
            self.analysis_metrics = AnalysisMetrics(
                total_csv_cases=total_cases,
                total_image_sets=cases_with_images,
                valid_detections=valid_detections,
                invalid_detections=invalid_detections,
                false_positive_rate=round(false_positive_rate, 2),
                filtering_effectiveness=round(filtering_effectiveness, 2),
                cases_with_images=cases_with_images,
                cases_without_images=cases_without_images,
                analysis_timestamp=datetime.now().isoformat()
            )
            
            analysis_result = {
                'metrics': asdict(self.analysis_metrics),
                'follow_up_analysis': follow_up_analysis,
                'camera_analysis': self._analyze_by_camera(),
                'temporal_analysis': self._analyze_temporal_patterns(),
                'infringement_analysis': self._analyze_infringement_types()
            }
            
            logger.info(f"Analysis complete: {false_positive_rate:.2f}% false positive rate")
            return analysis_result
            
        except Exception as e:
            logger.error(f"Error analyzing false positives: {e}")
            return {}
    
    def _analyze_by_camera(self) -> Dict[str, Any]:
        """Analyze violation patterns by camera"""
        camera_stats = {}
        
        for case in self.violation_cases:
            camera = case.camera or 'Unknown'
            if camera not in camera_stats:
                camera_stats[camera] = {
                    'total_cases': 0,
                    'with_images': 0,
                    'valid_detections': 0,
                    'invalid_detections': 0
                }
            
            camera_stats[camera]['total_cases'] += 1
            if case.has_images:
                camera_stats[camera]['with_images'] += 1
                if case.image_classification == 'valid':
                    camera_stats[camera]['valid_detections'] += 1
                elif case.image_classification == 'invalid':
                    camera_stats[camera]['invalid_detections'] += 1
        
        # Calculate accuracy rates
        for camera, stats in camera_stats.items():
            if stats['with_images'] > 0:
                stats['accuracy_rate'] = round(
                    (stats['valid_detections'] / stats['with_images']) * 100, 2
                )
            else:
                stats['accuracy_rate'] = 0
        
        return camera_stats
    
    def _analyze_temporal_patterns(self) -> Dict[str, Any]:
        """Analyze violation patterns over time"""
        temporal_stats = {}
        
        for case in self.violation_cases:
            try:
                # Extract date from alert start time
                if case.alert_start_time:
                    # Parse date (format: "Jun 1  2025 12:21 AM")
                    date_part = case.alert_start_time.split()[0:3]  # ["Jun", "1", "2025"]
                    date_key = f"{date_part[0]} {date_part[1]} {date_part[2]}"
                    
                    if date_key not in temporal_stats:
                        temporal_stats[date_key] = {
                            'total_cases': 0,
                            'with_images': 0,
                            'valid_detections': 0,
                            'invalid_detections': 0
                        }
                    
                    temporal_stats[date_key]['total_cases'] += 1
                    if case.has_images:
                        temporal_stats[date_key]['with_images'] += 1
                        if case.image_classification == 'valid':
                            temporal_stats[date_key]['valid_detections'] += 1
                        elif case.image_classification == 'invalid':
                            temporal_stats[date_key]['invalid_detections'] += 1
            except Exception:
                continue
        
        return temporal_stats
    
    def _analyze_infringement_types(self) -> Dict[str, Any]:
        """Analyze patterns by infringement type"""
        infringement_stats = {}
        
        for case in self.violation_cases:
            infringement = case.type_of_infringement or 'Unknown'
            if infringement not in infringement_stats:
                infringement_stats[infringement] = {
                    'total_cases': 0,
                    'with_images': 0,
                    'valid_detections': 0,
                    'invalid_detections': 0
                }
            
            infringement_stats[infringement]['total_cases'] += 1
            if case.has_images:
                infringement_stats[infringement]['with_images'] += 1
                if case.image_classification == 'valid':
                    infringement_stats[infringement]['valid_detections'] += 1
                elif case.image_classification == 'invalid':
                    infringement_stats[infringement]['invalid_detections'] += 1
        
        return infringement_stats
    
    def get_filtered_cases(self, filters: Dict[str, Any] = None) -> List[Dict]:
        """Get filtered violation cases based on criteria"""
        try:
            if not self.violation_cases:
                self.match_csv_with_images()
            
            filtered_cases = self.violation_cases.copy()
            
            if filters:
                # Apply filters
                if filters.get('has_images'):
                    filtered_cases = [c for c in filtered_cases if c.has_images]
                
                if filters.get('classification'):
                    classification = filters['classification']
                    filtered_cases = [c for c in filtered_cases 
                                    if c.image_classification == classification]
                
                if filters.get('follow_up'):
                    follow_up = filters['follow_up']
                    filtered_cases = [c for c in filtered_cases 
                                    if c.follow_up == follow_up]
                
                if filters.get('camera'):
                    camera = filters['camera']
                    filtered_cases = [c for c in filtered_cases 
                                    if c.camera == camera]
            
            # Convert to dict format for JSON serialization
            return [asdict(case) for case in filtered_cases]
            
        except Exception as e:
            logger.error(f"Error filtering cases: {e}")
            return []
    
    def detect_false_positive_patterns(self) -> Dict[str, Any]:
        """Advanced false positive detection using pattern analysis"""
        try:
            if not self.violation_cases:
                self.match_csv_with_images()

            patterns = {
                'camera_reliability': self._analyze_camera_reliability(),
                'temporal_anomalies': self._detect_temporal_anomalies(),
                'infringement_consistency': self._analyze_infringement_consistency(),
                'follow_up_patterns': self._analyze_follow_up_patterns(),
                'alert_duration_analysis': self._analyze_alert_durations(),
                'clustering_analysis': self._perform_clustering_analysis()
            }

            # Generate recommendations
            recommendations = self._generate_fp_recommendations(patterns)

            return {
                'patterns': patterns,
                'recommendations': recommendations,
                'confidence_scores': self._calculate_confidence_scores(patterns)
            }

        except Exception as e:
            logger.error(f"Error detecting false positive patterns: {e}")
            return {}

    def _analyze_camera_reliability(self) -> Dict[str, Any]:
        """Analyze camera reliability based on false positive rates"""
        camera_stats = defaultdict(lambda: {
            'total_alerts': 0,
            'valid_detections': 0,
            'invalid_detections': 0,
            'accuracy_rate': 0,
            'reliability_score': 0
        })

        for case in self.violation_cases:
            if case.has_images and case.camera:
                camera_stats[case.camera]['total_alerts'] += 1
                if case.image_classification == 'valid':
                    camera_stats[case.camera]['valid_detections'] += 1
                elif case.image_classification == 'invalid':
                    camera_stats[case.camera]['invalid_detections'] += 1

        # Calculate reliability scores
        for camera, stats in camera_stats.items():
            if stats['total_alerts'] > 0:
                stats['accuracy_rate'] = (stats['valid_detections'] / stats['total_alerts']) * 100
                # Reliability score considers both accuracy and volume
                volume_factor = min(stats['total_alerts'] / 50, 1.0)  # Normalize to 50 alerts
                stats['reliability_score'] = stats['accuracy_rate'] * volume_factor

        # Identify problematic cameras
        problematic_cameras = {
            camera: stats for camera, stats in camera_stats.items()
            if stats['accuracy_rate'] < 10 and stats['total_alerts'] > 10
        }

        return {
            'camera_stats': dict(camera_stats),
            'problematic_cameras': problematic_cameras,
            'total_cameras': len(camera_stats),
            'avg_accuracy': statistics.mean([s['accuracy_rate'] for s in camera_stats.values()]) if camera_stats else 0
        }

    def _detect_temporal_anomalies(self) -> Dict[str, Any]:
        """Detect temporal patterns that indicate false positives"""
        hourly_stats = defaultdict(lambda: {'total': 0, 'valid': 0, 'invalid': 0})
        daily_stats = defaultdict(lambda: {'total': 0, 'valid': 0, 'invalid': 0})

        for case in self.violation_cases:
            if case.has_images and case.alert_start_time:
                try:
                    # Parse time (format: "Jun 1  2025 12:21 AM")
                    time_parts = case.alert_start_time.split()
                    if len(time_parts) >= 4:
                        hour_part = time_parts[3]  # "12:21"
                        am_pm = time_parts[4] if len(time_parts) > 4 else "AM"

                        hour = int(hour_part.split(':')[0])
                        if am_pm == "PM" and hour != 12:
                            hour += 12
                        elif am_pm == "AM" and hour == 12:
                            hour = 0

                        day_key = f"{time_parts[0]} {time_parts[1]} {time_parts[2]}"

                        hourly_stats[hour]['total'] += 1
                        daily_stats[day_key]['total'] += 1

                        if case.image_classification == 'valid':
                            hourly_stats[hour]['valid'] += 1
                            daily_stats[day_key]['valid'] += 1
                        elif case.image_classification == 'invalid':
                            hourly_stats[hour]['invalid'] += 1
                            daily_stats[day_key]['invalid'] += 1

                except Exception:
                    continue

        # Identify anomalous hours (high false positive rates)
        anomalous_hours = {}
        for hour, stats in hourly_stats.items():
            if stats['total'] > 5:  # Minimum threshold
                fp_rate = (stats['invalid'] / stats['total']) * 100
                if fp_rate > 95:  # Very high false positive rate
                    anomalous_hours[hour] = {
                        'fp_rate': fp_rate,
                        'total_alerts': stats['total']
                    }

        return {
            'hourly_stats': dict(hourly_stats),
            'daily_stats': dict(daily_stats),
            'anomalous_hours': anomalous_hours,
            'peak_hours': sorted(hourly_stats.items(), key=lambda x: x[1]['total'], reverse=True)[:5]
        }

    def _analyze_infringement_consistency(self) -> Dict[str, Any]:
        """Analyze consistency of infringement type classifications"""
        infringement_stats = defaultdict(lambda: {
            'total': 0,
            'valid': 0,
            'invalid': 0,
            'cameras': set(),
            'follow_ups': Counter()
        })

        for case in self.violation_cases:
            if case.has_images:
                infringement = case.type_of_infringement or 'Unknown'
                infringement_stats[infringement]['total'] += 1
                infringement_stats[infringement]['cameras'].add(case.camera)
                infringement_stats[infringement]['follow_ups'][case.follow_up] += 1

                if case.image_classification == 'valid':
                    infringement_stats[infringement]['valid'] += 1
                elif case.image_classification == 'invalid':
                    infringement_stats[infringement]['invalid'] += 1

        # Calculate consistency scores
        for infringement, stats in infringement_stats.items():
            if stats['total'] > 0:
                stats['accuracy_rate'] = (stats['valid'] / stats['total']) * 100
                stats['camera_diversity'] = len(stats['cameras'])
                stats['cameras'] = list(stats['cameras'])  # Convert set to list for JSON serialization
                stats['follow_ups'] = dict(stats['follow_ups'])

        return dict(infringement_stats)

    def get_analysis_summary(self) -> Dict[str, Any]:
        """Get comprehensive analysis summary"""
        try:
            if not self.analysis_metrics:
                self.analyze_false_positives()

            return {
                'metrics': asdict(self.analysis_metrics) if self.analysis_metrics else {},
                'data_sources': {
                    'csv_file': self.csv_file_path,
                    'images_directory': self.images_base_path,
                    'csv_exists': os.path.exists(self.csv_file_path),
                    'images_exist': os.path.exists(self.images_base_path)
                },
                'processing_status': {
                    'csv_loaded': len(self.violation_cases) > 0,
                    'images_scanned': True,
                    'analysis_complete': self.analysis_metrics is not None
                }
            }

        except Exception as e:
            logger.error(f"Error getting analysis summary: {e}")
            return {}

    def _analyze_follow_up_patterns(self) -> Dict[str, Any]:
        """Analyze follow-up patterns to identify systematic issues"""
        follow_up_stats = defaultdict(lambda: {
            'total': 0,
            'valid': 0,
            'invalid': 0,
            'cameras': Counter(),
            'infringements': Counter()
        })

        for case in self.violation_cases:
            if case.has_images:
                follow_up = case.follow_up or 'Unknown'
                follow_up_stats[follow_up]['total'] += 1
                follow_up_stats[follow_up]['cameras'][case.camera] += 1
                follow_up_stats[follow_up]['infringements'][case.type_of_infringement] += 1

                if case.image_classification == 'valid':
                    follow_up_stats[follow_up]['valid'] += 1
                elif case.image_classification == 'invalid':
                    follow_up_stats[follow_up]['invalid'] += 1

        # Convert Counters to dicts for JSON serialization
        for follow_up, stats in follow_up_stats.items():
            if stats['total'] > 0:
                stats['accuracy_rate'] = (stats['valid'] / stats['total']) * 100
                stats['cameras'] = dict(stats['cameras'])
                stats['infringements'] = dict(stats['infringements'])

        return dict(follow_up_stats)

    def _analyze_alert_durations(self) -> Dict[str, Any]:
        """Analyze alert duration patterns"""
        duration_stats = {
            'quick_dismissals': 0,  # Alerts dismissed very quickly (likely false positives)
            'normal_processing': 0,
            'delayed_processing': 0,
            'avg_processing_time': 0
        }

        processing_times = []

        for case in self.violation_cases:
            if case.alert_start_time and case.alert_acknowledged_timestamp:
                try:
                    # This would require proper datetime parsing
                    # For now, we'll use a simplified approach
                    if case.alert_status == 'Invalid' and case.remarks == 'False Positive':
                        duration_stats['quick_dismissals'] += 1
                    else:
                        duration_stats['normal_processing'] += 1
                except Exception:
                    continue

        return duration_stats

    def _perform_clustering_analysis(self) -> Dict[str, Any]:
        """Perform clustering analysis to identify patterns"""
        # Group similar cases by camera, infringement type, and time patterns
        clusters = defaultdict(list)

        for case in self.violation_cases:
            if case.has_images:
                # Create a cluster key based on camera and infringement type
                cluster_key = f"{case.camera}_{case.type_of_infringement}"
                clusters[cluster_key].append({
                    'case_id': case.case_int_id,
                    'classification': case.image_classification,
                    'alert_status': case.alert_status,
                    'follow_up': case.follow_up
                })

        # Analyze clusters
        cluster_analysis = {}
        for cluster_key, cases in clusters.items():
            if len(cases) >= 5:  # Minimum cluster size
                valid_count = sum(1 for c in cases if c['classification'] == 'valid')
                invalid_count = sum(1 for c in cases if c['classification'] == 'invalid')

                cluster_analysis[cluster_key] = {
                    'total_cases': len(cases),
                    'valid_cases': valid_count,
                    'invalid_cases': invalid_count,
                    'false_positive_rate': (invalid_count / len(cases)) * 100 if cases else 0,
                    'pattern_strength': 'high' if invalid_count / len(cases) > 0.9 else 'medium' if invalid_count / len(cases) > 0.7 else 'low'
                }

        return {
            'clusters': cluster_analysis,
            'total_clusters': len(cluster_analysis),
            'high_fp_clusters': len([c for c in cluster_analysis.values() if c['false_positive_rate'] > 90])
        }

    def _generate_fp_recommendations(self, patterns: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate recommendations based on false positive patterns"""
        recommendations = []

        # Camera-based recommendations
        camera_patterns = patterns.get('camera_reliability', {})
        problematic_cameras = camera_patterns.get('problematic_cameras', {})

        if problematic_cameras:
            recommendations.append({
                'type': 'camera_adjustment',
                'priority': 'high',
                'title': 'Problematic Camera Detection',
                'description': f'Found {len(problematic_cameras)} cameras with accuracy rates below 10%',
                'action': 'Review camera positioning, calibration, and detection thresholds',
                'affected_cameras': list(problematic_cameras.keys())[:5]
            })

        # Temporal recommendations
        temporal_patterns = patterns.get('temporal_anomalies', {})
        anomalous_hours = temporal_patterns.get('anomalous_hours', {})

        if anomalous_hours:
            recommendations.append({
                'type': 'temporal_adjustment',
                'priority': 'medium',
                'title': 'Time-based False Positive Patterns',
                'description': f'High false positive rates detected during {len(anomalous_hours)} specific hours',
                'action': 'Adjust detection sensitivity during identified time periods',
                'affected_hours': list(anomalous_hours.keys())
            })

        # Clustering recommendations
        clustering_patterns = patterns.get('clustering_analysis', {})
        high_fp_clusters = clustering_patterns.get('high_fp_clusters', 0)

        if high_fp_clusters > 0:
            recommendations.append({
                'type': 'pattern_based_filtering',
                'priority': 'high',
                'title': 'Systematic False Positive Patterns',
                'description': f'Identified {high_fp_clusters} camera-infringement combinations with >90% false positive rates',
                'action': 'Implement pattern-based pre-filtering for identified combinations',
                'impact': 'Could reduce false positives by 30-50%'
            })

        return recommendations

    def _calculate_confidence_scores(self, patterns: Dict[str, Any]) -> Dict[str, float]:
        """Calculate confidence scores for different analysis aspects"""
        scores = {}

        # Camera reliability confidence
        camera_patterns = patterns.get('camera_reliability', {})
        total_cameras = camera_patterns.get('total_cameras', 0)
        if total_cameras > 0:
            scores['camera_analysis'] = min(total_cameras / 20, 1.0)  # Higher confidence with more cameras

        # Temporal analysis confidence
        temporal_patterns = patterns.get('temporal_anomalies', {})
        hourly_data_points = len(temporal_patterns.get('hourly_stats', {}))
        if hourly_data_points > 0:
            scores['temporal_analysis'] = min(hourly_data_points / 24, 1.0)  # Full day coverage

        # Overall confidence
        scores['overall'] = statistics.mean(scores.values()) if scores else 0.0

        return scores

# Global service instance
data_analysis_service = DataAnalysisService()
