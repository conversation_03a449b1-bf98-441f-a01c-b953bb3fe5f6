"""
AI-FARM Auto-Learning Engine
Implements pattern detection, threshold optimization, and customer-specific adaptations
"""

import logging
import numpy as np
from collections import Counter, defaultdict
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from sklearn.metrics import accuracy_score, precision_recall_curve
import json

from ..core.config import settings
from ..core.database import DatabaseOperations
from ..models.schemas import (
    AutoLearningInsights, CustomerEnvironmentAnalysis, 
    ProcessingResult, VLMAnalysisResult, ValidationStatus
)

logger = logging.getLogger(__name__)


class PatternDetector:
    """Detects customer-specific false positive patterns"""
    
    def __init__(self):
        self.structure_keywords = [
            'crane', 'boom', 'mast', 'tower', 'pole', 'beam', 'frame',
            'container', 'vessel', 'ship', 'structure', 'equipment'
        ]
        self.lighting_keywords = [
            'bright', 'dark', 'shadow', 'silhouette', 'backlit', 'glare',
            'sunset', 'sunrise', 'night', 'daylight', 'artificial'
        ]
        self.ppe_keywords = [
            'helmet', 'vest', 'harness', 'boots', 'gloves', 'mask',
            'safety', 'protective', 'high-vis', 'reflective'
        ]
    
    def analyze_customer_patterns(self, processing_results: List[ProcessingResult]) -> Dict[str, Any]:
        """
        Analyze customer-specific patterns from processing results
        
        Args:
            processing_results: List of processing results to analyze
            
        Returns:
            Dictionary containing detected patterns
        """
        try:
            patterns = {
                'common_structures': [],
                'false_positive_types': [],
                'lighting_conditions': [],
                'camera_angles': [],
                'equipment_types': [],
                'detection_patterns': {}
            }
            
            # Analyze VLM results for patterns
            for result in processing_results:
                for vlm_result in result.vlm_results:
                    self._extract_patterns_from_reasoning(vlm_result.reasoning, patterns)
                    self._analyze_detection_type_patterns(vlm_result, patterns)
            
            # Process and rank patterns
            processed_patterns = self._process_detected_patterns(patterns)
            
            logger.info(f"Detected patterns from {len(processing_results)} processing results")
            return processed_patterns
            
        except Exception as e:
            logger.error(f"Failed to analyze customer patterns: {e}")
            return {}
    
    def _extract_patterns_from_reasoning(self, reasoning: str, patterns: Dict[str, Any]) -> None:
        """
        Extract patterns from VLM reasoning text
        
        Args:
            reasoning: VLM reasoning text
            patterns: Patterns dictionary to update
        """
        reasoning_lower = reasoning.lower()
        
        # Extract structure mentions
        for keyword in self.structure_keywords:
            if keyword in reasoning_lower:
                patterns['common_structures'].append(keyword)
        
        # Extract lighting conditions
        for keyword in self.lighting_keywords:
            if keyword in reasoning_lower:
                patterns['lighting_conditions'].append(keyword)
        
        # Extract equipment types
        for keyword in self.ppe_keywords:
            if keyword in reasoning_lower:
                patterns['equipment_types'].append(keyword)
    
    def _analyze_detection_type_patterns(self, vlm_result: VLMAnalysisResult, 
                                       patterns: Dict[str, Any]) -> None:
        """
        Analyze detection type patterns
        
        Args:
            vlm_result: VLM analysis result
            patterns: Patterns dictionary to update
        """
        detection_type = vlm_result.detection_type
        
        # Track detection types
        if detection_type not in patterns['detection_patterns']:
            patterns['detection_patterns'][detection_type] = {
                'count': 0,
                'avg_confidence': 0,
                'false_positive_rate': 0
            }
        
        patterns['detection_patterns'][detection_type]['count'] += 1
        
        # Track false positive types
        if vlm_result.false_positive_likelihood > 70:
            patterns['false_positive_types'].append(detection_type)
    
    def _process_detected_patterns(self, raw_patterns: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process and rank detected patterns
        
        Args:
            raw_patterns: Raw pattern data
            
        Returns:
            Processed pattern analysis
        """
        processed = {
            'most_common_structures': Counter(raw_patterns['common_structures']).most_common(5),
            'most_common_false_positives': Counter(raw_patterns['false_positive_types']).most_common(5),
            'dominant_lighting': Counter(raw_patterns['lighting_conditions']).most_common(3),
            'equipment_patterns': Counter(raw_patterns['equipment_types']).most_common(5),
            'detection_type_distribution': raw_patterns['detection_patterns']
        }
        
        # Calculate summary insights
        processed['primary_false_positive_cause'] = (
            processed['most_common_false_positives'][0][0] 
            if processed['most_common_false_positives'] else 'UNKNOWN'
        )
        
        processed['dominant_lighting_condition'] = (
            processed['dominant_lighting'][0][0] 
            if processed['dominant_lighting'] else 'UNKNOWN'
        )
        
        return processed


class ThresholdOptimizer:
    """Optimizes confidence thresholds based on customer data"""
    
    def __init__(self):
        self.threshold_range = range(50, 95, 5)  # Test thresholds from 50% to 90%
        self.min_samples_required = 10  # Minimum samples needed for optimization
    
    def optimize_thresholds(self, processing_results: List[ProcessingResult],
                          ground_truth_validation: Optional[Dict[str, str]] = None) -> Dict[str, Any]:
        """
        Optimize confidence thresholds based on processing results
        
        Args:
            processing_results: List of processing results
            ground_truth_validation: Optional ground truth validation data
            
        Returns:
            Dictionary containing optimized thresholds and performance metrics
        """
        try:
            if len(processing_results) < self.min_samples_required:
                logger.warning(f"Insufficient data for threshold optimization: {len(processing_results)} samples")
                return self._get_default_thresholds()
            
            # Extract data for optimization
            optimization_data = self._prepare_optimization_data(processing_results, ground_truth_validation)
            
            if not optimization_data:
                return self._get_default_thresholds()
            
            # Optimize thresholds by detection type
            optimized_thresholds = {}
            performance_metrics = {}
            
            for detection_type, data in optimization_data.items():
                if len(data['scores']) >= self.min_samples_required:
                    optimal_threshold, metrics = self._optimize_single_threshold(
                        data['scores'], data['labels']
                    )
                    optimized_thresholds[detection_type] = optimal_threshold
                    performance_metrics[detection_type] = metrics
                else:
                    # Use default threshold if insufficient data
                    optimized_thresholds[detection_type] = settings.get_threshold_for_category(detection_type)
            
            # Calculate overall improvement
            improvement_metrics = self._calculate_improvement_metrics(
                processing_results, optimized_thresholds
            )
            
            result = {
                'optimized_thresholds': optimized_thresholds,
                'performance_metrics': performance_metrics,
                'improvement_metrics': improvement_metrics,
                'sample_sizes': {dt: len(data['scores']) for dt, data in optimization_data.items()},
                'optimization_successful': len(optimized_thresholds) > 0
            }
            
            logger.info(f"Threshold optimization completed for {len(optimized_thresholds)} detection types")
            return result
            
        except Exception as e:
            logger.error(f"Failed to optimize thresholds: {e}")
            return self._get_default_thresholds()
    
    def _prepare_optimization_data(self, processing_results: List[ProcessingResult],
                                 ground_truth_validation: Optional[Dict[str, str]] = None) -> Dict[str, Dict[str, List]]:
        """
        Prepare data for threshold optimization
        
        Args:
            processing_results: List of processing results
            ground_truth_validation: Optional ground truth validation data
            
        Returns:
            Dictionary organized by detection type
        """
        optimization_data = defaultdict(lambda: {'scores': [], 'labels': []})
        
        for result in processing_results:
            case_number = result.case_number
            
            # Get ground truth label
            if ground_truth_validation and case_number in ground_truth_validation:
                ground_truth = ground_truth_validation[case_number]
                is_false_positive = ground_truth.lower() in ['invalid', 'false_positive']
            else:
                # Fallback: assume high confidence VLM predictions are correct
                continue
            
            # Process VLM results
            for vlm_result in result.vlm_results:
                detection_type = vlm_result.detection_type
                confidence_score = vlm_result.false_positive_likelihood
                
                optimization_data[detection_type]['scores'].append(confidence_score)
                optimization_data[detection_type]['labels'].append(1 if is_false_positive else 0)
        
        return dict(optimization_data)
    
    def _optimize_single_threshold(self, scores: List[float], labels: List[int]) -> Tuple[int, Dict[str, float]]:
        """
        Optimize threshold for a single detection type
        
        Args:
            scores: List of confidence scores
            labels: List of ground truth labels (1 for false positive, 0 for true positive)
            
        Returns:
            Tuple of (optimal_threshold, performance_metrics)
        """
        scores_array = np.array(scores)
        labels_array = np.array(labels)
        
        best_threshold = 70  # Default
        best_f1_score = 0
        best_metrics = {}
        
        for threshold in self.threshold_range:
            # Convert scores to binary predictions
            predictions = (scores_array >= threshold).astype(int)
            
            # Calculate metrics
            accuracy = accuracy_score(labels_array, predictions)
            
            # Calculate precision, recall, F1
            tp = np.sum((predictions == 1) & (labels_array == 1))
            fp = np.sum((predictions == 1) & (labels_array == 0))
            fn = np.sum((predictions == 0) & (labels_array == 1))
            
            precision = tp / (tp + fp) if (tp + fp) > 0 else 0
            recall = tp / (tp + fn) if (tp + fn) > 0 else 0
            f1_score = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0
            
            # Use F1 score as optimization target
            if f1_score > best_f1_score:
                best_f1_score = f1_score
                best_threshold = threshold
                best_metrics = {
                    'accuracy': accuracy,
                    'precision': precision,
                    'recall': recall,
                    'f1_score': f1_score,
                    'threshold': threshold
                }
        
        return best_threshold, best_metrics
    
    def _calculate_improvement_metrics(self, processing_results: List[ProcessingResult],
                                     optimized_thresholds: Dict[str, int]) -> Dict[str, float]:
        """
        Calculate improvement metrics with optimized thresholds
        
        Args:
            processing_results: List of processing results
            optimized_thresholds: Dictionary of optimized thresholds
            
        Returns:
            Dictionary containing improvement metrics
        """
        # Simulate performance with default thresholds
        default_correct = 0
        optimized_correct = 0
        total_cases = 0
        
        for result in processing_results:
            for vlm_result in result.vlm_results:
                detection_type = vlm_result.detection_type
                confidence = vlm_result.false_positive_likelihood
                
                # Default threshold decision
                default_threshold = settings.get_threshold_for_category(detection_type)
                default_decision = confidence >= default_threshold
                
                # Optimized threshold decision
                optimized_threshold = optimized_thresholds.get(detection_type, default_threshold)
                optimized_decision = confidence >= optimized_threshold
                
                # Assume VLM is correct for high-confidence predictions
                likely_correct = confidence > 80 or confidence < 30
                
                if likely_correct:
                    actual_false_positive = confidence > 50
                    
                    if default_decision == actual_false_positive:
                        default_correct += 1
                    if optimized_decision == actual_false_positive:
                        optimized_correct += 1
                    
                    total_cases += 1
        
        if total_cases > 0:
            default_accuracy = default_correct / total_cases
            optimized_accuracy = optimized_correct / total_cases
            improvement = ((optimized_accuracy - default_accuracy) / default_accuracy) * 100
        else:
            default_accuracy = optimized_accuracy = improvement = 0
        
        return {
            'default_accuracy': default_accuracy,
            'optimized_accuracy': optimized_accuracy,
            'accuracy_improvement_percent': improvement,
            'total_cases_analyzed': total_cases
        }
    
    def _get_default_thresholds(self) -> Dict[str, Any]:
        """
        Get default threshold configuration
        
        Returns:
            Default threshold dictionary
        """
        return {
            'optimized_thresholds': {
                'STRUCTURE_MISIDENTIFIED': settings.threshold_structure_misid,
                'EQUIPMENT_MISIDENTIFIED': settings.threshold_structure_misid,
                'HUMAN_DETECTED': settings.threshold_proper_ppe,
                'GENUINE_VIOLATION': settings.threshold_no_violation
            },
            'performance_metrics': {},
            'improvement_metrics': {'accuracy_improvement_percent': 0},
            'sample_sizes': {},
            'optimization_successful': False
        }


class ConfidenceCalibrator:
    """Calibrates VLM confidence scores based on historical performance"""
    
    def __init__(self):
        self.calibration_bins = 10
        self.min_bin_size = 5
    
    def calibrate_confidence_scores(self, processing_results: List[ProcessingResult],
                                  ground_truth_validation: Optional[Dict[str, str]] = None) -> Dict[str, Any]:
        """
        Calibrate confidence scores based on actual performance
        
        Args:
            processing_results: List of processing results
            ground_truth_validation: Optional ground truth validation data
            
        Returns:
            Dictionary containing calibration data
        """
        try:
            if not processing_results:
                return {'calibration_successful': False, 'error': 'No data provided'}
            
            # Extract confidence and accuracy data
            confidence_accuracy_pairs = self._extract_confidence_accuracy_data(
                processing_results, ground_truth_validation
            )
            
            if len(confidence_accuracy_pairs) < 20:  # Need sufficient data
                return {'calibration_successful': False, 'error': 'Insufficient data for calibration'}
            
            # Create calibration curve
            calibration_curve = self._create_calibration_curve(confidence_accuracy_pairs)
            
            # Calculate calibration metrics
            calibration_metrics = self._calculate_calibration_metrics(confidence_accuracy_pairs)
            
            result = {
                'calibration_successful': True,
                'calibration_curve': calibration_curve,
                'calibration_metrics': calibration_metrics,
                'sample_size': len(confidence_accuracy_pairs)
            }
            
            logger.info(f"Confidence calibration completed with {len(confidence_accuracy_pairs)} samples")
            return result
            
        except Exception as e:
            logger.error(f"Failed to calibrate confidence scores: {e}")
            return {'calibration_successful': False, 'error': str(e)}
    
    def _extract_confidence_accuracy_data(self, processing_results: List[ProcessingResult],
                                        ground_truth_validation: Optional[Dict[str, str]] = None) -> List[Tuple[float, bool]]:
        """
        Extract confidence and accuracy pairs from processing results
        
        Args:
            processing_results: List of processing results
            ground_truth_validation: Optional ground truth validation data
            
        Returns:
            List of (confidence, is_correct) tuples
        """
        confidence_accuracy_pairs = []
        
        for result in processing_results:
            case_number = result.case_number
            
            # Get ground truth if available
            if ground_truth_validation and case_number in ground_truth_validation:
                ground_truth = ground_truth_validation[case_number]
                actual_false_positive = ground_truth.lower() in ['invalid', 'false_positive']
            else:
                # Skip cases without ground truth
                continue
            
            # Process VLM results
            for vlm_result in result.vlm_results:
                confidence = vlm_result.confidence_score
                predicted_false_positive = vlm_result.false_positive_likelihood > 70
                
                is_correct = predicted_false_positive == actual_false_positive
                confidence_accuracy_pairs.append((confidence, is_correct))
        
        return confidence_accuracy_pairs
    
    def _create_calibration_curve(self, confidence_accuracy_pairs: List[Tuple[float, bool]]) -> List[Dict[str, float]]:
        """
        Create calibration curve from confidence-accuracy pairs
        
        Args:
            confidence_accuracy_pairs: List of (confidence, is_correct) tuples
            
        Returns:
            List of calibration curve points
        """
        # Sort by confidence
        sorted_pairs = sorted(confidence_accuracy_pairs, key=lambda x: x[0])
        
        # Create bins
        bin_size = len(sorted_pairs) // self.calibration_bins
        calibration_curve = []
        
        for i in range(0, len(sorted_pairs), bin_size):
            bin_data = sorted_pairs[i:i + bin_size]
            
            if len(bin_data) >= self.min_bin_size:
                confidences = [pair[0] for pair in bin_data]
                accuracies = [pair[1] for pair in bin_data]
                
                avg_confidence = np.mean(confidences)
                avg_accuracy = np.mean(accuracies)
                bin_size_actual = len(bin_data)
                
                calibration_curve.append({
                    'avg_confidence': avg_confidence,
                    'avg_accuracy': avg_accuracy,
                    'bin_size': bin_size_actual,
                    'confidence_range': [min(confidences), max(confidences)]
                })
        
        return calibration_curve
    
    def _calculate_calibration_metrics(self, confidence_accuracy_pairs: List[Tuple[float, bool]]) -> Dict[str, float]:
        """
        Calculate calibration performance metrics
        
        Args:
            confidence_accuracy_pairs: List of (confidence, is_correct) tuples
            
        Returns:
            Dictionary containing calibration metrics
        """
        confidences = np.array([pair[0] for pair in confidence_accuracy_pairs])
        accuracies = np.array([pair[1] for pair in confidence_accuracy_pairs], dtype=float)
        
        # Expected Calibration Error (ECE)
        bin_boundaries = np.linspace(0, 1, self.calibration_bins + 1)
        bin_lowers = bin_boundaries[:-1]
        bin_uppers = bin_boundaries[1:]
        
        ece = 0
        for bin_lower, bin_upper in zip(bin_lowers, bin_uppers):
            in_bin = (confidences > bin_lower) & (confidences <= bin_upper)
            prop_in_bin = in_bin.mean()
            
            if prop_in_bin > 0:
                accuracy_in_bin = accuracies[in_bin].mean()
                avg_confidence_in_bin = confidences[in_bin].mean()
                ece += np.abs(avg_confidence_in_bin - accuracy_in_bin) * prop_in_bin
        
        # Overall accuracy
        overall_accuracy = accuracies.mean()
        avg_confidence = confidences.mean()
        
        return {
            'expected_calibration_error': ece,
            'overall_accuracy': overall_accuracy,
            'average_confidence': avg_confidence,
            'confidence_accuracy_gap': abs(avg_confidence - overall_accuracy)
        }


class AutoLearningEngine:
    """Main auto-learning engine that coordinates all learning components"""
    
    def __init__(self):
        self.pattern_detector = PatternDetector()
        self.threshold_optimizer = ThresholdOptimizer()
        self.confidence_calibrator = ConfidenceCalibrator()
    
    def learn_from_processing_results(
        self,
        processing_results: List[ProcessingResult],
        batch_id: str,
        ground_truth_validation: Optional[Dict[str, str]] = None
    ) -> AutoLearningInsights:
        """
        Run complete auto-learning analysis on processing results
        
        Args:
            processing_results: List of processing results
            batch_id: Batch identifier for tracking
            ground_truth_validation: Optional ground truth validation data
            
        Returns:
            AutoLearningInsights with all learning results
        """
        try:
            logger.info(f"Starting auto-learning analysis for batch {batch_id}")
            
            # Pattern detection
            detected_patterns = self.pattern_detector.analyze_customer_patterns(processing_results)
            
            # Threshold optimization
            threshold_optimization = self.threshold_optimizer.optimize_thresholds(
                processing_results, ground_truth_validation
            )
            
            # Confidence calibration
            confidence_calibration = self.confidence_calibrator.calibrate_confidence_scores(
                processing_results, ground_truth_validation
            )
            
            # Generate recommendations
            recommendations = self._generate_recommendations(
                detected_patterns, threshold_optimization, confidence_calibration
            )
            
            # Store learning results in database
            self._store_learning_results(batch_id, {
                'detected_patterns': detected_patterns,
                'threshold_optimization': threshold_optimization,
                'confidence_calibration': confidence_calibration,
                'recommendations': recommendations
            })
            
            # Create insights object
            insights = AutoLearningInsights(
                detected_patterns=detected_patterns,
                optimized_thresholds=threshold_optimization.get('optimized_thresholds', {}),
                accuracy_improvement=threshold_optimization.get('improvement_metrics', {}).get('accuracy_improvement_percent', 0),
                confidence_calibration=confidence_calibration.get('calibration_metrics', {}),
                recommendations=recommendations
            )
            
            logger.info(f"Auto-learning analysis completed for batch {batch_id}")
            return insights
            
        except Exception as e:
            logger.error(f"Auto-learning analysis failed for batch {batch_id}: {e}")
            
            # Return default insights
            return AutoLearningInsights(
                detected_patterns={},
                optimized_thresholds={},
                accuracy_improvement=0.0,
                confidence_calibration={},
                recommendations=[f"Auto-learning failed: {str(e)}"]
            )
    
    def _generate_recommendations(self, detected_patterns: Dict[str, Any],
                                threshold_optimization: Dict[str, Any],
                                confidence_calibration: Dict[str, Any]) -> List[str]:
        """
        Generate actionable recommendations based on learning results
        
        Args:
            detected_patterns: Pattern detection results
            threshold_optimization: Threshold optimization results
            confidence_calibration: Confidence calibration results
            
        Returns:
            List of recommendation strings
        """
        recommendations = []
        
        # Pattern-based recommendations
        if detected_patterns.get('primary_false_positive_cause'):
            cause = detected_patterns['primary_false_positive_cause']
            recommendations.append(f"Primary false positive cause identified: {cause}. Consider camera repositioning or additional training data.")
        
        # Threshold recommendations
        if threshold_optimization.get('optimization_successful'):
            improvement = threshold_optimization.get('improvement_metrics', {}).get('accuracy_improvement_percent', 0)
            if improvement > 5:
                recommendations.append(f"Threshold optimization achieved {improvement:.1f}% accuracy improvement. Implement optimized thresholds.")
        
        # Confidence calibration recommendations
        if confidence_calibration.get('calibration_successful'):
            ece = confidence_calibration.get('calibration_metrics', {}).get('expected_calibration_error', 0)
            if ece > 0.1:
                recommendations.append("Model confidence scores are poorly calibrated. Consider confidence score adjustment.")
        
        # Data quality recommendations
        dominant_lighting = detected_patterns.get('dominant_lighting_condition')
        if dominant_lighting and dominant_lighting in ['dark', 'backlit', 'shadow']:
            recommendations.append(f"Dominant lighting condition ({dominant_lighting}) may affect accuracy. Consider camera settings adjustment.")
        
        # Generic recommendations if no specific insights
        if not recommendations:
            recommendations.append("Continue monitoring performance. More data needed for specific recommendations.")
        
        return recommendations
    
    def _store_learning_results(self, batch_id: str, learning_results: Dict[str, Any]) -> None:
        """
        Store learning results in database
        
        Args:
            batch_id: Batch identifier
            learning_results: Dictionary containing all learning results
        """
        try:
            from ..models.database import AutoLearningMetrics
            from ..core.database import db_manager
            
            with db_manager.get_session_context() as session:
                learning_record = AutoLearningMetrics(
                    batch_id=batch_id,
                    detected_patterns=learning_results.get('detected_patterns'),
                    common_structures=learning_results.get('detected_patterns', {}).get('most_common_structures'),
                    false_positive_patterns=learning_results.get('detected_patterns', {}).get('most_common_false_positives'),
                    original_thresholds={
                        'structure_misid': settings.threshold_structure_misid,
                        'proper_ppe': settings.threshold_proper_ppe,
                        'no_violation': settings.threshold_no_violation
                    },
                    optimized_thresholds=learning_results.get('threshold_optimization', {}).get('optimized_thresholds', {}),
                    accuracy_improvement=learning_results.get('threshold_optimization', {}).get('improvement_metrics', {}).get('accuracy_improvement_percent', 0),
                    customer_environment=learning_results.get('detected_patterns'),
                    optimization_recommendations=learning_results.get('recommendations'),
                    confidence_calibration=learning_results.get('confidence_calibration', {}).get('calibration_metrics'),
                    processing_improvements=learning_results.get('threshold_optimization', {}).get('performance_metrics')
                )
                
                session.add(learning_record)
                session.commit()
                
                logger.info(f"Stored learning results for batch {batch_id}")
                
        except Exception as e:
            logger.error(f"Failed to store learning results: {e}")


# Global auto-learning engine instance
auto_learning_engine = AutoLearningEngine()