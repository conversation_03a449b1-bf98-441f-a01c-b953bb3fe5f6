"""
AI-FARM Main FastAPI Application
Production-ready FastAPI application with startup/shutdown handling
"""

import asyncio
import logging
import time
from contextlib import asynccontextmanager
from datetime import datetime
from typing import Dict, Any

from fastapi import FastAPI, HTTPException, Request, Response
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from fastapi.responses import JSONResponse
import uvicorn

from .core.config import settings
from .core.database import init_database, cleanup_database, db_manager
from .models.schemas import HealthCheckResponse, ErrorResponse
from .services.vlm_service import vlm_service
from .services.redis_service import redis_service
from .utils.background_tasks import start_task_manager, stop_task_manager
from .utils.logging import setup_logging, get_logger
from .api import batch_processing, health, status, metrics, data_analysis, vlm, insights, results

# Setup enhanced logging
setup_logging()

logger = get_logger(__name__)

# Application state
app_state = {
    "startup_time": None,
    "total_requests": 0,
    "healthy": False,
    "version": "1.0.0"
}


@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    Application lifespan manager for startup and shutdown tasks
    """
    # Startup
    logger.info("Starting AI-FARM application...")
    
    try:
        # Create necessary directories
        settings.create_directories()
        logger.info("Created application directories")
        
        # Initialize database
        init_database()
        logger.info("Database initialized successfully")

        # Initialize Redis service
        redis_initialized = await redis_service.initialize()
        if redis_initialized:
            logger.info("Redis service initialized successfully")
        else:
            logger.warning("Redis service initialization failed - caching disabled")

        # Start background task manager
        await start_task_manager()
        logger.info("Background task manager started")
        
        # Test VLM service connection (non-blocking)
        try:
            vlm_health = await asyncio.wait_for(vlm_service.health_check(), timeout=5.0)
            if vlm_health.get("status") == "connected":
                logger.info("VLM service connection verified")
            else:
                logger.warning(f"VLM service not accessible: {vlm_health.get('error', 'Unknown error')}")
        except asyncio.TimeoutError:
            logger.warning("VLM service health check timed out - continuing startup")
        except Exception as e:
            logger.warning(f"VLM service health check failed: {e} - continuing startup")
        
        # Update application state
        app_state["startup_time"] = datetime.utcnow()
        app_state["healthy"] = True
        
        # Schedule cleanup tasks
        asyncio.create_task(periodic_cleanup_task())
        
        logger.info("AI-FARM application started successfully")
        
    except Exception as e:
        logger.error(f"Failed to start application: {e}")
        app_state["healthy"] = False
        raise
    
    yield
    
    # Shutdown
    logger.info("Shutting down AI-FARM application...")
    
    try:
        # Stop background task manager
        await stop_task_manager()
        logger.info("Background task manager stopped")
        
        # Close Redis connection
        await redis_service.close()
        logger.info("Redis service closed")

        # Cleanup database connections
        cleanup_database()
        logger.info("Database cleanup completed")
        
        # Additional cleanup tasks
        app_state["healthy"] = False
        
        logger.info("AI-FARM application shutdown completed")
        
    except Exception as e:
        logger.error(f"Error during shutdown: {e}")


# Create FastAPI application with large file upload support
app = FastAPI(
    title="AI-FARM API",
    description="AI-FARM - False Positive Alert Reduction System",
    version=app_state["version"],
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# Configure large file upload limits
from starlette.middleware import Middleware
from starlette.middleware.base import BaseHTTPMiddleware

class LargeFileUploadMiddleware(BaseHTTPMiddleware):
    """Middleware to handle large file uploads efficiently"""

    async def dispatch(self, request, call_next):
        # Set maximum request body size to 1GB for file uploads
        if request.url.path.startswith("/api/v1/batch/upload"):
            # Increase timeout for large file uploads
            import asyncio
            try:
                response = await asyncio.wait_for(call_next(request), timeout=300.0)  # 5 minutes
                return response
            except asyncio.TimeoutError:
                from fastapi import HTTPException
                raise HTTPException(status_code=408, detail="Upload timeout - file too large or connection too slow")
        else:
            return await call_next(request)

# Add middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.add_middleware(GZipMiddleware, minimum_size=1000)
app.add_middleware(LargeFileUploadMiddleware)


# Request middleware for tracking
@app.middleware("http")
async def track_requests(request: Request, call_next):
    """
    Middleware to track requests and add timing information
    """
    start_time = time.time()
    
    # Increment request counter
    app_state["total_requests"] += 1
    
    # Add request ID for tracking
    request_id = f"req_{int(time.time() * 1000)}_{app_state['total_requests']}"
    
    # Process request
    try:
        response = await call_next(request)
        
        # Add timing and request ID headers
        process_time = time.time() - start_time
        response.headers["X-Process-Time"] = str(process_time)
        response.headers["X-Request-ID"] = request_id
        
        return response
        
    except Exception as e:
        logger.error(f"Request {request_id} failed: {e}")
        
        # Return error response
        return JSONResponse(
            status_code=500,
            content=ErrorResponse(
                error="internal_server_error",
                message="An internal server error occurred",
                request_id=request_id
            ).dict()
        )


# Exception handlers
@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    """
    Global HTTP exception handler
    """
    return JSONResponse(
        status_code=exc.status_code,
        content=ErrorResponse(
            error=f"http_{exc.status_code}",
            message=exc.detail,
            request_id=request.headers.get("X-Request-ID")
        ).dict()
    )


@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    """
    Global exception handler for unhandled exceptions
    """
    logger.error(f"Unhandled exception: {exc}", exc_info=True)
    
    return JSONResponse(
        status_code=500,
        content=ErrorResponse(
            error="internal_server_error",
            message="An unexpected error occurred",
            details={"exception_type": type(exc).__name__} if settings.debug else None,
            request_id=request.headers.get("X-Request-ID")
        ).dict()
    )


# Root endpoint
@app.get("/", response_model=Dict[str, Any])
async def root():
    """
    Root endpoint with basic API information
    """
    return {
        "name": "AI-FARM API",
        "version": app_state["version"],
        "description": "AI-FARM - False Positive Alert Reduction System",
        "status": "healthy" if app_state["healthy"] else "unhealthy",
        "uptime_seconds": int((datetime.utcnow() - app_state["startup_time"]).total_seconds()) if app_state["startup_time"] else 0,
        "endpoints": {
            "health": "/health",
            "docs": "/docs",
            "batch_processing": "/api/v1/batch",
            "status": "/api/v1/status",
            "metrics": "/api/v1/metrics",
            "data_analysis": "/api/v1/data-analysis"
        }
    }


# Health check endpoint
@app.get("/health", response_model=HealthCheckResponse)
async def health_check():
    """
    Comprehensive health check endpoint
    """
    try:
        # Check database connection
        db_health = db_manager.get_health_status()
        
        # Check VLM service (with timeout)
        try:
            vlm_health = await asyncio.wait_for(vlm_service.health_check(), timeout=3.0)
        except asyncio.TimeoutError:
            vlm_health = {"status": "timeout", "error": "Health check timed out"}
        except Exception as e:
            vlm_health = {"status": "error", "error": str(e)}

        # Check Redis service
        redis_health = await redis_service.get_health_status()
        
        # Determine overall health
        is_healthy = (
            app_state["healthy"] and
            db_health["status"] == "connected" and
            vlm_health["status"] in ["connected", "error"] and  # VLM can be temporarily unavailable
            redis_health["status"] in ["connected", "disabled", "error"]  # Redis can be disabled or temporarily unavailable
        )
        
        # Calculate uptime
        uptime_seconds = 0
        if app_state["startup_time"]:
            uptime_seconds = int((datetime.utcnow() - app_state["startup_time"]).total_seconds())
        
        return HealthCheckResponse(
            status="healthy" if is_healthy else "unhealthy",
            timestamp=datetime.utcnow(),
            version=app_state["version"],
            vlm_api_status=vlm_health["status"],
            database_status=db_health["status"],
            redis_status=redis_health["status"],
            uptime_seconds=uptime_seconds
        )
        
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return HealthCheckResponse(
            status="unhealthy",
            timestamp=datetime.utcnow(),
            version=app_state["version"],
            vlm_api_status="error",
            database_status="error",
            redis_status="error",
            uptime_seconds=0
        )


# Include API routers
app.include_router(
    batch_processing.router,
    prefix="/api/v1/batch",
    tags=["Batch Processing"]
)

app.include_router(
    health.router,
    prefix="/api/v1/health",
    tags=["Health & Monitoring"]
)

app.include_router(
    status.router,
    prefix="/api/v1/status",
    tags=["Status Monitoring"]
)

app.include_router(
    metrics.router,
    prefix="/api/v1/metrics",
    tags=["Demo Metrics"]
)

app.include_router(
    data_analysis.router,
    prefix="/api/v1/data-analysis",
    tags=["Data Analysis"]
)

app.include_router(
    vlm.router,
    prefix="/api/v1/vlm",
    tags=["VLM Analysis"]
)

app.include_router(
    insights.router,
    prefix="/api/v1/insights",
    tags=["Insights & Analytics"]
)

app.include_router(
    results.router,
    prefix="/api/v1/results",
    tags=["Results & Learning"]
)


async def periodic_cleanup_task():
    """
    Periodic task to clean up old data and maintain system health
    """
    while app_state["healthy"]:
        try:
            # Sleep for 1 hour
            await asyncio.sleep(3600)
            
            # Run cleanup tasks
            if settings.enable_customer_data_cleanup:
                cleaned_sessions = db_manager.cleanup_old_sessions(
                    settings.customer_data_retention_hours
                )
                if cleaned_sessions > 0:
                    logger.info(f"Cleaned up {cleaned_sessions} old demo sessions")
            
            # Additional maintenance tasks can be added here
            logger.debug("Periodic cleanup task completed")
            
        except asyncio.CancelledError:
            logger.info("Periodic cleanup task cancelled")
            break
        except Exception as e:
            logger.error(f"Periodic cleanup task error: {e}")


# Development server
if __name__ == "__main__":
    # Always use string import to avoid worker/reload issues
    uvicorn.run(
        "main:app",  # Use relative import when running from app directory
        host=settings.host,
        port=settings.port,
        log_level=settings.log_level.lower(),
        reload=settings.debug,
        workers=1 if settings.debug else settings.worker_processes,
        # Large file upload configuration
        limit_max_requests=1000,
        limit_concurrency=100,
        timeout_keep_alive=65,
        # Set maximum request body size to 1GB
        h11_max_incomplete_event_size=1024 * 1024 * 1024  # 1GB
    )