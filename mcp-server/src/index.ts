#!/usr/bin/env node

/**
 * AI-FARM MCP Server with Puppeteer
 * 
 * Provides web automation capabilities for AI-FARM including:
 * - Screenshot capture for violation analysis
 * - Web scraping for data collection
 * - Automated browser interactions
 * - PDF generation from web pages
 */

import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import {
  CallToolRequestSchema,
  ListToolsRequestSchema,
  Tool,
} from '@modelcontextprotocol/sdk/types.js';

import puppeteer, { Brows<PERSON>, <PERSON> } from 'puppeteer';
import puppeteerExtra from 'puppeteer-extra';
import StealthPlugin from 'puppeteer-extra-plugin-stealth';
import sharp from 'sharp';
import fs from 'fs-extra';
import path from 'path';

// Add stealth plugin to avoid detection
puppeteerExtra.use(StealthPlugin());

class AIFarmMCPServer {
  private server: Server;
  private browser: Browser | null = null;
  private screenshotsDir: string;

  constructor() {
    this.server = new Server(
      {
        name: 'ai-farm-mcp-puppeteer',
        version: '1.0.0',
      },
      {
        capabilities: {
          tools: {},
        },
      }
    );

    // Setup screenshots directory
    this.screenshotsDir = path.join(process.cwd(), '..', 'data', 'screenshots');
    fs.ensureDirSync(this.screenshotsDir);

    this.setupToolHandlers();
    this.setupErrorHandling();
  }

  private setupErrorHandling(): void {
    this.server.onerror = (error) => {
      console.error('[MCP Error]', error);
    };

    process.on('SIGINT', async () => {
      if (this.browser) {
        await this.browser.close();
      }
      process.exit(0);
    });
  }

  private setupToolHandlers(): void {
    this.server.setRequestHandler(ListToolsRequestSchema, async () => {
      return {
        tools: [
          {
            name: 'screenshot_url',
            description: 'Take a screenshot of a web page and analyze it for safety violations',
            inputSchema: {
              type: 'object',
              properties: {
                url: {
                  type: 'string',
                  description: 'URL to capture screenshot from',
                },
                width: {
                  type: 'number',
                  description: 'Screenshot width in pixels (default: 1920)',
                  default: 1920,
                },
                height: {
                  type: 'number', 
                  description: 'Screenshot height in pixels (default: 1080)',
                  default: 1080,
                },
                selector: {
                  type: 'string',
                  description: 'CSS selector to screenshot specific element (optional)',
                },
                filename: {
                  type: 'string',
                  description: 'Custom filename for screenshot (optional)',
                },
              },
              required: ['url'],
            },
          },
          {
            name: 'scrape_violation_data',
            description: 'Scrape safety violation data from web interfaces',
            inputSchema: {
              type: 'object',
              properties: {
                url: {
                  type: 'string',
                  description: 'URL to scrape data from',
                },
                selectors: {
                  type: 'object',
                  description: 'CSS selectors for data extraction',
                  properties: {
                    violations: { type: 'string' },
                    timestamps: { type: 'string' },
                    locations: { type: 'string' },
                    images: { type: 'string' },
                  },
                },
                waitFor: {
                  type: 'string',
                  description: 'CSS selector to wait for before scraping',
                },
              },
              required: ['url'],
            },
          },
          {
            name: 'automate_valo_interface',
            description: 'Automate interactions with VALO safety monitoring interface',
            inputSchema: {
              type: 'object',
              properties: {
                action: {
                  type: 'string',
                  enum: ['login', 'export_data', 'filter_violations', 'generate_report'],
                  description: 'Action to perform on VALO interface',
                },
                credentials: {
                  type: 'object',
                  properties: {
                    username: { type: 'string' },
                    password: { type: 'string' },
                  },
                  description: 'Login credentials (if needed)',
                },
                filters: {
                  type: 'object',
                  description: 'Filter criteria for violations',
                  properties: {
                    dateFrom: { type: 'string' },
                    dateTo: { type: 'string' },
                    location: { type: 'string' },
                    violationType: { type: 'string' },
                  },
                },
              },
              required: ['action'],
            },
          },
          {
            name: 'generate_demo_pdf',
            description: 'Generate PDF report from AI-FARM dashboard for customer presentations',
            inputSchema: {
              type: 'object',
              properties: {
                dashboardUrl: {
                  type: 'string',
                  description: 'URL of AI-FARM dashboard to convert to PDF',
                  default: 'http://localhost:3000',
                },
                reportType: {
                  type: 'string',
                  enum: ['roi', 'results', 'insights', 'full'],
                  description: 'Type of report to generate',
                  default: 'full',
                },
                filename: {
                  type: 'string',
                  description: 'Custom filename for PDF report',
                },
              },
              required: ['dashboardUrl'],
            },
          },
          {
            name: 'capture_violation_images',
            description: 'Batch capture screenshots of violation alerts for analysis',
            inputSchema: {
              type: 'object',
              properties: {
                urls: {
                  type: 'array',
                  items: { type: 'string' },
                  description: 'Array of URLs to capture',
                },
                batchName: {
                  type: 'string',
                  description: 'Name for this batch of captures',
                },
                delay: {
                  type: 'number',
                  description: 'Delay between captures in milliseconds',
                  default: 1000,
                },
              },
              required: ['urls'],
            },
          },
        ] as Tool[],
      };
    });

    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      const { name, arguments: args } = request.params;

      try {
        switch (name) {
          case 'screenshot_url':
            return await this.handleScreenshot(args);
          case 'scrape_violation_data':
            return await this.handleScrapeViolationData(args);
          case 'automate_valo_interface':
            return await this.handleValoAutomation(args);
          case 'generate_demo_pdf':
            return await this.handleGeneratePDF(args);
          case 'capture_violation_images':
            return await this.handleBatchCapture(args);
          default:
            throw new Error(`Unknown tool: ${name}`);
        }
      } catch (error) {
        return {
          content: [
            {
              type: 'text',
              text: `Error executing ${name}: ${error instanceof Error ? error.message : String(error)}`,
            },
          ],
        };
      }
    });
  }

  private async getBrowser(): Promise<Browser> {
    if (!this.browser) {
      this.browser = await puppeteerExtra.launch({
        headless: true,
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-gpu',
        ],
      });
    }
    return this.browser;
  }

  private async handleScreenshot(args: any) {
    const { url, width = 1920, height = 1080, selector, filename } = args;
    
    const browser = await this.getBrowser();
    const page = await browser.newPage();
    
    try {
      await page.setViewport({ width, height });
      await page.goto(url, { waitUntil: 'networkidle2' });
      
      // Generate filename
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const screenshotName = filename || `screenshot-${timestamp}.png`;
      const screenshotPath = path.join(this.screenshotsDir, screenshotName);
      
      let screenshotBuffer: Buffer;
      
      if (selector) {
        // Screenshot specific element
        const element = await page.$(selector);
        if (!element) {
          throw new Error(`Element with selector "${selector}" not found`);
        }
        screenshotBuffer = await element.screenshot();
      } else {
        // Full page screenshot
        screenshotBuffer = await page.screenshot({ fullPage: true });
      }
      
      // Save screenshot
      await fs.writeFile(screenshotPath, screenshotBuffer);
      
      // Optimize with sharp
      const optimizedBuffer = await sharp(screenshotBuffer)
        .jpeg({ quality: 85 })
        .toBuffer();
      
      const optimizedPath = screenshotPath.replace('.png', '.jpg');
      await fs.writeFile(optimizedPath, optimizedBuffer);
      
      return {
        content: [
          {
            type: 'text',
            text: `Screenshot captured successfully!\nURL: ${url}\nSaved to: ${optimizedPath}\nSize: ${width}x${height}px`,
          },
          {
            type: 'image',
            data: optimizedBuffer.toString('base64'),
            mimeType: 'image/jpeg',
          },
        ],
      };
    } finally {
      await page.close();
    }
  }

  private async handleScrapeViolationData(args: any) {
    const { url, selectors = {}, waitFor } = args;
    
    const browser = await this.getBrowser();
    const page = await browser.newPage();
    
    try {
      await page.goto(url, { waitUntil: 'networkidle2' });
      
      if (waitFor) {
        await page.waitForSelector(waitFor, { timeout: 10000 });
      }
      
      const data = await page.evaluate((selectors) => {
        const result: any = {};
        
        for (const [key, selector] of Object.entries(selectors)) {
          const elements = document.querySelectorAll(selector as string);
          result[key] = Array.from(elements).map(el => ({
            text: el.textContent?.trim(),
            href: el.getAttribute('href'),
            src: el.getAttribute('src'),
            className: el.className,
          }));
        }
        
        return result;
      }, selectors);
      
      return {
        content: [
          {
            type: 'text',
            text: `Scraped data from ${url}:\n${JSON.stringify(data, null, 2)}`,
          },
        ],
      };
    } finally {
      await page.close();
    }
  }

  private async handleValoAutomation(args: any) {
    const { action, credentials, filters } = args;
    
    const browser = await this.getBrowser();
    const page = await browser.newPage();
    
    try {
      let result = '';
      
      switch (action) {
        case 'login':
          if (!credentials) {
            throw new Error('Credentials required for login');
          }
          // Implement VALO login automation
          await page.goto('https://valo-system-url/login');
          await page.type('#username', credentials.username);
          await page.type('#password', credentials.password);
          await page.click('#login-button');
          await page.waitForNavigation();
          result = 'Successfully logged into VALO system';
          break;
          
        case 'export_data':
          // Implement data export automation
          await page.goto('https://valo-system-url/export');
          if (filters) {
            // Apply filters
            if (filters.dateFrom) await page.type('#date-from', filters.dateFrom);
            if (filters.dateTo) await page.type('#date-to', filters.dateTo);
            if (filters.location) await page.select('#location', filters.location);
          }
          await page.click('#export-button');
          result = 'Data export initiated';
          break;
          
        default:
          throw new Error(`Unknown VALO action: ${action}`);
      }
      
      return {
        content: [
          {
            type: 'text',
            text: result,
          },
        ],
      };
    } finally {
      await page.close();
    }
  }

  private async handleGeneratePDF(args: any) {
    const { dashboardUrl, reportType = 'full', filename } = args;
    
    const browser = await this.getBrowser();
    const page = await browser.newPage();
    
    try {
      await page.setViewport({ width: 1920, height: 1080 });
      
      let targetUrl = dashboardUrl;
      if (reportType !== 'full') {
        targetUrl += `/${reportType}`;
      }
      
      await page.goto(targetUrl, { waitUntil: 'networkidle2' });
      
      // Wait for charts and data to load
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const pdfName = filename || `ai-farm-${reportType}-report-${timestamp}.pdf`;
      const pdfPath = path.join(this.screenshotsDir, pdfName);
      
      await page.pdf({
        path: pdfPath,
        format: 'A4',
        printBackground: true,
        margin: {
          top: '20px',
          right: '20px',
          bottom: '20px',
          left: '20px',
        },
      });
      
      return {
        content: [
          {
            type: 'text',
            text: `PDF report generated successfully!\nType: ${reportType}\nSaved to: ${pdfPath}`,
          },
        ],
      };
    } finally {
      await page.close();
    }
  }

  private async handleBatchCapture(args: any) {
    const { urls, batchName = 'batch', delay = 1000 } = args;
    
    const browser = await this.getBrowser();
    const results = [];
    
    for (let i = 0; i < urls.length; i++) {
      const url = urls[i];
      const page = await browser.newPage();
      
      try {
        await page.setViewport({ width: 1920, height: 1080 });
        await page.goto(url, { waitUntil: 'networkidle2' });
        
        const filename = `${batchName}-${i + 1}.jpg`;
        const screenshotPath = path.join(this.screenshotsDir, filename);
        
        const screenshotBuffer = await page.screenshot({ fullPage: true });
        
        // Optimize with sharp
        const optimizedBuffer = await sharp(screenshotBuffer)
          .jpeg({ quality: 85 })
          .toBuffer();
        
        await fs.writeFile(screenshotPath, optimizedBuffer);
        
        results.push({
          url,
          filename,
          path: screenshotPath,
          size: optimizedBuffer.length,
        });
        
        // Add delay between captures
        if (i < urls.length - 1) {
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      } finally {
        await page.close();
      }
    }
    
    return {
      content: [
        {
          type: 'text',
          text: `Batch capture completed!\nProcessed ${results.length} URLs\nResults:\n${JSON.stringify(results, null, 2)}`,
        },
      ],
    };
  }

  async run(): Promise<void> {
    const transport = new StdioServerTransport();
    await this.server.connect(transport);
    console.error('AI-FARM MCP Puppeteer server running on stdio');
  }
}

const server = new AIFarmMCPServer();
server.run().catch(console.error);