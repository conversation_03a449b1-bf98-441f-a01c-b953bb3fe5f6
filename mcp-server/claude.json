{"name": "ai-farm-mcp-puppeteer", "version": "1.0.0", "description": "MCP server with Puppeteer for AI-FARM web automation and screenshot capabilities", "mcp": {"command": "node", "args": ["dist/index.js"], "cwd": "/home/<USER>/VALO_AI-FARM_2025/VALO_AI-FARM_2025/mcp-server"}, "tools": [{"name": "screenshot", "description": "Take a screenshot of a webpage", "parameters": {"url": "string", "width": "number (optional, default: 1920)", "height": "number (optional, default: 1080)", "selector": "string (optional)", "filename": "string (optional)"}}, {"name": "scrape", "description": "Extract content from a webpage", "parameters": {"url": "string", "selector": "string (optional)", "extractText": "boolean (optional, default: true)", "extractLinks": "boolean (optional, default: false)", "extractImages": "boolean (optional, default: false)"}}, {"name": "fill_form", "description": "Fill and submit a form on a webpage", "parameters": {"url": "string", "formData": "object", "submitSelector": "string (optional)"}}, {"name": "click_element", "description": "Click an element on a webpage", "parameters": {"url": "string", "selector": "string"}}, {"name": "generate_pdf", "description": "Generate a PDF from a webpage", "parameters": {"url": "string", "filename": "string (optional)"}}, {"name": "generate_aifarm_report", "description": "Generate AI-FARM performance report PDF", "parameters": {"reportType": "string ('performance' | 'accuracy' | 'trends')", "filename": "string (optional)"}}, {"name": "monitor_aifarm_kpis", "description": "Monitor and extract AI-FARM KPIs from the dashboard", "parameters": {}}, {"name": "check_valorant_status", "description": "Check VALORANT game server status", "parameters": {}}]}