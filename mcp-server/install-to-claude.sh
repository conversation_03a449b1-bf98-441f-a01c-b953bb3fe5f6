#!/bin/bash

# <PERSON><PERSON>t to help install AI-FARM MCP server to <PERSON>

echo "AI-FARM MCP Server Installation Helper"
echo "======================================"
echo ""
echo "To add this MCP server to <PERSON>, add the following to your claude_desktop_config.json:"
echo ""
echo "Location:"
echo "- macOS: ~/Library/Application Support/Claude/claude_desktop_config.json"
echo "- Windows: %APPDATA%/<PERSON>/claude_desktop_config.json"
echo "- Linux: ~/.config/Claude/claude_desktop_config.json"
echo ""
echo "Configuration to add:"
echo ""
cat << EOF
{
  "mcpServers": {
    "ai-farm-puppeteer": {
      "command": "node",
      "args": ["$(pwd)/dist/index.js"],
      "env": {
        "NODE_ENV": "production"
      }
    }
  }
}
EOF
echo ""
echo "Note: If you already have other MCP servers configured, add the 'ai-farm-puppeteer' entry to your existing 'mcpServers' object."
echo ""
echo "Current installation path: $(pwd)"