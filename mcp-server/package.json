{"name": "ai-farm-mcp-puppeteer", "version": "1.0.0", "description": "MCP server with Puppeteer for AI-FARM web automation and screenshot capabilities", "main": "dist/index.js", "type": "module", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "tsx src/index.ts", "watch": "tsc --watch", "mcp:start": "npm run build && npm start", "mcp:dev": "npm run dev", "postinstall": "npm run build"}, "keywords": ["mcp", "puppeteer", "ai-farm", "web-automation"], "author": "AI-FARM Team", "license": "MIT", "dependencies": {"@modelcontextprotocol/sdk": "^0.5.0", "puppeteer": "^22.0.0", "puppeteer-extra": "^3.3.6", "puppeteer-extra-plugin-stealth": "^2.11.2", "sharp": "^0.33.0", "fs-extra": "^11.2.0", "path": "^0.12.7"}, "devDependencies": {"@types/node": "^20.10.0", "@types/fs-extra": "^11.0.4", "typescript": "^5.3.0", "tsx": "^4.6.0"}}