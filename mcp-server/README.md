# AI-FARM MCP Puppeteer Server

This MCP (Model Context Protocol) server provides web automation and screenshot capabilities for AI-FARM using Puppeteer.

## Setup

1. Install dependencies:
   ```bash
   npm install
   ```

2. Build the TypeScript code:
   ```bash
   npm run build
   ```

3. Start the MCP server:
   ```bash
   npm start
   ```

## Available Tools

- **screenshot**: Take screenshots of webpages
- **scrape**: Extract content from webpages
- **fill_form**: Fill and submit forms
- **click_element**: Click elements on webpages
- **generate_pdf**: Generate PDFs from webpages
- **generate_aifarm_report**: Generate AI-FARM performance reports
- **monitor_aifarm_kpis**: Monitor AI-FARM KPIs
- **check_valorant_status**: Check VALORANT server status

## Claude Integration

To integrate with <PERSON>, use one of the following configuration methods:

### Option 1: Using mcp-config.json
Copy the `mcp-config.json` file to your Claude configuration directory.

### Option 2: Using claude.json
Reference the `claude.json` file in your Claude settings.

### Option 3: Manual Configuration
Add this server to your Claude configuration:

```json
{
  "mcpServers": {
    "ai-farm-puppeteer": {
      "command": "node",
      "args": ["/home/<USER>/VALO_AI-FARM_2025/VALO_AI-FARM_2025/mcp-server/dist/index.js"]
    }
  }
}
```

## Development

- Run in development mode: `npm run dev`
- Watch for changes: `npm run watch`
- Build and start: `npm run mcp:start`

## Directory Structure

- `src/` - TypeScript source files
- `dist/` - Compiled JavaScript files
- `screenshots/` - Generated screenshots and PDFs (created automatically)