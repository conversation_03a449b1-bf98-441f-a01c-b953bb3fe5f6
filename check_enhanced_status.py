#!/usr/bin/env python3
"""
Check status of enhanced prompt testing and show detailed metrics
"""

import json
import os
from datetime import datetime

def check_status():
    """Check current status of enhanced prompt test"""
    
    progress_file = 'enhanced_1250_progress.json'
    
    if not os.path.exists(progress_file):
        print("No progress file found. Test may not have started.")
        return
    
    with open(progress_file, 'r') as f:
        data = json.load(f)
    
    results = data.get('results', [])
    total = len(results)
    timestamp = data.get('timestamp', '')
    
    print("ENHANCED PROMPT TEST STATUS")
    print("="*60)
    print(f"Last update: {timestamp}")
    print(f"Progress: {total}/1250 cases ({total/1250*100:.1f}%)")
    
    if total == 0:
        print("No results yet.")
        return
    
    # Calculate metrics
    correct = sum(r['correct'] for r in results)
    accuracy = correct / total * 100
    
    # FP metrics
    fps = [r for r in results if r['actual_fp']]
    if fps:
        fp_detected = sum(r['predicted_fp'] for r in fps)
        fp_rate = fp_detected / len(fps) * 100
    else:
        fp_rate = 0
    
    # Valid protection
    valid_cases = [r for r in results if not r['actual_fp']]
    if valid_cases:
        valid_protected = sum(not r['predicted_fp'] for r in valid_cases)
        protection_rate = valid_protected / len(valid_cases) * 100
    else:
        protection_rate = 0
    
    print(f"\nPERFORMANCE METRICS:")
    print(f"├─ Overall Accuracy: {accuracy:.1f}%")
    print(f"├─ FP Detection: {fp_rate:.1f}% (Baseline: 76.3%)")
    print(f"├─ Valid Protection: {protection_rate:.1f}%")
    print(f"└─ Improvement: {fp_rate - 76.3:+.1f}%")
    
    # Entity breakdown
    entities = {}
    for r in results:
        e = r.get('entity_type', 'UNKNOWN')
        entities[e] = entities.get(e, 0) + 1
    
    print(f"\nENTITY DETECTION ({total} cases):")
    for entity, count in sorted(entities.items(), key=lambda x: x[1], reverse=True):
        pct = count / total * 100
        print(f"├─ {entity}: {count} ({pct:.1f}%)")
    
    # Violation type breakdown
    violations = {}
    for r in results:
        v = r.get('infringement_type', 'Unknown')
        violations[v] = violations.get(v, 0) + 1
    
    print(f"\nBY VIOLATION TYPE:")
    for vtype, count in sorted(violations.items(), key=lambda x: x[1], reverse=True):
        # Get accuracy for this type
        type_results = [r for r in results if r.get('infringement_type') == vtype]
        type_correct = sum(r['correct'] for r in type_results)
        type_acc = type_correct / len(type_results) * 100 if type_results else 0
        print(f"├─ {vtype}: {count} cases, {type_acc:.0f}% accurate")
    
    # Recent performance trend
    if total >= 10:
        recent = results[-10:]
        recent_correct = sum(r['correct'] for r in recent)
        recent_acc = recent_correct / 10 * 100
        print(f"\nLAST 10 CASES: {recent_acc:.0f}% accurate")
        
        for r in recent[-5:]:
            status = "✓" if r['correct'] else "✗"
            entity = r.get('entity_type', 'UNKNOWN')
            print(f"{status} {r['case_number']} - {entity}")
    
    # Estimate completion time
    if total >= 20:
        # Estimate based on recent progress
        time_diff = datetime.now() - datetime.fromisoformat(timestamp)
        minutes_elapsed = time_diff.total_seconds() / 60
        rate = total / minutes_elapsed if minutes_elapsed > 0 else 0
        remaining = 1250 - total
        eta_minutes = remaining / rate if rate > 0 else 0
        
        print(f"\nESTIMATED COMPLETION:")
        print(f"├─ Current rate: {rate:.1f} cases/min")
        print(f"├─ Time remaining: {eta_minutes:.0f} minutes")
        print(f"└─ ETA: {eta_minutes/60:.1f} hours")
    
    # Check if complete
    if total == 1250:
        print("\n✅ TEST COMPLETE!")
        print("Check enhanced_prompt_1250_final_report.json for full results")
    else:
        print(f"\n⏳ Test in progress... {1250 - total} cases remaining")

if __name__ == "__main__":
    check_status()