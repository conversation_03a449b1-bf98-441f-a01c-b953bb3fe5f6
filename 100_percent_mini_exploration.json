{"timestamp": "2025-07-25T13:51:07.642864", "sample_size": 10, "strategies": {"baseline": {"name": "Baseline (Round 3 Final)", "thresholds": {"structure": 91, "person": 50, "ppe_compliant": 75, "behavioral": 55}}, "ultra_conservative": {"name": "Ultra-Conservative", "thresholds": {"structure": 99, "person": 30, "ppe_compliant": 90, "behavioral": 40}}, "maximum_safety": {"name": "Maximum Safety Mode", "thresholds": {"structure": 98, "person": 35, "ppe_compliant": 95, "behavioral": 35}}}, "results": {"baseline": {"valid_protection": 60.0, "valid_protected": 3, "valid_total": 5, "fp_detection": 40.0, "fp_detected": 2, "fp_total": 5, "accuracy": 50.0}, "ultra_conservative": {"valid_protection": 100.0, "valid_protected": 5, "valid_total": 5, "fp_detection": 0.0, "fp_detected": 0, "fp_total": 5, "accuracy": 50.0}, "maximum_safety": {"valid_protection": 100.0, "valid_protected": 5, "valid_total": 5, "fp_detection": 0.0, "fp_detected": 0, "fp_total": 5, "accuracy": 50.0}}, "perfect_strategies": ["ultra_conservative", "maximum_safety"]}