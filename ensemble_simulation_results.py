#!/usr/bin/env python3
"""
Ensemble Simulation Based on Overnight Test Results
Uses actual performance data to simulate ensemble behavior
"""

import json
from datetime import datetime
import random

# Actual performance from overnight tests
ACTUAL_PERFORMANCE = {
    'assumption_based': {
        'fp_detection': 86.7,
        'valid_protection': 100.0,
        'confidence_pattern': {'high': 0.75, 'medium': 0.20, 'low': 0.05}
    },
    'alert_fatigue_prevention': {
        'fp_detection': 100.0,
        'valid_protection': 100.0,
        'confidence_pattern': {'high': 0.90, 'medium': 0.08, 'low': 0.02}
    },
    'worksite_reality': {
        'fp_detection': 75.0,
        'valid_protection': 100.0,
        'confidence_pattern': {'high': 0.60, 'medium': 0.30, 'low': 0.10}
    }
}


def simulate_ensemble_performance(num_cases=1000):
    """Simulate ensemble performance based on actual results"""
    
    # Generate test cases based on 97% FP rate
    test_cases = []
    for i in range(num_cases):
        is_fp = random.random() < 0.97
        test_cases.append({
            'id': i,
            'is_fp': is_fp,
            'difficulty': random.choice(['easy', 'medium', 'hard'])
        })
    
    # Simulate each approach
    results = {
        'assumption_based': simulate_approach('assumption_based', test_cases),
        'alert_fatigue': simulate_approach('alert_fatigue_prevention', test_cases),
        'worksite_reality': simulate_approach('worksite_reality', test_cases),
        'ensemble': simulate_ensemble(test_cases)
    }
    
    return results, test_cases


def simulate_approach(approach_name, test_cases):
    """Simulate single approach based on known performance"""
    perf = ACTUAL_PERFORMANCE[approach_name]
    
    correct_fp = 0
    correct_valid = 0
    total_fp = sum(1 for c in test_cases if c['is_fp'])
    total_valid = sum(1 for c in test_cases if not c['is_fp'])
    
    for case in test_cases:
        # Simulate prediction based on known accuracy
        if case['is_fp']:
            # Will it correctly identify as FP?
            if random.random() < perf['fp_detection'] / 100:
                correct_fp += 1
        else:
            # Will it correctly identify as valid?
            if random.random() < perf['valid_protection'] / 100:
                correct_valid += 1
    
    return {
        'approach': approach_name,
        'fp_detection': (correct_fp / total_fp * 100) if total_fp > 0 else 0,
        'valid_protection': (correct_valid / total_valid * 100) if total_valid > 0 else 100,
        'correct_fp': correct_fp,
        'correct_valid': correct_valid,
        'total_fp': total_fp,
        'total_valid': total_valid
    }


def simulate_ensemble(test_cases):
    """Simulate ensemble behavior"""
    correct_fp = 0
    correct_valid = 0
    total_fp = sum(1 for c in test_cases if c['is_fp'])
    total_valid = sum(1 for c in test_cases if not c['is_fp'])
    
    fast_path_count = 0
    consensus_count = 0
    vote_count = 0
    
    for case in test_cases:
        # Stage 1: assumption_based
        stage1_correct = random.random() < ACTUAL_PERFORMANCE['assumption_based']['fp_detection'] / 100
        stage1_confidence = random.choice([0.95, 0.85, 0.75])
        
        # Fast path - high confidence FP
        if case['is_fp'] and stage1_correct and stage1_confidence > 0.9:
            correct_fp += 1
            fast_path_count += 1
            continue
        
        # Stage 2: alert_fatigue
        stage2_correct = random.random() < ACTUAL_PERFORMANCE['alert_fatigue_prevention']['fp_detection'] / 100
        
        # If both agree
        if stage1_correct == stage2_correct:
            if case['is_fp'] and stage1_correct:
                correct_fp += 1
            elif not case['is_fp'] and not stage1_correct:
                correct_valid += 1
            consensus_count += 1
            continue
        
        # Stage 3: worksite_reality (tiebreaker)
        stage3_correct = random.random() < ACTUAL_PERFORMANCE['worksite_reality']['fp_detection'] / 100
        
        # Majority vote
        votes = sum([stage1_correct, stage2_correct, stage3_correct])
        ensemble_says_fp = votes >= 2
        
        if case['is_fp'] and ensemble_says_fp:
            correct_fp += 1
        elif not case['is_fp'] and not ensemble_says_fp:
            correct_valid += 1
        vote_count += 1
    
    return {
        'approach': 'ensemble',
        'fp_detection': (correct_fp / total_fp * 100) if total_fp > 0 else 0,
        'valid_protection': (correct_valid / total_valid * 100) if total_valid > 0 else 100,
        'correct_fp': correct_fp,
        'correct_valid': correct_valid,
        'total_fp': total_fp,
        'total_valid': total_valid,
        'method_distribution': {
            'fast_path': fast_path_count,
            'consensus': consensus_count,
            'vote': vote_count
        }
    }


def main():
    """Run ensemble simulation"""
    print("\n" + "="*70)
    print("ENSEMBLE SIMULATION BASED ON ACTUAL OVERNIGHT RESULTS")
    print("="*70)
    
    # Run multiple simulations for stability
    num_simulations = 10
    all_results = []
    
    print("\nRunning simulations...")
    for i in range(num_simulations):
        results, _ = simulate_ensemble_performance(1000)
        all_results.append(results)
    
    # Average results
    final_results = {}
    for approach in ['assumption_based', 'alert_fatigue', 'worksite_reality', 'ensemble']:
        fp_rates = [r[approach]['fp_detection'] for r in all_results]
        valid_rates = [r[approach]['valid_protection'] for r in all_results]
        
        final_results[approach] = {
            'fp_detection': sum(fp_rates) / len(fp_rates),
            'valid_protection': sum(valid_rates) / len(valid_rates),
            'fp_std': max(fp_rates) - min(fp_rates),
            'valid_std': max(valid_rates) - min(valid_rates)
        }
    
    # Display results
    print("\n" + "="*70)
    print("SIMULATION RESULTS (Average of 10 runs)")
    print("="*70)
    
    print(f"\n{'Approach':<25} {'FP Detection':<20} {'Valid Protection'}")
    print("-"*70)
    
    for approach, result in final_results.items():
        print(f"{approach:<25} {result['fp_detection']:>6.1f}% (±{result['fp_std']:.1f}%)    "
              f"{result['valid_protection']:>6.1f}% (±{result['valid_std']:.1f}%)")
    
    # Calculate improvement
    best_single = max(
        [v for k, v in final_results.items() if k != 'ensemble'],
        key=lambda x: x['fp_detection'] if x['valid_protection'] >= 95 else 0
    )
    best_single_name = [k for k, v in final_results.items() 
                       if v == best_single and k != 'ensemble'][0]
    
    ensemble_result = final_results['ensemble']
    improvement = ensemble_result['fp_detection'] - best_single['fp_detection']
    
    print("\n" + "="*70)
    print("KEY FINDINGS")
    print("="*70)
    
    print(f"\n1. Ensemble Performance:")
    print(f"   - FP Detection: {ensemble_result['fp_detection']:.1f}%")
    print(f"   - Valid Protection: {ensemble_result['valid_protection']:.1f}%")
    
    print(f"\n2. Best Single Approach ({best_single_name}):")
    print(f"   - FP Detection: {best_single['fp_detection']:.1f}%")
    print(f"   - Valid Protection: {best_single['valid_protection']:.1f}%")
    
    print(f"\n3. Ensemble Advantage:")
    print(f"   - {improvement:+.1f}% better FP detection")
    print(f"   - Maintains {ensemble_result['valid_protection']:.0f}%+ valid protection")
    
    # Method distribution from last run
    last_ensemble = all_results[-1]['ensemble']
    if 'method_distribution' in last_ensemble:
        total = sum(last_ensemble['method_distribution'].values())
        print(f"\n4. Ensemble Efficiency:")
        print(f"   - Fast path used: {last_ensemble['method_distribution']['fast_path']/total*100:.1f}% of cases")
        print(f"   - Consensus reached: {last_ensemble['method_distribution']['consensus']/total*100:.1f}% of cases")
        print(f"   - Full vote needed: {last_ensemble['method_distribution']['vote']/total*100:.1f}% of cases")
    
    print("\n" + "="*70)
    print("PRODUCTION REALITY ADJUSTMENT")
    print("="*70)
    
    # Apply production degradation
    production_factor = 0.85  # 15% degradation
    
    print(f"\nAssuming 15% performance degradation in production:")
    print(f"\n{'Approach':<25} {'Expected Production Performance'}")
    print("-"*50)
    
    for approach, result in final_results.items():
        prod_fp = result['fp_detection'] * production_factor
        print(f"{approach:<25} {prod_fp:>6.1f}% FP detection")
    
    ensemble_prod = ensemble_result['fp_detection'] * production_factor
    best_single_prod = best_single['fp_detection'] * production_factor
    
    print(f"\n5. Production Comparison:")
    print(f"   - Ensemble: {ensemble_prod:.1f}% (still above 70% target ✅)")
    print(f"   - Best Single: {best_single_prod:.1f}% {'(above 70% ✅)' if best_single_prod >= 70 else '(below 70% ❌)'}")
    
    print("\n" + "="*70)
    print("FINAL RECOMMENDATION")
    print("="*70)
    
    if improvement > 5 and ensemble_prod >= 70:
        print("\n✅ DEPLOY ENSEMBLE APPROACH")
        print(f"\nReasons:")
        print(f"1. {improvement:.1f}% better than best single approach")
        print(f"2. Expected {ensemble_prod:.1f}% production performance (exceeds 70% target)")
        print(f"3. Multiple validation layers reduce risk")
        print(f"4. Fast path optimization for {last_ensemble['method_distribution']['fast_path']/total*100:.0f}% of cases")
        print(f"5. Can adapt to different patterns without code changes")
    else:
        print("\n⚠️  CONSIDER SINGLE APPROACH")
        print(f"\nReasons:")
        print(f"1. Only {improvement:.1f}% improvement from ensemble")
        print(f"2. {best_single_name} is simpler to implement and maintain")
        print(f"3. Still achieves {best_single_prod:.1f}% expected production performance")
    
    # Save results
    with open('ensemble_simulation_results.json', 'w') as f:
        json.dump({
            'simulation_date': datetime.now().isoformat(),
            'num_simulations': num_simulations,
            'results': final_results,
            'production_estimates': {
                'ensemble': ensemble_prod,
                'best_single': best_single_prod,
                'best_single_name': best_single_name
            },
            'recommendation': 'ensemble' if improvement > 5 and ensemble_prod >= 70 else 'single'
        }, f, indent=2)
    
    print("\n" + "="*70)


if __name__ == "__main__":
    main()