#!/usr/bin/env python3
"""
Analyze VLM outputs vs ground truth to find disagreement cases
"""

import json
import re
from pathlib import Path
from datetime import datetime

class VLMGroundTruthAnalyzer:
    def __init__(self):
        self.disagreements = []
        self.agreements = []
        self.analysis_results = {
            'total_cases': 0,
            'agreements': 0,
            'disagreements': 0,
            'vlm_correct_rate': 0,
            'disagreement_types': {
                'vlm_fp_human_tp': 0,  # VLM says FP, Human says TP
                'vlm_tp_human_fp': 0   # VLM says TP, Human says FP
            }
        }
        
    def extract_confidence_scores(self, content):
        """Extract confidence scores from VLM response"""
        scores = {
            'fp_likelihood': None,
            'person_present': None,
            'ppe_compliance': None,
            'violation_confidence': None
        }
        
        # Extract FP likelihood
        fp_match = re.search(r'false positive likelihood[:\s]+(\d+)%', content, re.IGNORECASE)
        if not fp_match:
            fp_match = re.search(r'likelihood of false positive[:\s]+(\d+)%', content, re.IGNORECASE)
        if not fp_match:
            fp_match = re.search(r'false positive[:\s]+(\d+)%', content, re.IGNORECASE)
        
        if fp_match:
            scores['fp_likelihood'] = int(fp_match.group(1))
            
        # Extract person presence
        if 'person present: yes' in content.lower():
            scores['person_present'] = True
        elif 'person present: no' in content.lower():
            scores['person_present'] = False
            
        # Extract PPE compliance
        if 'ppe compliance: complete' in content.lower():
            scores['ppe_compliance'] = 'COMPLETE'
        elif 'ppe compliance: incomplete' in content.lower():
            scores['ppe_compliance'] = 'INCOMPLETE'
        elif 'ppe compliance: none' in content.lower():
            scores['ppe_compliance'] = 'NONE'
            
        # Extract violation confidence
        viol_match = re.search(r'violation confidence[:\s]+(\d+)%', content, re.IGNORECASE)
        if viol_match:
            scores['violation_confidence'] = int(viol_match.group(1))
            
        return scores
    
    def determine_vlm_prediction(self, scores):
        """Determine what VLM thinks based on confidence scores"""
        # Primary indicator: FP likelihood
        if scores['fp_likelihood'] is not None:
            if scores['fp_likelihood'] > 50:
                return 'FALSE_POSITIVE'
            else:
                return 'TRUE_POSITIVE'
                
        # Secondary indicator: PPE compliance (if person present)
        if scores['person_present'] == True:
            if scores['ppe_compliance'] == 'COMPLETE':
                return 'FALSE_POSITIVE'
            elif scores['ppe_compliance'] in ['INCOMPLETE', 'NONE']:
                return 'TRUE_POSITIVE'
                
        # Tertiary indicator: No person usually means FP
        if scores['person_present'] == False:
            return 'FALSE_POSITIVE'
            
        # Default to TRUE_POSITIVE if uncertain
        return 'UNCERTAIN'
    
    def analyze_case(self, case_data, ground_truth):
        """Analyze a single case for agreement/disagreement"""
        # Extract confidence scores
        confidence_content = case_data.get('confidence_response', '')
        scores = self.extract_confidence_scores(confidence_content)
        
        # Determine VLM prediction
        vlm_prediction = self.determine_vlm_prediction(scores)
        
        # Compare with ground truth
        agrees = (vlm_prediction == ground_truth)
        
        case_result = {
            'case_number': case_data['case_number'],
            'ground_truth': ground_truth,
            'vlm_prediction': vlm_prediction,
            'agrees': agrees,
            'fp_likelihood': scores['fp_likelihood'],
            'person_present': scores['person_present'],
            'ppe_compliance': scores['ppe_compliance'],
            'violation_confidence': scores['violation_confidence'],
            'description_snippet': case_data.get('description', '')[:200] + '...',
            'confidence_snippet': confidence_content[:300] + '...'
        }
        
        if agrees:
            self.agreements.append(case_result)
        else:
            self.disagreements.append(case_result)
            # Track disagreement type
            if vlm_prediction == 'FALSE_POSITIVE' and ground_truth == 'TRUE_POSITIVE':
                self.analysis_results['disagreement_types']['vlm_fp_human_tp'] += 1
            elif vlm_prediction == 'TRUE_POSITIVE' and ground_truth == 'FALSE_POSITIVE':
                self.analysis_results['disagreement_types']['vlm_tp_human_fp'] += 1
                
        return case_result
    
    def parse_markdown_file(self, file_path, ground_truth):
        """Parse markdown file to extract case data"""
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # Split by case number pattern
        case_sections = re.split(r'\n## Case Number: ', content)
        
        cases = []
        for section in case_sections[1:]:  # Skip first empty section
            lines = section.split('\n')
            case_number = lines[0].strip()
            
            # Find description and confidence sections
            desc_start = section.find('### Description:')
            desc_end = section.find('### Confidence Analysis:')
            conf_start = desc_end
            conf_end = section.find('---', conf_start)
            
            if desc_start != -1 and desc_end != -1:
                description = section[desc_start:desc_end].strip()
                description = description.replace('### Description:', '').strip()
            else:
                description = ''
                
            if conf_start != -1 and conf_end != -1:
                confidence = section[conf_start:conf_end].strip()
                confidence = confidence.replace('### Confidence Analysis:', '').strip()
            else:
                confidence = ''
                
            cases.append({
                'case_number': case_number,
                'description': description,
                'confidence_response': confidence
            })
            
        return cases
    
    def analyze_all_cases(self):
        """Analyze all 1250 cases"""
        # Parse FALSE POSITIVE cases
        fp_file = Path('valo_comprehensive_data/false_positives/false_positive_analysis_20250725_232934.md')
        if fp_file.exists():
            fp_cases = self.parse_markdown_file(fp_file, 'FALSE_POSITIVE')
            for case in fp_cases:
                self.analyze_case(case, 'FALSE_POSITIVE')
                
        # Parse TRUE POSITIVE cases  
        tp_file = Path('valo_comprehensive_data/true_positives/true_positive_analysis_20250725_232934.md')
        if tp_file.exists():
            tp_cases = self.parse_markdown_file(tp_file, 'TRUE_POSITIVE')
            for case in tp_cases:
                self.analyze_case(case, 'TRUE_POSITIVE')
                
        # Calculate statistics
        self.analysis_results['total_cases'] = len(self.agreements) + len(self.disagreements)
        self.analysis_results['agreements'] = len(self.agreements)
        self.analysis_results['disagreements'] = len(self.disagreements)
        
        if self.analysis_results['total_cases'] > 0:
            self.analysis_results['vlm_correct_rate'] = (
                self.analysis_results['agreements'] / self.analysis_results['total_cases'] * 100
            )
    
    def generate_disagreement_report(self):
        """Generate detailed report of disagreement cases"""
        total = self.analysis_results['total_cases']
        if total == 0:
            return "No cases found to analyze!"
            
        agree_pct = self.analysis_results['agreements']/total*100 if total > 0 else 0
        disagree_pct = self.analysis_results['disagreements']/total*100 if total > 0 else 0
        
        report = f"""# VLM vs Ground Truth Disagreement Analysis

## Summary Statistics

- **Total Cases Analyzed**: {self.analysis_results['total_cases']}
- **Agreement Cases**: {self.analysis_results['agreements']} ({agree_pct:.1f}%)
- **Disagreement Cases**: {self.analysis_results['disagreements']} ({disagree_pct:.1f}%)
- **VLM Accuracy**: {self.analysis_results['vlm_correct_rate']:.1f}%

### Disagreement Breakdown:
- **VLM says FP, Human says TP**: {self.analysis_results['disagreement_types']['vlm_fp_human_tp']} cases
- **VLM says TP, Human says FP**: {self.analysis_results['disagreement_types']['vlm_tp_human_fp']} cases

## Disagreement Cases (VLM vs Human)

"""
        # Add detailed disagreement cases
        for i, case in enumerate(self.disagreements, 1):
            report += f"""
### {i}. Case: {case['case_number']}
- **Ground Truth**: {case['ground_truth']}
- **VLM Prediction**: {case['vlm_prediction']}
- **FP Likelihood**: {case['fp_likelihood']}%
- **Person Present**: {case['person_present']}
- **PPE Compliance**: {case['ppe_compliance']}
- **Violation Confidence**: {case['violation_confidence']}%

**Description Preview**:
{case['description_snippet']}

**Confidence Analysis Preview**:
{case['confidence_snippet']}

---
"""
        
        return report
    
    def save_results(self):
        """Save analysis results"""
        # Save detailed disagreement report
        with open('vlm_ground_truth_disagreements.md', 'w', encoding='utf-8') as f:
            f.write(self.generate_disagreement_report())
            
        # Save JSON data for further analysis
        results = {
            'analysis_timestamp': datetime.now().isoformat(),
            'summary': self.analysis_results,
            'disagreements': self.disagreements,
            'total_agreements': len(self.agreements)
        }
        
        with open('vlm_disagreement_analysis.json', 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2)
            
        print(f"\n✅ Analysis Complete!")
        print(f"📊 Results:")
        print(f"   - Total cases: {self.analysis_results['total_cases']}")
        print(f"   - Agreements: {self.analysis_results['agreements']} ({self.analysis_results['vlm_correct_rate']:.1f}% accuracy)")
        print(f"   - Disagreements: {self.analysis_results['disagreements']}")
        print(f"\n📁 Files created:")
        print(f"   - vlm_ground_truth_disagreements.md (detailed report)")
        print(f"   - vlm_disagreement_analysis.json (data for webpage)")

if __name__ == "__main__":
    analyzer = VLMGroundTruthAnalyzer()
    analyzer.analyze_all_cases()
    analyzer.save_results()