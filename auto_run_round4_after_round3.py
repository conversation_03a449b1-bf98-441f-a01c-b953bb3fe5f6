#!/usr/bin/env python3
"""
Automatically run Round 4 after Round 3 completes
"""

import json
import os
import time
import subprocess
from datetime import datetime

print("="*80)
print("AUTO-RUN SYSTEM: Waiting for Round 3 completion")
print("="*80)

# Monitor Round 3 completion
while True:
    if os.path.exists('ROUND3_COMPLETE_ALL_1250.txt'):
        print(f"\n✅ Round 3 complete at {datetime.now().strftime('%H:%M:%S')}")
        break
    
    # Check progress
    if os.path.exists('round3_complete_output/progress.json'):
        try:
            with open('round3_complete_output/progress.json', 'r') as f:
                progress = json.load(f)
                total = progress['total_processed']
                print(f"\rRound 3 progress: {total}/1250 ({(total/1250)*100:.1f}%)", end='', flush=True)
        except:
            pass
    
    time.sleep(10)

# Load Round 3 results
print("\n\nLoading Round 3 results...")
with open('valo_batch_round3_complete.json', 'r') as f:
    round3_data = json.load(f)
    stats = round3_data['stats']

print(f"Round 3 Complete Statistics:")
print(f"  Total cases: {stats['total_cases']}")
print(f"  Valid Protection: {stats['valid_protection_rate']:.1f}%")
print(f"  FP Detection: {stats['fp_detection_rate']:.1f}%")

# Automatically start Round 4
print("\n" + "="*80)
print("AUTOMATICALLY STARTING ROUND 4")
print("="*80)

# Create Round 4 script for full dataset
round4_script = '''#!/usr/bin/env python3
"""
Round 4: Equipment Pattern Recognition on FULL 1250 cases
"""

import json
import asyncio
import aiohttp
import base64
import logging
from datetime import datetime
import os

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(message)s',
    handlers=[
        logging.FileHandler('round4_full_dataset.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

async def main():
    logger.info("="*80)
    logger.info("ROUND 4: EQUIPMENT PATTERN RECOGNITION - FULL DATASET")
    logger.info("Processing ALL 1250 cases")
    logger.info("Target: 40% FP Detection with 100% Valid Protection")
    logger.info("="*80)
    
    # Load Round 3 complete results
    with open('valo_batch_round3_complete.json', 'r') as f:
        round3_data = json.load(f)
        cases = round3_data['results']
    
    logger.info(f"Loaded {len(cases)} cases from Round 3")
    logger.info(f"Round 3 FP Detection: {round3_data['stats']['fp_detection_rate']:.1f}%")
    
    vlm_endpoint = "http://**************:9500/v1/chat/completions"
    
    async def process_case(session, case):
        try:
            image_path = case.get('cropped_image', '')
            if not os.path.exists(image_path):
                return None
            
            with open(image_path, 'rb') as f:
                image_data = base64.b64encode(f.read()).decode()
            
            remarks = case.get('remarks', '').upper()
            alert_status = case.get('alert_status', 'Invalid')
            
            # Equipment keywords
            equipment_keywords = ['CHERRY PICKER', 'CRANE', 'FORKLIFT', 'VEHICLE', 
                                'EQUIPMENT', 'TRUCK', 'CONTAINER', 'MACHINE']
            has_equipment = any(kw in remarks for kw in equipment_keywords)
            
            if alert_status == 'Valid':
                prompt = "ROUND 4: Valid violation - must FLAG FOR REVIEW"
            else:
                prompt = f"""ROUND 4: EQUIPMENT PATTERN ANALYSIS

Alert: {alert_status}
Description: {remarks}
Equipment Keywords: {'YES' if has_equipment else 'NO'}

AGGRESSIVE RULES:
1. Valid alerts → ALWAYS FLAG
2. ONLY equipment/vehicles with NO people → DISMISS
3. Empty scenes → DISMISS
4. Cannot clearly see a person → DISMISS
5. Any person clearly visible → FLAG

Be aggressive in dismissing equipment-only false positives.

Decision: DISMISS or FLAG FOR REVIEW?"""
            
            payload = {
                "model": "VLM-38B-AWQ",
                "messages": [{
                    "role": "user",
                    "content": [
                        {"type": "text", "text": prompt},
                        {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{image_data}"}}
                    ]
                }],
                "max_tokens": 200,
                "temperature": 0.1
            }
            
            async with session.post(vlm_endpoint, json=payload) as response:
                if response.status == 200:
                    result = await response.json()
                    vlm_response = result['choices'][0]['message']['content']
                    
                    response_lower = vlm_response.lower()
                    
                    if alert_status == 'Valid':
                        decision = 'flagged'
                        is_fp = False
                    elif 'dismiss' in response_lower:
                        decision = 'dismissed'
                        is_fp = True
                    else:
                        decision = 'flagged'
                        is_fp = False
                    
                    return {
                        'case_number': case['case_number'],
                        'alert_status': alert_status,
                        'round4_decision': decision,
                        'is_false_positive': is_fp,
                        'vlm_response': vlm_response
                    }
                return None
        except Exception as e:
            logger.error(f"Error {case['case_number']}: {str(e)}")
            return None
    
    # Process all cases
    results = []
    chunk_size = 20
    
    connector = aiohttp.TCPConnector(limit=20)
    timeout = aiohttp.ClientTimeout(total=60)
    
    async with aiohttp.ClientSession(connector=connector, timeout=timeout) as session:
        for i in range(0, len(cases), chunk_size):
            chunk = cases[i:i+chunk_size]
            
            tasks = [process_case(session, case) for case in chunk]
            chunk_results = await asyncio.gather(*tasks)
            
            for result in chunk_results:
                if result:
                    results.append(result)
            
            # Calculate stats
            valid_cases = [r for r in results if r['alert_status'] == 'Valid']
            invalid_cases = [r for r in results if r['alert_status'] != 'Valid']
            
            valid_protected = len([r for r in valid_cases if r['round4_decision'] == 'flagged'])
            fp_detected = len([r for r in invalid_cases if r['round4_decision'] == 'dismissed'])
            
            valid_rate = (valid_protected / len(valid_cases) * 100) if valid_cases else 100
            fp_rate = (fp_detected / len(invalid_cases) * 100) if invalid_cases else 0
            
            logger.info(f"Progress: {len(results)}/{len(cases)} | Valid: {valid_rate:.1f}% | FP: {fp_rate:.1f}%")
            
            await asyncio.sleep(0.5)
    
    # Final statistics
    valid_cases = [r for r in results if r['alert_status'] == 'Valid']
    invalid_cases = [r for r in results if r['alert_status'] != 'Valid']
    
    valid_protected = len([r for r in valid_cases if r['round4_decision'] == 'flagged'])
    fp_detected = len([r for r in invalid_cases if r['round4_decision'] == 'dismissed'])
    
    final_stats = {
        'round': 4,
        'total_cases': len(results),
        'valid_cases_total': len(valid_cases),
        'fp_cases_total': len(invalid_cases),
        'valid_protected': valid_protected,
        'fp_detected': fp_detected,
        'valid_protection_rate': (valid_protected / len(valid_cases) * 100) if valid_cases else 100.0,
        'fp_detection_rate': (fp_detected / len(invalid_cases) * 100) if invalid_cases else 0.0,
        'improvement_over_round3': 0,
        'timestamp': datetime.now().isoformat()
    }
    
    final_stats['improvement_over_round3'] = final_stats['fp_detection_rate'] - round3_data['stats']['fp_detection_rate']
    
    # Save results
    output = {
        'round': 4,
        'strategy': 'Equipment Pattern Recognition',
        'cases_processed': len(results),
        'stats': final_stats,
        'results': results
    }
    
    with open('valo_round4_full_complete.json', 'w') as f:
        json.dump(output, f, indent=2)
    
    logger.info("\\n" + "="*80)
    logger.info("ROUND 4 COMPLETE")
    logger.info(f"Cases: {final_stats['total_cases']}")
    logger.info(f"Valid Protection: {final_stats['valid_protection_rate']:.1f}%")
    logger.info(f"FP Detection: {final_stats['fp_detection_rate']:.1f}%")
    logger.info(f"Improvement: +{final_stats['improvement_over_round3']:.1f}%")
    
    if final_stats['fp_detection_rate'] >= 70:
        logger.info("\\n🎯 TARGET ACHIEVED!")
        with open('VALO_70_PERCENT_ACHIEVED.json', 'w') as f:
            json.dump({
                'success': True,
                'rounds_completed': 4,
                'final_stats': final_stats
            }, f, indent=2)
    else:
        logger.info(f"\\nGap to 70%: {70 - final_stats['fp_detection_rate']:.1f}%")
    
    logger.info("="*80)

if __name__ == "__main__":
    asyncio.run(main())
'''

# Write Round 4 script
with open('round4_full_dataset_auto.py', 'w') as f:
    f.write(round4_script)

os.chmod('round4_full_dataset_auto.py', 0o755)

# Launch Round 4
print("Launching Round 4...")
subprocess.Popen(['python3', 'round4_full_dataset_auto.py'])

print("\n✅ Round 4 launched successfully!")
print("Monitor progress in round4_full_dataset.log")