#!/usr/bin/env python3
"""
Monitor the auto-learning progress
"""

import json
import os
import time
from datetime import datetime

def monitor_progress():
    """Monitor the auto-learning progress"""
    
    progress_file = 'auto_learning_progress.json'
    
    print("VALO AUTO-LEARNING PROGRESS MONITOR")
    print("="*70)
    print("Press Ctrl+C to stop monitoring\n")
    
    while True:
        try:
            if os.path.exists(progress_file):
                with open(progress_file, 'r') as f:
                    progress = json.load(f)
                
                round_num = progress.get('current_round', 0)
                case_num = progress.get('current_case', 0)
                total_cases = progress.get('total_cases', 1250)
                thresholds = progress.get('thresholds', {})
                timestamp = progress.get('timestamp', '')
                
                # Calculate progress
                progress_pct = (case_num / total_cases * 100) if total_cases > 0 else 0
                
                # Clear screen and show status
                print("\033[2J\033[H")  # Clear screen
                print("VALO AUTO-LEARNING PROGRESS MONITOR")
                print("="*70)
                print(f"Last Update: {timestamp}")
                print(f"\nRound: {round_num}/5")
                print(f"Case: {case_num}/{total_cases} ({progress_pct:.1f}%)")
                print(f"\nCurrent Thresholds:")
                for key, value in thresholds.items():
                    print(f"  {key}: {value}%")
                
                # Check for round results
                for i in range(1, 6):
                    result_file = f'valo_auto_round_{i}_results.json'
                    if os.path.exists(result_file):
                        with open(result_file, 'r') as f:
                            round_data = json.load(f)
                        metrics = round_data.get('metrics', {})
                        print(f"\nRound {i} Results:")
                        print(f"  Accuracy: {metrics.get('accuracy', 0):.1f}%")
                        print(f"  FP Detection: {metrics.get('fp_detection', 0):.1f}%")
                        print(f"  Valid Protection: {metrics.get('valid_protection', 0):.1f}%")
            else:
                print("Waiting for auto-learning to start...")
            
            time.sleep(5)  # Update every 5 seconds
            
        except KeyboardInterrupt:
            print("\n\nMonitoring stopped.")
            break
        except Exception as e:
            print(f"Error: {e}")
            time.sleep(5)

if __name__ == "__main__":
    monitor_progress()