#!/usr/bin/env python3
"""
Quick test of enhanced prompt on key structure false positive patterns
"""

import json
import base64
import requests
import os
from datetime import datetime
import time

VLM_API_URL = "http://100.106.127.35:9500/v1/chat/completions"
VLM_MODEL = "VLM-38B-AWQ"

def encode_image(image_path):
    """Encode image to base64"""
    try:
        if os.path.exists(image_path):
            with open(image_path, 'rb') as f:
                return base64.b64encode(f.read()).decode('utf-8')
    except:
        pass
    return None

def call_vlm(source_b64, cropped_b64, prompt):
    """Call VLM with dual images"""
    
    payload = {
        "model": VLM_MODEL,
        "messages": [{
            "role": "user",
            "content": [
                {"type": "text", "text": prompt},
                {"type": "text", "text": "\n\nSOURCE IMAGE (full context):"},
                {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{source_b64}"}},
                {"type": "text", "text": "\n\nCROPPED IMAGE (area of concern):"},
                {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{cropped_b64}"}}
            ]
        }],
        "temperature": 0.1,
        "max_tokens": 200
    }
    
    try:
        response = requests.post(VLM_API_URL, json=payload, timeout=20)
        if response.status_code == 200:
            result = response.json()
            return result['choices'][0]['message']['content'].strip()
    except:
        pass
    
    return None

def main():
    print("QUICK TEST: ENHANCED PROMPT ON KEY STRUCTURE PATTERNS")
    print("="*60)
    
    # Load enhanced prompt
    with open('intelligent_prompt_enhanced_structures.txt', 'r') as f:
        enhanced_prompt = f.read()
    
    # Load data
    with open('valo_batch_round3_complete.json', 'r') as f:
        data = json.load(f)
    
    # Find specific test cases
    test_patterns = {
        "Crane Structure": "CRANE STRUCTURE CAPTURED AS LS",
        "Vessel Structure": "VESSEL STRUCTURE CAPTURED AS LS",
        "PM Structure": "PM STRUCTURE CAPTURED AS LS",
        "Spreader": "SPREADER",
        "Worker Full PPE": "LS FULL PPE"
    }
    
    results = []
    
    for pattern_name, search_term in test_patterns.items():
        # Find a case with this pattern
        test_case = None
        for case in data['results']:
            if case['is_false_positive'] and case.get('remarks'):
                if search_term in case['remarks'].upper():
                    test_case = case
                    break
        
        if not test_case:
            continue
            
        print(f"\n\nTesting: {pattern_name}")
        print(f"Case: {test_case['case_number']}")
        print(f"Remark: {test_case['remarks']}")
        
        # Encode images
        source_b64 = encode_image(test_case['source_image'])
        cropped_b64 = encode_image(test_case['cropped_image'])
        
        if not source_b64 or not cropped_b64:
            print("ERROR: Could not encode images")
            continue
        
        # Test enhanced prompt
        print("Calling VLM...")
        response = call_vlm(source_b64, cropped_b64, enhanced_prompt)
        
        if response:
            print(f"\nVLM Response:")
            print(response)
            
            # Check result
            predicted_fp = 'YES' in response.upper()
            correct = predicted_fp == test_case['is_false_positive']
            
            print(f"\nResult: {'✓ CORRECT' if correct else '✗ INCORRECT'}")
            
            # Extract entity type
            entity = None
            if 'Entity Type:' in response:
                entity_line = [l for l in response.split('\n') if 'Entity Type:' in l]
                if entity_line:
                    entity = entity_line[0].split('Entity Type:')[1].strip()
                    print(f"Detected as: {entity}")
            
            results.append({
                'pattern': pattern_name,
                'correct': correct,
                'entity': entity,
                'response': response
            })
        
        time.sleep(2)  # Rate limit
    
    # Summary
    print("\n\n" + "="*60)
    print("SUMMARY")
    print("="*60)
    
    if results:
        correct_count = sum(1 for r in results if r['correct'])
        accuracy = correct_count / len(results) * 100
        
        print(f"Tested: {len(results)} key patterns")
        print(f"Correct: {correct_count}/{len(results)} ({accuracy:.0f}%)")
        
        print("\nEntity Detection:")
        for r in results:
            print(f"- {r['pattern']}: {r.get('entity', 'Unknown')}")
        
        # Save results
        with open('enhanced_prompt_quick_test_results.json', 'w') as f:
            json.dump({
                'timestamp': datetime.now().isoformat(),
                'results': results,
                'accuracy': accuracy
            }, f, indent=2)
        
        print(f"\nResults saved to: enhanced_prompt_quick_test_results.json")
        
        if accuracy >= 80:
            print("\n✓ Enhanced prompt performing well!")
            print("Ready to proceed with full dataset testing")
        else:
            print("\n⚠ Enhanced prompt needs refinement")

if __name__ == "__main__":
    main()