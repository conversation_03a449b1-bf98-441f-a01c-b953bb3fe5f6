#!/usr/bin/env python3
"""
Check VLM endpoint status and connectivity
Test with a small sample before attempting full dataset
"""

import asyncio
import aiohttp
import json
import base64
import time
import os
from datetime import datetime

async def check_vlm_health():
    """Check if VLM endpoint is responsive"""
    endpoint = "http://100.106.127.35:9500/v1/chat/completions"
    
    print("="*60)
    print("VLM ENDPOINT STATUS CHECK")
    print("="*60)
    print(f"Endpoint: {endpoint}")
    print(f"Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Try a simple health check
    try:
        async with aiohttp.ClientSession() as session:
            # Test 1: Basic connectivity
            print("1. Testing basic connectivity...")
            start = time.time()
            
            timeout = aiohttp.ClientTimeout(total=10)
            async with session.get("http://100.106.127.35:9500", timeout=timeout) as response:
                latency = (time.time() - start) * 1000
                print(f"   ✓ Connection successful (latency: {latency:.0f}ms)")
                print(f"   Status: {response.status}")
    except Exception as e:
        print(f"   ✗ Connection failed: {e}")
        return False
    
    # Test 2: Try actual VLM request with a test image
    print("\n2. Testing VLM inference...")
    
    # Find a test image
    test_image = None
    with open('valo_batch_round3_complete.json', 'r') as f:
        data = json.load(f)
        if data['results']:
            test_image = data['results'][0]['cropped_image']
    
    if not test_image or not os.path.exists(test_image):
        print("   ✗ No test image found")
        return False
    
    try:
        with open(test_image, 'rb') as f:
            image_data = base64.b64encode(f.read()).decode('utf-8')
        
        payload = {
            "model": "VLM-38B-AWQ",
            "messages": [{
                "role": "user",
                "content": [
                    {"type": "text", "text": "Is this a false positive? YES/NO"},
                    {"type": "image_url", 
                     "image_url": {"url": f"data:image/jpeg;base64,{image_data}"}}
                ]
            }],
            "temperature": 0.1,
            "max_tokens": 50
        }
        
        async with aiohttp.ClientSession() as session:
            start = time.time()
            timeout = aiohttp.ClientTimeout(total=30)
            
            async with session.post(endpoint, json=payload, timeout=timeout) as response:
                inference_time = time.time() - start
                
                if response.status == 200:
                    result = await response.json()
                    print(f"   ✓ Inference successful")
                    print(f"   Response time: {inference_time:.1f}s")
                    print(f"   Model response: {result['choices'][0]['message']['content'][:50]}...")
                    return True
                else:
                    print(f"   ✗ Inference failed: HTTP {response.status}")
                    return False
                    
    except asyncio.TimeoutError:
        print(f"   ✗ Request timeout (>30s)")
        return False
    except Exception as e:
        print(f"   ✗ Error: {e}")
        return False

async def test_batch_performance():
    """Test VLM performance with different batch sizes"""
    print("\n3. Testing batch performance...")
    
    endpoint = "http://100.106.127.35:9500/v1/chat/completions"
    
    # Load test cases
    with open('valo_batch_round3_complete.json', 'r') as f:
        data = json.load(f)
        test_cases = data['results'][:20]  # Test with 20 cases
    
    batch_sizes = [1, 5, 10]
    results = {}
    
    for batch_size in batch_sizes:
        print(f"\n   Testing batch size: {batch_size}")
        
        successful = 0
        failed = 0
        total_time = 0
        
        async with aiohttp.ClientSession() as session:
            for i in range(0, len(test_cases), batch_size):
                batch = test_cases[i:i+batch_size]
                start_batch = time.time()
                
                # Create tasks for batch
                tasks = []
                for case in batch:
                    if os.path.exists(case['cropped_image']):
                        task = test_single_case(session, endpoint, case['cropped_image'])
                        tasks.append(task)
                
                # Execute batch
                batch_results = await asyncio.gather(*tasks, return_exceptions=True)
                batch_time = time.time() - start_batch
                total_time += batch_time
                
                # Count results
                for result in batch_results:
                    if isinstance(result, Exception) or not result:
                        failed += 1
                    else:
                        successful += 1
                
                print(f"      Batch {i//batch_size + 1}: {successful}/{successful+failed} successful, "
                      f"time: {batch_time:.1f}s")
                
                await asyncio.sleep(0.5)  # Small delay between batches
        
        results[batch_size] = {
            'successful': successful,
            'failed': failed,
            'total_time': total_time,
            'avg_time_per_case': total_time / len(test_cases) if test_cases else 0
        }
    
    # Display results
    print("\n   Batch Performance Summary:")
    print("   " + "-"*50)
    print(f"   {'Batch Size':<12} {'Success Rate':<15} {'Avg Time/Case'}")
    print("   " + "-"*50)
    
    for batch_size, stats in results.items():
        success_rate = stats['successful'] / (stats['successful'] + stats['failed']) * 100
        print(f"   {batch_size:<12} {success_rate:<15.1f}% {stats['avg_time_per_case']:.2f}s")
    
    # Recommend optimal batch size
    best_batch = min(results.items(), key=lambda x: x[1]['avg_time_per_case'])[0]
    print(f"\n   ✓ Recommended batch size: {best_batch}")

async def test_single_case(session, endpoint, image_path):
    """Test a single case"""
    try:
        with open(image_path, 'rb') as f:
            image_data = base64.b64encode(f.read()).decode('utf-8')
        
        payload = {
            "model": "VLM-38B-AWQ",
            "messages": [{
                "role": "user",
                "content": [
                    {"type": "text", "text": "Is this a false positive? YES/NO"},
                    {"type": "image_url", 
                     "image_url": {"url": f"data:image/jpeg;base64,{image_data}"}}
                ]
            }],
            "temperature": 0.1,
            "max_tokens": 50
        }
        
        timeout = aiohttp.ClientTimeout(total=20)
        async with session.post(endpoint, json=payload, timeout=timeout) as response:
            if response.status == 200:
                return True
            return False
            
    except Exception:
        return False

async def main():
    """Run all VLM status checks"""
    
    # Check basic health
    is_healthy = await check_vlm_health()
    
    if is_healthy:
        # Test batch performance
        await test_batch_performance()
        
        print("\n" + "="*60)
        print("VLM ENDPOINT STATUS: ✓ OPERATIONAL")
        print("="*60)
        print("\nRecommendations:")
        print("1. VLM is responsive and working")
        print("2. Use batch size 5-10 for optimal performance")
        print("3. Implement retry logic for occasional timeouts")
        print("4. Consider rate limiting to avoid overwhelming the endpoint")
    else:
        print("\n" + "="*60)
        print("VLM ENDPOINT STATUS: ✗ NOT OPERATIONAL")
        print("="*60)
        print("\nIssues detected:")
        print("1. VLM endpoint is not responding properly")
        print("2. Cannot proceed with full dataset testing")
        print("3. Check network connectivity and endpoint status")

if __name__ == "__main__":
    asyncio.run(main())