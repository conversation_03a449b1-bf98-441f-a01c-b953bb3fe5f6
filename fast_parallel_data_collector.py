#!/usr/bin/env python3
"""
Fast Parallel Data Collector - Complete remaining cases
Uses multiple workers for faster processing
"""

import json
import base64
import requests
import os
import time
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading
import queue

class FastParallelDataCollector:
    def __init__(self, num_workers=5):
        self.vlm_url = "http://**************:9500/v1/chat/completions"
        self.vlm_model = "VLM-38B-AWQ"
        self.num_workers = num_workers
        
        # Token limits
        self.description_tokens = 1200
        self.confidence_tokens = 600
        
        # Create session pool
        self.session_pool = queue.Queue()
        for _ in range(num_workers):
            self.session_pool.put(requests.Session())
        
        # Thread safety
        self.write_lock = threading.Lock()
        self.progress_lock = threading.Lock()
        
        # Progress tracking
        self.processed_count = 0
        self.start_time = None
        
    def get_session(self):
        """Get a session from the pool"""
        return self.session_pool.get()
    
    def return_session(self, session):
        """Return session to pool"""
        self.session_pool.put(session)
    
    def encode_image(self, image_path):
        """Encode image to base64"""
        try:
            if os.path.exists(image_path):
                with open(image_path, 'rb') as f:
                    return base64.b64encode(f.read()).decode('utf-8')
        except:
            pass
        return None
    
    def process_case_complete(self, case):
        """Process a single case - both description and confidence"""
        session = self.get_session()
        try:
            # Get description
            cropped_b64 = self.encode_image(case['cropped_image'])
            if not cropped_b64:
                return None
                
            # Description prompt
            desc_prompt = """Analyze this safety alert image in extreme detail. Provide a comprehensive description covering:

1. MAIN SUBJECT IDENTIFICATION
   - Primary subject type: Person/Equipment/Structure/Vehicle
   - If equipment: Exact type (crane/vessel/truck/spreader/container/machinery)
   - If person: Number of individuals, gender if visible, apparent role

2. PERSON DETAILS (if any visible)
   - Exact position and posture
   - Clothing description (colors, type, condition)
   - Safety equipment worn:
     * Head protection (helmet/hard hat) - color, style, properly worn?
     * High-visibility clothing - color, reflective strips visible?
     * Other PPE - gloves, safety shoes, harness, goggles
   - What safety equipment is MISSING?

3. ACTIVITY ANALYSIS
   - What specific action is being performed?
   - Tools or equipment being used
   - Body mechanics and positioning

4. ENVIRONMENT AND CONTEXT
   - Exact location (vessel deck/quay/yard/height/confined space)
   - Surrounding equipment and structures
   - Weather/lighting conditions
   - Potential hazards in vicinity

5. SAFETY ASSESSMENT
   - Primary safety concern visible
   - Violation type if apparent
   - Severity estimation

Provide the most detailed, factual description possible."""
            
            # Get description
            desc_payload = {
                "model": self.vlm_model,
                "messages": [{
                    "role": "user",
                    "content": [
                        {"type": "text", "text": desc_prompt},
                        {"type": "text", "text": "\nCROPPED ALERT IMAGE:"},
                        {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{cropped_b64}"}}
                    ]
                }],
                "temperature": 0.15,
                "max_tokens": self.description_tokens
            }
            
            desc_start = time.time()
            desc_response = session.post(self.vlm_url, json=desc_payload, timeout=90)
            
            if desc_response.status_code != 200:
                return None
                
            description = desc_response.json()['choices'][0]['message']['content']
            desc_time = time.time() - desc_start
            
            # Get confidence analysis
            time.sleep(0.5)  # Brief pause between requests
            
            conf_prompt = f"""You previously provided this description of a safety alert image:

"{description}"

Now, looking at the same image again, analyze:

1. DESCRIPTION ACCURACY (0-100%): How accurately does the description match the image?

2. SUBJECT CONFIRMATION:
   - Is there definitely a PERSON in this image? YES/NO
   - If NO, what is the main subject?
   - Confidence in subject identification: (0-100%)

3. PPE COMPLIANCE CHECK (if person present):
   - Helmet/Hard hat present? YES/NO/PARTIAL/NA
   - High-visibility vest present? YES/NO/PARTIAL/NA
   - Overall PPE compliance: COMPLETE/INCOMPLETE/NONE/NA

4. SAFETY VIOLATION ASSESSMENT:
   - Is there a genuine safety violation visible? YES/NO/UNCERTAIN
   - If YES, describe the specific violation
   - Violation confidence: (0-100%)

5. FALSE POSITIVE INDICATORS:
   - List any factors suggesting this might be a false positive
   - False positive likelihood: (0-100%)

Format your response EXACTLY as:
DESCRIPTION_ACCURACY: [X]%
PERSON_PRESENT: [YES/NO]
MAIN_SUBJECT: [Person/Equipment/Structure/Vehicle/Other]
SUBJECT_CONFIDENCE: [X]%
HELMET_STATUS: [YES/NO/PARTIAL/NA]
VEST_STATUS: [YES/NO/PARTIAL/NA]
PPE_COMPLIANCE: [COMPLETE/INCOMPLETE/NONE/NA]
PPE_CONFIDENCE: [X]%
SAFETY_VIOLATION: [YES/NO/UNCERTAIN]
VIOLATION_DESCRIPTION: [Description or NONE]
VIOLATION_SEVERITY: [MINOR/MODERATE/SEVERE/NA]
VIOLATION_CONFIDENCE: [X]%
FALSE_POSITIVE_LIKELIHOOD: [X]%
FP_INDICATORS: [List or NONE]"""
            
            conf_payload = {
                "model": self.vlm_model,
                "messages": [{
                    "role": "user",
                    "content": [
                        {"type": "text", "text": conf_prompt},
                        {"type": "text", "text": "\nANALYZING THE SAME IMAGE:"},
                        {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{cropped_b64}"}}
                    ]
                }],
                "temperature": 0.1,
                "max_tokens": self.confidence_tokens
            }
            
            conf_start = time.time()
            conf_response = session.post(self.vlm_url, json=conf_payload, timeout=90)
            
            if conf_response.status_code != 200:
                return None
                
            confidence_response = conf_response.json()['choices'][0]['message']['content']
            conf_time = time.time() - conf_start
            
            # Update progress
            with self.progress_lock:
                self.processed_count += 1
                if self.processed_count % 10 == 0:
                    elapsed = time.time() - self.start_time
                    rate = self.processed_count / elapsed * 60
                    print(f"Progress: {self.processed_count} cases, Rate: {rate:.1f} cases/min")
            
            # Return complete result
            return {
                'case_number': case['case_number'],
                'is_false_positive': case['is_false_positive'],
                'description': description,
                'confidence_response': confidence_response,
                'desc_time': desc_time,
                'conf_time': conf_time,
                'total_time': desc_time + conf_time
            }
            
        finally:
            self.return_session(session)
    
    def append_to_markdown(self, result, file_path):
        """Thread-safe append to markdown file"""
        from robust_data_collection_system import RobustDataCollectionSystem
        
        # Parse confidence data
        system = RobustDataCollectionSystem()
        confidence_data = system.parse_confidence_data(result['confidence_response'])
        
        # Create full result object
        full_result = {
            'case_number': result['case_number'],
            'is_false_positive': result['is_false_positive'],
            'description': result['description'],
            'description_length': len(result['description']),
            'confidence_response': result['confidence_response'],
            'confidence_data': confidence_data,
            'processing_time': result['total_time'],
            'timestamp': datetime.now().isoformat(),
            'cropped_image': f"ai_farm_images_fixed_250703/{result['case_number']}_cropped.png",
            'source_image': f"ai_farm_images_fixed_250703/{result['case_number']}.png"
        }
        
        with self.write_lock:
            system.save_to_markdown(full_result, file_path)
    
    def complete_remaining_cases(self):
        """Complete all remaining true positive cases"""
        print("="*80)
        print("🚀 FAST PARALLEL DATA COLLECTION - COMPLETING REMAINING CASES")
        print("="*80)
        print(f"Workers: {self.num_workers}")
        print(f"Starting at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        self.start_time = time.time()
        
        # Load all cases
        with open('valo_batch_round3_complete.json', 'r') as f:
            all_cases = json.load(f)['results']
        
        # Get true positive cases only (we already have all FPs)
        tp_cases = [c for c in all_cases if not c['is_false_positive']]
        
        # Skip first 65 already processed
        remaining_tp_cases = tp_cases[65:]
        
        print(f"\n📊 Remaining cases to process: {len(remaining_tp_cases)}")
        print(f"Estimated time: {len(remaining_tp_cases) * 20 / 60 / self.num_workers:.1f} minutes")
        
        # Output file
        tp_file = "valo_comprehensive_data/true_positives/true_positive_analysis_20250725_232934.md"
        
        # Process in parallel
        with ThreadPoolExecutor(max_workers=self.num_workers) as executor:
            # Submit all tasks
            future_to_case = {}
            for case in remaining_tp_cases:
                future = executor.submit(self.process_case_complete, case)
                future_to_case[future] = case
            
            # Process completed tasks
            completed = 0
            errors = 0
            
            for future in as_completed(future_to_case):
                case = future_to_case[future]
                try:
                    result = future.result()
                    if result:
                        self.append_to_markdown(result, tp_file)
                        completed += 1
                        
                        if completed % 5 == 0:
                            print(f"✓ Completed: {completed}/{len(remaining_tp_cases)}")
                    else:
                        errors += 1
                        print(f"✗ Failed: {case['case_number']}")
                        
                except Exception as e:
                    errors += 1
                    print(f"✗ Error processing {case['case_number']}: {str(e)}")
        
        # Final summary
        elapsed = time.time() - self.start_time
        print("\n" + "="*80)
        print("✅ FAST PARALLEL COLLECTION COMPLETE!")
        print("="*80)
        print(f"Total time: {elapsed/60:.1f} minutes")
        print(f"Cases processed: {completed}")
        print(f"Errors: {errors}")
        print(f"Processing rate: {completed/elapsed*60:.1f} cases/minute")
        print(f"\n📁 Data saved to: {tp_file}")
        print("\n🎉 All 1250 cases have now been processed!")
        print("Ready for comprehensive data analysis!")

if __name__ == "__main__":
    collector = FastParallelDataCollector(num_workers=5)
    collector.complete_remaining_cases()