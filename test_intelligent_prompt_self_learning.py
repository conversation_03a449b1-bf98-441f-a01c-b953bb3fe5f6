#!/usr/bin/env python3
"""
Self-learning test framework for intelligent VLM prompt
Tests with both source and cropped images, learns optimal parameters
"""

import json
import base64
import requests
import os
from datetime import datetime
import time
from collections import defaultdict
import numpy as np
from pathlib import Path

# VLM Configuration
VLM_API_URL = "http://100.106.127.35:9500/v1/chat/completions"
VLM_MODEL = "VLM-38B-AWQ"

class SelfLearningVLMTester:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({'Content-Type': 'application/json'})
        
        # Learning parameters
        self.confidence_thresholds = {
            "person_detection": 70,
            "structure_detection": 80,
            "ppe_detection": 65
        }
        
        # Camera-specific learning
        self.camera_params = defaultdict(lambda: self.confidence_thresholds.copy())
        
        # Violation-specific learning
        self.violation_params = defaultdict(lambda: self.confidence_thresholds.copy())
        
        # Performance tracking
        self.learning_history = []
        self.best_params = None
        self.best_performance = 0
        
    def encode_image(self, image_path):
        """Encode image to base64"""
        try:
            if not os.path.exists(image_path):
                return None
            with open(image_path, 'rb') as f:
                return base64.b64encode(f.read()).decode('utf-8')
        except Exception as e:
            print(f"\nError encoding {image_path}: {e}")
            return None
    
    def create_intelligent_prompt(self, violation_type, confidence_params):
        """Create prompt with current confidence parameters"""
        
        prompt = f"""INTELLIGENT SAFETY ANALYSIS - {violation_type}

DUAL IMAGE ANALYSIS:
- Analyze BOTH source (full view) and cropped (zoomed) images
- Cropped image must contain a PERSON for valid violation

STEP 1 - ENTITY DETECTION (Min confidence: {confidence_params['person_detection']}%):
What is in the CROPPED image?
A) PERSON - Human worker clearly visible
B) STRUCTURE - Crane/vessel/equipment/machinery
C) UNCLEAR - Cannot determine confidently

Structure patterns (confidence threshold: {confidence_params['structure_detection']}%):
- Crane structures, vessel structures, PM equipment
- Spreader mechanisms, mechanical equipment
- If confidence > {confidence_params['structure_detection']}% structure → FALSE POSITIVE

STEP 2 - PPE CHECK (if PERSON, min confidence: {confidence_params['ppe_detection']}%):
Compliant PPE combinations:
- Yellow/white/orange coveralls + vest + hard hat
- High-vis vest (any color) + hard hat
- Life vest + hard hat + safety equipment

If PPE detected with >{confidence_params['ppe_detection']}% confidence → FALSE POSITIVE

STEP 3 - CONTEXT:
From source image: Safe zone? Multiple workers? Standing/walking?

DECISION: Is this a FALSE POSITIVE? YES/NO
Confidence: [percentage]
Entity: [PERSON/STRUCTURE/UNCLEAR]
PPE: [PRESENT/ABSENT/N/A]"""

        return prompt
    
    def call_vlm_dual_image(self, source_b64, cropped_b64, prompt, timeout=20):
        """Call VLM with both source and cropped images"""
        
        payload = {
            "model": VLM_MODEL,
            "messages": [{
                "role": "user",
                "content": [
                    {"type": "text", "text": prompt},
                    {"type": "text", "text": "SOURCE IMAGE (full context):"},
                    {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{source_b64}"}},
                    {"type": "text", "text": "CROPPED IMAGE (area of concern):"},
                    {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{cropped_b64}"}}
                ]
            }],
            "temperature": 0.1,
            "max_tokens": 150
        }
        
        try:
            response = self.session.post(VLM_API_URL, json=payload, timeout=timeout)
            if response.status_code == 200:
                result = response.json()
                return result['choices'][0]['message']['content'].strip()
        except Exception as e:
            print(f"\nVLM Error: {e}")
        
        return None
    
    def parse_vlm_response(self, response):
        """Parse VLM response for learning"""
        if not response:
            return None
            
        result = {
            'is_fp': 'YES' in response.upper(),
            'confidence': 50,  # Default
            'entity_type': 'UNCLEAR',
            'ppe_status': 'N/A'
        }
        
        # Extract confidence
        import re
        conf_match = re.search(r'Confidence:\s*(\d+)', response, re.IGNORECASE)
        if conf_match:
            result['confidence'] = int(conf_match.group(1))
        
        # Extract entity type
        if 'PERSON' in response.upper():
            result['entity_type'] = 'PERSON'
        elif 'STRUCTURE' in response.upper():
            result['entity_type'] = 'STRUCTURE'
        
        # Extract PPE status
        if 'PPE: PRESENT' in response.upper() or 'FULL PPE' in response.upper():
            result['ppe_status'] = 'PRESENT'
        elif 'PPE: ABSENT' in response.upper() or 'NO PPE' in response.upper():
            result['ppe_status'] = 'ABSENT'
        
        return result
    
    def test_batch_with_learning(self, cases, iteration=1):
        """Test a batch of cases and learn from results"""
        results = []
        camera_performance = defaultdict(list)
        violation_performance = defaultdict(list)
        
        print(f"\n{'='*60}")
        print(f"ITERATION {iteration} - Testing {len(cases)} cases")
        print(f"{'='*60}")
        
        for i, case in enumerate(cases):
            case_num = case['case_number']
            violation_type = case['infringement_type']
            camera = case.get('camera_id', 'unknown')
            is_fp = case['is_false_positive']
            
            # Get current parameters
            params = self.violation_params[violation_type].copy()
            
            # Encode images
            source_b64 = self.encode_image(case['source_image'])
            cropped_b64 = self.encode_image(case['cropped_image'])
            
            if not source_b64 or not cropped_b64:
                continue
            
            # Create prompt with current parameters
            prompt = self.create_intelligent_prompt(violation_type, params)
            
            # Call VLM
            response = self.call_vlm_dual_image(source_b64, cropped_b64, prompt)
            
            if response:
                parsed = self.parse_vlm_response(response)
                if parsed:
                    correct = parsed['is_fp'] == is_fp
                    
                    result = {
                        'case_number': case_num,
                        'violation_type': violation_type,
                        'camera': camera,
                        'correct': correct,
                        'predicted_fp': parsed['is_fp'],
                        'actual_fp': is_fp,
                        'confidence': parsed['confidence'],
                        'entity_type': parsed['entity_type'],
                        'ppe_status': parsed['ppe_status']
                    }
                    
                    results.append(result)
                    camera_performance[camera].append(correct)
                    violation_performance[violation_type].append(correct)
                    
                    # Show progress
                    if (i + 1) % 10 == 0:
                        accuracy = sum(r['correct'] for r in results) / len(results) * 100
                        print(f"\rProgress: {i+1}/{len(cases)} | Accuracy: {accuracy:.1f}%", end='', flush=True)
        
        return results, camera_performance, violation_performance
    
    def learn_from_results(self, results, camera_perf, violation_perf):
        """Adjust parameters based on results"""
        print(f"\n\nLEARNING FROM RESULTS:")
        print("-"*40)
        
        # Analyze by violation type
        for violation_type, corrections in violation_perf.items():
            if len(corrections) >= 5:  # Need enough samples
                accuracy = sum(corrections) / len(corrections) * 100
                print(f"\n{violation_type}: {accuracy:.1f}% ({len(corrections)} cases)")
                
                # Analyze error patterns
                violation_results = [r for r in results if r['violation_type'] == violation_type]
                
                # Count error types
                structure_errors = sum(1 for r in violation_results 
                                     if not r['correct'] and r['entity_type'] == 'STRUCTURE')
                ppe_errors = sum(1 for r in violation_results 
                               if not r['correct'] and r['ppe_status'] == 'PRESENT')
                
                # Adjust thresholds based on errors
                if structure_errors > len(violation_results) * 0.2:
                    # Too many structures being seen as people
                    self.violation_params[violation_type]['structure_detection'] -= 5
                    print(f"  → Lowering structure threshold to {self.violation_params[violation_type]['structure_detection']}%")
                
                if ppe_errors > len(violation_results) * 0.3:
                    # Missing too much PPE
                    self.violation_params[violation_type]['ppe_detection'] -= 5
                    print(f"  → Lowering PPE threshold to {self.violation_params[violation_type]['ppe_detection']}%")
                
                # Adjust person detection based on overall accuracy
                if accuracy < 70:
                    self.violation_params[violation_type]['person_detection'] += 5
                    print(f"  → Raising person threshold to {self.violation_params[violation_type]['person_detection']}%")
        
        # Track overall performance
        overall_accuracy = sum(r['correct'] for r in results) / len(results) * 100
        fp_detection = sum(1 for r in results if r['actual_fp'] and r['predicted_fp']) / sum(1 for r in results if r['actual_fp']) * 100
        
        print(f"\n\nOVERALL PERFORMANCE:")
        print(f"Accuracy: {overall_accuracy:.1f}%")
        print(f"FP Detection Rate: {fp_detection:.1f}%")
        
        # Save if best performance
        if fp_detection > self.best_performance:
            self.best_performance = fp_detection
            self.best_params = {
                'violation_params': dict(self.violation_params),
                'camera_params': dict(self.camera_params)
            }
            print(f"✓ New best performance: {fp_detection:.1f}%")
        
        self.learning_history.append({
            'iteration': len(self.learning_history) + 1,
            'accuracy': overall_accuracy,
            'fp_detection': fp_detection,
            'params': dict(self.violation_params)
        })
    
    def run_self_learning_test(self, num_iterations=3, sample_size=100):
        """Run self-learning test iterations"""
        print(f"\nSTARTING SELF-LEARNING TEST")
        print(f"Iterations: {num_iterations}")
        print(f"Sample size per iteration: {sample_size}")
        
        # Load dataset
        with open('valo_batch_round3_complete.json', 'r') as f:
            data = json.load(f)
        
        all_cases = data['results']
        
        for iteration in range(1, num_iterations + 1):
            # Sample cases for this iteration
            import random
            sample_cases = random.sample(all_cases, min(sample_size, len(all_cases)))
            
            # Test with current parameters
            results, camera_perf, violation_perf = self.test_batch_with_learning(sample_cases, iteration)
            
            # Learn from results
            self.learn_from_results(results, camera_perf, violation_perf)
            
            # Save progress
            self.save_learning_progress()
        
        # Final report
        self.generate_final_report()
    
    def save_learning_progress(self):
        """Save learning progress"""
        progress = {
            'timestamp': datetime.now().isoformat(),
            'best_performance': self.best_performance,
            'best_params': self.best_params,
            'learning_history': self.learning_history
        }
        
        with open('intelligent_prompt_learning_progress.json', 'w') as f:
            json.dump(progress, f, indent=2)
    
    def generate_final_report(self):
        """Generate final optimized prompt and parameters"""
        print(f"\n\n{'='*60}")
        print("SELF-LEARNING COMPLETE")
        print(f"{'='*60}")
        
        print(f"\nBest FP Detection Rate: {self.best_performance:.1f}%")
        print(f"\nOptimized Parameters by Violation Type:")
        
        if self.best_params:
            for violation, params in self.best_params['violation_params'].items():
                print(f"\n{violation}:")
                for param, value in params.items():
                    print(f"  - {param}: {value}%")
        
        # Save optimized prompt
        self.save_optimized_prompt()
    
    def save_optimized_prompt(self):
        """Save the optimized prompt template"""
        template = """OPTIMIZED INTELLIGENT SAFETY ANALYSIS

Based on self-learning from {iterations} iterations:

VIOLATION-SPECIFIC PARAMETERS:
{params}

PROMPT TEMPLATE:
{prompt}

Performance: {performance:.1f}% FP Detection
"""
        
        with open('optimized_intelligent_prompt.txt', 'w') as f:
            f.write(template.format(
                iterations=len(self.learning_history),
                params=json.dumps(self.best_params, indent=2) if self.best_params else "Default",
                prompt=self.create_intelligent_prompt("General", self.confidence_thresholds),
                performance=self.best_performance
            ))

if __name__ == "__main__":
    tester = SelfLearningVLMTester()
    tester.run_self_learning_test(num_iterations=3, sample_size=50)