#!/bin/bash

# Test the original logic exactly as written

case_to_pk() {
    local case_number=$1
    local date_part=$(echo $case_number | cut -c6-11)
    local suffix=$(echo $case_number | cut -c9-)
    
    echo "DEBUG: case_number=$case_number"
    echo "DEBUG: date_part=$date_part (from cut -c6-11)"
    echo "DEBUG: suffix=$suffix (from cut -c9-)"
    
    case $date_part in
        "250623") 
            echo "MATCH: June 23 pattern"
            echo $((suffix + 142502)) 
            ;;
        "250630") 
            echo "MATCH: June 30 pattern"
            echo $((suffix + 143763)) 
            ;;
        *) 
            echo "NO MATCH: date_part '$date_part' doesn't match '250623' or '250630'"
            echo "UNKNOWN" 
            ;;
    esac
}

# Test cases
echo "Testing case_to_pk function:"
echo "============================"
echo ""

test_cases=(
    "V1250630118"
    "V1250623001"
)

for case_num in "${test_cases[@]}"; do
    echo "Testing: $case_num"
    result=$(case_to_pk "$case_num")
    echo "Result: $result"
    echo "-------------------"
done