#!/bin/bash

# AI-FARM Image Copy Script
# Copies and renames images from VALO system for AI-FARM processing
# Usage: ./copy_images.sh VALO_SQL_DATA_250630.csv

set -e

# Configuration
CSV_FILE="${1:-VALO_SQL_DATA_250630.csv}"
BASE_PATH="/video/data"
DEST_BASE="/tmp/ai_farm_images"
LOG_FILE="/tmp/ai_farm_copy.log"
ERROR_LOG="/tmp/ai_farm_errors.log"
DEBUG_LOG="/tmp/ai_farm_debug.log"

# Create destination directories
mkdir -p "$DEST_BASE/valid"
mkdir -p "$DEST_BASE/invalid"

# Initialize logs
echo "AI-FARM Image Copy Started: $(date)" > "$LOG_FILE"
echo "AI-FARM Copy Errors: $(date)" > "$ERROR_LOG"
echo "AI-FARM Debug Log: $(date)" > "$DEBUG_LOG"

# Counters
TOTAL_PROCESSED=0
SUCCESS_COUNT=0
ERROR_COUNT=0
MISSING_COUNT=0

echo "Starting image copy process..."
echo "CSV File: $CSV_FILE"
echo "Base Path: $BASE_PATH"
echo "Destination: $DEST_BASE"
echo "Log File: $LOG_FILE"
echo "Error Log: $ERROR_LOG"
echo "Debug Log: $DEBUG_LOG"
echo "----------------------------------------"

# Skip header line and process CSV
tail -n +2 "$CSV_FILE" | while IFS=',' read -r pk_event case_number url key; do
    TOTAL_PROCESSED=$((TOTAL_PROCESSED + 1))
    
    echo "DEBUG: Processing line $TOTAL_PROCESSED: pk_event=$pk_event, case_number=$case_number, url=$url, key=$key" >> "$DEBUG_LOG"
    
    # Extract event ID from URL path
    event_id=$(echo "$url" | grep -o '/events/[0-9]*/' | grep -o '[0-9]*')
    
    # Determine image type from URL
    if [[ "$url" == *"source-1.JPEG"* ]]; then
        image_type="source"
        filename="source-1.JPEG"
    elif [[ "$url" == *"cropped.JPEG"* ]]; then
        image_type="cropped"
        filename="cropped.JPEG"
    else
        echo "Unknown image type in URL: $url" >> "$ERROR_LOG"
        echo "DEBUG: Unknown image type in URL: $url" >> "$DEBUG_LOG"
        ERROR_COUNT=$((ERROR_COUNT + 1))
        continue
    fi
    
    # Build source path using correct base path
    source_path="${BASE_PATH}/VALO2SENFE1b/events/${event_id}/events_frames/${filename}"
    
    echo "DEBUG: Built source path: $source_path" >> "$DEBUG_LOG"
    
    # Build destination filename
    dest_filename="${case_number}_${image_type}_${key}.JPEG"
    dest_path="${DEST_BASE}/${key}/${dest_filename}"
    
    # Check if source file exists
    if [[ ! -f "$source_path" ]]; then
        echo "MISSING: $source_path -> $dest_filename" >> "$ERROR_LOG"
        echo "MISSING: $source_path" >> "$LOG_FILE"
        echo "DEBUG: File not found at: $source_path" >> "$DEBUG_LOG"
        echo "DEBUG: Directory listing for events_frames:" >> "$DEBUG_LOG"
        ls -la "${BASE_PATH}/VALO2SENFE1b/events/${event_id}/events_frames/" >> "$DEBUG_LOG" 2>&1 || echo "DEBUG: Directory does not exist: ${BASE_PATH}/VALO2SENFE1b/events/${event_id}/events_frames/" >> "$DEBUG_LOG"
        MISSING_COUNT=$((MISSING_COUNT + 1))
        continue
    fi
    
    # Copy file
    if cp "$source_path" "$dest_path"; then
        echo "SUCCESS: $source_path -> $dest_filename" >> "$LOG_FILE"
        SUCCESS_COUNT=$((SUCCESS_COUNT + 1))
        
        # Progress indicator every 100 files
        if [[ $((SUCCESS_COUNT % 100)) -eq 0 ]]; then
            echo "Processed: $SUCCESS_COUNT files..."
        fi
    else
        echo "COPY_ERROR: $source_path -> $dest_filename" >> "$ERROR_LOG"
        ERROR_COUNT=$((ERROR_COUNT + 1))
    fi
done

# Final summary
echo "----------------------------------------"
echo "Copy process completed: $(date)"
echo "Total processed: $TOTAL_PROCESSED"
echo "Successful copies: $SUCCESS_COUNT"
echo "Missing files: $MISSING_COUNT"
echo "Copy errors: $ERROR_COUNT"
echo "----------------------------------------"

# Write summary to log
echo "SUMMARY: Total=$TOTAL_PROCESSED, Success=$SUCCESS_COUNT, Missing=$MISSING_COUNT, Errors=$ERROR_COUNT" >> "$LOG_FILE"

# Check storage usage
echo "Storage usage in $DEST_BASE:"
du -sh "$DEST_BASE"
df -h "$DEST_BASE"

echo "Check logs for details:"
echo "Success log: $LOG_FILE"
echo "Error log: $ERROR_LOG"
echo "Debug log: $DEBUG_LOG"