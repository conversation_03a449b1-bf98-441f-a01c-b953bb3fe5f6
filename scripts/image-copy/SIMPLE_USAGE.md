# Simple Usage Guide - One Script Does Everything

## What This Script Does

✅ **Reads your CSV file**  
✅ **Converts CSV paths to your pattern**: `/video/data/VALO2SENFE1b/events/{pkevent_id}/events_frames`  
✅ **Validates if files exist**  
✅ **Copies files with proper naming**  
✅ **Shows clear progress and results**

## How to Use (3 steps)

### Step 1: Navigate to directory
```bash
cd /home/<USER>/VALO_AI-FARM_2025/VALO_AI-FARM_2025/scripts/image-copy
```

### Step 2: Run the script
```bash
./copy_images_simple.sh ../../VALO_SQL_DATA_250630.csv
```

### Step 3: Check results
```bash
ls -la /tmp/ai_farm_images/valid/
ls -la /tmp/ai_farm_images/invalid/
```

## What You'll See

**During processing:**
```
[1] Processing: V1250630118 (Event: 143881)
  Source: /video/data/VALO2SENFE1b/events/143881/events_frames/source-1.JPEG
  Destination: V1250630118_source_invalid.JPEG
  ✅ Successfully copied: V1250630118_source_invalid.JPEG

[2] Processing: V1250630118 (Event: 143881)
  Source: /video/data/VALO2SENFE1b/events/143881/events_frames/cropped.JPEG
  Destination: V1250630118_cropped_invalid.JPEG
  ❌ File not found: /video/data/VALO2SENFE1b/events/143881/events_frames/cropped.JPEG
  💡 Directory missing: /video/data/VALO2SENFE1b/events/143881/events_frames
```

**Final summary:**
```
Total processed: 100
✅ Successful copies: 45
❌ Missing files: 55
❌ Copy errors: 0

Results location: /tmp/ai_farm_images
Valid images: 20 files
Invalid images: 25 files
```

## File Naming

**Format:** `{case_number}_{image_type}_{status}.JPEG`

**Examples:**
- `V1250630118_source_invalid.JPEG`
- `V1250630118_cropped_invalid.JPEG`
- `V1250630119_source_valid.JPEG`

## Output Locations

- **Valid images:** `/tmp/ai_farm_images/valid/`
- **Invalid images:** `/tmp/ai_farm_images/invalid/`
- **Log file:** `/tmp/ai_farm_simple.log`

## If Files Are Missing

The script will tell you exactly what's missing:

```
❌ File not found: /video/data/VALO2SENFE1b/events/143881/events_frames/source-1.JPEG
💡 Directory missing: /video/data/VALO2SENFE1b/events/143881/events_frames
```

**Solution:** Your files are probably in a different location. Check:
```bash
find /home -name "events_frames" -type d 2>/dev/null
```

Then update the script's BASE_PATH line:
```bash
# Edit the script and change this line:
BASE_PATH="/video/data/VALO2SENFE1b/events"
# To your actual path, like:
BASE_PATH="/home/<USER>/db_files/VALO2SENFE1b/events"
```

## That's It!

No complex validation scripts, no separate path fixing - just run one command and get your images copied with proper names.