# AI-FARM Copy Images by Case Numbers - Usage Guide

## Version: 20250630_214855

### Purpose
Simplified image copy script that works with case numbers only (no CSV file required). Uses mathematical pattern to calculate pk_event automatically.

## Usage Options

### 1. Single Case Number
```bash
./copy_images_by_case_20250630_214855.sh V1250630118
```

### 2. Multiple Cases (Comma-Separated)
```bash
./copy_images_by_case_20250630_214855.sh "V1250630118,V1250630119,V1250630120"
```

### 3. Batch from File
```bash
# Create a file with case numbers (one per line)
echo "V1250630118" > case_list.txt
echo "V1250630119" >> case_list.txt
echo "V1250630120" >> case_list.txt

# Run script with file
./copy_images_by_case_20250630_214855.sh -f case_list.txt
```

## Supported Case Number Formats

### Valid Formats
- `V125062XXXX` - June 23, 2025 data
- `V125063XXXX` - June 30, 2025 data

### Examples
- ✅ `V1250630118` 
- ✅ `V1250623121`
- ❌ `V1250625XXX` (unsupported date)

## Output Structure

```
/tmp/ai_farm_images_by_case/
├── processed/              # Successfully processed cases
├── unknown_status/         # Images with unknown validation status
│   ├── V1250630118_source_unknown.JPEG
│   ├── V1250630118_cropped_unknown.JPEG
│   ├── V1250630119_source_unknown.JPEG
│   └── V1250630119_cropped_unknown.JPEG
└── logs/
```

## Key Features

### 🎯 No CSV Required
- Uses mathematical pattern to calculate pk_event
- Only needs case numbers as input

### 🔄 Flexible Input Methods
- Single case number
- Comma-separated batch
- File-based batch processing

### 📊 Automatic Calculation
```
Case Number → pk_event calculation:
V1250630118 → 118 + 143763 = 143881
```

### 🗂️ Smart Organization
- Creates organized directory structure
- Unknown status for manual classification later
- Comprehensive logging

## Log Files

- **Success Log**: `/tmp/ai_farm_copy_by_case.log`
- **Error Log**: `/tmp/ai_farm_errors_by_case.log`

## Post-Processing Steps

### 1. Review Copied Images
```bash
ls -la /tmp/ai_farm_images_by_case/unknown_status/
```

### 2. Manual Classification (if needed)
```bash
# Move valid violations
mkdir -p /tmp/ai_farm_images_by_case/valid
mv /tmp/ai_farm_images_by_case/unknown_status/*_valid_* /tmp/ai_farm_images_by_case/valid/

# Move invalid (false positives)  
mkdir -p /tmp/ai_farm_images_by_case/invalid
mv /tmp/ai_farm_images_by_case/unknown_status/*_invalid_* /tmp/ai_farm_images_by_case/invalid/
```

### 3. Archive for Transfer
```bash
cd /tmp
tar czf ai_farm_images_by_case.tar.gz ai_farm_images_by_case/
```

## Error Handling

### Common Issues
- **Invalid Format**: Case number doesn't match V125062XXXX or V125063XXXX
- **Missing Images**: Source or cropped image not found on server
- **Unknown Date**: Case number has unsupported date pattern

### Troubleshooting
```bash
# Check error log
tail -f /tmp/ai_farm_errors_by_case.log

# Verify case number format
echo "V1250630118" | grep -E "^V125062[0-9]{4}$|^V125063[0-9]{4}$"
```

## Comparison with CSV Version

| Feature | CSV Version | Case Number Version |
|---------|-------------|-------------------|
| Input Required | Full CSV file | Case numbers only |
| Validation Status | Automatic | Manual/Unknown |
| Flexibility | Fixed dataset | Any case numbers |
| Speed | Full batch | Selective processing |
| Use Case | Complete demo prep | Specific case analysis |

## Dependencies
- None (pure bash)
- Compatible with air-gapped environments
- Uses mathematical pattern calculation

## Integration with AI-FARM

This script is ideal for:
- **Selective Demos**: Process specific customer cases
- **Troubleshooting**: Analyze individual violations
- **Custom Analysis**: Focus on particular case patterns
- **Development**: Test with specific data subsets