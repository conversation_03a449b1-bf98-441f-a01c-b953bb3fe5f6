# Step-by-Step Guide: Fix DIR_NOT_FOUND Error

## Overview
Your image copy scripts are failing with DIR_NOT_FOUND because they're looking for files in the wrong location. This guide will help you:
1. Find where your actual image files are located
2. Fix the scripts to point to the correct location
3. Test and verify everything works

## Step 1: Navigate to the Scripts Directory

```bash
cd /home/<USER>/VALO_AI-FARM_2025/VALO_AI-FARM_2025/scripts/image-copy
```

## Step 2: Check Current Situation

Run the validation script to see what's missing:

```bash
./validate_paths.sh ../../VALO_SQL_DATA_250630.csv
```

**Expected output:** You'll see red ✗ marks showing that `/video/data` doesn't exist.

## Step 3: Find Your Actual Image Files

The CSV file contains paths like `/home/<USER>/db_files/VALO2SENFE1b/events/143881/events_frames/source-1.JPEG`

Let's check if files exist in various possible locations:

```bash
# Check if files exist in the CSV path
ls -la /home/<USER>/db_files/VALO2SENFE1b/events/143881/events_frames/

# Check for VALO directories in common locations
find /home -name "VALO*" -type d 2>/dev/null | head -10
find /data -name "VALO*" -type d 2>/dev/null | head -10
find /mnt -name "VALO*" -type d 2>/dev/null | head -10
find /var -name "VALO*" -type d 2>/dev/null | head -10

# Check for events_frames directories
find /home -name "events_frames" -type d 2>/dev/null | head -10
```

## Step 4: Identify the Correct Base Path

Based on Step 3 results, you'll find one of these scenarios:

### Scenario A: Files exist in `/home/<USER>/db_files/`
```bash
# Verify files exist
ls -la /home/<USER>/db_files/VALO2SENFE1b/events/143881/events_frames/
```

### Scenario B: Files exist in a different location
```bash
# Replace with your actual path found in Step 3
ls -la /your/actual/path/VALO2SENFE1b/events/143881/events_frames/
```

### Scenario C: Files don't exist anywhere
You'll need to obtain the image files from your VALO system first.

## Step 5: Update Scripts with Correct Path

Once you've found where your files are located, update the scripts:

### For Scenario A (files in `/home/<USER>/db_files/`):
```bash
./fix_base_path.sh /home/<USER>/db_files
```

### For Scenario B (files in custom location):
```bash
# Replace with your actual path
./fix_base_path.sh /your/actual/path
```

**What this does:**
- Creates backup copies of your scripts
- Updates the BASE_PATH variable in both scripts
- Confirms the changes were made

## Step 6: Verify the Fix

Run the validation script again:

```bash
./validate_paths.sh ../../VALO_SQL_DATA_250630.csv
```

**Expected output:** You should now see green ✓ marks showing directories exist.

## Step 7: Test with Small Dataset

Create a test file with just a few records:

```bash
# Create test CSV with header + 5 records
head -6 ../../VALO_SQL_DATA_250630.csv > test_data.csv

# Test the main script
./copy_images_20250630_213833.sh test_data.csv
```

## Step 8: Check Test Results

Look at the detailed logs:

```bash
# Check debug log for detailed path information
cat /tmp/ai_farm_debug.log

# Check error log for any issues
cat /tmp/ai_farm_errors.log

# Check success log
cat /tmp/ai_farm_copy.log
```

## Step 9: Verify Copied Files

Check if files were actually copied:

```bash
# Check destination directory
ls -la /tmp/ai_farm_images/

# Check subdirectories
ls -la /tmp/ai_farm_images/valid/
ls -la /tmp/ai_farm_images/invalid/
```

## Step 10: Run Full Processing (if test worked)

If the test in Step 7 worked, run with the full dataset:

```bash
./copy_images_20250630_213833.sh ../../VALO_SQL_DATA_250630.csv
```

## Alternative: Use Case-by-Case Script

If you prefer to process specific cases:

```bash
# Process single case
./copy_images_by_case_20250630_214855.sh V1250630118

# Process multiple cases
./copy_images_by_case_20250630_214855.sh "V1250630118,V1250630119,V1250630120"

# Process from file
echo "V1250630118" > my_cases.txt
echo "V1250630119" >> my_cases.txt
./copy_images_by_case_20250630_214855.sh -f my_cases.txt
```

## Troubleshooting

### If you still get DIR_NOT_FOUND:

1. **Check the debug log:**
   ```bash
   tail -50 /tmp/ai_farm_debug.log
   ```

2. **Manually verify a path:**
   ```bash
   # Replace with actual values from your CSV
   ls -la /your/base/path/VALO2SENFE1b/events/143881/events_frames/
   ```

3. **Check permissions:**
   ```bash
   # Make sure you can read the directory
   ls -la /your/base/path/VALO2SENFE1b/events/
   ```

### If files are being copied to wrong locations:

1. **Check destination directories:**
   ```bash
   ls -la /tmp/ai_farm_images/
   ```

2. **Verify CSV format:**
   ```bash
   head -5 ../../VALO_SQL_DATA_250630.csv
   ```

### If you get permission errors:

```bash
# Make sure destination is writable
chmod 755 /tmp/ai_farm_images/
```

## Key Files and Locations

- **Scripts:** `/home/<USER>/VALO_AI-FARM_2025/VALO_AI-FARM_2025/scripts/image-copy/`
- **CSV Data:** `/home/<USER>/VALO_AI-FARM_2025/VALO_AI-FARM_2025/VALO_SQL_DATA_250630.csv`
- **Debug Log:** `/tmp/ai_farm_debug.log`
- **Error Log:** `/tmp/ai_farm_errors.log`
- **Success Log:** `/tmp/ai_farm_copy.log`
- **Output Directory:** `/tmp/ai_farm_images/`

## Quick Reference Commands

```bash
# Navigate to scripts
cd /home/<USER>/VALO_AI-FARM_2025/VALO_AI-FARM_2025/scripts/image-copy

# Check current status
./validate_paths.sh ../../VALO_SQL_DATA_250630.csv

# Find your files
find /home -name "events_frames" -type d 2>/dev/null

# Update base path (replace with your actual path)
./fix_base_path.sh /home/<USER>/db_files

# Test with small dataset
head -6 ../../VALO_SQL_DATA_250630.csv > test_data.csv
./copy_images_20250630_213833.sh test_data.csv

# Check results
ls -la /tmp/ai_farm_images/valid/
ls -la /tmp/ai_farm_images/invalid/
```

## Success Criteria

You'll know everything is working when:
1. ✓ `validate_paths.sh` shows green checkmarks
2. ✓ Test run copies files without errors
3. ✓ You see files in `/tmp/ai_farm_images/valid/` and `/tmp/ai_farm_images/invalid/`
4. ✓ Debug log shows "SUCCESS: /path/to/source -> filename"

## Need Help?

If you encounter issues:
1. Run `./validate_paths.sh` to check paths
2. Check `/tmp/ai_farm_debug.log` for detailed information
3. Verify your BASE_PATH is correct
4. Test with a small dataset first

The enhanced scripts now provide detailed logging to help identify exactly where any issues occur.