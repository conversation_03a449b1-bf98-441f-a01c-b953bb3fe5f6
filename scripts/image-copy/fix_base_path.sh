#!/bin/bash

# Fix Base Path Script for AI-FARM Image Copy
# This script can update the BASE_PATH in the copy scripts if needed

set -e

# Configuration
SCRIPT1="copy_images_20250630_213833.sh"
SCRIPT2="copy_images_by_case_20250630_214855.sh"
CURRENT_BASE_PATH="/video/data"
NEW_BASE_PATH=""

show_usage() {
    echo "AI-FARM Base Path Fix Script"
    echo "Usage: $0 [new_base_path]"
    echo ""
    echo "Examples:"
    echo "  $0 /home/<USER>/db_files      # Update to CSV path"
    echo "  $0 /video/data                # Reset to default"
    echo ""
    echo "This script updates the BASE_PATH in both copy scripts."
    echo "Current base path: $CURRENT_BASE_PATH"
}

update_base_path() {
    local script_file="$1"
    local new_path="$2"
    
    if [[ ! -f "$script_file" ]]; then
        echo "Error: Script not found: $script_file"
        return 1
    fi
    
    echo "Updating $script_file..."
    
    # Create backup
    cp "$script_file" "${script_file}.backup.$(date +%Y%m%d_%H%M%S)"
    
    # Update BASE_PATH
    sed -i "s|BASE_PATH=\".*\"|BASE_PATH=\"$new_path\"|g" "$script_file"
    
    # Verify change
    if grep -q "BASE_PATH=\"$new_path\"" "$script_file"; then
        echo "  ✓ Successfully updated BASE_PATH to: $new_path"
    else
        echo "  ✗ Failed to update BASE_PATH"
        return 1
    fi
}

main() {
    if [[ $# -eq 0 ]]; then
        show_usage
        exit 1
    fi
    
    NEW_BASE_PATH="$1"
    
    echo "AI-FARM Base Path Fix Script"
    echo "=========================="
    echo "Current base path: $CURRENT_BASE_PATH"
    echo "New base path: $NEW_BASE_PATH"
    echo ""
    
    # Validate new path
    if [[ ! -d "$NEW_BASE_PATH" ]]; then
        echo "Warning: New base path does not exist: $NEW_BASE_PATH"
        echo "Continue anyway? (y/N)"
        read -r response
        if [[ "$response" != "y" && "$response" != "Y" ]]; then
            echo "Aborted"
            exit 1
        fi
    fi
    
    # Update both scripts
    echo "Updating scripts..."
    update_base_path "$SCRIPT1" "$NEW_BASE_PATH"
    update_base_path "$SCRIPT2" "$NEW_BASE_PATH"
    
    echo ""
    echo "=========================="
    echo "Update completed!"
    echo "=========================="
    echo ""
    echo "Backup files created:"
    ls -la *.backup.* 2>/dev/null || echo "No backup files found"
    echo ""
    echo "Next steps:"
    echo "1. Run ./validate_paths.sh to verify the new path"
    echo "2. Test the copy scripts with a small subset of data"
    echo "3. If issues occur, restore from backup files"
}

main "$@"