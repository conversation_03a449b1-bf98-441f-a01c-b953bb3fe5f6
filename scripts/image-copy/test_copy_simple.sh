#!/bin/bash

# AI-FARM Test Script - Process only first 3 lines to debug issues
# Usage: ./test_copy_simple.sh VALO_SQL_DATA_250630.csv

set -e

# Configuration
CSV_FILE="${1:-VALO_SQL_DATA_250630.csv}"
BASE_PATH="/video/data/VALO2SENFE1b/events"
TEST_LINES=3

echo "==========================================="
echo "AI-FARM Test Script (First $TEST_LINES lines only)"
echo "==========================================="
echo "CSV File: $CSV_FILE"
echo "Base Path: $BASE_PATH"
echo ""

# Check if CSV exists
if [[ ! -f "$CSV_FILE" ]]; then
    echo "ERROR: CSV file not found: $CSV_FILE"
    exit 1
fi

# Check for Windows line endings
echo "Checking CSV file format..."
if grep -q $'\r' "$CSV_FILE"; then
    echo "⚠️  Found Windows line endings (\\r\\n) in CSV file"
    echo "🔧 Converting to Unix line endings..."
    tr -d '\r' < "$CSV_FILE" > "${CSV_FILE}.clean"
    CSV_FILE="${CSV_FILE}.clean"
    echo "✅ CSV file cleaned: $CSV_FILE"
else
    echo "✅ CSV file has proper Unix line endings"
fi

echo ""
echo "Testing first $TEST_LINES lines..."
echo ""

# Process only first few lines (skip header)
tail -n +2 "$CSV_FILE" | head -$TEST_LINES | while IFS=',' read -r pk_event case_number url key; do
    # Strip carriage returns from all variables
    pk_event=$(echo "$pk_event" | tr -d '\r')
    case_number=$(echo "$case_number" | tr -d '\r')
    url=$(echo "$url" | tr -d '\r')
    key=$(echo "$key" | tr -d '\r')
    
    echo "Processing: $case_number (Event: $pk_event)"
    echo "  Raw CSV line: pk_event='$pk_event' case_number='$case_number' url='$url' key='$key'"
    
    # Determine image type from original URL
    if [[ "$url" == *"source-1.JPEG"* ]]; then
        image_type="source"
        filename="source-1.JPEG"
    elif [[ "$url" == *"cropped.JPEG"* ]]; then
        image_type="cropped"
        filename="cropped.JPEG"
    else
        echo "  ❌ Unknown image type in URL: $url"
        continue
    fi
    
    # Build source path using YOUR pattern
    source_path="${BASE_PATH}/${pk_event}/events_frames/${filename}"
    
    # Build destination filename
    dest_filename="${case_number}_${image_type}_${key}.JPEG"
    
    echo "  Image type: $image_type"
    echo "  Source path: $source_path"
    echo "  Destination: $dest_filename"
    
    # Check if source file exists
    if [[ -f "$source_path" ]]; then
        echo "  ✅ Source file exists"
    else
        echo "  ❌ Source file missing: $source_path"
        
        # Check if directory exists
        dir_path="${BASE_PATH}/${pk_event}/events_frames"
        if [[ -d "$dir_path" ]]; then
            echo "  💡 Directory exists: $dir_path"
            echo "  💡 Files in directory:"
            ls -la "$dir_path"
        else
            echo "  ❌ Directory missing: $dir_path"
            
            # Check if parent directory exists
            parent_dir="${BASE_PATH}/${pk_event}"
            if [[ -d "$parent_dir" ]]; then
                echo "  💡 Parent directory exists: $parent_dir"
                echo "  💡 Contents:"
                ls -la "$parent_dir"
            else
                echo "  ❌ Parent directory missing: $parent_dir"
            fi
        fi
    fi
    
    echo ""
done

# Cleanup
if [[ -f "${CSV_FILE}.clean" ]]; then
    rm -f "${CSV_FILE}.clean"
    echo "🧹 Cleaned up temporary files"
fi

echo "==========================================="
echo "Test completed - no files were copied"
echo "Use ./copy_images_simple.sh to actually copy files"
echo "==========================================="