# Script Explanation: Path Updates and File Naming

## How the Path Update Works

### 1. Current Script Configuration
Your script currently has this line:
```bash
BASE_PATH="/video/data"
```

### 2. What `fix_base_path.sh` Does
When you run:
```bash
./fix_base_path.sh /home/<USER>/db_files
```

It changes that line to:
```bash
BASE_PATH="/home/<USER>/db_files"
```

### 3. Before and After Path Construction

**BEFORE (causing DIR_NOT_FOUND):**
- CSV contains: `/home/<USER>/db_files/VALO2SENFE1b/events/143881/events_frames/source-1.JPEG`
- <PERSON><PERSON>t looks for: `/video/data/VALO2SENFE1b/events/143881/events_frames/source-1.JPEG`
- Result: ❌ File not found

**AFTER (fixed):**
- CSV contains: `/home/<USER>/db_files/VALO2SENFE1b/events/143881/events_frames/source-1.JPEG`
- <PERSON><PERSON><PERSON> looks for: `/home/<USER>/db_files/VALO2SENFE1b/events/143881/events_frames/source-1.JPEG`
- Result: ✅ File found (if it exists)

## Which Script to Use

### Main Script: `copy_images_20250630_213833.sh`
**Use this for:** Processing your entire CSV file

```bash
./copy_images_20250630_213833.sh VALO_SQL_DATA_250630.csv
```

**What it does:**
- Reads your CSV file line by line
- Processes ALL records in the CSV
- Copies images based on CSV data
- Organizes by validation status (`valid` vs `invalid`)

### Alternative Script: `copy_images_by_case_20250630_214855.sh`
**Use this for:** Processing specific case numbers

```bash
# Single case
./copy_images_by_case_20250630_214855.sh V1250630118

# Multiple cases
./copy_images_by_case_20250630_214855.sh "V1250630118,V1250630119,V1250630120"
```

**What it does:**
- Processes only the cases you specify
- Doesn't need the CSV file
- Calculates paths based on case numbers
- Puts files in `unknown_status` folder (since it doesn't know valid/invalid)

## How Images Are Named

### From Main Script (CSV-based):
**Format:** `{case_number}_{image_type}_{validation_status}.JPEG`

**Examples:**
- `V1250630118_source_invalid.JPEG`
- `V1250630118_cropped_invalid.JPEG`
- `V1250630119_source_valid.JPEG`
- `V1250630119_cropped_valid.JPEG`

**Where files go:**
- Valid images: `/tmp/ai_farm_images/valid/`
- Invalid images: `/tmp/ai_farm_images/invalid/`

### From Case-by-Case Script:
**Format:** `{case_number}_{image_type}_unknown.JPEG`

**Examples:**
- `V1250630118_source_unknown.JPEG`
- `V1250630118_cropped_unknown.JPEG`

**Where files go:**
- All images: `/tmp/ai_farm_images_by_case/unknown_status/`

## Step-by-Step: Update and Use Main Script

### Step 1: Update the Path
```bash
cd /home/<USER>/VALO_AI-FARM_2025/VALO_AI-FARM_2025/scripts/image-copy

# Update BASE_PATH to match your CSV paths
./fix_base_path.sh /home/<USER>/db_files
```

**Output:**
```
AI-FARM Base Path Fix Script
==========================
Current base path: /video/data
New base path: /home/<USER>/db_files

Updating scripts...
Updating copy_images_20250630_213833.sh...
  ✓ Successfully updated BASE_PATH to: /home/<USER>/db_files
Updating copy_images_by_case_20250630_214855.sh...
  ✓ Successfully updated BASE_PATH to: /home/<USER>/db_files
```

### Step 2: Test with Small Dataset
```bash
# Create test file with just 6 lines (header + 5 records)
head -6 ../../VALO_SQL_DATA_250630.csv > test_data.csv

# Run the main script
./copy_images_20250630_213833.sh test_data.csv
```

### Step 3: Check Results
```bash
# See copied files
ls -la /tmp/ai_farm_images/valid/
ls -la /tmp/ai_farm_images/invalid/

# Check debug log
cat /tmp/ai_farm_debug.log
```

### Step 4: Run Full Processing
```bash
# If test worked, run with full CSV
./copy_images_20250630_213833.sh ../../VALO_SQL_DATA_250630.csv
```

## Expected File Structure After Processing

```
/tmp/ai_farm_images/
├── valid/
│   ├── V1250630120_source_valid.JPEG
│   ├── V1250630120_cropped_valid.JPEG
│   └── ...
└── invalid/
    ├── V1250630118_source_invalid.JPEG
    ├── V1250630118_cropped_invalid.JPEG
    ├── V1250630119_source_invalid.JPEG
    ├── V1250630119_cropped_invalid.JPEG
    └── ...
```

## What Each Image Type Means

- **source**: Original full-size image from camera
- **cropped**: Cropped version focusing on violation area
- **valid**: Human confirmed this is a real violation
- **invalid**: Human confirmed this is a false positive

## Quick Commands Reference

```bash
# Navigate to scripts
cd /home/<USER>/VALO_AI-FARM_2025/VALO_AI-FARM_2025/scripts/image-copy

# Update path (replace with your actual path)
./fix_base_path.sh /home/<USER>/db_files

# Test with small dataset
head -6 ../../VALO_SQL_DATA_250630.csv > test_data.csv
./copy_images_20250630_213833.sh test_data.csv

# Check results
ls -la /tmp/ai_farm_images/valid/
ls -la /tmp/ai_farm_images/invalid/

# If test works, run full processing
./copy_images_20250630_213833.sh ../../VALO_SQL_DATA_250630.csv
```

## Troubleshooting

If you still get errors:
1. Check debug log: `cat /tmp/ai_farm_debug.log`
2. Verify paths exist: `ls -la /home/<USER>/db_files/VALO2SENFE1b/events/143881/events_frames/`
3. Check permissions: Make sure you can read the source files

The key is that `fix_base_path.sh` updates the `BASE_PATH` variable in your scripts to match where your files actually are located.