#!/bin/bash

# AI-FARM VALO Image Copy Script - Final Version
# Copies images from VALO structure: /video/data/VALO2SENFE1b/events/{pk_event}/events_frames/
# Usage: ./copy_valo_images_final.sh -c case_numbers.csv

# Configuration
BASE_PATH="/video/data/VALO2SENFE1b/events"
OUTPUT_DIR="./ai_farm_images"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Counters
TOTAL_CASES=0
FOUND_IMAGES=0
COPIED_IMAGES=0
MISSING_CASES=0

# Function to display usage
usage() {
    echo "AI-FARM VALO Image Copy Script"
    echo ""
    echo "Usage: $0 -c CSV_FILE [-o OUTPUT_DIR]"
    echo ""
    echo "Options:"
    echo "  -c CSV_FILE     CSV file containing case numbers (required)"
    echo "  -o OUTPUT_DIR   Output directory (default: ./ai_farm_images)"
    echo "  -h              Show this help message"
    echo ""
    echo "CSV Format:"
    echo "  V1250630118,V1250630119,V1250630120"
    echo ""
    echo "Images will be copied from:"
    echo "  ${BASE_PATH}/{pk_event}/events_frames/"
    exit 1
}

# Function to extract pk_event from case number
# Based on existing script logic - more complex conversion
extract_pk_event() {
    local case_number=$1
    
    # Validate format V125XXXXXXX
    if [[ ! "$case_number" =~ ^V125[0-9]{7}$ ]]; then
        echo ""
        return
    fi
    
    # Simple extraction - adjust if needed based on your data
    # Standard formula: pk_event = numeric_part - 630000
    local numeric_part=${case_number:4}
    local pk_event=$((10#$numeric_part - 630000))
    
    echo "$pk_event"
}

# Function to copy images for a case
copy_case_images() {
    local case_number=$1
    local output_base=$2
    
    ((TOTAL_CASES++))
    
    # Extract pk_event
    local pk_event=$(extract_pk_event "$case_number")
    
    if [ -z "$pk_event" ]; then
        echo -e "${RED}✗ Invalid case number: $case_number${NC}"
        echo "$case_number,INVALID_FORMAT" >> "$output_base/errors.csv"
        ((MISSING_CASES++))
        return 1
    fi
    
    # Build path to events_frames directory
    local events_dir="${BASE_PATH}/${pk_event}/events_frames"
    
    # Check if directory exists
    if [ ! -d "$events_dir" ]; then
        echo -e "${YELLOW}✗ Directory not found for case $case_number (pk_event: $pk_event)${NC}"
        echo "$case_number,$pk_event,DIR_NOT_FOUND" >> "$output_base/errors.csv"
        ((MISSING_CASES++))
        return 1
    fi
    
    # Look for specific image files (based on existing script pattern)
    local images_found=0
    local image_patterns=("source-1.JPEG" "cropped.JPEG" "*.JPEG" "*.jpg" "*.png")
    
    for pattern in "${image_patterns[@]}"; do
        for image_file in $events_dir/$pattern; do
            # Check if file exists (glob might not match)
            [ -f "$image_file" ] || continue
            
            # Get just the filename
            local filename=$(basename "$image_file")
            
            # Determine output filename
            local output_name="${case_number}_${filename}"
            
            # Copy the file
            if cp "$image_file" "$output_base/$output_name" 2>/dev/null; then
                echo -e "  ${GREEN}✓${NC} Copied: $filename → $output_name"
                ((COPIED_IMAGES++))
                ((images_found++))
            else
                echo -e "  ${RED}✗${NC} Failed to copy: $filename"
            fi
        done
    done
    
    if [ $images_found -gt 0 ]; then
        echo -e "${GREEN}✓ Case $case_number: Found $images_found images (pk_event: $pk_event)${NC}"
        ((FOUND_IMAGES++))
        echo "$case_number,$pk_event,$images_found,SUCCESS" >> "$output_base/summary.csv"
    else
        echo -e "${YELLOW}⚠ Case $case_number: No images found in $events_dir${NC}"
        echo "$case_number,$pk_event,0,NO_IMAGES" >> "$output_base/summary.csv"
        ((MISSING_CASES++))
    fi
}

# Parse command line arguments
CSV_FILE=""
while getopts "c:o:h" opt; do
    case $opt in
        c) CSV_FILE="$OPTARG" ;;
        o) OUTPUT_DIR="$OPTARG" ;;
        h) usage ;;
        *) usage ;;
    esac
done

# Validate required arguments
if [ -z "$CSV_FILE" ]; then
    echo -e "${RED}Error: CSV file is required${NC}"
    usage
fi

# Check if CSV file exists
if [ ! -f "$CSV_FILE" ]; then
    echo -e "${RED}Error: CSV file not found: $CSV_FILE${NC}"
    exit 1
fi

# Check if base path exists
if [ ! -d "$BASE_PATH" ]; then
    echo -e "${RED}Error: VALO base path not found: $BASE_PATH${NC}"
    echo "Please ensure you're running this on the VALO server"
    exit 1
fi

# Create output directory with timestamp
FINAL_OUTPUT_DIR="${OUTPUT_DIR}/extracted_${TIMESTAMP}"
mkdir -p "$FINAL_OUTPUT_DIR"

# Initialize CSV files
echo "case_number,pk_event,images_count,status" > "$FINAL_OUTPUT_DIR/summary.csv"
echo "case_number,pk_event,error_type" > "$FINAL_OUTPUT_DIR/errors.csv"

echo "=========================================="
echo -e "${BLUE}AI-FARM VALO Image Copy Script${NC}"
echo "=========================================="
echo "CSV File: $CSV_FILE"
echo "VALO Path: $BASE_PATH"
echo "Output: $FINAL_OUTPUT_DIR"
echo "=========================================="
echo ""

# Process CSV file
echo "Processing case numbers..."
echo ""

# Read CSV and process each case number
while IFS= read -r line; do
    # Skip empty lines
    [ -z "$line" ] && continue
    
    # Skip header lines
    if [[ "$line" =~ ^[[:space:]]*case|^[[:space:]]*Case|^[[:space:]]*CASE ]]; then
        continue
    fi
    
    # Split by comma and process each case
    IFS=',' read -ra CASES <<< "$line"
    for case in "${CASES[@]}"; do
        # Trim whitespace
        case=$(echo "$case" | tr -d '[:space:]')
        
        # Skip empty values
        [ -z "$case" ] && continue
        
        # Process this case
        copy_case_images "$case" "$FINAL_OUTPUT_DIR"
    done
done < "$CSV_FILE"

# Generate detailed report
{
    echo "AI-FARM VALO Image Copy Report"
    echo "=============================="
    echo "Generated: $(date)"
    echo "Source: $BASE_PATH"
    echo ""
    echo "Summary Statistics:"
    echo "-------------------"
    echo "Total cases processed: $TOTAL_CASES"
    echo "Cases with images: $FOUND_IMAGES"
    echo "Total images copied: $COPIED_IMAGES"
    echo "Cases without images: $MISSING_CASES"
    echo ""
    echo "File Locations:"
    echo "---------------"
    echo "Images: $FINAL_OUTPUT_DIR/"
    echo "Summary: $FINAL_OUTPUT_DIR/summary.csv"
    echo "Errors: $FINAL_OUTPUT_DIR/errors.csv"
} > "$FINAL_OUTPUT_DIR/report.txt"

# Print summary
echo ""
echo "=========================================="
echo -e "${BLUE}COPY COMPLETE${NC}"
echo "=========================================="
echo "Total cases processed: $TOTAL_CASES"
echo -e "Cases with images: ${GREEN}$FOUND_IMAGES${NC}"
echo -e "Total images copied: ${GREEN}$COPIED_IMAGES${NC}"
echo -e "Cases without images: ${YELLOW}$MISSING_CASES${NC}"
echo ""
echo "Output directory: $FINAL_OUTPUT_DIR"
echo "Detailed report: $FINAL_OUTPUT_DIR/report.txt"
echo "=========================================="

# Exit with appropriate code
if [ $COPIED_IMAGES -eq 0 ]; then
    exit 1
else
    exit 0
fi