#!/bin/bash

# AI-FARM Simple Image Copy Script
# One script that does everything: validate, fix paths, and copy images
# Usage: ./copy_images_simple.sh VALO_SQL_DATA_250630.csv

set -e

# Configuration
CSV_FILE="${1:-VALO_SQL_DATA_250630.csv}"
BASE_PATH="/video/data/VALO2SENFE1b/events"
DEST_BASE="/tmp/ai_farm_images"
LOG_FILE="/tmp/ai_farm_simple.log"

# Create destination directories
mkdir -p "$DEST_BASE/valid"
mkdir -p "$DEST_BASE/invalid"

# Initialize log
echo "AI-FARM Simple Copy Started: $(date)" > "$LOG_FILE"

# Counters
TOTAL_PROCESSED=0
SUCCESS_COUNT=0
ERROR_COUNT=0
MISSING_COUNT=0

echo "==========================================="
echo "AI-FARM Simple Image Copy Script"
echo "==========================================="
echo "CSV File: $CSV_FILE"
echo "Base Path: $BASE_PATH"
echo "Destination: $DEST_BASE"
echo "Log: $LOG_FILE"
echo ""

# Check if CSV exists
if [[ ! -f "$CSV_FILE" ]]; then
    echo "ERROR: CSV file not found: $CSV_FILE"
    exit 1
fi

echo "Checking CSV file format..."
# Check for Windows line endings and fix them
if grep -q $'\r' "$CSV_FILE"; then
    echo "⚠️  Found Windows line endings (\\r\\n) in CSV file"
    echo "🔧 Converting to Unix line endings..."
    # Create a cleaned version
    tr -d '\r' < "$CSV_FILE" > "${CSV_FILE}.clean"
    CSV_FILE="${CSV_FILE}.clean"
    echo "✅ CSV file cleaned: $CSV_FILE"
else
    echo "✅ CSV file has proper Unix line endings"
fi

echo ""
echo "Processing CSV file..."
echo ""

# Process each line in CSV (skip header)
tail -n +2 "$CSV_FILE" | while IFS=',' read -r pk_event case_number url key; do
    # Strip carriage returns from all variables (Windows line endings fix)
    pk_event=$(echo "$pk_event" | tr -d '\r')
    case_number=$(echo "$case_number" | tr -d '\r')
    url=$(echo "$url" | tr -d '\r')
    key=$(echo "$key" | tr -d '\r')
    
    TOTAL_PROCESSED=$((TOTAL_PROCESSED + 1))
    
    echo "[$TOTAL_PROCESSED] Processing: $case_number (Event: $pk_event)"
    echo "  Debug: pk_event='$pk_event' case_number='$case_number' key='$key'"
    
    # Determine image type from original URL
    if [[ "$url" == *"source-1.JPEG"* ]]; then
        image_type="source"
        filename="source-1.JPEG"
    elif [[ "$url" == *"cropped.JPEG"* ]]; then
        image_type="cropped"
        filename="cropped.JPEG"
    else
        echo "  ❌ Unknown image type in URL: $url"
        echo "ERROR: Unknown image type - $url" >> "$LOG_FILE"
        ERROR_COUNT=$((ERROR_COUNT + 1))
        continue
    fi
    
    # Build source path using YOUR pattern: /video/data/VALO2SENFE1b/events/{pkevent_id}/events_frames
    source_path="${BASE_PATH}/${pk_event}/events_frames/${filename}"
    
    # Build destination filename
    dest_filename="${case_number}_${image_type}_${key}.JPEG"
    dest_path="${DEST_BASE}/${key}/${dest_filename}"
    
    echo "  Source: $source_path"
    echo "  Destination: $dest_filename"
    
    # Check if source file exists
    if [[ ! -f "$source_path" ]]; then
        echo "  ❌ File not found: $source_path"
        echo "MISSING: $source_path" >> "$LOG_FILE"
        
        # Check if directory exists
        dir_path="${BASE_PATH}/${pk_event}/events_frames"
        if [[ ! -d "$dir_path" ]]; then
            echo "  💡 Directory missing: $dir_path"
            echo "MISSING_DIR: $dir_path" >> "$LOG_FILE"
        else
            echo "  💡 Directory exists, but file missing"
            echo "  Available files in directory:"
            ls -la "$dir_path" | head -3
        fi
        
        MISSING_COUNT=$((MISSING_COUNT + 1))
        echo ""
        continue
    fi
    
    # Copy file
    if cp "$source_path" "$dest_path"; then
        echo "  ✅ Successfully copied: $dest_filename"
        echo "SUCCESS: $source_path -> $dest_filename" >> "$LOG_FILE"
        SUCCESS_COUNT=$((SUCCESS_COUNT + 1))
    else
        echo "  ❌ Copy failed: $dest_filename"
        echo "COPY_ERROR: $source_path -> $dest_filename" >> "$LOG_FILE"
        ERROR_COUNT=$((ERROR_COUNT + 1))
    fi
    
    echo ""
done

# Final summary
echo "==========================================="
echo "PROCESSING COMPLETE"
echo "==========================================="
echo "Total processed: $TOTAL_PROCESSED"
echo "✅ Successful copies: $SUCCESS_COUNT"
echo "❌ Missing files: $MISSING_COUNT"
echo "❌ Copy errors: $ERROR_COUNT"
echo ""

# Show results
echo "Results location: $DEST_BASE"
echo "Valid images: $(ls -1 "$DEST_BASE/valid/" 2>/dev/null | wc -l) files"
echo "Invalid images: $(ls -1 "$DEST_BASE/invalid/" 2>/dev/null | wc -l) files"
echo ""

# Storage usage
echo "Storage usage:"
du -sh "$DEST_BASE" 2>/dev/null || echo "No files copied"

echo ""
echo "Log file: $LOG_FILE"
echo "==========================================="

# Show some sample results
echo ""
echo "Sample copied files:"
ls -la "$DEST_BASE/valid/" | head -3 2>/dev/null || echo "No valid files"
ls -la "$DEST_BASE/invalid/" | head -3 2>/dev/null || echo "No invalid files"

# Cleanup temporary files
if [[ -f "${CSV_FILE}.clean" ]]; then
    rm -f "${CSV_FILE}.clean"
    echo ""
    echo "🧹 Cleaned up temporary files"
fi