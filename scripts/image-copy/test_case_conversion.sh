#!/bin/bash

# Test script to verify case number to pk_event conversion

echo "Testing Case Number to pk_event Conversion"
echo "=========================================="
echo ""
echo "Algorithm:"
echo "  June 23, 2025 (V125062XXXX): pk_event = suffix + 142502"
echo "  June 30, 2025 (V125063XXXX): pk_event = suffix + 143763"
echo ""
echo "Examples:"
echo ""

# Test cases
test_cases=(
    "V1250630118"
    "V1250630001"
    "V1250630100"
    "V1250623001"
    "V1250623100"
)

for case_num in "${test_cases[@]}"; do
    # Extract date part - need "250623" or "250630"
    # V1250630118 - positions 3-8 give us "250630"
    date_part=${case_num:3:6}
    
    # Extract suffix (characters 9+, 1-indexed = 8+ in 0-indexed)
    suffix=${case_num:8}
    
    echo "DEBUG: case=$case_num, date_part='$date_part', suffix='$suffix'"
    # Remove leading zeros for arithmetic
    suffix=$((10#$suffix))
    
    # Calculate pk_event
    case $date_part in
        "250623")
            pk_event=$((suffix + 142502))
            echo "$case_num → pk_event: $pk_event (June 23: $suffix + 142502)"
            echo "  Path: /video/data/VALO2SENFE1b/events/$pk_event/events_frames/"
            ;;
        "250630")
            pk_event=$((suffix + 143763))
            echo "$case_num → pk_event: $pk_event (June 30: $suffix + 143763)"
            echo "  Path: /video/data/VALO2SENFE1b/events/$pk_event/events_frames/"
            ;;
        *)
            echo "$case_num → UNKNOWN DATE PATTERN"
            ;;
    esac
    echo ""
done