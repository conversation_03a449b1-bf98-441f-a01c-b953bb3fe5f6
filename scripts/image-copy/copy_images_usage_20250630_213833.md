# AI-FARM Image Copy Script Usage

## Overview
Script to copy and rename VALO system images for AI-FARM processing.

## Storage Requirements
- **Total images**: ~2,499 images (from CSV)
- **Estimated storage**: ~875MB - 1.5GB
- **Destination**: `/tmp/ai_farm_images/`

## Usage

### 1. Upload Files to Server
```bash
# Copy CSV file to server
scp VALO_SQL_DATA_250630.csv sensen@server:/tmp/

# Copy script to server  
scp copy_images.sh sensen@server:/tmp/
```

### 2. Execute on Server
```bash
cd /tmp
chmod +x copy_images.sh
./copy_images.sh VALO_SQL_DATA_250630.csv
```

### 3. Monitor Progress
```bash
# Watch progress
tail -f /tmp/ai_farm_copy.log

# Check errors
tail -f /tmp/ai_farm_errors.log

# Check storage usage
du -sh /tmp/ai_farm_images/
```

## Output Structure
```
/tmp/ai_farm_images/
├── valid/
│   ├── V1250630XXX_source_valid.JPEG
│   └── V1250630XXX_cropped_valid.JPEG
├── invalid/
│   ├── V1250630118_source_invalid.JPEG
│   └── V1250630118_cropped_invalid.JPEG
└── logs/
```

## File Naming Convention
Format: `{case_number}_{image_type}_{validation_status}.JPEG`

Examples:
- `V1250630118_source_invalid.JPEG`
- `V1250630118_cropped_invalid.JPEG`

## Log Files
- **Success Log**: `/tmp/ai_farm_copy.log`
- **Error Log**: `/tmp/ai_farm_errors.log`

## Error Handling
Script will:
- Skip missing files and log them
- Continue processing on copy errors
- Provide final summary with counts
- Show storage usage

## After Completion
```bash
# Create archive for transfer
cd /tmp
tar czf ai_farm_images.tar.gz ai_farm_images/

# Check final size
ls -lh ai_farm_images.tar.gz
```

## Dependencies
- None (uses only bash built-ins)
- Compatible with air-gapped environments