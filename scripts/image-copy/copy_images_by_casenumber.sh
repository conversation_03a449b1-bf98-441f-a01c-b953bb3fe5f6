#!/bin/bash

# Copy VALO images using case numbers from CSV file
# Usage: ./copy_images_by_casenumber.sh -c case_numbers.csv -s /valo/images -o ./extracted_images

# Default values
OUTPUT_DIR="./extracted_images"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Counters
TOTAL_CASES=0
FOUND_IMAGES=0
COPIED_IMAGES=0
MISSING_IMAGES=0

# Function to display usage
usage() {
    echo "Usage: $0 -c CSV_FILE -s SOURCE_DIR [-o OUTPUT_DIR]"
    echo ""
    echo "Options:"
    echo "  -c CSV_FILE     CSV file containing case numbers (required)"
    echo "  -s SOURCE_DIR   Source directory where VALO images are stored (required)"
    echo "  -o OUTPUT_DIR   Output directory for copied images (default: ./extracted_images)"
    echo "  -h              Show this help message"
    echo ""
    echo "CSV Format Examples:"
    echo "  V1250630118,V1250630119,V1250630120"
    echo "  V1250630121,V1250630122"
    exit 1
}

# Function to extract pk_event from case number
extract_pk_event() {
    local case_number=$1
    
    # Check if case number starts with V125
    if [[ ! "$case_number" =~ ^V125[0-9]{7}$ ]]; then
        echo ""
        return
    fi
    
    # Extract numeric part and calculate pk_event
    local numeric_part=${case_number:4}
    local pk_event=$((10#$numeric_part - 630000))
    echo "$pk_event"
}

# Function to find image file
find_image() {
    local case_number=$1
    local source_dir=$2
    local pk_event=$(extract_pk_event "$case_number")
    
    # If pk_event extraction failed, only search by case number
    if [ -z "$pk_event" ]; then
        echo -e "${YELLOW}Warning: Invalid case number format: $case_number${NC}" >&2
        pk_event=""
    fi
    
    # Common image extensions
    local extensions=("jpg" "jpeg" "png" "bmp" "tiff" "tif")
    
    # Search patterns
    local patterns=(
        "${case_number}"
        "*${case_number}*"
        "violation_${pk_event}"
        "alert_${pk_event}"
        "case_${case_number}"
        "${pk_event}"
        "*${pk_event}*"
    )
    
    # Search for the image
    for pattern in "${patterns[@]}"; do
        # Skip empty patterns
        [ -z "$pattern" ] && continue
        
        for ext in "${extensions[@]}"; do
            # Use find command to search
            local found_files=$(find "$source_dir" -type f -name "${pattern}.${ext}" 2>/dev/null | head -1)
            if [ -n "$found_files" ]; then
                echo "$found_files"
                return 0
            fi
        done
    done
    
    return 1
}

# Function to copy single image
copy_image() {
    local case_number=$1
    local source_dir=$2
    local output_dir=$3
    
    ((TOTAL_CASES++))
    
    # Find the image
    local image_path=$(find_image "$case_number" "$source_dir")
    
    if [ -z "$image_path" ] || [ ! -f "$image_path" ]; then
        echo -e "${RED}✗ Image not found for case: $case_number${NC}"
        echo "$case_number" >> "$output_dir/missing_images.txt"
        ((MISSING_IMAGES++))
        return 1
    fi
    
    ((FOUND_IMAGES++))
    
    # Get file extension
    local extension="${image_path##*.}"
    local output_file="$output_dir/${case_number}.${extension}"
    
    # Copy the file
    if cp "$image_path" "$output_file" 2>/dev/null; then
        echo -e "${GREEN}✓ Copied: $case_number -> $output_file${NC}"
        ((COPIED_IMAGES++))
        return 0
    else
        echo -e "${RED}✗ Failed to copy: $case_number${NC}"
        return 1
    fi
}

# Parse command line arguments
while getopts "c:s:o:h" opt; do
    case $opt in
        c) CSV_FILE="$OPTARG" ;;
        s) SOURCE_DIR="$OPTARG" ;;
        o) OUTPUT_DIR="$OPTARG" ;;
        h) usage ;;
        *) usage ;;
    esac
done

# Validate required arguments
if [ -z "$CSV_FILE" ] || [ -z "$SOURCE_DIR" ]; then
    echo -e "${RED}Error: CSV file and source directory are required${NC}"
    usage
fi

# Check if CSV file exists
if [ ! -f "$CSV_FILE" ]; then
    echo -e "${RED}Error: CSV file not found: $CSV_FILE${NC}"
    exit 1
fi

# Check if source directory exists
if [ ! -d "$SOURCE_DIR" ]; then
    echo -e "${RED}Error: Source directory not found: $SOURCE_DIR${NC}"
    exit 1
fi

# Create output directory with timestamp
FINAL_OUTPUT_DIR="${OUTPUT_DIR}/extracted_${TIMESTAMP}"
mkdir -p "$FINAL_OUTPUT_DIR"

echo "=========================================="
echo "VALO Image Copy Script"
echo "=========================================="
echo "CSV File: $CSV_FILE"
echo "Source Directory: $SOURCE_DIR"
echo "Output Directory: $FINAL_OUTPUT_DIR"
echo "=========================================="
echo ""

# Process CSV file
echo "Processing case numbers..."

# Read CSV file and process each case number
while IFS= read -r line; do
    # Skip empty lines
    [ -z "$line" ] && continue
    
    # Skip header lines that might contain "case"
    if [[ "$line" =~ ^[[:space:]]*case|^[[:space:]]*Case|^[[:space:]]*CASE ]]; then
        continue
    fi
    
    # Split by comma and process each case number
    IFS=',' read -ra CASES <<< "$line"
    for case in "${CASES[@]}"; do
        # Trim whitespace
        case=$(echo "$case" | tr -d '[:space:]')
        
        # Skip empty values
        [ -z "$case" ] && continue
        
        # Copy the image
        copy_image "$case" "$SOURCE_DIR" "$FINAL_OUTPUT_DIR"
    done
done < "$CSV_FILE"

# Print summary
echo ""
echo "=========================================="
echo "COPY SUMMARY"
echo "=========================================="
echo "Total case numbers processed: $TOTAL_CASES"
echo "Images found: $FOUND_IMAGES"
echo "Images copied successfully: $COPIED_IMAGES"
echo "Missing images: $MISSING_IMAGES"
echo ""
echo "Output directory: $FINAL_OUTPUT_DIR"

if [ -f "$FINAL_OUTPUT_DIR/missing_images.txt" ]; then
    echo "Missing images list: $FINAL_OUTPUT_DIR/missing_images.txt"
fi

echo "=========================================="

# Exit with appropriate code
if [ $COPIED_IMAGES -eq 0 ]; then
    exit 1
else
    exit 0
fi