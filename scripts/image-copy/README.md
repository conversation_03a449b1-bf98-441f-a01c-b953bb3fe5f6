# VALO Image Copy Scripts

Scripts for extracting and renaming VALO system images for AI-FARM processing.

## Available Scripts

### 1. Full CSV Processing
**File**: `copy_images_20250630_213833.sh`
**Usage**: `./copy_images_20250630_213833.sh VALO_SQL_DATA_250630.csv`

Processes entire CSV dataset:
- Processes all 2,499 cases from CSV
- Creates organized valid/invalid directories
- Estimated storage: ~875MB - 1.5GB
- Comprehensive logging and error handling

### 2. Selective Case Processing  
**File**: `copy_images_by_case_20250630_214855.sh`
**Usage**: Various input methods supported

Flexible case-number based processing:
```bash
# Single case
./copy_images_by_case_20250630_214855.sh V1250630118

# Multiple cases (comma-separated)
./copy_images_by_case_20250630_214855.sh "V1250630118,V1250630119,V1250630120"

# From file (one case per line)
./copy_images_by_case_20250630_214855.sh -f case_numbers.txt
```

## Mathematical Pattern Integration

Both scripts use the discovered mathematical relationship:
- **June 23, 2025**: `pk_event = suffix + 142502`
- **June 30, 2025**: `pk_event = suffix + 143763`

Example: `V1250630118` → `118 + 143763 = 143881`

## Output Structure

### Full CSV Version
```
/tmp/ai_farm_images/
├── valid/
│   ├── V1250630XXX_source_valid.JPEG
│   └── V1250630XXX_cropped_valid.JPEG
└── invalid/
    ├── V1250630118_source_invalid.JPEG
    └── V1250630118_cropped_invalid.JPEG
```

### Case-Based Version
```
/tmp/ai_farm_images_by_case/
├── unknown_status/
│   ├── V1250630118_source_unknown.JPEG
│   ├── V1250630118_cropped_unknown.JPEG
└── processed/
```

## Prerequisites

- **Environment**: RedHat/Ubuntu server with bash
- **Paths**: Access to `/video/data/VALO2SENFE1b/events/`
- **Storage**: Check available space in `/tmp/`
- **Dependencies**: None (pure bash scripts)

## Usage Examples

### Deployment to Server
```bash
# 1. Copy scripts to server
scp copy_images_*.sh server:/tmp/
scp *.csv server:/tmp/

# 2. Execute on server
ssh server
cd /tmp
chmod +x copy_images_*.sh

# 3. Run processing
./copy_images_20250630_213833.sh VALO_SQL_DATA_250630.csv
```

### Post-Processing
```bash
# Create archive for transfer
cd /tmp
tar czf ai_farm_images.tar.gz ai_farm_images*/

# Check results
du -sh ai_farm_images*
```

## Error Handling

Both scripts provide:
- **Comprehensive logging**: Success and error logs
- **Missing file handling**: Skip and log missing images
- **Progress tracking**: Status updates during processing
- **Storage monitoring**: Check available space
- **Graceful failures**: Continue processing on individual errors

## Log Files

- **Success logs**: `/tmp/ai_farm_copy*.log`
- **Error logs**: `/tmp/ai_farm_errors*.log`
- **Processing summary**: Final statistics and recommendations

## Supported Case Formats

- ✅ `V125062XXXX` (June 23, 2025 data)
- ✅ `V125063XXXX` (June 30, 2025 data)  
- ❌ Other date formats (will be logged as errors)

## Version History

See [../../docs/SCRIPT_VERSION_LOG.md](../../docs/SCRIPT_VERSION_LOG.md) for complete version tracking and development history.

## Business Context

These scripts prepare customer's actual safety violation data for AI-FARM demonstrations:
1. **Extract** - Copy customer images with proper naming
2. **Process** - VLM analyzes images for false positives
3. **Demo** - Live results shown to customer  
4. **ROI** - Calculate savings based on actual data

The extracted images enable the AI-FARM system to demonstrate 70% false positive reduction using the customer's own violation data.