#!/bin/bash

# Path Validation Script for AI-FARM Image Copy
# This script helps diagnose directory structure issues

set -e

# Configuration
CSV_FILE="${1:-../../VALO_SQL_DATA_250630.csv}"
BASE_PATH="/video/data"
EXPECTED_STRUCTURE="VALO2SENFE1b/events"

echo "AI-FARM Path Validation Script"
echo "=============================="
echo "CSV File: $CSV_FILE"
echo "Base Path: $BASE_PATH"
echo "Expected Structure: $BASE_PATH/$EXPECTED_STRUCTURE"
echo ""

# Check if base path exists
echo "1. Checking base path..."
if [[ -d "$BASE_PATH" ]]; then
    echo "   ✓ Base path exists: $BASE_PATH"
    echo "   Contents:"
    ls -la "$BASE_PATH" | head -10
else
    echo "   ✗ Base path does not exist: $BASE_PATH"
    echo "   Available paths under /video/:"
    ls -la /video/ 2>/dev/null || echo "   /video/ directory does not exist"
fi

echo ""

# Check if VALO2SENFE1b directory exists
echo "2. Checking VALO2SENFE1b directory..."
VALO_PATH="$BASE_PATH/VALO2SENFE1b"
if [[ -d "$VALO_PATH" ]]; then
    echo "   ✓ VALO2SENFE1b directory exists: $VALO_PATH"
    echo "   Contents:"
    ls -la "$VALO_PATH" | head -10
else
    echo "   ✗ VALO2SENFE1b directory does not exist: $VALO_PATH"
    echo "   Available directories under $BASE_PATH:"
    ls -la "$BASE_PATH/" 2>/dev/null || echo "   Cannot list $BASE_PATH"
fi

echo ""

# Check if events directory exists
echo "3. Checking events directory..."
EVENTS_PATH="$BASE_PATH/VALO2SENFE1b/events"
if [[ -d "$EVENTS_PATH" ]]; then
    echo "   ✓ Events directory exists: $EVENTS_PATH"
    echo "   Sample event directories:"
    ls -la "$EVENTS_PATH" | head -10
else
    echo "   ✗ Events directory does not exist: $EVENTS_PATH"
fi

echo ""

# Check specific event directories from CSV
echo "4. Checking specific event directories from CSV..."
if [[ -f "$CSV_FILE" ]]; then
    echo "   Reading first 5 events from CSV..."
    
    # Skip header and get first 5 unique event IDs
    tail -n +2 "$CSV_FILE" | head -5 | while IFS=',' read -r pk_event case_number url key; do
        echo "   Event: $pk_event ($case_number)"
        event_dir="$EVENTS_PATH/$pk_event"
        frames_dir="$event_dir/events_frames"
        
        if [[ -d "$event_dir" ]]; then
            echo "     ✓ Event directory exists: $event_dir"
            if [[ -d "$frames_dir" ]]; then
                echo "     ✓ Frames directory exists: $frames_dir"
                echo "     Files in frames directory:"
                ls -la "$frames_dir" | head -5
            else
                echo "     ✗ Frames directory missing: $frames_dir"
                echo "     Contents of event directory:"
                ls -la "$event_dir" 2>/dev/null || echo "     Cannot list directory"
            fi
        else
            echo "     ✗ Event directory missing: $event_dir"
        fi
        echo ""
    done
else
    echo "   ✗ CSV file not found: $CSV_FILE"
fi

echo ""

# Show alternative paths from CSV
echo "5. Alternative paths from CSV..."
if [[ -f "$CSV_FILE" ]]; then
    echo "   Original paths in CSV:"
    tail -n +2 "$CSV_FILE" | head -5 | while IFS=',' read -r pk_event case_number url key; do
        echo "     $url"
        
        # Check if original path exists
        if [[ -f "$url" ]]; then
            echo "       ✓ Original path exists"
        else
            echo "       ✗ Original path does not exist"
        fi
    done
fi

echo ""
echo "=============================="
echo "Validation completed"
echo "=============================="

# Summary recommendations
echo ""
echo "RECOMMENDATIONS:"
echo "1. If base path /video/data does not exist, you need to:"
echo "   - Create the directory structure"
echo "   - Or update the BASE_PATH in the scripts to the correct location"
echo ""
echo "2. If files exist in original CSV paths (/home/<USER>/db_files/...):"
echo "   - Consider creating symbolic links"
echo "   - Or copying files to the expected location"
echo "   - Or updating BASE_PATH to '/home/<USER>/db_files'"
echo ""
echo "3. Run this script with your CSV file:"
echo "   ./validate_paths.sh /path/to/your/VALO_SQL_DATA_250630.csv"