#!/bin/bash

# Debug script to understand the parsing

case_number="V1250630118"

echo "Case number: $case_number"
echo ""

# Test different extractions
echo "Using cut commands (like original script):"
echo "cut -c6-11: $(echo $case_number | cut -c6-11)"
echo "cut -c9-: $(echo $case_number | cut -c9-)"
echo ""

# Test bash substring
echo "Using bash substring:"
echo "Position reference: V 1 2 5 0 6 3 0 1 1 8"
echo "Index (0-based):   0 1 2 3 4 5 6 7 8 91011"
echo ""

for i in {0..10}; do
    for j in {1..6}; do
        echo "\${case_number:$i:$j} = ${case_number:$i:$j}"
    done
    echo "---"
done