#!/bin/bash

# AI-FARM Image Copy Script - Case Number Input Version
# Copies and renames images by providing case numbers (no CSV required)
# Usage: ./copy_images_by_case_20250630_214855.sh "V1250630118,V1250630119,V1250630120"
# Usage: ./copy_images_by_case_20250630_214855.sh V1250630118
# Usage: ./copy_images_by_case_20250630_214855.sh -f case_numbers.txt

set -e

# Configuration
BASE_PATH="/video/data"
DEST_BASE="/tmp/ai_farm_images_by_case"
LOG_FILE="/tmp/ai_farm_copy_by_case.log"
ERROR_LOG="/tmp/ai_farm_errors_by_case.log"
DEBUG_LOG="/tmp/ai_farm_debug_by_case.log"

# Create destination directories
mkdir -p "$DEST_BASE/processed"
mkdir -p "$DEST_BASE/unknown_status"

# Initialize logs
echo "AI-FARM Image Copy by Case Number Started: $(date)" > "$LOG_FILE"
echo "AI-FARM Copy by Case Number Errors: $(date)" > "$ERROR_LOG"
echo "AI-FARM Debug Log by Case Number: $(date)" > "$DEBUG_LOG"

# Counters
TOTAL_CASES=0
SUCCESS_COUNT=0
ERROR_COUNT=0
MISSING_COUNT=0

# Function to convert case_number to pk_event
case_to_pk() {
    local case_number=$1
    local date_part=$(echo $case_number | cut -c6-11)
    local suffix=$(echo $case_number | cut -c9-)
    
    case $date_part in
        "250623") echo $((suffix + 142502)) ;;
        "250630") echo $((suffix + 143763)) ;;
        *) echo "UNKNOWN" ;;
    esac
}

# Function to process a single case
process_case() {
    local case_number=$1
    TOTAL_CASES=$((TOTAL_CASES + 1))
    
    echo "Processing case: $case_number"
    echo "DEBUG: Starting processing for case: $case_number" >> "$DEBUG_LOG"
    
    # Calculate pk_event using pattern
    local pk_event=$(case_to_pk "$case_number")
    
    if [[ "$pk_event" == "UNKNOWN" ]]; then
        echo "ERROR: Unknown date pattern in case: $case_number" >> "$ERROR_LOG"
        echo "DEBUG: Unknown date pattern in case: $case_number" >> "$DEBUG_LOG"
        ERROR_COUNT=$((ERROR_COUNT + 1))
        return 1
    fi
    
    echo "  Calculated pk_event: $pk_event" >> "$LOG_FILE"
    echo "DEBUG: Calculated pk_event: $pk_event for case: $case_number" >> "$DEBUG_LOG"
    
    # Process both source and cropped images
    local image_types=("source-1.JPEG" "cropped.JPEG")
    local case_success=0
    
    for image_file in "${image_types[@]}"; do
        # Build source path
        local source_path="${BASE_PATH}/VALO2SENFE1b/events/${pk_event}/events_frames/${image_file}"
        
        echo "DEBUG: Built source path: $source_path" >> "$DEBUG_LOG"
        
        # Determine image type for naming
        local image_type=""
        if [[ "$image_file" == "source-1.JPEG" ]]; then
            image_type="source"
        elif [[ "$image_file" == "cropped.JPEG" ]]; then
            image_type="cropped"
        fi
        
        # Build destination filename (status unknown, will be added manually later)
        local dest_filename="${case_number}_${image_type}_unknown.JPEG"
        local dest_path="${DEST_BASE}/unknown_status/${dest_filename}"
        
        echo "DEBUG: Destination path: $dest_path" >> "$DEBUG_LOG"
        
        # Check if source file exists
        if [[ ! -f "$source_path" ]]; then
            echo "MISSING: $source_path" >> "$ERROR_LOG"
            echo "  MISSING: $image_type image" >> "$LOG_FILE"
            echo "DEBUG: File not found at: $source_path" >> "$DEBUG_LOG"
            echo "DEBUG: Directory listing for events_frames:" >> "$DEBUG_LOG"
            ls -la "${BASE_PATH}/VALO2SENFE1b/events/${pk_event}/events_frames/" >> "$DEBUG_LOG" 2>&1 || echo "DEBUG: Directory does not exist: ${BASE_PATH}/VALO2SENFE1b/events/${pk_event}/events_frames/" >> "$DEBUG_LOG"
            MISSING_COUNT=$((MISSING_COUNT + 1))
            continue
        fi
        
        # Copy file
        if cp "$source_path" "$dest_path"; then
            echo "SUCCESS: $source_path -> $dest_filename" >> "$LOG_FILE"
            case_success=$((case_success + 1))
        else
            echo "COPY_ERROR: $source_path -> $dest_filename" >> "$ERROR_LOG"
            ERROR_COUNT=$((ERROR_COUNT + 1))
        fi
    done
    
    # Count case as successful if at least one image was copied
    if [[ $case_success -gt 0 ]]; then
        SUCCESS_COUNT=$((SUCCESS_COUNT + 1))
        echo "  Case completed: $case_success/2 images copied" >> "$LOG_FILE"
    fi
}

# Function to show usage
show_usage() {
    echo "AI-FARM Image Copy by Case Numbers"
    echo "Usage:"
    echo "  $0 \"V1250630118,V1250630119,V1250630120\"    # Comma-separated list"
    echo "  $0 V1250630118                                # Single case"
    echo "  $0 -f case_numbers.txt                        # Read from file"
    echo ""
    echo "File format (one case per line):"
    echo "  V1250630118"
    echo "  V1250630119"
    echo "  V1250630120"
    echo ""
    echo "Supported date ranges:"
    echo "  V125062XXXX (June 23, 2025)"
    echo "  V125063XXXX (June 30, 2025)"
}

# Main processing logic
main() {
    echo "AI-FARM Image Copy by Case Numbers"
    echo "Version: 20250630_214855"
    echo "Base Path: $BASE_PATH"
    echo "Destination: $DEST_BASE"
    echo "Log File: $LOG_FILE"
    echo "Error Log: $ERROR_LOG"
    echo "Debug Log: $DEBUG_LOG"
    echo "----------------------------------------"
    
    # Parse arguments
    if [[ $# -eq 0 ]]; then
        show_usage
        exit 1
    fi
    
    local case_list=""
    
    if [[ "$1" == "-f" && $# -eq 2 ]]; then
        # Read from file
        if [[ ! -f "$2" ]]; then
            echo "Error: File not found: $2"
            exit 1
        fi
        case_list=$(cat "$2" | tr '\n' ',' | sed 's/,$//')
        echo "Reading case numbers from file: $2"
    else
        # Direct input
        case_list="$1"
    fi
    
    # Process each case number
    IFS=',' read -ra CASES <<< "$case_list"
    
    echo "Processing ${#CASES[@]} case number(s)..."
    echo ""
    
    for case_number in "${CASES[@]}"; do
        # Trim whitespace
        case_number=$(echo "$case_number" | xargs)
        
        # Skip empty cases
        if [[ -z "$case_number" ]]; then
            continue
        fi
        
        # Validate case number format
        if [[ ! "$case_number" =~ ^V125062[0-9]{4}$ ]] && [[ ! "$case_number" =~ ^V125063[0-9]{4}$ ]]; then
            echo "ERROR: Invalid case number format: $case_number" >> "$ERROR_LOG"
            ERROR_COUNT=$((ERROR_COUNT + 1))
            continue
        fi
        
        process_case "$case_number"
    done
    
    # Final summary
    echo ""
    echo "----------------------------------------"
    echo "Copy process completed: $(date)"
    echo "Total cases processed: $TOTAL_CASES"
    echo "Successful cases: $SUCCESS_COUNT"
    echo "Missing files: $MISSING_COUNT"
    echo "Errors: $ERROR_COUNT"
    echo "----------------------------------------"
    
    # Write summary to log
    echo "SUMMARY: Cases=$TOTAL_CASES, Success=$SUCCESS_COUNT, Missing=$MISSING_COUNT, Errors=$ERROR_COUNT" >> "$LOG_FILE"
    
    # Check storage usage
    echo "Storage usage in $DEST_BASE:"
    du -sh "$DEST_BASE"
    
    echo ""
    echo "Next steps:"
    echo "1. Review images in: $DEST_BASE/unknown_status/"
    echo "2. Manually classify as valid/invalid and move to appropriate folders"
    echo "3. Or run with validation CSV for automatic classification"
    echo ""
    echo "Check logs for details:"
    echo "Success log: $LOG_FILE"
    echo "Error log: $ERROR_LOG"
    echo "Debug log: $DEBUG_LOG"
}

# Run main function
main "$@"