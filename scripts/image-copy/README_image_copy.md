# Image Copy Scripts for VALO System

This directory contains scripts to extract images from the VALO system using case numbers.

## copy_images_by_casenumber.py

A flexible script that copies images based on case numbers provided in a CSV file.

### Features

- Reads case numbers from CSV (multiple formats supported)
- Automatically finds images using various naming patterns
- Handles the VALO case number format (V125XXXXXXX)
- Provides detailed logging and summary statistics
- Creates a list of missing images for troubleshooting

### Usage

```bash
python copy_images_by_casenumber.py --csv case_numbers.csv --source /path/to/valo/images --output ./extracted_images
```

### CSV Format Options

The script supports multiple CSV formats:

#### Option 1: Comma-separated values (no header)
```csv
V1250630118,V1250630119,V1250630120,V1250630121,V1250630122
V1250630123,V1250630124,V1250630125,V1250630126,V1250630127
```

#### Option 2: Single column with header
```csv
case_number
V1250630118
V1250630119
V1250630120
V1250630121
```

#### Option 3: Multiple values per line
```csv
V1250630118,V1250630119,V1250630120
V1250630121,V1250630122
V1250630123,V1250630124,V1250630125,V1250630126
```

### How It Works

1. **Case Number Processing**: 
   - Extracts pk_event from case number (V125XXXXXXX format)
   - pk_event = case_number[4:] - 630000

2. **Image Search Patterns**:
   - Searches for images using multiple naming patterns
   - Looks for common extensions: .jpg, .jpeg, .png, .bmp, .tiff, .tif
   - Searches recursively through subdirectories

3. **Output Organization**:
   - Creates timestamped output directory
   - Copies images with case number as filename
   - Generates missing_images.txt for troubleshooting

### Example

```bash
# Copy images using the example CSV
python copy_images_by_casenumber.py \
    --csv case_numbers_example.csv \
    --source /valo/terminal/images \
    --output ./extracted_images

# Output will be in: ./extracted_images/extracted_20240115_143022/
# Images will be named: V1250630118.jpg, V1250630119.jpg, etc.
```

### Command Line Options

- `--csv`: Path to CSV file containing case numbers (required)
- `--source`: Source directory where VALO images are stored (required)
- `--output`: Output directory for copied images (default: ./extracted_images)
- `--create-structure`: Create subdirectories by date (future feature)

### Output

The script provides:
1. Real-time progress updates
2. Summary statistics:
   - Total case numbers processed
   - Images found and copied
   - Failed copies
   - Missing images
3. A `missing_images.txt` file listing case numbers without images

### Troubleshooting

If images are not found:
1. Check that the source path is correct
2. Verify the image naming convention in your VALO system
3. Check the missing_images.txt file for case numbers without images
4. Ensure you have read permissions on the source directory

### Requirements

- Python 3.6+
- No external dependencies (uses standard library only)