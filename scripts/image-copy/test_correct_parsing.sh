#!/bin/bash

# Test what I think might be the correct parsing based on the documentation

echo "Testing different parsing interpretations:"
echo "=========================================="
echo ""

test_case="V1250630118"

echo "Test case: $test_case"
echo ""

# Method 1: Original script logic (which doesn't work)
echo "Method 1 - Original script:"
date_part1=$(echo $test_case | cut -c6-11)
suffix1=$(echo $test_case | cut -c9-)
echo "  date_part (cut -c6-11): $date_part1"
echo "  suffix (cut -c9-): $suffix1"
echo "  Looking for '250623' or '250630' in '$date_part1' - WILL NOT MATCH"
echo ""

# Method 2: What if they meant to extract just the date identifier?
echo "Method 2 - Extract date identifier (062/063):"
# V1250630118 - positions for "063"
date_id=$(echo $test_case | cut -c6-8)
suffix2=$(echo $test_case | cut -c9-)
echo "  date_id (cut -c6-8): $date_id"
echo "  suffix (cut -c9-): $suffix2"
if [[ "$date_id" == "062" ]]; then
    echo "  June 23 pattern detected"
    pk_event=$((10#$suffix2 + 142502))
    echo "  pk_event = $suffix2 + 142502 = $pk_event"
elif [[ "$date_id" == "063" ]]; then
    echo "  June 30 pattern detected"
    pk_event=$((10#$suffix2 + 143763))
    echo "  pk_event = $suffix2 + 143763 = $pk_event"
fi
echo ""

# Method 3: Different interpretation of suffix
echo "Method 3 - Last 4 digits as suffix (matching V125062XXXX pattern):"
# If pattern is V125062XXXX where XXXX is suffix
last4=$(echo $test_case | cut -c8-)  # Get everything after V125062/V125063
date_pattern=$(echo $test_case | cut -c5-7)  # Get 062 or 063
echo "  date pattern (cut -c5-7): $date_pattern"
echo "  last part (cut -c8-): $last4"
if [[ "$date_pattern" == "062" ]]; then
    echo "  June 23 pattern"
    pk_event=$((10#$last4 + 142502))
    echo "  pk_event = $last4 + 142502 = $pk_event"
elif [[ "$date_pattern" == "063" ]]; then
    echo "  June 30 pattern"
    pk_event=$((10#$last4 + 143763))
    echo "  pk_event = $last4 + 143763 = $pk_event"
fi
echo ""

# Test the README example
echo "Testing README example: V1250630118 → 118 + 143763 = 143881"
echo "This suggests suffix is last 3 digits (118)"
suffix_3digit="118"
result=$((suffix_3digit + 143763))
echo "Calculation: $suffix_3digit + 143763 = $result"
echo "Path would be: /video/data/VALO2SENFE1b/events/$result/events_frames/"