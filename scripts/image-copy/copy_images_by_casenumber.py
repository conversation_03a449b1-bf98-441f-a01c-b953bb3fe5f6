#!/usr/bin/env python3
"""
Copy VALO images using case numbers from a CSV file.

This script reads case numbers from a CSV file (comma-separated) and copies
the corresponding images from the VALO system to a specified output directory.

Usage:
    python copy_images_by_casenumber.py --csv case_numbers.csv --source /valo/images --output ./extracted_images

CSV Format Example:
    V1250630118,V1250630119,V1250630120,V1250630121
    V1250630122,V1250630123,V1250630124,V1250630125
    
Or single column:
    case_number
    V1250630118
    V1250630119
    V1250630120
"""

import os
import sys
import shutil
import argparse
import csv
from pathlib import Path
from typing import List, Tuple, Optional
import logging
from datetime import datetime

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class CaseNumberImageCopier:
    """Handles copying images based on case numbers."""
    
    def __init__(self, source_base_path: str, output_path: str):
        """
        Initialize the image copier.
        
        Args:
            source_base_path: Base path where VALO images are stored
            output_path: Directory where images will be copied to
        """
        self.source_base_path = Path(source_base_path)
        self.output_path = Path(output_path)
        self.output_path.mkdir(parents=True, exist_ok=True)
        
        # Statistics
        self.total_cases = 0
        self.found_images = 0
        self.copied_images = 0
        self.failed_copies = 0
        self.missing_images = []
        
    def extract_pk_event_from_case_number(self, case_number: str) -> Optional[int]:
        """
        Extract pk_event from case number.
        
        Case number format: V125XXXXXXX where XXXXXXX = pk_event + 630000
        
        Args:
            case_number: Case number in format V125XXXXXXX
            
        Returns:
            pk_event as integer, or None if invalid format
        """
        if not case_number or not case_number.startswith('V125'):
            logger.warning(f"Invalid case number format: {case_number}")
            return None
            
        try:
            # Extract the numeric part after V125
            numeric_part = case_number[4:]
            if len(numeric_part) != 7:
                logger.warning(f"Case number {case_number} has incorrect length")
                return None
                
            # Calculate pk_event
            pk_event = int(numeric_part) - 630000
            return pk_event
        except ValueError as e:
            logger.error(f"Error parsing case number {case_number}: {e}")
            return None
    
    def find_image_path(self, case_number: str) -> Optional[Path]:
        """
        Find the image file path for a given case number.
        
        This method searches for images using various naming patterns
        commonly used in the VALO system.
        
        Args:
            case_number: Case number to find image for
            
        Returns:
            Path to image file if found, None otherwise
        """
        pk_event = self.extract_pk_event_from_case_number(case_number)
        if pk_event is None:
            return None
        
        # Common image naming patterns in VALO system
        possible_patterns = [
            f"*{case_number}*",
            f"*{pk_event}*",
            f"violation_{pk_event}.*",
            f"alert_{pk_event}.*",
            f"case_{case_number}.*",
            f"{case_number}.*",
            f"{pk_event}.*"
        ]
        
        # Common image extensions
        extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif']
        
        # Search for the image
        for pattern in possible_patterns:
            for ext in extensions:
                # Try direct path first
                direct_path = self.source_base_path / f"{pattern.replace('*', '')}{ext}"
                if direct_path.exists():
                    return direct_path
                
                # Search recursively
                try:
                    matches = list(self.source_base_path.rglob(f"{pattern}{ext}"))
                    if matches:
                        # Return the first match
                        return matches[0]
                except Exception as e:
                    logger.debug(f"Error searching for {pattern}{ext}: {e}")
        
        return None
    
    def copy_image(self, case_number: str) -> bool:
        """
        Copy image for a single case number.
        
        Args:
            case_number: Case number to copy image for
            
        Returns:
            True if successful, False otherwise
        """
        self.total_cases += 1
        
        # Find the image
        image_path = self.find_image_path(case_number)
        
        if image_path is None or not image_path.exists():
            logger.warning(f"Image not found for case {case_number}")
            self.missing_images.append(case_number)
            return False
        
        self.found_images += 1
        
        # Determine output filename
        output_filename = f"{case_number}{image_path.suffix}"
        output_file_path = self.output_path / output_filename
        
        try:
            # Copy the file
            shutil.copy2(image_path, output_file_path)
            self.copied_images += 1
            logger.info(f"Copied: {case_number} -> {output_file_path}")
            return True
        except Exception as e:
            logger.error(f"Failed to copy {case_number}: {e}")
            self.failed_copies += 1
            return False
    
    def process_case_numbers(self, case_numbers: List[str]) -> None:
        """
        Process a list of case numbers.
        
        Args:
            case_numbers: List of case numbers to process
        """
        logger.info(f"Processing {len(case_numbers)} case numbers...")
        
        for i, case_number in enumerate(case_numbers, 1):
            case_number = case_number.strip()
            if case_number:  # Skip empty strings
                logger.info(f"Processing {i}/{len(case_numbers)}: {case_number}")
                self.copy_image(case_number)
        
        self.print_summary()
    
    def print_summary(self) -> None:
        """Print summary statistics."""
        logger.info("\n" + "="*50)
        logger.info("COPY SUMMARY")
        logger.info("="*50)
        logger.info(f"Total case numbers processed: {self.total_cases}")
        logger.info(f"Images found: {self.found_images}")
        logger.info(f"Images copied successfully: {self.copied_images}")
        logger.info(f"Failed copies: {self.failed_copies}")
        logger.info(f"Missing images: {len(self.missing_images)}")
        
        if self.missing_images:
            # Save missing images to a file
            missing_file = self.output_path / "missing_images.txt"
            with open(missing_file, 'w') as f:
                f.write("Missing images for case numbers:\n")
                for case in self.missing_images:
                    f.write(f"{case}\n")
            logger.info(f"\nList of missing images saved to: {missing_file}")


def read_case_numbers_from_csv(csv_file: str) -> List[str]:
    """
    Read case numbers from CSV file.
    
    Supports multiple formats:
    1. Comma-separated values in rows
    2. Single column with header 'case_number'
    3. Multiple columns where first column is case numbers
    
    Args:
        csv_file: Path to CSV file
        
    Returns:
        List of case numbers
    """
    case_numbers = []
    
    with open(csv_file, 'r', encoding='utf-8') as f:
        # Try to detect the format
        first_line = f.readline().strip()
        f.seek(0)  # Reset to beginning
        
        # Check if it's a header
        if 'case_number' in first_line.lower() or first_line.startswith('pk_event'):
            # It's a structured CSV with headers
            reader = csv.DictReader(f)
            for row in reader:
                # Try different column names
                case_num = row.get('case_number') or row.get('Case Number') or row.get('CASE_NUMBER')
                if case_num:
                    case_numbers.append(case_num)
        else:
            # It's comma-separated values without headers
            for line in f:
                line = line.strip()
                if line:
                    # Split by comma and add all values
                    cases = [c.strip() for c in line.split(',') if c.strip()]
                    case_numbers.extend(cases)
    
    # Remove duplicates while preserving order
    seen = set()
    unique_cases = []
    for case in case_numbers:
        if case not in seen:
            seen.add(case)
            unique_cases.append(case)
    
    return unique_cases


def main():
    """Main function."""
    parser = argparse.ArgumentParser(
        description="Copy VALO images using case numbers from CSV file"
    )
    parser.add_argument(
        '--csv',
        required=True,
        help='CSV file containing case numbers'
    )
    parser.add_argument(
        '--source',
        required=True,
        help='Source directory where VALO images are stored'
    )
    parser.add_argument(
        '--output',
        default='./extracted_images',
        help='Output directory for copied images (default: ./extracted_images)'
    )
    parser.add_argument(
        '--create-structure',
        action='store_true',
        help='Create subdirectories by date (YYYY/MM/DD) based on case numbers'
    )
    
    args = parser.parse_args()
    
    # Validate inputs
    if not os.path.exists(args.csv):
        logger.error(f"CSV file not found: {args.csv}")
        sys.exit(1)
    
    if not os.path.exists(args.source):
        logger.error(f"Source directory not found: {args.source}")
        sys.exit(1)
    
    # Read case numbers from CSV
    logger.info(f"Reading case numbers from: {args.csv}")
    case_numbers = read_case_numbers_from_csv(args.csv)
    
    if not case_numbers:
        logger.error("No case numbers found in CSV file")
        sys.exit(1)
    
    logger.info(f"Found {len(case_numbers)} unique case numbers")
    
    # Create output directory with timestamp
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = Path(args.output) / f"extracted_{timestamp}"
    
    # Initialize copier and process
    copier = CaseNumberImageCopier(args.source, str(output_dir))
    copier.process_case_numbers(case_numbers)
    
    logger.info(f"\nImages copied to: {output_dir}")


if __name__ == "__main__":
    main()