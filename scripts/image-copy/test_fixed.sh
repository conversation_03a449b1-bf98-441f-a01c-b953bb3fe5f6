#!/bin/bash

# AI-FARM Test Fixed Script - Process only first 3 lines to verify fix
# Usage: ./test_fixed.sh VALO_SQL_DATA_250630.csv

set -e

# Configuration
CSV_FILE="${1:-VALO_SQL_DATA_250630.csv}"
BASE_PATH="/video/data/VALO2SENFE1b/events"
TEST_LINES=3

echo "==========================================="
echo "AI-FARM Test Fixed Script (First $TEST_LINES lines)"
echo "==========================================="
echo "CSV File: $CSV_FILE"
echo "Base Path: $BASE_PATH"
echo ""

# Check if CSV exists
if [[ ! -f "$CSV_FILE" ]]; then
    echo "ERROR: CSV file not found: $CSV_FILE"
    exit 1
fi

# Aggressively clean the CSV file
echo "🔧 Cleaning CSV file from all line ending issues..."
CLEAN_CSV="/tmp/test_cleaned_csv_$(date +%s).csv"

# Remove all carriage returns, clean whitespace, and ensure proper format
sed 's/\r//g' "$CSV_FILE" | sed 's/[[:space:]]*$//' > "$CLEAN_CSV"

echo "✅ CSV cleaned: $CLEAN_CSV"
echo ""

# Show first few lines of cleaned CSV
echo "First few lines of cleaned CSV:"
head -4 "$CLEAN_CSV"
echo ""

# Process only first few lines
echo "Testing first $TEST_LINES lines..."
echo ""

line_count=0
while IFS=',' read -r pk_event case_number url key; do
    # Skip header
    if [[ "$pk_event" == "pk_event" ]]; then
        continue
    fi
    
    line_count=$((line_count + 1))
    if [[ $line_count -gt $TEST_LINES ]]; then
        break
    fi
    
    # Additional cleaning - remove any remaining whitespace/control characters
    pk_event=$(echo "$pk_event" | sed 's/[[:space:]]*$//' | sed 's/^[[:space:]]*//')
    case_number=$(echo "$case_number" | sed 's/[[:space:]]*$//' | sed 's/^[[:space:]]*//')
    url=$(echo "$url" | sed 's/[[:space:]]*$//' | sed 's/^[[:space:]]*//')
    key=$(echo "$key" | sed 's/[[:space:]]*$//' | sed 's/^[[:space:]]*//')
    
    echo "[$line_count] Processing: $case_number (Event: $pk_event)"
    echo "  Raw cleaned: pk_event='$pk_event' case_number='$case_number' url='$url' key='$key'"
    
    # Test variable cleanliness
    echo "  Variable lengths: pk_event=${#pk_event} case_number=${#case_number} key=${#key}"
    
    # Check for any remaining control characters
    if [[ "$key" =~ [[:cntrl:]] ]]; then
        echo "  ⚠️  WARNING: Control characters still found in key variable"
    else
        echo "  ✅ No control characters in key variable"
    fi
    
    # Determine image type from original URL
    if [[ "$url" == *"source-1.JPEG"* ]]; then
        image_type="source"
        filename="source-1.JPEG"
    elif [[ "$url" == *"cropped.JPEG"* ]]; then
        image_type="cropped"
        filename="cropped.JPEG"
    else
        echo "  ❌ Unknown image type in URL: $url"
        continue
    fi
    
    # Build paths
    source_path="${BASE_PATH}/${pk_event}/events_frames/${filename}"
    dest_filename="${case_number}_${image_type}_${key}.JPEG"
    dest_path="/tmp/test_dest/${key}/${dest_filename}"
    
    echo "  Image type: $image_type"
    echo "  Source path: $source_path"
    echo "  Destination: $dest_filename"
    echo "  Full dest path: $dest_path"
    
    # Test path construction
    echo "  Testing path construction..."
    echo "    Key directory: /tmp/test_dest/${key}/"
    echo "    Final filename: ${dest_filename}"
    
    # Check if source file exists
    if [[ -f "$source_path" ]]; then
        echo "  ✅ Source file exists"
    else
        echo "  ❌ Source file missing: $source_path"
    fi
    
    echo ""
done < "$CLEAN_CSV"

# Cleanup
rm -f "$CLEAN_CSV"
echo "🧹 Cleaned up temporary files"
echo ""
echo "==========================================="
echo "Test completed - no files were copied"
echo "If paths look clean, use ./copy_images_fixed.sh"
echo "=========================================="