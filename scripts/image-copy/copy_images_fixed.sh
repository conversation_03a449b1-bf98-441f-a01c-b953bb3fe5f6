#!/bin/bash

# AI-FARM Fixed Image Copy Script - Completely fixes carriage return issues
# Usage: ./copy_images_fixed.sh VALO_SQL_DATA_250630.csv

set -e

# Configuration
CSV_FILE="${1:-VALO_SQL_DATA_250630.csv}"
BASE_PATH="/video/data/VALO2SENFE1b/events"
DEST_BASE="/tmp/ai_farm_images_fixed"
LOG_FILE="/tmp/ai_farm_fixed.log"

# Create destination directories
mkdir -p "$DEST_BASE/valid"
mkdir -p "$DEST_BASE/invalid"

# Initialize log
echo "AI-FARM Fixed Copy Started: $(date)" > "$LOG_FILE"

# Counters
TOTAL_PROCESSED=0
SUCCESS_COUNT=0
ERROR_COUNT=0
MISSING_COUNT=0

echo "==========================================="
echo "AI-FARM Fixed Image Copy Script"
echo "==========================================="
echo "CSV File: $CSV_FILE"
echo "Base Path: $BASE_PATH"
echo "Destination: $DEST_BASE"
echo "Log: $LOG_FILE"
echo ""

# Check if CSV exists
if [[ ! -f "$CSV_FILE" ]]; then
    echo "ERROR: CSV file not found: $CSV_FILE"
    exit 1
fi

# Aggressively clean the CSV file
echo "🔧 Cleaning CSV file from all line ending issues..."
CLEAN_CSV="/tmp/cleaned_csv_$(date +%s).csv"

# Remove all carriage returns, clean whitespace, and ensure proper format
sed 's/\r//g' "$CSV_FILE" | sed 's/[[:space:]]*$//' > "$CLEAN_CSV"

echo "✅ CSV cleaned: $CLEAN_CSV"
echo ""

# Process the cleaned CSV
echo "Processing cleaned CSV file..."
echo ""

# Use process substitution to avoid subshell issues
while IFS=',' read -r pk_event case_number url key; do
    # Additional cleaning - remove any remaining whitespace/control characters
    pk_event=$(echo "$pk_event" | sed 's/[[:space:]]*$//' | sed 's/^[[:space:]]*//')
    case_number=$(echo "$case_number" | sed 's/[[:space:]]*$//' | sed 's/^[[:space:]]*//')
    url=$(echo "$url" | sed 's/[[:space:]]*$//' | sed 's/^[[:space:]]*//')
    key=$(echo "$key" | sed 's/[[:space:]]*$//' | sed 's/^[[:space:]]*//')
    
    # Skip empty lines
    if [[ -z "$pk_event" || -z "$case_number" || -z "$key" ]]; then
        continue
    fi
    
    TOTAL_PROCESSED=$((TOTAL_PROCESSED + 1))
    
    echo "[$TOTAL_PROCESSED] Processing: $case_number (Event: $pk_event)"
    echo "  Debug: pk_event='$pk_event' case_number='$case_number' key='$key'"
    
    # Determine image type from original URL
    if [[ "$url" == *"source-1.JPEG"* ]]; then
        image_type="source"
        filename="source-1.JPEG"
    elif [[ "$url" == *"cropped.JPEG"* ]]; then
        image_type="cropped"
        filename="cropped.JPEG"
    else
        echo "  ❌ Unknown image type in URL: $url"
        echo "ERROR: Unknown image type - $url" >> "$LOG_FILE"
        ERROR_COUNT=$((ERROR_COUNT + 1))
        continue
    fi
    
    # Build source path using YOUR pattern
    source_path="${BASE_PATH}/${pk_event}/events_frames/${filename}"
    
    # Build destination filename - extra cleaning
    dest_filename="${case_number}_${image_type}_${key}.JPEG"
    dest_path="${DEST_BASE}/${key}/${dest_filename}"
    
    echo "  Source: $source_path"
    echo "  Destination: $dest_filename"
    echo "  Full dest path: $dest_path"
    
    # Check if source file exists
    if [[ ! -f "$source_path" ]]; then
        echo "  ❌ File not found: $source_path"
        echo "MISSING: $source_path" >> "$LOG_FILE"
        
        # Check if directory exists
        dir_path="${BASE_PATH}/${pk_event}/events_frames"
        if [[ ! -d "$dir_path" ]]; then
            echo "  💡 Directory missing: $dir_path"
            echo "MISSING_DIR: $dir_path" >> "$LOG_FILE"
        else
            echo "  💡 Directory exists, but file missing"
        fi
        
        MISSING_COUNT=$((MISSING_COUNT + 1))
        echo ""
        continue
    fi
    
    # Copy file
    if cp "$source_path" "$dest_path"; then
        echo "  ✅ Successfully copied: $dest_filename"
        echo "SUCCESS: $source_path -> $dest_filename" >> "$LOG_FILE"
        SUCCESS_COUNT=$((SUCCESS_COUNT + 1))
    else
        echo "  ❌ Copy failed: $dest_filename"
        echo "COPY_ERROR: $source_path -> $dest_filename" >> "$LOG_FILE"
        ERROR_COUNT=$((ERROR_COUNT + 1))
    fi
    
    echo ""
    
done < <(tail -n +2 "$CLEAN_CSV")

# Final summary
echo "==========================================="
echo "PROCESSING COMPLETE"
echo "==========================================="
echo "Total processed: $TOTAL_PROCESSED"
echo "✅ Successful copies: $SUCCESS_COUNT"
echo "❌ Missing files: $MISSING_COUNT"
echo "❌ Copy errors: $ERROR_COUNT"
echo ""

# Show results
echo "Results location: $DEST_BASE"
if [[ -d "$DEST_BASE/valid" ]]; then
    valid_count=$(ls -1 "$DEST_BASE/valid/" 2>/dev/null | wc -l)
    echo "Valid images: $valid_count files"
fi

if [[ -d "$DEST_BASE/invalid" ]]; then
    invalid_count=$(ls -1 "$DEST_BASE/invalid/" 2>/dev/null | wc -l)
    echo "Invalid images: $invalid_count files"
fi

echo ""

# Storage usage
echo "Storage usage:"
du -sh "$DEST_BASE" 2>/dev/null || echo "No files copied"

echo ""
echo "Log file: $LOG_FILE"
echo "==========================================="

# Show some sample results
echo ""
echo "Sample copied files:"
ls -la "$DEST_BASE/valid/" | head -3 2>/dev/null || echo "No valid files"
ls -la "$DEST_BASE/invalid/" | head -3 2>/dev/null || echo "No invalid files"

# Cleanup
rm -f "$CLEAN_CSV"
echo ""
echo "🧹 Cleaned up temporary files"