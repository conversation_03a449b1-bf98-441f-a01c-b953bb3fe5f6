#!/bin/bash

# Copy VALO images using case numbers from CSV file
# Specifically designed for VALO directory structure: /video/data/VALO2SENFE1b/events/{eventid}/event_frames
# Usage: ./copy_valo_images_structured.sh -c case_numbers.csv -s /video/data/VALO2SENFE1b/events -o ./extracted_images

# Default values
OUTPUT_DIR="./extracted_images"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Counters
TOTAL_CASES=0
FOUND_IMAGES=0
COPIED_IMAGES=0
MISSING_IMAGES=0

# Function to display usage
usage() {
    echo "Usage: $0 -c CSV_FILE -s SOURCE_DIR [-o OUTPUT_DIR]"
    echo ""
    echo "Options:"
    echo "  -c CSV_FILE     CSV file containing case numbers (required)"
    echo "  -s SOURCE_DIR   Base VALO events directory (e.g., /video/data/VALO2SENFE1b/events)"
    echo "  -o OUTPUT_DIR   Output directory for copied images (default: ./extracted_images)"
    echo "  -h              Show this help message"
    echo ""
    echo "Example:"
    echo "  $0 -c cases.csv -s /video/data/VALO2SENFE1b/events -o ./output"
    exit 1
}

# Function to extract pk_event from case number
extract_pk_event() {
    local case_number=$1
    
    # Check if case number starts with V125
    if [[ ! "$case_number" =~ ^V125[0-9]{7}$ ]]; then
        echo ""
        return
    fi
    
    # Extract numeric part and calculate pk_event
    local numeric_part=${case_number:4}
    local pk_event=$((10#$numeric_part - 630000))
    echo "$pk_event"
}

# Function to copy images for a case
copy_case_images() {
    local case_number=$1
    local source_base=$2
    local output_dir=$3
    
    ((TOTAL_CASES++))
    
    # Extract event ID from case number
    local event_id=$(extract_pk_event "$case_number")
    
    if [ -z "$event_id" ]; then
        echo -e "${RED}✗ Invalid case number format: $case_number${NC}"
        echo "$case_number" >> "$output_dir/missing_cases.txt"
        ((MISSING_IMAGES++))
        return 1
    fi
    
    # Build path to events_frames directory (note the 's' in events_frames)
    local event_frames_dir="${source_base}/${event_id}/events_frames"
    
    # Check if directory exists
    if [ ! -d "$event_frames_dir" ]; then
        echo -e "${RED}✗ Event directory not found: $event_frames_dir${NC}"
        echo "$case_number (event_id: $event_id)" >> "$output_dir/missing_cases.txt"
        ((MISSING_IMAGES++))
        return 1
    fi
    
    # Create case-specific output directory
    local case_output_dir="${output_dir}/${case_number}"
    mkdir -p "$case_output_dir"
    
    # Copy all images from event_frames
    local images_copied=0
    for image in "$event_frames_dir"/*; do
        # Check if it's a file and an image
        if [ -f "$image" ]; then
            # Get filename
            local filename=$(basename "$image")
            
            # Check if it's likely an image file
            case "${filename,,}" in
                *.jpg|*.jpeg|*.png|*.bmp|*.tiff|*.tif)
                    if cp "$image" "$case_output_dir/" 2>/dev/null; then
                        ((images_copied++))
                    else
                        echo -e "${YELLOW}⚠ Failed to copy: $filename${NC}"
                    fi
                    ;;
            esac
        fi
    done
    
    if [ $images_copied -gt 0 ]; then
        echo -e "${GREEN}✓ Copied $images_copied images for case: $case_number (event_id: $event_id)${NC}"
        ((FOUND_IMAGES++))
        ((COPIED_IMAGES+=$images_copied))
        return 0
    else
        echo -e "${YELLOW}⚠ No images found in: $event_frames_dir${NC}"
        ((MISSING_IMAGES++))
        return 1
    fi
}

# Parse command line arguments
while getopts "c:s:o:h" opt; do
    case $opt in
        c) CSV_FILE="$OPTARG" ;;
        s) SOURCE_DIR="$OPTARG" ;;
        o) OUTPUT_DIR="$OPTARG" ;;
        h) usage ;;
        *) usage ;;
    esac
done

# Validate required arguments
if [ -z "$CSV_FILE" ] || [ -z "$SOURCE_DIR" ]; then
    echo -e "${RED}Error: CSV file and source directory are required${NC}"
    usage
fi

# Check if CSV file exists
if [ ! -f "$CSV_FILE" ]; then
    echo -e "${RED}Error: CSV file not found: $CSV_FILE${NC}"
    exit 1
fi

# Check if source directory exists
if [ ! -d "$SOURCE_DIR" ]; then
    echo -e "${RED}Error: Source directory not found: $SOURCE_DIR${NC}"
    exit 1
fi

# Create output directory with timestamp
FINAL_OUTPUT_DIR="${OUTPUT_DIR}/extracted_${TIMESTAMP}"
mkdir -p "$FINAL_OUTPUT_DIR"

echo "=========================================="
echo "VALO Image Copy Script (Structured)"
echo "=========================================="
echo "CSV File: $CSV_FILE"
echo "Source Directory: $SOURCE_DIR"
echo "Output Directory: $FINAL_OUTPUT_DIR"
echo "Directory Structure: {source}/{event_id}/event_frames/"
echo "=========================================="
echo ""

# Process CSV file
echo "Processing case numbers..."

# Read CSV file and process each case number
while IFS= read -r line; do
    # Skip empty lines
    [ -z "$line" ] && continue
    
    # Skip header lines that might contain "case"
    if [[ "$line" =~ ^[[:space:]]*case|^[[:space:]]*Case|^[[:space:]]*CASE ]]; then
        continue
    fi
    
    # Split by comma and process each case number
    IFS=',' read -ra CASES <<< "$line"
    for case in "${CASES[@]}"; do
        # Trim whitespace
        case=$(echo "$case" | tr -d '[:space:]')
        
        # Skip empty values
        [ -z "$case" ] && continue
        
        # Copy images for this case
        copy_case_images "$case" "$SOURCE_DIR" "$FINAL_OUTPUT_DIR"
    done
done < "$CSV_FILE"

# Create index file with mapping
if [ $FOUND_IMAGES -gt 0 ]; then
    echo "Creating index file..."
    {
        echo "VALO Image Extraction Index"
        echo "=========================="
        echo "Generated: $(date)"
        echo ""
        echo "Case Number -> Event ID Mapping:"
        echo "--------------------------------"
        
        # Re-read CSV to create mapping
        while IFS= read -r line; do
            [ -z "$line" ] && continue
            [[ "$line" =~ ^[[:space:]]*case|^[[:space:]]*Case|^[[:space:]]*CASE ]] && continue
            
            IFS=',' read -ra CASES <<< "$line"
            for case in "${CASES[@]}"; do
                case=$(echo "$case" | tr -d '[:space:]')
                [ -z "$case" ] && continue
                
                event_id=$(extract_pk_event "$case")
                [ -n "$event_id" ] && echo "$case -> Event ID: $event_id"
            done
        done < "$CSV_FILE"
    } > "$FINAL_OUTPUT_DIR/index.txt"
fi

# Print summary
echo ""
echo "=========================================="
echo "COPY SUMMARY"
echo "=========================================="
echo "Total case numbers processed: $TOTAL_CASES"
echo "Cases with images found: $FOUND_IMAGES"
echo "Total images copied: $COPIED_IMAGES"
echo "Cases with missing images: $MISSING_IMAGES"
echo ""
echo "Output directory: $FINAL_OUTPUT_DIR"

if [ -f "$FINAL_OUTPUT_DIR/missing_cases.txt" ]; then
    echo "Missing cases list: $FINAL_OUTPUT_DIR/missing_cases.txt"
fi

if [ -f "$FINAL_OUTPUT_DIR/index.txt" ]; then
    echo "Case number index: $FINAL_OUTPUT_DIR/index.txt"
fi

echo "=========================================="

# Exit with appropriate code
if [ $COPIED_IMAGES -eq 0 ]; then
    exit 1
else
    exit 0
fi