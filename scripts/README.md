# Scripts

Utility scripts for AI-FARM system operations and data processing.

## Directory Structure

### image-copy/
VALO system image extraction scripts for preparing customer demonstration data.

#### Scripts Available:
- **copy_images_20250630_213833.sh** - Full CSV-based image extraction
- **copy_images_by_case_20250630_214855.sh** - Case number-based selective extraction

See [image-copy/README.md](image-copy/README.md) for detailed usage instructions.

## Usage

All scripts are designed for air-gapped environments with no external dependencies.

### Prerequisites
- Bash shell (Ubuntu/RedHat compatible)
- Access to VALO production server paths
- Sufficient storage space (estimate provided per script)

### General Pattern
```bash
cd scripts/[script-category]
chmod +x script_name.sh
./script_name.sh [arguments]
```

## Development

When adding new scripts:
1. Follow timestamp naming convention: `script_name_YYYYMMDD_HHMMSS.sh`
2. Include comprehensive usage documentation
3. Add to version tracking in [docs/SCRIPT_VERSION_LOG.md](../docs/SCRIPT_VERSION_LOG.md)
4. Ensure air-gap compatibility (no external dependencies)
5. Include storage requirements and safety checks

## Production Notes

- All scripts tested on RedHat production environment
- Compatible with Ubuntu deployment targets
- Include comprehensive error logging and recovery
- Follow security best practices for production data handling