#!/usr/bin/env python3
"""
Generate sample images for VLM testing
Creates realistic-looking test images that simulate various safety scenarios
"""

import os
from pathlib import Path
from PIL import Image, ImageDraw, ImageFont
import random


def create_sample_images():
    """Create sample images for different safety scenarios"""
    
    # Create output directory
    output_dir = Path("data/sample_images")
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Define scenarios with their visual characteristics
    scenarios = [
        {
            "filename": "crane_boom.jpg",
            "base_color": (255, 140, 0),  # Orange
            "shapes": ["crane"],
            "text": "CRANE STRUCTURE",
            "description": "Crane boom - often mistaken for person"
        },
        {
            "filename": "person_violation.jpg",
            "base_color": (255, 0, 0),  # Red
            "shapes": ["person", "danger_zone"],
            "text": "DANGER ZONE",
            "description": "Person in restricted area"
        },
        {
            "filename": "equipment_shadow.jpg",
            "base_color": (128, 128, 128),  # Gray
            "shapes": ["shadow"],
            "text": "SHADOW",
            "description": "Equipment shadow creating false positive"
        },
        {
            "filename": "safety_gear.jpg",
            "base_color": (255, 255, 0),  # Yellow
            "shapes": ["person", "no_helmet"],
            "text": "NO PPE",
            "description": "Person without safety gear"
        },
        {
            "filename": "machinery_only.jpg",
            "base_color": (0, 128, 0),  # Green
            "shapes": ["machinery"],
            "text": "EQUIPMENT ONLY",
            "description": "Machinery with no people present"
        },
        {
            "filename": "actual_violation.jpg",
            "base_color": (255, 0, 0),  # Red
            "shapes": ["person", "machinery", "danger"],
            "text": "VIOLATION",
            "description": "Real safety violation"
        },
        {
            "filename": "lighting_artifact.jpg",
            "base_color": (64, 64, 64),  # Dark gray
            "shapes": ["glare"],
            "text": "POOR LIGHTING",
            "description": "Lighting artifact causing false alert"
        },
        {
            "filename": "debris.jpg",
            "base_color": (139, 69, 19),  # Brown
            "shapes": ["debris"],
            "text": "DEBRIS",
            "description": "Debris on ground"
        },
        {
            "filename": "authorized_work.jpg",
            "base_color": (0, 255, 0),  # Green
            "shapes": ["person", "safety_zone"],
            "text": "AUTHORIZED",
            "description": "Authorized work in progress"
        },
        {
            "filename": "reflection.jpg",
            "base_color": (173, 216, 230),  # Light blue
            "shapes": ["reflection"],
            "text": "REFLECTION",
            "description": "Glass reflection"
        }
    ]
    
    print("Generating sample images for VLM testing...")
    
    for scenario in scenarios:
        # Create image
        img = Image.new('RGB', (800, 600), scenario["base_color"])
        draw = ImageDraw.Draw(img)
        
        # Add shapes based on scenario
        if "crane" in scenario["shapes"]:
            # Draw crane-like structure
            draw.line([(400, 100), (400, 500)], fill=(100, 100, 100), width=20)
            draw.line([(200, 200), (600, 200)], fill=(100, 100, 100), width=15)
            
        if "person" in scenario["shapes"]:
            # Draw person silhouette
            # Head
            draw.ellipse([(380, 250), (420, 290)], fill=(50, 50, 50))
            # Body
            draw.rectangle([(385, 290), (415, 380)], fill=(50, 50, 50))
            # Arms and legs
            draw.line([(390, 310), (360, 350)], fill=(50, 50, 50), width=8)
            draw.line([(410, 310), (440, 350)], fill=(50, 50, 50), width=8)
            draw.line([(390, 380), (380, 450)], fill=(50, 50, 50), width=10)
            draw.line([(410, 380), (420, 450)], fill=(50, 50, 50), width=10)
            
        if "machinery" in scenario["shapes"]:
            # Draw machinery
            draw.rectangle([(500, 300), (700, 450)], fill=(80, 80, 80))
            draw.rectangle([(520, 320), (680, 430)], fill=(120, 120, 120))
            
        if "shadow" in scenario["shapes"]:
            # Draw shadow
            draw.ellipse([(300, 400), (500, 500)], fill=(40, 40, 40))
            
        if "danger_zone" in scenario["shapes"]:
            # Draw danger zone markings
            for i in range(0, 800, 100):
                draw.rectangle([(i, 550), (i+50, 600)], fill=(255, 255, 0))
                draw.rectangle([(i+50, 550), (i+100, 600)], fill=(0, 0, 0))
                
        if "safety_zone" in scenario["shapes"]:
            # Draw safety zone
            draw.rectangle([(100, 100), (700, 500)], outline=(0, 255, 0), width=5)
            
        if "debris" in scenario["shapes"]:
            # Draw random debris
            for _ in range(10):
                x = random.randint(200, 600)
                y = random.randint(400, 550)
                size = random.randint(20, 60)
                draw.ellipse([(x, y), (x+size, y+size)], fill=(100, 80, 60))
                
        if "glare" in scenario["shapes"]:
            # Draw light glare
            for i in range(5):
                alpha = 255 - i * 40
                color = (alpha, alpha, alpha)
                draw.ellipse([(350-i*30, 250-i*30), (450+i*30, 350+i*30)], fill=color)
                
        if "reflection" in scenario["shapes"]:
            # Draw reflection effect
            draw.rectangle([(200, 200), (600, 400)], fill=(200, 230, 255))
            draw.rectangle([(250, 250), (550, 350)], fill=(150, 200, 255))
            
        if "no_helmet" in scenario["shapes"]:
            # Add warning indicator
            draw.polygon([(400, 200), (350, 240), (450, 240)], fill=(255, 0, 0))
            draw.text((395, 215), "!", fill=(255, 255, 255))
            
        if "danger" in scenario["shapes"]:
            # Add danger indicators
            draw.line([(480, 350), (520, 350)], fill=(255, 0, 0), width=10)
            draw.text((460, 360), "DANGER", fill=(255, 0, 0))
        
        # Add text label
        try:
            # Try to use a font, fall back to default if not available
            font = ImageFont.truetype("/usr/share/fonts/truetype/dejavu/DejaVuSans-Bold.ttf", 40)
        except:
            font = None
            
        text_bbox = draw.textbbox((0, 0), scenario["text"], font=font)
        text_width = text_bbox[2] - text_bbox[0]
        text_x = (800 - text_width) // 2
        draw.text((text_x, 50), scenario["text"], fill=(255, 255, 255), font=font)
        
        # Save image
        output_path = output_dir / scenario["filename"]
        img.save(output_path, quality=95)
        print(f"  ✓ Created {scenario['filename']}: {scenario['description']}")
    
    print(f"\nGenerated {len(scenarios)} sample images in {output_dir}")
    print("\nThese images simulate various safety monitoring scenarios:")
    print("- False positives (crane structures, shadows, reflections)")
    print("- True violations (people in danger zones, missing PPE)")
    print("- Edge cases (poor lighting, debris)")
    

def create_demo_batch_csv():
    """Create a demo batch CSV file"""
    csv_content = """pk_event,case_number,url,key
2001,V1250640001,data/sample_images/crane_boom.jpg,invalid
2002,V1250640002,data/sample_images/person_violation.jpg,valid
2003,V1250640003,data/sample_images/equipment_shadow.jpg,invalid
2004,V1250640004,data/sample_images/safety_gear.jpg,valid
2005,V1250640005,data/sample_images/machinery_only.jpg,invalid
2006,V1250640006,data/sample_images/actual_violation.jpg,valid
2007,V1250640007,data/sample_images/lighting_artifact.jpg,invalid
2008,V1250640008,data/sample_images/debris.jpg,invalid
2009,V1250640009,data/sample_images/authorized_work.jpg,valid
2010,V1250640010,data/sample_images/reflection.jpg,invalid
"""
    
    csv_path = Path("data/demo_batch.csv")
    csv_path.write_text(csv_content)
    print(f"\nCreated demo batch CSV: {csv_path}")
    print("This CSV references the generated sample images for batch processing tests")


if __name__ == "__main__":
    create_sample_images()
    create_demo_batch_csv()
    
    print("\n✅ Sample data generation complete!")
    print("\nYou can now use these files to test the VLM integration:")
    print("- Individual images: data/sample_images/*.jpg")
    print("- Batch CSV: data/demo_batch.csv")
    print("- Test data CSV: data/sample_vlm_test_data.csv")