{"name": "ai-farm", "version": "1.0.0", "description": "AI-powered safety violation detection system for workplace monitoring", "main": "index.js", "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "cd backend && uvicorn app.main:app --reload --host 0.0.0.0 --port 8001", "dev:frontend": "cd frontend && npm start", "build": "npm run build:frontend", "build:frontend": "cd frontend && npm run build", "test": "npm run test:backend && npm run test:frontend", "test:backend": "cd backend && python -m pytest", "test:frontend": "cd frontend && npm test", "test:e2e": "./run-e2e-tests.sh", "start:production": "cd backend && uvicorn app.main:app --host 0.0.0.0 --port 8001", "mcp:setup": "./setup-mcp-servers.sh", "clean": "rm -rf frontend/node_modules backend/.pytest_cache mcp-server/node_modules", "install:all": "npm ci && cd frontend && npm ci && cd ../mcp-server && npm ci"}, "keywords": ["ai", "machine-learning", "safety", "violation-detection", "workplace-monitoring", "computer-vision", "<PERSON><PERSON><PERSON>", "react", "typescript"], "author": "AI Farm Development Team", "license": "MIT", "dependencies": {"@modelcontextprotocol/sdk": "^1.13.2", "fs-extra": "^11.3.0", "puppeteer": "^24.11.1"}, "devDependencies": {"concurrently": "^8.2.2"}, "workspaces": ["frontend", "mcp-server"], "engines": {"node": ">=18.0.0", "python": ">=3.9.0"}, "repository": {"type": "git", "url": "git+https://github.com/your-org/ai-farm.git"}, "bugs": {"url": "https://github.com/your-org/ai-farm/issues"}, "homepage": "https://github.com/your-org/ai-farm#readme"}