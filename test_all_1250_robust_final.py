#!/usr/bin/env python3
"""
Test ALL 1250 cases with maximum robustness
This will complete the full dataset testing as requested
"""

import json
import base64
import requests
import os
from datetime import datetime
import time
import threading
from queue import Queue
import signal
import sys

# VLM Configuration
VLM_API_URL = "http://100.106.127.35:9500/v1/chat/completions"
VLM_MODEL = "VLM-38B-AWQ"

# The proven assumption-based prompt
ASSUMPTION_BASED_PROMPT = """SAFETY ALERT ANALYSIS
DEFAULT ASSUMPTION: This is a false positive (97% probability)
To override this assumption, you need CLEAR evidence:
- Person clearly visible AND  
- No helmet visible AND
- No safety vest visible

Is this a FALSE POSITIVE? YES/NO"""

class RobustTester:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({'Content-Type': 'application/json'})
        self.results = []
        self.processed_count = 0
        self.error_count = 0
        self.start_time = time.time()
        self.save_interval = 10  # Save every 10 cases
        
    def encode_image(self, image_path):
        """Encode image to base64"""
        try:
            if not os.path.exists(image_path):
                return None
            with open(image_path, 'rb') as f:
                return base64.b64encode(f.read()).decode('utf-8')
        except Exception as e:
            print(f"\nImage encoding error: {e}")
            return None

    def call_vlm_robust(self, image_base64, case_number, max_retries=5):
        """Call VLM with maximum robustness"""
        payload = {
            "model": VLM_MODEL,
            "messages": [{
                "role": "user",
                "content": [
                    {"type": "text", "text": ASSUMPTION_BASED_PROMPT},
                    {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{image_base64}"}}
                ]
            }],
            "temperature": 0.1,
            "max_tokens": 50
        }
        
        for attempt in range(max_retries):
            try:
                # Progressive timeout increase
                timeout = 10 + (attempt * 5)  # 10, 15, 20, 25, 30 seconds
                
                response = self.session.post(VLM_API_URL, json=payload, timeout=timeout)
                
                if response.status_code == 200:
                    result = response.json()
                    return result['choices'][0]['message']['content'].strip()
                else:
                    print(f"\nVLM API error {response.status_code} for {case_number}, attempt {attempt+1}")
                    if attempt < max_retries - 1:
                        time.sleep(2 ** attempt)  # Exponential backoff
                        
            except requests.Timeout:
                print(f"\nTimeout for {case_number}, attempt {attempt+1}/{max_retries}")
                if attempt < max_retries - 1:
                    time.sleep(3 ** attempt)  # Longer backoff for timeouts
                    
            except Exception as e:
                print(f"\nError for {case_number}: {e}")
                if attempt < max_retries - 1:
                    time.sleep(2 ** attempt)
        
        return None

    def process_case(self, case):
        """Process a single case"""
        case_num = case['case_number']
        cropped_path = case['cropped_image']
        is_false_positive = case['is_false_positive']
        
        # Encode image
        image_base64 = self.encode_image(cropped_path)
        if not image_base64:
            self.error_count += 1
            return {
                'case_number': case_num,
                'status': 'error',
                'error': 'Failed to encode image',
                'timestamp': datetime.now().isoformat()
            }
        
        # Call VLM
        response = self.call_vlm_robust(image_base64, case_num)
        
        if response:
            predicted_fp = 'YES' in response.upper()
            correct = predicted_fp == is_false_positive
            
            return {
                'case_number': case_num,
                'status': 'success',
                'response': response,
                'predicted_fp': predicted_fp,
                'actual_fp': is_false_positive,
                'correct': correct,
                'timestamp': datetime.now().isoformat()
            }
        else:
            self.error_count += 1
            return {
                'case_number': case_num,
                'status': 'failed',
                'error': 'VLM call failed after all retries',
                'timestamp': datetime.now().isoformat()
            }

    def save_progress(self):
        """Save current progress"""
        progress_data = {
            'timestamp': datetime.now().isoformat(),
            'processed': self.processed_count,
            'total': 1250,
            'success_rate': len([r for r in self.results if r['status'] == 'success']) / max(1, len(self.results)) * 100,
            'error_count': self.error_count,
            'elapsed_time': time.time() - self.start_time,
            'results': self.results
        }
        
        with open('all_1250_progress.json', 'w') as f:
            json.dump(progress_data, f, indent=2)

    def test_all_1250_cases(self):
        """Test all 1250 cases with maximum determination"""
        print("\n" + "="*80)
        print("TESTING ALL 1250 CASES - ROBUST APPROACH")
        print("="*80)
        
        # Load dataset
        print("\n1. Loading dataset...")
        with open('valo_batch_round3_complete.json', 'r') as f:
            data = json.load(f)
        
        all_cases = data['results']
        print(f"Total cases to test: {len(all_cases)}")
        
        # Load existing progress if any
        existing_results = {}
        if os.path.exists('all_1250_progress.json'):
            with open('all_1250_progress.json', 'r') as f:
                existing = json.load(f)
                for result in existing.get('results', []):
                    if result['status'] == 'success':
                        existing_results[result['case_number']] = result
            print(f"Found {len(existing_results)} existing successful results")
        
        # Filter cases to test
        cases_to_test = [case for case in all_cases if case['case_number'] not in existing_results]
        print(f"Cases remaining to test: {len(cases_to_test)}")
        
        if not cases_to_test:
            print("All cases already tested!")
            return
        
        # Start testing
        print(f"\n2. Testing {len(cases_to_test)} cases...")
        print("Press Ctrl+C to stop and save progress")
        
        # Add existing results
        self.results = list(existing_results.values())
        self.processed_count = len(existing_results)
        
        try:
            for i, case in enumerate(cases_to_test):
                print(f"\rProgress: {self.processed_count + i + 1}/1250 | Case: {case['case_number']}", end='', flush=True)
                
                result = self.process_case(case)
                self.results.append(result)
                self.processed_count += 1
                
                # Save progress periodically
                if (i + 1) % self.save_interval == 0:
                    self.save_progress()
                    successful = len([r for r in self.results if r['status'] == 'success'])
                    print(f"\n  → Saved progress: {successful} successful, {self.error_count} errors")
                
        except KeyboardInterrupt:
            print("\n\nInterrupted by user. Saving progress...")
            self.save_progress()
            return
        
        # Final save
        self.save_progress()
        
        # Calculate final results
        print(f"\n\n3. FINAL RESULTS")
        print("="*80)
        
        successful = [r for r in self.results if r['status'] == 'success']
        failed = [r for r in self.results if r['status'] != 'success']
        
        print(f"Total processed: {len(self.results)}/1250")
        print(f"Successful: {len(successful)}")
        print(f"Failed/Errors: {len(failed)}")
        
        if successful:
            # Calculate performance
            correct = sum(1 for r in successful if r.get('correct', False))
            fp_cases = [r for r in successful if r.get('actual_fp', True)]
            fp_detected = sum(1 for r in fp_cases if r.get('predicted_fp', False))
            
            accuracy = (correct / len(successful)) * 100
            fp_rate = (fp_detected / len(fp_cases)) * 100 if fp_cases else 0
            
            print(f"\nPerformance:")
            print(f"  - Overall Accuracy: {accuracy:.1f}%")
            print(f"  - FP Detection Rate: {fp_rate:.1f}%")
            print(f"  - Cases Analyzed: {len(successful)}")
            
            # Production estimates
            print(f"\nProduction Estimates (based on {fp_rate:.1f}% test performance):")
            print(f"  - Optimistic (15% degradation): {fp_rate * 0.85:.1f}%")
            print(f"  - Realistic (20% degradation): {fp_rate * 0.80:.1f}%")
            print(f"  - Conservative (25% degradation): {fp_rate * 0.75:.1f}%")
        
        elapsed = time.time() - self.start_time
        print(f"\nTotal time: {elapsed/3600:.1f} hours")
        print(f"Average per case: {elapsed/len(self.results):.1f} seconds")
        
        # Save final comprehensive report
        final_report = {
            'test_date': datetime.now().isoformat(),
            'total_cases': 1250,
            'processed_cases': len(self.results),
            'successful_cases': len(successful),
            'failed_cases': len(failed),
            'performance': {
                'accuracy': accuracy if successful else 0,
                'fp_detection_rate': fp_rate if successful else 0
            },
            'production_estimates': {
                'optimistic': fp_rate * 0.85 if successful else 0,
                'realistic': fp_rate * 0.80 if successful else 0,
                'conservative': fp_rate * 0.75 if successful else 0
            },
            'testing_duration_hours': elapsed / 3600,
            'results': self.results
        }
        
        with open('FINAL_ALL_1250_RESULTS.json', 'w') as f:
            json.dump(final_report, f, indent=2)
        
        print(f"\n✓ Final results saved to: FINAL_ALL_1250_RESULTS.json")
        print("="*80)

def signal_handler(sig, frame):
    """Handle Ctrl+C gracefully"""
    print('\n\nReceived interrupt signal. Saving progress and exiting...')
    sys.exit(0)

if __name__ == "__main__":
    signal.signal(signal.SIGINT, signal_handler)
    tester = RobustTester()
    tester.test_all_1250_cases()