#!/usr/bin/env python3
"""
Round 9: Aggressive False Positive Detection
Lower threshold for auto-dismissal
"""
import asyncio
import json
import logging
import aiohttp
from datetime import datetime

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def analyze_case_round9(session, case, vlm_endpoint):
    """Aggressive FP detection"""
    try:
        remarks = case.get('remarks', '').upper()
        
        # Aggressive detection patterns
        aggressive_patterns = [
            'VESSEL', 'CRANE', 'EQUIPMENT', 'STRUCTURE', 'MACHINE',
            'NO PERSON', 'EMPTY', 'OBJECT', 'TOOL', 'VEHICLE'
        ]
        
        # Check remarks first
        remarks_match = any(pattern in remarks for pattern in aggressive_patterns)
        
        prompt = f"""ROUND 9: AGGRESSIVE FALSE POSITIVE DETECTION

This system aggressively filters false positives.
Any doubt = dismiss as false positive.

Quick checks:
1. No clear person visible = FALSE POSITIVE
2. Equipment/structure only = FALSE POSITIVE  
3. Unclear/blurry = FALSE POSITIVE
4. PPE compliance visible = FALSE POSITIVE

Is this likely a FALSE POSITIVE?
Answer: YES/NO"""

        payload = {
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": prompt},
                        {"type": "image_url", "image_url": {"url": f"file://{case['cropped_image']}"}}
                    ]
                }
            ],
            "temperature": 0.3,  # Higher temp for more aggressive
            "max_tokens": 100
        }
        
        async with session.post(vlm_endpoint, json=payload) as response:
            if response.status == 200:
                result = await response.json()
                content = result['choices'][0]['message']['content']
                
                # Aggressive: YES or any doubt = dismiss
                decision = "YES" in content.upper() or "UNCLEAR" in content.upper()
                
                # Boost decision if remarks match
                if remarks_match and not decision:
                    decision = True
                
                return {
                    'case_number': case['case_number'],
                    'is_false_positive': case['is_false_positive'],
                    'vlm_decision': decision,
                    'confidence': 75 if decision else 90,
                    'reasoning': content
                }
    except Exception as e:
        logger.error(f"Error: {e}")
        return None

async def main():
    vlm_endpoint = "http://100.106.127.35:9500/v1/chat/completions"
    
    # Load cases
    with open('valo_batch_round3_complete.json', 'r') as f:
        all_cases = json.load(f)['results']
    
    logger.info("="*80)
    logger.info("ROUND 9: AGGRESSIVE FALSE POSITIVE DETECTION")
    logger.info("="*80)
    
    async with aiohttp.ClientSession() as session:
        results = []
        for i in range(0, len(all_cases), 20):
            batch = all_cases[i:i+20]
            tasks = [analyze_case_round9(session, case, vlm_endpoint) for case in batch]
            batch_results = await asyncio.gather(*tasks)
            results.extend([r for r in batch_results if r])
            
            if len(results) % 100 == 0:
                logger.info(f"Progress: {len(results)}/{len(all_cases)}")
        
        # Calculate stats
        tp = sum(1 for r in results if r['is_false_positive'] and r['vlm_decision'])
        tn = sum(1 for r in results if not r['is_false_positive'] and not r['vlm_decision'])
        fp_total = sum(1 for r in results if r['is_false_positive'])
        valid_total = sum(1 for r in results if not r['is_false_positive'])
        
        fp_rate = (tp / fp_total * 100) if fp_total > 0 else 0
        valid_rate = (tn / valid_total * 100) if valid_total > 0 else 100
        
        logger.info(f"Round 9 Complete: {fp_rate:.1f}% FP detection")
        
        # Save results
        output = {
            'stats': {
                'round': 9,
                'fp_detection_rate': fp_rate,
                'valid_protection_rate': valid_rate,
                'total_cases': len(results)
            },
            'results': results
        }
        
        with open('valo_round9_aggressive_complete.json', 'w') as f:
            json.dump(output, f, indent=2)

if __name__ == "__main__":
    asyncio.run(main())
