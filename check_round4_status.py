#!/usr/bin/env python3
"""Check Round 4 processing status"""

import json
import os
import glob
from datetime import datetime

print("="*80)
print(f"Round 4 Status Check - {datetime.now().strftime('%H:%M:%S')}")
print("="*80)

# Check for Round 4 files
round4_files = glob.glob('*round4*.json')
print(f"\nRound 4 files found: {len(round4_files)}")
for f in round4_files:
    print(f"  - {f}")

# Check for progress files
progress_files = glob.glob('valo_*_progress.json')
print(f"\nProgress files:")
for pf in sorted(progress_files):
    try:
        with open(pf, 'r') as f:
            data = json.load(f)
            print(f"  {pf}: Round {data.get('round', '?')} - {data.get('cases_processed', 0)}/1250 cases")
    except:
        pass

# Check Round 3 completion
if os.path.exists('valo_batch_round3_complete.json'):
    with open('valo_batch_round3_complete.json', 'r') as f:
        r3 = json.load(f)
        print(f"\nRound 3 Complete: FP Detection {r3['stats']['fp_detection_rate']:.1f}%")

# Check for safe complete files
safe_files = glob.glob('valo_round*_safe_complete.json')
print(f"\nSafe complete files: {len(safe_files)}")
for sf in sorted(safe_files):
    print(f"  - {sf}")

# Check running processes
import subprocess
try:
    ps = subprocess.check_output(['ps', 'aux'], text=True)
    python_procs = [line for line in ps.split('\n') if 'python' in line and ('round' in line or 'achieve' in line)]
    print(f"\nRunning processes: {len(python_procs)}")
    for proc in python_procs[:3]:
        parts = proc.split()
        if len(parts) > 10:
            print(f"  - {' '.join(parts[10:])[:80]}...")
except:
    pass

print("\n" + "="*80)