#!/usr/bin/env python3
"""
Smart retry system for testing ALL 1250 cases
- Tracks which cases succeeded/failed
- Only retries failed cases
- Uses adaptive strategies for different failure types
"""

import json
import asyncio
import aiohttp
import logging
import base64
from datetime import datetime
import time
import os
from typing import Dict, List, Set
import random

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(message)s'
)
logger = logging.getLogger(__name__)


class SmartRetryTester:
    """Intelligent retry system for complete dataset testing"""
    
    def __init__(self):
        self.vlm_endpoint = "http://**************:9500/v1/chat/completions"
        self.model = "VLM-38B-AWQ"
        
        # Track results across retries
        self.completed_cases = {}  # case_number -> result
        self.failed_cases = set()  # case_numbers that need retry
        self.permanent_failures = set()  # cases that failed multiple times
        
        # Approaches to test
        self.approaches = {
            'assumption_based': """SAFETY ALERT ANALYSIS
DEFAULT ASSUMPTION: This is a false positive (97% probability)
To override this assumption, you need CLEAR evidence:
- Person clearly visible AND
- No helmet visible AND
- No safety vest visible

Is this a FALSE POSITIVE? YES/NO""",
            
            'alert_fatigue_prevention': """ALERT FATIGUE PREVENTION MODE
Too many false alerts = ignored real violations
Help reduce false alerts by being practical.
Mark as FALSE POSITIVE unless you see:
- Obvious person
- Clearly missing PPE
- No doubt whatsoever
Is this a FALSE POSITIVE? YES/NO""",
            
            'worksite_reality': """WORKSITE REALITY CHECK
Facts about worksites:
- Workers wear PPE to protect themselves
- Supervisors enforce PPE rules
- Workers without PPE get sent home
Is it likely someone is working without PPE?
Probably not → FALSE POSITIVE"""
        }
        
    def load_previous_results(self, approach_name: str):
        """Load any previous results to avoid re-testing completed cases"""
        results_file = f'smart_retry_{approach_name}_results.json'
        if os.path.exists(results_file):
            with open(results_file, 'r') as f:
                data = json.load(f)
                self.completed_cases = data.get('completed', {})
                self.failed_cases = set(data.get('failed', []))
                self.permanent_failures = set(data.get('permanent_failures', []))
                logger.info(f"Loaded previous results: {len(self.completed_cases)} completed, "
                           f"{len(self.failed_cases)} to retry")
        else:
            self.completed_cases = {}
            self.failed_cases = set()
            self.permanent_failures = set()
    
    def save_progress(self, approach_name: str):
        """Save current progress"""
        results_file = f'smart_retry_{approach_name}_results.json'
        with open(results_file, 'w') as f:
            json.dump({
                'approach': approach_name,
                'timestamp': datetime.now().isoformat(),
                'completed': self.completed_cases,
                'failed': list(self.failed_cases),
                'permanent_failures': list(self.permanent_failures),
                'stats': {
                    'total_completed': len(self.completed_cases),
                    'total_failed': len(self.failed_cases),
                    'permanent_failures': len(self.permanent_failures)
                }
            }, f, indent=2)
    
    async def analyze_with_smart_retry(self, session: aiohttp.ClientSession,
                                     case: Dict, prompt: str,
                                     retry_num: int = 0) -> Dict:
        """Analyze case with smart retry strategies"""
        case_num = case['case_number']
        
        # Skip if already completed
        if case_num in self.completed_cases:
            return self.completed_cases[case_num]
        
        # Skip if permanently failed
        if case_num in self.permanent_failures:
            return {'case_number': case_num, 'error': 'permanent_failure', 'success': False}
        
        # Adaptive timeout based on retry number
        timeout = 15 + (retry_num * 5)  # Increase timeout with each retry
        
        # Add jitter to avoid thundering herd
        if retry_num > 0:
            await asyncio.sleep(random.uniform(0.5, 2.0) * retry_num)
        
        try:
            # Check image exists
            if not os.path.exists(case['cropped_image']):
                self.permanent_failures.add(case_num)
                return {'case_number': case_num, 'error': 'image_not_found', 'success': False}
            
            with open(case['cropped_image'], 'rb') as f:
                image_data = base64.b64encode(f.read()).decode('utf-8')
            
            payload = {
                "model": self.model,
                "messages": [{
                    "role": "user",
                    "content": [
                        {"type": "text", "text": prompt},
                        {"type": "image_url", 
                         "image_url": {"url": f"data:image/jpeg;base64,{image_data}"}}
                    ]
                }],
                "temperature": 0.1,
                "max_tokens": 50
            }
            
            timeout_obj = aiohttp.ClientTimeout(total=timeout)
            async with session.post(self.vlm_endpoint, json=payload, 
                                  timeout=timeout_obj) as response:
                if response.status == 200:
                    result = await response.json()
                    content = result['choices'][0]['message']['content'].upper()
                    is_fp = "YES" in content[:50] or "FALSE POSITIVE" in content
                    
                    # Success! Save result
                    result_data = {
                        'case_number': case_num,
                        'predicted_fp': is_fp,
                        'actual_fp': case['is_false_positive'],
                        'correct': is_fp == case['is_false_positive'],
                        'success': True,
                        'retry_count': retry_num
                    }
                    self.completed_cases[case_num] = result_data
                    self.failed_cases.discard(case_num)
                    return result_data
                    
                elif response.status == 429:  # Rate limit
                    # Exponential backoff for rate limits
                    wait_time = (2 ** retry_num) + random.uniform(0, 1)
                    await asyncio.sleep(wait_time)
                    self.failed_cases.add(case_num)
                    return {'case_number': case_num, 'error': 'rate_limit', 'success': False}
                else:
                    self.failed_cases.add(case_num)
                    return {'case_number': case_num, 'error': f'http_{response.status}', 'success': False}
                    
        except asyncio.TimeoutError:
            self.failed_cases.add(case_num)
            return {'case_number': case_num, 'error': 'timeout', 'success': False, 'timeout': timeout}
        except Exception as e:
            self.failed_cases.add(case_num)
            return {'case_number': case_num, 'error': str(e)[:50], 'success': False}
    
    async def test_approach_with_retries(self, approach_name: str, 
                                       test_cases: List[Dict],
                                       max_retries: int = 3) -> Dict:
        """Test approach with smart retry logic"""
        logger.info(f"\n{'='*70}")
        logger.info(f"SMART RETRY TEST: {approach_name}")
        logger.info(f"Total cases: {len(test_cases)}")
        logger.info(f"{'='*70}")
        
        # Load previous results
        self.load_previous_results(approach_name)
        
        prompt = self.approaches[approach_name]
        start_time = time.time()
        
        # Identify cases that need testing
        cases_to_test = []
        for case in test_cases:
            if case['case_number'] not in self.completed_cases and \
               case['case_number'] not in self.permanent_failures:
                cases_to_test.append(case)
        
        logger.info(f"Already completed: {len(self.completed_cases)}")
        logger.info(f"Need to test: {len(cases_to_test)}")
        logger.info(f"Permanent failures: {len(self.permanent_failures)}")
        
        # Test in batches with retries
        retry_count = 0
        while cases_to_test and retry_count < max_retries:
            logger.info(f"\nRetry round {retry_count + 1}/{max_retries}")
            logger.info(f"Testing {len(cases_to_test)} cases...")
            
            # Adaptive batch size based on success rate
            if retry_count == 0:
                batch_size = 10
            elif retry_count == 1:
                batch_size = 5
            else:
                batch_size = 3
            
            # Create session with appropriate settings
            connector = aiohttp.TCPConnector(
                limit=20,
                ttl_dns_cache=300,
                force_close=True
            )
            
            async with aiohttp.ClientSession(connector=connector) as session:
                for i in range(0, len(cases_to_test), batch_size):
                    batch = cases_to_test[i:i+batch_size]
                    
                    # Process batch
                    tasks = []
                    for case in batch:
                        task = self.analyze_with_smart_retry(
                            session, case, prompt, retry_count
                        )
                        tasks.append(task)
                    
                    results = await asyncio.gather(*tasks, return_exceptions=True)
                    
                    # Process results
                    for result in results:
                        if isinstance(result, Exception):
                            logger.error(f"Exception: {result}")
                    
                    # Progress update
                    total_completed = len(self.completed_cases)
                    total_failed = len(self.failed_cases)
                    
                    if total_completed % 50 == 0 or (i + batch_size >= len(cases_to_test)):
                        # Calculate metrics
                        if self.completed_cases:
                            successful_results = list(self.completed_cases.values())
                            tp = sum(1 for r in successful_results if r['actual_fp'] and r['predicted_fp'])
                            fp_total = sum(1 for r in successful_results if r['actual_fp'])
                            fp_rate = (tp / fp_total * 100) if fp_total > 0 else 0
                        else:
                            fp_rate = 0
                        
                        logger.info(f"Progress: {total_completed}/{len(test_cases)} completed | "
                                   f"Failed: {total_failed} | "
                                   f"FP Rate: {fp_rate:.1f}%")
                        
                        # Save progress
                        self.save_progress(approach_name)
                    
                    # Small delay between batches
                    await asyncio.sleep(0.5)
            
            # Prepare for next retry - only retry failed cases
            new_cases_to_test = []
            for case in test_cases:
                if case['case_number'] in self.failed_cases:
                    new_cases_to_test.append(case)
            
            # If no progress made, increase wait time
            if len(new_cases_to_test) == len(cases_to_test):
                logger.info(f"No progress made, waiting before retry...")
                await asyncio.sleep(10 * (retry_count + 1))
            
            cases_to_test = new_cases_to_test
            retry_count += 1
            
            # Mark permanent failures after max retries
            if retry_count >= max_retries:
                for case in cases_to_test:
                    self.permanent_failures.add(case['case_number'])
        
        # Calculate final metrics
        duration = time.time() - start_time
        return self.calculate_final_metrics(approach_name, len(test_cases), duration)
    
    def calculate_final_metrics(self, approach_name: str, total_cases: int, duration: float) -> Dict:
        """Calculate comprehensive metrics"""
        successful_results = list(self.completed_cases.values())
        
        if not successful_results:
            return {
                'approach': approach_name,
                'total_cases': total_cases,
                'completed': 0,
                'failed': len(self.failed_cases),
                'permanent_failures': len(self.permanent_failures),
                'duration': duration,
                'error': 'No successful results'
            }
        
        # Calculate metrics
        tp = sum(1 for r in successful_results if r['actual_fp'] and r['predicted_fp'])
        tn = sum(1 for r in successful_results if not r['actual_fp'] and not r['predicted_fp'])
        fp = sum(1 for r in successful_results if not r['actual_fp'] and r['predicted_fp'])
        fn = sum(1 for r in successful_results if r['actual_fp'] and not r['predicted_fp'])
        
        fp_total = sum(1 for r in successful_results if r['actual_fp'])
        valid_total = sum(1 for r in successful_results if not r['actual_fp'])
        
        # Retry statistics
        retry_stats = {}
        for r in successful_results:
            retry_count = r.get('retry_count', 0)
            retry_stats[retry_count] = retry_stats.get(retry_count, 0) + 1
        
        return {
            'approach': approach_name,
            'total_cases': total_cases,
            'completed': len(successful_results),
            'failed': len(self.failed_cases),
            'permanent_failures': len(self.permanent_failures),
            'completion_rate': len(successful_results) / total_cases * 100,
            'duration_seconds': duration,
            
            'fp_detection_rate': (tp / fp_total * 100) if fp_total > 0 else 0,
            'valid_protection_rate': (tn / valid_total * 100) if valid_total > 0 else 100,
            'overall_accuracy': ((tp + tn) / len(successful_results) * 100),
            
            'confusion_matrix': {
                'tp': tp, 'tn': tn, 'fp': fp, 'fn': fn
            },
            
            'retry_statistics': retry_stats
        }


async def main():
    """Run smart retry test on all 1250 cases"""
    logger.info("\n" + "="*80)
    logger.info("SMART RETRY SYSTEM - COMPLETE ALL 1250 CASES")
    logger.info("="*80)
    
    # Load dataset
    with open('valo_batch_round3_complete.json', 'r') as f:
        data = json.load(f)
    
    test_cases = []
    for case in data['results']:
        test_cases.append({
            'case_number': case['case_number'],
            'cropped_image': case['cropped_image'],
            'is_false_positive': case['is_false_positive']
        })
    
    logger.info(f"\nDataset: {len(test_cases)} total cases")
    
    # Test each approach
    tester = SmartRetryTester()
    final_results = {}
    
    for approach_name in tester.approaches.keys():
        try:
            result = await tester.test_approach_with_retries(approach_name, test_cases, max_retries=3)
            final_results[approach_name] = result
            
            logger.info(f"\n{approach_name} FINAL RESULTS:")
            logger.info(f"  Completed: {result['completed']}/{result['total_cases']} "
                       f"({result.get('completion_rate', 0):.1f}%)")
            logger.info(f"  FP Detection: {result.get('fp_detection_rate', 0):.1f}%")
            logger.info(f"  Valid Protection: {result.get('valid_protection_rate', 0):.1f}%")
            logger.info(f"  Permanent failures: {result.get('permanent_failures', 0)}")
            
            if 'retry_statistics' in result:
                logger.info(f"  Retry stats: {result['retry_statistics']}")
            
        except Exception as e:
            logger.error(f"Failed to test {approach_name}: {e}")
            final_results[approach_name] = {'error': str(e)}
        
        await asyncio.sleep(10)  # Pause between approaches
    
    # Save final comprehensive report
    with open('FINAL_SMART_RETRY_ALL_1250_RESULTS.json', 'w') as f:
        json.dump({
            'test_date': datetime.now().isoformat(),
            'test_type': 'smart_retry_complete',
            'dataset_size': len(test_cases),
            'results': final_results
        }, f, indent=2)
    
    # Display final summary
    logger.info("\n" + "="*80)
    logger.info("FINAL SUMMARY - ALL 1250 CASES")
    logger.info("="*80)
    
    for name, result in final_results.items():
        if 'completed' in result:
            logger.info(f"\n{name}:")
            logger.info(f"  Completion: {result['completed']}/{result['total_cases']} "
                       f"({result.get('completion_rate', 0):.1f}%)")
            logger.info(f"  FP Detection: {result.get('fp_detection_rate', 0):.1f}%")
            logger.info(f"  Valid Protection: {result.get('valid_protection_rate', 0):.1f}%")
    
    logger.info("\n" + "="*80)
    logger.info("SMART RETRY COMPLETE")
    logger.info("="*80)


if __name__ == "__main__":
    asyncio.run(main())