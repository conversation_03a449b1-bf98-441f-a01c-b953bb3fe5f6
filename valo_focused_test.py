#!/usr/bin/env python3
"""
VALO AI-FARM Focused Auto-Learning Test
Uses actual image-CSV mappings for real testing
"""

import asyncio
import pandas as pd
import json
import sys
import os
from pathlib import Path
from typing import List, Dict, Any
from datetime import datetime
import httpx
import time

# Add backend to Python path
sys.path.append('/home/<USER>/VALO_AI-FARM_2025/backend')

async def test_vlm_endpoint():
    """Test VLM endpoint connectivity"""
    print("🔌 Testing VLM endpoint connectivity...")
    
    # Test primary endpoint (100.x)
    try:
        async with httpx.AsyncClient(timeout=10.0) as client:
            response = await client.get("http://100.106.127.35:9500/v1/models")
            if response.status_code == 200:
                models = response.json()
                print(f"✅ Primary VLM endpoint connected: {len(models.get('data', []))} models available")
                return "http://100.106.127.35:9500/v1", "VLM-38B-AWQ"
    except Exception as e:
        print(f"❌ Primary endpoint failed: {e}")
    
    # Test Friendli fallback
    try:
        async with httpx.AsyncClient(timeout=10.0) as client:
            response = await client.post(
                "https://api.friendli.ai/dedicated/v1/chat/completions",
                headers={
                    "Content-Type": "application/json",
                    "Authorization": "Bearer flp_Xf21SJG60333K9KZ2qPOs5Uh0CMwltZ5c4xEefBO2pRe2"
                },
                json={
                    "model": "nhyws8db6r6t",
                    "messages": [{"role": "user", "content": "test"}],
                    "max_tokens": 5
                }
            )
            if response.status_code == 200:
                print("✅ Friendli endpoint connected")
                return "https://api.friendli.ai/dedicated/v1", "nhyws8db6r6t"
    except Exception as e:
        print(f"❌ Friendli endpoint failed: {e}")
    
    raise Exception("No VLM endpoints available")

async def analyze_image_direct(image_path: str, case_number: str, prompt: str) -> Dict:
    """Direct VLM analysis without backend service"""
    print(f"🔍 Analyzing {case_number} with VLM...")
    
    # Read and encode image
    with open(image_path, 'rb') as f:
        image_data = f.read()
    
    import base64
    base64_image = base64.b64encode(image_data).decode('utf-8')
    
    # Test primary endpoint first
    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.post(
                "http://100.106.127.35:9500/v1/chat/completions",
                headers={"Content-Type": "application/json"},
                json={
                    "model": "VLM-38B-AWQ",
                    "messages": [
                        {
                            "role": "user",
                            "content": [
                                {"type": "text", "text": prompt},
                                {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{base64_image}"}}
                            ]
                        }
                    ],
                    "max_tokens": 1000,
                    "temperature": 0.1
                }
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result['choices'][0]['message']['content']
                
                # Parse VLM response
                return parse_vlm_response(content, case_number)
            else:
                print(f"❌ Primary VLM failed: {response.status_code}")
                
    except Exception as e:
        print(f"❌ Primary VLM error: {e}")
    
    # Fallback to Friendli
    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.post(
                "https://api.friendli.ai/dedicated/v1/chat/completions",
                headers={
                    "Content-Type": "application/json",
                    "Authorization": "Bearer flp_Xf21SJG60333K9KZ2qPOs5Uh0CMwltZ5c4xEefBO2pRe2"
                },
                json={
                    "model": "nhyws8db6r6t",
                    "messages": [
                        {
                            "role": "user",
                            "content": [
                                {"type": "text", "text": prompt},
                                {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{base64_image}"}}
                            ]
                        }
                    ],
                    "max_tokens": 1000,
                    "temperature": 0.1
                }
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result['choices'][0]['message']['content']
                
                # Parse VLM response
                return parse_vlm_response(content, case_number)
            else:
                print(f"❌ Friendli VLM failed: {response.status_code}")
                
    except Exception as e:
        print(f"❌ Friendli VLM error: {e}")
    
    raise Exception("All VLM endpoints failed")

def parse_vlm_response(content: str, case_number: str) -> Dict:
    """Parse VLM response into structured data"""
    
    # Extract confidence score
    confidence = 50  # default
    if "CONFIDENCE" in content:
        try:
            confidence_line = [line for line in content.split('\n') if 'CONFIDENCE' in line][0]
            confidence = int(''.join(filter(str.isdigit, confidence_line)))
        except:
            pass
    
    # Extract detection type
    detection_type = "unclear"
    if "DETECTION_TYPE" in content:
        try:
            detection_line = [line for line in content.split('\n') if 'DETECTION_TYPE' in line][0]
            if "personnel" in detection_line.lower():
                detection_type = "personnel"
            elif "equipment" in detection_line.lower():
                detection_type = "equipment"
            elif "structure" in detection_line.lower():
                detection_type = "structure"
        except:
            pass
    
    # Extract false positive determination
    is_false_positive = True  # default assumption
    if "IS_FALSE_POSITIVE" in content:
        try:
            fp_line = [line for line in content.split('\n') if 'IS_FALSE_POSITIVE' in line][0]
            is_false_positive = "true" in fp_line.lower()
        except:
            pass
    
    # Extract recommendation
    recommendation = "REQUIRES_REVIEW"
    if "RECOMMENDATION" in content:
        try:
            rec_line = [line for line in content.split('\n') if 'RECOMMENDATION' in line][0]
            if "DISMISS" in rec_line:
                recommendation = "DISMISS_ALERT"
            elif "CONFIRMED" in rec_line:
                recommendation = "CONFIRMED_VIOLATION"
        except:
            pass
    
    return {
        'case_number': case_number,
        'confidence_score': confidence,
        'detection_type': detection_type,
        'is_false_positive': is_false_positive,
        'recommendation': recommendation,
        'reasoning': content,
        'endpoint_used': 'primary' if 'VLM-38B-AWQ' in content else 'friendli'
    }

def find_matching_csv_cases(images_dir: str, csv_path: str) -> List[Dict]:
    """Find CSV cases that have corresponding images"""
    print("🔍 Finding CSV cases with matching images...")
    
    # Get all image case numbers
    image_files = []
    for subdir in ['invalid', 'valid']:
        subdir_path = os.path.join(images_dir, subdir)
        if os.path.exists(subdir_path):
            for file in os.listdir(subdir_path):
                if file.endswith('.JPEG'):
                    image_files.append(file)
    
    # Extract unique case numbers from images
    image_case_numbers = set()
    for file in image_files:
        case_number = file.split('_')[0]  # V1250623121_cropped_invalid.JPEG -> V1250623121
        image_case_numbers.add(case_number)
    
    print(f"📸 Found {len(image_case_numbers)} unique case numbers with images")
    
    # Load CSV and find matching cases
    df = pd.read_csv(csv_path)
    matching_cases = []
    
    for _, row in df.iterrows():
        case_number = row['Case Int. ID']
        if case_number in image_case_numbers:
            case = {
                'case_number': case_number,
                'camera_id': row['Camera'],
                'terminal': row['Terminal'],
                'alert_status': row['Alert Status'],
                'infringement_type': row['Type of Infringement'],
                'follow_up': row['Follow Up'],
                'remarks': row['Remarks'],
                'acknowledged_by': row['Acknowledged By'],
                'reviewed_by': row['Reviewed By']
            }
            matching_cases.append(case)
    
    print(f"✅ Found {len(matching_cases)} CSV cases with matching images")
    
    # Balance the sample
    invalid_cases = [c for c in matching_cases if c['alert_status'] == 'Invalid']
    valid_cases = [c for c in matching_cases if c['alert_status'] == 'Valid']
    
    print(f"   - Invalid (False Positive): {len(invalid_cases)}")
    print(f"   - Valid (True Violation): {len(valid_cases)}")
    
    # Take balanced sample
    sample_size = min(10, len(invalid_cases), len(valid_cases))
    balanced_sample = invalid_cases[:sample_size] + valid_cases[:sample_size]
    
    print(f"📊 Using balanced sample of {len(balanced_sample)} cases ({sample_size} invalid + {sample_size} valid)")
    
    return balanced_sample

def generate_valo_prompt(case: Dict) -> str:
    """Generate VALO-specific analysis prompt"""
    return f"""
VALO PORT SAFETY VIOLATION ANALYSIS

CASE CONTEXT:
- Case Number: {case['case_number']}
- Camera: {case['camera_id']}
- Terminal: {case['terminal']}
- Infringement Type: {case['infringement_type']}
- Historical Assessment: {case['alert_status']}
- Human Reviewer Notes: {case['remarks']}

ANALYSIS TASK:
Analyze this port safety image to determine if this is a genuine safety violation or a false positive.

SPECIFIC ANALYSIS FOR {case['infringement_type']}:
{'Focus on PPE compliance - check for hard hat, high-vis vest, safety boots. Watch for crane/equipment misidentified as people.' if 'PPE' in case['infringement_type'] else ''}
{'Assess container proximity and worker safety distance. Consider camera perspective distortion.' if 'Container Distance' in case['infringement_type'] else ''}
{'Count personnel in lashing area. Verify actual lashing activity vs inspection.' if 'Lashing' in case['infringement_type'] else ''}
{'Check if person is in actual exclusion zone vs visual perspective issue.' if 'Ex.Row' in case['infringement_type'] else ''}

KNOWN VALO FALSE POSITIVE PATTERNS:
Based on PSA historical data, common false positives include:
- Crane structures misidentified as personnel
- Equipment shadows creating false human shapes
- Workers in proper PPE flagged due to lighting/angles
- Vessel/port structures confused with people

REQUIRED OUTPUT FORMAT:
CONFIDENCE: [0-100 score]
DETECTION_TYPE: [personnel|equipment|structure|unclear]
IS_FALSE_POSITIVE: [true|false]
RECOMMENDATION: [DISMISS_ALERT|REQUIRES_REVIEW|CONFIRMED_VIOLATION]
REASONING: [Detailed explanation of analysis]

Focus on accuracy to help reduce the 97% false positive rate plaguing PSA operations.
"""

async def run_focused_auto_learning():
    """Run focused auto-learning test with real data"""
    print("🚀 VALO AI-FARM FOCUSED AUTO-LEARNING TEST")
    print("=" * 60)
    
    csv_path = "/home/<USER>/VALO_AI-FARM_2025/ai_farm_images_fixed_250703/psa_valo_violation_report_without_images_csv_2025-07-03_14-14-01.CSV"
    images_dir = "/home/<USER>/VALO_AI-FARM_2025/ai_farm_images_fixed_250703/ai_farm_images_fixed"
    
    # Test VLM connectivity
    await test_vlm_endpoint()
    
    # Find matching cases
    cases = find_matching_csv_cases(images_dir, csv_path)
    
    # Process cases
    results = []
    
    for i, case in enumerate(cases, 1):
        print(f"\n[{i}/{len(cases)}] Processing case {case['case_number']}")
        
        # Find images
        status_dir = "invalid" if case['alert_status'] == "Invalid" else "valid"
        status_suffix = "invalid" if case['alert_status'] == "Invalid" else "valid"
        
        # Use cropped image preferentially
        cropped_image = f"{images_dir}/{status_dir}/{case['case_number']}_cropped_{status_suffix}.JPEG"
        source_image = f"{images_dir}/{status_dir}/{case['case_number']}_source_{status_suffix}.JPEG"
        
        image_path = None
        image_type = None
        
        if os.path.exists(cropped_image):
            image_path = cropped_image
            image_type = "cropped"
        elif os.path.exists(source_image):
            image_path = source_image
            image_type = "source"
        
        if not image_path:
            print(f"❌ No images found for {case['case_number']}")
            continue
        
        print(f"📸 Using {image_type} image: {os.path.basename(image_path)}")
        
        # Generate prompt
        prompt = generate_valo_prompt(case)
        
        try:
            # Analyze with VLM
            start_time = time.time()
            vlm_result = await analyze_image_direct(image_path, case['case_number'], prompt)
            processing_time = int((time.time() - start_time) * 1000)
            
            # Create comprehensive result
            result = {
                'case_number': case['case_number'],
                'camera_id': case['camera_id'],
                'terminal': case['terminal'],
                'infringement_type': case['infringement_type'],
                'ground_truth_status': case['alert_status'],
                'human_remarks': case['remarks'],
                'image_type_used': image_type,
                'vlm_confidence': vlm_result['confidence_score'],
                'vlm_detection_type': vlm_result['detection_type'],
                'vlm_is_false_positive': vlm_result['is_false_positive'],
                'vlm_recommendation': vlm_result['recommendation'],
                'vlm_reasoning': vlm_result['reasoning'],
                'processing_time_ms': processing_time,
                'endpoint_used': vlm_result.get('endpoint_used', 'unknown')
            }
            
            # Calculate accuracy
            ground_truth_fp = case['alert_status'] == 'Invalid'
            vlm_fp = vlm_result['is_false_positive']
            accuracy = "CORRECT" if ground_truth_fp == vlm_fp else "INCORRECT"
            result['accuracy'] = accuracy
            
            results.append(result)
            
            print(f"✅ Confidence: {vlm_result['confidence_score']}%, "
                  f"FP: {vlm_result['is_false_positive']}, "
                  f"Accuracy: {accuracy}")
            
            # Small delay to avoid overwhelming the API
            await asyncio.sleep(1)
            
        except Exception as e:
            print(f"❌ Analysis failed: {e}")
            continue
    
    # Analyze results
    print(f"\n📊 ANALYSIS COMPLETE - {len(results)} successful analyses")
    
    if results:
        # Calculate overall accuracy
        correct = len([r for r in results if r['accuracy'] == 'CORRECT'])
        accuracy_rate = (correct / len(results)) * 100
        
        # Analyze patterns
        fp_causes = {}
        camera_performance = {}
        terminal_performance = {}
        
        for result in results:
            # False positive cause analysis
            if result['ground_truth_status'] == 'Invalid':
                remarks = result['human_remarks'].upper()
                if 'CRANE STRUCTURE' in remarks:
                    fp_causes['crane_misidentification'] = fp_causes.get('crane_misidentification', 0) + 1
                elif 'VESSEL STRUCTURE' in remarks:
                    fp_causes['vessel_misidentification'] = fp_causes.get('vessel_misidentification', 0) + 1
                elif 'FULL PPE' in remarks:
                    fp_causes['ppe_misclassification'] = fp_causes.get('ppe_misclassification', 0) + 1
            
            # Camera performance
            camera = result['camera_id']
            if camera not in camera_performance:
                camera_performance[camera] = {'total': 0, 'correct': 0}
            camera_performance[camera]['total'] += 1
            if result['accuracy'] == 'CORRECT':
                camera_performance[camera]['correct'] += 1
        
        # Save results
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_file = f"/home/<USER>/VALO_AI-FARM_2025/valo_focused_results_{timestamp}.json"
        
        output = {
            'test_info': {
                'timestamp': timestamp,
                'total_cases': len(results),
                'overall_accuracy': accuracy_rate,
                'csv_source': csv_path,
                'images_source': images_dir
            },
            'results': results,
            'patterns': {
                'false_positive_causes': fp_causes,
                'camera_performance': camera_performance,
                'terminal_performance': terminal_performance
            }
        }
        
        with open(results_file, 'w') as f:
            json.dump(output, f, indent=2)
        
        # Print summary
        print("\n" + "=" * 60)
        print("🎯 VALO AUTO-LEARNING RESULTS SUMMARY")
        print("=" * 60)
        print(f"📊 OVERALL PERFORMANCE:")
        print(f"   Total Cases Analyzed: {len(results)}")
        print(f"   Overall Accuracy: {accuracy_rate:.1f}%")
        print(f"   Correct Predictions: {correct}/{len(results)}")
        
        print(f"\n🔍 FALSE POSITIVE PATTERN DETECTION:")
        if fp_causes:
            for cause, count in fp_causes.items():
                print(f"   {cause.replace('_', ' ').title()}: {count} cases detected")
        else:
            print("   No specific patterns detected in this sample")
        
        print(f"\n📷 CAMERA PERFORMANCE:")
        for camera, stats in camera_performance.items():
            acc = (stats['correct'] / stats['total'] * 100) if stats['total'] > 0 else 0
            print(f"   {camera}: {acc:.1f}% accuracy ({stats['correct']}/{stats['total']})")
        
        print(f"\n💾 Results saved to: {results_file}")
        print(f"\n✅ VALO auto-learning test completed successfully!")
        
        return results
    
    else:
        print("❌ No successful analyses completed")
        return []

async def main():
    try:
        results = await run_focused_auto_learning()
        if results:
            print(f"\n🎉 Test completed with {len(results)} successful analyses!")
        else:
            print("\n❌ Test completed but no analyses succeeded")
    except KeyboardInterrupt:
        print("\n❌ Test interrupted by user")
    except Exception as e:
        print(f"\n💥 Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())