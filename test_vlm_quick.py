#!/usr/bin/env python3
"""
Quick VLM connectivity test
"""

import requests
import json

url = "http://100.106.127.35:9500/v1/chat/completions"

# Simple test payload
payload = {
    "model": "VLM-38B-AWQ",
    "messages": [{
        "role": "user",
        "content": [{
            "type": "text",
            "text": "Test: What is 2+2?"
        }]
    }],
    "temperature": 0.1,
    "max_tokens": 50
}

print("Testing VLM endpoint...")
print(f"URL: {url}")

try:
    response = requests.post(url, json=payload, timeout=10)
    print(f"Status: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        print("✓ VLM is responding correctly")
        print(f"Response: {result['choices'][0]['message']['content']}")
    else:
        print(f"✗ Error: {response.text}")
except Exception as e:
    print(f"✗ Connection error: {e}")