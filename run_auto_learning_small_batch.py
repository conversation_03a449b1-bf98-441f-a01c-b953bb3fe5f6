#!/usr/bin/env python3
"""
VALO Auto-Learning Test - Small Batch Version
Tests just 50 cases per round for quick results
"""

import json
import base64
import requests
import os
from datetime import datetime
import time

class SmallBatchAutoLearning:
    def __init__(self):
        self.vlm_url = "http://100.106.127.35:9500/v1/chat/completions"
        self.vlm_model = "VLM-38B-AWQ"
        self.session = requests.Session()
        
        # Test configuration
        self.cases_per_round = 50  # Small batch for testing
        self.max_rounds = 3  # Just 3 rounds
        
        # Performance targets
        self.targets = {
            'valid_protection': 98.0,
            'fp_detection': 75.0,
        }
        
        # Dynamic confidence thresholds
        self.thresholds = {
            'structure': 90,
            'person': 50,
            'ppe_compliant': 70,
            'behavioral': 60
        }
        
        # Results tracking
        self.all_results = []
        
    def encode_image(self, image_path):
        """Encode image to base64"""
        try:
            if os.path.exists(image_path):
                with open(image_path, 'rb') as f:
                    return base64.b64encode(f.read()).decode('utf-8')
        except:
            pass
        return None
    
    def create_adaptive_prompt(self, round_num):
        """Create prompt with adaptive thresholds"""
        
        # Load base prompt
        with open('FINAL_PRODUCTION_PROMPT.txt', 'r') as f:
            base_prompt = f.read()
        
        # Add threshold info
        threshold_info = f"""
Current Thresholds (Round {round_num}):
- Structure confidence required: {self.thresholds['structure']}%
- Person confidence required: {self.thresholds['person']}%
- PPE compliance confidence: {self.thresholds['ppe_compliant']}%
- Behavioral violation confidence: {self.thresholds['behavioral']}%
"""
        
        # Insert after instructions
        prompt = base_prompt.replace(
            "INSTRUCTIONS: Analyze BOTH provided images",
            f"INSTRUCTIONS: Analyze BOTH provided images\n{threshold_info}"
        )
        
        return prompt
    
    def test_single_case(self, case, prompt):
        """Test a single case"""
        
        print(f"  Testing {case['case_number']}...", end='', flush=True)
        
        # Encode images
        source_b64 = self.encode_image(case['source_image'])
        cropped_b64 = self.encode_image(case['cropped_image'])
        
        if not source_b64 or not cropped_b64:
            print(" [SKIP - No images]")
            return None
        
        payload = {
            "model": self.vlm_model,
            "messages": [{
                "role": "user",
                "content": [
                    {"type": "text", "text": prompt},
                    {"type": "text", "text": "\n\nSOURCE IMAGE:"},
                    {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{source_b64}"}},
                    {"type": "text", "text": "\n\nCROPPED IMAGE:"},
                    {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{cropped_b64}"}}
                ]
            }],
            "temperature": 0.1,
            "max_tokens": 250
        }
        
        try:
            response = self.session.post(self.vlm_url, json=payload, timeout=15)
            if response.status_code == 200:
                vlm_response = response.json()['choices'][0]['message']['content']
                
                # Parse response
                predicted_fp = 'YES' in vlm_response.split('FALSE POSITIVE:')[1][:5]
                
                result = {
                    'case_number': case['case_number'],
                    'actual_fp': case['is_false_positive'],
                    'predicted_fp': predicted_fp,
                    'correct': predicted_fp == case['is_false_positive'],
                    'response': vlm_response
                }
                
                print(f" [{'✓' if result['correct'] else '✗'}]")
                return result
        except Exception as e:
            print(f" [ERROR: {str(e)[:30]}]")
        
        return None
    
    def run_round(self, round_num, test_cases):
        """Run a single round"""
        
        print(f"\n{'='*60}")
        print(f"ROUND {round_num}")
        print(f"{'='*60}")
        
        # Create prompt
        prompt = self.create_adaptive_prompt(round_num)
        
        print(f"Testing {len(test_cases)} cases with thresholds:")
        for k, v in self.thresholds.items():
            print(f"  {k}: {v}%")
        
        results = []
        
        for case in test_cases:
            result = self.test_single_case(case, prompt)
            if result:
                results.append(result)
            time.sleep(0.5)  # Rate limit
        
        # Calculate metrics
        if results:
            metrics = self.calculate_metrics(results)
            
            print(f"\nRound {round_num} Results:")
            print(f"├─ Accuracy: {metrics['accuracy']:.1f}%")
            print(f"├─ FP Detection: {metrics['fp_detection']:.1f}%")
            print(f"└─ Valid Protection: {metrics['valid_protection']:.1f}%")
            
            # Adjust thresholds
            self.adjust_thresholds(results, metrics)
            
            # Save results
            round_data = {
                'round': round_num,
                'metrics': metrics,
                'thresholds': self.thresholds.copy(),
                'results': results
            }
            
            self.all_results.append(round_data)
            
            return metrics
        
        return None
    
    def calculate_metrics(self, results):
        """Calculate performance metrics"""
        
        total = len(results)
        correct = sum(r['correct'] for r in results)
        
        # FP metrics
        actual_fps = [r for r in results if r['actual_fp']]
        fp_detected = sum(r['predicted_fp'] for r in actual_fps) if actual_fps else 0
        
        # Valid metrics
        actual_valid = [r for r in results if not r['actual_fp']]
        valid_protected = sum(not r['predicted_fp'] for r in actual_valid) if actual_valid else 0
        
        return {
            'accuracy': correct / total * 100 if total > 0 else 0,
            'fp_detection': fp_detected / len(actual_fps) * 100 if actual_fps else 0,
            'valid_protection': valid_protected / len(actual_valid) * 100 if actual_valid else 100,
            'total_cases': total,
            'fp_cases': len(actual_fps),
            'valid_cases': len(actual_valid)
        }
    
    def adjust_thresholds(self, results, metrics):
        """Adjust thresholds based on results"""
        
        # Count errors
        false_negatives = sum(1 for r in results if not r['actual_fp'] and r['predicted_fp'])
        false_positives = sum(1 for r in results if r['actual_fp'] and not r['predicted_fp'])
        
        print(f"\nErrors: {false_negatives} valid missed, {false_positives} FPs not caught")
        
        # Priority: Protect valid violations
        if false_negatives > 1:
            self.thresholds['structure'] = min(95, self.thresholds['structure'] + 3)
            self.thresholds['behavioral'] = max(50, self.thresholds['behavioral'] - 5)
            print(f"Adjusting for safety: structure→{self.thresholds['structure']}%, behavioral→{self.thresholds['behavioral']}%")
        
        # Secondary: Improve FP detection
        elif false_positives > 5 and false_negatives == 0:
            self.thresholds['structure'] = max(85, self.thresholds['structure'] - 2)
            print(f"Adjusting for FP detection: structure→{self.thresholds['structure']}%")
    
    def run(self):
        """Run the auto-learning test"""
        
        print("VALO AUTO-LEARNING TEST - SMALL BATCH")
        print("="*60)
        print(f"Testing {self.cases_per_round} cases per round, max {self.max_rounds} rounds")
        print(f"Targets: Valid Protection ≥{self.targets['valid_protection']}%, FP Detection ≥{self.targets['fp_detection']}%")
        
        # Load data
        print("\nLoading data...")
        with open('valo_batch_round3_complete.json', 'r') as f:
            all_cases = json.load(f)['results']
        
        print(f"Total cases available: {len(all_cases)}")
        
        # Run rounds
        for round_num in range(1, self.max_rounds + 1):
            # Sample cases for this round
            import random
            test_cases = random.sample(all_cases, min(self.cases_per_round, len(all_cases)))
            
            metrics = self.run_round(round_num, test_cases)
            
            if metrics and metrics['valid_protection'] >= self.targets['valid_protection'] and \
               metrics['fp_detection'] >= self.targets['fp_detection']:
                print(f"\n✅ TARGETS ACHIEVED IN ROUND {round_num}!")
                break
        
        # Save final results
        final_report = {
            'timestamp': datetime.now().isoformat(),
            'rounds': len(self.all_results),
            'final_thresholds': self.thresholds,
            'all_rounds': self.all_results
        }
        
        with open('auto_learning_small_batch_results.json', 'w') as f:
            json.dump(final_report, f, indent=2)
        
        print(f"\nResults saved to: auto_learning_small_batch_results.json")

def main():
    learner = SmallBatchAutoLearning()
    learner.run()

if __name__ == "__main__":
    main()