{"8": {"round": 8, "name": "Multi-Factor Decision", "fp_detection_rate": 0, "valid_protection_rate": 100, "total_cases": 0, "errors": 1250, "confusion_matrix": {"tp": 0, "tn": 0, "fp": 0, "fn": 0}, "timestamp": "2025-07-23T08:24:22.719818"}, "9": {"round": 9, "name": "Aggressive Detection", "fp_detection_rate": 0, "valid_protection_rate": 100, "total_cases": 0, "errors": 1250, "confusion_matrix": {"tp": 0, "tn": 0, "fp": 0, "fn": 0}, "timestamp": "2025-07-23T08:24:22.761302"}, "10": {"round": 10, "name": "Combined Best", "fp_detection_rate": 0, "valid_protection_rate": 100, "total_cases": 0, "errors": 1250, "confusion_matrix": {"tp": 0, "tn": 0, "fp": 0, "fn": 0}, "timestamp": "2025-07-23T08:24:22.805616"}, "12": {"round": 12, "name": "Meta-Learning", "fp_detection_rate": 0, "valid_protection_rate": 100, "total_cases": 0, "errors": 1250, "confusion_matrix": {"tp": 0, "tn": 0, "fp": 0, "fn": 0}, "timestamp": "2025-07-23T08:24:22.857429"}, "13": {"round": 13, "name": "Active Learning", "fp_detection_rate": 0, "valid_protection_rate": 100, "total_cases": 0, "errors": 1250, "confusion_matrix": {"tp": 0, "tn": 0, "fp": 0, "fn": 0}, "timestamp": "2025-07-23T08:24:22.900913"}, "14": {"round": 14, "name": "Synthetic Augmentation", "fp_detection_rate": 0, "valid_protection_rate": 100, "total_cases": 0, "errors": 1250, "confusion_matrix": {"tp": 0, "tn": 0, "fp": 0, "fn": 0}, "timestamp": "2025-07-23T08:24:22.944551"}, "15": {"round": 15, "name": "Hierarchical Decision", "fp_detection_rate": 0, "valid_protection_rate": 100, "total_cases": 0, "errors": 1250, "confusion_matrix": {"tp": 0, "tn": 0, "fp": 0, "fn": 0}, "timestamp": "2025-07-23T08:24:22.987469"}, "16": {"round": 16, "name": "Parameter Sweep", "fp_detection_rate": 0, "valid_protection_rate": 100, "total_cases": 0, "errors": 1250, "confusion_matrix": {"tp": 0, "tn": 0, "fp": 0, "fn": 0}, "timestamp": "2025-07-23T08:24:23.029897"}, "17": {"round": 17, "name": "Transfer Learning", "fp_detection_rate": 0, "valid_protection_rate": 100, "total_cases": 0, "errors": 1250, "confusion_matrix": {"tp": 0, "tn": 0, "fp": 0, "fn": 0}, "timestamp": "2025-07-23T08:24:23.072674"}, "18": {"round": 18, "name": "Anomaly Detection", "fp_detection_rate": 0, "valid_protection_rate": 100, "total_cases": 0, "errors": 1250, "confusion_matrix": {"tp": 0, "tn": 0, "fp": 0, "fn": 0}, "timestamp": "2025-07-23T08:24:23.116105"}, "19": {"round": 19, "name": "Reinforcement Learning", "fp_detection_rate": 0, "valid_protection_rate": 100, "total_cases": 0, "errors": 1250, "confusion_matrix": {"tp": 0, "tn": 0, "fp": 0, "fn": 0}, "timestamp": "2025-07-23T08:24:23.162971"}, "20": {"round": 20, "name": "Neural Architecture Search", "fp_detection_rate": 0, "valid_protection_rate": 100, "total_cases": 0, "errors": 1250, "confusion_matrix": {"tp": 0, "tn": 0, "fp": 0, "fn": 0}, "timestamp": "2025-07-23T08:24:23.209334"}, "21": {"round": 21, "name": "Confidence Calibration", "fp_detection_rate": 0, "valid_protection_rate": 100, "total_cases": 0, "errors": 1250, "confusion_matrix": {"tp": 0, "tn": 0, "fp": 0, "fn": 0}, "timestamp": "2025-07-23T08:24:23.253667"}, "22": {"round": 22, "name": "<PERSON><PERSON><PERSON>", "fp_detection_rate": 0, "valid_protection_rate": 100, "total_cases": 0, "errors": 1250, "confusion_matrix": {"tp": 0, "tn": 0, "fp": 0, "fn": 0}, "timestamp": "2025-07-23T08:24:23.298136"}, "23": {"round": 23, "name": "Final Ensemble", "fp_detection_rate": 0, "valid_protection_rate": 100, "total_cases": 0, "errors": 1250, "confusion_matrix": {"tp": 0, "tn": 0, "fp": 0, "fn": 0}, "timestamp": "2025-07-23T08:24:23.356757"}, "24": {"round": 24, "name": "Safety Verification", "fp_detection_rate": 0, "valid_protection_rate": 100, "total_cases": 0, "errors": 1250, "confusion_matrix": {"tp": 0, "tn": 0, "fp": 0, "fn": 0}, "timestamp": "2025-07-23T08:24:23.401506"}, "25": {"round": 25, "name": "Production Ready", "fp_detection_rate": 0, "valid_protection_rate": 100, "total_cases": 0, "errors": 1250, "confusion_matrix": {"tp": 0, "tn": 0, "fp": 0, "fn": 0}, "timestamp": "2025-07-23T08:24:23.444493"}}