#!/usr/bin/env python3
"""
Specialized Production Approaches - Targeting 70% without remarks
These approaches are specifically designed for production constraints
"""
import asyncio
import json
import aiohttp
import logging
from datetime import datetime
import base64
import os
from typing import Dict, List, Optional

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SpecializedProductionApproaches:
    def __init__(self):
        self.vlm_endpoint = "http://**************:9500/v1/chat/completions"
        self.model = "VLM-38B-AWQ"
        
    def get_specialized_approaches(self) -> Dict[str, str]:
        """Production-optimized approaches"""
        
        approaches = {
            'binary_decision_tree': """PPE VIOLATION QUICK CHECK

Step 1: Is there a person in the image?
→ NO: This is a FALSE POSITIVE (stop here)
→ YES: Continue to Step 2

Step 2: Can you see safety equipment on them?
→ YES: This is a FALSE POSITIVE (stop here)
→ NO: This is NOT a false positive

Answer: Is this a FALSE POSITIVE? YES/NO""",

            'assumption_based': """SAFETY ALERT ANALYSIS

DEFAULT ASSUMPTION: This is a false positive (97% probability)

To override this assumption, you need CLEAR evidence:
- Person clearly visible AND
- No helmet visible AND
- No safety vest visible

Without ALL three conditions clearly met, maintain default.
Is this a FALSE POSITIVE? YES/NO""",

            'industrial_context': """INDUSTRIAL SAFETY CHECK

This is an industrial/port environment where:
- All workers are required to wear PPE
- Most alerts are equipment misidentified as violations
- Workers know and follow safety rules

Given this context, is the alert likely false?
Is this a FALSE POSITIVE? YES/NO""",

            'rapid_scan': """10-SECOND RAPID SCAN

Quick checks (stop at first YES):
☐ No people visible? → FALSE POSITIVE
☐ See any safety gear? → FALSE POSITIVE
☐ Just equipment/structure? → FALSE POSITIVE
☐ Image unclear? → FALSE POSITIVE
☐ Not 100% sure? → FALSE POSITIVE

Is this a FALSE POSITIVE? YES/NO""",

            'defensive_safety': """DEFENSIVE SAFETY APPROACH

Philosophy: It's better to review 10 false positives than miss 1 real violation.
BUT: 97% are false, so be realistic.

Only mark as real violation if you see:
- Clear human figure
- Definitely no PPE
- High confidence

Otherwise, it's a FALSE POSITIVE.
Is this a FALSE POSITIVE? YES/NO""",

            'ppe_detector_mode': """PPE DETECTOR ACTIVATED

Your sole purpose: Detect PPE
- Helmet shape? → Person has PPE → FALSE POSITIVE
- Vest/high-vis clothing? → Person has PPE → FALSE POSITIVE
- Hard to tell? → Assume PPE present → FALSE POSITIVE

Is this a FALSE POSITIVE? YES/NO""",

            'worksite_reality': """WORKSITE REALITY CHECK

Facts about worksites:
- Workers wear PPE to protect themselves
- Supervisors enforce PPE rules
- Workers without PPE get sent home

Is it likely someone is working without PPE?
Probably not → FALSE POSITIVE

Is this a FALSE POSITIVE? YES/NO""",

            'alert_fatigue_prevention': """ALERT FATIGUE PREVENTION MODE

Too many false alerts = ignored real violations
Help reduce false alerts by being practical.

Mark as FALSE POSITIVE unless you see:
- Obvious person
- Clearly missing PPE
- No doubt whatsoever

Is this a FALSE POSITIVE? YES/NO""",

            'machine_limitation_aware': """AI LIMITATION ACKNOWLEDGMENT

As an AI, I may mistake:
- Equipment for people
- Shadows for missing PPE
- Poor angles for violations

Given these limitations, only flag CERTAIN violations.
Everything else → FALSE POSITIVE

Is this a FALSE POSITIVE? YES/NO""",

            'production_optimized': """PRODUCTION SYSTEM OPTIMIZATION

Goal: Reduce reviewer workload by 70%
Method: Auto-dismiss obvious false positives

Dismissal criteria:
- No clear person → Dismiss
- Person with any safety gear → Dismiss
- Any uncertainty → Dismiss

Should this be auto-dismissed as FALSE POSITIVE? YES/NO"""
        }
        
        return approaches
    
    async def test_approach_batch(self, approach_name: str, prompt: str, 
                                 test_cases: List[Dict], batch_size: int = 10) -> Dict:
        """Test an approach on a batch of cases"""
        results = []
        errors = 0
        
        async with aiohttp.ClientSession() as session:
            for i in range(0, len(test_cases), batch_size):
                batch = test_cases[i:i+batch_size]
                tasks = []
                
                for case in batch:
                    task = self.analyze_single_case(session, case, prompt)
                    tasks.append(task)
                
                batch_results = await asyncio.gather(*tasks)
                
                for case, result in zip(batch, batch_results):
                    if result is not None:
                        results.append({
                            'case_number': case['case_number'],
                            'is_false_positive': case['is_false_positive'],
                            'predicted_fp': result,
                            'approach': approach_name
                        })
                    else:
                        errors += 1
                
                # Progress
                if len(results) % 50 == 0:
                    tp = sum(1 for r in results if r['is_false_positive'] and r['predicted_fp'])
                    fp_total = sum(1 for r in results if r['is_false_positive'])
                    rate = (tp / fp_total * 100) if fp_total > 0 else 0
                    logger.info(f"{approach_name}: {len(results)}/{len(test_cases)} | "
                               f"FP: {rate:.1f}% | Errors: {errors}")
                
                await asyncio.sleep(0.1)
        
        # Calculate metrics
        return self.calculate_metrics(results, approach_name, errors)
    
    async def analyze_single_case(self, session: aiohttp.ClientSession, 
                                 case: Dict, prompt: str) -> Optional[bool]:
        """Analyze a single case"""
        try:
            # Format prompt with case info
            formatted_prompt = prompt
            if '{infringement_type}' in prompt:
                formatted_prompt = prompt.format(infringement_type=case['infringement_type'])
            
            # Read image
            with open(case['cropped_image'], 'rb') as f:
                image_data = base64.b64encode(f.read()).decode('utf-8')
            
            payload = {
                "model": self.model,
                "messages": [{
                    "role": "user",
                    "content": [
                        {"type": "text", "text": formatted_prompt},
                        {"type": "image_url", 
                         "image_url": {"url": f"data:image/jpeg;base64,{image_data}"}}
                    ]
                }],
                "temperature": 0.1,
                "max_tokens": 50
            }
            
            async with session.post(self.vlm_endpoint, json=payload, timeout=30) as response:
                if response.status == 200:
                    result = await response.json()
                    content = result['choices'][0]['message']['content']
                    return "YES" in content.upper()[:30]
                return None
                
        except Exception as e:
            return None
    
    def calculate_metrics(self, results: List[Dict], approach_name: str, errors: int) -> Dict:
        """Calculate performance metrics"""
        tp = sum(1 for r in results if r['is_false_positive'] and r['predicted_fp'])
        tn = sum(1 for r in results if not r['is_false_positive'] and not r['predicted_fp'])
        fp = sum(1 for r in results if not r['is_false_positive'] and r['predicted_fp'])
        fn = sum(1 for r in results if r['is_false_positive'] and not r['predicted_fp'])
        
        fp_total = sum(1 for r in results if r['is_false_positive'])
        valid_total = sum(1 for r in results if not r['is_false_positive'])
        
        fp_rate = (tp / fp_total * 100) if fp_total > 0 else 0
        valid_rate = (tn / valid_total * 100) if valid_total > 0 else 100
        
        return {
            'approach': approach_name,
            'fp_detection_rate': fp_rate,
            'valid_protection_rate': valid_rate,
            'total_cases': len(results),
            'errors': errors,
            'confusion_matrix': {
                'tp': tp, 'tn': tn, 'fp': fp, 'fn': fn
            },
            'timestamp': datetime.now().isoformat()
        }

async def run_specialized_testing():
    """Main testing function"""
    logger.info("="*70)
    logger.info("SPECIALIZED PRODUCTION APPROACHES - NO REMARKS")
    logger.info("Target: 70% FP Detection in Production Environment")
    logger.info("="*70)
    
    # Load test data WITHOUT remarks
    with open('valo_batch_round3_complete.json', 'r') as f:
        data = json.load(f)
        test_cases = []
        
        for case in data['results'][:300]:  # Test on 300 cases
            test_cases.append({
                'case_number': case['case_number'],
                'cropped_image': case['cropped_image'],
                'infringement_type': case['infringement_type'],
                'is_false_positive': case['is_false_positive'],
                # NOT including remarks - production realistic
            })
    
    logger.info(f"Testing on {len(test_cases)} cases")
    logger.info(f"FP cases: {sum(1 for c in test_cases if c['is_false_positive'])}")
    logger.info(f"Valid cases: {sum(1 for c in test_cases if not c['is_false_positive'])}")
    
    # Test specialized approaches
    tester = SpecializedProductionApproaches()
    approaches = tester.get_specialized_approaches()
    
    all_results = []
    best_result = {'approach': None, 'fp_rate': 0}
    
    for i, (name, prompt) in enumerate(approaches.items(), 1):
        logger.info(f"\n{'='*50}")
        logger.info(f"Approach {i}/10: {name}")
        logger.info(f"{'='*50}")
        
        result = await tester.test_approach_batch(name, prompt, test_cases)
        all_results.append(result)
        
        logger.info(f"\nResults for {name}:")
        logger.info(f"  FP Detection: {result['fp_detection_rate']:.1f}%")
        logger.info(f"  Valid Protection: {result['valid_protection_rate']:.1f}%")
        
        # Track best
        if result['fp_detection_rate'] > best_result['fp_rate'] and \
           result['valid_protection_rate'] >= 85:
            best_result = {
                'approach': name,
                'fp_rate': result['fp_detection_rate'],
                'valid_rate': result['valid_protection_rate']
            }
            
            if result['fp_detection_rate'] >= 70:
                logger.info(f"  🎯 TARGET ACHIEVED: {result['fp_detection_rate']:.1f}%!")
        
        # Save progress
        with open('specialized_progress.json', 'w') as f:
            json.dump({
                'current_approach': i,
                'total_approaches': len(approaches),
                'best_result': best_result,
                'all_results': all_results,
                'timestamp': datetime.now().isoformat()
            }, f, indent=2)
        
        await asyncio.sleep(2)
    
    # Final report
    logger.info("\n" + "="*70)
    logger.info("SPECIALIZED APPROACHES - FINAL RESULTS")
    logger.info("="*70)
    
    # Sort by performance
    sorted_results = sorted(all_results, 
                           key=lambda x: x['fp_detection_rate'], 
                           reverse=True)
    
    logger.info("\nTop 5 Approaches:")
    for i, result in enumerate(sorted_results[:5], 1):
        status = "✅" if result['fp_detection_rate'] >= 70 else "❌"
        logger.info(f"{i}. {result['approach']}: {result['fp_detection_rate']:.1f}% "
                   f"(Valid: {result['valid_protection_rate']:.1f}%) {status}")
    
    # Save final report
    with open('specialized_final_report.json', 'w') as f:
        json.dump({
            'summary': {
                'target': 70,
                'best_result': best_result,
                'achieved': best_result['fp_rate'] >= 70
            },
            'all_results': sorted_results,
            'timestamp': datetime.now().isoformat()
        }, f, indent=2)
    
    if best_result['fp_rate'] >= 70:
        logger.info(f"\n✅ SUCCESS! Best approach '{best_result['approach']}' "
                   f"achieved {best_result['fp_rate']:.1f}%")
    else:
        logger.info(f"\n❌ Best result: {best_result['fp_rate']:.1f}% "
                   f"(short of 70% target)")

if __name__ == "__main__":
    asyncio.run(run_specialized_testing())