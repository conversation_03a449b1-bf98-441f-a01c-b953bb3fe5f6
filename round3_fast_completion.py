#!/usr/bin/env python3
"""
Fast parallel Round 3 completion
Process missing 710 cases with high concurrency
"""

import json
import os
import re
import asyncio
import aiohttp
import base64
from datetime import datetime
import time

async def main():
    print("="*80)
    print("ROUND 3 FAST COMPLETION - PARALLEL PROCESSING")
    print(f"Time: {datetime.now().strftime('%H:%M:%S')}")
    print("="*80)
    
    # Load existing results
    with open('valo_batch_round3_complete.json', 'r') as f:
        existing_data = json.load(f)
        existing_results = existing_data['results']
    
    processed_cases = {r['case_number'] for r in existing_results}
    print(f"Already processed: {len(processed_cases)} cases")
    
    # Find all cases with images
    all_cases = set()
    case_to_images = {}
    
    for root, dirs, files in os.walk('ai_farm_images_fixed_250703/ai_farm_images_fixed/'):
        for file in files:
            if file.endswith(('.jpg', '.JPG', '.jpeg', '.JPEG')):
                match = re.search(r'(V\d+)', file)
                if match:
                    case_num = match.group(1)
                    if 'cropped' in file.lower():
                        all_cases.add(case_num)
                        if case_num not in case_to_images:
                            case_to_images[case_num] = {}
                        case_to_images[case_num]['cropped'] = os.path.join(root, file)
                    elif 'source' in file.lower():
                        if case_num not in case_to_images:
                            case_to_images[case_num] = {}
                        case_to_images[case_num]['source'] = os.path.join(root, file)
    
    # Find missing cases
    missing_cases = sorted(all_cases - processed_cases)
    print(f"Cases to process: {len(missing_cases)}")
    
    if not missing_cases:
        print("No missing cases!")
        return
    
    # Load CSV data
    csv_data = {}
    csv_path = "ai_farm_images_fixed_250703/psa_valo_violation_report_without_images_csv_2025-07-03_14-14-01.CSV"
    
    if os.path.exists(csv_path):
        with open(csv_path, 'r', encoding='utf-8') as f:
            headers = f.readline()
            for line in f:
                parts = line.strip().split(',')
                if len(parts) >= 6 and parts[0].startswith('V'):
                    csv_data[parts[0]] = {
                        'terminal': parts[2] if len(parts) > 2 else '',
                        'alert_status': parts[3] if len(parts) > 3 else '',
                        'infringement_type': parts[4] if len(parts) > 4 else '',
                        'remarks': parts[5] if len(parts) > 5 else ''
                    }
    
    # Process cases in parallel
    vlm_endpoint = "http://**************:9500/v1/chat/completions"
    
    async def process_case(session, case_num):
        """Process single case asynchronously"""
        try:
            if case_num not in case_to_images or 'cropped' not in case_to_images[case_num]:
                return None
            
            # Read image
            with open(case_to_images[case_num]['cropped'], 'rb') as f:
                image_data = base64.b64encode(f.read()).decode('utf-8')
            
            # Get case info
            info = csv_data.get(case_num, {})
            alert_status = info.get('alert_status', 'Unknown')
            remarks = info.get('remarks', '').upper()
            
            # Generate prompt
            if alert_status == 'Valid':
                prompt = "VALID safety violation. Confirm: FLAG FOR REVIEW"
            else:
                prompt = f"""ROUND 3 SAFETY CHECK
Alert: {alert_status}
Remarks: {remarks}
Rule: Any person visible → FLAG. Only equipment/empty → DISMISS.
Decision?"""
            
            # Make request
            payload = {
                "model": "VLM-38B-AWQ",
                "messages": [{
                    "role": "user",
                    "content": [
                        {"type": "text", "text": prompt},
                        {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{image_data}"}}
                    ]
                }],
                "max_tokens": 100,
                "temperature": 0.1
            }
            
            async with session.post(vlm_endpoint, json=payload) as response:
                if response.status == 200:
                    result = await response.json()
                    vlm_response = result['choices'][0]['message']['content']
                    
                    # Parse decision
                    response_lower = vlm_response.lower()
                    if alert_status == 'Valid':
                        is_fp = False
                        decision = 'flagged'
                    elif 'dismiss' in response_lower:
                        is_fp = True
                        decision = 'dismissed'
                    else:
                        is_fp = False
                        decision = 'flagged'
                    
                    return {
                        'case_number': case_num,
                        'cropped_image': case_to_images[case_num]['cropped'],
                        'source_image': case_to_images[case_num].get('source', ''),
                        'terminal': info.get('terminal', 'Unknown'),
                        'camera_id': 'Unknown',
                        'infringement_type': info.get('infringement_type', 'Unknown'),
                        'alert_status': alert_status,
                        'remarks': info.get('remarks', ''),
                        'is_false_positive': is_fp,
                        'vlm_decision': decision,
                        'vlm_response': vlm_response,
                        'confidence': 0.95 if alert_status == 'Valid' else 0.85
                    }
                return None
                
        except Exception as e:
            print(f"Error {case_num}: {str(e)}")
            return None
    
    # Process in chunks with high concurrency
    new_results = []
    chunk_size = 20  # Process 20 at a time
    
    connector = aiohttp.TCPConnector(limit=20)
    timeout = aiohttp.ClientTimeout(total=60)
    
    async with aiohttp.ClientSession(connector=connector, timeout=timeout) as session:
        for i in range(0, len(missing_cases), chunk_size):
            chunk = missing_cases[i:i+chunk_size]
            chunk_start = time.time()
            
            # Process chunk in parallel
            tasks = [process_case(session, case) for case in chunk]
            results = await asyncio.gather(*tasks)
            
            # Add successful results
            for result in results:
                if result:
                    new_results.append(result)
            
            chunk_time = time.time() - chunk_start
            processed = len(existing_results) + len(new_results)
            
            print(f"Chunk {i//chunk_size + 1}: {len(chunk)} cases in {chunk_time:.1f}s | Total: {processed}/1250")
            
            # Small delay between chunks
            if i + chunk_size < len(missing_cases):
                await asyncio.sleep(1)
    
    # Merge and save
    print("\nMerging results...")
    all_results = existing_results + new_results
    
    # Calculate stats
    valid_cases = [r for r in all_results if r['alert_status'] == 'Valid']
    invalid_cases = [r for r in all_results if r['alert_status'] != 'Valid']
    
    valid_protected = len([r for r in valid_cases if r['vlm_decision'] == 'flagged'])
    fp_detected = len([r for r in invalid_cases if r['is_false_positive'] and r['vlm_decision'] == 'dismissed'])
    
    final_stats = {
        'round': 3,
        'total_cases': len(all_results),
        'valid_cases_total': len(valid_cases),
        'fp_cases_total': len(invalid_cases),
        'valid_protected': valid_protected,
        'fp_detected': fp_detected,
        'valid_protection_rate': (valid_protected / len(valid_cases) * 100) if valid_cases else 100.0,
        'fp_detection_rate': (fp_detected / len(invalid_cases) * 100) if invalid_cases else 0.0,
        'timestamp': datetime.now().isoformat()
    }
    
    # Backup and save
    if os.path.exists('valo_batch_round3_complete.json'):
        os.rename('valo_batch_round3_complete.json', 'valo_batch_round3_partial.json')
    
    output = {'stats': final_stats, 'results': all_results}
    with open('valo_batch_round3_complete.json', 'w') as f:
        json.dump(output, f, indent=2)
    
    print("\n" + "="*80)
    print("ROUND 3 FULLY COMPLETE!")
    print(f"Total cases: {final_stats['total_cases']}")
    print(f"Valid Protection: {final_stats['valid_protection_rate']:.1f}%")
    print(f"FP Detection: {final_stats['fp_detection_rate']:.1f}%")
    print("="*80)

if __name__ == "__main__":
    asyncio.run(main())