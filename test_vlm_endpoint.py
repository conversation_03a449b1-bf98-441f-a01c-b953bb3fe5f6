#!/usr/bin/env python3
"""
Test script to verify VLM API endpoint connectivity at **************
Tests the connection to http://**************:9500/v1/inference
"""

import asyncio
import aiohttp
import base64
import json
import sys
from pathlib import Path
from datetime import datetime


async def test_vlm_endpoint():
    """Test the VLM endpoint with actual VALO image"""
    endpoint = "http://**************:9500/v1/chat/completions"
    print(f"🔍 Testing VLM endpoint at {endpoint}")
    print("=" * 60)
    
    # Find a real VALO image to test with
    image_dir = Path("/home/<USER>/VALO_AI-FARM_2025/ai_farm_images_fixed_250703/ai_farm_images_fixed")
    test_images = list(image_dir.glob("**/*_cropped_*.JPEG"))[:1]
    
    if not test_images:
        print("❌ No test images found!")
        return False
    
    test_image = test_images[0]
    print(f"📸 Using test image: {test_image.name}")
    
    # Read and encode the image
    with open(test_image, 'rb') as f:
        image_data = f.read()
    base64_image = base64.b64encode(image_data).decode('utf-8')
    
    # OpenAI-compatible format for VLM
    payload = {
        "model": "VLM-38B-AWQ",
        "messages": [
            {
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": "Is there a person visible in this image? Please describe what you see in detail."
                    },
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": f"data:image/jpeg;base64,{base64_image}"
                        }
                    }
                ]
            }
        ],
        "temperature": 0,
        "max_tokens": 300
    }
    
    # Headers
    headers = {
        "Content-Type": "application/json"
    }
    
    print(f"📤 Request URL: {endpoint}")
    print(f"📤 Request Headers: {headers}")
    print(f"📤 Query: {payload['messages'][0]['content'][0]['text']}")
    print()
    
    try:
        timeout = aiohttp.ClientTimeout(total=30)
        async with aiohttp.ClientSession(timeout=timeout) as session:
            print("🔄 Sending request...")
            start_time = datetime.now()
            
            async with session.post(
                endpoint,
                headers=headers,
                json=payload
            ) as response:
                elapsed_time = (datetime.now() - start_time).total_seconds()
                
                print(f"📥 Response Status: {response.status}")
                print(f"⏱️  Response Time: {elapsed_time:.2f} seconds")
                print()
                
                if response.status == 200:
                    result = await response.json()
                    print("✅ SUCCESS! VLM API responded correctly")
                    print(f"📥 Response Body: {json.dumps(result, indent=2)}")
                    
                    # Validate OpenAI-compatible response format
                    if "choices" in result and len(result.get("choices", [])) > 0:
                        message_content = result["choices"][0].get("message", {}).get("content", "")
                        print(f"\n🤖 VLM Response: {message_content}")
                        print("\n✅ OpenAI-compatible format is valid")
                        print("✅ Endpoint is working correctly!")
                    else:
                        print("⚠️  Response format may not be standard OpenAI format")
                    
                    return True
                else:
                    error_text = await response.text()
                    print(f"❌ FAILED! HTTP {response.status}")
                    print(f"📥 Error Response: {error_text}")
                    return False
                    
    except aiohttp.ClientConnectorError as e:
        print(f"❌ CONNECTION FAILED: Cannot connect to {endpoint}")
        print(f"   Error: {str(e)}")
        print("   Make sure the VLM API server is accessible at **************:9500")
        return False
    except asyncio.TimeoutError as e:
        print(f"❌ TIMEOUT: Request timed out after 30 seconds")
        print(f"   Error: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ UNEXPECTED ERROR: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


async def test_concurrent_requests():
    """Test multiple concurrent requests to check API stability"""
    endpoint = "http://**************:9500/v1/chat/completions"
    print("\n🔍 Testing concurrent requests")
    print("-" * 40)
    
    # Get multiple test images
    image_dir = Path("/home/<USER>/VALO_AI-FARM_2025/ai_farm_images_fixed_250703/ai_farm_images_fixed")
    test_images = list(image_dir.glob("**/*_cropped_*.JPEG"))[:3]
    
    if len(test_images) < 3:
        print("Not enough images for concurrent test")
        return
    
    async def single_request(image_path: Path, index: int):
        """Single request for concurrent testing"""
        try:
            with open(image_path, 'rb') as f:
                image_data = f.read()
            encoded_image = base64.b64encode(image_data).decode('utf-8')
            
            payload = {
                "model": "VLM-38B-AWQ",
                "messages": [
                    {
                        "role": "user",
                        "content": [
                            {"type": "text", "text": "Is this a safety violation? Answer yes or no with brief reasoning."},
                            {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{encoded_image}"}}
                        ]
                    }
                ],
                "temperature": 0,
                "max_tokens": 100
            }
            
            start = datetime.now()
            timeout = aiohttp.ClientTimeout(total=30)
            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.post(endpoint, json=payload) as response:
                    elapsed = (datetime.now() - start).total_seconds()
                    if response.status == 200:
                        result = await response.json()
                        content = result.get("choices", [{}])[0].get("message", {}).get("content", "")[:50]
                        print(f"  Request {index}: ✅ Success ({elapsed:.2f}s) - {content}...")
                        return True
                    else:
                        print(f"  Request {index}: ❌ Failed with status {response.status}")
                        return False
        except Exception as e:
            print(f"  Request {index}: ❌ Error - {str(e)[:50]}...")
            return False
    
    print("📤 Sending 3 concurrent requests...")
    tasks = [single_request(img, i+1) for i, img in enumerate(test_images)]
    results = await asyncio.gather(*tasks)
    
    success_count = sum(1 for r in results if r)
    print(f"\n📊 Concurrent test results: {success_count}/3 successful")


async def main():
    """Main test function"""
    print("🚀 VALO AI-FARM VLM Endpoint Test")
    print("Testing VLM API at **************:9500")
    print("=" * 60)
    
    # Test main endpoint
    endpoint_ok = await test_vlm_endpoint()
    
    if endpoint_ok:
        # Test concurrent requests
        await test_concurrent_requests()
        
        print("\n" + "=" * 60)
        print("🎉 SUCCESS! VLM endpoint is working correctly.")
        print("✅ The endpoint accepts image analysis requests")
        print("✅ Response format is correct")
        print("\n🔧 Configuration verified:")
        print("   - URL: http://**************:9500/v1/chat/completions")
        print("   - Headers: Content-Type: application/json")
        print("   - Format: OpenAI-compatible chat completions API")
        print("\n📌 Ready to continue with Round 3 processing!")
        return 0
    else:
        print("\n" + "=" * 60)
        print("❌ FAILED! VLM endpoint is not working.")
        print("🔧 Troubleshooting steps:")
        print("   1. Check if VLM API server is running on **************:9500")
        print("   2. Verify network connectivity to the server")
        print("   3. Check if firewall is blocking the connection")
        print("   4. Try the Friendli fallback endpoint")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
