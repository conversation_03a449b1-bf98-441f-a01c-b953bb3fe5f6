#!/usr/bin/env python3
"""
Show complete VLM request/response examples with the intelligent prompt
"""

import json

def show_complete_examples():
    print("COMPLETE INTELLIGENT PROMPT - VLM EXAMPLES")
    print("="*70)
    
    # The complete intelligent prompt (showing key parts)
    prompt_summary = """
SAFETY VIOLATION DETECTION WITH LEARNED PATTERNS

STEP 1: ENTITY IDENTIFICATION
A) INDUSTRIAL STRUCTURE
   - CRANE: Geometric metal frames, painted beams
   - VESSEL: Ship railings, deck components  
   - PM: Boxy mechanical truck unit
   - SPREADER: Rectangular container frame
   
B) PERSON
   - Human shape, clothing/PPE visible
   
STEP 2: PPE COMPLIANCE
FULL PPE: Helmet + High-vis vest + Coveralls
COMPLIANT: White/orange/yellow helmet + Orange/yellow vest

STEP 3: BEHAVIORAL VIOLATIONS
Mobile phone, missing equipment, unsafe location, etc.

STEP 4: DECISION RULES
- Structure only → FALSE POSITIVE: YES
- Person + Full PPE + No violations → FALSE POSITIVE: YES  
- Person + Any violation → FALSE POSITIVE: NO
"""
    
    print("PROMPT OVERVIEW:")
    print(prompt_summary)
    
    print("\n" + "="*70)
    print("VLM API REQUEST STRUCTURE:")
    print("="*70)
    
    api_request = {
        "url": "http://**************:9500/v1/chat/completions",
        "method": "POST",
        "body": {
            "model": "VLM-38B-AWQ",
            "messages": [{
                "role": "user",
                "content": [
                    {"type": "text", "text": "[Complete intelligent prompt with all descriptions]"},
                    {"type": "text", "text": "\n\nSOURCE IMAGE:"},
                    {"type": "image_url", "image_url": {"url": "data:image/jpeg;base64,[source_image]"}},
                    {"type": "text", "text": "\n\nCROPPED IMAGE:"},
                    {"type": "image_url", "image_url": {"url": "data:image/jpeg;base64,[cropped_image]"}}
                ]
            }],
            "temperature": 0.1,
            "max_tokens": 200
        }
    }
    
    print(json.dumps(api_request, indent=2))
    
    # Example scenarios and expected responses
    examples = [
        {
            "scenario": "Crane Structure Only",
            "description": "Image shows crane geometric beams with no person",
            "expected_response": """FALSE POSITIVE: YES
Entity: STRUCTURE
If PERSON - PPE Status: N/A
Violations: None
Confidence: High""",
            "explanation": "VLM recognizes crane structure characteristics → Correctly marks as false positive"
        },
        
        {
            "scenario": "Worker with Full PPE at Wharf",
            "description": "Person wearing white helmet and orange vest, working normally",
            "expected_response": """FALSE POSITIVE: YES
Entity: PERSON
If PERSON - PPE Status: COMPLIANT
Violations: None
Confidence: High""",
            "explanation": "VLM recognizes full PPE compliance + no behavioral violations → False positive"
        },
        
        {
            "scenario": "Worker Using Mobile Phone",
            "description": "Person in full PPE but using mobile phone",
            "expected_response": """FALSE POSITIVE: NO
Entity: PERSON
If PERSON - PPE Status: COMPLIANT
Violations: Mobile phone/device use
Confidence: High""",
            "explanation": "Despite full PPE, behavioral violation detected → Valid violation"
        },
        
        {
            "scenario": "Worker Missing Helmet",
            "description": "Person visible but no helmet/hard hat",
            "expected_response": """FALSE POSITIVE: NO
Entity: PERSON
If PERSON - PPE Status: NON-COMPLIANT
Violations: No helmet visible
Confidence: High""",
            "explanation": "PPE violation detected → Valid safety violation"
        },
        
        {
            "scenario": "Vessel Structure with Railings",
            "description": "Ship deck components and railings, no person",
            "expected_response": """FALSE POSITIVE: YES
Entity: STRUCTURE
If PERSON - PPE Status: N/A
Violations: None
Confidence: High""",
            "explanation": "VLM recognizes vessel structure characteristics → False positive"
        },
        
        {
            "scenario": "Worker Missing GO/STOP Bat",
            "description": "WOS in full PPE but missing required equipment",
            "expected_response": """FALSE POSITIVE: NO
Entity: PERSON
If PERSON - PPE Status: COMPLIANT
Violations: Missing required equipment (GO/STOP bat)
Confidence: Medium""",
            "explanation": "Behavioral violation (missing equipment) → Valid violation"
        },
        
        {
            "scenario": "PM Structure (Prime Mover)",
            "description": "Boxy mechanical truck unit with wheels",
            "expected_response": """FALSE POSITIVE: YES
Entity: STRUCTURE
If PERSON - PPE Status: N/A
Violations: None
Confidence: High""",
            "explanation": "VLM recognizes PM structure characteristics → False positive"
        },
        
        {
            "scenario": "Unclear/Poor Quality Image",
            "description": "Cannot clearly determine if person or structure",
            "expected_response": """FALSE POSITIVE: NO
Entity: UNCLEAR
If PERSON - PPE Status: N/A
Violations: Cannot assess
Confidence: Low""",
            "explanation": "Safety-first approach when uncertain → Valid violation for review"
        }
    ]
    
    print("\n" + "="*70)
    print("EXAMPLE SCENARIOS AND VLM RESPONSES:")
    print("="*70)
    
    for i, example in enumerate(examples, 1):
        print(f"\n{i}. {example['scenario']}")
        print("-"*50)
        print(f"Description: {example['description']}")
        print(f"\nExpected VLM Response:")
        print(example['expected_response'])
        print(f"\nExplanation: {example['explanation']}")
    
    print("\n" + "="*70)
    print("KEY IMPROVEMENTS WITH THIS PROMPT:")
    print("="*70)
    print("1. VLM knows specific visual characteristics of each structure type")
    print("2. VLM understands what proper PPE compliance looks like")
    print("3. VLM checks for behavioral violations even with full PPE")
    print("4. Clear decision tree with no ambiguity")
    print("5. Safety-first default for unclear cases")
    
    print("\n" + "="*70)
    print("PYTHON CODE TO PROCESS RESPONSE:")
    print("="*70)
    
    code_example = '''
def parse_vlm_response(response):
    """Parse the structured VLM response"""
    
    # Extract key fields
    lines = response.strip().split('\\n')
    
    result = {
        'is_false_positive': None,
        'entity': None,
        'ppe_status': None,
        'violations': None,
        'confidence': None
    }
    
    for line in lines:
        if 'FALSE POSITIVE:' in line:
            result['is_false_positive'] = 'YES' in line
        elif 'Entity:' in line:
            result['entity'] = line.split('Entity:')[1].strip()
        elif 'PPE Status:' in line:
            result['ppe_status'] = line.split('PPE Status:')[1].strip()
        elif 'Violations:' in line:
            result['violations'] = line.split('Violations:')[1].strip()
        elif 'Confidence:' in line:
            result['confidence'] = line.split('Confidence:')[1].strip()
    
    return result

# Example usage
response = """FALSE POSITIVE: NO
Entity: PERSON
If PERSON - PPE Status: COMPLIANT
Violations: Mobile phone/device use
Confidence: High"""

result = parse_vlm_response(response)
print(f"Is False Positive: {result['is_false_positive']}")  # False
print(f"Entity: {result['entity']}")  # PERSON
print(f"Violations: {result['violations']}")  # Mobile phone/device use

# Decision
if result['is_false_positive']:
    print("→ Filter out this alert")
else:
    print("→ Alert safety team! Real violation detected")
'''
    
    print(code_example)

if __name__ == "__main__":
    show_complete_examples()