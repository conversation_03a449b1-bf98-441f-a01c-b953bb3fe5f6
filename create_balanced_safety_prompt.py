#!/usr/bin/env python3
"""
Create a balanced prompt that catches false positives 
while protecting real safety violations
SAFETY FIRST - We cannot miss real violations!
"""

def create_balanced_prompt():
    """Create a prompt that balances FP detection with safety"""
    
    prompt = """SAFETY VIOLATION ANALYSIS - BALANCED APPROACH

CRITICAL: This is a safety system. When in doubt, flag as potential violation.
We must NOT miss real safety violations.

ANALYZE BOTH IMAGES:
- SOURCE IMAGE: Full context view
- CROPPED IMAGE: Specific area of concern

STEP 1 - ENTITY IDENTIFICATION:
What is in the CROPPED image?

A) INDUSTRIAL STRUCTURE (High confidence needed: >85%)
   Characteristics that indicate structure:
   🏗️ CRANE: Large geometric frames, metal beams, angular supports, wheels
   🚢 VESSEL: Ship components, railings, deck structures, painted metal
   🔧 PM EQUIPMENT: Boxy mechanical units with wheels, industrial labels
   📦 SPREADER: Container handling frame, corner guides, lifting mechanisms
   🏭 OTHER: Camera structures, cell guides, water reflections

B) PERSON (Any confidence level)
   - Human form visible (even partial)
   - Clothing/PPE visible
   - Human proportions
   - Movement capability

C) UNCLEAR/MIXED
   - Cannot determine with confidence
   - Both person and structure visible

STEP 2 - SAFETY VIOLATION ASSESSMENT:

IF STRUCTURE (>85% confidence) AND no person visible:
→ Likely FALSE POSITIVE

IF PERSON detected (any confidence):
→ CHECK FOR VIOLATIONS:
   
   PPE Violations:
   ❌ Missing helmet
   ❌ Missing vest
   ❌ Vest not fastened properly
   ❌ Missing required safety equipment
   
   Behavioral Violations (even with full PPE):
   ❌ Using mobile phone
   ❌ Not carrying required equipment (GO/STOP bat, etc.)
   ❌ In restricted/dangerous area
   ❌ Improper operations
   ❌ One person doing two-person job
   ❌ Not maintaining safe distance
   ❌ Taking unsafe shortcuts (spreader rides, etc.)

IF UNCLEAR/MIXED:
→ Default to POTENTIAL VIOLATION (safety first)

DECISION FRAMEWORK:

1. STRUCTURE ONLY (>85% confidence) + No person = FALSE POSITIVE
2. PERSON + Clear PPE violation = VALID VIOLATION  
3. PERSON + Behavioral violation = VALID VIOLATION
4. PERSON + Full PPE + Safe behavior + Safe location = FALSE POSITIVE
5. UNCLEAR = VALID VIOLATION (err on side of safety)

IMPORTANT SAFETY RULES:
- A person in full PPE can still have violations
- Mobile phone use is a violation regardless of PPE
- Missing required equipment is a violation
- Being in wrong location is a violation
- When uncertain, flag for human review

OUTPUT:
FALSE POSITIVE: [YES/NO]
Entity: [PERSON/STRUCTURE/UNCLEAR]
If PERSON - Violation Type: [PPE/BEHAVIORAL/NONE]
Confidence: [0-100]%
Safety Risk: [HIGH/MEDIUM/LOW/NONE]
Reason: [Brief explanation]"""

    return prompt

def create_violation_specific_prompts():
    """Create prompts for specific violation types"""
    
    prompts = {
        "PPE Non-compliance": """PPE VIOLATION CHECK

Focus: Personal Protective Equipment compliance

1. Entity Check:
   - Is this definitely a structure (>90% confidence)? → FALSE POSITIVE
   - Is this a person? → Continue to step 2
   
2. PPE Assessment (for PERSON):
   Required PPE:
   ✓ Hard hat/helmet (any color)
   ✓ Safety vest (must be worn AND fastened)
   ✓ Additional as required (harness, gloves, etc.)
   
   Common violations:
   - No helmet
   - No vest
   - Vest not fastened/worn properly
   - Missing specific PPE for task
   
3. Context matters:
   - Ship crew may have different PPE requirements
   - Mobile phone use is ALWAYS a violation
   - Missing equipment (GO/STOP bat) is a violation

DECISION: Only mark FALSE POSITIVE if:
- Structure with >90% confidence, OR
- Person with ALL required PPE properly worn AND no other violations""",

        "One man Lashing": """ONE MAN LASHING CHECK

Focus: Ensuring two-person safety protocol

1. Entity Check:
   - Structure only? → FALSE POSITIVE
   - Person visible? → Continue
   
2. Activity Check:
   - Is person doing lashing/unlashing work?
   - Are there TWO people working together?
   - Is it actually a one-person violation?
   
3. Common false patterns:
   - Cell guides mistaken for people
   - Two people present = usually FALSE POSITIVE
   - Person not actually doing lashing = FALSE POSITIVE

DECISION: Mark as FALSE POSITIVE only if:
- No person visible (structure only), OR  
- Two or more people working together, OR
- Person not doing lashing work""",

        "2-Container Distance": """CONTAINER DISTANCE VIOLATION CHECK

Focus: Safe distance from container operations

1. Entity Check:
   - Spreader/structure only? → Check carefully
   - Person near containers? → Continue
   
2. Safety Check:
   - Is person maintaining 2-container distance?
   - Are they in the danger zone?
   - Do they have T-LAD if required?
   
DECISION: This is HIGH RISK - when in doubt, flag as violation"""
    }
    
    return prompts

def main():
    # Create main balanced prompt
    balanced_prompt = create_balanced_prompt()
    
    # Save main prompt
    with open('balanced_safety_prompt.txt', 'w') as f:
        f.write(balanced_prompt)
    
    # Create violation-specific prompts
    specific_prompts = create_violation_specific_prompts()
    
    # Save specific prompts
    import json
    with open('violation_specific_prompts.json', 'w') as f:
        json.dump(specific_prompts, f, indent=2)
    
    print("BALANCED SAFETY PROMPTS CREATED")
    print("="*60)
    print("\nKey improvements:")
    print("1. Higher confidence threshold for structures (85%)")
    print("2. Recognizes behavioral violations even with PPE")
    print("3. Defaults to safety when unclear")
    print("4. Specific logic for different violation types")
    print("\nFiles created:")
    print("- balanced_safety_prompt.txt")
    print("- violation_specific_prompts.json")
    
    # Create a test configuration
    test_config = {
        "structure_confidence_threshold": 85,
        "person_confidence_threshold": 50,
        "default_to_safety": True,
        "behavioral_violations": [
            "mobile_phone_use",
            "missing_equipment",
            "unsafe_location",
            "improper_operations"
        ]
    }
    
    with open('balanced_prompt_config.json', 'w') as f:
        json.dump(test_config, f, indent=2)
    
    print("\nRECOMMENDED APPROACH:")
    print("1. Test on subset with known valid violations")
    print("2. Ensure 100% valid violation protection")
    print("3. Then optimize for FP detection")
    print("4. Safety first - better to have more FPs than miss real violations")

if __name__ == "__main__":
    main()