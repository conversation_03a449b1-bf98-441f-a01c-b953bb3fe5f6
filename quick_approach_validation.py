#!/usr/bin/env python3
"""
Quick validation of approaches on 10 cases
"""

import json
import base64
import requests
import os
import time

def quick_test():
    """Test simple equipment approach on 10 cases"""
    
    print("QUICK VALIDATION TEST - 10 CASES")
    print("="*50)
    
    vlm_url = "http://**************:9500/v1/chat/completions"
    vlm_model = "VLM-38B-AWQ"
    
    # Simple equipment detection prompt
    prompt = "Is this image primarily showing industrial equipment (crane, vessel, truck, or spreader) with no people visible? Answer only YES or NO."
    
    # Load test cases
    with open('valo_batch_round3_complete.json', 'r') as f:
        all_cases = json.load(f)['results'][:10]  # First 10 cases
    
    print(f"Testing {len(all_cases)} cases...")
    
    correct = 0
    for case in all_cases:
        # Encode images
        source_b64 = None
        cropped_b64 = None
        
        if os.path.exists(case['source_image']):
            with open(case['source_image'], 'rb') as f:
                source_b64 = base64.b64encode(f.read()).decode('utf-8')
        
        if os.path.exists(case['cropped_image']):
            with open(case['cropped_image'], 'rb') as f:
                cropped_b64 = base64.b64encode(f.read()).decode('utf-8')
        
        if not source_b64 or not cropped_b64:
            print(f"✗ {case['case_number']}: Missing images")
            continue
        
        # Query VLM
        payload = {
            "model": vlm_model,
            "messages": [{
                "role": "user",
                "content": [
                    {"type": "text", "text": prompt},
                    {"type": "text", "text": "\n\nSOURCE IMAGE:"},
                    {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{source_b64}"}},
                    {"type": "text", "text": "\n\nCROPPED IMAGE:"},
                    {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{cropped_b64}"}}
                ]
            }],
            "temperature": 0.1,
            "max_tokens": 50
        }
        
        try:
            response = requests.post(vlm_url, json=payload, timeout=30)
            if response.status_code == 200:
                vlm_response = response.json()['choices'][0]['message']['content']
                
                # Parse response
                is_equipment = "YES" in vlm_response.upper()[:10]
                predicted_fp = is_equipment  # Equipment = False Positive
                actual_fp = case['is_false_positive']
                
                is_correct = predicted_fp == actual_fp
                if is_correct:
                    correct += 1
                
                status = "✓" if is_correct else "✗"
                print(f"{status} {case['case_number']}: VLM={vlm_response.strip()}, Predicted={predicted_fp}, Actual={actual_fp}")
            else:
                print(f"✗ {case['case_number']}: API error {response.status_code}")
        except Exception as e:
            print(f"✗ {case['case_number']}: {str(e)}")
        
        time.sleep(1)
    
    print(f"\n✓ Quick test complete: {correct}/{len(all_cases)} correct ({correct/len(all_cases)*100:.1f}%)")
    print("\nIf accuracy > 50%, the full test should proceed successfully.")

if __name__ == "__main__":
    quick_test()