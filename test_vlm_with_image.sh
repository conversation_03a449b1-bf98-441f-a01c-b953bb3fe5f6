#!/bin/bash

# Test VLM with an actual image
echo "Testing VLM endpoint with actual image..."

# Get first image from dataset
IMAGE_PATH=$(python3 -c "
import json
with open('valo_batch_round3_complete.json', 'r') as f:
    data = json.load(f)
    print(data['results'][0]['cropped_image'])
")

echo "Using image: $IMAGE_PATH"

# Check if image exists
if [ ! -f "$IMAGE_PATH" ]; then
    echo "Image not found!"
    exit 1
fi

# Encode image to base64
IMAGE_BASE64=$(base64 -w 0 "$IMAGE_PATH")

# Create JSON payload
cat > /tmp/vlm_test_payload.json << EOF
{
  "model": "VLM-38B-AWQ",
  "messages": [{
    "role": "user",
    "content": [
      {"type": "text", "text": "Is this a false positive? YES/NO"},
      {"type": "image_url", "image_url": {"url": "data:image/jpeg;base64,$IMAGE_BASE64"}}
    ]
  }],
  "temperature": 0.1,
  "max_tokens": 50
}
EOF

# Test with timeout
echo "Sending request to VLM (30s timeout)..."
START_TIME=$(date +%s)

curl -X POST http://100.106.127.35:9500/v1/chat/completions \
  -H "Content-Type: application/json" \
  -d @/tmp/vlm_test_payload.json \
  --max-time 30 \
  -w "\nStatus: %{http_code}\nTime: %{time_total}s\n" \
  -o /tmp/vlm_response.json

END_TIME=$(date +%s)
DURATION=$((END_TIME - START_TIME))

echo "Request took: ${DURATION} seconds"

if [ -f /tmp/vlm_response.json ]; then
    echo "Response:"
    cat /tmp/vlm_response.json | python3 -m json.tool
fi

# Clean up
rm -f /tmp/vlm_test_payload.json /tmp/vlm_response.json