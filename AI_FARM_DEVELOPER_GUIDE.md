# AI-FARM Developer Guide: File Processing Features

## Quick Start Guide

### Prerequisites
- **Node.js**: >=18.0.0
- **Python**: >=3.9.0
- **Redis**: For caching (optional but recommended)
- **PostgreSQL**: For production database

### Environment Setup
```bash
# Clone and install dependencies
git clone <repository-url>
cd ai-farm
npm run install:all

# Copy environment configuration
cp .env.example .env

# Configure environment variables
# VLM_API_BASE_URL=http://**************:9500/v1
# VLM_API_KEY=your_api_key
# DATABASE_URL=postgresql://user:pass@localhost/ai_farm
# REDIS_URL=redis://localhost:6379/0
```

### Development Startup
```bash
# Start all services
npm run dev

# Individual services
npm run dev:backend  # FastAPI server on :8001
npm run dev:frontend # React app on :3000

# With Docker
npm run docker:up
```

## File Processing Architecture Overview

### Data Flow
```
1. Frontend Upload → 2. API Validation → 3. Background Processing → 4. Results Storage → 5. UI Presentation
```

### Key Components
- **FileUpload Component**: React drag-and-drop interface
- **Batch Processing API**: FastAPI upload and processing endpoints
- **VLM Service**: Vision Language Model integration
- **Redis Cache**: Result caching and session management
- **Database**: SQLAlchemy ORM with PostgreSQL/SQLite

## Modifying Upload Processing Behavior

### 1. Customizing File Validation

#### Frontend Validation (`/frontend/src/services/batch-service.ts`)
```typescript
// Add new file type validation
validateCustomFile(file: File): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  // Custom validation logic
  if (file.name.includes('invalid_pattern')) {
    errors.push('File name contains invalid pattern');
  }
  
  // Size validation
  if (file.size > this.MAX_CUSTOM_SIZE) {
    errors.push(`File too large (max ${this.formatFileSize(this.MAX_CUSTOM_SIZE)})`);
  }
  
  return { isValid: errors.length === 0, errors };
}

// Update upload validation
async uploadAndProcess(csvFile: File, imagesZip?: File) {
  // Add custom validation
  const csvValidation = this.validateCustomFile(csvFile);
  if (!csvValidation.isValid) {
    throw new Error(csvValidation.errors.join(', '));
  }
  
  // Continue with existing upload logic
  // ...
}
```

#### Backend Validation (`/backend/app/api/batch_processing.py`)
```python
# Add custom validation function
def validate_custom_csv(file: UploadFile) -> List[str]:
    """Custom CSV validation with business rules."""
    errors = []
    
    # File size validation
    if file.size > 100 * 1024 * 1024:  # 100MB
        errors.append("CSV file exceeds 100MB limit")
    
    # Content type validation
    if file.content_type not in ['text/csv', 'application/csv']:
        errors.append("Invalid content type - must be CSV")
    
    # Custom filename validation
    if 'test' in file.filename.lower():
        errors.append("Test files not allowed in production")
    
    return errors

# Update upload endpoint
@router.post("/upload", response_model=BatchProcessingResponse)
async def upload_and_process_batch(
    csv_file: UploadFile = File(...),
    # ... other parameters
):
    # Apply custom validation
    validation_errors = validate_custom_csv(csv_file)
    if validation_errors:
        raise HTTPException(status_code=400, detail="; ".join(validation_errors))
    
    # Continue with processing
    # ...
```

### 2. Adding New File Format Support

#### Step 1: Update Frontend Constants (`/frontend/src/utils/constants.ts`)
```typescript
export const UPLOAD_LIMITS = {
  csvMaxSize: 50 * 1024 * 1024,
  imagesMaxSize: 1024 * 1024 * 1024,
  // Add new format
  jsonMaxSize: 25 * 1024 * 1024,
  
  allowedCsvTypes: ['.csv'],
  allowedImageTypes: ['.zip'],
  // Add JSON support
  allowedJsonTypes: ['.json', '.jsonl'],
  
  allowedImageFormats: ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'],
};
```

#### Step 2: Update FileUpload Component
```typescript
// Add JSON file upload support
interface FileUploadProps {
  accept?: { [key: string]: string[] };
  fileType?: 'csv' | 'images' | 'json';  // Add new type
  // ... other props
}

const FileUpload: React.FC<FileUploadProps> = ({ 
  fileType = 'csv',
  // ... other props 
}) => {
  // Define accept types based on file type
  const getAcceptTypes = () => {
    switch (fileType) {
      case 'csv':
        return { 'text/csv': ['.csv'] };
      case 'images':
        return { 'application/zip': ['.zip'] };
      case 'json':
        return { 'application/json': ['.json', '.jsonl'] };
      default:
        return {};
    }
  };

  const { getRootProps, getInputProps } = useDropzone({
    accept: getAcceptTypes(),
    maxSize: getMaxSize(fileType),
    // ... other options
  });
};
```

#### Step 3: Backend Processing Support (`/backend/app/services/batch_processor.py`)
```python
class JSONProcessor:
    """Handle JSON file processing."""
    
    def __init__(self):
        self.required_fields = ['case_id', 'image_path', 'metadata']
    
    def process_json_file(self, json_path: Path) -> List[CaseData]:
        """Process JSON/JSONL file with case data."""
        try:
            case_data_list = []
            
            if json_path.suffix == '.jsonl':
                # Process JSONL (newline-delimited JSON)
                with open(json_path, 'r') as f:
                    for line_num, line in enumerate(f, 1):
                        try:
                            data = json.loads(line.strip())
                            case_data = self._process_json_record(data, line_num)
                            if case_data:
                                case_data_list.append(case_data)
                        except json.JSONDecodeError as e:
                            logger.warning(f"Invalid JSON on line {line_num}: {e}")
                            continue
            else:
                # Process regular JSON
                with open(json_path, 'r') as f:
                    data = json.load(f)
                    if isinstance(data, list):
                        for idx, record in enumerate(data):
                            case_data = self._process_json_record(record, idx)
                            if case_data:
                                case_data_list.append(case_data)
                    else:
                        case_data = self._process_json_record(data, 0)
                        if case_data:
                            case_data_list.append(case_data)
            
            return case_data_list
            
        except Exception as e:
            logger.error(f"Failed to process JSON file: {e}")
            raise
    
    def _process_json_record(self, record: dict, record_num: int) -> Optional[CaseData]:
        """Process individual JSON record."""
        try:
            # Validate required fields
            missing_fields = set(self.required_fields) - set(record.keys())
            if missing_fields:
                logger.warning(f"Record {record_num} missing fields: {missing_fields}")
                return None
            
            # Create CaseData object
            return CaseData(
                pk_event=record.get('pk_event', self._generate_pk_event()),
                case_number=record['case_id'],
                image_path=record['image_path'],
                validation_status=self._map_validation_status(record.get('status', 'unknown'))
            )
            
        except Exception as e:
            logger.warning(f"Failed to process JSON record {record_num}: {e}")
            return None
```

### 3. Customizing Visualization Outputs

#### Adding New Chart Types (`/frontend/src/components/charts/`)
```typescript
// Create new chart component
import { PieChart, Pie, Cell, ResponsiveContainer, Tooltip, Legend } from 'recharts';

interface ConfidenceDistributionChartProps {
  data: {
    confidenceRange: string;
    count: number;
    percentage: number;
  }[];
}

export const ConfidenceDistributionChart: React.FC<ConfidenceDistributionChartProps> = ({ data }) => {
  const COLORS = ['#00ff88', '#22c55e', '#ffaa00', '#ff3366'];
  
  const renderCustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-gray-800 p-3 rounded shadow-lg border border-gray-600">
          <p className="text-white font-medium">{data.confidenceRange}</p>
          <p className="text-green-400">Count: {data.count}</p>
          <p className="text-blue-400">Percentage: {data.percentage.toFixed(1)}%</p>
        </div>
      );
    }
    return null;
  };

  return (
    <ResponsiveContainer width="100%" height={300}>
      <PieChart>
        <Pie
          data={data}
          cx="50%"
          cy="50%"
          labelLine={false}
          label={({ confidenceRange, percentage }) => `${confidenceRange}: ${percentage.toFixed(1)}%`}
          outerRadius={80}
          fill="#8884d8"
          dataKey="count"
        >
          {data.map((entry, index) => (
            <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
          ))}
        </Pie>
        <Tooltip content={renderCustomTooltip} />
        <Legend />
      </PieChart>
    </ResponsiveContainer>
  );
};
```

#### Custom Data Processing for Charts (`/backend/app/services/analytics_service.py`)
```python
class AnalyticsService:
    """Enhanced analytics for custom visualizations."""
    
    def generate_confidence_distribution(self, batch_id: str) -> Dict[str, Any]:
        """Generate confidence score distribution data."""
        with db_manager.get_session() as session:
            # Query confidence scores
            results = session.query(CaseProcessingResult).filter(
                CaseProcessingResult.batch_id == batch_id,
                CaseProcessingResult.confidence_score.isnot(None)
            ).all()
            
            # Group by confidence ranges
            ranges = {
                '90-100%': 0,
                '80-89%': 0,
                '70-79%': 0,
                '60-69%': 0,
                '50-59%': 0,
                'Below 50%': 0
            }
            
            total_count = len(results)
            
            for result in results:
                score = result.confidence_score
                if score >= 90:
                    ranges['90-100%'] += 1
                elif score >= 80:
                    ranges['80-89%'] += 1
                elif score >= 70:
                    ranges['70-79%'] += 1
                elif score >= 60:
                    ranges['60-69%'] += 1
                elif score >= 50:
                    ranges['50-59%'] += 1
                else:
                    ranges['Below 50%'] += 1
            
            # Convert to chart data format
            chart_data = [
                {
                    'confidenceRange': range_name,
                    'count': count,
                    'percentage': (count / total_count * 100) if total_count > 0 else 0
                }
                for range_name, count in ranges.items()
                if count > 0  # Only include non-zero ranges
            ]
            
            return {
                'chartData': chart_data,
                'totalProcessed': total_count,
                'averageConfidence': sum(r.confidence_score for r in results) / total_count if results else 0
            }
```

## Debugging Common Upload/Processing Issues

### 1. File Upload Failures

#### Debug Frontend Issues
```typescript
// Enhanced error logging in FileUpload component
const handleUploadError = (error: any) => {
  console.error('Upload error details:', {
    error: error.message,
    stack: error.stack,
    fileInfo: {
      name: selectedFile?.name,
      size: selectedFile?.size,
      type: selectedFile?.type
    },
    timestamp: new Date().toISOString()
  });
  
  // Send to error tracking service
  if (window.gtag) {
    window.gtag('event', 'upload_error', {
      error_message: error.message,
      file_size: selectedFile?.size,
      file_type: selectedFile?.type
    });
  }
};
```

#### Debug Backend Issues
```python
# Enhanced logging in upload endpoint
@router.post("/upload")
async def upload_and_process_batch(
    csv_file: UploadFile = File(...),
    # ... other parameters
):
    request_id = str(uuid.uuid4())
    logger.info(f"Upload request {request_id} started", extra={
        'request_id': request_id,
        'csv_filename': csv_file.filename,
        'csv_size': csv_file.size,
        'content_type': csv_file.content_type
    })
    
    try:
        # Process upload
        result = await process_upload(csv_file, request_id)
        
        logger.info(f"Upload request {request_id} completed successfully", extra={
            'request_id': request_id,
            'batch_id': result.batch_id,
            'total_cases': result.total_cases
        })
        
        return result
        
    except Exception as e:
        logger.error(f"Upload request {request_id} failed", extra={
            'request_id': request_id,
            'error': str(e),
            'error_type': type(e).__name__
        }, exc_info=True)
        raise
```

### 2. CSV Processing Issues

#### Common Problems and Solutions
```python
# Robust CSV processing with detailed error reporting
class EnhancedCSVProcessor:
    def process_csv_with_validation(self, csv_path: Path) -> ProcessingResult:
        """Process CSV with comprehensive validation and error reporting."""
        validation_errors = []
        processing_warnings = []
        successful_rows = 0
        failed_rows = 0
        
        try:
            # Read CSV with error handling
            df = pd.read_csv(csv_path, dtype={
                'pk_event': 'Int64',
                'case_number': str,
                'url': str,
                'key': str
            })
            
            # Validate structure
            missing_columns = set(self.required_columns) - set(df.columns)
            if missing_columns:
                validation_errors.append(f"Missing columns: {', '.join(missing_columns)}")
            
            # Check for empty dataframe
            if df.empty:
                validation_errors.append("CSV file is empty")
                return ProcessingResult(
                    success=False,
                    errors=validation_errors,
                    total_rows=0,
                    successful_rows=0,
                    failed_rows=0
                )
            
            # Process each row with detailed error tracking
            case_data_list = []
            
            for index, row in df.iterrows():
                try:
                    # Validate row data
                    row_errors = self._validate_row_data(row, index)
                    if row_errors:
                        processing_warnings.extend(row_errors)
                        failed_rows += 1
                        continue
                    
                    # Process row
                    case_data = self._process_csv_row(row, index)
                    if case_data:
                        case_data_list.append(case_data)
                        successful_rows += 1
                    else:
                        failed_rows += 1
                        processing_warnings.append(f"Row {index + 1}: Failed to create case data")
                
                except Exception as e:
                    failed_rows += 1
                    processing_warnings.append(f"Row {index + 1}: {str(e)}")
                    logger.warning(f"Failed to process CSV row {index + 1}: {e}")
            
            return ProcessingResult(
                success=len(validation_errors) == 0,
                errors=validation_errors,
                warnings=processing_warnings,
                case_data=case_data_list,
                total_rows=len(df),
                successful_rows=successful_rows,
                failed_rows=failed_rows
            )
            
        except Exception as e:
            logger.error(f"CSV processing failed: {e}")
            return ProcessingResult(
                success=False,
                errors=[f"CSV processing failed: {str(e)}"],
                total_rows=0,
                successful_rows=0,
                failed_rows=0
            )
    
    def _validate_row_data(self, row: pd.Series, index: int) -> List[str]:
        """Validate individual row data."""
        errors = []
        
        # Check case number format
        case_number = str(row.get('case_number', ''))
        if not case_number.startswith('V125'):
            errors.append(f"Row {index + 1}: Invalid case number format '{case_number}'")
        
        # Check URL/image path
        url = str(row.get('url', ''))
        if not url or url.lower() == 'nan':
            errors.append(f"Row {index + 1}: Missing or invalid URL")
        
        # Check validation key
        key = str(row.get('key', ''))
        if key not in ['valid', 'invalid', '']:
            errors.append(f"Row {index + 1}: Invalid validation key '{key}'")
        
        return errors
```

### 3. VLM Integration Issues

#### API Connection Debugging
```python
# Enhanced VLM service with detailed error tracking
class EnhancedVLMService:
    async def analyze_image_with_retry(self, image_path: str, case_number: str, max_retries: int = 3) -> VLMAnalysisResult:
        """Analyze image with retry logic and detailed error tracking."""
        last_error = None
        
        for attempt in range(max_retries):
            try:
                result = await self.analyze_image(image_path, case_number)
                
                # Log successful analysis
                logger.info(f"VLM analysis successful for {case_number}", extra={
                    'case_number': case_number,
                    'image_path': image_path,
                    'attempt': attempt + 1,
                    'confidence_score': result.confidence_score,
                    'processing_time_ms': result.processing_time_ms
                })
                
                return result
                
            except httpx.TimeoutException as e:
                last_error = e
                wait_time = (2 ** attempt) * 1  # Exponential backoff
                logger.warning(f"VLM API timeout for {case_number}, attempt {attempt + 1}/{max_retries}", extra={
                    'case_number': case_number,
                    'attempt': attempt + 1,
                    'wait_time': wait_time,
                    'error': str(e)
                })
                
                if attempt < max_retries - 1:
                    await asyncio.sleep(wait_time)
                
            except httpx.HTTPStatusError as e:
                last_error = e
                logger.error(f"VLM API HTTP error for {case_number}", extra={
                    'case_number': case_number,
                    'status_code': e.response.status_code,
                    'response_text': e.response.text,
                    'attempt': attempt + 1
                })
                
                # Don't retry on 4xx errors
                if 400 <= e.response.status_code < 500:
                    break
                
            except Exception as e:
                last_error = e
                logger.error(f"VLM analysis error for {case_number}", extra={
                    'case_number': case_number,
                    'error_type': type(e).__name__,
                    'error': str(e),
                    'attempt': attempt + 1
                }, exc_info=True)
        
        # Return error result after all retries failed
        return VLMAnalysisResult(
            case_number=case_number,
            image_path=image_path,
            confidence_score=0,
            is_false_positive=False,
            analysis_text=f"Analysis failed after {max_retries} attempts: {str(last_error)}",
            processing_time_ms=0,
            error_details=str(last_error)
        )
```

## Testing Strategies for File Processing Features

### 1. Unit Testing

#### Frontend Component Tests
```typescript
// FileUpload component tests
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { FileUpload } from '../FileUpload';

describe('FileUpload Component', () => {
  test('validates CSV file type correctly', async () => {
    const mockOnUpload = jest.fn();
    render(<FileUpload onUpload={mockOnUpload} fileType="csv" />);
    
    const file = new File(['test,data\n1,2'], 'test.csv', { type: 'text/csv' });
    const input = screen.getByRole('button');
    
    await userEvent.upload(input, file);
    
    expect(mockOnUpload).toHaveBeenCalledWith(file);
  });
  
  test('rejects invalid file types', async () => {
    const mockOnUpload = jest.fn();
    render(<FileUpload onUpload={mockOnUpload} fileType="csv" />);
    
    const file = new File(['invalid'], 'test.txt', { type: 'text/plain' });
    const input = screen.getByRole('button');
    
    await userEvent.upload(input, file);
    
    expect(screen.getByText(/file type is not supported/i)).toBeInTheDocument();
    expect(mockOnUpload).not.toHaveBeenCalled();
  });
});
```

#### Backend Service Tests
```python
# VLM service tests
import pytest
from unittest.mock import AsyncMock, patch
from app.services.vlm_service import VLMService
from app.models.schemas import VLMAnalysisResult

class TestVLMService:
    @pytest.fixture
    def vlm_service(self):
        return VLMService()
    
    @pytest.mark.asyncio
    async def test_analyze_image_success(self, vlm_service):
        """Test successful image analysis."""
        mock_response = {
            'choices': [{
                'message': {
                    'content': 'CONFIDENCE: 85\nCLASSIFICATION: false_positive\nREASON: No safety violation detected'
                }
            }]
        }
        
        with patch('httpx.AsyncClient.post') as mock_post:
            mock_post.return_value.json.return_value = mock_response
            mock_post.return_value.raise_for_status = AsyncMock()
            
            result = await vlm_service.analyze_image('test_image.jpg', 'V1250630118')
            
            assert isinstance(result, VLMAnalysisResult)
            assert result.confidence_score == 85
            assert result.is_false_positive is True
            assert 'No safety violation detected' in result.analysis_text
    
    @pytest.mark.asyncio
    async def test_analyze_image_with_retry(self, vlm_service):
        """Test retry logic on API failure."""
        with patch('httpx.AsyncClient.post') as mock_post:
            # First call fails, second succeeds
            mock_post.side_effect = [
                httpx.TimeoutException("Request timeout"),
                AsyncMock(json=lambda: {'choices': [{'message': {'content': 'CONFIDENCE: 75\nCLASSIFICATION: valid'}}]})
            ]
            
            result = await vlm_service.analyze_image_with_retry('test_image.jpg', 'V1250630118')
            
            assert result.confidence_score == 75
            assert mock_post.call_count == 2
```

### 2. Integration Testing

#### End-to-End Upload Test
```python
# Integration test for complete upload workflow
import pytest
from fastapi.testclient import TestClient
from app.main import app

class TestUploadIntegration:
    @pytest.fixture
    def client(self):
        return TestClient(app)
    
    def test_complete_upload_workflow(self, client):
        """Test complete upload and processing workflow."""
        # Prepare test files
        csv_content = "pk_event,case_number,url,key\n143881,V1250630118,/test/image.jpg,invalid"
        csv_file = ('csv_file', ('test.csv', csv_content, 'text/csv'))
        
        # Upload files
        response = client.post('/api/v1/batch/upload', files=[csv_file])
        
        assert response.status_code == 200
        batch_data = response.json()
        
        assert 'batch_id' in batch_data
        assert batch_data['status'] in ['pending', 'processing']
        
        # Check batch status
        batch_id = batch_data['batch_id']
        status_response = client.get(f'/api/v1/batch/{batch_id}')
        
        assert status_response.status_code == 200
        status_data = status_response.json()
        
        assert status_data['batch_id'] == batch_id
        assert 'total_cases' in status_data
```

### 3. Performance Testing

#### Load Testing Script
```python
# Performance test for file upload endpoints
import asyncio
import aiohttp
import time
from concurrent.futures import ThreadPoolExecutor

async def upload_test_file(session, file_data, endpoint_url):
    """Upload test file and measure response time."""
    start_time = time.time()
    
    try:
        async with session.post(endpoint_url, data=file_data) as response:
            result = await response.json()
            end_time = time.time()
            
            return {
                'status_code': response.status,
                'response_time': end_time - start_time,
                'success': response.status == 200,
                'batch_id': result.get('batch_id') if response.status == 200 else None
            }
    except Exception as e:
        end_time = time.time()
        return {
            'status_code': 0,
            'response_time': end_time - start_time,
            'success': False,
            'error': str(e)
        }

async def run_load_test(concurrent_requests=10, total_requests=100):
    """Run load test with concurrent file uploads."""
    endpoint_url = 'http://localhost:8001/api/v1/batch/upload'
    
    # Prepare test data
    csv_content = "pk_event,case_number,url,key\n143881,V1250630118,/test/image.jpg,invalid"
    file_data = aiohttp.FormData()
    file_data.add_field('csv_file', csv_content, filename='test.csv', content_type='text/csv')
    
    async with aiohttp.ClientSession() as session:
        semaphore = asyncio.Semaphore(concurrent_requests)
        
        async def limited_upload():
            async with semaphore:
                return await upload_test_file(session, file_data, endpoint_url)
        
        # Run concurrent requests
        tasks = [limited_upload() for _ in range(total_requests)]
        results = await asyncio.gather(*tasks)
        
        # Analyze results
        successful_requests = sum(1 for r in results if r['success'])
        avg_response_time = sum(r['response_time'] for r in results) / len(results)
        max_response_time = max(r['response_time'] for r in results)
        min_response_time = min(r['response_time'] for r in results)
        
        print(f"Load Test Results:")
        print(f"Total Requests: {total_requests}")
        print(f"Successful Requests: {successful_requests}")
        print(f"Success Rate: {successful_requests/total_requests*100:.2f}%")
        print(f"Average Response Time: {avg_response_time:.3f}s")
        print(f"Min Response Time: {min_response_time:.3f}s")
        print(f"Max Response Time: {max_response_time:.3f}s")

if __name__ == "__main__":
    asyncio.run(run_load_test())
```

This developer guide provides comprehensive information for working with AI-FARM's file processing features, including customization examples, debugging strategies, and testing approaches.