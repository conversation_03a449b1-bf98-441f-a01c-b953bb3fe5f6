#!/usr/bin/env python3
"""
Stop current Round 3 processing and start safety-first Round 4
"""

import os
import signal
import subprocess
import time
import psutil


def find_and_kill_process(process_name):
    """Find and kill processes by name"""
    killed = False
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            # Check if process name matches
            if process_name in proc.info['name']:
                print(f"Found process: {proc.info['name']} (PID: {proc.info['pid']})")
                proc.kill()
                killed = True
            # Also check command line
            elif proc.info['cmdline'] and any(process_name in arg for arg in proc.info['cmdline']):
                print(f"Found process by cmdline: {' '.join(proc.info['cmdline'][:3])}... (PID: {proc.info['pid']})")
                proc.kill()
                killed = True
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
            pass
    return killed


def main():
    print("="*80)
    print("STOPPING ROUND 3 AND STARTING SAFETY-FIRST ROUND 4")
    print("="*80)
    
    # Step 1: Stop any running intelligent learning processes
    print("\n1. Stopping current Round 3 processing...")
    if find_and_kill_process("valo_intelligent_learning"):
        print("   ✓ Stopped intelligent learning processes")
        time.sleep(2)  # Give processes time to die
    else:
        print("   - No active processes found")
    
    # Step 2: Analyze Round 3 failures
    print("\n2. Analyzing Round 3 failures...")
    try:
        subprocess.run([
            "python3", 
            "/home/<USER>/VALO_AI-FARM_2025/analyze_round3_failures.py"
        ], check=True)
    except subprocess.CalledProcessError as e:
        print(f"   ⚠ Analysis failed: {e}")
    
    # Step 3: Ask for confirmation before starting Round 4
    print("\n3. Ready to start Safety-First Round 4")
    print("   This will prioritize 100% valid protection rate")
    print("   False positive detection will be secondary")
    
    response = input("\nProceed with Safety-First Round 4? (y/n): ")
    
    if response.lower() == 'y':
        print("\n4. Starting Safety-First Round 4...")
        print("   Priority: NEVER dismiss valid safety violations")
        print("   Running with enhanced safety checks...\n")
        
        # Start the safety-first processor
        subprocess.run([
            "python3",
            "/home/<USER>/VALO_AI-FARM_2025/valo_intelligent_learning_fixed.py"
        ])
    else:
        print("\nRound 4 cancelled. Review the failure analysis before proceeding.")


if __name__ == "__main__":
    main()