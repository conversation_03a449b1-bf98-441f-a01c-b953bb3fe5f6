[{"name": "safety_first", "structure_threshold": 95, "person_threshold": 20, "default_action": "valid_violation"}, {"name": "balanced_90", "structure_threshold": 90, "person_threshold": 40, "default_action": "valid_violation"}, {"name": "balanced_85", "structure_threshold": 85, "person_threshold": 50, "default_action": "review"}, {"name": "hybrid_smart", "structure_threshold": 90, "person_threshold": 40, "default_action": "context_based"}]